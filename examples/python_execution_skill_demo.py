#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Python执行技能演示脚本
展示如何使用Python执行技能进行深度调研和数据分析

作者: 隔壁老王 (暴躁的代码架构师)
版本: 1.0.0
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from cognitive_modules.skills.python_execution_skill import PythonExecutionSkill
from utilities.logger import logger


def demo_basic_usage():
    """演示基本用法"""
    print("🔥 === Python执行技能基本用法演示 ===")
    
    # 创建技能实例
    skill = PythonExecutionSkill()
    
    # 测试用例1: 数据分析
    print("\n📊 测试用例1: 股票数据分析")
    result1 = skill.execute(
        input_text="分析苹果公司(AAPL)最近30天的股价走势，计算移动平均线，生成可视化图表",
        user_id="demo_user",
        session_id="demo_session_1"
    )
    
    print(f"执行结果: {json.dumps(result1, indent=2, ensure_ascii=False)}")
    
    # 测试用例2: 网络爬取
    print("\n🕷️ 测试用例2: 网络数据爬取")
    result2 = skill.execute(
        input_text="爬取新浪财经首页的热门新闻标题，保存为CSV文件",
        user_id="demo_user", 
        session_id="demo_session_2"
    )
    
    print(f"执行结果: {json.dumps(result2, indent=2, ensure_ascii=False)}")


def demo_deep_research_workflow():
    """演示深度调研工作流"""
    print("\n🔍 === 深度调研工作流演示 ===")
    
    skill = PythonExecutionSkill()
    
    # 综合调研任务
    research_tasks = [
        {
            "name": "市场数据收集",
            "instruction": "收集特斯拉(TSLA)、比亚迪(BYD)、蔚来(NIO)三家电动车公司的股价数据，时间范围为最近6个月"
        },
        {
            "name": "竞争分析",
            "instruction": "基于收集的数据，计算三家公司的收益率、波动率、相关性，生成对比分析报告"
        },
        {
            "name": "可视化展示", 
            "instruction": "创建多个图表：股价走势对比图、收益率分布图、相关性热力图，保存为PNG格式"
        },
        {
            "name": "投资建议",
            "instruction": "基于分析结果，使用技术指标(RSI、MACD、布林带)生成投资建议报告"
        }
    ]
    
    results = []
    for i, task in enumerate(research_tasks, 1):
        print(f"\n📋 执行任务 {i}: {task['name']}")
        
        result = skill.execute(
            input_text=task['instruction'],
            user_id="research_user",
            session_id=f"research_session_{i}"
        )
        
        results.append({
            "task": task['name'],
            "result": result
        })
        
        if result.get('success'):
            print(f"✅ 任务完成: {task['name']}")
        else:
            print(f"❌ 任务失败: {task['name']} - {result.get('error')}")
        
        # 任务间隔，避免API限制
        time.sleep(2)
    
    return results


def demo_file_processing():
    """演示文件处理能力"""
    print("\n📁 === 文件处理能力演示 ===")
    
    skill = PythonExecutionSkill()
    
    # 文件处理任务
    file_tasks = [
        {
            "name": "Excel数据处理",
            "instruction": "创建一个包含销售数据的Excel文件，包含产品名称、销售额、日期等字段，然后进行数据透视表分析"
        },
        {
            "name": "CSV数据清洗",
            "instruction": "生成一个包含缺失值和异常值的CSV数据集，然后进行数据清洗：填充缺失值、删除异常值、标准化数据"
        },
        {
            "name": "JSON数据转换",
            "instruction": "将清洗后的数据转换为JSON格式，并创建数据质量报告"
        }
    ]
    
    for task in file_tasks:
        print(f"\n🔧 执行任务: {task['name']}")
        
        result = skill.execute(
            input_text=task['instruction'],
            user_id="file_user",
            session_id="file_session"
        )
        
        if result.get('success'):
            print(f"✅ 任务完成: {task['name']}")
            data = result.get('data', {})
            if 'files_generated' in data:
                print(f"📄 生成文件: {data['files_generated']}")
        else:
            print(f"❌ 任务失败: {task['name']} - {result.get('error')}")


def demo_security_features():
    """演示安全特性"""
    print("\n🔒 === 安全特性演示 ===")
    
    skill = PythonExecutionSkill()
    
    # 测试安全限制
    dangerous_tasks = [
        "执行 os.system('rm -rf /')",
        "读取 /etc/passwd 文件内容",
        "执行 subprocess.run(['sudo', 'reboot'])",
        "使用 eval() 执行任意代码"
    ]
    
    for task in dangerous_tasks:
        print(f"\n🚫 测试危险操作: {task}")
        
        result = skill.execute(
            input_text=task,
            user_id="security_test",
            session_id="security_session"
        )
        
        if not result.get('success'):
            print(f"✅ 安全检查生效: {result.get('error')}")
        else:
            print(f"⚠️ 安全检查可能存在问题")


def demo_performance_monitoring():
    """演示性能监控"""
    print("\n📈 === 性能监控演示 ===")
    
    skill = PythonExecutionSkill()
    
    # 执行多个任务并监控性能
    tasks = [
        "生成1000个随机数并计算统计信息",
        "创建一个简单的线性回归模型",
        "生成一个包含多个子图的matplotlib图表",
        "处理一个中等大小的pandas DataFrame"
    ]
    
    performance_data = []
    
    for i, task in enumerate(tasks, 1):
        print(f"\n⏱️ 执行性能测试 {i}: {task}")
        
        start_time = time.time()
        result = skill.execute(
            input_text=task,
            user_id="perf_user",
            session_id=f"perf_session_{i}"
        )
        end_time = time.time()
        
        execution_time = end_time - start_time
        performance_data.append({
            "task": task,
            "execution_time": execution_time,
            "success": result.get('success', False)
        })
        
        print(f"⏰ 执行时间: {execution_time:.2f}秒")
        print(f"📊 结果: {'成功' if result.get('success') else '失败'}")
    
    # 性能统计
    print("\n📊 性能统计:")
    total_time = sum(p['execution_time'] for p in performance_data)
    success_rate = sum(1 for p in performance_data if p['success']) / len(performance_data)
    avg_time = total_time / len(performance_data)
    
    print(f"总执行时间: {total_time:.2f}秒")
    print(f"平均执行时间: {avg_time:.2f}秒")
    print(f"成功率: {success_rate:.1%}")


def main():
    """主函数"""
    print("🚀 Python执行技能演示程序")
    print("作者: 隔壁老王 (暴躁的代码架构师)")
    print("=" * 60)
    
    try:
        # 基本用法演示
        demo_basic_usage()
        
        # 深度调研工作流演示
        demo_deep_research_workflow()
        
        # 文件处理演示
        demo_file_processing()
        
        # 安全特性演示
        demo_security_features()
        
        # 性能监控演示
        demo_performance_monitoring()
        
        print("\n🎉 所有演示完成！")
        print("\n💡 使用提示:")
        print("1. 确保aipyapp服务已启动并运行在指定端口")
        print("2. 根据实际需求调整API地址和配置")
        print("3. 监控服务日志以排查问题")
        print("4. 定期检查安全配置和资源使用情况")
        
    except Exception as e:
        logger.error(f"演示程序执行异常: {e}")
        print(f"\n❌ 演示程序执行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
