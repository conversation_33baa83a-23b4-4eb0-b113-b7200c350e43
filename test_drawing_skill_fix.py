#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王的绘画技能修复测试脚本
测试修复后的drawing_skill是否能正确处理即梦4.0的返回格式
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, PROJECT_ROOT)

def test_drawing_skill_fix():
    """测试修复后的绘画技能"""
    
    print("🔥 老王测试修复后的绘画技能...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 导入绘画技能
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        print("✅ 成功导入DrawingSkill")
        
        # 初始化绘画技能
        drawing_skill = DrawingSkill()
        print("✅ 成功初始化DrawingSkill")
        
        # 测试提示词
        test_prompt = "一只可爱的小猫咪，坐在阳光下，简单测试"
        
        print(f"📝 测试提示词: {test_prompt}")
        print("🎨 开始绘画测试...")
        print("⚠️ 注意：这可能需要几分钟时间...")
        print("-" * 40)
        
        # 执行绘画
        result = drawing_skill.execute(
            input_text=test_prompt,
            user_id="test_user",
            session_id="test_session"
        )
        
        print("🎯 绘画执行完成！")
        print("=" * 60)
        
        # 分析结果
        if result:
            print("📊 执行结果分析:")
            print(f"结果类型: {type(result)}")
            
            if isinstance(result, dict):
                print("📋 结果详情:")
                for key, value in result.items():
                    if key == "result" and isinstance(value, dict):
                        print(f"  {key}:")
                        for sub_key, sub_value in value.items():
                            print(f"    {sub_key}: {sub_value}")
                    else:
                        print(f"  {key}: {value}")
                
                # 检查是否成功
                success = result.get("success", False)
                message = result.get("message", "")
                
                if success:
                    print("🎉 绘画成功！")
                    result_data = result.get("result", {})
                    if isinstance(result_data, dict):
                        image_url = result_data.get("image_url", "")
                        service = result_data.get("service", "")
                        if image_url:
                            print(f"🖼️ 图片URL: {image_url}")
                            print(f"🔧 使用服务: {service}")
                        else:
                            print("⚠️ 未找到图片URL")
                else:
                    print("❌ 绘画失败")
                    print(f"📝 失败原因: {message}")
                    
                    # 如果是即梦4.0的详细错误信息，显示更多细节
                    if "即梦4.0" in message:
                        print("🔍 即梦4.0错误分析:")
                        print("   这是预期的错误，因为即梦4.0返回空data数组")
                        print("   修复代码已正确识别并处理了这种情况")
                        print("   ✅ 错误处理逻辑工作正常")
            else:
                print(f"⚠️ 结果格式异常: {result}")
        else:
            print("❌ 执行结果为空")
        
        print("=" * 60)
        return result
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_jimeng_method_directly():
    """直接测试即梦方法"""
    
    print("🔧 直接测试即梦生成方法...")
    
    try:
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        drawing_skill = DrawingSkill()
        
        # 直接调用即梦方法
        result = drawing_skill.jimeng_generate_images(
            prompt="测试小猫",
            model="jimeng-4.0",
            negative_prompt="低质量",
            width=512,
            height=1024,
            sample_strength=0.8,
            session_id=drawing_skill.jimeng_session_id
        )
        
        print("📊 即梦方法直接调用结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return result
        
    except Exception as e:
        print(f"❌ 直接测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🔥 老王的绘画技能修复测试")
    print("=" * 60)
    
    # 测试1: 完整的绘画技能测试
    print("🧪 测试1: 完整绘画技能测试")
    result1 = test_drawing_skill_fix()
    
    print("\n" + "=" * 60)
    
    # 测试2: 直接测试即梦方法
    print("🧪 测试2: 直接测试即梦方法")
    result2 = test_jimeng_method_directly()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    
    if result1:
        print("✅ 完整绘画技能测试: 有结果")
    else:
        print("❌ 完整绘画技能测试: 无结果")
    
    if result2:
        print("✅ 即梦方法直接测试: 有结果")
    else:
        print("❌ 即梦方法直接测试: 无结果")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔥 老王修复测试完成！")
