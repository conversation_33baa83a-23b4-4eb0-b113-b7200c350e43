#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试_try_jimeng_service方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill

def test_try_jimeng_service():
    """测试_try_jimeng_service方法"""
    print("🔥 老王测试_try_jimeng_service方法")
    print("=" * 60)
    
    try:
        # 初始化drawing skill
        drawing_skill = DrawingSkill()
        
        # 直接调用_try_jimeng_service方法
        result = drawing_skill._try_jimeng_service(
            ai_image_prompt="流浪地球画面",
            image_width=1080,
            image_height=1920
        )
        
        print(f"📋 返回结果: {result}")
        
        if isinstance(result, dict):
            if result.get("success"):
                print(f"✅ 成功！")
                print(f"   消息: {result.get('message', 'N/A')}")
                print(f"   图片URL: {result.get('result', {}).get('image_url', 'N/A')}")
            else:
                print(f"❌ 失败: {result.get('message', 'N/A')}")
        else:
            print(f"⚠️ 返回格式异常: {type(result)}")
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_try_jimeng_service()
