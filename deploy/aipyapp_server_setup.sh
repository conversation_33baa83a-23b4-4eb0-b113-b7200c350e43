#!/bin/bash

# aipyapp 远程服务器部署脚本
# 作者: 隔壁老王 (暴躁的代码架构师)
# 版本: 1.0.0
# 用途: 在Linux服务器上部署aipyapp Agent模式服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
CONDA_ENV_NAME="aipy"
PYTHON_VERSION="3.11"
AIPYAPP_PORT="8848"
AIPYAPP_HOST="0.0.0.0"
SERVICE_NAME="aipyapp-agent"
WORK_DIR="/opt/aipyapp"
LOG_DIR="/var/log/aipyapp"
CONFIG_DIR="/etc/aipyapp"

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查conda是否安装
    if ! command -v conda &> /dev/null; then
        log_error "未找到conda，请先安装Anaconda或Miniconda"
        exit 1
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        log_info "安装curl..."
        apt-get update && apt-get install -y curl
    fi
    
    # 检查systemctl
    if ! command -v systemctl &> /dev/null; then
        log_error "系统不支持systemd，无法创建系统服务"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 创建工作目录
create_directories() {
    log_info "创建工作目录..."
    
    mkdir -p "$WORK_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$CONFIG_DIR"
    
    # 设置权限
    chmod 755 "$WORK_DIR"
    chmod 755 "$LOG_DIR"
    chmod 755 "$CONFIG_DIR"
    
    log_success "工作目录创建完成"
}

# 创建conda环境
setup_conda_env() {
    log_info "创建conda环境: $CONDA_ENV_NAME"
    
    # 检查环境是否已存在
    if conda env list | grep -q "$CONDA_ENV_NAME"; then
        log_warning "环境 $CONDA_ENV_NAME 已存在，将删除并重新创建"
        conda env remove -n "$CONDA_ENV_NAME" -y
    fi
    
    # 创建新环境
    conda create -n "$CONDA_ENV_NAME" python="$PYTHON_VERSION" -y
    
    log_success "conda环境创建完成"
}

# 安装aipyapp
install_aipyapp() {
    log_info "安装aipyapp..."
    
    # 激活环境并安装
    source "$(conda info --base)/etc/profile.d/conda.sh"
    conda activate "$CONDA_ENV_NAME"
    
    # 安装aipyapp
    pip install aipyapp
    
    # 安装常用数据分析库
    pip install pandas numpy matplotlib seaborn plotly scipy scikit-learn
    pip install requests beautifulsoup4 lxml feedparser
    pip install openpyxl xlsxwriter
    pip install yfinance
    
    # 验证安装
    if python -c "import aipyapp; print('aipyapp安装成功')" 2>/dev/null; then
        log_success "aipyapp安装完成"
    else
        log_error "aipyapp安装失败"
        exit 1
    fi
    
    conda deactivate
}

# 创建配置文件
create_config() {
    log_info "创建aipyapp配置文件..."
    
    # 创建配置目录
    mkdir -p "$CONFIG_DIR/config"
    
    # 创建主配置文件
    cat > "$CONFIG_DIR/aipyapp.toml" << EOF
[llm.deepseek]
type = "deepseek"
api_key = "YOUR_DEEPSEEK_API_KEY"

[server]
host = "$AIPYAPP_HOST"
port = $AIPYAPP_PORT
workers = 4
timeout = 300

[security]
enabled = true
max_execution_time = 600
max_memory_mb = 1024
allowed_modules = [
    "pandas", "numpy", "matplotlib", "seaborn", "plotly",
    "requests", "beautifulsoup4", "json", "csv", "sqlite3",
    "openpyxl", "scipy", "sklearn", "yfinance", "feedparser"
]

[logging]
level = "INFO"
file = "$LOG_DIR/aipyapp.log"
max_size = "100MB"
backup_count = 5
EOF
    
    log_success "配置文件创建完成"
}

# 创建启动脚本
create_startup_script() {
    log_info "创建启动脚本..."
    
    cat > "$WORK_DIR/start_aipyapp.sh" << EOF
#!/bin/bash

# aipyapp启动脚本
# 自动生成，请勿手动修改

set -e

# 激活conda环境
source "\$(conda info --base)/etc/profile.d/conda.sh"
conda activate "$CONDA_ENV_NAME"

# 设置环境变量
export AIPYAPP_CONFIG_DIR="$CONFIG_DIR"
export AIPYAPP_LOG_DIR="$LOG_DIR"

# 启动aipyapp Agent服务
cd "$WORK_DIR"
exec aipy --agent --host "$AIPYAPP_HOST" --port "$AIPYAPP_PORT" --config-dir "$CONFIG_DIR"
EOF
    
    chmod +x "$WORK_DIR/start_aipyapp.sh"
    
    log_success "启动脚本创建完成"
}

# 创建systemd服务
create_systemd_service() {
    log_info "创建systemd服务..."
    
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=AIPyApp Agent Service
After=network.target
Wants=network.target

[Service]
Type=exec
User=root
Group=root
WorkingDirectory=$WORK_DIR
ExecStart=$WORK_DIR/start_aipyapp.sh
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$WORK_DIR $LOG_DIR $CONFIG_DIR /tmp

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    log_success "systemd服务创建完成"
}

# 创建日志轮转配置
create_logrotate_config() {
    log_info "创建日志轮转配置..."
    
    cat > "/etc/logrotate.d/aipyapp" << EOF
$LOG_DIR/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF
    
    log_success "日志轮转配置创建完成"
}

# 创建防火墙规则
setup_firewall() {
    log_info "配置防火墙规则..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian使用ufw
        ufw allow "$AIPYAPP_PORT/tcp"
        log_success "ufw防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL使用firewalld
        firewall-cmd --permanent --add-port="$AIPYAPP_PORT/tcp"
        firewall-cmd --reload
        log_success "firewalld防火墙规则已添加"
    else
        log_warning "未检测到防火墙管理工具，请手动开放端口 $AIPYAPP_PORT"
    fi
}

# 启动服务
start_service() {
    log_info "启动aipyapp服务..."
    
    # 启用服务
    systemctl enable "$SERVICE_NAME"
    
    # 启动服务
    systemctl start "$SERVICE_NAME"
    
    # 检查服务状态
    sleep 5
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "aipyapp服务启动成功"
        
        # 显示服务状态
        systemctl status "$SERVICE_NAME" --no-pager -l
        
        # 测试API
        log_info "测试API连接..."
        if curl -s "http://localhost:$AIPYAPP_PORT/health" > /dev/null; then
            log_success "API测试成功，服务运行正常"
        else
            log_warning "API测试失败，请检查服务日志"
        fi
    else
        log_error "aipyapp服务启动失败"
        systemctl status "$SERVICE_NAME" --no-pager -l
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 aipyapp部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "服务名称: $SERVICE_NAME"
    echo "监听地址: $AIPYAPP_HOST:$AIPYAPP_PORT"
    echo "工作目录: $WORK_DIR"
    echo "日志目录: $LOG_DIR"
    echo "配置目录: $CONFIG_DIR"
    echo "conda环境: $CONDA_ENV_NAME"
    echo
    echo "=== 常用命令 ==="
    echo "启动服务: systemctl start $SERVICE_NAME"
    echo "停止服务: systemctl stop $SERVICE_NAME"
    echo "重启服务: systemctl restart $SERVICE_NAME"
    echo "查看状态: systemctl status $SERVICE_NAME"
    echo "查看日志: journalctl -u $SERVICE_NAME -f"
    echo
    echo "=== API测试 ==="
    echo "健康检查: curl http://localhost:$AIPYAPP_PORT/health"
    echo "API文档: http://localhost:$AIPYAPP_PORT/docs"
    echo
    echo "⚠️  重要提醒："
    echo "1. 请修改配置文件 $CONFIG_DIR/aipyapp.toml 中的API密钥"
    echo "2. 根据需要调整安全配置和资源限制"
    echo "3. 定期检查日志文件和服务状态"
}

# 主函数
main() {
    log_info "开始部署aipyapp服务..."
    
    check_root
    check_dependencies
    create_directories
    setup_conda_env
    install_aipyapp
    create_config
    create_startup_script
    create_systemd_service
    create_logrotate_config
    setup_firewall
    start_service
    show_deployment_info
    
    log_success "🚀 部署完成！aipyapp服务已启动并运行"
}

# 执行主函数
main "$@"
