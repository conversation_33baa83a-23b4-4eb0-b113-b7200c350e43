#!/bin/bash

# 林嫣然数字生命体系统 - Linux服务器版本服务管理脚本
# 版本: v3.1.1-linux
# 优化Linux服务器环境和conda虚拟环境支持

set -euo pipefail

# ==================== 配置变量 ====================

# 项目基础配置
PROJECT_NAME="yanran-digital-life"
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MAIN_SCRIPT="main.py"
PYTHON_CMD="python"

# 服务配置
ENVIRONMENT="${ENVIRONMENT:-production}"
LOG_LEVEL="${LOG_LEVEL:-INFO}"
API_HOST="${API_HOST:-127.0.0.1}"
API_PORT="${API_PORT:-56839}"
WEBSOCKET_PORT="${WEBSOCKET_PORT:-8765}"

# 🔥 老王修复：MySQL连接配置，解决Malformed packet问题
export MYSQL_OPT_MAX_ALLOWED_PACKET=16777216  # 16MB
export MYSQL_OPT_CONNECT_TIMEOUT=10
export MYSQL_OPT_READ_TIMEOUT=30
export MYSQL_OPT_WRITE_TIMEOUT=30
export MYSQL_OPT_RECONNECT=1

# 路径配置 - Linux生产环境
PID_FILE="${PID_FILE:-$PROJECT_DIR/yanran.pid}"
LOG_DIR="${LOG_DIR:-$PROJECT_DIR/logs}"
DATA_DIR="${DATA_DIR:-$PROJECT_DIR/data}"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_DIR/backups}"
CONFIG_DIR="${CONFIG_DIR:-$PROJECT_DIR/config}"

# Conda环境配置 - 🔥 老王修复：支持默认base环境
CONDA_ENV_NAME="${CONDA_ENV_NAME:-base}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# ==================== 工具函数 ====================

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示横幅
show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   🌸 林嫣然数字生命体系统 🌸                   ║"
    echo "║                  Linux服务器版本管理工具                       ║"
    echo "║                        Version 3.1.1                        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查并激活conda环境
setup_conda_environment() {
    log_info "设置Conda环境..."
    
    # 检查conda是否可用
    if ! command -v conda &> /dev/null; then
        log_error "Conda未安装或不在PATH中"
        return 1
    fi
    
    # 初始化conda
    eval "$(conda shell.bash hook)" 2>/dev/null || {
        log_warn "无法初始化conda shell hook，尝试source conda.sh"
        local conda_base=$(conda info --base 2>/dev/null)
        if [[ -n "$conda_base" && -f "$conda_base/etc/profile.d/conda.sh" ]]; then
            source "$conda_base/etc/profile.d/conda.sh"
        else
            log_error "无法找到conda初始化脚本"
            return 1
        fi
    }
    
    # 检查当前环境
    local current_env="${CONDA_DEFAULT_ENV:-base}"
    log_info "当前Conda环境: $current_env"
    
    # 如果不是目标环境，尝试激活
    if [[ "$current_env" != "$CONDA_ENV_NAME" ]]; then
        log_info "激活Conda环境: $CONDA_ENV_NAME"
        conda activate "$CONDA_ENV_NAME" 2>/dev/null && {
            log_success "成功激活Conda环境: $CONDA_ENV_NAME"
            PYTHON_CMD="python"
        } || {
            log_error "无法激活Conda环境: $CONDA_ENV_NAME"
            log_info "可用环境列表:"
            conda env list
            return 1
        }
    else
        log_success "已在目标Conda环境: $CONDA_ENV_NAME"
        PYTHON_CMD="python"
    fi
    
    return 0
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 设置conda环境
    if ! setup_conda_environment; then
        log_error "Conda环境设置失败"
        return 1
    fi
    
    # 检查Python版本
    if ! command -v $PYTHON_CMD &> /dev/null; then
        log_error "Python未安装或不在PATH中"
        return 1
    fi
    
    local python_version=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    # 检查Python包
    local required_packages=("flask" "websockets" "redis")
    local mysql_packages=("mysql.connector" "pymysql")
    local missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if ! $PYTHON_CMD -c "import $package" 2>/dev/null; then
            missing_packages+=("$package")
        fi
    done
    
    # 🔥 老王修复：检查MySQL连接包
    local mysql_available=false
    for mysql_pkg in "${mysql_packages[@]}"; do
        if $PYTHON_CMD -c "import $mysql_pkg" 2>/dev/null; then
            mysql_available=true
            log_info "MySQL连接包可用: $mysql_pkg"
            break
        fi
    done
    
    if [[ "$mysql_available" == false ]]; then
        missing_packages+=("mysql-connector-python或pymysql")
    fi
    
    if [[ ${#missing_packages[@]} -gt 0 ]]; then
        log_warn "缺少Python包: ${missing_packages[*]}"
        log_info "请在$CONDA_ENV_NAME环境中安装缺少的包"
    else
        log_success "所有必要的Python包已安装"
    fi
    
    # 检查端口占用
    check_port_usage
    
    return 0
}

# 检查端口占用
check_port_usage() {
    local ports=("$API_PORT" "$WEBSOCKET_PORT")
    
    for port in "${ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep ":$port " >/dev/null; then
            local pid=$(lsof -ti :$port 2>/dev/null | head -1 || echo "unknown")
            log_warn "端口 $port 已被占用 (PID: $pid)"
        fi
    done
}

# 创建必要目录
create_directories() {
    for dir in "$LOG_DIR" "$DATA_DIR" "$BACKUP_DIR"; do
        if [[ ! -d "$dir" ]]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        fi
    done
}

# 获取进程PID
get_pid() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "$pid"
            return 0
        else
            log_warn "PID文件存在但进程已停止，清理PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    return 1
}

# 检查服务状态
is_running() {
    get_pid > /dev/null 2>&1
}

# 等待进程启动
wait_for_start() {
    local max_wait=${1:-30}
    local count=0
    
    log_info "等待服务启动..."
    
    while [[ $count -lt $max_wait ]]; do
        if is_running; then
            log_success "服务启动成功"
            return 0
        fi
        
        sleep 1
        ((count++))
        
        if [[ $((count % 5)) -eq 0 ]]; then
            log_info "等待中... ($count/$max_wait 秒)"
        fi
    done
    
    log_error "服务启动超时"
    return 1
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 给服务更多时间启动
    sleep 3
    
    # 检查API服务
    local max_attempts=5
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s --connect-timeout 5 "http://localhost:$API_PORT/api/health" > /dev/null 2>&1; then
            log_success "API服务健康检查通过"
            return 0
        else
            log_info "健康检查尝试 $attempt/$max_attempts 失败，等待重试..."
            sleep 2
            ((attempt++))
        fi
    done
    
    log_error "API服务健康检查失败"
    return 1
}

# 显示服务信息
show_service_info() {
    echo
    echo -e "${CYAN}🌸 服务信息 🌸${NC}"
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    if is_running; then
        local pid=$(get_pid)
        echo -e "📊 服务状态: ${GREEN}运行中${NC} (PID: $pid)"
        echo -e "🌐 API地址: http://$API_HOST:$API_PORT"
        echo -e "📡 WebSocket端口: $WEBSOCKET_PORT"
        echo -e "🐍 Python环境: $CONDA_ENV_NAME"
        echo -e "📁 项目目录: $PROJECT_DIR"
        echo -e "📝 日志目录: $LOG_DIR"
        echo -e "💾 数据目录: $DATA_DIR"
    else
        echo -e "📊 服务状态: ${RED}已停止${NC}"
    fi
    
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo
}

# ==================== 主要功能函数 ====================

# 启动服务
start_service() {
    log_info "启动林嫣然数字生命体系统..."
    
    if is_running; then
        local pid=$(get_pid)
        log_warn "服务已在运行 (PID: $pid)"
        show_service_info
        return 0
    fi
    
    # 检查依赖
    if ! check_dependencies; then
        log_error "依赖检查失败"
        return 1
    fi
    
    # 创建目录
    create_directories
    
    # 切换到项目目录
    cd "$PROJECT_DIR"
    
    # 确保conda环境已激活
    if ! setup_conda_environment; then
        log_error "无法设置Conda环境"
        return 1
    fi
    
    # 🔥 老王修复：设置MySQL环境变量解决packet问题
    export PYTHONPATH="$PROJECT_DIR:${PYTHONPATH:-}"
    export MYSQL_OPT_MAX_ALLOWED_PACKET=16777216
    export MYSQL_OPT_CONNECT_TIMEOUT=10
    export MYSQL_OPT_READ_TIMEOUT=30
    export MYSQL_OPT_WRITE_TIMEOUT=30
    
    # 构建启动命令
    local start_cmd="$PYTHON_CMD $MAIN_SCRIPT"
    start_cmd+=" --log-level $LOG_LEVEL"
    start_cmd+=" --api-host $API_HOST"
    start_cmd+=" --api-port $API_PORT"
    start_cmd+=" --api-only"  # 服务器环境只启动API，不启动CLI
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        start_cmd+=" --config-dir $CONFIG_DIR"
    fi
    
    # 启动服务
    log_info "执行启动命令: $start_cmd"
    
    # 后台运行
    nohup $start_cmd > "$LOG_DIR/yanran.out" 2> "$LOG_DIR/yanran.err" &
    local pid=$!
    echo $pid > "$PID_FILE"
    log_info "服务已启动 (PID: $pid)"
    
    # 等待启动完成
    if wait_for_start 30; then
        # 执行健康检查
        if health_check; then
            log_success "🌸 林嫣然数字生命体系统启动成功！"
            show_service_info
        else
            log_error "健康检查失败，请检查日志文件"
            log_info "输出日志: $LOG_DIR/yanran.out"
            log_info "错误日志: $LOG_DIR/yanran.err"
            return 1
        fi
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止林嫣然数字生命体系统..."
    
    if ! is_running; then
        log_warn "服务未运行"
        return 0
    fi
    
    local pid=$(get_pid)
    log_info "发送停止信号到进程 $pid"
    
    # 优雅停止
    kill -TERM "$pid" 2>/dev/null || true
    
    # 等待停止
    local count=0
    while [[ $count -lt 30 ]]; do
        if ! is_running; then
            rm -f "$PID_FILE"
            log_success "🌸 林嫣然数字生命体系统已停止"
            return 0
        fi
        sleep 1
        ((count++))
    done
    
    log_warn "优雅停止超时，强制终止进程"
    kill -KILL "$pid" 2>/dev/null || true
    rm -f "$PID_FILE"
    log_success "服务已强制停止"
}

# 重启服务
restart_service() {
    log_info "重启林嫣然数字生命体系统..."
    stop_service
    sleep 2
    start_service
}

# 查看状态
status_service() {
    show_service_info
}

# 查看日志
logs_service() {
    local lines=${1:-50}
    
    echo -e "${CYAN}🌸 最近 $lines 行日志 🌸${NC}"
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    if [[ -f "$LOG_DIR/yanran.out" ]]; then
        echo -e "${GREEN}📤 输出日志:${NC}"
        tail -n $lines "$LOG_DIR/yanran.out"
        echo
    fi
    
    if [[ -f "$LOG_DIR/yanran.err" ]]; then
        echo -e "${RED}📥 错误日志:${NC}"
        tail -n $lines "$LOG_DIR/yanran.err"
    fi
}

# 🔥 老王修复：MySQL连接测试和修复
mysql_test() {
    echo -e "${CYAN}🌸 MySQL连接诊断和修复 🌸${NC}"
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    # 设置MySQL环境变量
    export MYSQL_OPT_MAX_ALLOWED_PACKET=16777216
    export MYSQL_OPT_CONNECT_TIMEOUT=10
    export MYSQL_OPT_READ_TIMEOUT=30
    export MYSQL_OPT_WRITE_TIMEOUT=30
    export MYSQL_OPT_RECONNECT=1
    
    if ! setup_conda_environment; then
        log_error "无法设置Conda环境"
        return 1
    fi
    
    echo -e "🔧 正在执行MySQL连接诊断..."
    
    $PYTHON_CMD -c "
import sys
sys.path.append('$PROJECT_DIR')

def test_mysql_connection():
    try:
        print('1. 测试MySQL连接包导入...')
        import mysql.connector
        print('   ✅ mysql.connector导入成功')
        
        print('2. 测试连接器初始化...')
        from connectors.database.mysql_connector import MySQLConnector
        mysql = MySQLConnector()
        print('   ✅ MySQL连接器初始化成功')
        
        print('3. 测试连接可用性...')
        if mysql.is_available():
            print('   ✅ MySQL连接器可用')
        else:
            print('   ❌ MySQL连接器不可用')
            return False
            
        print('4. 测试基础查询...')
        success, result, error = mysql.query('SELECT 1 as test')
        if success:
            print('   ✅ 基础查询成功')
            if result:
                print(f'   📊 查询结果: {result[0]}')
        else:
            print(f'   ❌ 基础查询失败: {error}')
            return False
            
        print('5. 测试数据库表访问...')
        success, result, error = mysql.query('SHOW TABLES')
        if success:
            print(f'   ✅ 表访问成功，找到 {len(result) if result else 0} 个表')
        else:
            print(f'   ❌ 表访问失败: {error}')
            
        print('6. 测试连接池状态...')
        status = mysql.get_status()
        print(f'   📈 连接统计: {status}')
        
        return True
        
    except Exception as e:
        print(f'❌ MySQL诊断失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    if test_mysql_connection():
        print('\\n✅ MySQL连接诊断通过')
    else:
        print('\\n❌ MySQL连接诊断失败')
        print('\\n🔧 建议修复措施:')
        print('   1. 检查网络连接到124.221.30.195:3306')
        print('   2. 确认MySQL服务器正在运行')
        print('   3. 验证用户名密码是否正确')
        print('   4. 检查防火墙设置')
        print('   5. 尝试重启服务: ./yanran_service_linux.sh restart')
"
    
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
}

# 环境检查
env_check() {
    echo -e "${CYAN}🌸 环境检查 🌸${NC}"
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    # 检查操作系统
    echo -e "🖥️  操作系统: $(uname -s) $(uname -r)"
    
    # 检查conda
    if command -v conda &> /dev/null; then
        echo -e "🐍 Conda版本: $(conda --version)"
        echo -e "🌍 Conda环境列表:"
        conda env list | grep -E "^\s*\*?\s*\w+" | sed 's/^/   /'
    else
        echo -e "❌ Conda未安装"
    fi
    
    # 检查Python
    if setup_conda_environment; then
        echo -e "🐍 Python版本: $($PYTHON_CMD --version)"
        echo -e "📦 已安装的关键包:"
        for pkg in flask websockets redis mysql-connector-python; do
            if $PYTHON_CMD -c "import $pkg; print(f'   ✅ $pkg')" 2>/dev/null; then
                :
            else
                echo -e "   ❌ $pkg (未安装)"
            fi
        done
    fi
    
    # 检查端口
    echo -e "🔌 端口状态:"
    for port in $API_PORT $WEBSOCKET_PORT; do
        if netstat -tuln 2>/dev/null | grep ":$port " >/dev/null; then
            echo -e "   ❌ 端口 $port 已被占用"
        else
            echo -e "   ✅ 端口 $port 可用"
        fi
    done
    
    # 🔥 老王修复：MySQL连接测试
    echo -e "🗄️  MySQL连接测试:"
    if setup_conda_environment; then
        if $PYTHON_CMD -c "
import sys
sys.path.append('$PROJECT_DIR')
try:
    from connectors.database.mysql_connector import MySQLConnector
    mysql = MySQLConnector()
    if mysql.is_available():
        success, result, error = mysql.query('SELECT 1 as test')
        if success:
            print('   ✅ MySQL连接正常')
        else:
            print('   ❌ MySQL查询失败: ' + str(error))
    else:
        print('   ❌ MySQL连接器不可用')
except Exception as e:
    print('   ❌ MySQL连接测试失败: ' + str(e))
" 2>/dev/null; then
            :
        else
            echo -e "   ❌ MySQL连接测试执行失败"
        fi
    fi
    
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
}

# 显示帮助
show_help() {
    echo -e "${CYAN}🌸 林嫣然数字生命体系统 - Linux服务器版本管理工具 🌸${NC}"
    echo
    echo -e "${WHITE}用法:${NC}"
    echo "  $0 {start|stop|restart|status|logs|env|help}"
    echo
    echo -e "${WHITE}命令:${NC}"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看服务状态"
    echo "  logs        查看服务日志"
    echo "  env         检查运行环境"
    echo "  mysql-test  MySQL连接诊断和修复"
    echo "  help        显示此帮助信息"
    echo
    echo -e "${WHITE}环境变量:${NC}"
    echo "  CONDA_ENV_NAME=$CONDA_ENV_NAME"
    echo "  API_PORT=$API_PORT"
    echo "  LOG_LEVEL=$LOG_LEVEL"
    echo "  ENVIRONMENT=$ENVIRONMENT"
    echo
    echo -e "${WHITE}注意事项:${NC}"
    echo "  1. 请确保已安装conda并创建了linyanran环境"
    echo "  2. 服务将在后台运行，仅启动API接口"
    echo "  3. 使用 'logs' 命令查看详细运行日志"
    echo
}

# ==================== 主程序入口 ====================

main() {
    case "${1:-help}" in
        start)
            show_banner
            start_service
            ;;
        stop)
            show_banner
            stop_service
            ;;
        restart)
            show_banner
            restart_service
            ;;
        status)
            show_banner
            status_service
            ;;
        logs)
            logs_service "${2:-50}"
            ;;
        env)
            show_banner
            env_check
            ;;
        mysql-test)
            mysql_test
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@" 