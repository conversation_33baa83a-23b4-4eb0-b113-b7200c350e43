#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
林嫣然数字生命体上帝视角监测系统启动器
==========================================

快速启动和配置上帝视角监测系统

使用方法:
    python start_god_view.py                    # 使用默认配置启动
    python start_god_view.py --config custom    # 使用自定义配置
    python start_god_view.py --level quantum    # 设置监测级别
    python start_god_view.py --help            # 显示帮助信息

作者: AI Assistant
创建日期: 2025-06-27
版本: 1.0
"""

import asyncio
import argparse
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from god_view_monitor import GodViewMonitor, MonitorLevel, GodViewCLI

def load_config(config_path: str = None) -> dict:
    """加载配置文件"""
    if config_path is None:
        config_path = "config/god_view_monitor.json"
    
    config_file = project_root / config_path
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_file}")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return {}
    else:
        print(f"⚠️ 配置文件不存在: {config_file}，使用默认配置")
        return {}

def create_default_config():
    """创建默认配置文件"""
    default_config = {
        "monitor_config": {
            "monitor_level": "deep",
            "update_interval": 1.0,
            "display_refresh_rate": 2.0
        },
        "connection_config": {
            "websocket_url": "ws://localhost:8765",
            "api_base_url": "http://localhost:56839/api"
        }
    }
    
    config_dir = project_root / "config"
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / "god_view_monitor.json"
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=4, ensure_ascii=False)
        print(f"✅ 默认配置文件已创建: {config_file}")
        return default_config
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return default_config

def validate_environment():
    """验证运行环境"""
    print("🔍 验证运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    
    # 检查必要的依赖
    required_modules = ['asyncio', 'websockets', 'requests']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少必要依赖: {', '.join(missing_modules)}")
        print("请运行: pip install websockets requests")
        return False
    
    print("✅ 运行环境验证通过")
    return True

def check_digital_life_status():
    """检查数字生命体系统状态"""
    print("🔍 检查数字生命体系统状态...")
    
    try:
        import requests
        
        # 检查API服务
        api_url = "http://localhost:56839/api/health"
        response = requests.get(api_url, timeout=5)
        
        if response.status_code == 200:
            print("✅ 数字生命体API服务正常")
            return True
        else:
            print(f"⚠️ 数字生命体API服务异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到数字生命体API服务")
        print("请确保数字生命体系统正在运行 (python main.py)")
        return False
    except Exception as e:
        print(f"❌ 检查数字生命体状态失败: {e}")
        return False

def print_startup_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🔮 林嫣然数字生命体上帝视角监测系统 🔮                    ║
║                                                                              ║
║                          全面监测数字生命的所有维度                          ║
║                                                                              ║
║  🧠 意识状态 | 💖 情感状态 | 🎯 认知状态 | ⚡ 技能状态 | 🌟 生理状态  ║
║                                                                              ║
║                    📊 活动轨迹 | 🧬 心理分析 | 🌊 事件流                    ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def print_help():
    """打印帮助信息"""
    help_text = """
🔮 林嫣然数字生命体上帝视角监测系统

使用方法:
    python start_god_view.py [选项]

选项:
    --config PATH       指定配置文件路径 (默认: config/god_view_monitor.json)
    --level LEVEL       设置监测级别 (surface/deep/neural/quantum, 默认: deep)
    --interval SECONDS  设置更新间隔 (默认: 1.0秒)
    --no-check         跳过系统状态检查
    --create-config    创建默认配置文件
    --help, -h         显示此帮助信息

监测级别说明:
    surface  - 表面监测，基础状态信息
    deep     - 深度监测，详细状态分析 (推荐)
    neural   - 神经级别监测，底层神经活动
    quantum  - 量子级别监测，最深层意识活动

示例:
    python start_god_view.py
    python start_god_view.py --level quantum --interval 0.5
    python start_god_view.py --config my_config.json --no-check

按 Ctrl+C 停止监测系统
"""
    print(help_text)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="林嫣然数字生命体上帝视角监测系统",
        add_help=False
    )
    
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--level', type=str, choices=['surface', 'deep', 'neural', 'quantum'],
                       default='deep', help='监测级别')
    parser.add_argument('--interval', type=float, default=1.0, help='更新间隔(秒)')
    parser.add_argument('--no-check', action='store_true', help='跳过系统状态检查')
    parser.add_argument('--create-config', action='store_true', help='创建默认配置文件')
    parser.add_argument('--help', '-h', action='store_true', help='显示帮助信息')
    
    args = parser.parse_args()
    
    # 显示帮助信息
    if args.help:
        print_help()
        return
    
    # 创建默认配置文件
    if args.create_config:
        create_default_config()
        return
    
    # 打印启动横幅
    print_startup_banner()
    
    # 验证运行环境
    if not validate_environment():
        return
    
    # 检查数字生命体系统状态
    if not args.no_check:
        if not check_digital_life_status():
            print("\n⚠️ 数字生命体系统未运行或异常")
            print("你可以使用 --no-check 参数跳过此检查")
            choice = input("是否继续启动监测系统? (y/N): ").strip().lower()
            if choice not in ['y', 'yes']:
                print("监测系统启动已取消")
                return
    
    # 加载配置
    config = load_config(args.config)
    
    # 应用命令行参数
    if not config:
        config = create_default_config()
    
    # 更新配置
    if 'monitor_config' not in config:
        config['monitor_config'] = {}
    
    config['monitor_config']['monitor_level'] = args.level
    config['monitor_config']['update_interval'] = args.interval
    
    print(f"🎯 监测级别: {args.level}")
    print(f"⏱️ 更新间隔: {args.interval}秒")
    print(f"🔗 WebSocket: {config.get('connection_config', {}).get('websocket_url', 'ws://localhost:8765')}")
    print(f"🌐 API地址: {config.get('connection_config', {}).get('api_base_url', 'http://localhost:56839/api')}")
    
    print("\n🚀 正在启动上帝视角监测系统...")
    print("按 Ctrl+C 停止监测")
    
    try:
        # 创建并启动监测器
        cli = GodViewCLI()
        cli.monitor = GodViewMonitor(config)
        
        await cli.start()
        
    except KeyboardInterrupt:
        print("\n\n🛑 收到停止信号，正在安全关闭...")
    except Exception as e:
        print(f"\n❌ 系统异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🔮 上帝视角监测系统已停止")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常退出: {e}") 