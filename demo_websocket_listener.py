#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
林嫣然数字生命 WebSocket 主动消息接收示例
演示如何实时接收数字生命的主动服务和交互信息
"""

import asyncio
import json
import websockets
import argparse
import signal
import sys
from datetime import datetime
from typing import Dict, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YanranWebSocketListener:
    """林嫣然数字生命WebSocket监听器"""
    
    def __init__(self, uri: str = "ws://localhost:8765", user_id: str = "demo_user"):
        self.uri = uri
        self.user_id = user_id
        self.websocket = None
        self.is_running = False
        self.stats = {
            "connected_at": None,
            "total_messages": 0,
            "message_types": {},
            "priorities": {},
            "levels": {}
        }
        
    async def connect(self):
        """连接到WebSocket服务"""
        try:
            print(f"🔌 正在连接到: {self.uri}")
            # 使用更简单的连接方法
            self.websocket = await websockets.connect(self.uri, timeout=10)
            self.is_running = True
            self.stats["connected_at"] = datetime.now()
            print(f"✅ 连接成功！")
            return True
        except websockets.exceptions.ConnectionClosed:
            print(f"❌ 连接被服务器关闭")
            print(f"💡 服务器可能正在重启或配置有误")
            return False
        except websockets.exceptions.InvalidURI:
            print(f"❌ 无效的WebSocket地址: {self.uri}")
            return False
        except ConnectionRefusedError:
            print(f"❌ 连接被拒绝")
            print(f"💡 请检查:")
            print(f"   1. 主服务是否已启动: python main.py")
            print(f"   2. WebSocket服务是否正常运行")
            print(f"   3. 端口8765是否被正确监听")
            return False
        except asyncio.TimeoutError:
            print(f"❌ 连接超时")
            print(f"💡 可能的原因:")
            print(f"   1. 网络延迟过高")
            print(f"   2. 服务器响应慢")
            print(f"   3. 防火墙阻止连接")
            return False
        except Exception as e:
            print(f"❌ 连接失败: {type(e).__name__}: {e}")
            print(f"💡 请确认主服务已启动 (python main.py) 且WebSocket服务正常运行")
            print(f"🔍 可以检查端口占用: lsof -i :8765")
            print(f"🔍 可以查看主服务日志中的WebSocket启动信息")
            return False
    
    async def subscribe(self, message_types: list = None):
        """订阅消息类型"""
        if not self.websocket:
            print("❌ 未连接到WebSocket服务")
            return False
            
        try:
            # 订阅消息
            subscribe_msg = {
                "type": "subscribe",
                "message_types": message_types or ["all"],
                "user_id": self.user_id
            }
            
            await self.websocket.send(json.dumps(subscribe_msg))
            print(f"📡 已订阅消息类型: {message_types or ['all']}")
            print(f"👤 用户ID: {self.user_id}")
            
            return True
        except Exception as e:
            print(f"❌ 订阅失败: {e}")
            return False
    
    def format_message_display(self, message: Dict[str, Any]) -> str:
        """格式化消息显示"""
        msg_type = message.get('type', 'unknown')
        target_user = message.get('target_user_id', 'N/A')
        level = message.get('message_level', 'N/A')
        priority = message.get('priority', 'N/A')
        timestamp = message.get('timestamp', 0)
        msg_id = message.get('message_id', 'N/A')
        data = message.get('data', {})
        
        # 格式化时间
        if timestamp:
            time_str = datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
        else:
            time_str = 'N/A'
        
        # 选择合适的emoji
        type_emojis = {
            'reminder_notification': '⏰',
            'proactive_expression': '💬',
            'emotion_update': '😊',
            'cognition_update': '🧠',
            'skill_execution': '🛠️',
            'system_status': '⚙️',
            'chat_response': '💭',
            'market_insight': '📈',
            'creative_expression': '🎨'
        }
        
        priority_emojis = {
            'urgent': '🚨',
            'high': '🔴',
            'normal': '🟡',
            'low': '🟢'
        }
        
        level_emojis = {
            'user': '👤',
            'public': '🌐'
        }
        
        emoji = type_emojis.get(msg_type, '📨')
        priority_emoji = priority_emojis.get(priority, '⚪')
        level_emoji = level_emojis.get(level, '❓')
        
        # 构建显示内容
        lines = [
            f"{emoji} 【{msg_type.upper()}】",
            f"   🕐 时间: {time_str}",
            f"   {level_emoji} 级别: {level}",
            f"   {priority_emoji} 优先级: {priority}",
            f"   👤 目标用户: {target_user}",
            f"   🆔 消息ID: {msg_id}"
        ]
        
        # 添加数据内容
        if data:
            lines.append("   📦 数据内容:")
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    value_str = json.dumps(value, ensure_ascii=False)
                else:
                    value_str = str(value)
                lines.append(f"      {key}: {value_str}")
        
        return "\n".join(lines)
    
    def update_stats(self, message: Dict[str, Any]):
        """更新统计信息"""
        self.stats["total_messages"] += 1
        
        msg_type = message.get('type', 'unknown')
        priority = message.get('priority', 'unknown')
        level = message.get('message_level', 'unknown')
        
        self.stats["message_types"][msg_type] = self.stats["message_types"].get(msg_type, 0) + 1
        self.stats["priorities"][priority] = self.stats["priorities"].get(priority, 0) + 1
        self.stats["levels"][level] = self.stats["levels"].get(level, 0) + 1
    
    def print_stats(self):
        """打印统计信息"""
        if not self.stats["connected_at"]:
            return
            
        uptime = datetime.now() - self.stats["connected_at"]
        
        print("\n" + "=" * 50)
        print("📊 WebSocket监听统计")
        print("=" * 50)
        print(f"⏱️  连接时长: {uptime}")
        print(f"📨 总消息数: {self.stats['total_messages']}")
        
        if self.stats["message_types"]:
            print(f"📋 消息类型分布:")
            for msg_type, count in self.stats["message_types"].items():
                print(f"   {msg_type}: {count}")
        
        if self.stats["priorities"]:
            print(f"⚡ 优先级分布:")
            for priority, count in self.stats["priorities"].items():
                print(f"   {priority}: {count}")
        
        if self.stats["levels"]:
            print(f"📊 级别分布:")
            for level, count in self.stats["levels"].items():
                print(f"   {level}: {count}")
        
        print("=" * 50)
    
    async def listen(self):
        """监听WebSocket消息"""
        if not self.websocket:
            print("❌ 未连接到WebSocket服务")
            return
            
        print(f"👂 开始监听消息...")
        print(f"💡 按 Ctrl+C 停止监听")
        print("-" * 50)
        
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    
                    # 处理不同类型的消息
                    msg_type = data.get('type')
                    
                    if msg_type == 'welcome':
                        print(f"🌸 {data.get('message', '欢迎连接')}")
                        print(f"🔗 客户端ID: {data.get('client_id')}")
                        if 'supported_types' in data:
                            print(f"📋 支持的消息类型: {', '.join(data['supported_types'])}")
                        continue
                        
                    elif msg_type == 'subscription_confirmed':
                        print(f"✅ 订阅确认: {', '.join(data.get('subscribed_types', []))}")
                        continue
                        
                    elif msg_type == 'pong':
                        # 心跳响应，不显示
                        continue
                    
                    # 显示业务消息
                    print(f"\n📨 收到新消息:")
                    print(self.format_message_display(data))
                    print("-" * 50)
                    
                    # 更新统计
                    self.update_stats(data)
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                except Exception as e:
                    print(f"❌ 处理消息失败: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("🔌 WebSocket连接已关闭")
        except Exception as e:
            print(f"❌ 监听过程中发生错误: {e}")
        finally:
            self.is_running = False
    
    async def send_ping(self):
        """发送心跳"""
        while self.is_running and self.websocket:
            try:
                await self.websocket.send(json.dumps({"type": "ping"}))
                await asyncio.sleep(30)  # 每30秒发送一次心跳
            except Exception:
                break
    
    async def close(self):
        """关闭连接"""
        self.is_running = False
        if self.websocket:
            await self.websocket.close()
            print("🔌 WebSocket连接已关闭")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='林嫣然数字生命WebSocket监听器')
    parser.add_argument('--uri', default='ws://localhost:8765', help='WebSocket服务地址')
    parser.add_argument('--user', default='demo_user', help='用户ID')
    parser.add_argument('--types', nargs='*', help='订阅的消息类型（默认订阅所有）')
    parser.add_argument('--stats-interval', type=int, default=60, help='统计信息显示间隔（秒）')
    
    args = parser.parse_args()
    
    print("🌸 林嫣然数字生命WebSocket主动消息监听器")
    print("=" * 60)
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔗 服务地址: {args.uri}")
    print(f"👤 用户ID: {args.user}")
    
    # 创建监听器
    listener = YanranWebSocketListener(args.uri, args.user)
    
    # 设置信号处理
    def signal_handler():
        print("\n🛑 收到停止信号，正在关闭...")
        asyncio.create_task(listener.close())
    
    # 注册信号处理器
    for sig in [signal.SIGINT, signal.SIGTERM]:
        signal.signal(sig, lambda s, f: signal_handler())
    
    try:
        # 连接到WebSocket
        if not await listener.connect():
            return 1
        
        # 订阅消息
        if not await listener.subscribe(args.types):
            return 1
        
        # 创建任务
        listen_task = asyncio.create_task(listener.listen())
        ping_task = asyncio.create_task(listener.send_ping())
        
        # 定期显示统计信息
        async def show_stats():
            while listener.is_running:
                await asyncio.sleep(args.stats_interval)
                if listener.is_running:
                    listener.print_stats()
        
        stats_task = asyncio.create_task(show_stats())
        
        # 等待任务完成
        await asyncio.gather(listen_task, ping_task, stats_task, return_exceptions=True)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
    finally:
        await listener.close()
        listener.print_stats()
        print("\n🎉 监听器已停止")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 再见！")
        sys.exit(0) 