#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王调试session_id问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill
import requests

def debug_session_id():
    """调试session_id问题"""
    print("🔥 老王调试session_id问题")
    print("=" * 60)
    
    try:
        # 初始化drawing skill
        drawing_skill = DrawingSkill()
        
        # 检查session_id
        print(f"📋 Drawing skill的session_id:")
        print(f"   完整值: {drawing_skill.jimeng_session_id}")
        print(f"   长度: {len(drawing_skill.jimeng_session_id)}")
        print(f"   类型: {type(drawing_skill.jimeng_session_id)}")
        
        # 检查配置文件中的session_id
        jimeng_config = drawing_skill.config.get("jimeng", {})
        config_session_id = jimeng_config.get("session_id", "")
        print(f"\n📋 配置文件中的session_id:")
        print(f"   完整值: {config_session_id}")
        print(f"   长度: {len(config_session_id)}")
        
        # 分割session_id（如果有多个）
        if "," in drawing_skill.jimeng_session_id:
            session_ids = drawing_skill.jimeng_session_id.split(",")
            print(f"\n📋 分割后的session_id列表:")
            for i, sid in enumerate(session_ids):
                print(f"   {i+1}: {sid.strip()}")
        
        # 测试第一个session_id
        test_session_id = drawing_skill.jimeng_session_id.split(",")[0].strip()
        print(f"\n🎯 测试第一个session_id: {test_session_id}")
        
        url = "http://**************:47653/v1/images/generations"
        headers = {
            "Authorization": f"Bearer {test_session_id}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "jimeng-4.0",
            "prompt": "流浪地球画面",
            "negative_prompt": "低质量",
            "width": 1024,
            "height": 1024,
            "sample_strength": 0.5,
            "response_format": "url"
        }
        
        print("📤 发送请求...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"📊 HTTP状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            data_list = result.get("data", [])
            print(f"📋 返回数据: {result}")
            
            if data_list:
                print(f"✅ 成功！返回 {len(data_list)} 张图片")
            else:
                print(f"❌ 返回空数据")
        else:
            print(f"❌ HTTP错误: {response.text}")
        
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    debug_session_id()
