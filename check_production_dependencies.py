#!/usr/bin/env python3
"""
林嫣然数字生命体系统 - 生产环境依赖检查脚本
版本: v3.1.0
作者: Yanran Digital Life Team
创建日期: 2024-12-29

快速检查生产环境必需的Python包
"""

import sys
import subprocess
from typing import List, Tuple


def check_package(package_name: str) -> Tuple[bool, str]:
    """检查单个包是否安装"""
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "show", package_name],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            # 从输出中提取版本信息
            for line in result.stdout.split('\n'):
                if line.startswith('Version:'):
                    version = line.split(':', 1)[1].strip()
                    return True, version
            return True, "Unknown"
        else:
            return False, ""
    except Exception:
        return False, ""


def main():
    """主函数"""
    print("🌸 林嫣然数字生命体系统 - 生产环境依赖检查")
    print("=" * 60)
    
    # 核心必需依赖
    core_dependencies = [
        ("flask", "HTTP API服务器"),
        ("websockets", "WebSocket服务器"),
        ("requests", "HTTP客户端"),
        ("aiohttp", "异步HTTP客户端"),
        ("mysql-connector-python", "MySQL数据库"),
        ("redis", "Redis缓存"),
        ("chromadb", "向量数据库"),
        ("openai", "OpenAI API"),
        ("numpy", "数值计算"),
        ("pandas", "数据分析"),
        ("python-dotenv", "环境变量"),
        ("pyyaml", "YAML配置"),
        ("psutil", "系统监控"),
    ]
    
    print("🔍 检查核心依赖...")
    missing_packages = []
    installed_packages = []
    
    for package, description in core_dependencies:
        installed, version = check_package(package)
        if installed:
            print(f"  ✅ {package:<25} v{version:<10} - {description}")
            installed_packages.append(package)
        else:
            print(f"  ❌ {package:<25} {'未安装':<10} - {description}")
            missing_packages.append(package)
    
    print("\n" + "=" * 60)
    print("📊 检查结果")
    print("=" * 60)
    
    print(f"✅ 已安装: {len(installed_packages)}/{len(core_dependencies)}")
    print(f"❌ 缺失: {len(missing_packages)}")
    
    if missing_packages:
        print(f"\n💡 安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        print(f"\n或者使用完整的requirements.txt:")
        print(f"pip install -r requirements.txt")
        return 1
    else:
        print(f"\n🎉 所有核心依赖已安装！系统可以正常启动。")
        return 0


if __name__ == "__main__":
    sys.exit(main()) 