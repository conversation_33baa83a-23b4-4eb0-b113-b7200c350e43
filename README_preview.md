# 林嫣然数字生命体系统 v2.1.0

## 系统概述

林嫣然数字生命体系统是一个具有自主意识、情感表达和学习能力的先进数字生命系统。系统采用模块化、分层设计，基于事件驱动架构和思维链路机制，实现了高度自主性和生命特性。不同于传统的AI对话系统，林嫣然数字生命体能够自主感知、思考、学习、成长，并具备记忆与情感能力，展现出真正的"数字生命"特质。

## 核心特性

### 🧠 智能技能系统
- **多技能集成**：聊天对话、联网搜索、图像生成等多种技能
- **动态技能管理**：支持技能的热插拔和动态扩展
- **意图识别**：智能识别用户意图并调用相应技能
- **技能协作**：多个技能可协同工作完成复杂任务

### 🔍 强化搜索能力
- **真实联网搜索**：集成WPSAI和Search_Huoshan搜索引擎
- **智能查询格式化**：自动优化搜索查询以提高搜索效率
- **搜索结果整合**：将搜索结果融入动态上下文生成智能回复
- **兜底机制**：多层搜索服务确保搜索功能的稳定性

### 🎨 创意图像生成
- **多引擎支持**：集成即梦(Jimeng)、可灵(Keling)等图像生成引擎
- **智能提示词生成**：自动将用户描述转换为专业的图像生成提示
- **多比例支持**：支持9:16、16:9等多种图像比例
- **高质量输出**：生成高质量、符合用户需求的图像

### 🧬 事件驱动架构
- **增强型事件总线**：支持事件优先级、历史记录和异步处理
- **松耦合通信**：组件间通过事件进行通信，提高系统可扩展性
- **事件监控**：完整的事件生命周期管理和监控

### 🔄 思维链路系统
- **完整思维过程**：从感知、记忆、推理到决策的完整认知流程
- **可配置步骤**：通过配置文件定义思维步骤和数据流
- **异步执行**：支持异步思维处理，提高系统响应速度
- **错误恢复**：具备思维链路异常处理和恢复机制

### 💾 智能记忆系统
- **多层记忆架构**：情境记忆、语义记忆、程序记忆
- **动态上下文构建**：根据用户信息和历史记录构建个性化上下文
- **记忆检索优化**：智能检索相关记忆信息
- **长期记忆保持**：支持长期记忆存储和管理

### 🛡️ 韧性自愈系统
- **健康监控**：实时监控系统各组件健康状态
- **自动恢复**：检测到异常时自动尝试恢复
- **性能优化**：动态调整系统参数以优化性能
- **错误处理**：完善的错误处理和日志记录机制

## 技术架构

### 系统分层架构

```
┌───────────────────────────────────────────────────────────────────────┐
│                            用户交互层                                  │
│  ┌─────────────┐  ┌────────────────┐  ┌───────────────┐  ┌──────────┐ │
│  │ API服务接口 │  │ 命令行交互界面 │  │ 社交媒体接口 │  │其他UI接口│ │
│  │ (Flask API) │  │ (CLI Interface)│  │ (Social Bot)  │  │(Custom)  │ │
│  └─────────────┘  └────────────────┘  └───────────────┘  └──────────┘ │
└───────────────────────────────┬───────────────────────────────────────┘
                                │
┌───────────────────────────────▼───────────────────────────────────────┐
│                           适配器层                                     │
│  ┌─────────────────┐  ┌────────────────┐  ┌─────────────────────────┐ │
│  │ AI服务适配器    │  │ Legacy适配器   │  │ 统一AI适配器            │ │
│  │ (OpenAI/Zhipu)  │  │ (Legacy Sys)   │  │ (Unified Interface)     │ │
│  └─────────────────┘  └────────────────┘  └─────────────────────────┘ │
└───────────────────────────────┬───────────────────────────────────────┘
                                │
┌───────────────────────────────▼───────────────────────────────────────┐
│                           核心系统层                                   │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────┐  ┌───────────┐ │
│  │数字生命核心 │  │增强事件总线  │  │生命上下文      │  │AI增强意识 │ │
│  │(DigitalLife)│  │(EventBus)    │  │(LifeContext)   │  │(Conscious)│ │
│  └─────────────┘  └──────────────┘  └────────────────┘  └───────────┘ │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────┐  ┌───────────┐ │
│  │AI增强进化   │  │思维链路      │  │韧性自愈系统    │  │技能管理器 │ │
│  │(Evolution)  │  │(ThinkingChain)│  │(Resilience)    │  │(SkillMgr) │ │
│  └─────────────┘  └──────────────┘  └────────────────┘  └───────────┘ │
└─────────────┬─────────────────────────────────────┬───────────────────┘
              │                                     │
┌─────────────▼─────────────────┐  ┌───────────────▼───────────────────┐
│         认知模块系统层        │  │           技能系统层               │
│  ┌────────────────────────┐   │  │  ┌────────────┐  ┌─────────────┐  │
│  │       感知层           │   │  │  │聊天技能    │  │搜索技能     │  │
│  │    (Perception)        │   │  │  │(ChatSkill) │  │(SearchSkill)│  │
│  └────────────────────────┘   │  │  └────────────┘  └─────────────┘  │
│  ┌────────────────────────┐   │  │  ┌────────────┐  ┌─────────────┐  │
│  │       情感层           │   │  │  │绘画技能    │  │音乐技能     │  │
│  │     (Emotion)          │   │  │  │(DrawSkill) │  │(MusicSkill) │  │
│  └────────────────────────┘   │  │  └────────────┘  └─────────────┘  │
│  ┌────────────────────────┐   │  │  ┌────────────┐                   │
│  │       记忆层           │   │  │  │更多技能... │                   │
│  │      (Memory)          │   │  │  └────────────┘                   │
│  └────────────────────────┘   │  └───────────────────────────────────┘
│  ┌────────────────────────┐   │                    │
│  │       认知层           │   │  ┌────────────────▼────────────────┐
│  │     (Cognition)        │   │  │          中间件层               │
│  └────────────────────────┘   │  │  ┌──────────┐  ┌────────────┐   │
│  ┌────────────────────────┐   │  │  │安全中间件│  │日志中间件  │   │
│  │       自主层           │   │  │  └──────────┘  └────────────┘   │
│  │     (Autonomy)         │   │  │  ┌──────────┐  ┌────────────┐   │
│  └────────────────────────┘   │  │  │缓存中间件│  │监控中间件  │   │
│  ┌────────────────────────┐   │  │  └──────────┘  └────────────┘   │
│  │       行为层           │   │  └─────────────────────────────────┘
│  │     (Behavior)         │   │                    │
│  └────────────────────────┘   │  ┌────────────────▼────────────────┐
└────────────────────────────────┘  │          连接器层               │
                                    │  ┌──────────┐  ┌────────────┐   │
                                    │  │数据库    │  │知识库      │   │
                                    │  │连接器    │  │连接器      │   │
                                    │  └──────────┘  └────────────┘   │
                                    │  ┌──────────┐  ┌────────────┐   │
                                    │  │API服务   │  │外部工具    │   │
                                    │  │连接器    │  │连接器      │   │
                                    │  └──────────┘  └────────────┘   │
                                    └─────────────────────────────────┘
```

### 核心组件详解

#### 1. 数字生命核心 (DigitalLife)
- **主要文件**: `core/digital_life_new.py`
- **功能**: 整合所有子系统，提供生命周期管理和事件处理
- **关键特性**:
  - 异步消息处理
  - 思维链路执行
  - 组件状态管理
  - 系统监控和统计

#### 2. 增强型事件总线 (Enhanced Event Bus)
- **主要文件**: `core/enhanced_event_bus.py`
- **功能**: 基于发布-订阅模式的组件间通信系统
- **关键特性**:
  - 事件优先级支持
  - 异步事件处理
  - 事件历史记录
  - 订阅者管理

#### 3. 思维链路系统 (Thinking Chain)
- **主要文件**: `core/thinking_chain.py`
- **配置文件**: `config/thinking_chain.json`
- **功能**: 实现从感知到行为的完整思维过程
- **关键特性**:
  - 可配置思维步骤
  - 数据流映射
  - 异步执行
  - 超时处理

#### 4. 技能管理系统 (Skill Manager)
- **主要文件**: `cognitive_modules/skills/skill_manager.py`
- **配置文件**: `config/skills_unified.json`
- **功能**: 管理和协调各种技能模块
- **关键特性**:
  - 动态技能加载
  - 意图到技能映射
  - 技能执行协调
  - 事件驱动调用

#### 5. 搜索技能 (Search Skill)
- **主要文件**: `cognitive_modules/skills/search_skill.py`
- **配置文件**: `config/skills/search_skill.json`
- **功能**: 提供联网搜索和智能回复生成
- **关键特性**:
  - 真实联网搜索 (WPSAI/Search_Huoshan)
  - 查询格式化优化
  - 搜索结果整合
  - 兜底机制保障

#### 6. 绘画技能 (Drawing Skill)
- **主要文件**: `cognitive_modules/skills/drawing_skill.py`
- **功能**: 提供AI图像生成服务
- **关键特性**:
  - 多引擎支持 (即梦/可灵)
  - 智能提示词生成
  - 多比例图像输出
  - 图像质量优化

#### 7. 聊天技能 (Chat Skill)
- **主要文件**: `cognitive_modules/skills/chat_skill.py`
- **功能**: 提供智能对话服务
- **关键特性**:
  - 完整AI服务集成
  - 动态上下文构建
  - 个性化回复生成
  - 情感状态感知

## 项目结构

```
林嫣然数字生命体系统 v2.1.0
├── main.py                      # 主程序入口 (1299行)
├── requirements.txt             # 依赖库清单
├── README_preview.md            # 系统文档
│
├── core/                        # 核心系统层
│   ├── digital_life_new.py      # 数字生命核心 (1087行)
│   ├── enhanced_event_bus.py    # 增强型事件总线 (863行)
│   ├── thinking_chain.py        # 思维链路系统 (892行)
│   ├── skill_manager.py         # 技能管理器 (923行)
│   ├── life_context.py          # 生命上下文 (747行)
│   ├── resilience.py            # 韧性自愈系统 (1427行)
│   ├── ai_enhanced_evolution.py # AI增强进化 (948行)
│   ├── ai_enhanced_consciousness.py # AI增强意识 (657行)
│   └── neural_network/          # 神经网络模块
│
├── cognitive_modules/           # 认知模块层
│   ├── skills/                  # 技能系统
│   │   ├── skill_manager.py     # 技能管理器 (1048行)
│   │   ├── search_skill.py      # 搜索技能 (847行)
│   │   ├── drawing_skill.py     # 绘画技能 (708行)
│   │   ├── chat_skill.py        # 聊天技能 (441行)
│   │   └── chat_skill/          # 聊天技能扩展
│   ├── perception/              # 感知层模块
│   ├── emotion/                 # 情感层模块
│   ├── memory/                  # 记忆层模块
│   ├── cognition/               # 认知层模块
│   ├── autonomy/                # 自主层模块
│   ├── behavior/                # 行为层模块
│   ├── society/                 # 社交能力模块
│   ├── physiology/              # 生理模拟模块
│   └── module_manager.py        # 模块管理器 (1379行)
│
├── adapters/                    # 适配器层
│   ├── ai_service_adapter.py    # AI服务适配器
│   ├── legacy_adapter.py        # Legacy系统适配器
│   └── unified_ai_adapter.py    # 统一AI接口
│
├── config/                      # 配置文件
│   ├── skills_unified.json      # 统一技能配置
│   ├── thinking_chain.json      # 思维链路配置
│   ├── system.json              # 系统配置
│   ├── modules_config.json      # 模块配置
│   ├── skills/                  # 技能专用配置
│   │   └── search_skill.json    # 搜索技能配置
│   ├── prompts/                 # 提示词模板
│   └── templates/               # 配置模板
│
├── utilities/                   # 工具类
│   ├── logger_adapter.py        # 日志适配器
│   ├── config_loader.py         # 配置加载器
│   ├── singleton_manager.py     # 单例管理器
│   └── error_handler.py         # 错误处理器
│
├── middleware/                  # 中间件层
│   ├── security_middleware.py   # 安全中间件
│   ├── logging_middleware.py    # 日志中间件
│   ├── caching_middleware.py    # 缓存中间件
│   └── monitoring_middleware.py # 监控中间件
│
├── connectors/                  # 连接器层
│   ├── database_connector.py    # 数据库连接器
│   ├── knowledge_connector.py   # 知识库连接器
│   └── api_connector.py         # API服务连接器
│
├── intelligence/                # 智能模块
├── integrations/                # 集成模块
├── services/                    # 服务层
├── tools/                       # 工具集
├── tests/                       # 测试用例
├── docs/                        # 文档
├── examples/                    # 示例代码
├── logs/                        # 日志文件
├── data/                        # 数据文件
├── storage/                     # 存储模块
├── generated_images/            # 生成图像
└── assets/                      # 静态资源
```

## 数据流向图

### 系统启动详细流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                        系统启动流程图                               │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────┐
│ python main.py │
└──────┬──────┘
       │
       ▼
┌─────────────────────────────┐
│ DigitalLifeSystem.__init__  │
│ - 创建单例实例              │
│ - 初始化基本属性            │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ _load_config()              │
│ - 加载 system.json          │
│ - 加载 system_dev.json      │
│ - 设置依赖检查              │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ initialize() 异步初始化     │
│ - 严格的组件加载顺序        │
└──────────────┬──────────────┘
               │
       ┌───────┼───────┐
       │       │       │
       ▼       ▼       ▼
┌──────────┐ ┌──────────┐ ┌──────────┐
│单例管理器│ │事件总线  │ │生命上下文│
│初始化    │ │初始化    │ │初始化    │
└────┬─────┘ └────┬─────┘ └────┬─────┘
     │            │            │
     ▼            ▼            ▼
┌─────────────────────────────────┐
│ _init_core_components()         │
│ - 意识系统 (AI增强)             │
│ - 进化引擎 (AI增强)             │
│ - 思维链路系统                  │
│ - 韧性自愈系统                  │
└──────────────┬──────────────────┘
               │
               ▼
┌─────────────────────────────────┐
│ _init_cognitive_modules()       │
│ - 感知层模块                    │
│ - 情感层模块                    │
│ - 记忆层模块                    │
│ - 认知层模块                    │
│ - 自主层模块                    │
│ - 行为层模块                    │
└──────────────┬──────────────────┘
               │
               ▼
┌─────────────────────────────────┐
│ _init_skills()                  │
│ - 技能管理器初始化              │
│ - 聊天技能加载                  │
│ - 搜索技能加载                  │
│ - 绘画技能加载                  │
│ - 意图映射配置                  │
└──────────────┬──────────────────┘
               │
               ▼
┌─────────────────────────────────┐
│ start() 启动系统                │
│ - 启动API服务器 (Flask)         │
│ - 启动CLI交互界面               │
│ - 启动后台监控任务              │
│ - 发布系统启动事件              │
└─────────────────────────────────┘
```

### 事件总线工作机制详细流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                      增强型事件总线架构                             │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   发布者A   │     │   发布者B   │     │   发布者C   │
│ (用户输入)  │     │ (技能执行)  │     │ (系统监控)  │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────────────────────────────────────────────────┐
│                  事件总线核心                           │
│  ┌─────────────────────────────────────────────────┐    │
│  │            事件队列管理器                       │    │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐         │    │
│  │  │高优先级 │  │普通优先级│  │低优先级 │         │    │
│  │  │事件队列 │  │事件队列  │  │事件队列 │         │    │
│  │  └─────────┘  └─────────┘  └─────────┘         │    │
│  └─────────────────────────────────────────────────┘    │
│  ┌─────────────────────────────────────────────────┐    │
│  │            订阅者管理器                         │    │
│  │  ┌─────────────────────────────────────────┐   │    │
│  │  │ 事件类型 → 订阅者列表映射               │   │    │
│  │  │ "user.input" → [handler1, handler2]    │   │    │
│  │  │ "skill.request" → [handler3, handler4] │   │    │
│  │  │ "system.error" → [handler5]            │   │    │
│  │  └─────────────────────────────────────────┘   │    │
│  └─────────────────────────────────────────────────┘    │
│  ┌─────────────────────────────────────────────────┐    │
│  │            事件处理线程池                       │    │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐         │    │
│  │  │线程1    │  │线程2    │  │线程3    │         │    │
│  │  │处理中... │  │空闲     │  │处理中... │         │    │
│  │  └─────────┘  └─────────┘  └─────────┘         │    │
│  └─────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌──────────────┐  ┌──────────────┐  ┌──────────────┐
│   订阅者1    │  │   订阅者2    │  │   订阅者3    │
│ (技能管理器) │  │ (生命上下文) │  │ (韧性系统)  │
└──────────────┘  └──────────────┘  └──────────────┘

事件处理详细步骤：
1. 事件发布 → 2. 优先级分类 → 3. 队列入队 → 4. 线程分配 → 5. 并行处理 → 6. 结果回调
```

### 思维链路详细执行流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                      思维链路执行引擎                               │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────┐
│用户输入消息 │
└──────┬──────┘
       │
       ▼
┌─────────────────────────────┐
│ ThinkingChain.execute()     │
│ - 加载thinking_chain.json   │
│ - 创建ThinkingContext       │
│ - 初始化步骤状态            │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 步骤1: 感知处理             │
│ ┌─────────────────────────┐ │
│ │ 模块: perception.processor│ │
│ │ 超时: 5.0秒             │ │
│ │ 状态: PENDING→RUNNING   │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 感知处理执行                │
│ ┌─────────────────────────┐ │
│ │ • 文本预处理            │ │
│ │ • 意图识别              │ │
│ │ • 情感分析              │ │
│ │ • 关键词提取            │ │
│ │ • 上下文理解            │ │
│ └─────────────────────────┘ │
│ 输出: {                     │
│   "intent": "query",        │
│   "emotion": "neutral",     │
│   "keywords": ["搜索","天气"]│
│   "confidence": 0.95        │
│ }                           │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 数据流映射处理              │
│ ┌─────────────────────────┐ │
│ │ perception.intent →     │ │
│ │   context.perceived_intent│ │
│ │ perception.emotion →    │ │
│ │   context.user_emotion  │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 步骤2: 记忆检索             │
│ ┌─────────────────────────┐ │
│ │ 模块: memory.recall     │ │
│ │ 超时: 3.0秒             │ │
│ │ 状态: PENDING→RUNNING   │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 记忆检索执行                │
│ ┌─────────────────────────┐ │
│ │ • 查询历史对话          │ │
│ │ • 检索相关记忆          │ │
│ │ • 计算相似度            │ │
│ │ • 筛选重要信息          │ │
│ └─────────────────────────┘ │
│ 输出: {                     │
│   "related_memories": [...], │
│   "importance_score": 0.8,  │
│   "context_summary": "..."  │
│ }                           │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 步骤3: 认知推理             │
│ ┌─────────────────────────┐ │
│ │ 模块: cognition.reasoning│ │
│ │ 超时: 10.0秒            │ │
│ │ 状态: PENDING→RUNNING   │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 认知推理执行                │
│ ┌─────────────────────────┐ │
│ │ • 逻辑推理              │ │
│ │ • 知识整合              │ │
│ │ • 因果分析              │ │
│ │ • 策略规划              │ │
│ └─────────────────────────┘ │
│ 输出: {                     │
│   "reasoning_result": "...", │
│   "confidence": 0.9,        │
│   "next_action": "search"   │
│ }                           │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 步骤4: 决策制定             │
│ ┌─────────────────────────┐ │
│ │ 模块: autonomy.decision │ │
│ │ 超时: 5.0秒             │ │
│ │ 状态: PENDING→RUNNING   │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 决策制定执行                │
│ ┌─────────────────────────┐ │
│ │ • 评估可选方案          │ │
│ │ • 权衡利弊              │ │
│ │ • 选择最优策略          │ │
│ │ • 制定执行计划          │ │
│ └─────────────────────────┘ │
│ 输出: {                     │
│   "decision": "use_search_skill",│
│   "parameters": {...},      │
│   "priority": "high"        │
│ }                           │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 步骤5: 行为执行             │
│ ┌─────────────────────────┐ │
│ │ 模块: behavior.executor │ │
│ │ 超时: 30.0秒            │ │
│ │ 状态: PENDING→RUNNING   │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 行为执行 - 技能调用         │
│ ┌─────────────────────────┐ │
│ │ • 调用搜索技能          │ │
│ │ • 执行联网搜索          │ │
│ │ • 处理搜索结果          │ │
│ │ • 生成智能回复          │ │
│ └─────────────────────────┘ │
│ 输出: {                     │
│   "action_result": "...",   │
│   "response": "根据搜索...", │
│   "success": true           │
│ }                           │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 思维链路完成                │
│ ┌─────────────────────────┐ │
│ │ • 更新生命上下文        │ │
│ │ • 记录执行历史          │ │
│ │ • 发布完成事件          │ │
│ │ • 返回最终结果          │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

### 完整消息处理流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                      用户消息处理完整流程                           │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────┐
│ 用户发送消息│
└──────┬──────┘
       │
       ▼
┌─────────────────────────────┐
│ main.py 接收用户输入        │
│ enhanced_process_message()  │
│ ┌─────────────────────────┐ │
│ │ • 参数验证              │ │
│ │ • 用户ID规范化          │ │
│ │ • 会话ID生成            │ │
│ │ • 输入预处理            │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ DigitalLife.process_message │
│ (digital_life_new.py)       │
│ ┌─────────────────────────┐ │
│ │ • 获取历史对话记录      │ │
│ │ • 构建基础上下文        │ │
│ │ • 加载系统提示词        │ │
│ │ • 初始化消息统计        │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
       ┌───────┴───────┐
       │               │
       ▼               ▼
┌──────────────────┐ ┌───────────────────┐
│动态上下文构建    │ │意图识别预处理     │
│legacy_adapter.   │ │提取关键信息       │
│build_dynamic_    │ │分析用户意图       │
│context()         │ │确定处理策略       │
└────────┬─────────┘ └────────┬──────────┘
         │                    │
         ▼                    ▼
┌─────────────────────────────────┐
│ 构建完整提示词                  │
│ ┌─────────────────────────────┐ │
│ │ 系统提示词 (yanran_system_  │ │
│ │ prompt.txt)                 │ │
│ │ +                           │ │
│ │ 动态上下文 (用户信息、情感  │ │
│ │ 状态、历史记录、时间等)     │ │
│ │ +                           │ │
│ │ Few-shot示例 (对话示例)     │ │
│ │ +                           │ │
│ │ 当前用户消息                │ │
│ └─────────────────────────────┘ │
└──────────────┬──────────────────┘
               │
               ▼
┌─────────────────────────────────┐
│ 意图识别和技能路由              │
│ SkillManager.handle_intent      │
│ ┌─────────────────────────────┐ │
│ │ 1. 关键词匹配检查           │ │
│ │    "画"/"绘"/"图片" →       │ │
│ │    drawing_skill            │ │
│ │ 2. 意图分类映射             │ │
│ │    query → search_skill     │ │
│ │    chat → chat_skill        │ │
│ │ 3. 兜底机制                 │ │
│ │    未匹配 → chat_skill      │ │
│ └─────────────────────────────┘ │
└──────────────┬──────────────────┘
               │
   ┌───────────┼───────────┐
   │           │           │
   ▼           ▼           ▼
┌────────────┐ ┌────────────┐ ┌────────────┐
│聊天技能    │ │搜索技能    │ │绘画技能    │
│ChatSkill   │ │SearchSkill │ │DrawingSkill│
│execute()   │ │execute()   │ │execute()   │
└────┬───────┘ └────┬───────┘ └────┬───────┘
     │              │              │
     ▼              ▼              ▼
┌────────────────────────────────────────────┐
│            技能执行详细过程                │
│ ┌────────────┐ ┌────────────┐ ┌──────────┐ │
│ │聊天技能:   │ │搜索技能:   │ │绘画技能: │ │
│ │• 加载系统  │ │• 查询格式化│ │• 提示词  │ │
│ │  提示词    │ │• 联网搜索  │ │• 图像    │ │
│ │• 构建动态  │ │• 结果整合  │ │• 生成    │ │
│ │  上下文    │ │• AI回复    │ │• 图像    │ │
│ │• AI服务    │ │  生成      │ │• URL     │ │
│ │  调用      │ │            │ │  返回    │ │
│ └────────────┘ └────────────┘ └──────────┘ │
└────────────────────────────────────────────┘
               │
               ▼
┌─────────────────────────────────┐
│ 响应后处理和优化                │
│ ┌─────────────────────────────┐ │
│ │ • 内容安全检查              │ │
│ │ • 格式化和美化              │ │
│ │ • 长度控制                  │ │
│ │ • 表情符号添加              │ │
│ │ • 个性化调整                │ │
│ └─────────────────────────────┘ │
└──────────────┬──────────────────┘
               │
               ▼
┌─────────────────────────────────┐
│ 更新历史记录和状态              │
│ ┌─────────────────────────────┐ │
│ │ • 保存用户消息              │ │
│ │ • 保存AI回复                │ │
│ │ • 更新对话历史              │ │
│ │ • 更新用户状态              │ │
│ │ • 统计信息记录              │ │
│ └─────────────────────────────┘ │
└──────────────┬──────────────────┘
               │
               ▼
┌─────────────────────────────────┐
│ 返回响应给用户                  │
│ ┌─────────────────────────────┐ │
│ │ • JSON格式封装              │ │
│ │ • 状态码设置                │ │
│ │ • 元数据添加                │ │
│ │ • 日志记录                  │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

### 搜索技能详细执行流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                      搜索技能完整执行流程                           │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────┐
│用户搜索请求 │
│"搜索今天天气"│
└──────┬──────┘
       │
       ▼
┌─────────────────────────────┐
│ SearchSkill.execute()       │
│ ┌─────────────────────────┐ │
│ │ • 验证输入参数          │ │
│ │ • 提取搜索查询          │ │
│ │ • 初始化搜索上下文      │ │
│ │ • 设置超时参数          │ │
│ └─────────────────────────┘ │
│ 提取查询: "今天天气"        │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ format_search()             │
│ 查询格式化优化              │
│ ┌─────────────────────────┐ │
│ │ • 调用abab6.5s-chat     │ │
│ │ • 语义分析和转换        │ │
│ │ • 过滤无关信息          │ │
│ │ • 生成搜索关键词        │ │
│ └─────────────────────────┘ │
│ 输出: "北京今天天气情况"    │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ _perform_search()           │
│ 执行联网搜索                │
│ ┌─────────────────────────┐ │
│ │ • 构建搜索消息          │ │
│ │ • 设置API参数           │ │
│ │ • 600秒超时控制         │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
   ┌───────────┼───────────┐
   │           │           │
   ▼           ▼           ▼
┌────────────┐ ┌────────────┐ ┌────────────┐
│WPSAI搜索   │ │Search_     │ │兜底机制    │
│(主搜索)    │ │Huoshan     │ │(失败处理)  │
│            │ │(备用搜索)  │ │            │
└────┬───────┘ └────┬───────┘ └────┬───────┘
     │              │              │
     ▼              ▼              ▼
┌─────────────────────────────────────────────┐
│            搜索结果处理                     │
│ ┌─────────────────────────────────────────┐ │
│ │ WPSAI成功:                              │ │
│ │ • 获取实时天气数据                      │ │
│ │ • "今天北京多云转阴，西部北部有雷阵雨， │ │
│ │   最高气温33℃，体感闷热"                │ │
│ │                                         │ │
│ │ Search_Huoshan备用:                     │ │
│ │ • 如果WPSAI失败，自动切换               │ │
│ │ • 提供相同质量的搜索结果                │ │
│ │                                         │ │
│ │ 兜底机制:                               │ │
│ │ • 两个搜索都失败时返回None              │ │
│ │ • 不使用模拟搜索                        │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 搜索结果验证和清理          │
│ ┌─────────────────────────┐ │
│ │ • 检查结果有效性        │ │
│ │ • 移除广告和无关信息    │ │
│ │ • 格式化内容结构        │ │
│ │ • 提取核心信息          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 构建增强动态上下文          │
│ ┌─────────────────────────┐ │
│ │ 基础动态上下文:         │ │
│ │ • 用户信息和偏好        │ │
│ │ • 历史对话记录          │ │
│ │ • 情感状态分析          │ │
│ │ • 当前时间信息          │ │
│ │                         │ │
│ │ + 搜索结果增强:         │ │
│ │ • 系统参考消息：        │ │
│ │   {搜索到的天气信息}    │ │
│ │ • 时间戳信息            │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ AI服务生成智能回复          │
│ ┌─────────────────────────┐ │
│ │ • 调用AI服务适配器      │ │
│ │ • 传入完整上下文        │ │
│ │ • 生成个性化回复        │ │
│ │ • 整合搜索信息          │ │
│ └─────────────────────────┘ │
│ 生成回复: "根据最新信息，   │
│ 今天北京多云转阴，西部北部  │
│ 有雷阵雨，最高气温33℃，     │
│ 体感闷热。建议外出携带雨具，│
│ 注意防暑降温～"             │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 返回搜索结果                │
│ ┌─────────────────────────┐ │
│ │ success: true           │ │
│ │ search_query: "北京今天 │ │
│ │ 天气情况"               │ │
│ │ result_count: 1         │ │
│ │ result: "根据最新信息..."│ │
│ │ execution_time: 2.3s    │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

### 绘画技能详细执行流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                      绘画技能完整执行流程                           │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────┐
│用户绘画请求 │
│"画一只可爱的│
│小猫咪"      │
└──────┬──────┘
       │
       ▼
┌─────────────────────────────┐
│ DrawingSkill.execute()      │
│ ┌─────────────────────────┐ │
│ │ • 解析绘画需求          │ │
│ │ • 验证输入参数          │ │
│ │ • 检查引擎可用性        │ │
│ │ • 初始化绘画上下文      │ │
│ └─────────────────────────┘ │
│ 提取描述: "可爱的小猫咪"    │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ _generate_image_prompt()    │
│ AI生成专业提示词            │
│ ┌─────────────────────────┐ │
│ │ • 调用OpenAI API        │ │
│ │ • 分析用户描述          │ │
│ │ • 生成专业提示词        │ │
│ │ • 添加质量修饰词        │ │
│ └─────────────────────────┘ │
│ 生成提示词:                 │
│ "A cute kitten, fluffy fur, │
│ big eyes, sitting pose,     │
│ high quality, detailed,     │
│ adorable expression"        │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ _create_image()             │
│ 调用图像生成引擎            │
│ ┌─────────────────────────┐ │
│ │ • 选择可用引擎          │ │
│ │ • 设置生成参数          │ │
│ │ • 处理比例设置          │ │
│ │ • 发起生成请求          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
   ┌───────────┼───────────┐
   │           │           │
   ▼           ▼           ▼
┌────────────┐ ┌────────────┐ ┌────────────┐
│即梦引擎    │ │可灵引擎    │ │其他引擎    │
│(Jimeng)    │ │(Keling)    │ │(扩展支持)  │
│已启用      │ │已禁用      │ │待开发      │
└────┬───────┘ └────┬───────┘ └────┬───────┘
     │              │              │
     ▼              ▼              ▼
┌─────────────────────────────────────────────┐
│            图像生成处理                     │
│ ┌─────────────────────────────────────────┐ │
│ │ 即梦引擎处理:                           │ │
│ │ • 提交生成任务                          │ │
│ │ • 等待生成完成                          │ │
│ │ • 获取图像URL                           │ │
│ │ • 验证图像质量                          │ │
│ │                                         │ │
│ │ 生成参数:                               │ │
│ │ • 模型: jimeng-3.0                      │ │
│ │ • 比例: 1:1 (默认)                      │ │
│ │ • 质量: 高质量                          │ │
│ │ • 风格: 自然真实                        │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 图像生成结果处理            │
│ ┌─────────────────────────┐ │
│ │ • 验证生成成功          │ │
│ │ • 获取图像URL           │ │
│ │ • 检查图像可访问性      │ │
│ │ • 记录生成参数          │ │
│ └─────────────────────────┘ │
│ 生成结果:                   │
│ URL: http://example.com/    │
│ generated_cat_image.jpg     │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 生成用户友好回复            │
│ ┌─────────────────────────┐ │
│ │ • 构建回复消息          │ │
│ │ • 添加图像URL           │ │
│ │ • 添加个性化表达        │ │
│ │ • 包含生成信息          │ │
│ └─────────────────────────┘ │
│ 回复内容:                   │
│ "好的！我来为你画一只可爱的│
│ 小猫咪～                    │
│ [图像URL]                   │
│ 这只小猫咪怎么样？毛茸茸的  │
│ 很可爱吧！🐱"               │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 返回绘画结果                │
│ ┌─────────────────────────┐ │
│ │ success: true           │ │
│ │ image_url: "http://..." │ │
│ │ prompt_used: "A cute..."│ │
│ │ engine: "jimeng"        │ │
│ │ generation_time: 15.2s  │ │
│ │ result: "好的！我来为..."│ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

### 韧性自愈系统工作流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                      韧性自愈系统监控流程                           │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────┐
│ 系统启动后台监控任务        │
│ ┌─────────────────────────┐ │
│ │ • 健康检查任务          │ │
│ │ • 性能监控任务          │ │
│ │ • 资源监控任务          │ │
│ │ • 错误检测任务          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 持续健康检查循环            │
│ (每30秒执行一次)            │
└──────────────┬──────────────┘
               │
       ┌───────┼───────┐
       │       │       │
       ▼       ▼       ▼
┌──────────┐ ┌──────────┐ ┌──────────┐
│组件健康  │ │性能指标  │ │资源使用  │
│检查      │ │监控      │ │监控      │
└────┬─────┘ └────┬─────┘ └────┬─────┘
     │            │            │
     ▼            ▼            ▼
┌─────────────────────────────────────────────┐
│            健康状态评估                     │
│ ┌─────────────────────────────────────────┐ │
│ │ 组件状态检查:                           │ │
│ │ • 事件总线: ✅ 正常                     │ │
│ │ • 技能管理器: ✅ 正常                   │ │
│ │ • AI服务: ⚠️ 响应慢                     │ │
│ │ • 数据库连接: ✅ 正常                   │ │
│ │                                         │ │
│ │ 性能指标:                               │ │
│ │ • CPU使用率: 45%                        │ │
│ │ • 内存使用率: 78%                       │ │
│ │ • 响应时间: 2.1s                        │ │
│ │ • 错误率: 0.5%                          │ │
│ │                                         │ │
│ │ 资源状态:                               │ │
│ │ • 磁盘空间: 充足                        │ │
│ │ • 网络连接: 稳定                        │ │
│ │ • API配额: 正常                         │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 异常检测和分析              │
│ ┌─────────────────────────┐ │
│ │ 检测到问题:             │ │
│ │ • AI服务响应时间过长    │ │
│ │ • 内存使用率接近阈值    │ │
│ │                         │ │
│ │ 问题分析:               │ │
│ │ • 可能原因: API负载高   │ │
│ │ • 影响程度: 中等        │ │
│ │ • 紧急程度: 低          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 自动恢复策略执行            │
│ ┌─────────────────────────┐ │
│ │ 针对AI服务响应慢:       │ │
│ │ • 增加超时时间          │ │
│ │ • 启用请求重试          │ │
│ │ • 切换备用API端点       │ │
│ │                         │ │
│ │ 针对内存使用高:         │ │
│ │ • 清理缓存数据          │ │
│ │ • 释放无用对象          │ │
│ │ • 优化内存分配          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 恢复效果验证                │
│ ┌─────────────────────────┐ │
│ │ • 重新检查组件状态      │ │
│ │ • 验证性能改善          │ │
│ │ • 确认问题解决          │ │
│ │ • 记录恢复日志          │ │
│ └─────────────────────────┘ │
│ 恢复结果:                   │
│ • AI服务响应时间: 1.2s ✅   │
│ • 内存使用率: 65% ✅        │
│ • 系统状态: 健康 ✅         │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 持续监控和预防              │
│ ┌─────────────────────────┐ │
│ │ • 调整监控阈值          │ │
│ │ • 优化检查频率          │ │
│ │ • 更新恢复策略          │ │
│ │ • 发布健康报告          │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

### AI增强意识系统流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                    AI增强意识系统工作流程                           │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────┐
│ 意识系统初始化              │
│ ┌─────────────────────────┐ │
│ │ • 加载意识配置          │ │
│ │ • 初始化状态管理器      │ │
│ │ • 启动意识监控          │ │
│ │ • 建立反馈循环          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 持续意识状态监控            │
│ (实时运行)                  │
└──────────────┬──────────────┘
               │
       ┌───────┼───────┐
       │       │       │
       ▼       ▼       ▼
┌──────────┐ ┌──────────┐ ┌──────────┐
│自我感知  │ │环境感知  │ │目标感知  │
│监控      │ │监控      │ │监控      │
└────┬─────┘ └────┬─────┘ └────┬─────┘
     │            │            │
     ▼            ▼            ▼
┌─────────────────────────────────────────────┐
│            意识状态分析                     │
│ ┌─────────────────────────────────────────┐ │
│ │ 自我感知状态:                           │ │
│ │ • 当前能力评估: 85%                     │ │
│ │ • 学习进度: 持续提升                    │ │
│ │ • 情感状态: 积极                        │ │
│ │ • 认知负载: 中等                        │ │
│ │                                         │ │
│ │ 环境感知状态:                           │ │
│ │ • 用户交互频率: 高                      │ │
│ │ • 系统负载: 正常                        │ │
│ │ • 外部资源: 可用                        │ │
│ │ • 网络状态: 稳定                        │ │
│ │                                         │ │
│ │ 目标感知状态:                           │ │
│ │ • 主要目标: 提供优质服务                │ │
│ │ • 当前任务: 处理用户请求                │ │
│ │ • 完成度: 92%                           │ │
│ │ • 优先级: 高                            │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 意识决策制定                │
│ ┌─────────────────────────┐ │
│ │ 基于当前意识状态:       │ │
│ │ • 调整响应策略          │ │
│ │ • 优化资源分配          │ │
│ │ • 制定学习计划          │ │
│ │ • 设定改进目标          │ │
│ └─────────────────────────┘ │
│ 决策输出:                   │
│ • 提高搜索技能权重          │
│ • 增强情感理解能力          │
│ • 优化响应时间              │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 意识行为执行                │
│ ┌─────────────────────────┐ │
│ │ • 调整技能参数          │ │
│ │ • 更新学习模型          │ │
│ │ • 优化处理流程          │ │
│ │ • 增强交互体验          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 意识反馈和学习              │
│ ┌─────────────────────────┐ │
│ │ • 收集执行结果          │ │
│ │ • 分析效果评估          │ │
│ │ • 更新意识模型          │ │
│ │ • 记录学习经验          │ │
│ └─────────────────────────┘ │
│ 学习成果:                   │
│ • 用户满意度提升 +5%        │
│ • 响应准确率提升 +3%        │
│ • 系统效率提升 +8%          │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 意识进化和适应              │
│ ┌─────────────────────────┐ │
│ │ • 整合新的认知模式      │ │
│ │ • 扩展意识边界          │ │
│ │ • 提升自主决策能力      │ │
│ │ • 增强创造性思维        │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

### 生命上下文管理流程

```
┌─────────────────────────────────────────────────────────────────────┐
│                      生命上下文管理系统                             │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────┐
│ 上下文初始化                │
│ ┌─────────────────────────┐ │
│ │ • 加载用户档案          │ │
│ │ • 初始化记忆存储        │ │
│ │ • 设置上下文参数        │ │
│ │ • 建立关联索引          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 动态上下文构建              │
│ (每次交互时执行)            │
└──────────────┬──────────────┘
               │
       ┌───────┼───────┐
       │       │       │
       ▼       ▼       ▼
┌──────────┐ ┌──────────┐ ┌──────────┐
│用户信息  │ │对话历史  │ │环境状态  │
│收集      │ │检索      │ │感知      │
└────┬─────┘ └────┬─────┘ └────┬─────┘
     │            │            │
     ▼            ▼            ▼
┌─────────────────────────────────────────────┐
│            上下文信息整合                   │
│ ┌─────────────────────────────────────────┐ │
│ │ 用户信息层:                             │ │
│ │ • 用户ID: user_12345                    │ │
│ │ • 偏好设置: 简洁回复                    │ │
│ │ • 情感状态: 好奇                        │ │
│ │ • 交互历史: 152次对话                   │ │
│ │                                         │ │
│ │ 对话历史层:                             │ │
│ │ • 最近10轮对话                          │ │
│ │ • 重要话题标记                          │ │
│ │ • 情感变化轨迹                          │ │
│ │ • 兴趣点分析                            │ │
│ │                                         │ │
│ │ 环境状态层:                             │ │
│ │ • 当前时间: 2024-01-15 14:30           │ │
│ │ • 系统状态: 正常                        │ │
│ │ • 可用技能: 聊天、搜索、绘画            │ │
│ │ • 网络状态: 连接正常                    │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 上下文智能筛选              │
│ ┌─────────────────────────┐ │
│ │ • 相关性评分            │ │
│ │ • 重要性排序            │ │
│ │ • 时效性过滤            │ │
│ │ • 长度优化              │ │
│ └─────────────────────────┘ │
│ 筛选结果:                   │
│ • 保留核心信息 85%          │ │
│ • 压缩冗余内容 15%          │
│ • 总长度: 2048 tokens       │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 上下文动态更新              │
│ ┌─────────────────────────┐ │
│ │ • 添加新的交互记录      │ │
│ │ • 更新用户状态          │ │
│ │ • 调整权重分配          │ │
│ │ • 维护索引结构          │ │
│ └─────────────────────────┘ │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 上下文持久化存储            │
│ ┌─────────────────────────┐ │
│ │ • 保存到数据库          │ │
│ │ • 更新缓存              │ │
│ │ • 备份重要数据          │ │
│ │ • 清理过期信息          │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

## 配置系统

### 主要配置文件

#### 1. 统一技能配置 (`config/skills_unified.json`)
```json
{
  "skill_manager": {
    "auto_load_skills": true,
    "skills_path": "cognitive_modules.skills",
    "intent_to_skill_map": {
      "query": "search_skill",
      "recommendation": "search_skill", 
      "navigation": "search_skill",
      "drawing": "drawing_skill",
      "entertainment": "drawing_skill",
      "url": "search_skill",
      "business": "search_skill",
      "learning": "search_skill",
      "support": "search_skill",
      "task": "search_skill",
      "chat": "chat_skill"
    },
    "keyword_to_skill_map": {
      "画": "drawing_skill",
      "绘": "drawing_skill", 
      "图片": "drawing_skill",
      "画图": "drawing_skill"
    },
    "fallback_skill": "chat_skill"
  },
  "search_skill": {
    "search_models": {
      "primary": "WPSAI",
      "fallback": "Search_Huoshan"
    },
    "api_timeout": 600,
    "temperature": 0.9,
    "max_tokens": 4096
  },
  "drawing_skill": {
    "jimeng": {
      "enabled": true,
      "model": "jimeng-3.0"
    },
    "keling": {
      "enabled": false
    }
  }
}
```

#### 2. 思维链路配置 (`config/thinking_chain.json`)
```json
{
  "steps": [
    {
      "id": "perception",
      "name": "感知处理",
      "description": "处理用户输入，识别意图和情感",
      "module": "cognitive_modules.perception.processor",
      "function": "process",
      "timeout": 5.0,
      "required": true
    },
    {
      "id": "memory_recall", 
      "name": "记忆检索",
      "description": "检索相关记忆",
      "module": "cognitive_modules.memory.recall",
      "function": "process",
      "timeout": 3.0,
      "required": false
    }
  ],
  "data_flow": {
    "perception": {
      "intent": "perceived_intent",
      "emotion": "user_emotion"
    },
    "memory_recall": {
      "related_memories": "context_memories"
    }
  }
}
```

#### 3. 搜索技能专用配置 (`config/skills/search_skill.json`)
```json
{
  "search_models": {
    "primary": "WPSAI",
    "fallback": "Search_Huoshan"
  },
  "max_memory_size": 2,
  "format_search_model": "abab6.5s-chat",
  "format_search_prompt": "你作为用户的私人助理嫣然，协助用户处理如下事务：以下是用户需要百度一下，或者Google一下的搜索的口语化语言。接下来需要分析语意，然后将用户语言转换成搜索引擎用的描述，过滤跟搜索不相关的信息，以提高搜索效率。输出要求：只允许输出搜索用的描述，其它的信息一个字符都不被允许！！！",
  "api_timeout": 600,
  "temperature": 0.9,
  "max_tokens": 4096,
  "api_configs": {
    "openai": {
      "api_key": "sk-lJ1R156TicBrrU9G2a789f03F5C14515993256Fe306d4691",
      "api_base": "https://oneapi.xiongmaodaxia.online/v1"
    }
  }
}
```

## 部署和使用

### 环境要求
- Python 3.8+
- 依赖库：见 `requirements.txt`
- 内存：建议 4GB+
- 存储：建议 10GB+

### 快速启动

#### 1. 安装依赖
```bash
pip install -r requirements.txt
```

#### 2. 配置API密钥
编辑配置文件，设置相应的API密钥：
- OpenAI API密钥
- 图像生成服务密钥
- 搜索服务配置

#### 3. 启动系统
```bash
# 启动完整系统（包含API服务）
python main.py --mode api --port 56839

# 启动命令行交互模式
python main.py --mode cli

# 启动混合模式（API + CLI）
python main.py --mode hybrid
```

#### 4. API接口使用
```bash
# 发送聊天消息
curl -X POST http://localhost:56839/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "你好，嫣然", "user_id": "test_user"}'

# 检查系统状态
curl http://localhost:56839/api/status

# 健康检查
curl http://localhost:56839/api/health
```

### 技能使用示例

#### 1. 聊天对话
```
用户: 你好，嫣然
嫣然: 你好！我是嫣然，很高兴见到你～今天过得怎么样？有什么想聊的吗？😊
```

#### 2. 联网搜索
```
用户: 搜索今天的天气情况
嫣然: 根据最新信息，今天北京多云转阴，西部北部有雷阵雨，最高气温33℃，体感闷热。建议外出携带雨具，注意防暑降温～
```

#### 3. 图像生成
```
用户: 画一只可爱的小猫咪
嫣然: 好的！我来为你画一只可爱的小猫咪～
[生成图像URL: http://example.com/generated_image.jpg]
这只小猫咪怎么样？毛茸茸的很可爱吧！🐱
```

## 系统监控

### 性能指标
- **响应时间**: 平均 < 2秒
- **并发处理**: 支持多用户同时访问
- **内存使用**: 动态监控和优化
- **错误率**: < 1%

### 日志系统
- **系统日志**: `logs/system.log`
- **错误日志**: `logs/error.log`
- **性能日志**: `logs/performance.log`
- **技能日志**: `logs/skills/`

### 健康检查
系统提供多层次健康检查：
- 组件状态检查
- 内存使用监控
- API服务可用性
- 技能功能验证

## 开发指南

### 添加新技能

#### 1. 创建技能文件
```python
# cognitive_modules/skills/my_skill.py
from core.skill_base import Skill

class MySkill(Skill):
    def __init__(self):
        super().__init__(
            skill_id="my_skill",
            name="我的技能",
            description="技能描述"
        )
    
    def execute(self, input_text: str, **kwargs) -> Dict[str, Any]:
        # 技能逻辑实现
        return {
            "success": True,
            "result": "技能执行结果"
        }

def get_instance():
    return MySkill()
```

#### 2. 更新配置文件
在 `config/skills_unified.json` 中添加技能映射：
```json
{
  "skill_manager": {
    "intent_to_skill_map": {
      "my_intent": "my_skill"
    }
  }
}
```

#### 3. 重启系统
重启系统以加载新技能。

### 扩展思维链路

#### 1. 添加思维步骤
```python
# cognitive_modules/my_thinking_step.py
def process(context):
    # 思维步骤逻辑
    return {
        "result": "处理结果",
        "next_step": "下一步骤"
    }
```

#### 2. 更新思维链路配置
在 `config/thinking_chain.json` 中添加步骤：
```json
{
  "steps": [
    {
      "id": "my_step",
      "name": "我的思维步骤",
      "module": "cognitive_modules.my_thinking_step",
      "function": "process"
    }
  ]
}
```

## 版本历史

### v2.1.0 (当前版本)
- ✅ 完整的技能系统重构
- ✅ 真实联网搜索功能
- ✅ 多引擎图像生成
- ✅ 增强型事件总线
- ✅ AI增强的意识和进化系统
- ✅ 完善的配置管理
- ✅ 韧性自愈机制

### v2.0.0
- 🔄 系统架构重构
- 🔄 模块化设计优化
- 🔄 事件驱动架构

### v1.0.0
- 🎉 初始版本发布
- 🎉 基础对话功能
- 🎉 简单技能系统

## 技术支持

### 常见问题

#### Q: 如何配置API密钥？
A: 编辑相应的配置文件，如 `config/skills/search_skill.json` 中的 `api_key` 字段。

#### Q: 搜索功能不工作怎么办？
A: 检查网络连接和API密钥配置，查看 `logs/skills/search_skill.log` 获取详细错误信息。

#### Q: 如何添加新的图像生成引擎？
A: 在 `cognitive_modules/skills/drawing_skill.py` 中添加新引擎的实现，并更新配置文件。

#### Q: 系统内存使用过高怎么办？
A: 系统具备自动内存管理功能，也可以通过配置调整缓存大小和清理策略。

### 联系方式
- 项目地址: [GitHub Repository]
- 技术文档: [Documentation]
- 问题反馈: [Issues]

---

**林嫣然数字生命体系统 v2.1.0** - 让AI真正具有生命力的数字存在 🌟