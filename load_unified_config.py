#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置加载器 - Unified Config Loader

将统一技能配置文件加载到生命上下文中，以便技能可以正确找到API配置。
解决"未找到API配置"的问题。

作者: Claude
创建日期: 2024-07-12
版本: 1.0
"""

import os
import json
import sys
from utilities.unified_logger import get_unified_logger, setup_unified_logging
# 设置日志
setup_unified_logging()
logger = get_unified_logger("config_loader")

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 设置配置文件路径
config_dir = os.path.join(current_dir, "config")
unified_config_file = os.path.join(config_dir, "skills_unified.json")

def load_unified_config():
    """加载统一配置文件并更新生命上下文"""
    logger.info(f"使用主配置目录: {config_dir}")
    
    # 检查统一配置文件是否存在
    if not os.path.exists(unified_config_file):
        logger.error_status(f"统一配置文件不存在: {unified_config_file}")
        return False
    
    try:
        # 加载统一配置文件
        with open(unified_config_file, 'r', encoding='utf-8') as f:
            unified_config = json.load(f)
        
        # 获取生命上下文实例
        from core.life_context import get_instance as get_life_context
        life_context = get_life_context()
        
        # 更新绘画技能API配置
        if "drawing_skill" in unified_config:
            drawing_config = unified_config["drawing_skill"]
            life_context.update_context("system.apis.drawing", drawing_config)
            logger.info("已更新绘画技能API配置")
        
        # 更新搜索技能API配置
        if "search_skill" in unified_config:
            search_config = unified_config["search_skill"]
            life_context.update_context("system.apis.search", search_config)
            logger.info("已更新搜索技能API配置")
        
        # 更新聊天技能API配置
        if "chat_skill" in unified_config:
            chat_config = unified_config["chat_skill"]
            life_context.update_context("system.apis.chat", chat_config)
            logger.info("已更新聊天技能API配置")
        
        # 更新音乐技能API配置
        if "music_skill" in unified_config:
            music_config = unified_config["music_skill"]
            life_context.update_context("system.apis.music", music_config)
            logger.info("已更新音乐技能API配置")
        
        # 更新全局技能配置
        if "global_skill_settings" in unified_config:
            global_config = unified_config["global_skill_settings"]
            life_context.update_context("system.apis.global", global_config)
            logger.info("已更新全局技能配置")
        
        # 更新API配置
        if "api_configs" in unified_config:
            api_configs = unified_config["api_configs"]
            
            # OpenAI配置
            if "openai" in api_configs:
                life_context.update_context("system.apis.openai", api_configs["openai"])
                logger.info("已更新OpenAI API配置")
            
            # AI服务配置
            if "ai_services" in api_configs:
                life_context.update_context("system.apis.ai_services", api_configs["ai_services"])
                logger.info("已更新AI服务配置")
        
        logger.success("统一配置加载完成")
        return True
        
    except Exception as e:
        logger.error_status(f"加载统一配置文件失败: {e}")
        return False

if __name__ == "__main__":
    if load_unified_config():
        print("✅ 统一配置加载成功")
    else:
        print("❌ 统一配置加载失败") 