#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王对比draft_content差异
"""

import json

# Web页面的draft_content（4.0成功的例子）
web_draft_content = {
    "type": "draft",
    "id": "d28b5fdb-e003-3ef6-701e-723cd5448a77",
    "min_version": "3.0.2",
    "min_features": [],
    "is_from_tsn": True,
    "version": "3.0.2",
    "main_component_id": "aaf8b7d5-7407-c3b4-824d-d69be9fb08fb",
    "component_list": [
        {
            "type": "image_base_component",
            "id": "aaf8b7d5-7407-c3b4-824d-d69be9fb08fb",
            "min_version": "3.0.2",
            "aigc_mode": "workbench",
            "gen_type": 1,
            "metadata": {
                "type": "",
                "id": "56527ae8-5305-7457-2f26-0c154f80c3d2",
                "created_platform": 3,
                "created_platform_version": "",
                "created_time_in_ms": "1757579498117",
                "created_did": ""
            },
            "generate_type": "generate",
            "abilities": {
                "type": "",
                "id": "6f634d49-2e28-0991-f680-05a0abffc9a9",
                "generate": {
                    "type": "",
                    "id": "9e7611be-63e9-13b8-605f-2a0cd9e8863a",
                    "core_param": {
                        "type": "",
                        "id": "34b0eff9-a403-8c1c-772e-7553bedccc23",
                        "model": "high_aes_general_v40",
                        "prompt": "赛伯朋克2077",
                        "negative_prompt": "",
                        "seed": 1986234001,
                        "sample_strength": 0.5,
                        "image_ratio": 8,
                        "large_image_info": {
                            "type": "",
                            "id": "c22879fc-ef3e-e0c7-8ffa-3e0f0fbcb39e",
                            "height": 1296,
                            "width": 3024,
                            "resolution_type": "2k"
                        },
                        "intelligent_ratio": False
                    }
                }
            }
        }
    ]
}

def analyze_draft_content():
    print("🔥 老王分析draft_content差异")
    print("=" * 60)
    
    print("📋 Web页面成功的4.0 draft_content结构:")
    print("-" * 40)
    
    component = web_draft_content["component_list"][0]
    core_param = component["abilities"]["generate"]["core_param"]
    
    print("🔍 关键字段分析:")
    print(f"1. min_features: {web_draft_content.get('min_features', '缺失')}")
    print(f"2. gen_type: {component.get('gen_type', '缺失')}")
    print(f"3. metadata存在: {'metadata' in component}")
    print(f"4. image_ratio: {core_param.get('image_ratio', '缺失')}")
    print(f"5. resolution_type: {core_param['large_image_info'].get('resolution_type', '缺失')}")
    print(f"6. intelligent_ratio: {core_param.get('intelligent_ratio', '缺失')}")
    
    print("\n🔧 我的jimeng-free-api实现对比:")
    print("-" * 40)
    
    # 检查我的实现
    print("✅ min_features: [] - 已实现")
    print("✅ gen_type: 1 - 已实现")
    print("✅ metadata - 已实现")
    print("❓ image_ratio: 计算逻辑可能有问题")
    print("✅ resolution_type: '2k' - 已实现")
    print("✅ intelligent_ratio: false - 已实现")
    
    print("\n🚨 可能的问题:")
    print("-" * 40)
    
    # 分析image_ratio
    width = 3024
    height = 1296
    ratio = width / height
    web_image_ratio = 8
    
    print(f"Web页面: {width}x{height} -> image_ratio: {web_image_ratio}")
    print(f"宽高比: {ratio:.2f}")
    
    # 我的计算逻辑
    if ratio > 2.0:
        my_image_ratio = 8
    elif ratio > 1.5:
        my_image_ratio = 6
    else:
        my_image_ratio = 4
        
    print(f"我的计算: {width}x{height} -> image_ratio: {my_image_ratio}")
    
    if my_image_ratio == web_image_ratio:
        print("✅ image_ratio计算正确")
    else:
        print("❌ image_ratio计算错误")
    
    print("\n💡 其他可能的问题:")
    print("-" * 40)
    print("1. 字段顺序可能重要")
    print("2. 某些字段的值类型可能不对")
    print("3. 可能缺少某些隐藏字段")
    print("4. 4.0模型可能需要特殊的权限或配置")
    
    # 检查字段顺序
    print("\n📋 Web页面component字段顺序:")
    for key in component.keys():
        print(f"  - {key}")
    
    print("\n📋 我的实现字段顺序:")
    my_fields = [
        "type", "id", "min_version", "aigc_mode", "gen_type", 
        "metadata", "generate_type", "abilities"
    ]
    for field in my_fields:
        print(f"  - {field}")

def main():
    analyze_draft_content()
    
    print("\n" + "=" * 60)
    print("🎯 分析结论:")
    print("需要检查:")
    print("1. 字段顺序是否影响解析")
    print("2. 是否有遗漏的字段")
    print("3. 字段值的类型是否正确")
    print("4. 是否需要调整image_ratio的计算逻辑")

if __name__ == "__main__":
    main()
