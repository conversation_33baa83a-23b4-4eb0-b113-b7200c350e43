#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试即梦官网所有支持的尺寸比例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill

def test_all_official_ratios():
    """测试即梦官网所有支持的尺寸比例"""
    print("🔥 老王测试即梦官网所有支持的尺寸比例")
    print("=" * 80)
    
    # 初始化drawing skill
    drawing_skill = DrawingSkill()
    
    # 即梦官网支持的所有比例
    test_ratios = [
        ("智能", "智能"),
        ("超宽屏", "21:9"),
        ("宽屏", "16:9"), 
        ("中等比例", "3:2"),
        ("传统屏幕", "4:3"),
        ("正方形", "1:1"),
        ("竖屏", "3:4"),
        ("竖屏", "2:3"),
        ("手机竖屏", "9:16")
    ]
    
    print("📋 尺寸映射测试:")
    print("-" * 80)
    
    for name, ratio in test_ratios:
        try:
            # 测试_get_image_dimensions方法
            image_width, image_height, keling_width, keling_height = drawing_skill._get_image_dimensions(ratio)
            
            # 计算实际比例
            actual_ratio = round(image_width / image_height, 3) if image_height != 0 else 0
            
            print(f"✅ {name:12} ({ratio:4}) → {image_width:4}x{image_height:4} (比例: {actual_ratio:.3f}) | Keling: {keling_width}:{keling_height}")
            
        except Exception as e:
            print(f"❌ {name:12} ({ratio:4}) → 错误: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 选择一个比例进行实际API测试 (避免频繁调用):")
    
    # 测试一个比例的实际API调用
    test_ratio = "1:1"  # 选择正方形进行测试
    print(f"📐 测试比例: {test_ratio}")
    
    try:
        image_width, image_height, keling_width, keling_height = drawing_skill._get_image_dimensions(test_ratio)
        print(f"📏 图片尺寸: {image_width}x{image_height}")
        
        # 直接调用jimeng服务测试
        result = drawing_skill._try_jimeng_service(
            ai_image_prompt="简单的风景画",
            image_width=image_width,
            image_height=image_height
        )
        
        if result and result.get("success"):
            print(f"✅ {test_ratio} 比例API测试成功！")
            print(f"🖼️ 图片URL: {result.get('result', {}).get('image_url', 'N/A')}")
        else:
            print(f"⚠️ {test_ratio} 比例API测试失败: {result.get('message', 'N/A') if result else 'None'}")
            
    except Exception as e:
        print(f"❌ API测试异常: {e}")
    
    print("\n🎉 所有官网支持的尺寸比例已完美集成到drawing_skill.py中！")

if __name__ == "__main__":
    test_all_official_ratios()
