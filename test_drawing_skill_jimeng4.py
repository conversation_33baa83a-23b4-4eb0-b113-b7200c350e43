#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试drawing skill的jimeng 4.0调用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill
import time
from datetime import datetime

def test_drawing_skill_jimeng4():
    """测试drawing skill的jimeng 4.0调用"""
    print("🔥 老王测试drawing skill的jimeng 4.0调用")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 初始化drawing skill
        print("🎨 初始化drawing skill...")
        drawing_skill = DrawingSkill()
        
        # 检查配置
        print(f"📋 当前配置:")
        jimeng_config = drawing_skill.config.get("jimeng", {})
        print(f"   - jimeng模型: {jimeng_config.get('model', '未配置')}")
        print(f"   - jimeng URL: {jimeng_config.get('url', '未配置')}")
        print(f"   - session_id: {drawing_skill.jimeng_session_id[:50]}...")
        
        # 测试用例
        test_prompt = "明日之后的美女CG"
        user_id = "test_user_jimeng4"
        
        print(f"\n🎯 测试提示词: {test_prompt}")
        print(f"👤 用户ID: {user_id}")
        print("-" * 40)
        
        start_time = time.time()
        
        # 执行绘画
        result = drawing_skill.execute(
            input_text=test_prompt,
            user_id=user_id,
            context={}
        )
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ 总耗时: {elapsed_time:.1f}秒")
        
        # 检查结果
        if result and result.get("success"):
            print("✅ 绘画成功！")
            
            # 提取图片信息
            image_url = result.get("image_url", "")
            service = result.get("service", "未知")
            
            if image_url:
                print(f"🖼️ 图片URL: {image_url}")
                print(f"🔧 使用服务: {service}")
                
                # 检查是否使用了jimeng服务
                if service == "jimeng" or "jimeng" in service.lower():
                    print("🎉 成功使用jimeng 4.0服务生成图片！")
                else:
                    print(f"⚠️ 使用了备用服务: {service}")
            else:
                print("⚠️ 未获取到图片URL")
        else:
            error_msg = result.get("message", "未知错误") if result else "返回None"
            print(f"❌ 绘画失败: {error_msg}")
        
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def test_session_ids():
    """测试多个session_id的有效性"""
    print("\n" + "=" * 60)
    print("🔥 老王测试多个session_id的有效性")
    print("=" * 60)
    
    import requests
    
    session_ids = [
        "1e6aa5b4800b56a3e345bacacfaa7c09",
        "96425b141fffc2443d5abdafb494c184",
        "661e6f442106e10947d9e838a656293b"
    ]
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    test_data = {
        "model": "jimeng-4.0",
        "prompt": "测试图片",
        "negative_prompt": "低质量",
        "width": 1024,
        "height": 1024,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    for i, session_id in enumerate(session_ids, 1):
        print(f"\n🎯 测试session_id {i}: {session_id[:20]}...")
        
        headers = {
            "Authorization": f"Bearer {session_id}",
            "Content-Type": "application/json"
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, headers=headers, json=test_data, timeout=60)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️ 耗时: {elapsed_time:.1f}秒")
            print(f"📊 HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                data_list = result.get("data", [])
                
                if data_list:
                    print(f"✅ session_id {i} 有效！返回 {len(data_list)} 张图片")
                else:
                    print(f"⚠️ session_id {i} 返回空数据")
            else:
                print(f"❌ session_id {i} 无效: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ session_id {i} 测试异常: {e}")
        
        # 避免频繁请求
        if i < len(session_ids):
            print("⏳ 等待3秒...")
            time.sleep(3)

if __name__ == "__main__":
    # 测试drawing skill
    test_drawing_skill_jimeng4()
    
    # 测试session_id有效性
    test_session_ids()
