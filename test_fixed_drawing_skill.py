#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试修复后的drawing skill
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill
import time

def test_fixed_drawing_skill():
    """测试修复后的drawing skill"""
    print("🔥 老王测试修复后的drawing skill")
    print("=" * 60)
    
    try:
        # 初始化drawing skill
        drawing_skill = DrawingSkill()
        
        # 测试提示词
        test_prompt = "流浪地球画面"
        user_id = "test_user_fixed"
        
        print(f"🎯 测试提示词: {test_prompt}")
        print(f"👤 用户ID: {user_id}")
        print("-" * 40)
        
        start_time = time.time()
        
        # 执行绘画
        result = drawing_skill.execute(
            input_text=test_prompt,
            user_id=user_id,
            context={}
        )
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ 总耗时: {elapsed_time:.1f}秒")
        
        # 检查结果
        if result and result.get("success"):
            print("✅ 绘画成功！")
            
            # 提取图片信息
            image_url = result.get("image_url", "")
            service = result.get("service", "未知")
            
            if image_url:
                print(f"🖼️ 图片URL: {image_url}")
                print(f"🔧 使用服务: {service}")
                
                # 检查是否使用了jimeng服务
                if service == "jimeng" or "jimeng" in service.lower():
                    print("🎉 成功使用jimeng 4.0服务生成图片！")
                    print("✅ 多session_id自动切换机制工作正常！")
                else:
                    print(f"⚠️ 使用了备用服务: {service}")
                    print("ℹ️ jimeng服务可能仍有问题，但降级机制正常")
            else:
                print("⚠️ 未获取到图片URL")
        else:
            error_msg = result.get("message", "未知错误") if result else "返回None"
            print(f"❌ 绘画失败: {error_msg}")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_fixed_drawing_skill()
