#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Activity Generator 集成增强

为enhanced_activity_script_generator.py集成新的人设配置和活动生成逻辑

作者: 老王
创建时间: 2025-08-30
版本: 1.0
"""

import json
import os
from typing import Dict, Any

def load_enhanced_personality_config() -> Dict[str, Any]:
    """加载增强版人格配置"""
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'yanran_personality_enhanced.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载增强人格配置失败: {e}")
        # 返回默认配置
        return {}

def load_activity_generation_config() -> Dict[str, Any]:
    """加载活动生成专用配置"""
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'activity_generation.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载活动生成配置失败: {e}")
        # 返回默认配置
        return {}

def get_enhanced_personality_prompt(config: Dict[str, Any]) -> str:
    """根据配置生成增强的人格提示"""
    
    # 获取AI决策提示配置
    ai_prompts = config.get('ai_decision_prompts', {})
    sharing_eval = ai_prompts.get('sharing_evaluation', {})
    system_prompt = sharing_eval.get('system_prompt', '')
    
    # 获取活动生成增强配置
    activity_enhancement = sharing_eval.get('activity_generation_enhancement', {})
    context_prompt = activity_enhancement.get('context_prompt', '')
    
    return f"""
{system_prompt}

## 🎯 活动生成专用指引
{context_prompt}

在生成活动时，请特别注意体现：
1. **财经专业身份** - 体现市场敏感度、数据分析能力、行业视角
2. **旅游探索精神** - 展现文化好奇心、地理敏感度、探索动力
3. **生活品质追求** - 注重体验质量、文化内涵、个人成长
4. **真实可信性** - 基于具体场景、合理逻辑、自然表达
"""

def get_activity_type_weights(time_slot: str, config: Dict[str, Any]) -> Dict[str, float]:
    """根据时间段获取活动类型权重"""
    
    activity_profile = config.get('activity_generation_profile', {})
    weights = activity_profile.get('persona_driven_weights', {})
    
    return weights.get(time_slot, {
        '财经工作': 0.25,
        '旅游探索': 0.25,
        '日常生活': 0.35,
        '社交娱乐': 0.15
    })

def get_preferred_locations(config: Dict[str, Any]) -> Dict[str, list]:
    """获取偏好地点配置"""
    
    activity_profile = config.get('activity_generation_profile', {})
    location_prefs = activity_profile.get('location_preferences', {})
    
    return {
        'finance_locations': location_prefs.get('finance_priority_locations', []),
        'travel_locations': location_prefs.get('travel_priority_locations', []),
        'avoid_locations': location_prefs.get('avoid_overuse_locations', [])
    }

def enhance_poi_selection(nearby_pois: list, activity_type: str, config: Dict[str, Any]) -> list:
    """根据配置增强POI选择逻辑"""
    
    location_prefs = get_preferred_locations(config)
    
    # 根据活动类型优先选择对应POI
    if '财经' in activity_type or '工作' in activity_type:
        preferred = location_prefs['finance_locations']
    elif '旅游' in activity_type or '探索' in activity_type:
        preferred = location_prefs['travel_locations']
    else:
        preferred = []
    
    # 避免过度使用的地点
    avoid_locations = location_prefs['avoid_locations']
    
    # 重新排序POI
    enhanced_pois = []
    
    # 优先加入偏好地点
    for poi in nearby_pois:
        poi_name = poi.get('name', '')
        if any(pref in poi_name for pref in preferred):
            enhanced_pois.append(poi)
    
    # 加入其他地点（但避免过度使用的）
    for poi in nearby_pois:
        poi_name = poi.get('name', '')
        if poi not in enhanced_pois and not any(avoid in poi_name for avoid in avoid_locations):
            enhanced_pois.append(poi)
    
    # 如果没有偏好地点，保留原始顺序但移除避免地点
    if not enhanced_pois:
        enhanced_pois = [poi for poi in nearby_pois 
                        if not any(avoid in poi.get('name', '') for avoid in avoid_locations)]
    
    return enhanced_pois

def get_activity_quality_prompt(activity_type: str, config: Dict[str, Any]) -> str:
    """获取活动质量提升提示"""
    
    activity_gen_config = config.get('activity_generation_config', {})
    templates = activity_gen_config.get('activity_templates', {})
    
    if '财经' in activity_type or '工作' in activity_type:
        template_examples = templates.get('finance_professional', [])
    elif '旅游' in activity_type or '探索' in activity_type:
        template_examples = templates.get('travel_exploration', [])
    else:
        template_examples = []
    
    if template_examples:
        example_text = "\n".join([f"  • {t.get('template', '')}" for t in template_examples[:2]])
        return f"""
## 📝 活动描述参考模板：
{example_text}

请确保活动描述具有相应的专业深度和文化内涵。
"""
    
    return ""

# 集成函数示例
def integrate_enhanced_configs():
    """集成增强配置的示例函数"""
    
    # 加载配置
    personality_config = load_enhanced_personality_config()
    activity_config = load_activity_generation_config()
    
    # 生成增强提示
    enhanced_prompt = get_enhanced_personality_prompt(personality_config)
    
    # 获取权重配置
    evening_weights = get_activity_type_weights('evening', personality_config)
    
    # 获取地点偏好
    location_prefs = get_preferred_locations(personality_config)
    
    print("集成配置示例:")
    print(f"晚间活动权重: {evening_weights}")
    print(f"财经地点偏好: {location_prefs['finance_locations'][:3]}")
    print(f"旅游地点偏好: {location_prefs['travel_locations'][:3]}")
    
    return {
        'personality_config': personality_config,
        'activity_config': activity_config,
        'enhanced_prompt': enhanced_prompt,
        'location_preferences': location_prefs
    }

if __name__ == "__main__":
    integrate_enhanced_configs()
