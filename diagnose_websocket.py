#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket服务诊断工具
帮助用户诊断WebSocket连接问题
"""

import subprocess
import sys
import os
import json
from datetime import datetime

def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return -1, "", "命令超时"
    except Exception as e:
        return -1, "", str(e)

def check_port_usage(port):
    """检查端口占用情况"""
    print(f"🔍 检查端口 {port} 占用情况...")
    
    code, stdout, stderr = run_command(f"lsof -i :{port}")
    
    if code == 0 and stdout:
        print(f"✅ 端口 {port} 正在被使用:")
        lines = stdout.split('\n')
        for line in lines:
            if line.strip():
                print(f"   {line}")
        return True
    else:
        print(f"❌ 端口 {port} 未被使用")
        return False

def check_main_process():
    """检查主服务进程"""
    print(f"🔍 检查主服务进程...")
    
    code, stdout, stderr = run_command("ps aux | grep 'python.*main.py' | grep -v grep")
    
    if code == 0 and stdout:
        print(f"✅ 发现主服务进程:")
        lines = stdout.split('\n')
        for line in lines:
            if line.strip():
                print(f"   {line}")
        return True
    else:
        print(f"❌ 未发现主服务进程")
        print(f"💡 请启动主服务: python main.py")
        return False

def check_websocket_logs():
    """检查WebSocket相关日志"""
    print(f"🔍 检查WebSocket服务日志...")
    
    if not os.path.exists("main.log"):
        print(f"❌ 未找到日志文件 main.log")
        return False
    
    code, stdout, stderr = run_command("grep -i 'websocket\\|unified.*service' main.log | tail -10")
    
    if code == 0 and stdout:
        print(f"✅ 最近的WebSocket相关日志:")
        lines = stdout.split('\n')
        for line in lines:
            if line.strip():
                print(f"   {line}")
        return True
    else:
        print(f"❌ 未找到WebSocket相关日志")
        return False

def test_basic_connection():
    """测试基础网络连接"""
    print(f"🔍 测试基础网络连接...")
    
    # 测试本地回环
    code, stdout, stderr = run_command("ping -c 1 127.0.0.1")
    if code == 0:
        print(f"✅ 本地回环连接正常")
    else:
        print(f"❌ 本地回环连接异常")
        return False
    
    # 测试端口连通性
    code, stdout, stderr = run_command("nc -z 127.0.0.1 8765")
    if code == 0:
        print(f"✅ 端口8765连通性正常")
        return True
    else:
        print(f"❌ 端口8765连通性异常")
        return False

def check_python_dependencies():
    """检查Python依赖"""
    print(f"🔍 检查Python依赖...")
    
    try:
        import websockets
        print(f"✅ websockets库已安装，版本: {websockets.__version__}")
    except ImportError:
        print(f"❌ websockets库未安装")
        print(f"💡 请安装: pip install websockets")
        return False
    
    try:
        import asyncio
        print(f"✅ asyncio库可用")
    except ImportError:
        print(f"❌ asyncio库不可用")
        return False
    
    return True

def main():
    """主诊断流程"""
    print(f"🩺 WebSocket服务诊断工具")
    print(f"⏰ 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    all_checks = []
    
    # 检查Python依赖
    print(f"\n1️⃣ Python依赖检查")
    print("-" * 30)
    all_checks.append(check_python_dependencies())
    
    # 检查主服务进程
    print(f"\n2️⃣ 主服务进程检查")
    print("-" * 30)
    all_checks.append(check_main_process())
    
    # 检查端口占用
    print(f"\n3️⃣ 端口占用检查")
    print("-" * 30)
    all_checks.append(check_port_usage(8765))
    
    # 检查WebSocket日志
    print(f"\n4️⃣ WebSocket日志检查")
    print("-" * 30)
    all_checks.append(check_websocket_logs())
    
    # 测试基础连接
    print(f"\n5️⃣ 基础连接测试")
    print("-" * 30)
    all_checks.append(test_basic_connection())
    
    # 诊断结果
    print(f"\n📊 诊断结果汇总")
    print("=" * 60)
    
    passed = sum(all_checks)
    total = len(all_checks)
    
    print(f"✅ 通过检查: {passed}/{total}")
    
    if passed == total:
        print(f"🎉 所有检查都通过了！")
        print(f"💡 WebSocket服务应该可以正常工作")
        print(f"💡 如果仍有连接问题，可能是客户端代码的问题")
    else:
        print(f"⚠️ 发现 {total - passed} 个问题")
        print(f"💡 请根据上述检查结果修复问题后重试")
    
    # 提供解决方案建议
    print(f"\n💡 常见解决方案:")
    print(f"   1. 确保主服务正在运行: python main.py")
    print(f"   2. 检查端口是否被其他程序占用")
    print(f"   3. 查看main.log中的错误信息")
    print(f"   4. 重启主服务以应用最新配置")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 诊断中断")
        sys.exit(1) 