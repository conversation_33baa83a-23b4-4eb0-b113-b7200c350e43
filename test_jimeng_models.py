#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试不同的即梦模型
"""

import requests
import json
import time
from datetime import datetime

def test_jimeng_model(model_name, timeout=300):
    """测试指定的即梦模型"""
    
    print(f"🧪 测试模型: {model_name}")
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model_name,
        "prompt": "一只可爱的小猫",
        "negative_prompt": "低质量",
        "width": 512,
        "height": 512,
        "sample_strength": 0.8,
        "response_format": "url"
    }
    
    try:
        start_time = time.time()
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        response = requests.post(url, headers=headers, json=data, timeout=timeout)
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ 耗时: {elapsed_time:.1f}秒")
        
        if response.status_code == 200:
            result = response.json()
            data_list = result.get("data", [])
            
            if data_list:
                print(f"✅ 成功！返回 {len(data_list)} 张图片")
                for i, item in enumerate(data_list):
                    if isinstance(item, dict) and "url" in item:
                        print(f"   图片 {i+1}: {item['url'][:50]}...")
                return True
            else:
                print("❌ 返回空数据")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"   错误内容: {response.text[:100]}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ 超时（{timeout}秒）")
        return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def main():
    print("🔥 老王测试不同的即梦模型")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试不同的模型
    models_to_test = [
        "jimeng-3.0",    # 试试3.0版本
        "jimeng-2.1",    # 试试2.1版本
        "jimeng-4.0",    # 最后试4.0
    ]
    
    successful_models = []
    
    for model in models_to_test:
        print(f"\n📋 测试模型: {model}")
        print("-" * 40)
        
        success = test_jimeng_model(model, timeout=600)  # 10分钟超时
        
        if success:
            successful_models.append(model)
            print(f"🎉 模型 {model} 测试成功！")
            break  # 找到一个能用的就停止
        else:
            print(f"❌ 模型 {model} 测试失败")
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    
    if successful_models:
        print(f"✅ 成功的模型: {', '.join(successful_models)}")
        print("💡 建议：在drawing_skill中使用成功的模型")
    else:
        print("❌ 所有模型都失败了")
        print("💡 可能的原因：")
        print("   1. session_id无效或过期")
        print("   2. 即梦服务端问题")
        print("   3. 账户配额不足")
        print("   4. jimeng-free-api配置问题")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
