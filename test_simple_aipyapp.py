#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化版aipyapp测试
直接测试HTTP API调用，不依赖复杂的技能系统

作者: 隔壁老王 (暴躁的代码架构师)
"""

import json
import time
import requests
from urllib.parse import urljoin


def test_api_connection():
    """测试API连接"""
    print("🔗 === API连接测试 ===")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8848/health", timeout=10)
        if response.status_code == 200:
            print("✅ API服务连接正常")
            health_data = response.json()
            print(f"服务状态: {health_data.get('status')}")
            print(f"时间戳: {health_data.get('timestamp')}")
            return True
        else:
            print(f"❌ API服务响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确认aipyapp服务是否启动")
        print("启动命令: aipy --agent --host 0.0.0.0 --port 8848")
        return False
    except Exception as e:
        print(f"❌ API连接测试异常: {e}")
        return False


def test_simple_task():
    """测试简单任务"""
    print("\n🧪 === 简单任务测试 ===")
    
    api_base_url = "http://localhost:8848"
    
    # 简单的Python任务
    simple_instruction = """
    请帮我创建一个简单的北疆旅游景点数据分析：
    
    1. 创建一个包含北疆主要景点的数据表，包含以下信息：
       - 景点名称
       - 最佳游览时间
       - 门票价格
       - 推荐游览天数
    
    2. 计算平均门票价格
    3. 生成一个简单的统计图表
    4. 输出分析结果
    
    请使用pandas和matplotlib来完成这个任务。
    """
    
    try:
        # 构建API请求
        api_url = urljoin(api_base_url, "/tasks")
        
        payload = {
            "instruction": simple_instruction,
            "metadata": {
                "execution_id": "test_simple",
                "source": "test_script",
                "timestamp": time.time()
            }
        }
        
        print("🚀 发送任务请求...")
        print(f"API地址: {api_url}")
        
        # 发送任务请求
        response = requests.post(
            api_url,
            json=payload,
            timeout=30,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"✅ 任务提交成功，任务ID: {task_id}")
            
            # 轮询任务状态
            return poll_task_status(api_base_url, task_id)
        else:
            print(f"❌ 任务提交失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 任务执行异常: {e}")
        return False


def test_xinjiang_travel_task():
    """测试北疆旅游攻略任务"""
    print("\n🏔️ === 北疆6日游攻略分析测试 ===")
    
    api_base_url = "http://localhost:8848"
    
    # 北疆旅游攻略分析任务
    travel_instruction = """
    请帮我制定一个详细的"10.1 北疆6日游攻略"，包括以下内容：

    1. 创建北疆主要景点数据表：
       - 景点：喀纳斯湖、禾木村、白哈巴村、五彩滩、可可托海、天山天池
       - 包含：景点名称、最佳游览时间、门票价格、推荐停留时间、特色描述

    2. 10月份天气分析：
       - 创建10月份北疆天气数据（温度、降水、风力等）
       - 分析最适合旅游的日期

    3. 行程规划：
       - 设计6天的详细行程安排
       - 计算景点间距离和行车时间
       - 推荐住宿地点

    4. 费用预算：
       - 估算交通、住宿、门票、餐饮费用
       - 制作费用预算表和饼图

    5. 生成可视化图表：
       - 行程路线图
       - 费用分布饼图  
       - 天气趋势图

    请使用pandas、matplotlib、seaborn等库来完成数据分析和可视化。
    """
    
    try:
        # 构建API请求
        api_url = urljoin(api_base_url, "/tasks")
        
        payload = {
            "instruction": travel_instruction,
            "metadata": {
                "execution_id": "xinjiang_travel",
                "source": "test_script",
                "timestamp": time.time(),
                "task_type": "travel_analysis"
            }
        }
        
        print("🚀 发送北疆旅游攻略分析任务...")
        
        # 发送任务请求
        response = requests.post(
            api_url,
            json=payload,
            timeout=30,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"✅ 任务提交成功，任务ID: {task_id}")
            
            # 轮询任务状态
            return poll_task_status(api_base_url, task_id)
        else:
            print(f"❌ 任务提交失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 任务执行异常: {e}")
        return False


def poll_task_status(api_base_url, task_id):
    """轮询任务状态"""
    max_polls = 60  # 最多轮询60次
    poll_interval = 5  # 每5秒轮询一次
    
    print(f"⏳ 开始轮询任务状态，任务ID: {task_id}")
    
    for i in range(max_polls):
        try:
            # 查询任务状态
            status_url = urljoin(api_base_url, f"/tasks/{task_id}")
            response = requests.get(status_url, timeout=30)

            if response.status_code == 200:
                status_data = response.json()
                status = status_data.get("status")

                print(f"📊 轮询 {i+1}/{max_polls}: 任务状态 = {status}")

                if status == "completed":
                    print("🎉 任务执行完成！")

                    # 获取任务结果
                    result_url = urljoin(api_base_url, f"/tasks/{task_id}/result")
                    result_response = requests.get(result_url, timeout=30)
                    
                    if result_response.status_code == 200:
                        result_data = result_response.json()
                        
                        print("\n📋 === 任务执行结果 ===")
                        print(f"任务ID: {result_data.get('task_id')}")
                        print(f"指令: {result_data.get('instruction', '')[:100]}...")
                        print(f"状态: {result_data.get('status')}")
                        print(f"创建时间: {result_data.get('created_at')}")
                        print(f"完成时间: {result_data.get('completed_at')}")
                        
                        # 显示输出结果
                        output = result_data.get("output")
                        if output:
                            print(f"\n📝 执行输出:")
                            if isinstance(output, dict):
                                for key, value in output.items():
                                    print(f"  {key}: {str(value)[:200]}...")
                            else:
                                print(f"  {str(output)[:500]}...")
                        
                        return True
                    else:
                        print(f"❌ 获取任务结果失败: {result_response.status_code}")
                        return False
                        
                elif status == "failed":
                    error = status_data.get("error", "任务执行失败")
                    print(f"❌ 任务执行失败: {error}")
                    return False
                    
                elif status in ["pending", "running"]:
                    # 继续等待
                    time.sleep(poll_interval)
                    continue
                else:
                    print(f"❌ 未知任务状态: {status}")
                    return False
            else:
                print(f"⚠️ 状态查询失败: {response.status_code}")
                time.sleep(poll_interval)
                
        except Exception as e:
            print(f"⚠️ 状态轮询异常: {e}")
            time.sleep(poll_interval)
    
    print("⏰ 任务执行超时")
    return False


def main():
    """主函数"""
    print("🏔️ aipyapp 北疆旅游攻略分析测试")
    print("作者: 隔壁老王 (暴躁的代码架构师)")
    print("=" * 60)
    
    # 1. 测试API连接
    if not test_api_connection():
        print("\n❌ API连接失败，请先启动aipyapp服务")
        print("启动命令: aipy --agent --host 0.0.0.0 --port 8848")
        return 1
    
    # 2. 测试简单任务
    print("\n" + "="*60)
    simple_success = test_simple_task()
    
    if simple_success:
        print("\n✅ 简单任务测试通过！")
        
        # 3. 测试复杂的北疆旅游攻略任务
        print("\n" + "="*60)
        complex_success = test_xinjiang_travel_task()
        
        if complex_success:
            print("\n🎉 北疆旅游攻略分析测试完成！")
        else:
            print("\n⚠️ 复杂任务测试失败")
    else:
        print("\n⚠️ 简单任务测试失败，跳过复杂任务")
    
    print("\n" + "="*60)
    print("🔥 老王点评:")
    print("如果测试通过，说明aipyapp集成成功！")
    print("现在可以通过HTTP API执行复杂的Python分析任务了！")
    print("这就是数字生命的'手和脚'能力！")
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
