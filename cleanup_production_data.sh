#!/bin/bash

# 林嫣然数字生命体系统 - 生产环境数据清理脚本
# 版本: v3.1.0
# 作者: Yanran Digital Life Team
# 创建日期: 2024-12-29

set -euo pipefail

# ==================== 配置变量 ====================

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DATA_DIR="$PROJECT_DIR/data"
BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
CLEANUP_LOG="cleanup_${BACKUP_TIMESTAMP}.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# ==================== 工具函数 ====================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$CLEANUP_LOG"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$CLEANUP_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$CLEANUP_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$CLEANUP_LOG"
}

show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   🌸 林嫣然数字生命体系统 🌸                   ║"
    echo "║                     生产环境数据清理工具                        ║"
    echo "║                        Version 3.1.0                        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 计算目录大小
get_dir_size() {
    local dir="$1"
    if [[ -d "$dir" ]]; then
        du -sh "$dir" 2>/dev/null | cut -f1
    else
        echo "0B"
    fi
}

# 计算文件数量
get_file_count() {
    local dir="$1"
    if [[ -d "$dir" ]]; then
        find "$dir" -type f 2>/dev/null | wc -l
    else
        echo "0"
    fi
}

# 安全删除目录
safe_remove_dir() {
    local dir="$1"
    local description="$2"
    
    if [[ -d "$dir" ]]; then
        local size=$(get_dir_size "$dir")
        local count=$(get_file_count "$dir")
        
        log_info "删除 $description: $dir (大小: $size, 文件: $count个)"
        rm -rf "$dir"
        log_success "✅ 已删除: $description"
    else
        log_info "⏭️  跳过 $description: 目录不存在"
    fi
}

# 安全删除文件
safe_remove_file() {
    local file="$1"
    local description="$2"
    
    if [[ -f "$file" ]]; then
        local size=$(du -sh "$file" 2>/dev/null | cut -f1)
        
        log_info "删除 $description: $file (大小: $size)"
        rm -f "$file"
        log_success "✅ 已删除: $description"
    else
        log_info "⏭️  跳过 $description: 文件不存在"
    fi
}

# 确认操作
confirm_action() {
    local message="$1"
    
    echo -e "${YELLOW}$message${NC}"
    read -p "确认继续吗? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
}

# ==================== 数据分析函数 ====================

analyze_test_data() {
    log_info "🔍 分析测试数据..."
    
    echo -e "${CYAN}==================== 测试数据分析 ====================${NC}"
    
    # 1. 备份目录分析
    echo -e "${WHITE}📦 2025年6月6日备份目录:${NC}"
    local backup_dirs=(
        "analytics_backup_20250606_112247"
        "autonomy_backup_20250606_112246" 
        "emotion_backup_20250606_112247"
        "emotions_backup_20250606_112247"
        "errors_backup_20250606_112247"
        "feedback_learning_backup_20250606_112247"
        "feedback_visualization_backup_20250606_112246"
        "histories_backup_20250606_112246"
        "intelligence_backup_20250606_112246"
        "memory_backup_20250606_112246"
        "memories_backup_20250606_112247"
        "mock_backup_20250606_112246"
        "semantic_memory_backup_20250606_112246"
        "snapshots_backup_20250606_112246"
        "society_backup_20250606_112246"
        "users_backup_20250606_112246"
        "visualization_backup_20250606_112246"
        "visualizations_backup_20250606_112246"
    )
    
    local total_backup_size=0
    local total_backup_files=0
    
    for dir in "${backup_dirs[@]}"; do
        local full_path="$DATA_DIR/$dir"
        if [[ -d "$full_path" ]]; then
            local size=$(get_dir_size "$full_path")
            local count=$(get_file_count "$full_path")
            echo "  📁 $dir: $size ($count 文件)"
            
            # 累计大小（简化计算，仅统计KB以上）
            if [[ "$size" =~ ([0-9]+\.?[0-9]*)M ]]; then
                total_backup_size=$((total_backup_size + $(echo "${BASH_REMATCH[1]}" | cut -d. -f1)))
            fi
            total_backup_files=$((total_backup_files + count))
        fi
    done
    
    echo -e "${YELLOW}  💾 备份总计: ~${total_backup_size}MB, ${total_backup_files} 文件${NC}"
    
    # 2. 意识状态备份分析
    echo -e "${WHITE}🧠 意识状态备份:${NC}"
    local consciousness_backup_dir="$DATA_DIR/consciousness/backups"
    if [[ -d "$consciousness_backup_dir" ]]; then
        local consciousness_size=$(get_dir_size "$consciousness_backup_dir")
        local consciousness_count=$(get_file_count "$consciousness_backup_dir")
        echo "  📁 consciousness/backups: $consciousness_size ($consciousness_count 文件)"
    fi
    
    # 3. Demo和测试目录分析
    echo -e "${WHITE}🧪 Demo和测试目录:${NC}"
    local demo_dirs=(
        "demo"
        "test"
        "temp_chroma"
        "cache"
        "mock"
    )
    
    for dir in "${demo_dirs[@]}"; do
        local full_path="$DATA_DIR/$dir"
        if [[ -d "$full_path" ]]; then
            local size=$(get_dir_size "$full_path")
            local count=$(get_file_count "$full_path")
            echo "  📁 $dir: $size ($count 文件)"
        fi
    done
    
    # 4. 神经模型和快照分析
    echo -e "${WHITE}🤖 模型和快照数据:${NC}"
    local model_dirs=(
        "neural_models"
        "snapshots"
    )
    
    for dir in "${model_dirs[@]}"; do
        local full_path="$DATA_DIR/$dir"
        if [[ -d "$full_path" ]]; then
            local size=$(get_dir_size "$full_path")
            local count=$(get_file_count "$full_path")
            echo "  📁 $dir: $size ($count 文件)"
        fi
    done
    
    echo -e "${CYAN}=================================================${NC}"
}

# ==================== 清理函数 ====================

cleanup_backup_data() {
    log_info "🗑️  清理2025年6月6日备份数据..."
    
    # 删除所有2025年6月6日的备份目录
    local backup_dirs=(
        "analytics_backup_20250606_112247"
        "autonomy_backup_20250606_112246"
        "emotion_backup_20250606_112247"
        "emotions_backup_20250606_112247"
        "errors_backup_20250606_112247"
        "feedback_learning_backup_20250606_112247"
        "feedback_visualization_backup_20250606_112246"
        "histories_backup_20250606_112246"
        "intelligence_backup_20250606_112246"
        "memory_backup_20250606_112246"
        "memories_backup_20250606_112247"
        "mock_backup_20250606_112246"
        "semantic_memory_backup_20250606_112246"
        "snapshots_backup_20250606_112246"
        "society_backup_20250606_112246"
        "users_backup_20250606_112246"
        "visualization_backup_20250606_112246"
        "visualizations_backup_20250606_112246"
    )
    
    for dir in "${backup_dirs[@]}"; do
        safe_remove_dir "$DATA_DIR/$dir" "2025年6月6日备份: $dir"
    done
}

cleanup_consciousness_backups() {
    log_info "🧠 清理意识状态备份文件..."
    
    local consciousness_backup_dir="$DATA_DIR/consciousness/backups"
    if [[ -d "$consciousness_backup_dir" ]]; then
        # 保留最新的10个备份文件，删除其余的
        local backup_files=($(ls -t "$consciousness_backup_dir"/consciousness_state_*.json 2>/dev/null | tail -n +11))
        
        if [[ ${#backup_files[@]} -gt 0 ]]; then
            log_info "保留最新10个意识状态备份，删除 ${#backup_files[@]} 个旧备份"
            for file in "${backup_files[@]}"; do
                rm -f "$file"
            done
            log_success "✅ 清理完成，保留最新10个意识状态备份"
        else
            log_info "⏭️  意识状态备份文件数量合理，无需清理"
        fi
    fi
}

cleanup_demo_test_data() {
    log_info "🧪 清理Demo和测试数据..."
    
    # 删除demo和test目录
    safe_remove_dir "$DATA_DIR/demo" "Demo演示数据"
    safe_remove_dir "$DATA_DIR/test" "测试数据"
    
    # 删除临时和缓存目录
    safe_remove_dir "$DATA_DIR/temp_chroma" "临时ChromaDB数据"
    safe_remove_dir "$DATA_DIR/cache" "缓存数据"
    safe_remove_dir "$DATA_DIR/mock" "模拟数据"
    
    # 删除包含demo的记忆备份目录中的demo数据
    local memories_backup_dir="$DATA_DIR/memories_backup_20250606_112247"
    if [[ -d "$memories_backup_dir" ]]; then
        find "$memories_backup_dir" -name "*demo*" -type d -exec rm -rf {} + 2>/dev/null || true
        find "$memories_backup_dir" -name "*test*" -type d -exec rm -rf {} + 2>/dev/null || true
        log_success "✅ 清理记忆备份中的demo数据"
    fi
}

cleanup_visualization_data() {
    log_info "📊 清理可视化数据..."
    
    # 清理可视化相关目录（通常是开发调试用）
    safe_remove_dir "$DATA_DIR/feedback_visualization" "反馈可视化数据"
    safe_remove_dir "$DATA_DIR/visualization" "可视化数据"
    safe_remove_dir "$DATA_DIR/visualizations" "可视化图表数据"
    safe_remove_dir "$DATA_DIR/pattern_recognition" "模式识别数据"
}

cleanup_large_model_data() {
    log_info "🤖 清理大型模型数据..."
    
    echo -e "${YELLOW}⚠️  注意: 以下操作将删除训练好的神经模型和系统快照${NC}"
    echo -e "${YELLOW}   这些数据较大但包含系统学习成果，删除后需要重新训练${NC}"
    
    confirm_action "是否删除神经模型数据? (约34MB)"
    safe_remove_dir "$DATA_DIR/neural_models" "神经网络模型数据"
    
    confirm_action "是否删除系统快照数据? (约131MB)"
    safe_remove_dir "$DATA_DIR/snapshots" "系统状态快照数据"
}

cleanup_empty_directories() {
    log_info "📁 清理空目录..."
    
    # 查找并删除空目录
    find "$DATA_DIR" -type d -empty -delete 2>/dev/null || true
    
    log_success "✅ 空目录清理完成"
}

# ==================== 备份关键数据 ====================

backup_essential_data() {
    log_info "💾 备份关键生产数据..."
    
    local backup_dir="data_backup_before_cleanup_$BACKUP_TIMESTAMP"
    mkdir -p "$backup_dir"
    
    # 备份关键配置和数据
    local essential_files=(
        "digital_life.db"
        "yanran.db"
        "user_preferences.json"
        "features.json"
        "decision_history.json"
    )
    
    local essential_dirs=(
        "consciousness/consciousness_state.json"
        "emotion/current_state.json"
        "emotions/current_state.json"
        "chroma_db"
        "memories/index.json"
        "users"
        "histories"
    )
    
    # 备份关键文件
    for file in "${essential_files[@]}"; do
        if [[ -f "$DATA_DIR/$file" ]]; then
            cp "$DATA_DIR/$file" "$backup_dir/"
            log_info "✅ 已备份: $file"
        fi
    done
    
    # 备份关键目录
    for dir in "${essential_dirs[@]}"; do
        if [[ -e "$DATA_DIR/$dir" ]]; then
            cp -r "$DATA_DIR/$dir" "$backup_dir/"
            log_info "✅ 已备份: $dir"
        fi
    done
    
    # 压缩备份
    tar -czf "${backup_dir}.tar.gz" "$backup_dir"
    rm -rf "$backup_dir"
    
    log_success "🎉 关键数据备份完成: ${backup_dir}.tar.gz"
}

# ==================== 主要清理流程 ====================

run_full_cleanup() {
    log_info "🚀 开始完整数据清理流程..."
    
    # 1. 备份关键数据
    backup_essential_data
    
    # 2. 清理备份数据
    cleanup_backup_data
    
    # 3. 清理意识状态备份
    cleanup_consciousness_backups
    
    # 4. 清理demo和测试数据
    cleanup_demo_test_data
    
    # 5. 清理可视化数据
    cleanup_visualization_data
    
    # 6. 清理空目录
    cleanup_empty_directories
    
    log_success "🎉 数据清理完成!"
}

run_safe_cleanup() {
    log_info "🛡️  开始安全数据清理流程..."
    
    # 只清理明确的测试和备份数据，保留所有可能有用的数据
    
    # 1. 备份关键数据
    backup_essential_data
    
    # 2. 清理明确的备份数据
    cleanup_backup_data
    
    # 3. 清理部分意识状态备份（保留最新10个）
    cleanup_consciousness_backups
    
    # 4. 清理明确的demo和test目录
    safe_remove_dir "$DATA_DIR/demo" "Demo演示数据"
    safe_remove_dir "$DATA_DIR/test" "测试数据"
    safe_remove_dir "$DATA_DIR/temp_chroma" "临时ChromaDB数据"
    safe_remove_dir "$DATA_DIR/cache" "缓存数据"
    
    # 5. 清理空目录
    cleanup_empty_directories
    
    log_success "🎉 安全数据清理完成!"
}

# ==================== 显示帮助 ====================

show_help() {
    show_banner
    echo -e "${WHITE}用法:${NC} $0 [命令]"
    echo ""
    echo -e "${WHITE}命令:${NC}"
    echo -e "  ${GREEN}analyze${NC}      分析测试数据"
    echo -e "  ${GREEN}safe${NC}         安全清理（推荐）"
    echo -e "  ${GREEN}full${NC}         完整清理"
    echo -e "  ${GREEN}models${NC}       清理大型模型数据"
    echo -e "  ${GREEN}backup${NC}       仅备份关键数据"
    echo -e "  ${GREEN}help${NC}         显示帮助"
    echo ""
    echo -e "${WHITE}清理类型说明:${NC}"
    echo -e "  ${YELLOW}safe${NC}    - 删除明确的测试数据和备份，保留可能有用的数据"
    echo -e "  ${YELLOW}full${NC}    - 删除所有测试数据、备份和可视化数据"
    echo -e "  ${YELLOW}models${NC}  - 删除大型神经模型和快照数据（需要确认）"
    echo ""
    echo -e "${WHITE}示例:${NC}"
    echo -e "  $0 analyze        # 分析测试数据"
    echo -e "  $0 safe           # 安全清理（推荐）"
    echo -e "  $0 full           # 完整清理"
    echo ""
}

# ==================== 主程序 ====================

main() {
    local command="${1:-help}"
    
    # 检查data目录是否存在
    if [[ ! -d "$DATA_DIR" ]]; then
        log_error "data目录不存在: $DATA_DIR"
        exit 1
    fi
    
    cd "$PROJECT_DIR"
    
    case $command in
        analyze)
            show_banner
            analyze_test_data
            ;;
        safe)
            show_banner
            echo -e "${GREEN}🛡️  安全清理模式${NC}"
            echo -e "${YELLOW}将删除明确的测试数据和备份，保留可能有用的数据${NC}"
            confirm_action "确认开始安全清理?"
            run_safe_cleanup
            ;;
        full)
            show_banner
            echo -e "${RED}⚠️  完整清理模式${NC}"
            echo -e "${YELLOW}将删除所有测试数据、备份和可视化数据${NC}"
            confirm_action "确认开始完整清理?"
            run_full_cleanup
            ;;
        models)
            show_banner
            cleanup_large_model_data
            ;;
        backup)
            show_banner
            backup_essential_data
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
    
    # 显示清理后的状态
    if [[ "$command" != "help" && "$command" != "analyze" ]]; then
        echo ""
        log_info "📊 清理后data目录状态:"
        du -sh "$DATA_DIR"/* 2>/dev/null | sort -hr | head -10
        echo ""
        log_info "📋 清理日志已保存到: $CLEANUP_LOG"
    fi
}

# 执行主程序
main "$@" 