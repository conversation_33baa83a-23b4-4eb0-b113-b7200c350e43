#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证自主探索器返回真实地点

确保所有地点都是真实存在的物理地址，不再有"示例"或虚拟数据
作者: 老王
创建时间: 2025-08-31
"""

import asyncio
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.autonomous_exploration.autonomous_location_explorer import get_autonomous_location_explorer

async def validate_real_locations():
    """验证返回的地点都是真实存在的"""
    print("🔍 验证自主探索器返回真实地点功能...")
    
    explorer = get_autonomous_location_explorer()
    
    # 测试地点（上海市中心）
    test_location = {
        'latitude': 31.2304,
        'longitude': 121.4737,
        'city': '上海',
        'district': '黄浦区'
    }
    
    test_context = {
        'mood': '兴奋',
        'weather': {'temperature': 25, 'condition': '晴天'},
        'time_slot': 'afternoon'
    }
    
    # 测试多种活动类型
    activity_types = ['财经工作', '旅游探索', '日常生活', '咖啡休闲', '健身运动']
    
    all_results = []
    
    print("\n" + "="*60)
    
    for activity_type in activity_types:
        print(f"\n🎯 验证 {activity_type} 活动的真实地点发现...")
        
        try:
            result = await explorer.autonomous_explore_location(
                user_id="linyanran",
                current_location=test_location,
                activity_type=activity_type,
                context=test_context
            )
            
            location_name = result.location.get('name', '未知')
            location_address = result.location.get('address', '无地址')
            
            print(f"  ✅ 发现地点: {location_name}")
            print(f"  📍 地址: {location_address}")
            print(f"  🔍 发现方法: {result.discovery_method}")
            print(f"  💡 探索理由: {result.exploration_reason}")
            print(f"  📊 置信度: {result.confidence:.2f}")
            
            # 检查是否是假数据
            is_fake = (
                '示例' in location_name or 
                'example' in location_name.lower() or
                'test' in location_name.lower() or
                'mock' in location_name.lower() or
                '虚拟' in location_name
            )
            
            if is_fake:
                print(f"  ❌ 警告: 发现虚假地点名称: {location_name}")
            else:
                print(f"  ✅ 地点名称合规: {location_name}")
            
            all_results.append({
                'activity_type': activity_type,
                'location_name': location_name,
                'location_address': location_address,
                'discovery_method': result.discovery_method,
                'confidence': result.confidence,
                'is_real': not is_fake
            })
            
        except Exception as e:
            print(f"  ❌ 探索失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 汇总验证结果
    print("\n" + "="*60)
    print("📊 验证结果汇总:")
    
    total_tests = len(all_results)
    real_locations = sum(1 for r in all_results if r['is_real'])
    fake_locations = total_tests - real_locations
    
    print(f"  • 总测试数: {total_tests}")
    print(f"  • 真实地点: {real_locations} ✅")
    print(f"  • 虚假地点: {fake_locations} ❌")
    print(f"  • 真实率: {(real_locations/total_tests*100):.1f}%")
    
    # 显示地点多样性
    unique_locations = len(set(r['location_name'] for r in all_results))
    unique_methods = set(r['discovery_method'] for r in all_results)
    
    print(f"\n🎯 多样性分析:")
    print(f"  • 地点多样性: {unique_locations}/{total_tests} ({unique_locations/total_tests*100:.1f}%)")
    print(f"  • 发现方法: {list(unique_methods)}")
    
    # 显示具体地点
    print(f"\n📍 发现的地点列表:")
    for r in all_results:
        status = "✅" if r['is_real'] else "❌"
        print(f"  {status} {r['location_name']} ({r['activity_type']}) - {r['discovery_method']}")
    
    print("\n" + "="*60)
    
    if fake_locations == 0:
        print("🎉 验证通过！所有地点都是真实存在的！")
        return True
    else:
        print(f"⚠️ 发现{fake_locations}个虚假地点，需要进一步修复")
        return False

async def main():
    """主验证函数"""
    print("🚀 开始验证数字生命自主探索器真实地点功能")
    print("目标：确保所有地点都是真实存在的物理地址")
    
    success = await validate_real_locations()
    
    if success:
        print("\n🌟 验证完成！数字生命自主探索器已完全脱离虚假数据！")
    else:
        print("\n🔧 需要继续修复虚假数据问题")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
