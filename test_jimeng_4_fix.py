#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试即梦4.0修复效果
"""

import requests
import json
import time
from datetime import datetime

def test_jimeng_4_fix():
    """测试修复后的即梦4.0"""
    
    print("🔥 老王测试即梦4.0修复效果")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 测试即梦4.0
    data = {
        "model": "jimeng-4.0",
        "prompt": "2077年的纽约时代广场，霓虹灯闪烁，赛博朋克风格",
        "negative_prompt": "低质量，模糊画面",
        "width": 1024,
        "height": 1024,
        "sample_strength": 0.8,
        "response_format": "url"
    }
    
    try:
        start_time = time.time()
        print(f"🎯 测试即梦4.0模型")
        print(f"📝 提示词: {data['prompt']}")
        print(f"📐 尺寸: {data['width']}x{data['height']}")
        print(f"⚠️ 注意：修复后的4.0可能需要更长时间...")
        print("-" * 40)
        
        response = requests.post(url, headers=headers, json=data, timeout=900)  # 15分钟超时
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ 总耗时: {elapsed_time:.1f}秒 ({elapsed_time/60:.1f}分钟)")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ HTTP状态: {response.status_code}")
            print(f"📊 响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            data_list = result.get("data", [])
            
            if data_list:
                print(f"🎉 成功！返回 {len(data_list)} 张图片")
                for i, item in enumerate(data_list):
                    if isinstance(item, dict) and "url" in item:
                        print(f"   图片 {i+1}: {item['url']}")
                        
                print("\n🎯 修复验证结果:")
                print("✅ 即梦4.0修复成功！")
                print("✅ 能够正常生成图片")
                print("✅ 返回完整的图片URL")
                return True
            else:
                print("❌ 仍然返回空数据")
                print(f"📊 完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                print("\n🔍 可能的原因:")
                print("1. 服务器端的jimeng-free-api还没有重启")
                print("2. 修复的代码还没有部署到服务器")
                print("3. 需要清除服务器端的缓存")
                print("4. 4.0模型仍然有其他问题")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"   错误内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ 超时（15分钟）")
        print("💡 这可能是正常的，4.0模型确实需要很长时间")
        return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def main():
    success = test_jimeng_4_fix()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    
    if success:
        print("✅ 即梦4.0修复成功！")
        print("💡 可以将drawing_skill中的默认模型改回jimeng-4.0")
    else:
        print("❌ 即梦4.0仍有问题")
        print("💡 可能需要:")
        print("   1. 重启服务器端的jimeng-free-api服务")
        print("   2. 部署修复后的代码到服务器")
        print("   3. 进一步调试和修复")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
