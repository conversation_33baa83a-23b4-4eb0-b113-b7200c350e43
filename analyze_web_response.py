#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王分析用户提供的web页面response
"""

import json

# 用户提供的web页面response
web_response = {
    "ret": "0",
    "errmsg": "success",
    "systime": "1757579498",
    "logid": "2025091116313876C4FD54BFE9F82786E3",
    "data": {
        "aigc_data": {
            "generate_type": 1,
            "history_record_id": "25504793783554",
            "origin_history_record_id": None,
            "created_time": 1757579498.78,
            "item_list": [],
            "origin_item_list": [],
            "task": {
                "task_id": "25504793783554",
                "submit_id": "61826a44-9001-47bd-aeba-627a7a28153d",
                "aid": 0,
                "status": 20,
                "finish_time": 0,
                "history_id": "25504793783554",
                "task_payload": None,
                "original_input": None,
                "req_first_frame_image": None,
                "ai_gen_prompt": "",
                "priority": 0,
                "lip_sync_info": None,
                "multi_size_first_frame_image": None,
                "multi_size_end_frame_image": None,
                "process_flows": None,
                "create_time": 0,
                "aigc_image_params": None,
                "ref_item": None,
                "resp_ret": {
                    "ret": ""
                }
            },
            "mode": "workbench",
            "asset_option": {
                "has_favorited": False
            },
            "uid": "317079856886999",
            "aigc_flow": {
                "version": "3.0.2"
            },
            "status": 20,
            "history_group_key_md5": "9dc0cd866acf79f927aef66bdd03fb4f",
            "history_group_key": "赛伯朋克2077",
            "draft_content": "{\"type\":\"draft\",\"id\":\"d28b5fdb-e003-3ef6-701e-723cd5448a77\",\"min_version\":\"3.0.2\",\"min_features\":[],\"is_from_tsn\":true,\"version\":\"3.0.2\",\"main_component_id\":\"aaf8b7d5-7407-c3b4-824d-d69be9fb08fb\",\"component_list\":[{\"type\":\"image_base_component\",\"id\":\"aaf8b7d5-7407-c3b4-824d-d69be9fb08fb\",\"min_version\":\"3.0.2\",\"aigc_mode\":\"workbench\",\"gen_type\":1,\"metadata\":{\"type\":\"\",\"id\":\"56527ae8-5305-7457-2f26-0c154f80c3d2\",\"created_platform\":3,\"created_platform_version\":\"\",\"created_time_in_ms\":\"1757579498117\",\"created_did\":\"\"},\"generate_type\":\"generate\",\"abilities\":{\"type\":\"\",\"id\":\"6f634d49-2e28-0991-f680-05a0abffc9a9\",\"generate\":{\"type\":\"\",\"id\":\"9e7611be-63e9-13b8-605f-2a0cd9e8863a\",\"core_param\":{\"type\":\"\",\"id\":\"34b0eff9-a403-8c1c-772e-7553bedccc23\",\"model\":\"high_aes_general_v40\",\"prompt\":\"赛伯朋克2077\",\"negative_prompt\":\"\",\"seed\":1986234001,\"sample_strength\":0.5,\"image_ratio\":8,\"large_image_info\":{\"type\":\"\",\"id\":\"c22879fc-ef3e-e0c7-8ffa-3e0f0fbcb39e\",\"height\":1296,\"width\":3024,\"resolution_type\":\"2k\"},\"intelligent_ratio\":false}}}}]}",
            "submit_id": "61826a44-9001-47bd-aeba-627a7a28153d",
            "capflow_id": "61826a44-9001-47bd-aeba-627a7a28153d",
            "metrics_extra": "{\"promptSource\":\"custom\",\"generateCount\":1,\"enterFrom\":\"click\",\"generateId\":\"61826a44-9001-47bd-aeba-627a7a28153d\",\"isRegenerate\":false}",
            "generate_id": "2025091116313876C4FD54BFE9F82786E3",
            "finish_time": 0,
            "model_info": {
                "icon_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3&x-expires=1789115496&x-signature=stowB0CA2IoNaBDlSnGjBmFCGSs%3D",
                "model_name_starling_key": "dreamina_image4_title",
                "model_tip_starling_key": "dreamina_image4_desc",
                "model_req_key": "high_aes_general_v40",
                "model_name": "图片 4.0",
                "video_model_options": None
            },
            "forecast_generate_cost": 31,
            "forecast_queue_cost": 501,
            "fail_starling_key": "",
            "fail_starling_message": "",
            "min_feats": None,
            "queue_info": {
                "queue_idx": 30409,
                "priority": 5,
                "queue_status": 1,
                "queue_length": 30409,
                "polling_config": {
                    "interval_seconds": 30,
                    "timeout_seconds": 86400
                }
            },
            "agent_history_id": None,
            "total_image_count": 0,
            "finished_image_count": 0,
            "confirm_status": 0,
            "confirm_token": "",
            "image_type": 0
        },
        "fail_code": "",
        "fail_starling_key": "",
        "fail_starling_message": ""
    }
}

def analyze_web_response():
    print("🔥 老王分析web页面response")
    print("=" * 60)
    
    # 解析draft_content
    draft_content_str = web_response["data"]["aigc_data"]["draft_content"]
    draft_content = json.loads(draft_content_str)
    
    print("📋 关键信息分析:")
    print(f"- 状态: {web_response['data']['aigc_data']['status']}")
    print(f"- 模型: {web_response['data']['aigc_data']['model_info']['model_req_key']}")
    print(f"- 版本: {draft_content['version']}")
    print(f"- 最小版本: {draft_content['min_version']}")
    
    # 分析component_list
    component = draft_content["component_list"][0]
    core_param = component["abilities"]["generate"]["core_param"]
    
    print("\n📊 核心参数分析:")
    print(f"- 模型: {core_param['model']}")
    print(f"- 提示词: {core_param['prompt']}")
    print(f"- 图像比例: {core_param['image_ratio']}")
    print(f"- 精细度: {core_param['sample_strength']}")
    
    # 分析large_image_info
    large_image_info = core_param["large_image_info"]
    print(f"- 图像尺寸: {large_image_info['width']}x{large_image_info['height']}")
    print(f"- 分辨率类型: {large_image_info.get('resolution_type', '未指定')}")
    
    # 检查与jimeng-free-api的差异
    print("\n🔍 与jimeng-free-api的差异分析:")
    
    # 检查是否有额外字段
    extra_fields = []
    if "metadata" in component:
        extra_fields.append("metadata")
    if "gen_type" in component:
        extra_fields.append("gen_type")
    if "resolution_type" in large_image_info:
        extra_fields.append("resolution_type")
    if "intelligent_ratio" in core_param:
        extra_fields.append("intelligent_ratio")
    
    if extra_fields:
        print(f"- 额外字段: {', '.join(extra_fields)}")
    else:
        print("- 没有发现额外字段")
    
    # 检查image_ratio差异
    web_image_ratio = core_param.get("image_ratio", 1)
    jimeng_api_ratio = 1  # jimeng-free-api中固定为1
    
    if web_image_ratio != jimeng_api_ratio:
        print(f"- ⚠️ image_ratio差异: web={web_image_ratio}, jimeng-api={jimeng_api_ratio}")
    
    # 检查版本差异
    print(f"- 版本信息: {draft_content['version']}")
    
    return draft_content

def compare_with_jimeng_api():
    print("\n🔧 jimeng-free-api修复建议:")
    
    draft_content = analyze_web_response()
    component = draft_content["component_list"][0]
    
    print("\n可能需要添加的字段:")
    if "metadata" in component:
        print("1. metadata字段")
        metadata = component["metadata"]
        for key, value in metadata.items():
            print(f"   - {key}: {value}")
    
    if "gen_type" in component:
        print(f"2. gen_type字段: {component['gen_type']}")
    
    core_param = component["abilities"]["generate"]["core_param"]
    if "intelligent_ratio" in core_param:
        print(f"3. intelligent_ratio字段: {core_param['intelligent_ratio']}")
    
    large_image_info = core_param["large_image_info"]
    if "resolution_type" in large_image_info:
        print(f"4. resolution_type字段: {large_image_info['resolution_type']}")

if __name__ == "__main__":
    compare_with_jimeng_api()
