# AIPyApp 集成指南 - 数字生命的"手和脚"

> 作者: 隔壁老王 (暴躁的代码架构师)  
> 版本: 1.0.0  
> 日期: 2025-09-12

## 🎯 项目概述

本指南详细介绍如何将 [aipyapp](https://github.com/knownsec/aipyapp) 项目集成到数字生命系统中，为AI提供强大的Python代码执行能力，实现真正的"手和脚"功能。

### 核心特性

- **Python代码执行**: 让AI能够执行真实的Python代码
- **深度调研能力**: 自动化数据收集、分析、报告生成
- **安全沙箱机制**: 多层安全防护，防止恶意代码执行
- **工作流自动化**: 预定义的调研工作流模板
- **远程服务部署**: 支持Linux服务器部署

## 🏗️ 架构设计

```mermaid
graph TD
    A[数字生命系统] --> B[技能管理器]
    B --> C[Python执行技能]
    C --> D[安全沙箱]
    C --> E[HTTP API客户端]
    E --> F[远程aipyapp服务]
    F --> G[Python执行环境]
    C --> H[深度调研工作流]
    H --> I[数据收集]
    H --> J[数据分析]
    H --> K[报告生成]
```

## 🚀 快速部署

### 1. 远程服务器部署

在Linux服务器上部署aipyapp服务：

```bash
# 1. 下载部署脚本
wget https://your-server/deploy/aipyapp_server_setup.sh

# 2. 赋予执行权限
chmod +x aipyapp_server_setup.sh

# 3. 执行部署（需要root权限）
sudo ./aipyapp_server_setup.sh
```

部署脚本会自动完成：
- 创建conda虚拟环境 `aipy`
- 安装aipyapp和相关依赖
- 配置systemd服务
- 设置防火墙规则
- 启动HTTP API服务

### 2. 配置API密钥

编辑配置文件 `/etc/aipyapp/aipyapp.toml`：

```toml
[llm.deepseek]
type = "deepseek"
api_key = "YOUR_DEEPSEEK_API_KEY"  # 替换为实际的API密钥

[server]
host = "0.0.0.0"
port = 8848
workers = 4
timeout = 300
```

### 3. 重启服务

```bash
sudo systemctl restart aipyapp-agent
sudo systemctl status aipyapp-agent
```

## 🔧 数字生命系统集成

### 1. 技能配置

更新 `config/skills/skill_manager.json`：

```json
{
  "intent_to_skill_map": {
    "python": "python_execution_skill",
    "代码": "python_execution_skill",
    "分析数据": "python_execution_skill",
    "调研": "python_execution_skill"
  },
  "skill_priorities": {
    "python_execution_skill": 9
  },
  "skill_configs": {
    "python_execution_skill": {
      "enabled": true,
      "timeout": 300,
      "max_retries": 2
    }
  }
}
```

### 2. API地址配置

更新 `config/skills/python_execution_skill.json`：

```json
{
  "api_config": {
    "api_base_url": "http://your-server:8848",
    "timeout": 300,
    "max_retries": 3
  }
}
```

### 3. 重启数字生命系统

```bash
# 重启数字生命服务
sudo systemctl restart yanran-digital-life
```

## 📋 使用示例

### 基本用法

用户可以通过自然语言触发Python执行：

```
用户: "帮我分析一下苹果公司最近30天的股价走势，生成图表"
AI: 正在执行Python代码分析苹果股价...
    [执行数据获取、分析、可视化代码]
    已生成股价走势图表，分析显示...
```

### 深度调研工作流

```python
from cognitive_modules.skills.workflows.deep_research_workflow import create_research_workflow

# 创建工作流
workflow = create_research_workflow()

# 执行综合分析
result = workflow.execute_workflow("comprehensive_analysis", {
    "topic": "电动汽车市场趋势",
    "user_id": "researcher"
})

print(f"工作流结果: {result.summary}")
```

### 支持的调研类型

1. **综合分析** (`comprehensive_analysis`)
   - 数据收集 → 清洗 → 分析 → 可视化 → 报告

2. **市场调研** (`market_research`)
   - 市场数据 → 竞争分析 → 机会识别

3. **金融分析** (`financial_analysis`)
   - 数据获取 → 技术分析 → 基本面分析 → 投资建议

## 🔒 安全机制

### 多层安全防护

1. **代码静态分析**: 检测危险操作和恶意代码
2. **模块白名单**: 只允许安全的Python模块
3. **文件系统保护**: 禁止访问敏感目录
4. **资源限制**: CPU、内存、执行时间限制
5. **网络访问控制**: 域名白名单机制

### 安全配置示例

```json
{
  "security": {
    "allowed_modules": [
      "pandas", "numpy", "matplotlib", "requests"
    ],
    "blocked_operations": [
      "os.system", "subprocess.run", "exec", "eval"
    ],
    "max_execution_time": 600,
    "max_memory_mb": 1024
  }
}
```

## 📊 监控和维护

### 服务状态检查

```bash
# 检查服务状态
sudo systemctl status aipyapp-agent

# 查看服务日志
sudo journalctl -u aipyapp-agent -f

# 测试API连接
curl http://localhost:8848/health
```

### 性能监控

```bash
# 查看资源使用
htop
df -h
free -h

# 查看端口占用
netstat -tlnp | grep 8848
```

### 日志管理

日志文件位置：
- 系统日志: `/var/log/aipyapp/`
- 应用日志: `journalctl -u aipyapp-agent`
- 技能日志: `logs/python_execution_skill.log`

## 🛠️ 故障排除

### 常见问题

1. **API连接失败**
   ```bash
   # 检查服务是否运行
   sudo systemctl status aipyapp-agent
   
   # 检查端口是否开放
   sudo ufw status
   ```

2. **执行超时**
   - 增加timeout配置
   - 检查服务器资源使用情况
   - 优化代码执行逻辑

3. **安全检查失败**
   - 检查代码是否包含禁止操作
   - 调整安全配置白名单
   - 查看详细错误信息

### 调试模式

启用调试模式：

```bash
# 修改配置文件
sudo vim /etc/aipyapp/aipyapp.toml

[logging]
level = "DEBUG"

# 重启服务
sudo systemctl restart aipyapp-agent
```

## 🔄 升级和维护

### 升级aipyapp

```bash
# 激活环境
conda activate aipy

# 升级aipyapp
pip install --upgrade aipyapp

# 重启服务
sudo systemctl restart aipyapp-agent
```

### 备份配置

```bash
# 备份配置文件
sudo cp -r /etc/aipyapp /etc/aipyapp.backup.$(date +%Y%m%d)

# 备份日志
sudo tar -czf /tmp/aipyapp_logs_$(date +%Y%m%d).tar.gz /var/log/aipyapp/
```

## 📈 性能优化

### 服务器配置建议

- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: SSD，50GB以上可用空间
- **网络**: 稳定的网络连接

### 优化配置

```toml
[server]
workers = 4  # 根据CPU核心数调整
timeout = 300
max_requests = 1000

[security]
max_execution_time = 600
max_memory_mb = 2048
```

## 🎉 总结

通过集成aipyapp，数字生命系统获得了强大的Python代码执行能力，真正实现了AI的"手和脚"功能。这个集成方案具有以下优势：

1. **功能强大**: 支持数据分析、网络爬取、可视化等
2. **安全可靠**: 多层安全防护机制
3. **易于部署**: 自动化部署脚本
4. **高度集成**: 与现有技能系统无缝集成
5. **可扩展性**: 支持自定义工作流

艹！这就是老王想要的真正的AI执行能力！现在数字生命不仅能思考，还能动手干活了！

---

> 💡 **提示**: 如有问题，请查看日志文件或联系技术支持。记住，代码不会撒谎，日志会告诉你一切！
