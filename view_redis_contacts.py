#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查看Redis联系人缓存数据
"""

import os
import sys
import json
import redis

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from utilities.unified_logger import get_unified_logger

logger = get_unified_logger(__name__)

def view_redis_contacts():
    """查看Redis中的联系人缓存数据"""
    try:
        # 连接Redis
        redis_client = redis.Redis(
            host='localhost',
            port=6379,
            db=0,
            decode_responses=True
        )
        
        # 测试连接
        redis_client.ping()
        logger.success("✅ 连接Redis成功")
        
        print("\n" + "="*60)
        print("🔍 Redis联系人缓存数据查看")
        print("="*60)
        
        # 1. 查看所有联系人相关的key
        print("\n📋 所有联系人相关的Redis Key:")
        contact_keys = redis_client.keys("contacts:*")
        user_keys = redis_client.keys("user:*")
        yanran_keys = redis_client.keys("yanran:*")
        
        all_keys = contact_keys + user_keys + yanran_keys
        
        if not all_keys:
            print("❌ 未找到任何联系人缓存数据")
            return
        
        for i, key in enumerate(all_keys, 1):
            print(f"  {i}. {key}")
        
        # 2. 显示每个key的数据
        print("\n📊 详细数据内容:")
        for key in all_keys:
            try:
                data_type = redis_client.type(key)
                ttl = redis_client.ttl(key)
                
                print(f"\n🔑 Key: {key}")
                print(f"   类型: {data_type}")
                print(f"   TTL: {ttl}秒 ({'永不过期' if ttl == -1 else '已过期' if ttl == -2 else f'{ttl//3600}小时{(ttl%3600)//60}分钟'})")
                
                if data_type == 'string':
                    value = redis_client.get(key)
                    try:
                        # 尝试解析JSON
                        parsed_value = json.loads(value)
                        print(f"   内容: {json.dumps(parsed_value, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"   内容: {value}")
                        
                elif data_type == 'hash':
                    hash_data = redis_client.hgetall(key)
                    print(f"   内容: {json.dumps(hash_data, ensure_ascii=False, indent=6)}")
                    
                elif data_type == 'list':
                    list_data = redis_client.lrange(key, 0, -1)
                    print(f"   内容: {list_data}")
                    
                elif data_type == 'set':
                    set_data = redis_client.smembers(key)
                    print(f"   内容: {list(set_data)}")
                    
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        
        # 3. 统计信息
        print(f"\n📈 统计信息:")
        print(f"   总Key数量: {len(all_keys)}")
        print(f"   contacts:前缀: {len(contact_keys)}")
        print(f"   user:前缀: {len(user_keys)}")
        print(f"   yanran:前缀: {len(yanran_keys)}")
        
        return True
        
    except redis.ConnectionError:
        logger.error("❌ 无法连接到Redis服务器，请确保Redis正在运行")
        print("\n💡 解决方案:")
        print("   1. 启动Redis服务: brew services start redis (macOS)")
        print("   2. 或者: sudo systemctl start redis (Linux)")
        print("   3. 检查Redis配置: redis-cli ping")
        return False
        
    except Exception as e:
        logger.error(f"❌ 查看Redis数据失败: {e}")
        return False

def clear_redis_contacts():
    """清理Redis联系人缓存"""
    try:
        redis_client = redis.Redis(
            host='localhost',
            port=6379,
            db=0,
            decode_responses=True
        )
        
        # 获取所有联系人相关的key
        contact_keys = redis_client.keys("contacts:*")
        user_keys = redis_client.keys("user:*")
        all_keys = contact_keys + user_keys
        
        if not all_keys:
            print("❌ 没有找到需要清理的联系人缓存")
            return
        
        print(f"🗑️  准备清理 {len(all_keys)} 个联系人缓存key...")
        
        # 删除所有key
        deleted_count = redis_client.delete(*all_keys)
        print(f"✅ 成功清理 {deleted_count} 个联系人缓存key")
        
    except Exception as e:
        logger.error(f"❌ 清理Redis缓存失败: {e}")

def main():
    """主函数"""
    print("🔧 Redis联系人缓存管理工具")
    print("1. 查看缓存数据")
    print("2. 清理缓存数据")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == '1':
            view_redis_contacts()
        elif choice == '2':
            confirm = input("确认清理所有联系人缓存？(y/N): ").strip().lower()
            if confirm == 'y':
                clear_redis_contacts()
        elif choice == '3':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入 1-3")

if __name__ == "__main__":
    main() 