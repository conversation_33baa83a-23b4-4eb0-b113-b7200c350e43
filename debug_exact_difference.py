#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王精确对比API调用差异
"""

import requests
import json
import time

def test_exact_same_call():
    """使用与drawing skill完全相同的参数调用API"""
    print("🔥 老王精确对比API调用差异")
    print("=" * 60)
    
    # 完全模拟drawing skill的调用
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 使用drawing skill中_try_jimeng_service的确切参数
    data = {
        "model": "jimeng-4.0",
        "prompt": "流浪地球画面",  # 简单提示词，不经过优化
        "negative_prompt": "低质量，模糊画面，混乱布局，扭曲的场景，畸形的肢体，不协调的颜色，不真实",
        "width": 1080,  # drawing skill使用的尺寸
        "height": 1920,
        "sample_strength": 0.8,  # drawing skill使用的精细度
        "response_format": "url"
    }
    
    print("📋 请求参数:")
    print(f"   URL: {url}")
    print(f"   Headers: {headers}")
    print(f"   Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
    print("-" * 40)
    
    try:
        start_time = time.time()
        
        # 使用与drawing skill完全相同的超时时间
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ 耗时: {elapsed_time:.1f}秒")
        print(f"📊 HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 检查响应格式
            print(f"\n🔍 响应分析:")
            print(f"   类型: {type(result)}")
            print(f"   是否为字典: {isinstance(result, dict)}")
            
            if isinstance(result, dict):
                print(f"   包含字段: {list(result.keys())}")
                
                if "data" in result:
                    data_list = result["data"]
                    print(f"   data类型: {type(data_list)}")
                    print(f"   data是否为列表: {isinstance(data_list, list)}")
                    print(f"   data长度: {len(data_list) if isinstance(data_list, list) else 'N/A'}")
                    
                    if isinstance(data_list, list) and data_list:
                        print(f"   第一个元素: {data_list[0]}")
                        print(f"✅ 成功！返回 {len(data_list)} 张图片")
                    else:
                        print(f"❌ data为空或格式错误")
                else:
                    print(f"❌ 缺少data字段")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"   错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    test_exact_same_call()
