#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试简单提示词是否被过滤
"""

import requests
import json

def test_simple_prompts():
    """测试简单提示词"""
    print("🔥 老王测试简单提示词是否被过滤")
    print("=" * 60)
    
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    # 测试不同复杂度的提示词
    test_prompts = [
        "小猫",
        "一只小猫",
        "风景画",
        "明日之后",
        "美女",
        "CG图片"
    ]
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n🎯 测试 {i}/{len(test_prompts)}: '{prompt}'")
        
        data = {
            "model": "jimeng-4.0",
            "prompt": prompt,
            "negative_prompt": "低质量",
            "width": 1024,
            "height": 1024,
            "sample_strength": 0.5,
            "response_format": "url"
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                data_list = result.get("data", [])
                
                if data_list:
                    print(f"✅ 成功！返回 {len(data_list)} 张图片")
                    break  # 找到一个成功的就停止
                else:
                    print(f"❌ 返回空数据")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")

if __name__ == "__main__":
    test_simple_prompts()
