#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王简单测试jimeng_generate_images方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill

def test_simple_jimeng():
    """简单测试jimeng_generate_images方法"""
    print("🔥 老王简单测试jimeng_generate_images方法")
    print("=" * 60)
    
    try:
        # 初始化drawing skill
        drawing_skill = DrawingSkill()
        
        print(f"📋 session_id: {drawing_skill.jimeng_session_id}")
        print("-" * 40)
        
        # 使用与成功测试完全相同的参数
        result = drawing_skill.jimeng_generate_images(
            prompt="流浪地球画面",
            model="jimeng-4.0",
            negative_prompt="低质量，模糊画面，混乱布局，扭曲的场景，畸形的肢体，不协调的颜色，不真实",
            width=1080,
            height=1920,
            sample_strength=0.8,
            session_id=drawing_skill.jimeng_session_id
        )
        
        print(f"📋 返回结果: {result}")
        
        if isinstance(result, dict):
            if "image_urls" in result:
                print(f"✅ 成功！返回 {len(result['image_urls'])} 张图片")
            elif "error" in result:
                print(f"❌ 失败: {result['error']}")
            else:
                print(f"⚠️ 未知格式")
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_simple_jimeng()
