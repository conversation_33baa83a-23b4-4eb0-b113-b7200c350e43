#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
北疆6日游攻略分析测试
测试Python执行技能的实际应用场景

作者: 隔壁老王 (暴躁的代码架构师)
测试场景: 10.1 北疆6日游攻略分析
"""

import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from cognitive_modules.skills.python_execution_skill import PythonExecutionSkill
from utilities.logger import get_logger
logger = get_logger(__name__)


def test_xinjiang_travel_analysis():
    """测试北疆旅游攻略分析"""
    print("🏔️ === 北疆6日游攻略分析测试 ===")
    print("作者: 隔壁老王 (暴躁的代码架构师)")
    print("=" * 60)
    
    # 创建Python执行技能实例
    skill = PythonExecutionSkill()
    
    # 测试任务：北疆6日游攻略分析
    travel_analysis_task = """
    请帮我分析和制定一个详细的"10.1 北疆6日游攻略"，包括以下内容：

    1. 数据收集和分析：
       - 收集北疆主要景点信息（喀纳斯、禾木、白哈巴、五彩滩等）
       - 分析10月份北疆的天气情况和最佳游览时间
       - 收集交通路线和住宿信息

    2. 行程规划：
       - 设计6天的详细行程安排
       - 计算各景点间的距离和行车时间
       - 推荐最佳的游览顺序

    3. 费用预算：
       - 估算交通、住宿、门票、餐饮等各项费用
       - 制作详细的费用预算表
       - 提供不同预算档次的选择

    4. 实用信息：
       - 整理必备物品清单
       - 提供当地特色美食推荐
       - 注意事项和旅游贴士

    5. 数据可视化：
       - 创建行程路线图
       - 生成费用分布饼图
       - 制作天气趋势图表

    请使用Python代码来收集数据、进行分析，并生成一份完整的旅游攻略报告。
    """
    
    print("🚀 开始执行北疆旅游攻略分析任务...")
    print(f"📋 任务描述: {travel_analysis_task[:100]}...")
    
    start_time = time.time()
    
    try:
        # 执行分析任务
        result = skill.execute(
            input_text=travel_analysis_task,
            user_id="travel_analyst",
            session_id="xinjiang_travel_test",
            intent_data={
                "type": "travel_analysis",
                "location": "北疆",
                "duration": "6日",
                "season": "10月"
            }
        )
        
        execution_time = time.time() - start_time
        
        print(f"\n⏰ 执行时间: {execution_time:.2f}秒")
        print("=" * 60)
        
        if result.get("success"):
            print("✅ 任务执行成功！")
            
            # 显示执行结果
            data = result.get("data", {})
            print(f"\n📊 执行结果:")
            print(f"执行ID: {data.get('execution_id', 'N/A')}")
            print(f"任务描述: {data.get('task_description', 'N/A')[:100]}...")
            print(f"执行时间: {data.get('execution_time', 0):.2f}秒")
            
            # 显示输出内容
            output = data.get("output", {})
            if output:
                print(f"\n📝 分析输出:")
                if isinstance(output, dict):
                    for key, value in output.items():
                        print(f"  {key}: {str(value)[:200]}...")
                else:
                    print(f"  {str(output)[:500]}...")
            
            # 显示生成的文件
            files_generated = data.get("files_generated", [])
            if files_generated:
                print(f"\n📁 生成的文件:")
                for file_path in files_generated:
                    print(f"  - {file_path}")
            
            # 显示摘要
            summary = data.get("summary", "")
            if summary:
                print(f"\n📋 执行摘要:")
                print(f"  {summary}")
            
            print(f"\n🎉 北疆旅游攻略分析完成！")
            
        else:
            print("❌ 任务执行失败！")
            error = result.get("error", "未知错误")
            print(f"错误信息: {error}")
            
            # 显示详细的错误信息
            print(f"\n🔍 详细结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return result
        
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"\n💥 测试执行异常: {e}")
        print(f"⏰ 执行时间: {execution_time:.2f}秒")
        return {"success": False, "error": str(e)}


def test_simple_task():
    """测试简单任务"""
    print("\n🧪 === 简单任务测试 ===")
    
    skill = PythonExecutionSkill()
    
    simple_task = """
    请帮我创建一个简单的北疆旅游数据分析：
    1. 创建一个包含北疆主要景点的数据表
    2. 包含景点名称、最佳游览时间、门票价格等信息
    3. 生成一个简单的数据统计图表
    """
    
    print("🚀 执行简单任务...")
    
    result = skill.execute(
        input_text=simple_task,
        user_id="test_user",
        session_id="simple_test"
    )
    
    if result.get("success"):
        print("✅ 简单任务执行成功！")
        data = result.get("data", {})
        print(f"执行时间: {data.get('execution_time', 0):.2f}秒")
    else:
        print("❌ 简单任务执行失败！")
        print(f"错误: {result.get('error')}")
    
    return result


def test_api_connection():
    """测试API连接"""
    print("\n🔗 === API连接测试 ===")
    
    import requests
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8848/health", timeout=10)
        if response.status_code == 200:
            print("✅ API服务连接正常")
            health_data = response.json()
            print(f"服务状态: {health_data.get('status')}")
            print(f"时间戳: {health_data.get('timestamp')}")
        else:
            print(f"❌ API服务响应异常: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确认aipyapp服务是否启动")
        print("启动命令: aipy --agent --host 0.0.0.0 --port 8848")
        return False
    except Exception as e:
        print(f"❌ API连接测试异常: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("🏔️ 北疆6日游攻略分析测试程序")
    print("作者: 隔壁老王 (暴躁的代码架构师)")
    print("=" * 60)
    
    # 1. 测试API连接
    if not test_api_connection():
        print("\n❌ API连接失败，请先启动aipyapp服务")
        print("启动命令: aipy --agent --host 0.0.0.0 --port 8848")
        return 1
    
    # 2. 测试简单任务
    simple_result = test_simple_task()
    
    # 3. 如果简单任务成功，再测试复杂任务
    if simple_result.get("success"):
        print("\n✅ 简单任务测试通过，开始复杂任务测试...")
        complex_result = test_xinjiang_travel_analysis()
    else:
        print("\n⚠️ 简单任务测试失败，跳过复杂任务测试")
        print("请检查aipyapp服务配置和API连接")
        return 1
    
    print("\n🎉 所有测试完成！")
    print("\n💡 测试总结:")
    print("1. API连接测试: ✅ 通过")
    print(f"2. 简单任务测试: {'✅ 通过' if simple_result.get('success') else '❌ 失败'}")
    
    if 'complex_result' in locals():
        print(f"3. 复杂任务测试: {'✅ 通过' if complex_result.get('success') else '❌ 失败'}")
    
    print("\n🔥 老王点评:")
    print("如果所有测试都通过，说明Python执行技能集成成功！")
    print("现在数字生命真正拥有了'手和脚'的能力！")
    print("可以进行复杂的数据分析和调研任务了！")
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
