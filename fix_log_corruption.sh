#!/bin/bash

# 修复日志文件损坏问题
# 老王专用：解决@^@^@^@填充问题
# 创建时间: 2025-08-26
# 作者: 老王 (暴躁但技术过硬)

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_DIR="$PROJECT_DIR/logs"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查日志文件是否损坏
check_log_corruption() {
    local file="$1"
    local filename=$(basename "$file")
    
    if [[ ! -f "$file" ]]; then
        return 1
    fi
    
    # 检查文件大小
    local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
    
    # 检查是否包含大量null字符
    local null_count=$(tr -cd '\000' < "$file" | wc -c | tr -d ' ')
    local null_percentage=$((null_count * 100 / (size + 1)))
    
    log_info "文件: $filename"
    log_info "  大小: ${size} 字节"
    log_info "  NULL字符数: ${null_count}"
    log_info "  NULL字符占比: ${null_percentage}%"
    
    if [[ $null_percentage -gt 10 ]]; then
        log_warn "  状态: 损坏 (NULL字符占比过高)"
        return 0
    else
        log_success "  状态: 正常"
        return 1
    fi
}

# 修复单个日志文件
fix_single_log() {
    local file="$1"
    local filename=$(basename "$file")
    local backup_file="${file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    log_info "修复日志文件: $filename"
    
    # 创建备份
    cp "$file" "$backup_file"
    log_info "  备份已创建: $(basename "$backup_file")"
    
    # 方法1: 使用strings命令（推荐）
    if command -v strings >/dev/null 2>&1; then
        log_info "  使用strings命令清理..."
        strings "$file" > "${file}.clean"
    else
        # 方法2: 使用tr命令
        log_info "  使用tr命令清理..."
        tr -d '\000-\010\013\014\016-\037' < "$file" > "${file}.clean"
    fi
    
    # 检查清理结果
    local original_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
    local cleaned_size=$(stat -f%z "${file}.clean" 2>/dev/null || stat -c%s "${file}.clean" 2>/dev/null || echo "0")
    
    if [[ $cleaned_size -gt 0 ]]; then
        mv "${file}.clean" "$file"
        log_success "  修复完成: ${original_size} → ${cleaned_size} 字节"
        
        # 如果清理后文件明显变小，说明修复有效
        if [[ $cleaned_size -lt $((original_size / 2)) ]]; then
            log_success "  文件大小显著减少，修复效果明显"
        fi
    else
        log_error "  修复失败，恢复备份"
        rm -f "${file}.clean"
        cp "$backup_file" "$file"
        return 1
    fi
}

# 扫描并修复所有日志文件
scan_and_fix_logs() {
    log_info "扫描日志目录: $LOG_DIR"
    
    if [[ ! -d "$LOG_DIR" ]]; then
        log_error "日志目录不存在: $LOG_DIR"
        return 1
    fi
    
    local total_files=0
    local corrupted_files=0
    local fixed_files=0
    
    # 查找所有日志文件
    while IFS= read -r -d '' file; do
        ((total_files++))
        
        if check_log_corruption "$file"; then
            ((corrupted_files++))
            
            if fix_single_log "$file"; then
                ((fixed_files++))
            fi
        fi
        
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
    done < <(find "$LOG_DIR" -name "*.log" -o -name "*.out" -o -name "*.err" -print0)
    
    # 输出统计结果
    echo
    log_info "扫描完成统计:"
    log_info "  总文件数: $total_files"
    log_warn "  损坏文件数: $corrupted_files"
    log_success "  修复文件数: $fixed_files"
    
    if [[ $fixed_files -gt 0 ]]; then
        log_success "日志修复完成！"
    elif [[ $corrupted_files -eq 0 ]]; then
        log_success "所有日志文件状态正常！"
    else
        log_error "部分文件修复失败，请检查权限或手动处理"
    fi
}

# 清理旧备份文件
clean_backups() {
    log_info "清理7天前的备份文件..."
    
    local cleaned=0
    while IFS= read -r -d '' backup_file; do
        rm -f "$backup_file"
        ((cleaned++))
    done < <(find "$LOG_DIR" -name "*.backup.*" -mtime +7 -print0 2>/dev/null || true)
    
    if [[ $cleaned -gt 0 ]]; then
        log_success "已清理 $cleaned 个旧备份文件"
    else
        log_info "没有需要清理的旧备份文件"
    fi
}

# 主程序
main() {
    echo -e "${CYAN}🔧 日志文件损坏修复工具 🔧${NC}"
    echo -e "${CYAN}老王出品，专治各种日志@^@^@问题${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    
    case "${1:-scan}" in
        scan)
            scan_and_fix_logs
            ;;
        clean-backups)
            clean_backups
            ;;
        fix)
            if [[ -z "${2:-}" ]]; then
                log_error "请指定要修复的文件路径"
                exit 1
            fi
            if check_log_corruption "$2"; then
                fix_single_log "$2"
            else
                log_info "文件状态正常，无需修复"
            fi
            ;;
        *)
            echo "用法: $0 {scan|clean-backups|fix <文件路径>}"
            echo
            echo "  scan          - 扫描并修复所有损坏的日志文件"
            echo "  clean-backups - 清理7天前的备份文件"
            echo "  fix <file>    - 修复指定的日志文件"
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"
