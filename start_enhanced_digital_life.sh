#!/bin/bash
# 数字生命生产环境启动脚本
# CEO要求：确保所有修复在生产环境中生效

echo "🚀 启动数字生命生产环境..."
echo "集成了100%修复成功的所有组件"
echo "=================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    exit 1
fi

# 🔥 P0级别修复：使用正确的main.py文件
if [ ! -f "main.py" ]; then
    echo "❌ main.py不存在"
    exit 1
fi

# 🔥 P0级别修复：启动完整的数字生命系统（非API模式）
# 这样可以支持物理世界实时感知和自主决策表达
echo "🌟 启动完整版数字生命系统..."
echo "📡 支持物理世界实时感知"
echo "🧠 支持自主决策表达"
echo "💬 使用WeChat统一出口"
python3 main.py --log-level INFO

echo "👋 数字生命已停止"
