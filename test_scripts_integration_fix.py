#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王的Scripts集成服务修复测试脚本
测试超时配置修复是否有效
"""

import asyncio
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utilities.unified_logger import get_unified_logger

logger = get_unified_logger("test_scripts_integration_fix")

async def test_scripts_integration_service():
    """测试Scripts集成服务的修复效果"""
    try:
        logger.info("🔥 开始测试Scripts集成服务修复效果...")
        
        # 导入服务
        from services.scripts_integration_service import get_scripts_integration_service
        
        # 获取服务实例
        service = get_scripts_integration_service()
        
        # 初始化服务
        logger.info("📊 初始化Scripts集成服务...")
        init_success = await service.initialize()
        if not init_success:
            logger.error("❌ Scripts集成服务初始化失败")
            return False
        
        logger.success("✅ Scripts集成服务初始化成功")
        
        # 测试活动生成
        logger.info("📊 测试活动生成...")
        start_time = time.time()
        
        result = await service.generate_and_save_activity(
            user_id="linyanran",
            activity_type="daily_life",
            time_slot="morning",
            context={'test': True}
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"📊 活动生成完成，耗时: {execution_time:.2f}秒")
        
        if result and result.get('success'):
            logger.success(f"✅ 活动生成成功: {result.get('activity', '')[:50]}...")
            logger.info(f"📊 真实性评分: {result.get('reality_score', 0):.2f}")
            logger.info(f"📊 是否跳过: {result.get('skipped', False)}")
            return True
        else:
            logger.error(f"❌ 活动生成失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        import traceback
        logger.debug(f"异常详情: {traceback.format_exc()}")
        return False

async def test_ai_adapter_timeout():
    """测试AI适配器超时修复"""
    try:
        logger.info("🔥 测试AI适配器超时修复...")
        
        from adapters.ai_service_adapter import get_ai_service_adapter
        
        ai_adapter = get_ai_service_adapter()
        
        # 测试异步调用
        start_time = time.time()
        
        try:
            response = await ai_adapter.get_completion_async(
                messages=[{"role": "user", "content": "简单回答：今天天气怎么样？"}],
                model="MiniMax-M1",
                temperature=0.7,
                max_tokens=100
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.info(f"📊 AI调用完成，耗时: {execution_time:.2f}秒")
            
            if response and isinstance(response, dict):
                success = response.get('success', False)
                if success:
                    content = response.get('content', response.get('response', ''))
                    logger.success(f"✅ AI调用成功: {content[:50]}...")
                    return True
                else:
                    error_info = response.get('error', '未知错误')
                    logger.warning(f"⚠️ AI调用失败: {error_info}")
                    return False
            else:
                logger.warning(f"⚠️ AI响应格式异常: {type(response)}")
                return False
                
        except asyncio.TimeoutError:
            logger.error("❌ AI调用超时")
            return False
            
    except Exception as e:
        logger.error(f"❌ AI适配器测试异常: {e}")
        import traceback
        logger.debug(f"异常详情: {traceback.format_exc()}")
        return False

async def test_universal_scheduler_timeout():
    """测试通用调度器超时修复"""
    try:
        logger.info("🔥 测试通用调度器超时修复...")
        
        from core.universal_scheduler import ScriptsIntegrationTask
        from services.scripts_integration_service import get_scripts_integration_service
        from connectors.database.mysql import get_mysql_connector
        
        # 获取依赖
        mysql_connector = get_mysql_connector()
        scripts_service = get_scripts_integration_service()
        
        # 创建任务
        task = ScriptsIntegrationTask(mysql_connector, scripts_service)
        
        # 执行任务
        logger.info("📊 执行调度器任务...")
        start_time = time.time()
        
        result = await task.execute()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"📊 调度器任务完成，耗时: {execution_time:.2f}秒")
        
        if result and result.success:
            logger.success(f"✅ 调度器任务执行成功")
            logger.info(f"📊 任务状态: {result.status}")
            logger.info(f"📊 任务持续时间: {result.duration:.2f}秒")
            return True
        else:
            logger.error(f"❌ 调度器任务执行失败: {result.error_message if result else '未知错误'}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 调度器测试异常: {e}")
        import traceback
        logger.debug(f"异常详情: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("🔥 老王的Scripts集成服务修复测试开始...")
    
    test_results = []
    
    # 测试1：Scripts集成服务
    logger.info("=" * 50)
    logger.info("测试1：Scripts集成服务")
    result1 = await test_scripts_integration_service()
    test_results.append(("Scripts集成服务", result1))
    
    # 测试2：AI适配器超时
    logger.info("=" * 50)
    logger.info("测试2：AI适配器超时")
    result2 = await test_ai_adapter_timeout()
    test_results.append(("AI适配器超时", result2))
    
    # 测试3：通用调度器超时
    logger.info("=" * 50)
    logger.info("测试3：通用调度器超时")
    result3 = await test_universal_scheduler_timeout()
    test_results.append(("通用调度器超时", result3))
    
    # 汇总结果
    logger.info("=" * 50)
    logger.info("🔥 测试结果汇总:")
    
    success_count = 0
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    total_tests = len(test_results)
    logger.info(f"📊 总体结果: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有测试通过！修复成功！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试脚本异常: {e}")
        sys.exit(1)
