#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linux服务器启动脚本
香草为Linux服务器创建的专用启动脚本 💕
"""

import sys
import os

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 导入辅助函数
sys.path.insert(0, PROJECT_ROOT)
from linux_import_helper import setup_python_path, clear_module_cache

def main():
    """主启动函数"""
    print("🎵 香草启动Linux服务器...")
    
    # 设置Python路径
    setup_python_path()
    
    # 清理模块缓存
    clear_module_cache()
    
    try:
        # 导入并启动main
        from main import DigitalLifeSystem
        
        # 创建系统实例
        system = DigitalLifeSystem()
        
        # 启动系统
        import asyncio
        asyncio.run(system.run())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
