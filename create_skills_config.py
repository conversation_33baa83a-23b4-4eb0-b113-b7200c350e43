#!/usr/bin/env python3
import json
import os

config_dir = "config"
skills_dir = os.path.join(config_dir, "skills")
output_file = os.path.join(config_dir, "skills_unified.json")

print(f"创建统一技能配置到 {output_file}")

# 创建统一配置字典
unified_config = {}

try:
    # 读取技能管理器配置
    with open(os.path.join(skills_dir, "skill_manager.json"), 'r', encoding='utf-8') as f:
        unified_config["skill_manager"] = json.load(f)
        print("✅ 已加载技能管理器配置")

    # 读取聊天技能配置
    with open(os.path.join(skills_dir, "chat_skill.json"), 'r', encoding='utf-8') as f:
        unified_config["chat_skill"] = json.load(f)
        print("✅ 已加载聊天技能配置")

    # 读取绘画技能配置
    with open(os.path.join(skills_dir, "drawing_skill.json"), 'r', encoding='utf-8') as f:
        unified_config["drawing_skill"] = json.load(f)
        print("✅ 已加载绘画技能配置")

    # 读取搜索技能配置
    with open(os.path.join(skills_dir, "search_skill.json"), 'r', encoding='utf-8') as f:
        unified_config["search_skill"] = json.load(f)
        print("✅ 已加载搜索技能配置")

    # 尝试加载音乐技能配置
    try:
        with open(os.path.join(skills_dir, "music_skill.json"), 'r', encoding='utf-8') as f:
            unified_config["music_skill"] = json.load(f)
            print("✅ 已加载音乐技能配置")
    except FileNotFoundError:
        print("❓ 未找到音乐技能配置，使用默认值")
        unified_config["music_skill"] = {
            "enabled": True,
            "api_key": "",
            "max_memory_size": 2,
            "default_settings": {
                "format": "mp3",
                "quality": "high"
            }
        }

    # 添加全局设置
    unified_config["global_skill_settings"] = {
        "cache_enabled": True,
        "cache_ttl": 3600,
        "default_confidence_threshold": 0.6,
        "log_level": "INFO",
        "max_retry_attempts": 3,
        "timeout": 30
    }

    # 加载和整合API配置
    api_configs = {}
    
    # OpenAI API配置 (可能被多个技能使用)
    try:
        with open(os.path.join(config_dir, "openai_config.json"), 'r', encoding='utf-8') as f:
            api_configs["openai"] = json.load(f)
            print("✅ 已加载OpenAI API配置")
    except FileNotFoundError:
        print("❓ 未找到OpenAI API配置")
        
    # 尝试加载AI服务配置
    try:
        with open(os.path.join(config_dir, "ai_services.json"), 'r', encoding='utf-8') as f:
            api_configs["ai_services"] = json.load(f)
            print("✅ 已加载AI服务配置")
    except FileNotFoundError:
        print("❓ 未找到AI服务配置")

    # 将API配置整合到统一配置中
    unified_config["api_configs"] = api_configs

    # 写入统一配置文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(unified_config, f, ensure_ascii=False, indent=2)
        print(f"✅ 已创建统一配置文件: {output_file}")
        
    print("🎉 配置整合完成！")
    
except Exception as e:
    print(f"❌ 错误: {str(e)}")
