#!/usr/bin/env python3
"""
自主决策情感系统 - Autonomous Emotion System

结合数字生命的涌现能力和自主思考能力，让情感更新过程变得更加智能和自主。
不再依赖简单的规则匹配，而是通过意识决策来判断情感变化。

作者: AI助手 & 用户协作
创建日期: 2025-01-19
版本: 1.0
"""

import os
import sys
import json
import time
import random
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

from utilities.unified_logger import get_unified_logger, setup_unified_logging
from cognitive_modules.autonomy.conscious_decision import get_instance as get_conscious_decision
from core.enhanced_event_bus import get_instance as get_event_bus
from connectors.database.mysql_connector import MySQLConnector

# 设置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

@dataclass
class EmotionAnalysisContext:
    """情感分析上下文"""
    user_id: str
    message: str
    current_emotion: str
    current_intensity: int
    interaction_history: List[Dict]
    relationship_context: Dict
    environmental_factors: Dict
    time_context: Dict

@dataclass
class EmotionDecision:
    """情感决策结果"""
    new_emotion: str
    new_intensity: int
    reasoning: str
    confidence: float
    factors_considered: List[str]
    alternative_emotions: List[Dict]

class AutonomousEmotionSystem:
    """自主决策情感系统"""
    
    def __init__(self, mysql_connector: MySQLConnector = None):
        """初始化自主情感系统"""
        self.mysql_connector = mysql_connector
        self.conscious_decision = get_conscious_decision()
        self.event_bus = get_event_bus()
        
        # 加载情感等级标准
        self.emotion_levels_config = self._load_emotion_levels_config()
        
        # 情感分析能力配置
        self.analysis_capabilities = {
            "semantic_understanding": 0.85,
            "context_awareness": 0.80,
            "emotional_intelligence": 0.90,
            "relationship_sensitivity": 0.88,
            "temporal_reasoning": 0.75
        }
        
        # 自主决策参数
        self.autonomy_config = {
            "decision_threshold": 0.7,  # 自主决策置信度阈值
            "learning_rate": 0.1,       # 学习率
            "adaptation_speed": 0.05,   # 适应速度
            "memory_influence": 0.3,    # 记忆影响权重
            "context_influence": 0.4,   # 上下文影响权重
            "personality_influence": 0.3 # 个性影响权重
        }
        
        logger.success("自主决策情感系统初始化完成")
    
    def _load_emotion_levels_config(self) -> Dict:
        """加载情感等级配置"""
        try:
            config_path = os.path.join(PROJECT_ROOT, "config", "emotion_levels_standard.json")
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning_status(f"加载情感等级配置失败: {e}")
            return self._get_default_emotion_levels()
    
    def _get_default_emotion_levels(self) -> Dict:
        """获取默认情感等级配置"""
        return {
            "emotion_levels_standard": {
                "levels": [
                    {"range": [-200, -100], "label": "陌生人"},
                    {"range": [-101, -1], "label": "临时联系人"},
                    {"range": [0, 0], "label": "素不相识"},
                    {"range": [1, 10], "label": "几面之缘"},
                    {"range": [11, 50], "label": "点赞之交"},
                    {"range": [51, 200], "label": "泛泛之交"},
                    {"range": [201, 500], "label": "点头之交"},
                    {"range": [501, 1000], "label": "聊天之友"},
                    {"range": [1001, 2500], "label": "酒肉朋友"},
                    {"range": [2501, 5000], "label": "工作伙伴"},
                    {"range": [5001, 10000], "label": "志同道合"},
                    {"range": [10001, 50000], "label": "谈心密友"},
                    {"range": [50001, 200000], "label": "知心好友"},
                    {"range": [200001, 500000], "label": "挚友"},
                    {"range": [500001, 5000000], "label": "生死之交"},
                    {"range": [5000001, float('inf')], "label": "灵魂缔造者"}
                ]
            }
        }
    
    def get_emotion_level(self, intensity: int) -> str:
        """获取情感强度对应的关系级别"""
        levels = self.emotion_levels_config["emotion_levels_standard"]["levels"]
        
        # 处理小于-200的情况
        if intensity < -200:
            intensity = 0
        
        for level in levels:
            range_val = level["range"]
            if len(range_val) == 2:
                if range_val[1] == "infinity":
                    if intensity >= range_val[0]:
                        return level["label"]
                elif range_val[0] == range_val[1]:  # 单点值
                    if intensity == range_val[0]:
                        return level["label"]
                else:  # 范围值
                    if range_val[0] <= intensity <= range_val[1]:
                        return level["label"]
        
        return "未知"
    
    def process_user_message_autonomous(self, user_id: str, message: str) -> EmotionDecision:
        """
        自主处理用户消息并决策情感更新
        
        这是核心方法，融合了：
        1. 语义理解
        2. 上下文分析  
        3. 关系评估
        4. 自主决策
        5. 学习适应
        """
        try:
            # 1. 构建分析上下文
            context = self._build_analysis_context(user_id, message)
            
            # 2. 进行多维度情感分析
            analysis_result = self._comprehensive_emotion_analysis(context)
            
            # 3. 自主决策情感更新
            decision = self._make_autonomous_emotion_decision(context, analysis_result)
            
            # 4. 执行情感更新
            self._execute_emotion_update(user_id, decision)
            
            # 5. 学习和适应
            self._learn_from_interaction(context, decision)
            
            logger.info(f"自主情感决策完成: {decision.new_emotion}({decision.new_intensity}) - {decision.reasoning[:50]}...")
            return decision
            
        except Exception as e:
            logger.error_status(f"自主情感处理失败: {e}")
            # 降级到基础处理
            return self._fallback_emotion_processing(user_id, message)
    
    def _build_analysis_context(self, user_id: str, message: str) -> EmotionAnalysisContext:
        """构建情感分析上下文"""
        # 获取当前情感状态
        current_emotion, current_intensity = self._get_current_emotion(user_id)
        
        # 获取交互历史
        interaction_history = self._get_interaction_history(user_id, limit=10)
        
        # 获取关系上下文
        relationship_context = self._get_relationship_context(user_id)
        
        # 获取环境因素
        environmental_factors = self._get_environmental_factors()
        
        # 获取时间上下文
        time_context = self._get_time_context()
        
        return EmotionAnalysisContext(
            user_id=user_id,
            message=message,
            current_emotion=current_emotion,
            current_intensity=current_intensity,
            interaction_history=interaction_history,
            relationship_context=relationship_context,
            environmental_factors=environmental_factors,
            time_context=time_context
        )
    
    def _comprehensive_emotion_analysis(self, context: EmotionAnalysisContext) -> Dict[str, Any]:
        """综合情感分析"""
        analysis = {
            "semantic_signals": self._analyze_semantic_signals(context.message),
            "contextual_cues": self._analyze_contextual_cues(context),
            "relationship_dynamics": self._analyze_relationship_dynamics(context),
            "temporal_patterns": self._analyze_temporal_patterns(context),
            "emotional_trajectory": self._analyze_emotional_trajectory(context)
        }
        
        # 计算综合情感倾向
        analysis["overall_tendency"] = self._calculate_overall_tendency(analysis)
        
        return analysis
    
    def _make_autonomous_emotion_decision(self, context: EmotionAnalysisContext, 
                                        analysis: Dict[str, Any]) -> EmotionDecision:
        """进行自主情感决策"""
        
        # 构建决策输入
        decision_input = {
            "context": {
                "user_message": context.message,
                "current_emotion": context.current_emotion,
                "current_intensity": context.current_intensity,
                "relationship_level": self.get_emotion_level(context.current_intensity),
                "interaction_count": len(context.interaction_history),
                "recent_interactions": context.interaction_history[-3:] if context.interaction_history else []
            },
            "analysis": analysis,
            "capabilities": self.analysis_capabilities,
            "autonomy_config": self.autonomy_config
        }
        
        # 使用意识决策模块进行决策
        try:
            conscious_result = self.conscious_decision.make_decision(
                input_data=decision_input,
                shared_data={"emotion_system": "autonomous"}
            )
            
            # 解析决策结果
            return self._parse_conscious_decision(conscious_result, context)
            
        except Exception as e:
            logger.warning_status(f"意识决策失败，使用启发式决策: {e}")
            return self._heuristic_emotion_decision(context, analysis)
    
    def _parse_conscious_decision(self, conscious_result: Any, 
                                context: EmotionAnalysisContext) -> EmotionDecision:
        """解析意识决策结果"""
        
        # 如果conscious_result是Decision对象
        if hasattr(conscious_result, 'content'):
            decision_content = conscious_result.content
            reasoning = conscious_result.reasoning
            confidence = conscious_result.confidence
        else:
            # 如果是字典格式
            decision_content = str(conscious_result)
            reasoning = "自主决策分析"
            confidence = 0.8
        
        # 从决策内容中提取情感和强度
        new_emotion, new_intensity = self._extract_emotion_from_decision(
            decision_content, context
        )
        
        return EmotionDecision(
            new_emotion=new_emotion,
            new_intensity=new_intensity,
            reasoning=reasoning,
            confidence=confidence,
            factors_considered=["语义分析", "上下文理解", "关系动态", "时间模式"],
            alternative_emotions=[]
        )
    
    def _extract_emotion_from_decision(self, decision_content: str, 
                                     context: EmotionAnalysisContext) -> Tuple[str, int]:
        """从决策内容中提取情感和强度"""
        
        # 基于消息内容的情感关键词分析
        positive_keywords = ["谢谢", "感谢", "太好了", "很棒", "喜欢", "开心", "高兴"]
        negative_keywords = ["不好", "失望", "生气", "难过", "讨厌", "烦"]
        neutral_keywords = ["好的", "知道了", "嗯", "哦"]
        
        message_lower = context.message.lower()
        
        # 计算情感倾向
        positive_score = sum(1 for kw in positive_keywords if kw in message_lower)
        negative_score = sum(1 for kw in negative_keywords if kw in message_lower)
        
        # 确定新的情感强度变化
        if positive_score > negative_score:
            # 正面交互
            intensity_change = random.randint(1, 5) * (1 + positive_score * 0.5)
            new_emotion = self._select_positive_emotion(context.current_emotion)
        elif negative_score > positive_score:
            # 负面交互
            intensity_change = -random.randint(1, 3) * (1 + negative_score * 0.5)
            new_emotion = self._select_negative_emotion(context.current_emotion)
        else:
            # 中性交互
            intensity_change = random.randint(-1, 2)
            new_emotion = context.current_emotion
        
        # 计算新强度
        new_intensity = context.current_intensity + int(intensity_change)
        
        # 应用边界规则
        new_intensity = self._apply_intensity_boundaries(new_intensity)
        
        # 根据新强度调整情感类型
        new_emotion = self._adjust_emotion_by_intensity(new_emotion, new_intensity)
        
        return new_emotion, new_intensity
    
    def _select_positive_emotion(self, current_emotion: str) -> str:
        """选择正面情感"""
        positive_emotions = ["高兴", "愉悦", "欣喜", "满足", "感激", "温暖"]
        if current_emotion in positive_emotions:
            return current_emotion
        return random.choice(positive_emotions)
    
    def _select_negative_emotion(self, current_emotion: str) -> str:
        """选择负面情感"""
        negative_emotions = ["困惑", "担忧", "沮丧", "失望"]
        if current_emotion in negative_emotions:
            return current_emotion
        return random.choice(negative_emotions)
    
    def _apply_intensity_boundaries(self, intensity: int) -> int:
        """应用强度边界规则"""
        if intensity < -200:
            return 0
        elif intensity > 5000000:
            return 5000000
        return intensity
    
    def _adjust_emotion_by_intensity(self, emotion: str, intensity: int) -> str:
        """根据强度调整情感类型"""
        level = self.get_emotion_level(intensity)
        
        # 根据关系级别调整情感表达
        if level in ["陌生人", "临时联系人", "素不相识"]:
            return "中性" if emotion not in ["困惑", "担忧"] else emotion
        elif level in ["几面之缘", "点赞之交"]:
            return emotion if emotion != "激动" else "高兴"
        else:
            return emotion
    
    def _heuristic_emotion_decision(self, context: EmotionAnalysisContext,
                                  analysis: Dict[str, Any]) -> EmotionDecision:
        """启发式情感决策（备用方案）"""
        
        # 简化的规则决策
        message = context.message
        current_intensity = context.current_intensity
        
        if any(word in message for word in ["谢谢", "感谢", "太好了"]):
            new_emotion = "感激"
            intensity_change = random.randint(2, 5)
        elif any(word in message for word in ["不好", "失望", "生气"]):
            new_emotion = "沮丧"
            intensity_change = random.randint(-3, -1)
        else:
            new_emotion = context.current_emotion
            intensity_change = random.randint(-1, 2)
        
        new_intensity = self._apply_intensity_boundaries(current_intensity + intensity_change)
        
        return EmotionDecision(
            new_emotion=new_emotion,
            new_intensity=new_intensity,
            reasoning="基于启发式规则的情感决策",
            confidence=0.6,
            factors_considered=["关键词匹配", "基础规则"],
            alternative_emotions=[]
        )
    
    def _execute_emotion_update(self, user_id: str, decision: EmotionDecision):
        """执行情感更新"""
        try:
            if self.mysql_connector:
                # 更新到新的ai_user_relationships表
                self._update_to_new_table(user_id, decision)
            else:
                logger.warning_status("MySQL连接不可用，情感更新仅在内存中进行")
                
        except Exception as e:
            logger.error_status(f"执行情感更新失败: {e}")
    
    def _update_to_new_table(self, user_id: str, decision: EmotionDecision):
        """更新到新的ai_user_relationships表"""
        try:
            # 检查记录是否存在
            check_query = """
                SELECT id FROM ai_user_relationships 
                WHERE user_id = %s AND ai_id = 'yanran'
            """
            result = self.mysql_connector.query(check_query, (user_id,))
            
            if result and result[0]:
                # 更新现有记录
                update_query = """
                    UPDATE ai_user_relationships 
                    SET emotional_weight = %s,
                        relationship_type = %s,
                        interaction_count = interaction_count + 1,
                        last_interaction_time = NOW(),
                        emotional_state = JSON_SET(
                            COALESCE(emotional_state, '{}'),
                            '$.current_emotion', %s,
                            '$.intensity', %s,
                            '$.last_update', NOW(),
                            '$.reasoning', %s
                        ),
                        updated_at = NOW()
                    WHERE user_id = %s AND ai_id = 'yanran'
                """
                
                # 计算情感权重 (0-1之间)
                emotional_weight = min(1.0, max(0.0, (decision.new_intensity + 200) / 5200))
                relationship_type = self._map_level_to_type(decision.new_intensity)
                
                success, result, error = self.mysql_connector.execute_update(update_query, (
                    emotional_weight,
                    relationship_type,
                    decision.new_emotion,
                    decision.new_intensity,
                    decision.reasoning,
                    user_id
                ))
            else:
                # 插入新记录
                insert_query = """
                    INSERT INTO ai_user_relationships (
                        user_id, ai_id, relationship_type, emotional_weight,
                        trust_level, intimacy_level, interaction_count,
                        last_interaction_time, emotional_state, created_at
                    ) VALUES (%s, 'yanran', %s, %s, %s, %s, 1, NOW(), %s, NOW())
                """
                
                emotional_weight = min(1.0, max(0.0, (decision.new_intensity + 200) / 5200))
                relationship_type = self._map_level_to_type(decision.new_intensity)
                trust_level = min(1.0, max(0.0, emotional_weight * 0.8))
                intimacy_level = min(1.0, max(0.0, emotional_weight * 0.6))
                
                emotional_state = json.dumps({
                    "current_emotion": decision.new_emotion,
                    "intensity": decision.new_intensity,
                    "last_update": datetime.now().isoformat(),
                    "reasoning": decision.reasoning
                })
                
                success, result, error = self.mysql_connector.execute_update(insert_query, (
                    user_id, relationship_type, emotional_weight,
                    trust_level, intimacy_level, emotional_state
                ))
                
            logger.success(f"情感数据已更新到ai_user_relationships表: {user_id}")
            
        except Exception as e:
            logger.error_status(f"更新ai_user_relationships表失败: {e}")
    
    def _map_level_to_type(self, intensity: int) -> str:
        """将情感强度映射到关系类型"""
        level = self.get_emotion_level(intensity)
        
        mapping = {
            "陌生人": "stranger",
            "临时联系人": "acquaintance", 
            "素不相识": "stranger",
            "几面之缘": "acquaintance",
            "点赞之交": "acquaintance",
            "泛泛之交": "acquaintance",
            "点头之交": "acquaintance",
            "聊天之友": "friend",
            "酒肉朋友": "friend",
            "工作伙伴": "friend",
            "志同道合": "close_friend",
            "谈心密友": "close_friend",
            "知心好友": "best_friend",
            "挚友": "best_friend",
            "生死之交": "soulmate",
            "灵魂缔造者": "soulmate"
        }
        
        return mapping.get(level, "friend")
    
    def _learn_from_interaction(self, context: EmotionAnalysisContext, decision: EmotionDecision):
        """从交互中学习"""
        # 这里可以实现学习逻辑，比如：
        # 1. 更新用户偏好模型
        # 2. 调整决策参数
        # 3. 优化情感识别准确率
        pass
    
    def _get_current_emotion(self, user_id: str) -> Tuple[str, int]:
        """获取当前情感状态"""
        try:
            if self.mysql_connector:
                query = """
                    SELECT emotional_state FROM ai_user_relationships 
                    WHERE user_id = %s AND ai_id = 'yanran'
                """
                result = self.mysql_connector.query(query, (user_id,))
                
                if result and result[0] and result[0][0]:
                    emotional_state = json.loads(result[0][0])
                    return (
                        emotional_state.get("current_emotion", "中性"),
                        emotional_state.get("intensity", 0)
                    )
        except Exception as e:
            logger.warning_status(f"获取当前情感状态失败: {e}")
        
        return "中性", 0
    
    def _get_interaction_history(self, user_id: str, limit: int = 10) -> List[Dict]:
        """获取交互历史"""
        try:
            if self.mysql_connector:
                query = """
                    SELECT content, role, timestamp FROM messages 
                    WHERE user_id = %s 
                    ORDER BY timestamp DESC 
                    LIMIT %s
                """
                result = self.mysql_connector.query(query, (user_id, limit))
                
                if result:
                    return [
                        {
                            "content": row[0],
                            "role": row[1],
                            "timestamp": row[2].isoformat() if row[2] else None
                        }
                        for row in result
                    ]
        except Exception as e:
            logger.warning_status(f"获取交互历史失败: {e}")
        
        return []
    
    def _get_relationship_context(self, user_id: str) -> Dict:
        """获取关系上下文"""
        try:
            if self.mysql_connector:
                query = """
                    SELECT relationship_type, emotional_weight, trust_level, 
                           intimacy_level, interaction_count, last_interaction_time
                    FROM ai_user_relationships 
                    WHERE user_id = %s AND ai_id = 'yanran'
                """
                result = self.mysql_connector.query(query, (user_id,))
                
                if result and result[0]:
                    row = result[0]
                    return {
                        "relationship_type": row[0],
                        "emotional_weight": float(row[1]) if row[1] else 0.0,
                        "trust_level": float(row[2]) if row[2] else 0.0,
                        "intimacy_level": float(row[3]) if row[3] else 0.0,
                        "interaction_count": row[4] or 0,
                        "last_interaction_time": row[5].isoformat() if row[5] else None
                    }
        except Exception as e:
            logger.warning_status(f"获取关系上下文失败: {e}")
        
        return {
            "relationship_type": "friend",
            "emotional_weight": 0.5,
            "trust_level": 0.5,
            "intimacy_level": 0.0,
            "interaction_count": 0,
            "last_interaction_time": None
        }
    
    def _get_environmental_factors(self) -> Dict:
        """获取环境因素"""
        return {
            "time_of_day": datetime.now().hour,
            "day_of_week": datetime.now().weekday(),
            "system_load": "normal"  # 可以扩展
        }
    
    def _get_time_context(self) -> Dict:
        """获取时间上下文"""
        now = datetime.now()
        return {
            "current_time": now.isoformat(),
            "hour": now.hour,
            "day_of_week": now.weekday(),
            "is_weekend": self._is_weekend()
        }
    
    def _analyze_semantic_signals(self, message: str) -> Dict:
        """分析语义信号"""
        return {
            "sentiment": "positive" if any(w in message for w in ["好", "谢谢", "棒"]) else "neutral",
            "emotional_intensity": len([w for w in ["很", "非常", "超级", "太"] if w in message]),
            "question_count": message.count("?") + message.count("？")
        }
    
    def _analyze_contextual_cues(self, context: EmotionAnalysisContext) -> Dict:
        """分析上下文线索"""
        return {
            "conversation_length": len(context.interaction_history),
            "recent_sentiment": "positive",  # 简化实现
            "topic_continuity": True
        }
    
    def _analyze_relationship_dynamics(self, context: EmotionAnalysisContext) -> Dict:
        """分析关系动态"""
        return {
            "relationship_progression": "stable",
            "trust_trend": "increasing",
            "intimacy_change": "gradual"
        }
    
    def _analyze_temporal_patterns(self, context: EmotionAnalysisContext) -> Dict:
        """分析时间模式"""
        return {
            "interaction_frequency": "regular",
            "time_since_last": "recent",
            "seasonal_factor": "normal"
        }
    
    def _analyze_emotional_trajectory(self, context: EmotionAnalysisContext) -> Dict:
        """分析情感轨迹"""
        return {
            "trend": "stable",
            "volatility": "low",
            "direction": "positive"
        }
    
    def _calculate_overall_tendency(self, analysis: Dict) -> Dict:
        """计算综合情感倾向"""
        return {
            "primary_emotion": "positive",
            "confidence": 0.8,
            "intensity_suggestion": "moderate"
        }
    
    def _fallback_emotion_processing(self, user_id: str, message: str) -> EmotionDecision:
        """降级情感处理"""
        return EmotionDecision(
            new_emotion="中性",
            new_intensity=0,
            reasoning="系统降级处理",
            confidence=0.5,
            factors_considered=["降级处理"],
            alternative_emotions=[]
        )
    
    def adjust_emotion_based_on_frequency_autonomous(self, user_id: str) -> EmotionDecision:
        """基于交互频率的自主情感调整"""
        try:
            # 获取最后交互时间
            if self.mysql_connector:
                query = """
                    SELECT last_interaction_time, emotional_state 
                    FROM ai_user_relationships 
                    WHERE user_id = %s AND ai_id = 'yanran'
                """
                result = self.mysql_connector.query(query, (user_id,))
                
                if result and result[0]:
                    last_interaction = result[0][0]
                    emotional_state = json.loads(result[0][1]) if result[0][1] else {}
                    
                    if last_interaction:
                        time_diff = datetime.now() - last_interaction
                        days_inactive = time_diff.days
                        
                        # 获取衰减配置
                        decay_rules = self.emotion_levels_config["emotion_levels_standard"]["update_triggers"]["time_decay"]["rules"]
                        
                        # 找到适用的衰减规则
                        applicable_decay = 0
                        for rule in decay_rules:
                            if days_inactive >= rule["days_inactive"]:
                                applicable_decay = rule["decay_rate"]
                        
                        if applicable_decay > 0:
                            current_intensity = emotional_state.get("intensity", 0)
                            new_intensity = max(0, current_intensity - applicable_decay)
                            
                            decision = EmotionDecision(
                                new_emotion=emotional_state.get("current_emotion", "中性"),
                                new_intensity=int(new_intensity),
                                reasoning=f"基于{days_inactive}天未互动的自动衰减调整",
                                confidence=0.9,
                                factors_considered=["时间衰减", "交互频率"],
                                alternative_emotions=[]
                            )
                            
                            # 执行更新
                            self._execute_emotion_update(user_id, decision)
                            return decision
                            
        except Exception as e:
            logger.error_status(f"自主频率调整失败: {e}")
        
        # 返回无变化的决策
        current_emotion, current_intensity = self._get_current_emotion(user_id)
        return EmotionDecision(
            new_emotion=current_emotion,
            new_intensity=current_intensity,
            reasoning="无需调整",
            confidence=0.8,
            factors_considered=["频率检查"],
            alternative_emotions=[]
        )
    
    def _is_weekend(self) -> bool:
        """判断是否为周末/休息日（基于数据库t_calendar表）- 🔥 老王性能优化"""
        try:
            from utilities.workday_cache_manager import is_workday_fast
            return not is_workday_fast()
        except Exception as e:
            logger.warning_status(f"获取工作日信息失败，使用默认判断: {e}")
            # 降级到默认判断
            return datetime.now().weekday() >= 5


# 单例模式
_autonomous_emotion_instance = None

def get_autonomous_emotion_system(mysql_connector: MySQLConnector = None) -> AutonomousEmotionSystem:
    """获取自主情感系统实例"""
    global _autonomous_emotion_instance
    if _autonomous_emotion_instance is None:
        _autonomous_emotion_instance = AutonomousEmotionSystem(mysql_connector)
    return _autonomous_emotion_instance 