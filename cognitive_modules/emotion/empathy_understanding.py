#!/usr/bin/env python3
"""
共情理解模块 - Empathy Understanding

该模块实现了数字生命体的共情能力，用于理解和回应用户的情感状态，
提供适当的情感支持和反馈，增强人机交互的情感连接。

作者: Claude
创建日期: 2024-07-15
版本: 1.0
"""

import os
import sys
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import time
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from collections import defaultdict, deque

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase

# 尝试导入事件总线
try:
    from core.enhanced_event_bus import get_instance as get_event_bus
except ImportError:
    try:
        from core.event_bus import get_instance as get_event_bus
    except ImportError:
        logger = get_unified_logger(__name__)
        logger.warning_status("无法导入事件总线，将使用模拟版本")
        
        class DummyEventBus:
            def publish(self, *args, **kwargs): pass
            def subscribe(self, *args, **kwargs): pass
            
        def get_event_bus():
            return DummyEventBus()

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.emotion.empathy")

class EmpathyUnderstanding(CognitiveModuleBase):
    """
    共情理解模块
    
    负责理解用户的情感状态，提供共情反馈和情感支持。
    主要功能包括：
    
    1. 情感识别：识别用户表达的情感状态
    2. 共情生成：生成适当的共情回应
    3. 情感记忆：记住用户的情感历史和偏好
    4. 个性化共情：根据用户特点提供个性化的共情反馈
    """
    
    _instance = None
    
    @classmethod
    def get_instance(cls, config: Dict[str, Any] = None) -> 'EmpathyUnderstanding':
        """
        获取模块单例实例
        
        参数:
            config: 配置信息
            
        返回:
            EmpathyUnderstanding: 模块实例
        """
        if cls._instance is None:
            cls._instance = EmpathyUnderstanding("empathy_understanding", "emotion", config)
        return cls._instance
    
    def __init__(self, module_id: str, module_type: str, config: Dict[str, Any] = None):
        """
        初始化共情理解模块
        
        参数:
            module_id: 模块ID
            module_type: 模块类型
            config: 配置信息
        """
        super().__init__(module_id, module_type, config)
        
        # 设置模块描述
        self.description = "共情理解模块 - 负责理解用户情感并提供适当的共情反馈"
        
        # 获取事件总线
        self.event_bus = get_event_bus()
        
        # 默认配置
        self.default_config = {
            "empathy_level": 0.7,             # 共情级别 (0-1)
            "emotion_memory_size": 50,        # 每个用户的情感记忆大小
            "user_history_limit": 100,        # 记忆的最大用户数
            "response_templates_path": "",    # 共情回应模板路径
            "min_confidence": 0.4,            # 最小情感识别置信度
            "context_window": 5,              # 情感分析上下文窗口大小
            "empathy_adaptation_rate": 0.05,  # 共情适应率
            "cultural_sensitivity": 0.6,      # 文化敏感度
            "personal_distance": 0.5          # 个人距离保持度
        }
        
        # 加载配置
        self.empathy_config = self.default_config.copy()
        if config:
            self.empathy_config.update(config)
        
        # 情感类别及其关键词
        self.emotion_categories = {
            "快乐": ["高兴", "开心", "快乐", "喜悦", "欣喜", "愉快", "兴奋", "欢乐", "满足", "幸福"],
            "悲伤": ["悲伤", "难过", "伤心", "痛苦", "忧伤", "哀愁", "悲痛", "沮丧", "失落", "哀伤"],
            "愤怒": ["愤怒", "生气", "恼怒", "气愤", "暴怒", "恼火", "发火", "不满", "烦躁", "狂怒"],
            "恐惧": ["恐惧", "害怕", "惊恐", "惊吓", "担忧", "忧虑", "紧张", "焦虑", "恐慌", "不安"],
            "厌恶": ["厌恶", "讨厌", "反感", "恶心", "憎恨", "嫌弃", "鄙视", "抵触", "厌烦", "痛恨"],
            "惊讶": ["惊讶", "震惊", "吃惊", "惊异", "意外", "震撼", "惊喜", "惊叹", "诧异", "不可思议"],
            "期待": ["期待", "盼望", "希望", "憧憬", "渴望", "企盼", "期盼", "向往", "期许", "憧憬"],
            "信任": ["信任", "相信", "信赖", "依靠", "依赖", "信赖", "放心", "托付", "信服", "信心"],
            "疲倦": ["疲倦", "累", "困", "乏力", "疲乏", "疲劳", "困倦", "精疲力竭", "劳累", "困乏"],
            "迷茫": ["迷茫", "困惑", "不解", "茫然", "疑惑", "迷惘", "不明白", "不懂", "不理解", "费解"]
        }
        
        # 共情回应模板
        self.empathy_templates = {
            "快乐": [
                "我为你感到高兴！",
                "这真是太棒了，我能感受到你的喜悦。",
                "听到这个消息我也很开心！",
                "你的快乐也感染了我，真为你开心。"
            ],
            "悲伤": [
                "我能理解你的感受，这确实令人难过。",
                "听到这些我很遗憾，希望你能渐渐好起来。",
                "我能感受到你的悲伤，如果需要倾诉，我在这里。",
                "这种经历确实令人伤心，你的感受是很自然的。"
            ],
            "愤怒": [
                "我理解你为什么会感到生气，这种情况确实令人恼火。",
                "你的愤怒是可以理解的，遇到这种事情换做是谁都会不满。",
                "我能感受到你的不满，这确实是个令人恼火的情况。",
                "你有权利感到愤怒，这种感受是很正常的。"
            ],
            "恐惧": [
                "面对未知的事情感到担忧是很自然的，我能理解你的感受。",
                "我理解你的忧虑，这种情况确实会让人不安。",
                "你的担心是合理的，我们可以一起想想解决办法。",
                "感到害怕是很正常的反应，我理解你现在的心情。"
            ],
            "厌恶": [
                "我理解你的反感，这种情况确实令人不舒服。",
                "你的感受是可以理解的，这确实让人不愉快。",
                "我能理解你为什么会有这种反应，这确实令人不适。",
                "你的厌恶感是很自然的反应，我能理解。"
            ],
            "惊讶": [
                "这确实很出人意料，我能理解你的惊讶。",
                "我也没想到会这样，这确实令人吃惊。",
                "这真是个意外的发展，我能理解你的震惊。",
                "这种情况确实让人惊讶，我和你有同样的感受。"
            ],
            "期待": [
                "我能感受到你的期待，希望一切顺利！",
                "你的期待是很自然的，我也为你感到期待。",
                "这确实是值得期待的事情，我理解你的心情。",
                "我能理解你的期待心情，希望结果不会让你失望。"
            ],
            "信任": [
                "你的信任对我来说非常重要，我会珍视这份信任。",
                "我理解信任对你的重要性，这是人际关系的基础。",
                "我能感受到你的信任，这让我们的交流更加顺畅。",
                "信任是需要时间建立的，我很高兴能获得你的信任。"
            ],
            "疲倦": [
                "我能理解你感到疲倦，适当的休息很重要。",
                "长时间的工作确实会让人感到疲惫，照顾好自己很重要。",
                "我能感受到你的疲惫，希望你能尽快恢复精力。",
                "疲劳是身体发出的信号，提醒我们需要休息，我理解你的感受。"
            ],
            "迷茫": [
                "面对复杂的情况感到迷茫是很正常的，我能理解你的困惑。",
                "我理解你的不确定感，有时候我们需要时间来理清思路。",
                "迷茫是成长过程中的一部分，我能理解你的心情。",
                "当面临选择时感到困惑是很自然的，我理解你的感受。"
            ]
        }
        
        # 用户情感历史
        self.user_emotion_history = defaultdict(lambda: deque(maxlen=self.empathy_config["emotion_memory_size"]))
        
        # 用户共情偏好
        self.user_empathy_preferences = {}
        
        # 最近处理的消息
        self.recent_messages = deque(maxlen=self.empathy_config["context_window"])
        
        # 标记模块已创建
        logger.info("共情理解模块已创建")
    
    def _do_initialize(self) -> None:
        """
        执行初始化逻辑
        """
        try:
            # 订阅相关事件
            self._subscribe_events()
            
            # 加载共情回应模板
            self._load_empathy_templates()
            
            # 加载用户共情偏好
            self._load_user_preferences()
            
            # 发布模块初始化事件
            self.event_bus.publish(
                "emotion.empathy.initialized",
                {
                    "module_id": self.module_id,
                    "time": time.time()
                },
                source=self.module_id
            )
            
            logger.success("共情理解模块初始化完成")
        except Exception as e:
            logger.error_status(f"共情理解模块初始化失败: {str(e)}")
            raise
    
    def _subscribe_events(self) -> None:
        """
        订阅相关事件
        """
        # 用户消息事件
        self.event_bus.subscribe("user.message.received", self._handle_user_message)
        
        # 情感分析事件
        self.event_bus.subscribe("emotion.analysis.completed", self._handle_emotion_analysis)
        
        # 用户反馈事件
        self.event_bus.subscribe("user.feedback.received", self._handle_user_feedback)
        
        # 系统关闭事件
        self.event_bus.subscribe("system.shutdown", self._handle_system_shutdown)
    
    def _handle_user_message(self, event_data: Dict[str, Any]) -> None:
        """
        处理用户消息事件
        
        参数:
            event_data: 事件数据
        """
        if "user_id" in event_data and "message" in event_data:
            user_id = event_data["user_id"]
            message = event_data["message"]
            
            # 保存消息到最近消息队列
            self.recent_messages.append({
                "user_id": user_id,
                "message": message,
                "timestamp": time.time()
            })
            
            # 提取情感
            emotion_result = self._extract_emotion(message)
            
            # 如果情感识别置信度足够高，处理情感
            if emotion_result["confidence"] >= self.empathy_config["min_confidence"]:
                # 添加到用户情感历史
                self._add_to_user_history(user_id, emotion_result)
                
                # 生成共情回应
                empathy_response = self._generate_empathy_response(user_id, emotion_result)
                
                # 发布共情回应事件
                self.event_bus.publish(
                    "emotion.empathy.response_generated",
                    {
                        "user_id": user_id,
                        "original_emotion": emotion_result,
                        "empathy_response": empathy_response,
                        "time": time.time()
                    },
                    source=self.module_id
                )
    
    def _handle_emotion_analysis(self, event_data: Dict[str, Any]) -> None:
        """
        处理情感分析事件
        
        参数:
            event_data: 事件数据
        """
        if "user_id" in event_data and "emotion_result" in event_data:
            user_id = event_data["user_id"]
            emotion_result = event_data["emotion_result"]
            
            # 添加到用户情感历史
            self._add_to_user_history(user_id, emotion_result)
            
            # 生成共情回应
            empathy_response = self._generate_empathy_response(user_id, emotion_result)
            
            # 发布共情回应事件
            self.event_bus.publish(
                "emotion.empathy.response_generated",
                {
                    "user_id": user_id,
                    "original_emotion": emotion_result,
                    "empathy_response": empathy_response,
                    "time": time.time()
                },
                source=self.module_id
            )
    
    def _handle_user_feedback(self, event_data: Dict[str, Any]) -> None:
        """
        处理用户反馈事件
        
        参数:
            event_data: 事件数据
        """
        if "user_id" in event_data and "feedback" in event_data:
            user_id = event_data["user_id"]
            feedback = event_data["feedback"]
            
            # 根据反馈调整用户共情偏好
            if "empathy_rating" in feedback:
                rating = feedback["empathy_rating"]
                self._update_user_preference(user_id, rating)
    
    def _handle_system_shutdown(self, event_data: Dict[str, Any]) -> None:
        """
        处理系统关闭事件
        
        参数:
            event_data: 事件数据
        """
        # 保存用户共情偏好
        self._save_user_preferences()
    
    def _do_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        根据输入的操作类型执行不同的处理。
        
        参数:
            input_data: 输入数据，包含操作类型和相关参数
            
        返回:
            Dict[str, Any]: 处理结果
        """
        operation = input_data.get("operation", "")
        
        if operation == "extract_emotion":
            if "text" in input_data:
                return {
                    "result": self._extract_emotion(input_data["text"])
                }
        
        elif operation == "generate_empathy":
            if "user_id" in input_data and "emotion" in input_data:
                return {
                    "result": self._generate_empathy_response(
                        input_data["user_id"],
                        input_data["emotion"]
                    )
                }
        
        elif operation == "get_user_history":
            if "user_id" in input_data:
                return {
                    "result": self._get_user_emotion_history(input_data["user_id"])
                }
        
        elif operation == "update_empathy_config":
            if "config" in input_data:
                success = self._update_empathy_config(input_data["config"])
                return {
                    "success": success
                }
        
        return {
            "error": "unknown_operation",
            "message": f"未知的操作: {operation}"
        }
    
    def _extract_emotion(self, text: str) -> Dict[str, Any]:
        """
        提取文本中的情感
        
        参数:
            text: 文本内容
            
        返回:
            Dict[str, Any]: 情感分析结果
        """
        # 初始化结果
        result = {
            "dominant_emotion": "中性",
            "emotions": {},
            "confidence": 0.0,
            "intensity": 0.0,
            "valence": 0.0  # 情感效价 (-1.0 to 1.0)
        }
        
        # 统计情感关键词出现频率
        emotion_scores = {}
        for emotion, keywords in self.emotion_categories.items():
            score = 0
            for keyword in keywords:
                if keyword in text:
                    score += 1
            
            if score > 0:
                # 归一化分数
                normalized_score = min(1.0, score / len(keywords) * 2)
                emotion_scores[emotion] = normalized_score
        
        # 如果找到情感关键词
        if emotion_scores:
            # 找出主导情感
            dominant_emotion = max(emotion_scores.items(), key=lambda x: x[1])
            result["dominant_emotion"] = dominant_emotion[0]
            
            # 设置置信度和强度
            result["confidence"] = dominant_emotion[1]
            result["intensity"] = dominant_emotion[1]
            
            # 设置所有情感分数
            result["emotions"] = emotion_scores
            
            # 设置情感效价
            valence_map = {
                "快乐": 0.8, "悲伤": -0.7, "愤怒": -0.6, "恐惧": -0.5,
                "厌恶": -0.8, "惊讶": 0.2, "期待": 0.6, "信任": 0.7,
                "疲倦": -0.3, "迷茫": -0.2
            }
            
            # 计算加权平均效价
            total_weight = sum(emotion_scores.values())
            if total_weight > 0:
                weighted_valence = sum(
                    emotion_scores[emotion] * valence_map.get(emotion, 0)
                    for emotion in emotion_scores
                ) / total_weight
                
                result["valence"] = weighted_valence
        
        return result
    
    def _generate_empathy_response(self, user_id: str, emotion_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成共情回应
        
        参数:
            user_id: 用户ID
            emotion_result: 情感分析结果
            
        返回:
            Dict[str, Any]: 共情回应
        """
        # 获取主导情感
        dominant_emotion = emotion_result.get("dominant_emotion", "中性")
        
        # 获取适合的共情模板
        templates = self.empathy_templates.get(dominant_emotion, ["我理解你的感受。"])
        
        # 如果是中性情感或未识别情感
        if dominant_emotion == "中性":
            templates = ["我听到了你说的话。", "我在仔细聆听。", "请继续说下去。", "我在这里。"]
        
        # 选择一个模板（这里简单实现，可以根据历史避免重复）
        import random
        template = random.choice(templates)
        
        # 获取用户共情偏好
        empathy_level = self.empathy_config["empathy_level"]
        if user_id in self.user_empathy_preferences:
            empathy_level = self.user_empathy_preferences[user_id]
        
        # 根据共情级别调整回应
        response = template
        
        # 生成共情回应结果
        result = {
            "text": response,
            "emotion": dominant_emotion,
            "empathy_level": empathy_level,
            "intensity": emotion_result.get("intensity", 0.5),
            "context_aware": True
        }
        
        return result
    
    def _add_to_user_history(self, user_id: str, emotion_result: Dict[str, Any]) -> None:
        """
        添加情感结果到用户历史
        
        参数:
            user_id: 用户ID
            emotion_result: 情感分析结果
        """
        # 添加时间戳
        entry = emotion_result.copy()
        entry["timestamp"] = time.time()
        
        # 添加到用户历史
        self.user_emotion_history[user_id].append(entry)
        
        # 限制记忆的用户数量
        if len(self.user_emotion_history) > self.empathy_config["user_history_limit"]:
            # 移除最旧的用户记录
            oldest_user = next(iter(self.user_emotion_history))
            del self.user_emotion_history[oldest_user]
    
    def _update_user_preference(self, user_id: str, rating: float) -> None:
        """
        更新用户共情偏好
        
        参数:
            user_id: 用户ID
            rating: 共情评分 (0-1)
        """
        # 获取当前共情级别
        current_level = self.user_empathy_preferences.get(user_id, self.empathy_config["empathy_level"])
        
        # 计算新的共情级别
        adaptation_rate = self.empathy_config["empathy_adaptation_rate"]
        new_level = current_level * (1 - adaptation_rate) + rating * adaptation_rate
        
        # 更新用户偏好
        self.user_empathy_preferences[user_id] = new_level
    
    def _get_user_emotion_history(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取用户情感历史
        
        参数:
            user_id: 用户ID
            
        返回:
            List[Dict[str, Any]]: 情感历史记录
        """
        if user_id in self.user_emotion_history:
            return list(self.user_emotion_history[user_id])
        return []
    
    def _update_empathy_config(self, config: Dict[str, Any]) -> bool:
        """
        更新共情配置
        
        参数:
            config: 新的配置参数
            
        返回:
            bool: 更新是否成功
        """
        try:
            # 更新配置
            for key, value in config.items():
                if key in self.empathy_config:
                    self.empathy_config[key] = value
            
            # 更新情感记忆大小
            if "emotion_memory_size" in config:
                new_size = config["emotion_memory_size"]
                for user_id in self.user_emotion_history:
                    # 创建新的队列
                    history = self.user_emotion_history[user_id]
                    self.user_emotion_history[user_id] = deque(history, maxlen=new_size)
            
            logger.info(f"共情配置已更新: {config}")
            return True
        except Exception as e:
            logger.error_status(f"更新共情配置失败: {str(e)}")
            return False
    
    def _load_empathy_templates(self) -> None:
        """
        加载共情回应模板
        """
        # 检查是否指定了模板路径
        templates_path = self.empathy_config["response_templates_path"]
        if templates_path and os.path.exists(templates_path):
            try:
                with open(templates_path, 'r', encoding='utf-8') as f:
                    templates = json.load(f)
                    self.empathy_templates.update(templates)
                logger.info(f"从 {templates_path} 加载了共情回应模板")
            except Exception as e:
                logger.error_status(f"加载共情回应模板失败: {str(e)}")
    
    def _load_user_preferences(self) -> None:
        """
        加载用户共情偏好
        """
        # 实际实现可能会从文件或数据库加载
        pass
    
    def _save_user_preferences(self) -> None:
        """
        保存用户共情偏好
        """
        # 实际实现可能会保存到文件或数据库
        pass
    
    def _do_update(self, context: Dict[str, Any]) -> None:
        """
        执行状态更新逻辑
        
        参数:
            context: 上下文信息
        """
        # 如果上下文包含文化信息，调整文化敏感度
        if "culture" in context:
            self._adjust_cultural_sensitivity(context["culture"])
    
    def _adjust_cultural_sensitivity(self, culture_info: Dict[str, Any]) -> None:
        """
        调整文化敏感度
        
        参数:
            culture_info: 文化相关信息
        """
        # 根据文化信息调整文化敏感度
        culture_type = culture_info.get("type", "")
        
        # 例如，对于不同文化背景的用户，调整共情表达方式
        if culture_type == "eastern":
            # 东方文化可能更含蓄
            self.empathy_config["personal_distance"] = 0.7
        elif culture_type == "western":
            # 西方文化可能更直接
            self.empathy_config["personal_distance"] = 0.4
    
    def _do_shutdown(self) -> None:
        """
        执行关闭逻辑
        """
        # 保存用户共情偏好
        self._save_user_preferences()
        
        # 发布模块关闭事件
        self.event_bus.publish(
            "emotion.empathy.shutdown",
            {
                "module_id": self.module_id,
                "time": time.time()
            },
            source=self.module_id
        )
        
        logger.info("共情理解模块已关闭")

# 模块单例获取函数
def get_instance(config: Dict[str, Any] = None) -> EmpathyUnderstanding:
    """
    获取模块实例
    
    参数:
        config: 配置信息
        
    返回:
        EmpathyUnderstanding: 模块实例
    """
    return EmpathyUnderstanding.get_instance(config) 