"""
情感智能模块

负责识别、理解和管理情感，包括情感识别、情感响应、情感记忆等功能。
从legacy系统的emotion_system演化而来，但增加了更多的情感智能能力。
"""

import os
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
from typing import Dict, Any, List, Optional, Tuple

# 配置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

class EmotionalIntelligence:
    """
    情感智能模块类
    
    负责识别、理解和管理情感，包括：
    - 情感识别（从文本、语音等输入中识别情感）
    - 情感评估（评估情感的类型、强度、原因等）
    - 情感记忆（记录和回忆情感经历）
    - 情感表达（生成合适的情感响应）
    - 情感管理（调节自身情感状态）
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化情感智能模块
        
        Args:
            config: 模块配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        self.config = config or {}
        
        # 情感数据存储路径
        self.emotions_dir = self.config.get('emotions_dir', os.path.join('data', 'emotions'))
        
        # 情感缓存
        self.emotions_cache = {}
        self.emotions_cache_time = {}
        self.emotions_cache_expiry = self.config.get('emotions_cache_expiry', 300)  # 默认5分钟
        
        # 加载统一的情感等级标准
        self.emotion_levels = self._load_emotion_levels_standard()
        
        # 情感类型映射表
        self.emotion_types = {
            "高兴": {"value": 5, "category": "积极", "intensity": "中等"},
            "愉悦": {"value": 8, "category": "积极", "intensity": "中等"},
            "欣喜": {"value": 10, "category": "积极", "intensity": "高"},
            "激动": {"value": 12, "category": "积极", "intensity": "高"},
            "狂喜": {"value": 15, "category": "积极", "intensity": "极高"},
            "满足": {"value": 3, "category": "积极", "intensity": "低"},
            "感激": {"value": 7, "category": "积极", "intensity": "中等"},
            "温暖": {"value": 4, "category": "积极", "intensity": "低"},
            "放松": {"value": 2, "category": "积极", "intensity": "低"},
            "宁静": {"value": 1, "category": "积极", "intensity": "低"},
            "中性": {"value": 0, "category": "中性", "intensity": "无"},
            "困惑": {"value": -2, "category": "消极", "intensity": "低"},
            "担忧": {"value": -3, "category": "消极", "intensity": "低"},
            "焦虑": {"value": -5, "category": "消极", "intensity": "中等"},
            "紧张": {"value": -4, "category": "消极", "intensity": "中等"},
            "恐惧": {"value": -8, "category": "消极", "intensity": "高"},
            "沮丧": {"value": -6, "category": "消极", "intensity": "中等"},
            "悲伤": {"value": -7, "category": "消极", "intensity": "中等"},
            "痛苦": {"value": -10, "category": "消极", "intensity": "高"},
            "绝望": {"value": -15, "category": "消极", "intensity": "极高"},
            "愤怒": {"value": -9, "category": "消极", "intensity": "高"},
            "厌恶": {"value": -7, "category": "消极", "intensity": "中等"},
            "鄙视": {"value": -6, "category": "消极", "intensity": "中等"},
            "嫉妒": {"value": -5, "category": "消极", "intensity": "中等"},
            "惊讶": {"value": 1, "category": "中性", "intensity": "低"}
        }
        
        # 情感关键词词典
        self.emotion_keywords = {
            "高兴": ["高兴", "开心", "快乐", "愉快", "欢喜", "欢乐", "快活", "乐意"],
            "愉悦": ["愉悦", "舒畅", "畅快", "享受", "快意", "舒心", "惬意"],
            "欣喜": ["欣喜", "欣慰", "欢欣", "喜悦", "雀跃", "欢欣鼓舞"],
            "激动": ["激动", "兴奋", "振奋", "亢奋", "热血沸腾"],
            "狂喜": ["狂喜", "狂欢", "狂热", "极度兴奋", "欣喜若狂"],
            "满足": ["满足", "满意", "称心", "如意", "心满意足"],
            "感激": ["感激", "感谢", "谢谢", "感恩", "感动", "谢意"],
            "温暖": ["温暖", "温馨", "暖心", "贴心", "感人", "暖意"],
            "放松": ["放松", "轻松", "松弛", "自在", "休闲", "悠闲"],
            "宁静": ["宁静", "平静", "安宁", "祥和", "安详", "平和"],
            "中性": ["一般", "普通", "正常", "平常", "还好", "凑合"],
            "困惑": ["困惑", "迷惑", "疑惑", "不解", "不明白", "迷茫"],
            "担忧": ["担忧", "担心", "忧虑", "顾虑", "忧心", "发愁"],
            "焦虑": ["焦虑", "不安", "忧心忡忡", "提心吊胆", "坐立不安"],
            "紧张": ["紧张", "紧迫", "不自在", "拘谨", "局促", "慌张"],
            "恐惧": ["恐惧", "害怕", "惊恐", "惧怕", "畏惧", "恐慌"],
            "沮丧": ["沮丧", "消沉", "低落", "郁闷", "闷闷不乐", "情绪低落"],
            "悲伤": ["悲伤", "难过", "伤心", "哀伤", "凄凉", "忧伤"],
            "痛苦": ["痛苦", "煎熬", "折磨", "难受", "痛心", "心痛"],
            "绝望": ["绝望", "无望", "灰心", "丧气", "心灰意冷", "万念俱灰"],
            "愤怒": ["愤怒", "生气", "气愤", "恼怒", "发火", "暴怒"],
            "厌恶": ["厌恶", "讨厌", "反感", "嫌弃", "嫌恶", "恶心"],
            "鄙视": ["鄙视", "轻蔑", "看不起", "瞧不起", "蔑视", "不屑"],
            "嫉妒": ["嫉妒", "妒忌", "眼红", "吃醋", "不平"],
            "惊讶": ["惊讶", "惊讶", "吃惊", "诧异", "惊异", "震惊"]
        }
        
        # 确保情感数据目录存在
        if not os.path.exists(self.emotions_dir):
            try:
                os.makedirs(self.emotions_dir)
            except Exception as e:
                logger.error_status(f"创建情感数据目录失败: {e}")
        
        logger.success("情感智能模块初始化完成")
    
    def _load_emotion_levels_standard(self) -> List[Tuple]:
        """加载统一的情感等级标准"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/emotion_levels_standard.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            levels = []
            for level in config['emotion_levels_standard']['levels']:
                range_data = level['range']
                label = level['label']
                
                if len(range_data) == 2:
                    if range_data[1] == "infinity":
                        levels.append((range_data[0], float('inf'), label))
                    elif range_data[0] == range_data[1]:
                        levels.append((range_data[0], label))
                    else:
                        levels.append((range_data[0], range_data[1], label))
            
            logger.success("成功加载统一情感等级标准")
            return levels
            
        except Exception as e:
            logger.error(f"加载情感等级标准失败，使用默认配置: {e}")
            # 备用默认配置
            return [
                (-200, -100, "陌生人"),
                (-101, -1, "临时联系人"),
                (0, 0, "素不相识"),
                (1, 10, "几面之缘"),
                (11, 50, "点赞之交"),
                (51, 200, "泛泛之交"),
                (201, 500, "点头之交"),
                (501, 1000, "聊天之友"),
                (1001, 2500, "酒肉朋友"),
                (2501, 5000, "工作伙伴"),
                (5001, 10000, "志同道合"),
                (10001, 50000, "谈心密友"),
                (50001, 200000, "知心好友"),
                (200001, 500000, "挚友"),
                (500001, 5000000, "生死之交"),
                (5000001, float('inf'), "灵魂缔造者")
            ]
    
    def get_emotion_level(self, intensity: int) -> str:
        """
        获取情感强度对应的关系级别（统一标准）
        
        Args:
            intensity: 情感强度值
            
        Returns:
            关系级别描述
        """
        # 如果小于-200，自动归0
        if intensity < -200:
            intensity = 0
            
        for level in self.emotion_levels:
            if len(level) == 3 and level[0] <= intensity <= level[1]:
                return level[2]
            elif len(level) == 2 and intensity == level[0]:
                return level[1]
        return "未知"
    
    def get_emotion_state(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户当前情感状态的详细信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            包含情感状态详细信息的字典
        """
        emotion, intensity = self.get_emotion(user_id)
        
        # 获取情感类型的详细信息
        emotion_info = self.emotion_types.get(emotion, {
            "value": 0, 
            "category": "中性", 
            "intensity": "无"
        })
        
        # 获取关系级别
        relationship_level = self.get_relationship_level(user_id)
        
        # 构建情感状态信息
        emotion_state = {
            "emotion": emotion,
            "intensity": intensity,
            "category": emotion_info["category"],
            "intensity_level": emotion_info["intensity"],
            "value": emotion_info["value"],
            "relationship_level": relationship_level,
            "timestamp": time.time()
        }
        
        return emotion_state
    
    def get_emotion(self, user_id: str) -> Tuple[str, int]:
        """
        获取用户当前情感状态
        
        Args:
            user_id: 用户ID
            
        Returns:
            情感类型和强度的元组
        """
        # 检查缓存
        if user_id in self.emotions_cache:
            cache_time = self.emotions_cache_time.get(user_id, 0)
            if time.time() - cache_time < self.emotions_cache_expiry:
                logger.debug(f"使用缓存的情感数据: {user_id}")
                return self.emotions_cache[user_id]
        
        # 从文件加载
        emotion_data = self._load_emotion_data(user_id)
        
        if emotion_data and "current" in emotion_data:
            emotion = emotion_data["current"]["emotion"]
            intensity = emotion_data["current"]["intensity"]
        else:
            # 默认情感状态
            emotion = "中性"
            intensity = 0
            
            # 创建新的情感数据
            emotion_data = {
                "user_id": user_id,
                "current": {
                    "emotion": emotion,
                    "intensity": intensity,
                    "updated_at": datetime.datetime.now().isoformat()
                },
                "history": []
            }
            self._save_emotion_data(user_id, emotion_data)
        
        # 更新缓存
        self.emotions_cache[user_id] = (emotion, intensity)
        self.emotions_cache_time[user_id] = time.time()
        
        return emotion, intensity
    
    def _load_emotion_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """从文件加载情感数据"""
        emotion_file = os.path.join(self.emotions_dir, f"{user_id}.json")
        
        if os.path.exists(emotion_file):
            try:
                with open(emotion_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error_status(f"加载情感数据失败: {e}")
                return None
        else:
            logger.debug(f"情感数据文件不存在: {emotion_file}")
            return None
    
    def _save_emotion_data(self, user_id: str, emotion_data: Dict[str, Any]) -> bool:
        """保存情感数据到文件"""
        emotion_file = os.path.join(self.emotions_dir, f"{user_id}.json")
        
        try:
            with open(emotion_file, 'w', encoding='utf-8') as f:
                json.dump(emotion_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error_status(f"保存情感数据失败: {e}")
            return False
    
    def set_emotion(self, user_id: str, emotion: str, intensity: int) -> bool:
        """
        设置用户情感状态
        
        Args:
            user_id: 用户ID
            emotion: 情感类型
            intensity: 情感强度
            
        Returns:
            是否设置成功
        """
        # 获取当前情感数据
        emotion_data = self._load_emotion_data(user_id)
        
        current_time = datetime.datetime.now().isoformat()
        
        if not emotion_data:
            # 创建新的情感数据
            emotion_data = {
                "user_id": user_id,
                "current": {
                    "emotion": emotion,
                    "intensity": intensity,
                    "updated_at": current_time
                },
                "history": []
            }
        else:
            # 如果有历史记录，将当前情感加入历史
            if "current" in emotion_data:
                current = emotion_data["current"]
                if "history" not in emotion_data:
                    emotion_data["history"] = []
                
                # 只有当情感或强度变化时才记录历史
                if current["emotion"] != emotion or current["intensity"] != intensity:
                    emotion_data["history"].append({
                        "emotion": current["emotion"],
                        "intensity": current["intensity"],
                        "timestamp": current["updated_at"]
                    })
                    
                    # 限制历史记录长度
                    max_history = self.config.get('max_emotion_history', 100)
                    if len(emotion_data["history"]) > max_history:
                        emotion_data["history"] = emotion_data["history"][-max_history:]
            
            # 更新当前情感
            emotion_data["current"] = {
                "emotion": emotion,
                "intensity": intensity,
                "updated_at": current_time
            }
        
        # 保存到文件
        success = self._save_emotion_data(user_id, emotion_data)
        
        # 更新缓存
        if success:
            self.emotions_cache[user_id] = (emotion, intensity)
            self.emotions_cache_time[user_id] = time.time()
        
        return success
    
    def update_emotion(self, user_id: str, emotion_change: str, intensity_change: int = 0) -> Tuple[str, int]:
        """
        更新用户情感状态
        
        Args:
            user_id: 用户ID
            emotion_change: 情感变化类型
            intensity_change: 情感强度变化值
            
        Returns:
            更新后的情感类型和强度
        """
        # 获取当前情感状态
        current_emotion, current_intensity = self.get_emotion(user_id)
        
        # 如果是情感类型变化
        if emotion_change != current_emotion:
            # 获取新情感类型的基础值
            emotion_info = self.emotion_types.get(emotion_change, {"value": 0, "category": "中性", "intensity": "无"})
            new_emotion = emotion_change
            
            # 如果是相同类别的情感，保留一部分强度
            if emotion_info["category"] == self.emotion_types.get(current_emotion, {"category": "中性"})["category"]:
                # 保留30%的原强度
                base_intensity = current_intensity * 0.3
            else:
                # 不同类别的情感，重置强度
                base_intensity = 0
            
            # 计算新的强度值
            new_intensity = int(base_intensity) + emotion_info["value"] + intensity_change
        else:
            # 仅强度变化
            new_emotion = current_emotion
            new_intensity = current_intensity + intensity_change
        
        # 设置新的情感状态
        self.set_emotion(user_id, new_emotion, new_intensity)
        
        return new_emotion, new_intensity
    
    def analyze_message_emotion(self, message: str) -> Tuple[str, int]:
        """
        分析消息的情感
        
        Args:
            message: 消息文本
            
        Returns:
            情感类型和强度的元组
        """
        # 统计各情感类型的关键词出现次数
        emotion_scores = {}
        for emotion, keywords in self.emotion_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in message:
                    score += 1
            if score > 0:
                emotion_scores[emotion] = score
        
        # 如果没有检测到明显情感，返回中性
        if not emotion_scores:
            return "中性", 0
        
        # 找出得分最高的情感
        max_emotion = max(emotion_scores.items(), key=lambda x: x[1])[0]
        
        # 获取情感基础值
        emotion_value = self.emotion_types.get(max_emotion, {"value": 0})["value"]
        
        # 根据关键词出现次数调整强度
        intensity = emotion_value * (1 + 0.2 * emotion_scores[max_emotion])
        
        return max_emotion, int(intensity)
    
    def process_user_message(self, user_id: str, message: str) -> Tuple[str, int]:
        """
        处理用户消息并更新情感状态
        
        Args:
            user_id: 用户ID
            message: 用户消息
            
        Returns:
            更新后的情感类型和强度
        """
        # 分析消息情感
        emotion, intensity = self.analyze_message_emotion(message)
        
        # 更新用户情感
        return self.update_emotion(user_id, emotion, intensity)
    
    def get_emotion_history(self, user_id: str, count: int = 10) -> List[Dict[str, Any]]:
        """
        获取用户情感历史记录
        
        Args:
            user_id: 用户ID
            count: 返回的历史记录数量
            
        Returns:
            情感历史记录列表
        """
        emotion_data = self._load_emotion_data(user_id)
        
        if emotion_data and "history" in emotion_data:
            # 返回最近的n条记录
            return emotion_data["history"][-count:]
        else:
            return []
    
    def get_relationship_level(self, user_id: str) -> str:
        """
        获取与用户的关系级别
        
        Args:
            user_id: 用户ID
            
        Returns:
            关系级别描述
        """
        _, intensity = self.get_emotion(user_id)
        return self.get_emotion_level(intensity)
    
    def adjust_emotion_based_on_frequency(self, user_id: str, last_interaction: float) -> Tuple[str, int]:
        """
        根据交互频率调整情感
        
        Args:
            user_id: 用户ID
            last_interaction: 上次交互的时间戳
            
        Returns:
            调整后的情感类型和强度
        """
        current_time = time.time()
        elapsed_time = current_time - last_interaction
        
        # 如果超过24小时未互动
        if elapsed_time > 86400:
            current_emotion, current_intensity = self.get_emotion(user_id)
            
            # 逐渐减弱情感强度
            if current_intensity <= -100:
                new_intensity = 0
                new_emotion = "中性"
            else:
                days_passed = int(elapsed_time / 86400)
                decay_rate = min(days_passed, 10)  # 最多降低10点
                new_intensity = current_intensity - decay_rate
                
                # 如果强度接近0，设为中性
                if abs(new_intensity) < 5:
                    new_emotion = "中性"
                    new_intensity = 0
                else:
                    new_emotion = current_emotion
            
            # 更新情感状态
            self.set_emotion(user_id, new_emotion, new_intensity)
            return new_emotion, new_intensity
        
        # 如果互动频率正常，不调整
        return self.get_emotion(user_id)
    
    def generate_emotional_response(self, user_id: str, message: str) -> Dict[str, Any]:
        """
        生成情感化的响应建议
        
        Args:
            user_id: 用户ID
            message: 用户消息
            
        Returns:
            情感响应建议
        """
        # 获取当前情感状态
        current_emotion, current_intensity = self.get_emotion(user_id)
        
        # 分析消息情感
        message_emotion, message_intensity = self.analyze_message_emotion(message)
        
        # 获取关系级别
        relationship_level = self.get_relationship_level(user_id)
        
        # 判断情感一致性
        if message_emotion == current_emotion:
            emotion_consistency = "一致"
        elif (self.emotion_types.get(message_emotion, {"category": "中性"})["category"] == 
              self.emotion_types.get(current_emotion, {"category": "中性"})["category"]):
            emotion_consistency = "相似"
        else:
            emotion_consistency = "不一致"
        
        # 判断应该的情感响应
        if emotion_consistency == "一致":
            # 情感一致，强化情感
            response_emotion = message_emotion
            response_intensity = "增强"
        elif emotion_consistency == "相似":
            # 情感相似，保持情感
            response_emotion = message_emotion
            response_intensity = "保持"
        else:
            # 情感不一致，根据关系级别决定
            if current_intensity > 1000:  # 关系较好
                # 尝试调和情感
                response_emotion = message_emotion
                response_intensity = "温和"
            else:
                # 保持自己的情感
                response_emotion = current_emotion
                response_intensity = "保持"
        
        # 生成情感关键词
        emotion_keywords = self.emotion_keywords.get(response_emotion, [])
        suggested_keywords = emotion_keywords[:3] if emotion_keywords else []
        
        return {
            "user_emotion": message_emotion,
            "user_emotion_intensity": message_intensity,
            "relationship_level": relationship_level,
            "current_emotion": current_emotion,
            "current_intensity": current_intensity,
            "suggested_response_emotion": response_emotion,
            "suggested_response_intensity": response_intensity,
            "suggested_keywords": suggested_keywords,
            "emotion_consistency": emotion_consistency
        }
    
    def clear_cache(self, user_id: str = None):
        """
        清除缓存
        
        Args:
            user_id: 用户ID，如果提供则只清除该用户的缓存
        """
        if user_id:
            # 清除特定用户的缓存
            if user_id in self.emotions_cache:
                del self.emotions_cache[user_id]
            if user_id in self.emotions_cache_time:
                del self.emotions_cache_time[user_id]
            logger.info(f"已清除用户 {user_id} 的情感缓存")
        else:
            # 清除所有缓存
            self.emotions_cache.clear()
            self.emotions_cache_time.clear()
            logger.info("已清除所有情感缓存")
    
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化或重新初始化模块
        
        Args:
            config: 新的配置信息
            
        Returns:
            初始化是否成功
        """
        if config:
            self.config = config
            
            # 更新配置项
            self.emotions_dir = self.config.get('emotions_dir', self.emotions_dir)
            self.emotions_cache_expiry = self.config.get('emotions_cache_expiry', self.emotions_cache_expiry)
            
            # 确保情感数据目录存在
            if not os.path.exists(self.emotions_dir):
                try:
                    os.makedirs(self.emotions_dir)
                except Exception as e:
                    logger.error_status(f"创建情感数据目录失败: {e}")
                    return False
            
            logger.success("情感智能模块重新初始化完成")
            
        return True
    
    def shutdown(self) -> bool:
        """
        关闭模块，执行必要的清理工作
        
        Returns:
            关闭是否成功
        """
        # 清空缓存
        self.emotions_cache.clear()
        self.emotions_cache_time.clear()
        
        logger.info("情感智能模块已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> EmotionalIntelligence:
    """
    获取情感智能模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        情感智能模块实例
    """
    return EmotionalIntelligence(config) 