#!/usr/bin/env python3
"""
情感系统集成模块 - Emotion System Integration

将新的自主情感管理器集成到现有的数字生命系统中，
实现统一的情感等级标准和智能的情感更新逻辑。

作者: AI助手 & 用户协作
创建日期: 2025-01-19
版本: 1.0
"""

import os
import sys
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# 添加项目根目录到系统路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

from utilities.unified_logger import get_unified_logger, setup_unified_logging
from cognitive_modules.emotion.autonomous_emotion_manager import get_autonomous_emotion_manager
from connectors.database.mysql_connector import MySQLConnector

# 设置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

class EmotionSystemIntegration:
    """情感系统集成器"""
    
    def __init__(self, mysql_connector: MySQLConnector = None):
        """初始化情感系统集成器"""
        self.mysql_connector = mysql_connector
        self.autonomous_emotion_manager = get_autonomous_emotion_manager(mysql_connector)
        
        # 兼容性映射：将旧的情感系统调用映射到新系统
        self.compatibility_mapping = {
            "get_emotion": self._get_emotion_compatible,
            "set_emotion": self._set_emotion_compatible,
            "update_emotion": self._update_emotion_compatible,
            "process_user_message": self._process_user_message_compatible,
            "adjust_emotion_based_on_frequency": self._adjust_emotion_based_on_frequency_compatible,
            "get_emotion_level": self._get_emotion_level_compatible
        }
        
        logger.success("情感系统集成器初始化完成")
    
    def get_emotion_level(self, intensity: int) -> str:
        """获取情感等级（统一标准）"""
        return self.autonomous_emotion_manager.get_emotion_level(intensity)
    
    def process_user_message(self, user_id: str, message: str) -> Dict[str, Any]:
        """
        处理用户消息并更新情感（新接口）
        
        这是主要的情感处理接口，使用自主决策能力
        """
        try:
            decision = self.autonomous_emotion_manager.process_user_message_with_autonomous_decision(
                user_id, message
            )
            
            return {
                "success": True,
                "emotion": decision.new_emotion,
                "intensity": decision.new_intensity,
                "level": self.get_emotion_level(decision.new_intensity),
                "reasoning": decision.reasoning,
                "confidence": decision.confidence,
                "factors_considered": decision.factors_considered
            }
            
        except Exception as e:
            logger.error_status(f"情感处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "emotion": "中性",
                "intensity": 0,
                "level": "素不相识"
            }
    
    def adjust_emotion_based_on_frequency(self, user_id: str) -> Dict[str, Any]:
        """
        基于交互频率调整情感（新接口）
        """
        try:
            decision = self.autonomous_emotion_manager.adjust_emotion_based_on_frequency_autonomous(user_id)
            
            return {
                "success": True,
                "emotion": decision.new_emotion,
                "intensity": decision.new_intensity,
                "level": self.get_emotion_level(decision.new_intensity),
                "reasoning": decision.reasoning,
                "confidence": decision.confidence
            }
            
        except Exception as e:
            logger.error_status(f"频率调整失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_current_emotion_state(self, user_id: str) -> Dict[str, Any]:
        """
        获取当前情感状态（新接口）
        """
        try:
            current_emotion, current_intensity = self.autonomous_emotion_manager._get_current_emotion(user_id)
            relationship_context = self.autonomous_emotion_manager._get_relationship_context(user_id)
            
            return {
                "success": True,
                "emotion": current_emotion,
                "intensity": current_intensity,
                "level": self.get_emotion_level(current_intensity),
                "relationship_context": relationship_context
            }
            
        except Exception as e:
            logger.error_status(f"获取情感状态失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "emotion": "中性",
                "intensity": 0,
                "level": "素不相识"
            }
    
    def migrate_old_emotions_data(self) -> Dict[str, Any]:
        """
        迁移旧的emotions表数据到新的ai_user_relationships表
        """
        if not self.mysql_connector:
            return {"success": False, "error": "无数据库连接"}
        
        try:
            # 检查旧emotions表是否存在
            check_old_table = """
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = DATABASE() AND table_name = 'emotions'
            """
            result = self.mysql_connector.query(check_old_table)
            
            if not result or result[0][0] == 0:
                return {"success": True, "message": "旧emotions表不存在，无需迁移"}
            
            # 获取旧数据
            get_old_data = """
                SELECT user_id, emotion, intensity, created_at
                FROM emotions
                ORDER BY user_id, created_at DESC
            """
            old_data = self.mysql_connector.query(get_old_data)
            
            if not old_data:
                return {"success": True, "message": "旧emotions表无数据"}
            
            # 按用户分组，取最新的情感数据
            user_emotions = {}
            for row in old_data:
                user_id, emotion, intensity, created_at = row
                if user_id not in user_emotions:
                    user_emotions[user_id] = {
                        "emotion": emotion,
                        "intensity": intensity,
                        "created_at": created_at
                    }
            
            # 迁移到新表
            migrated_count = 0
            for user_id, emotion_data in user_emotions.items():
                try:
                    # 检查新表中是否已存在
                    check_new = """
                        SELECT id FROM ai_user_relationships 
                        WHERE user_id = %s AND ai_id = 'yanran'
                    """
                    existing = self.mysql_connector.query(check_new, (user_id,))
                    
                    if existing and existing[0]:
                        # 更新现有记录
                        update_query = """
                            UPDATE ai_user_relationships 
                            SET emotional_state = JSON_SET(
                                COALESCE(emotional_state, '{}'),
                                '$.migrated_emotion', %s,
                                '$.migrated_intensity', %s,
                                '$.migrated_at', %s
                            )
                            WHERE user_id = %s AND ai_id = 'yanran'
                        """
                        success, result, error = self.mysql_connector.execute_update(update_query, (
                            emotion_data["emotion"],
                            emotion_data["intensity"],
                            datetime.now().isoformat(),
                            user_id
                        ))
                    else:
                        # 插入新记录
                        emotional_weight = min(1.0, max(0.0, (emotion_data["intensity"] + 200) / 5200))
                        relationship_type = self._map_intensity_to_type(emotion_data["intensity"])
                        
                        emotional_state = json.dumps({
                            "current_emotion": emotion_data["emotion"],
                            "intensity": emotion_data["intensity"],
                            "migrated_from_old_system": True,
                            "migrated_at": datetime.now().isoformat(),
                            "level": self.get_emotion_level(emotion_data["intensity"])
                        })
                        
                        insert_query = """
                            INSERT INTO ai_user_relationships (
                                user_id, ai_id, relationship_type, emotional_weight,
                                trust_level, intimacy_level, interaction_count,
                                last_interaction_time, emotional_state, created_at
                            ) VALUES (%s, 'yanran', %s, %s, %s, %s, 1, %s, %s, %s)
                        """
                        
                        success, result, error = self.mysql_connector.execute_update(insert_query, (
                            user_id, relationship_type, emotional_weight,
                            emotional_weight * 0.8, emotional_weight * 0.6,
                            emotion_data["created_at"], emotional_state,
                            emotion_data["created_at"]
                        ))
                    
                    migrated_count += 1
                    
                except Exception as e:
                    logger.warning_status(f"迁移用户{user_id}的数据失败: {e}")
            
            return {
                "success": True,
                "message": f"成功迁移{migrated_count}个用户的情感数据",
                "migrated_count": migrated_count,
                "total_users": len(user_emotions)
            }
            
        except Exception as e:
            logger.error_status(f"数据迁移失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _map_intensity_to_type(self, intensity: int) -> str:
        """将强度映射到关系类型"""
        return self.autonomous_emotion_manager._map_level_to_type(intensity)
    
    # 兼容性方法：保持与旧系统的接口兼容
    def _get_emotion_compatible(self, user_id: str) -> Tuple[str, int]:
        """兼容旧的get_emotion方法"""
        return self.autonomous_emotion_manager._get_current_emotion(user_id)
    
    def _set_emotion_compatible(self, user_id: str, emotion: str, intensity: int):
        """兼容旧的set_emotion方法"""
        try:
            # 创建一个模拟的决策对象
            from cognitive_modules.emotion.autonomous_emotion_manager import EmotionDecision
            
            decision = EmotionDecision(
                new_emotion=emotion,
                new_intensity=intensity,
                reasoning="兼容性设置",
                confidence=1.0,
                factors_considered=["手动设置"]
            )
            
            self.autonomous_emotion_manager._execute_emotion_update(user_id, decision)
            
        except Exception as e:
            logger.error_status(f"兼容性设置情感失败: {e}")
    
    def _update_emotion_compatible(self, user_id: str, emotion: str, intensity: int):
        """兼容旧的update_emotion方法"""
        self._set_emotion_compatible(user_id, emotion, intensity)
    
    def _process_user_message_compatible(self, user_id: str, message: str):
        """兼容旧的process_user_message方法"""
        result = self.process_user_message(user_id, message)
        if result["success"]:
            # 更新情感状态
            self._set_emotion_compatible(user_id, result["emotion"], result["intensity"])
    
    def _adjust_emotion_based_on_frequency_compatible(self, user_id: str, last_interaction):
        """兼容旧的adjust_emotion_based_on_frequency方法"""
        return self.adjust_emotion_based_on_frequency(user_id)
    
    def _get_emotion_level_compatible(self, intensity: int) -> str:
        """兼容旧的get_emotion_level方法"""
        return self.get_emotion_level(intensity)
    
    def create_emotion_system_wrapper(self):
        """
        创建情感系统包装器，用于替换旧的EmotionSystem类
        """
        class EmotionSystemWrapper:
            def __init__(self, integration_instance):
                self.integration = integration_instance
                
                # 复制统一的情感等级标准
                self.emotion_levels = self.integration.autonomous_emotion_manager.emotion_levels
            
            def get_emotion_level(self, intensity):
                return self.integration.get_emotion_level(intensity)
            
            def get_emotion(self, user_id):
                return self.integration._get_emotion_compatible(user_id)
            
            def set_emotion(self, user_id, emotion, intensity):
                return self.integration._set_emotion_compatible(user_id, emotion, intensity)
            
            def update_emotion(self, user_id, emotion, intensity):
                return self.integration._update_emotion_compatible(user_id, emotion, intensity)
            
            def process_user_message(self, user_id, message):
                return self.integration._process_user_message_compatible(user_id, message)
            
            def adjust_emotion_based_on_frequency(self, user_id, last_interaction):
                return self.integration._adjust_emotion_based_on_frequency_compatible(
                    user_id, last_interaction
                )
        
        return EmotionSystemWrapper(self)
    
    def generate_integration_report(self) -> Dict[str, Any]:
        """生成集成报告"""
        report = {
            "integration_status": "completed",
            "timestamp": datetime.now().isoformat(),
            "features": {
                "unified_emotion_levels": True,
                "autonomous_decision_making": True,
                "database_integration": self.mysql_connector is not None,
                "backward_compatibility": True,
                "frequency_based_adjustment": True,
                "data_migration_support": True
            },
            "emotion_levels_count": len(self.autonomous_emotion_manager.emotion_levels),
            "supported_operations": [
                "process_user_message",
                "adjust_emotion_based_on_frequency", 
                "get_current_emotion_state",
                "migrate_old_emotions_data",
                "get_emotion_level"
            ],
            "compatibility_methods": list(self.compatibility_mapping.keys())
        }
        
        return report


# 全局单例实例
_emotion_system_integration_instance = None

def get_emotion_system_integration(mysql_connector: MySQLConnector = None) -> EmotionSystemIntegration:
    """获取情感系统集成器实例"""
    global _emotion_system_integration_instance
    if _emotion_system_integration_instance is None:
        _emotion_system_integration_instance = EmotionSystemIntegration(mysql_connector)
    return _emotion_system_integration_instance

def create_unified_emotion_system(mysql_connector: MySQLConnector = None):
    """
    创建统一的情感系统
    
    这个函数返回一个兼容旧接口的情感系统实例，
    但内部使用新的自主决策逻辑
    """
    integration = get_emotion_system_integration(mysql_connector)
    return integration.create_emotion_system_wrapper() 