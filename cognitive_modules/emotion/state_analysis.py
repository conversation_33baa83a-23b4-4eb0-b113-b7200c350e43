"""
情感状态分析模块 - Emotion State Analysis

该模块负责分析当前的情感状态。
"""

from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random
import time
from typing import Dict, Any, List

logger = get_unified_logger("cognitive_modules.emotion.state_analysis")

# 情感维度
EMOTION_DIMENSIONS = {
    "valence": (-1.0, 1.0),  # 情感价 (负面-正面)
    "arousal": (0.0, 1.0),   # 唤醒度 (平静-激动)
    "dominance": (0.0, 1.0)  # 支配度 (顺从-主导)
}

# 基本情感
BASIC_EMOTIONS = [
    "joy",          # 喜悦
    "sadness",      # 悲伤
    "anger",        # 愤怒
    "fear",         # 恐惧
    "disgust",      # 厌恶
    "surprise",     # 惊讶
    "trust",        # 信任
    "anticipation"  # 期待
]

# 情感关键词映射
EMOTION_KEYWORDS = {
    "joy": ["高兴", "快乐", "开心", "喜悦", "欢喜", "愉快", "欣喜", "兴奋", "满意"],
    "sadness": ["悲伤", "难过", "伤心", "痛苦", "忧郁", "失落", "沮丧", "消沉", "哀伤"],
    "anger": ["生气", "愤怒", "恼火", "烦躁", "激怒", "恼怒", "暴怒", "气愤", "不满"],
    "fear": ["害怕", "恐惧", "惊恐", "担忧", "忧虑", "焦虑", "紧张", "惊慌", "惶恐"],
    "disgust": ["厌恶", "讨厌", "反感", "恶心", "嫌弃", "憎恶", "鄙视", "轻蔑", "排斥"],
    "surprise": ["惊讶", "震惊", "吃惊", "诧异", "意外", "惊奇", "惊异", "惊愕", "惊骇"],
    "trust": ["信任", "相信", "依赖", "依靠", "信赖", "信心", "确信", "放心", "安心"],
    "anticipation": ["期待", "盼望", "期盼", "憧憬", "企盼", "希望", "展望", "等待", "预期"]
}

def process(context):
    """
    处理函数
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.info("执行情感状态分析处理")
    
    # 获取输入数据
    input_data = context.get("input_data", {})
    text = input_data.get("text", "")
    user_id = input_data.get("user_id", "default")
    
    # 获取共享数据
    shared_data = context.get("shared_data", {})
    intent = shared_data.get("user_intent", "")
    
    # 分析情感状态
    emotion_state = _analyze_emotion(text, intent)
    
    # 获取情感变化趋势
    emotion_trend = _get_emotion_trend(emotion_state)
    
    result = {
        "emotion_state": emotion_state,
        "emotion_trend": emotion_trend,
        "input_text": text
    }
    
    logger.info(f"情感分析结果: {emotion_state['dominant_emotion']}, 强度: {emotion_state['intensity']:.2f}")
    
    return result

def _analyze_emotion(text, intent=None):
    """
    分析文本情感
    
    Args:
        text: 输入文本
        intent: 用户意图
        
    Returns:
        情感状态字典
    """
    # 初始化情感评分
    emotion_scores = {emotion: 0.0 for emotion in BASIC_EMOTIONS}
    
    # 基于关键词的简单情感分析
    text_lower = text.lower()
    for emotion, keywords in EMOTION_KEYWORDS.items():
        for keyword in keywords:
            if keyword in text_lower:
                emotion_scores[emotion] += 0.3
    
    # 基于意图的情感调整
    if intent == "greeting":
        emotion_scores["joy"] += 0.2
        emotion_scores["trust"] += 0.2
    elif intent == "farewell":
        emotion_scores["sadness"] += 0.1
        emotion_scores["anticipation"] += 0.1
    elif intent == "query_identity":
        emotion_scores["trust"] += 0.1
        emotion_scores["anticipation"] += 0.1
    elif intent == "criticism":
        emotion_scores["sadness"] += 0.2
        emotion_scores["fear"] += 0.1
    elif intent == "compliment":
        emotion_scores["joy"] += 0.3
        emotion_scores["trust"] += 0.2
    
    # 找出主导情感
    dominant_emotion = max(emotion_scores, key=emotion_scores.get)
    intensity = emotion_scores[dominant_emotion]
    
    # 如果强度太低，默认为中性
    if intensity < 0.2:
        dominant_emotion = "neutral"
        intensity = 0.1
        # 为中性情感设置默认VAD值
        emotion_scores["neutral"] = 0.1
    
    # 计算VAD值（Valence-Arousal-Dominance）
    vad = _calculate_vad(emotion_scores)
    
    return {
        "dominant_emotion": dominant_emotion,
        "intensity": intensity,
        "emotion_scores": emotion_scores,
        "valence": vad["valence"],
        "arousal": vad["arousal"],
        "dominance": vad["dominance"],
        "timestamp": time.time()
    }

def _calculate_vad(emotion_scores):
    """
    根据情感评分计算VAD值
    
    Args:
        emotion_scores: 情感评分字典
        
    Returns:
        VAD值字典
    """
    # 简化的VAD映射
    emotion_to_vad = {
        "joy": (0.8, 0.7, 0.7),          # 正面，中等唤醒，中等支配
        "sadness": (-0.7, 0.3, 0.2),     # 负面，低唤醒，低支配
        "anger": (-0.6, 0.8, 0.8),       # 负面，高唤醒，高支配
        "fear": (-0.8, 0.7, 0.2),        # 负面，高唤醒，低支配
        "disgust": (-0.7, 0.5, 0.4),     # 负面，中等唤醒，中等支配
        "surprise": (0.1, 0.8, 0.4),     # 中性，高唤醒，中等支配
        "trust": (0.6, 0.3, 0.5),        # 正面，低唤醒，中等支配
        "anticipation": (0.5, 0.6, 0.5)   # 正面，中等唤醒，中等支配
    }
    
    # 初始化VAD值
    valence = 0.0
    arousal = 0.0
    dominance = 0.0
    total_weight = 0.0
    
    # 加权平均计算VAD
    for emotion, score in emotion_scores.items():
        if score > 0:
            v, a, d = emotion_to_vad.get(emotion, (0, 0, 0))
            valence += v * score
            arousal += a * score
            dominance += d * score
            total_weight += score
    
    # 归一化
    if total_weight > 0:
        valence /= total_weight
        arousal /= total_weight
        dominance /= total_weight
    
    return {
        "valence": valence,
        "arousal": arousal,
        "dominance": dominance
    }

def _get_emotion_trend(current_state):
    """
    获取情感变化趋势（模拟实现）
    
    Args:
        current_state: 当前情感状态
        
    Returns:
        情感趋势字典
    """
    # 在实际系统中，应该与历史情感状态比较
    # 这里只是简单模拟
    
    # 随机生成趋势（在真实系统中应该基于历史数据）
    stability = random.uniform(0.7, 0.9)
    trend_direction = random.choice(["stable", "increasing", "decreasing"])
    
    if current_state["dominant_emotion"] == "neutral":
        trend_direction = "stable"
        stability = 0.9
    
    return {
        "stability": stability,
        "direction": trend_direction,
        "period": "short_term"  # 短期趋势
    } 