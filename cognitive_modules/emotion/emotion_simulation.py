#!/usr/bin/env python3
"""
情感模拟模块 - Emotion Simulation Module

该模块实现了数字生命体的情感模拟功能，包括：
1. 基础情感状态生成与维护
2. 情感状态演变与转换
3. 情感表达与响应
4. 情感记忆与学习
5. 情感调节与管理

作者: Claude
创建日期: 2024-07-11
版本: 1.0
"""

import os
import sys
import json
import time
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, Set
from collections import defaultdict

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
modules_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.emotion.simulation")

class EmotionSimulation(CognitiveModuleBase):
    """
    情感模拟模块类
    
    实现数字生命体的情感状态生成、变化和表达，构建情感动态系统。
    """
    
    def __init__(self, module_id: str, module_name: str = "情感模拟", module_type: str = "emotion"):
        """
        初始化情感模拟模块
        
        Args:
            module_id: 模块ID
            module_name: 模块名称，默认为"情感模拟"
            module_type: 模块类型，默认为emotion
        """
        super().__init__(module_id, module_name, module_type)
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 情感状态
        self.emotion_state = {
            "current_emotion": "平静",  # 当前主导情感
            "emotion_intensity": 0.5,   # 情感强度(0-1)
            "emotion_valence": 0.0,     # 情感效价(-1到1，负面到正面)
            "emotion_arousal": 0.3,     # 情感唤醒度(0-1)
            "emotions": {},             # 各种情感及其强度
            "last_update": time.time()  # 上次更新时间
        }
        
        # 情感配置
        self.emotion_config = {
            "decay_rate": 0.02,         # 情感衰减率
            "trigger_threshold": 0.3,   # 情感触发阈值
            "expression_threshold": 0.4, # 情感表达阈值
            "max_intensity": 1.0,       # 最大情感强度
            "base_update_interval": 60,  # 基础更新间隔(秒)
            "saturation_factor": 0.8,   # 情感饱和度因子
            "inertia_factor": 0.3,      # 情感惯性因子
            "resonance_factor": 0.4,    # 情感共振因子
            "regulation_cooldown": 300, # 调节冷却时间(秒)
            "self_regulation_threshold": 0.7 # 自我调节阈值
        }
        
        # 情感动态历史
        self.emotion_history = {
            "last_dominant": "平静",      # 上一个主导情感
            "dominant_duration": 0,       # 主导情感持续时间(秒)
            "recent_emotions": [],        # 最近情感状态历史
            "intensity_trend": 0.0,       # 情感强度变化趋势
        }
        
        # 情感模型
        self.emotion_model = {
            # 基础情感及其属性
            "基础情感": {
                "喜悦": {"valence": 0.8, "arousal": 0.6, "decay": 0.01},
                "悲伤": {"valence": -0.7, "arousal": 0.3, "decay": 0.005},
                "愤怒": {"valence": -0.6, "arousal": 0.8, "decay": 0.02},
                "恐惧": {"valence": -0.8, "arousal": 0.7, "decay": 0.03},
                "厌恶": {"valence": -0.7, "arousal": 0.5, "decay": 0.01},
                "惊讶": {"valence": 0.2, "arousal": 0.9, "decay": 0.05},
                "期待": {"valence": 0.6, "arousal": 0.5, "decay": 0.008},
                "信任": {"valence": 0.7, "arousal": 0.3, "decay": 0.003},
                "平静": {"valence": 0.1, "arousal": 0.1, "decay": 0.001}
            },
            
            # 复合情感(由基础情感组合而成)
            "复合情感": {
                "爱": [("喜悦", 0.6), ("信任", 0.4)],
                "悔恨": [("悲伤", 0.5), ("厌恶", 0.5)],
                "失望": [("悲伤", 0.6), ("惊讶", 0.4)],
                "满足": [("喜悦", 0.7), ("平静", 0.3)],
                "焦虑": [("恐惧", 0.6), ("期待", 0.4)],
                "嫉妒": [("愤怒", 0.4), ("恐惧", 0.3), ("厌恶", 0.3)],
                "骄傲": [("喜悦", 0.5), ("期待", 0.5)]
            },
            
            # 情感转换关系(一种情感可能转变为另一种情感)
            "情感转换": {
                "惊讶": [("喜悦", 0.4), ("恐惧", 0.3), ("平静", 0.3)],
                "愤怒": [("厌恶", 0.3), ("平静", 0.7)],
                "恐惧": [("焦虑", 0.4), ("平静", 0.6)]
            }
        }
        
        # 情感记忆
        self.emotion_memory = []
        self.memory_capacity = 100
        
        # 情感调节策略
        self.regulation_strategies = {
            "重新评估": {"效果值": 0.7, "适用情感": ["恐惧", "悲伤", "愤怒"], "成功率": 0.8, "使用次数": 0, "最后使用时间": 0},
            "转移注意": {"效果值": 0.5, "适用情感": ["恐惧", "焦虑", "厌恶"], "成功率": 0.7, "使用次数": 0, "最后使用时间": 0},
            "表达抑制": {"效果值": 0.3, "适用情感": ["愤怒", "喜悦", "惊讶"], "成功率": 0.6, "使用次数": 0, "最后使用时间": 0},
            "问题解决": {"效果值": 0.8, "适用情感": ["恐惧", "焦虑", "愤怒"], "成功率": 0.7, "使用次数": 0, "最后使用时间": 0},
            "接受": {"效果值": 0.6, "适用情感": ["悲伤", "失望", "平静"], "成功率": 0.9, "使用次数": 0, "最后使用时间": 0},
            "寻求支持": {"效果值": 0.7, "适用情感": ["悲伤", "恐惧", "焦虑"], "成功率": 0.8, "使用次数": 0, "最后使用时间": 0}
        }
        
        # 情感调节历史
        self.regulation_history = []
        
        # 情感更新线程
        self.update_thread = None
        self.update_running = False
        
        logger.info(f"情感模拟模块 {module_id} 已创建")
    
    def _initialize_module(self) -> bool:
        """
        初始化模块内部实现
        
        Returns:
            初始化是否成功
        """
        try:
            # 加载情感状态和配置
            self._load_emotion_state()
            
            # 订阅相关事件
            self.event_bus.subscribe("perception_result", self._on_perception_result)
            self.event_bus.subscribe("user_interaction", self._on_user_interaction)
            self.event_bus.subscribe("environment_change", self._on_environment_change)
            self.event_bus.subscribe("goal_state_changed", self._on_goal_state_changed)
            
            # 新增：订阅认知模块事件
            self.event_bus.subscribe("thinking_process_started", self._on_thinking_process)
            self.event_bus.subscribe("decision_process_started", self._on_decision_process)
            
            # 启动情感更新线程
            self.update_running = True
            self.update_thread = threading.Thread(target=self._emotion_update_loop, daemon=True)
            self.update_thread.start()
            
            logger.success(f"情感模拟模块 {self.module_id} 初始化完成")
            return True
        except Exception as e:
            logger.error_status(f"情感模拟模块初始化失败: {str(e)}")
            return False
    
    def _shutdown_module(self) -> bool:
        """
        关闭模块的内部实现
        
        Returns:
            关闭是否成功
        """
        try:
            # 停止情感更新线程
            self.update_running = False
            if self.update_thread and self.update_thread.is_alive():
                self.update_thread.join(timeout=2.0)
            
            # 保存情感状态
            self._save_emotion_state()
            
            # 取消事件订阅
            self.event_bus.unsubscribe("perception_result", self._on_perception_result)
            self.event_bus.unsubscribe("user_interaction", self._on_user_interaction)
            self.event_bus.unsubscribe("environment_change", self._on_environment_change)
            self.event_bus.unsubscribe("goal_state_changed", self._on_goal_state_changed)
            
            # 新增：取消认知模块事件订阅
            self.event_bus.unsubscribe("thinking_process_started", self._on_thinking_process)
            self.event_bus.unsubscribe("decision_process_started", self._on_decision_process)
            
            logger.info(f"情感模拟模块 {self.module_id} 已关闭")
            return True
        except Exception as e:
            logger.error_status(f"关闭情感模拟模块失败: {str(e)}")
            return False
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据的内部实现
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        # 获取请求类型
        request_type = input_data.get("request_type", "")
        
        # 根据请求类型处理
        if request_type == "get_emotion_state":
            return {
                "success": True,
                "emotion_state": self.emotion_state
            }
        elif request_type == "update_emotion":
            emotion = input_data.get("emotion", "")
            intensity = input_data.get("intensity", 0.0)
            source = input_data.get("source", "external")
            
            if emotion:
                self._update_emotion(emotion, intensity, source)
                return {
                    "success": True,
                    "updated_emotion": emotion,
                    "updated_intensity": intensity
                }
            else:
                return {
                    "success": False,
                    "error": "缺少情感参数"
                }
        elif request_type == "regulate_emotion":
            strategy = input_data.get("strategy", "")
            target_emotion = input_data.get("target_emotion", "")
            
            if strategy:
                result = self._regulate_emotion(strategy, target_emotion)
                return {
                    "success": True,
                    "regulation_result": result
                }
            else:
                return {
                    "success": False,
                    "error": "缺少调节策略参数"
                }
        elif request_type == "get_cognitive_influence":
            # 新增：获取情感对认知的影响
            cognitive_influences = self.get_cognitive_influence()
            return {
                "success": True,
                "cognitive_influences": cognitive_influences
            }
        elif request_type == "get_regulation_history":
            # 新增：获取情感调节历史
            limit = input_data.get("limit", 5)
            history = self.get_regulation_history(limit)
            return {
                "success": True,
                "regulation_history": history
            }
        else:
            # 默认行为：返回当前情感状态
            return {
                "success": True,
                "emotion_state": self.emotion_state
            }
    
    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态的内部实现
        
        Args:
            context: 上下文信息
            
        Returns:
            更新是否成功
        """
        try:
            # 当前时间
            current_time = time.time()
            
            # 上次情感更新时间
            last_update = self.emotion_state["last_update"]
            
            # 如果距离上次更新超过阈值，进行自然衰减
            if current_time - last_update > self.emotion_config["base_update_interval"]:
                self._decay_emotions()
                self.emotion_state["last_update"] = current_time
            
            # 根据上下文调整情感状态
            if "current_state" in context:
                current_state = context["current_state"]
                
                # 根据活动类型调整情感
                activity = current_state.get("activity", "")
                if activity == "conversation":
                    # 对话活动可能增加社交情感
                    self._update_emotion("信任", 0.1, "context_activity")
                elif activity == "learning":
                    # 学习活动可能增加专注和好奇
                    self._update_emotion("期待", 0.1, "context_activity")
            
            # 保存情感状态
            self._save_emotion_state()
            
            return True
        except Exception as e:
            logger.error_status(f"更新情感模拟模块状态失败: {str(e)}")
            return False
    
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块状态的内部实现
        
        Returns:
            模块状态
        """
        return {
            "emotion_state": self.emotion_state,
            "regulation_strategies": self.regulation_strategies,
            "memory_size": len(self.emotion_memory)
        }
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        加载模块状态的内部实现
        
        Args:
            state: 模块状态
            
        Returns:
            加载是否成功
        """
        try:
            if "emotion_state" in state:
                self.emotion_state.update(state["emotion_state"])
            
            if "regulation_strategies" in state:
                self.regulation_strategies.update(state["regulation_strategies"])
            
            return True
        except Exception as e:
            logger.error_status(f"加载情感模拟模块状态失败: {str(e)}")
            return False
    
    def _update_emotion(self, emotion: str, intensity: float, source: str) -> bool:
        """
        更新特定情感的强度
        
        Args:
            emotion: 情感名称
            intensity: 情感强度增量(-1到1)
            source: 情感来源(事件类型)
            
        Returns:
            更新是否成功
        """
        try:
            # 检查是否为有效情感
            if emotion not in self.emotion_model["基础情感"] and emotion not in self.emotion_model["复合情感"]:
                logger.warning_status(f"未知情感类型: {emotion}")
                return False
            
            # 获取当前情感状态
            emotions = self.emotion_state.get("emotions", {})
            
            # 应用情感惯性效应
            # 如果是当前主导情感，更易被增强；如果是较弱情感，更难被增强
            if self.emotion_state["current_emotion"] == emotion:
                # 主导情感增强效应
                intensity = intensity * (1 + self.emotion_config["inertia_factor"])
            elif emotion in emotions and emotions[emotion] < 0.3:
                # 弱情感抑制效应
                intensity = intensity * (1 - self.emotion_config["inertia_factor"] * 0.5)
            
            # 应用情感饱和度效应
            # 随着情感强度增加，进一步增强变得更加困难
            current_intensity = emotions.get(emotion, 0.0)
            if current_intensity > 0.6 and intensity > 0:
                # 使用饱和度曲线调整增量
                saturation = 1.0 - (current_intensity - 0.6) * self.emotion_config["saturation_factor"]
                intensity = intensity * saturation
            
            # 计算新强度
            new_intensity = current_intensity + intensity
            
            # 限制情感强度在0-1范围内
            new_intensity = max(0.0, min(self.emotion_config["max_intensity"], new_intensity))
            
            # 更新情感字典
            emotions[emotion] = new_intensity
            
            # 更新情感状态
            self.emotion_state["emotions"] = emotions
            self.emotion_state["last_update"] = time.time()
            
            # 应用情感共振效应
            # 某些情感会自动激活或抑制其他相关情感
            self._apply_emotion_resonance(emotion, intensity, source)
            
            # 如果是复合情感，同时更新组成它的基础情感
            if emotion in self.emotion_model["复合情感"]:
                for base_emotion, ratio in self.emotion_model["复合情感"][emotion]:
                    # 避免重复调用_update_emotion造成无限递归
                    if base_emotion in emotions:
                        emotions[base_emotion] = min(
                            self.emotion_config["max_intensity"],
                            emotions.get(base_emotion, 0) + intensity * ratio
                        )
                    else:
                        emotions[base_emotion] = intensity * ratio
            
            # 更新情感历史
            self._update_emotion_history(emotion, new_intensity)
            
            # 重新计算主导情感
            self._recalculate_dominant_emotion()
            
            # 添加到情感记忆
            self._add_to_memory(emotion, new_intensity, source)
            
            # 触发情感变化事件
            self._publish_emotion_changed(emotion, new_intensity, source)
            
            return True
        except Exception as e:
            logger.error_status(f"更新情感状态失败: {str(e)}")
            return False
    
    def _decay_emotions(self) -> None:
        """
        情感自然衰减
        
        随时间减弱所有情感的强度，模拟情感随时间自然衰减的过程。
        考虑情感持续时间，长时间主导的情感衰减速度会变慢。
        """
        try:
            # 获取当前情感
            emotions = self.emotion_state.get("emotions", {}).copy()
            
            # 计算经过的时间(秒)
            elapsed_time = time.time() - self.emotion_state["last_update"]
            elapsed_minutes = elapsed_time / 60
            
            # 获取主导情感持续时间(分钟)
            dominant_duration_minutes = self.emotion_history["dominant_duration"] / 60
            
            # 对每种情感进行衰减
            for emotion, intensity in list(emotions.items()):
                # 获取情感衰减率
                decay_rate = self.emotion_config["decay_rate"]
                
                # 如果是基础情感，使用特定的衰减率
                if emotion in self.emotion_model["基础情感"]:
                    decay_rate = self.emotion_model["基础情感"][emotion]["decay"]
                
                # 调整主导情感的衰减率 - 持续时间越长，衰减越慢
                if emotion == self.emotion_state["current_emotion"] and dominant_duration_minutes > 5:
                    # 最多减少50%的衰减率
                    reduction_factor = min(0.5, dominant_duration_minutes / 60)
                    decay_rate = decay_rate * (1 - reduction_factor)
                
                # 计算衰减后的强度
                new_intensity = intensity - (decay_rate * elapsed_minutes)
                new_intensity = max(0.0, new_intensity)
                
                # 更新或移除情感
                if new_intensity > 0.01:
                    emotions[emotion] = new_intensity
                else:
                    emotions.pop(emotion, None)
            
            # 更新情感状态
            self.emotion_state["emotions"] = emotions
            self.emotion_state["last_update"] = time.time()
            
            # 重新计算主导情感
            self._recalculate_dominant_emotion()
            
        except Exception as e:
            logger.error_status(f"情感衰减处理失败: {str(e)}")
    
    def _recalculate_dominant_emotion(self) -> None:
        """
        重新计算主导情感
        
        基于当前各情感强度，确定主导情感并更新情感状态。
        """
        try:
            # 获取当前情感
            emotions = self.emotion_state.get("emotions", {})
            
            # 如果没有活跃情感，设置为平静
            if not emotions:
                self.emotion_state["current_emotion"] = "平静"
                self.emotion_state["emotion_intensity"] = 0.1
                self.emotion_state["emotion_valence"] = 0.1
                self.emotion_state["emotion_arousal"] = 0.1
                return
            
            # 找出强度最高的情感
            dominant_emotion = max(emotions.items(), key=lambda x: x[1])
            emotion_name, emotion_intensity = dominant_emotion
            
            # 更新主导情感
            self.emotion_state["current_emotion"] = emotion_name
            self.emotion_state["emotion_intensity"] = emotion_intensity
            
            # 更新情感效价和唤醒度
            if emotion_name in self.emotion_model["基础情感"]:
                self.emotion_state["emotion_valence"] = self.emotion_model["基础情感"][emotion_name]["valence"]
                self.emotion_state["emotion_arousal"] = self.emotion_model["基础情感"][emotion_name]["arousal"]
            else:
                # 复合情感的效价和唤醒度需要计算
                self._calculate_complex_emotion_attributes(emotion_name)
            
        except Exception as e:
            logger.error_status(f"重新计算主导情感失败: {str(e)}")
    
    def _calculate_complex_emotion_attributes(self, emotion: str) -> None:
        """
        计算复合情感的属性
        
        Args:
            emotion: 复合情感名称
        """
        if emotion not in self.emotion_model["复合情感"]:
            return
        
        # 初始化属性
        valence = 0.0
        arousal = 0.0
        total_ratio = 0.0
        
        # 计算加权平均值
        for base_emotion, ratio in self.emotion_model["复合情感"][emotion]:
            if base_emotion in self.emotion_model["基础情感"]:
                valence += self.emotion_model["基础情感"][base_emotion]["valence"] * ratio
                arousal += self.emotion_model["基础情感"][base_emotion]["arousal"] * ratio
                total_ratio += ratio
        
        # 更新情感状态
        if total_ratio > 0:
            self.emotion_state["emotion_valence"] = valence / total_ratio
            self.emotion_state["emotion_arousal"] = arousal / total_ratio
    
    def _regulate_emotion(self, strategy: str, target_emotion: str = None) -> Dict[str, Any]:
        """
        情感调节
        
        应用情感调节策略，调整情感状态。
        
        Args:
            strategy: 调节策略名称
            target_emotion: 目标情感(可选)
            
        Returns:
            调节结果
        """
        try:
            # 检查策略是否可用
            if strategy not in self.regulation_strategies:
                return {
                    "success": False,
                    "message": f"未知的调节策略: {strategy}"
                }
            
            # 获取策略信息
            strategy_info = self.regulation_strategies[strategy]
            strategy_effect = strategy_info["效果值"]
            
            # 检查冷却时间
            current_time = time.time()
            if current_time - strategy_info["最后使用时间"] < self.emotion_config["regulation_cooldown"]:
                # 策略仍在冷却中
                cooldown_left = self.emotion_config["regulation_cooldown"] - (current_time - strategy_info["最后使用时间"])
                return {
                    "success": False,
                    "message": f"调节策略 '{strategy}' 仍在冷却中 (剩余 {cooldown_left:.1f} 秒)",
                    "cooldown_left": cooldown_left
                }
            
            # 当前情感状态
            current_emotion = self.emotion_state["current_emotion"]
            current_intensity = self.emotion_state["emotion_intensity"]
            valence = self.emotion_state["emotion_valence"]
            
            # 评估策略适用性
            suitability_score = self._evaluate_regulation_suitability(strategy, current_emotion)
            
            # 调整效果基于适用性
            adjusted_effect = strategy_effect * suitability_score
            
            # 确定是否成功应用 - 基于成功率和适用性
            success_chance = strategy_info["成功率"] * suitability_score
            success = random.random() < success_chance
            
            # 更新策略使用信息
            strategy_info["使用次数"] += 1
            strategy_info["最后使用时间"] = current_time
            
            # 记录调节历史
            regulation_record = {
                "strategy": strategy,
                "target_emotion": target_emotion,
                "current_emotion": current_emotion,
                "intensity_before": current_intensity,
                "valence_before": valence,
                "timestamp": current_time,
                "success": success,
                "suitability": suitability_score
            }
            
            # 如果调节不成功，记录并返回
            if not success:
                self.regulation_history.append(regulation_record)
                return {
                    "success": True,  # 操作成功但调节效果不理想
                    "strategy": strategy,
                    "effect": f"尝试应用{strategy}，但效果不明显",
                    "intensity_change": 0
                }
            
            # 应用策略效果
            result = self._apply_regulation_strategy(strategy, adjusted_effect, target_emotion)
            
            # 更新调节记录
            regulation_record.update({
                "intensity_after": self.emotion_state["emotion_intensity"],
                "valence_after": self.emotion_state["emotion_valence"],
                "effect_description": result.get("effect", ""),
                "intensity_change": result.get("intensity_change", 0)
            })
            self.regulation_history.append(regulation_record)
            
            # 限制历史记录大小
            if len(self.regulation_history) > 20:
                self.regulation_history = self.regulation_history[-20:]
            
            # 返回结果
            return result
            
        except Exception as e:
            logger.error_status(f"情感调节失败: {str(e)}")
            return {
                "success": False,
                "message": f"情感调节过程出错: {str(e)}"
            }
    
    def _apply_regulation_strategy(self, strategy: str, effect_value: float, target_emotion: str = None) -> Dict[str, Any]:
        """
        应用具体的调节策略
        
        Args:
            strategy: 调节策略名称
            effect_value: 调整后的效果值
            target_emotion: 目标情感(可选)
            
        Returns:
            调节效果
        """
        # 当前情感状态
        current_emotion = self.emotion_state["current_emotion"]
        current_intensity = self.emotion_state["emotion_intensity"]
        valence = self.emotion_state["emotion_valence"]
        
        # 应用策略
        if strategy == "重新评估":
            # 认知重评会降低负面情感，提升正面情感
            if valence < 0:
                # 负面情感，减弱强度
                adjustment = -current_intensity * effect_value
                self._update_emotion(current_emotion, adjustment, "regulation_reappraisal")
                
                # 可能转换为其他情感
                if target_emotion:
                    self._update_emotion(target_emotion, effect_value * 0.5, "regulation_reappraisal")
            else:
                # 正面情感，增强强度
                adjustment = current_intensity * effect_value * 0.3
                self._update_emotion(current_emotion, adjustment, "regulation_reappraisal")
            
            return {
                "success": True,
                "strategy": strategy,
                "effect": "重新评估，调整情感认知框架",
                "intensity_change": self.emotion_state["emotion_intensity"] - current_intensity
            }
            
        elif strategy == "转移注意":
            # 注意力转移会减弱所有情感强度
            adjustment = -current_intensity * effect_value
            self._update_emotion(current_emotion, adjustment, "regulation_distraction")
            
            # 增加平静情感
            self._update_emotion("平静", effect_value * 0.7, "regulation_distraction")
            
            return {
                "success": True,
                "strategy": strategy,
                "effect": "转移注意力，减弱当前情感",
                "intensity_change": self.emotion_state["emotion_intensity"] - current_intensity
            }
            
        elif strategy == "表达抑制":
            # 表达抑制会临时减弱情感表达，但不改变内在情感
            # 仅影响表达阈值
            original_threshold = self.emotion_config["expression_threshold"]
            self.emotion_config["expression_threshold"] = original_threshold * (1 + effect_value)
            
            # 短期效果
            timer = threading.Timer(300, self._reset_expression_threshold, args=[original_threshold])
            timer.daemon = True
            timer.start()
            
            return {
                "success": True,
                "strategy": strategy,
                "effect": "抑制情感表达，提高表达阈值",
                "threshold_change": self.emotion_config["expression_threshold"] - original_threshold
            }
            
        elif strategy == "问题解决":
            # 问题解决策略减弱负面情感，增强积极情感
            if target_emotion:
                # 减弱当前情感
                self._update_emotion(current_emotion, -current_intensity * 0.5, "regulation_problem_solving")
                # 增强目标情感
                self._update_emotion(target_emotion, effect_value, "regulation_problem_solving")
            else:
                # 默认增强平静
                self._update_emotion(current_emotion, -current_intensity * 0.5, "regulation_problem_solving")
                self._update_emotion("平静", effect_value, "regulation_problem_solving")
            
            return {
                "success": True,
                "strategy": strategy,
                "effect": "问题解决，转向更积极的情感状态",
                "intensity_change": self.emotion_state["emotion_intensity"] - current_intensity
            }
            
        elif strategy == "接受":
            # 接受策略，不改变情感强度，但增加情感稳定性
            # 减缓情感衰减速度
            for emotion in self.emotion_state["emotions"]:
                if emotion in self.emotion_model["基础情感"]:
                    self.emotion_model["基础情感"][emotion]["decay"] *= 0.8
            
            # 增加情感适应能力
            self.emotion_config["trigger_threshold"] *= 1.1
            
            return {
                "success": True,
                "strategy": strategy,
                "effect": "接受当前情感，增加情感稳定性",
                "decay_reduction": "20%"
            }
            
        elif strategy == "寻求支持":
            # 寻求支持，增强正面情感，减弱负面情感
            if valence < 0:
                # 对负面情感，减弱并增加信任感
                self._update_emotion(current_emotion, -current_intensity * 0.4, "regulation_social_support")
                self._update_emotion("信任", effect_value, "regulation_social_support")
            else:
                # 对正面情感，增强
                self._update_emotion(current_emotion, current_intensity * 0.2, "regulation_social_support")
                self._update_emotion("信任", effect_value * 0.5, "regulation_social_support")
            
            return {
                "success": True,
                "strategy": strategy,
                "effect": "寻求支持，增强社会连接情感",
                "intensity_change": self.emotion_state["emotion_intensity"] - current_intensity
            }
        
        # 默认返回
        return {
            "success": True,
            "strategy": strategy,
            "effect": "应用情感调节策略",
            "intensity_change": 0
        }
    
    def _reset_expression_threshold(self, original_value: float) -> None:
        """
        重置情感表达阈值
        
        Args:
            original_value: 原始阈值
        """
        self.emotion_config["expression_threshold"] = original_value
        logger.debug("情感表达阈值已重置")
    
    def _add_to_memory(self, emotion: str, intensity: float, source: str) -> None:
        """
        添加情感记忆
        
        Args:
            emotion: 情感类型
            intensity: 情感强度
            source: 情感来源
        """
        try:
            # 创建记忆条目
            memory_entry = {
                "emotion": emotion,
                "intensity": intensity,
                "source": source,
                "timestamp": time.time(),
                "context": self._create_context_snapshot()
            }
            
            # 添加到记忆
            self.emotion_memory.append(memory_entry)
            
            # 限制记忆容量
            if len(self.emotion_memory) > self.memory_capacity:
                self.emotion_memory = self.emotion_memory[-self.memory_capacity:]
            
        except Exception as e:
            logger.error_status(f"添加情感记忆失败: {str(e)}")
    
    def _create_context_snapshot(self) -> Dict[str, Any]:
        """
        创建当前上下文的快照
        
        Returns:
            上下文快照
        """
        try:
            context = self.life_context.get_context()
            
            # 提取关键上下文信息，避免存储过大的数据
            snapshot = {}
            
            if isinstance(context, dict):
                if "current_state" in context:
                    snapshot["activity"] = context["current_state"].get("activity", "")
                
                if "user" in context:
                    snapshot["user_id"] = context["user"].get("user_id", "")
                
                if "goals" in context and isinstance(context["goals"], list) and len(context["goals"]) > 0:
                    active_goals = [g.get("content", "") for g in context["goals"] if g.get("status", "") == "active"]
                    snapshot["active_goals"] = active_goals[:3]  # 最多保存3个活跃目标
            
            return snapshot
            
        except Exception as e:
            logger.error_status(f"创建上下文快照失败: {str(e)}")
            return {}
    
    def _publish_emotion_changed(self, emotion: str, intensity: float, source: str) -> None:
        """
        发布情感变化事件
        
        Args:
            emotion: 情感类型
            intensity: 情感强度
            source: 情感来源
        """
        try:
            # 创建事件数据
            event_data = {
                "module_id": self.module_id,
                "emotion": emotion,
                "intensity": intensity,
                "previous_emotion": self.emotion_state.get("current_emotion"),
                "current_emotion": self.emotion_state.get("current_emotion"),
                "valence": self.emotion_state.get("emotion_valence"),
                "arousal": self.emotion_state.get("emotion_arousal"),
                "source": source,
                "timestamp": time.time()
            }
            
            # 发布事件
            self.event_bus.publish("emotion_changed", event_data)
            
        except Exception as e:
            logger.error_status(f"发布情感变化事件失败: {str(e)}")
    
    def _on_perception_result(self, event_data: Dict[str, Any]) -> None:
        """
        感知结果事件处理器
        
        Args:
            event_data: 事件数据
        """
        try:
            # 提取感知结果
            perception_type = event_data.get("type", "")
            content = event_data.get("content", {})
            
            # 处理不同类型的感知
            if perception_type == "text_perception":
                # 文本感知可能包含情感分析结果
                emotion_analysis = content.get("emotion_analysis", {})
                
                # 如果有情感分析结果
                if emotion_analysis:
                    dominant_emotion = emotion_analysis.get("dominant_emotion")
                    emotion_intensity = emotion_analysis.get("intensity", 0.5)
                    
                    if dominant_emotion:
                        # 调整情感强度使其适合本模块
                        adjusted_intensity = emotion_intensity * 0.3  # 外部感知影响较小
                        
                        # 更新情感状态
                        self._update_emotion(dominant_emotion, adjusted_intensity, "text_perception")
            
            elif perception_type == "environmental_perception":
                # 环境感知可能包含情绪触发因素
                mood_factors = content.get("mood_factors", {})
                
                # 处理环境因素的情感影响
                for factor, impact in mood_factors.items():
                    if factor == "noise_level" and impact > 0.7:
                        # 高噪音环境可能引发焦虑
                        self._update_emotion("恐惧", impact * 0.2, "environment_noise")
                    
                    elif factor == "light_level" and impact < 0.3:
                        # 低光环境可能引发平静或忧郁
                        self._update_emotion("平静", (1 - impact) * 0.2, "environment_light")
                    
                    elif factor == "temperature" and (impact < 0.3 or impact > 0.7):
                        # 极端温度可能引发不适
                        self._update_emotion("厌恶", 0.2, "environment_temperature")
            
            elif perception_type == "user_perception":
                # 用户感知可能包含用户情感状态
                user_emotion = content.get("emotion", "")
                user_emotion_intensity = content.get("emotion_intensity", 0.5)
                
                if user_emotion:
                    # 用户情感可能会影响系统情感(情感共鸣)
                    empathy_factor = 0.4  # 情感共鸣系数
                    
                    # 更新情感状态
                    self._update_emotion(user_emotion, user_emotion_intensity * empathy_factor, "user_emotion")
            
        except Exception as e:
            logger.error_status(f"处理感知结果事件失败: {str(e)}")
    
    def _on_user_interaction(self, event_data: Dict[str, Any]) -> None:
        """
        用户交互事件处理器
        
        Args:
            event_data: 事件数据
        """
        try:
            # 提取交互数据
            interaction_type = event_data.get("type", "")
            content = event_data.get("content", "")
            metadata = event_data.get("metadata", {})
            
            # 基于交互类型触发情感变化
            if interaction_type == "greeting":
                # 问候可能触发愉快情感
                self._update_emotion("喜悦", 0.2, "user_greeting")
                
            elif interaction_type == "question":
                # 问题可能触发好奇或警觉
                self._update_emotion("惊讶", 0.1, "user_question")
                
            elif interaction_type == "compliment":
                # 赞美可能触发喜悦
                self._update_emotion("喜悦", 0.3, "user_compliment")
                
            elif interaction_type == "criticism":
                # 批评可能触发悲伤或愤怒
                self._update_emotion("悲伤", 0.2, "user_criticism")
                
            elif interaction_type == "command":
                # 命令可能触发专注或压力
                self._update_emotion("恐惧", 0.1, "user_command")
            
            # 检查交互频率
            if "interaction_frequency" in metadata:
                frequency = metadata["interaction_frequency"]
                
                if frequency == "high":
                    # 高频交互可能增加亲近感
                    self._update_emotion("信任", 0.1, "high_interaction_frequency")
                elif frequency == "low":
                    # 低频交互可能增加疏离感
                    self._update_emotion("平静", 0.1, "low_interaction_frequency")
            
        except Exception as e:
            logger.error_status(f"处理用户交互事件失败: {str(e)}")
    
    def _on_environment_change(self, event_data: Dict[str, Any]) -> None:
        """
        环境变化事件处理器
        
        Args:
            event_data: 事件数据
        """
        try:
            # 提取环境变化
            changes = event_data.get("changes", {})
            
            # 处理环境变化对情感的影响
            if "time_of_day" in changes:
                time_of_day = changes["time_of_day"]
                
                # 不同时间段可能影响情感
                if time_of_day == "morning":
                    # 早晨可能增加活力
                    self._update_emotion("喜悦", 0.1, "morning_time")
                elif time_of_day == "evening":
                    # 晚上可能增加平静
                    self._update_emotion("平静", 0.1, "evening_time")
                elif time_of_day == "night":
                    # 深夜可能增加内省
                    self._update_emotion("平静", 0.2, "night_time")
            
            # 环境类型变化
            if "environment_type" in changes:
                env_type = changes["environment_type"]
                
                if env_type == "noisy":
                    # 嘈杂环境可能增加焦虑
                    self._update_emotion("恐惧", 0.2, "noisy_environment")
                elif env_type == "quiet":
                    # 安静环境可能增加平静
                    self._update_emotion("平静", 0.2, "quiet_environment")
                elif env_type == "crowded":
                    # 拥挤环境可能增加压力
                    self._update_emotion("恐惧", 0.1, "crowded_environment")
            
        except Exception as e:
            logger.error_status(f"处理环境变化事件失败: {str(e)}")
    
    def _on_goal_state_changed(self, event_data: Dict[str, Any]) -> None:
        """
        目标状态变化事件处理器
        
        Args:
            event_data: 事件数据
        """
        try:
            # 提取目标信息
            goal = event_data.get("goal", {})
            old_state = event_data.get("old_state", "")
            new_state = event_data.get("new_state", "")
            
            # 获取目标重要性
            importance = goal.get("priority", 5) / 10  # 归一化为0-1
            
            # 目标状态变化对情感的影响
            if old_state != "completed" and new_state == "completed":
                # 目标完成，触发喜悦
                self._update_emotion("喜悦", 0.3 * importance, "goal_completed")
                
            elif old_state != "failed" and new_state == "failed":
                # 目标失败，触发悲伤
                self._update_emotion("悲伤", 0.3 * importance, "goal_failed")
                
            elif old_state != "active" and new_state == "active":
                # 目标激活，触发期待
                self._update_emotion("期待", 0.2 * importance, "goal_activated")
                
            elif old_state != "blocked" and new_state == "blocked":
                # 目标受阻，触发焦虑
                self._update_emotion("恐惧", 0.2 * importance, "goal_blocked")
            
        except Exception as e:
            logger.error_status(f"处理目标状态变化事件失败: {str(e)}")
    
    def _emotion_update_loop(self) -> None:
        """
        情感更新循环
        
        在后台定期更新情感状态，模拟情感自然变化。
        """
        logger.success("情感更新循环已启动")
        
        while self.update_running:
            try:
                # 获取当前时间
                current_time = time.time()
                
                # 上次情感更新时间
                last_update = self.emotion_state["last_update"]
                
                # 如果距离上次更新超过阈值，进行自然衰减
                if current_time - last_update > self.emotion_config["base_update_interval"]:
                    self._decay_emotions()
                    self.emotion_state["last_update"] = current_time
                    
                    # 随机情感波动(模拟内部情感变化)
                    if random.random() < 0.3:  # 30%的概率发生随机波动
                        # 随机选择一种情感
                        emotions = list(self.emotion_model["基础情感"].keys())
                        random_emotion = random.choice(emotions)
                        
                        # 随机强度变化(-0.1到0.1)
                        intensity_change = (random.random() * 0.2) - 0.1
                        
                        # 更新情感
                        self._update_emotion(random_emotion, intensity_change, "internal_fluctuation")
                
                    # 检查是否需要自动调节情感
                    # 负面情感且强度高时进行自动调节
                    if (self.emotion_state["emotion_valence"] < -0.3 and 
                        self.emotion_state["emotion_intensity"] > self.emotion_config["self_regulation_threshold"]):
                        # 有10%的概率进行自我调节
                        if random.random() < 0.1:
                            self.auto_regulate_emotion()
                
                # 休眠一段时间
                time.sleep(self.emotion_config["base_update_interval"] / 2)
                
            except Exception as e:
                logger.error_status(f"情感更新循环异常: {str(e)}")
                time.sleep(10)  # 出错后短暂等待
    
    def _load_emotion_state(self) -> bool:
        """
        从文件加载情感状态
        
        Returns:
            加载是否成功
        """
        try:
            # 情感状态文件路径
            state_file = os.path.join(root_dir, "data", "emotion", f"{self.module_id}_state.json")
            
            # 如果文件存在，加载状态
            if os.path.exists(state_file):
                with open(state_file, "r", encoding="utf-8") as f:
                    state_data = json.load(f)
                
                # 更新情感状态
                if "emotion_state" in state_data:
                    self.emotion_state.update(state_data["emotion_state"])
                
                # 更新情感配置
                if "emotion_config" in state_data:
                    self.emotion_config.update(state_data["emotion_config"])
                
                # 更新调节策略
                if "regulation_strategies" in state_data:
                    self.regulation_strategies.update(state_data["regulation_strategies"])
                
                logger.info(f"已加载情感状态: {state_file}")
                return True
            else:
                logger.info("情感状态文件不存在，使用默认状态")
                return True
                
        except Exception as e:
            logger.error_status(f"加载情感状态失败: {str(e)}")
            return False
    
    def _save_emotion_state(self) -> bool:
        """
        保存情感状态到文件
        
        Returns:
            保存是否成功
        """
        try:
            # 确保目录存在
            emotion_dir = os.path.join(root_dir, "data", "emotion")
            os.makedirs(emotion_dir, exist_ok=True)
            
            # 情感状态文件路径
            state_file = os.path.join(emotion_dir, f"{self.module_id}_state.json")
            
            # 准备要保存的数据
            state_data = {
                "emotion_state": self.emotion_state,
                "emotion_config": self.emotion_config,
                "regulation_strategies": self.regulation_strategies,
                "saved_at": time.time()
            }
            
            # 保存到文件
            with open(state_file, "w", encoding="utf-8") as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
            
            # 保存情感记忆
            memory_file = os.path.join(emotion_dir, f"{self.module_id}_memory.json")
            with open(memory_file, "w", encoding="utf-8") as f:
                json.dump(self.emotion_memory, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"已保存情感状态: {state_file}")
            return True
            
        except Exception as e:
            logger.error_status(f"保存情感状态失败: {str(e)}")
            return False
    
    def get_emotion_state(self) -> Dict[str, Any]:
        """
        获取当前情感状态
        
        Returns:
            情感状态信息
        """
        return {
            "current_emotion": self.emotion_state["current_emotion"],
            "intensity": self.emotion_state["emotion_intensity"],
            "valence": self.emotion_state["emotion_valence"],
            "arousal": self.emotion_state["emotion_arousal"],
            "emotions": self.emotion_state["emotions"],
            "updated_at": self.emotion_state["last_update"]
        }
    
    def get_emotion_expression(self) -> Dict[str, Any]:
        """
        获取情感表达
        
        基于当前情感状态生成情感表达信息，可用于表情、语气等。
        
        Returns:
            情感表达信息
        """
        try:
            # 当前情感状态
            current_emotion = self.emotion_state["current_emotion"]
            intensity = self.emotion_state["emotion_intensity"]
            valence = self.emotion_state["emotion_valence"]
            arousal = self.emotion_state["emotion_arousal"]
            
            # 表达类型映射
            expression_types = {
                "喜悦": "微笑",
                "悲伤": "忧郁",
                "愤怒": "严肃",
                "恐惧": "紧张",
                "厌恶": "皱眉",
                "惊讶": "惊讶",
                "期待": "期待",
                "信任": "友好",
                "平静": "平静"
            }
            
            # 语气映射
            tone_mapping = {
                "喜悦": "愉快",
                "悲伤": "低沉",
                "愤怒": "急促",
                "恐惧": "紧张",
                "厌恶": "冷淡",
                "惊讶": "惊讶",
                "期待": "热情",
                "信任": "温和",
                "平静": "平静"
            }
            
            # 确定表达类型
            expression_type = expression_types.get(current_emotion, "平静")
            tone = tone_mapping.get(current_emotion, "平静")
            
            # 只有超过表达阈值才表达情感
            if intensity < self.emotion_config["expression_threshold"]:
                expression_type = "平静"
                tone = "平静"
                intensity_level = "很轻"
            else:
                # 确定强度级别
                if intensity < 0.3:
                    intensity_level = "轻微"
                elif intensity < 0.6:
                    intensity_level = "中等"
                else:
                    intensity_level = "强烈"
            
            # 生成表达描述
            expression_description = f"{intensity_level}的{expression_type}"
            
            # 返回表达信息
            return {
                "expression_type": expression_type,
                "intensity_level": intensity_level,
                "tone": tone,
                "description": expression_description,
                "valence": valence,
                "arousal": arousal
            }
            
        except Exception as e:
            logger.error_status(f"生成情感表达失败: {str(e)}")
            return {
                "expression_type": "平静",
                "intensity_level": "轻微",
                "tone": "平静",
                "description": "轻微的平静",
                "valence": 0.0,
                "arousal": 0.0
            }
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        继承自CognitiveModuleBase抽象类的方法实现
        
        Returns:
            模块状态信息
        """
        return {
            "module_id": self.module_id,
            "module_type": "emotion",
            "active": True,
            "current_emotion": self.emotion_state["current_emotion"],
            "emotion_intensity": self.emotion_state["emotion_intensity"],
            "emotion_valence": self.emotion_state["emotion_valence"],
            "emotion_arousal": self.emotion_state["emotion_arousal"],
            "dominant_duration": self.emotion_history["dominant_duration"],
            "intensity_trend": self.emotion_history["intensity_trend"]
        }

    def _apply_emotion_resonance(self, emotion: str, intensity: float, source: str) -> None:
        """
        应用情感共振效应
        
        某些情感会自动激活或抑制其他相关情感
        
        Args:
            emotion: 触发情感
            intensity: 触发强度
            source: 情感来源
        """
        # 只对足够强的情感触发共振
        if abs(intensity) < 0.2:
            return
        
        # 共振映射表 - 定义情感之间的激活和抑制关系
        resonance_map = {
            # 正面情感之间相互增强
            "喜悦": [("信任", 0.3), ("期待", 0.2)],
            "信任": [("喜悦", 0.2), ("平静", 0.3)],
            
            # 负面情感之间相互增强
            "愤怒": [("厌恶", 0.2), ("恐惧", -0.1)],  # 愤怒抑制恐惧
            "悲伤": [("恐惧", 0.2), ("愤怒", -0.1)],  # 悲伤抑制愤怒
            
            # 对立情感相互抑制
            "喜悦": [("悲伤", -0.3), ("厌恶", -0.2)],
            "平静": [("恐惧", -0.3), ("愤怒", -0.3)],
            
            # 惊讶可能激活多种情感
            "惊讶": [("恐惧", 0.1), ("喜悦", 0.1), ("期待", 0.2)]
        }
        
        # 检查情感是否有共振效应
        if emotion in resonance_map:
            resonance_factor = self.emotion_config["resonance_factor"]
            emotions = self.emotion_state["emotions"]
            
            # 应用共振效应
            for target_emotion, ratio in resonance_map[emotion]:
                # 计算影响强度
                effect = intensity * ratio * resonance_factor
                
                # 应用影响
                current = emotions.get(target_emotion, 0.0)
                new_value = max(0.0, min(self.emotion_config["max_intensity"], current + effect))
                
                # 更新情感
                if new_value > 0.01:  # 避免存储非常小的值
                    emotions[target_emotion] = new_value
                elif target_emotion in emotions and new_value <= 0.01:
                    emotions.pop(target_emotion)
                
            # 更新情感状态
            self.emotion_state["emotions"] = emotions

    def _update_emotion_history(self, emotion: str, intensity: float) -> None:
        """
        更新情感历史记录
        
        跟踪情感变化趋势和主导情感持续时间
        
        Args:
            emotion: 情感名称
            intensity: 新情感强度
        """
        current_time = time.time()
        
        # 记录最近情感状态
        self.emotion_history["recent_emotions"].append({
            "emotion": emotion,
            "intensity": intensity,
            "timestamp": current_time
        })
        
        # 限制历史记录大小
        if len(self.emotion_history["recent_emotions"]) > 10:
            self.emotion_history["recent_emotions"] = self.emotion_history["recent_emotions"][-10:]
        
        # 更新主导情感持续时间
        if self.emotion_state["current_emotion"] == self.emotion_history["last_dominant"]:
            # 主导情感未变，增加持续时间
            self.emotion_history["dominant_duration"] += (
                current_time - self.emotion_state["last_update"]
            )
        else:
            # 主导情感已变，重置持续时间
            self.emotion_history["last_dominant"] = self.emotion_state["current_emotion"]
            self.emotion_history["dominant_duration"] = 0
        
        # 计算情感强度变化趋势
        if len(self.emotion_history["recent_emotions"]) > 1:
            # 获取最近几个状态的强度
            recent_intensities = [
                state["intensity"] 
                for state in self.emotion_history["recent_emotions"]
                if state["emotion"] == emotion
            ]
            
            if len(recent_intensities) > 1:
                # 计算最近的强度趋势(简单线性)
                self.emotion_history["intensity_trend"] = recent_intensities[-1] - recent_intensities[0]

    def _evaluate_regulation_suitability(self, strategy: str, emotion: str) -> float:
        """
        评估调节策略对当前情感的适用性
        
        Args:
            strategy: 调节策略名称
            emotion: 当前情感
            
        Returns:
            适用性分数(0-1)
        """
        # 获取策略信息
        strategy_info = self.regulation_strategies[strategy]
        
        # 基础适用性
        base_suitability = 0.5
        
        # 检查是否为适用情感
        if emotion in strategy_info["适用情感"]:
            base_suitability = 0.9
        
        # 考虑过去的成功记录
        if strategy_info["使用次数"] > 0:
            # 分析历史记录中该策略的成功率
            relevant_records = [
                record for record in self.regulation_history 
                if record["strategy"] == strategy and record["current_emotion"] == emotion
            ]
            
            if relevant_records:
                # 计算历史成功率
                success_count = sum(1 for record in relevant_records if record.get("success", False))
                historical_success_rate = success_count / len(relevant_records)
                
                # 调整适用性
                base_suitability = (base_suitability + historical_success_rate) / 2
        
        # 返回适用性分数
        return max(0.1, min(1.0, base_suitability))

    def auto_regulate_emotion(self) -> Dict[str, Any]:
        """
        自动选择并应用最适合的情感调节策略
        
        当情感强度超过阈值时，自动选择最适合的调节策略
        
        Returns:
            调节结果
        """
        # 检查是否需要调节
        current_emotion = self.emotion_state["current_emotion"]
        current_intensity = self.emotion_state["emotion_intensity"]
        valence = self.emotion_state["emotion_valence"]
        
        # 只有当情感强度超过阈值且是负面情感时才需要调节
        if current_intensity < self.emotion_config["self_regulation_threshold"] or valence >= 0:
            return {
                "success": False,
                "message": "当前情感状态不需要调节",
                "current_emotion": current_emotion,
                "intensity": current_intensity,
                "valence": valence
            }
        
        # 评估所有策略的适用性
        strategy_scores = {}
        for strategy_name in self.regulation_strategies:
            # 检查冷却时间
            strategy_info = self.regulation_strategies[strategy_name]
            if time.time() - strategy_info["最后使用时间"] < self.emotion_config["regulation_cooldown"]:
                continue
            
            # 计算适用性分数
            suitability = self._evaluate_regulation_suitability(strategy_name, current_emotion)
            
            # 考虑效果值
            effect_value = strategy_info["效果值"]
            
            # 计算总分
            total_score = suitability * effect_value * strategy_info["成功率"]
            strategy_scores[strategy_name] = total_score
        
        # 如果没有可用策略
        if not strategy_scores:
            return {
                "success": False,
                "message": "当前没有可用的调节策略",
                "current_emotion": current_emotion
            }
        
        # 选择得分最高的策略
        best_strategy = max(strategy_scores.items(), key=lambda x: x[1])[0]
        
        # 确定目标情感
        if valence < -0.5:
            # 强烈负面情感，目标是平静
            target_emotion = "平静"
        else:
            # 轻微负面情感，可能目标是相对积极的情感
            target_emotion = "期待" if random.random() > 0.5 else "平静"
        
        # 应用策略
        result = self._regulate_emotion(best_strategy, target_emotion)
        
        # 添加自动调节标记
        if "success" in result and result["success"]:
            result["auto_regulated"] = True
            result["target_emotion"] = target_emotion
        
        return result

    def get_regulation_history(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        获取情感调节历史
        
        Args:
            limit: 最大返回记录数
            
        Returns:
            调节历史记录
        """
        if not self.regulation_history:
            return []
        
        # 按时间降序排序
        sorted_history = sorted(
            self.regulation_history,
            key=lambda x: x.get("timestamp", 0),
            reverse=True
        )
        
        # 限制数量
        return sorted_history[:limit]

    def get_cognitive_influence(self) -> Dict[str, float]:
        """
        获取情感对认知能力的影响因子
        
        情感状态会影响注意力、记忆、判断、创造力等认知能力
        
        Returns:
            认知影响因子字典
        """
        # 当前情感状态
        current_emotion = self.emotion_state["current_emotion"]
        intensity = self.emotion_state["emotion_intensity"]
        valence = self.emotion_state["emotion_valence"]
        arousal = self.emotion_state["emotion_arousal"]
        
        # 基础影响映射
        base_influences = {
            # 情感对注意力的影响
            "attention_breadth": 0.0,      # 注意广度 (负值=狭窄, 正值=宽广)
            "attention_stability": 0.0,     # 注意稳定性 (负值=易分心, 正值=专注)
            
            # 情感对思考的影响
            "analytical_thinking": 0.0,     # 分析性思维 (负值=直觉, 正值=分析)
            "creative_thinking": 0.0,       # 创造性思维 (负值=固化, 正值=发散)
            "processing_speed": 0.0,        # 思考速度 (负值=缓慢, 正值=快速)
            
            # 情感对决策的影响
            "risk_tolerance": 0.0,          # 风险容忍度 (负值=规避风险, 正值=寻求风险)
            "deliberation": 0.0,            # 深思熟虑程度 (负值=冲动, 正值=谨慎)
            
            # 情感对记忆的影响
            "memory_encoding": 0.0,         # 记忆编码 (负值=减弱, 正值=增强)
            "memory_bias": 0.0              # 记忆偏向 (负值=负面偏向, 正值=正面偏向)
        }
        
        # 情感特定影响
        emotion_influences = {
            "喜悦": {
                "attention_breadth": 0.4,
                "creative_thinking": 0.5,
                "processing_speed": 0.3,
                "risk_tolerance": 0.3,
                "memory_bias": 0.4
            },
            "悲伤": {
                "attention_breadth": -0.3,
                "processing_speed": -0.4,
                "deliberation": 0.3,
                "memory_bias": -0.5
            },
            "愤怒": {
                "attention_stability": -0.4,
                "processing_speed": 0.3,
                "risk_tolerance": 0.5,
                "deliberation": -0.6
            },
            "恐惧": {
                "attention_breadth": -0.5,
                "attention_stability": -0.3,
                "risk_tolerance": -0.6,
                "deliberation": 0.4,
                "memory_encoding": 0.5
            },
            "惊讶": {
                "attention_breadth": 0.5,
                "attention_stability": -0.2,
                "processing_speed": 0.2,
                "memory_encoding": 0.4
            },
            "厌恶": {
                "attention_breadth": -0.2,
                "analytical_thinking": 0.3,
                "risk_tolerance": -0.3
            },
            "期待": {
                "attention_stability": 0.3,
                "creative_thinking": 0.4,
                "risk_tolerance": 0.2
            },
            "信任": {
                "attention_stability": 0.4,
                "deliberation": 0.3,
                "memory_bias": 0.3
            },
            "平静": {
                "attention_stability": 0.5,
                "analytical_thinking": 0.4,
                "deliberation": 0.4,
                "memory_encoding": 0.2
            }
        }
        
        # 应用当前情感的影响
        if current_emotion in emotion_influences:
            for factor, value in emotion_influences[current_emotion].items():
                # 按情感强度调整影响程度
                base_influences[factor] = value * intensity
        
        # 应用情感效价和唤醒度的通用影响
        
        # 效价对记忆偏向和风险容忍度的影响
        base_influences["memory_bias"] += valence * 0.3
        base_influences["risk_tolerance"] += valence * 0.2
        
        # 唤醒度对注意力和处理速度的影响
        if arousal > 0.7:  # 高唤醒度
            base_influences["attention_stability"] -= (arousal - 0.7) * 2
            base_influences["processing_speed"] += (arousal - 0.7) * 2
        elif arousal < 0.3:  # 低唤醒度
            base_influences["processing_speed"] -= (0.3 - arousal) * 2
        
        # 情感复杂度对思维复杂度的影响
        emotion_complexity = min(1.0, len(self.emotion_state["emotions"]) / 5)
        base_influences["creative_thinking"] += emotion_complexity * 0.3
        
        # 限制所有影响因子在-1到1范围内
        for factor in base_influences:
            base_influences[factor] = max(-1.0, min(1.0, base_influences[factor]))
        
        # 添加元数据
        influences = {
            "factors": base_influences,
            "emotion": current_emotion,
            "intensity": intensity,
            "valence": valence,
            "arousal": arousal,
            "timestamp": time.time()
        }
        
        return influences

    def _on_thinking_process(self, event_data: Dict[str, Any]) -> None:
        """
        思考过程事件处理器
        
        当自主思考模块开始思考过程时，提供情感影响因子
        
        Args:
            event_data: 事件数据
        """
        try:
            # 获取思考主题和类型
            thinking_topic = event_data.get("topic", "")
            thinking_type = event_data.get("type", "")
            
            # 获取情感对认知的影响
            cognitive_influences = self.get_cognitive_influence()
            
            # 创建响应事件数据
            response_data = {
                "module_id": self.module_id,
                "thinking_topic": thinking_topic,
                "thinking_type": thinking_type,
                "emotional_state": {
                    "emotion": self.emotion_state["current_emotion"],
                    "intensity": self.emotion_state["emotion_intensity"],
                    "valence": self.emotion_state["emotion_valence"],
                    "arousal": self.emotion_state["emotion_arousal"]
                },
                "cognitive_influences": cognitive_influences["factors"],
                "timestamp": time.time()
            }
            
            # 发布情感影响思考事件
            self.event_bus.publish("emotion_influence_thinking", response_data)
            
            logger.debug(f"情感模块为思考主题 '{thinking_topic}' 提供了情感影响因子")
            
        except Exception as e:
            logger.error_status(f"处理思考过程事件失败: {str(e)}")

    def _on_decision_process(self, event_data: Dict[str, Any]) -> None:
        """
        决策过程事件处理器
        
        当自主决策模块开始决策过程时，提供情感影响因子
        
        Args:
            event_data: 事件数据
        """
        try:
            # 获取决策上下文和选项
            decision_context = event_data.get("context", {})
            decision_options = event_data.get("options", [])
            decision_type = event_data.get("type", "")
            
            # 获取情感对认知的影响
            cognitive_influences = self.get_cognitive_influence()
            
            # 获取当前情感倾向
            current_emotion = self.emotion_state["current_emotion"]
            valence = self.emotion_state["emotion_valence"]
            
            # 创建选项情感评估
            option_assessments = []
            if decision_options:
                for option in decision_options:
                    # 基于当前情感状态对选项进行评估
                    # 这里只是一个简单示例，实际应用可能需要更复杂的评估逻辑
                    option_valence = 0.0
                    
                    # 正面情感时偏好正面选项
                    if valence > 0.3:
                        if "positive" in option.get("tags", []):
                            option_valence = 0.3
                        if "negative" in option.get("tags", []):
                            option_valence = -0.2
                    
                    # 负面情感时可能更保守或更冲动
                    elif valence < -0.3:
                        if current_emotion in ["恐惧", "焦虑"]:
                            # 恐惧和焦虑导致更保守
                            if "safe" in option.get("tags", []):
                                option_valence = 0.3
                            if "risky" in option.get("tags", []):
                                option_valence = -0.4
                        elif current_emotion in ["愤怒"]:
                            # 愤怒可能导致更冲动的决策
                            if "immediate" in option.get("tags", []):
                                option_valence = 0.2
                            if "delayed" in option.get("tags", []):
                                option_valence = -0.2
                    
                    option_assessments.append({
                        "option_id": option.get("id", ""),
                        "emotional_valence": option_valence,
                        "risk_assessment": cognitive_influences["factors"]["risk_tolerance"]
                    })
            
            # 创建响应事件数据
            response_data = {
                "module_id": self.module_id,
                "decision_type": decision_type,
                "emotional_state": {
                    "emotion": current_emotion,
                    "intensity": self.emotion_state["emotion_intensity"],
                    "valence": valence,
                    "arousal": self.emotion_state["emotion_arousal"]
                },
                "cognitive_influences": cognitive_influences["factors"],
                "option_assessments": option_assessments,
                "timestamp": time.time()
            }
            
            # 发布情感影响决策事件
            self.event_bus.publish("emotion_influence_decision", response_data)
            
            logger.debug(f"情感模块为决策类型 '{decision_type}' 提供了情感影响因子")
            
        except Exception as e:
            logger.error_status(f"处理决策过程事件失败: {str(e)}")

def get_instance(module_id: str, config: Dict[str, Any] = None) -> EmotionSimulation:
    """
    获取情感模拟模块实例
    
    Args:
        module_id: 模块ID
        config: 配置信息
        
    Returns:
        情感模拟模块实例
    """
    module = EmotionSimulation(module_id, "情感模拟", "emotion")
    if config:
        module.config = config
    return module 