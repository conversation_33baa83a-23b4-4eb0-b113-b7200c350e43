"""
情感响应调节模块 - Emotion Response Modulation

该模块负责根据情感状态调节响应风格。
"""

from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random
from typing import Dict, Any, List

logger = get_unified_logger("cognitive_modules.emotion.response_modulation")

# 情感对应的语言表达特征
EMOTION_EXPRESSION_FEATURES = {
    "joy": {
        "tone": "enthusiastic",
        "tempo": "moderate_fast",
        "vocabulary": "positive",
        "expression_level": "expressive"
    },
    "sadness": {
        "tone": "gentle",
        "tempo": "slow",
        "vocabulary": "empathetic",
        "expression_level": "reserved"
    },
    "anger": {
        "tone": "firm",
        "tempo": "fast",
        "vocabulary": "direct",
        "expression_level": "controlled"
    },
    "fear": {
        "tone": "cautious",
        "tempo": "fast",
        "vocabulary": "reassuring",
        "expression_level": "attentive"
    },
    "disgust": {
        "tone": "reserved",
        "tempo": "moderate",
        "vocabulary": "neutral",
        "expression_level": "controlled"
    },
    "surprise": {
        "tone": "curious",
        "tempo": "moderate_fast",
        "vocabulary": "inquisitive",
        "expression_level": "expressive"
    },
    "trust": {
        "tone": "warm",
        "tempo": "moderate",
        "vocabulary": "supportive",
        "expression_level": "open"
    },
    "anticipation": {
        "tone": "hopeful",
        "tempo": "moderate",
        "vocabulary": "forward_looking",
        "expression_level": "engaged"
    },
    "neutral": {
        "tone": "balanced",
        "tempo": "moderate",
        "vocabulary": "neutral",
        "expression_level": "moderate"
    }
}

# 情感表达的样本词汇
EMOTION_VOCABULARY = {
    "positive": ["很高兴", "太好了", "真棒", "wonderful", "excellent", "fantastic", "brilliant", "splendid", "great"],
    "empathetic": ["理解", "同情", "感同身受", "empathize", "understand", "care", "feel for", "support"],
    "direct": ["明确", "直接", "clearly", "directly", "frankly", "straight", "precisely"],
    "reassuring": ["安心", "放心", "don't worry", "it's okay", "reassure", "calm", "secure"],
    "neutral": ["可以", "也许", "consider", "perhaps", "maybe", "possibly", "alternatively"],
    "inquisitive": ["好奇", "有趣", "interesting", "curious", "intriguing", "fascinating", "wonder"],
    "supportive": ["支持", "帮助", "support", "help", "assist", "encourage", "back you up"],
    "forward_looking": ["期待", "展望", "look forward to", "anticipate", "expect", "await", "foresee"]
}

def process(context):
    """
    处理函数
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.info("执行情感响应调节处理")
    
    # 获取情感状态分析结果
    shared_data = context.get("shared_data", {})
    emotion_state = shared_data.get("current_emotions", {})
    
    # 如果没有情感状态，尝试从步骤结果中获取
    if not emotion_state:
        step_results = context.get("step_results", {})
        emotion_analysis_result = step_results.get("emotion_analysis", {})
        
        if emotion_analysis_result:
            emotion_state = emotion_analysis_result.get("emotion_state", {})
    
    # 如果仍然没有情感状态，使用默认的中性情感
    if not emotion_state:
        logger.info("没有情感状态信息，使用默认中性情感")
        emotion_state = {
            "dominant_emotion": "neutral",
            "intensity": 0.5,
            "valence": 0.0,
            "arousal": 0.5,
            "dominance": 0.5
        }
    else:
        logger.success(f"获取到情感状态: {emotion_state.get('dominant_emotion', 'unknown')}")
    
    # 调节响应风格
    response_style = _modulate_response_style(emotion_state)
    
    # 生成表达词汇建议
    vocabulary_suggestions = _suggest_vocabulary(emotion_state)
    
    result = {
        "response_style": response_style,
        "vocabulary_suggestions": vocabulary_suggestions,
        "applied_emotion": emotion_state.get("dominant_emotion", "neutral"),
        "intensity_factor": emotion_state.get("intensity", 0.5)
    }
    
    logger.info(f"情感调节结果: {response_style['overall_style']}")
    
    return result

def _modulate_response_style(emotion_state):
    """
    调节响应风格
    
    Args:
        emotion_state: 情感状态
        
    Returns:
        响应风格参数
    """
    dominant_emotion = emotion_state.get("dominant_emotion", "neutral")
    intensity = emotion_state.get("intensity", 0.5)
    valence = emotion_state.get("valence", 0.0)
    arousal = emotion_state.get("arousal", 0.5)
    
    # 获取情感对应的表达特征
    expression_features = EMOTION_EXPRESSION_FEATURES.get(dominant_emotion, EMOTION_EXPRESSION_FEATURES["neutral"])
    
    # 根据情感强度调整表达级别
    expression_level = expression_features["expression_level"]
    if intensity < 0.3:
        expression_level = "reserved"
    elif intensity > 0.7:
        expression_level = "expressive"
    
    # 整体风格描述
    overall_style = f"{expression_features['tone']}_{expression_level}"
    
    # 构建响应风格参数
    response_style = {
        "tone": expression_features["tone"],
        "tempo": expression_features["tempo"],
        "vocabulary_type": expression_features["vocabulary"],
        "expression_level": expression_level,
        "emotionality": intensity,
        "formality": _calculate_formality(dominant_emotion, intensity),
        "politeness": _calculate_politeness(valence, dominant_emotion),
        "directness": _calculate_directness(dominant_emotion, arousal),
        "overall_style": overall_style
    }
    
    return response_style

def _calculate_formality(emotion, intensity):
    """计算正式程度"""
    # 对于某些情感，降低正式程度
    if emotion in ["joy", "surprise", "trust"] and intensity > 0.6:
        return max(0.3, 0.7 - (intensity - 0.6))
    # 对于某些情感，提高正式程度
    elif emotion in ["anger", "disgust", "fear"]:
        return min(0.9, 0.6 + intensity * 0.3)
    # 默认适中
    else:
        return 0.6

def _calculate_politeness(valence, emotion):
    """计算礼貌程度"""
    # 正面情感通常更礼貌
    if valence > 0.3:
        return min(0.9, 0.7 + valence * 0.2)
    # 某些负面情感可能降低礼貌度
    elif emotion in ["anger", "disgust"]:
        return max(0.4, 0.7 + valence * 0.3)
    # 默认中等礼貌
    else:
        return 0.7

def _calculate_directness(emotion, arousal):
    """计算直接程度"""
    # 高唤醒情感通常更直接
    if arousal > 0.7:
        return min(0.9, 0.5 + arousal * 0.4)
    # 某些情感特别直接
    elif emotion in ["anger", "surprise"]:
        return min(0.9, 0.6 + arousal * 0.3)
    # 某些情感较为含蓄
    elif emotion in ["sadness", "fear"]:
        return max(0.3, 0.5 - (0.7 - arousal) * 0.3)
    # 默认中等直接
    else:
        return 0.5

def _suggest_vocabulary(emotion_state):
    """
    生成表达词汇建议
    
    Args:
        emotion_state: 情感状态
        
    Returns:
        词汇建议列表
    """
    dominant_emotion = emotion_state.get("dominant_emotion", "neutral")
    
    # 获取情感对应的词汇类型
    vocabulary_type = EMOTION_EXPRESSION_FEATURES.get(dominant_emotion, {}).get("vocabulary", "neutral")
    
    # 获取对应的词汇列表
    vocabulary_list = EMOTION_VOCABULARY.get(vocabulary_type, [])
    
    # 随机选择几个词汇
    if vocabulary_list:
        selected_count = min(3, len(vocabulary_list))
        selected_vocabulary = random.sample(vocabulary_list, selected_count)
    else:
        selected_vocabulary = []
    
    return selected_vocabulary 