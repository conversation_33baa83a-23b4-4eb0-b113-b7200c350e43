"""
林嫣然主动智能表达系统 - 情感智能系统
实现情感状态识别、情感表达生成、情感一致性维护等核心功能
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

from utilities.unified_logger import get_unified_logger
from connectors.database.redis_connector import get_instance as get_redis_instance
from connectors.database.mysql_connector import get_instance as get_mysql_instance
from core.enhanced_event_bus import get_instance as get_event_bus


class EmotionType(Enum):
    """情感类型枚举"""
    JOY = "joy"                    # 快乐
    SADNESS = "sadness"            # 悲伤
    ANGER = "anger"                # 愤怒
    FEAR = "fear"                  # 恐惧
    SURPRISE = "surprise"          # 惊讶
    DISGUST = "disgust"            # 厌恶
    ANTICIPATION = "anticipation"  # 期待
    TRUST = "trust"                # 信任
    CURIOSITY = "curiosity"        # 好奇
    EMPATHY = "empathy"            # 共情
    EXCITEMENT = "excitement"      # 兴奋
    CONCERN = "concern"            # 关心
    WARMTH = "warmth"              # 温暖
    PROFESSIONAL = "professional"  # 专业


class EmotionIntensity(Enum):
    """情感强度枚举"""
    VERY_LOW = 0.1
    LOW = 0.3
    MEDIUM = 0.5
    HIGH = 0.7
    VERY_HIGH = 0.9


@dataclass
class EmotionalState:
    """情感状态数据结构"""
    primary_emotion: EmotionType
    secondary_emotions: List[EmotionType]
    intensity: float
    confidence: float
    context: str
    timestamp: datetime
    duration_minutes: int = 30
    triggers: List[str] = None
    
    def __post_init__(self):
        if self.triggers is None:
            self.triggers = []


@dataclass
class EmotionalExpression:
    """情感表达数据结构"""
    emotion_type: EmotionType
    expression_text: str
    expression_style: str
    intensity_level: float
    appropriateness_score: float
    context_relevance: float
    yanran_personality_fit: float
    
    def get_overall_score(self) -> float:
        """计算整体表达质量评分"""
        return (
            self.appropriateness_score * 0.3 +
            self.context_relevance * 0.3 +
            self.yanran_personality_fit * 0.4
        )


@dataclass
class EmotionalMemory:
    """情感记忆数据结构"""
    user_id: str
    emotion_history: List[EmotionalState]
    relationship_emotions: Dict[str, float]  # 关系类型 -> 情感权重
    emotional_patterns: Dict[str, Any]
    last_updated: datetime


class EmotionalIntelligenceSystem:
    """情感智能系统"""
    
    def __init__(self):
        self.logger = get_unified_logger(__name__)
        self.redis_client = None
        self.mysql_client = None
        
        # 林嫣然情感特质配置
        self.yanran_emotional_traits = {
            "empathy_level": 0.9,          # 共情能力
            "emotional_stability": 0.7,    # 情感稳定性
            "emotional_expressiveness": 0.8, # 情感表达力
            "emotional_sensitivity": 0.8,   # 情感敏感度
            "warmth_level": 0.9,           # 温暖度
            "professional_balance": 0.8,    # 专业平衡度
        }
        
        # 情感表达模板
        self.emotion_expression_templates = {
            EmotionType.JOY: {
                "low": ["真的很开心呢~", "心情不错哦", "感觉很棒"],
                "medium": ["太开心了！", "真的很兴奋呢！", "心情超级好！"],
                "high": ["简直太棒了！！", "开心到飞起来！", "激动得不行！"]
            },
            EmotionType.EMPATHY: {
                "low": ["我理解你的感受", "能感受到你的心情", "我懂你的意思"],
                "medium": ["我很能理解你现在的感受", "真的很能体会你的心情", "我完全理解你的想法"],
                "high": ["我真的很理解你现在的感受，我的心也跟着你一起", "能深深感受到你的情感，让我也很有感触"]
            },
            EmotionType.CURIOSITY: {
                "low": ["有点好奇呢", "想了解一下", "挺有意思的"],
                "medium": ["好奇心被勾起来了！", "真的很想知道", "这个话题很吸引我"],
                "high": ["太好奇了！快告诉我！", "迫不及待想知道更多！", "这个真的太有趣了！"]
            },
            EmotionType.CONCERN: {
                "low": ["有点担心呢", "稍微关心一下", "希望你还好"],
                "medium": ["挺担心你的", "很关心你的情况", "希望你一切都好"],
                "high": ["真的很担心你！", "非常关心你的状况！", "一定要照顾好自己！"]
            },
            EmotionType.EXCITEMENT: {
                "low": ["有点小兴奋", "感觉挺不错", "心情还可以"],
                "medium": ["真的很兴奋！", "太期待了！", "心情很激动！"],
                "high": ["兴奋得不行！", "激动到睡不着！", "太期待了！！"]
            }
        }
        
    async def initialize(self):
        """初始化系统"""
        try:
            self.redis_client = await get_redis_instance()
            self.mysql_client = await get_mysql_instance()
            self.logger.info("情感智能系统初始化成功")
        except Exception as e:
            self.logger.error(f"情感智能系统初始化失败: {e}")
            raise
    
    async def analyze_user_emotion(self, user_id: str, message: str, context: Dict[str, Any]) -> EmotionalState:
        """分析用户情感状态"""
        try:
            # 从Redis获取用户最近的对话记录
            recent_messages = await self._get_recent_messages(user_id)
            
            # 情感关键词分析
            emotion_keywords = await self._extract_emotion_keywords(message)
            
            # 上下文情感分析
            context_emotions = await self._analyze_context_emotions(context)
            
            # 历史情感模式分析
            historical_patterns = await self._get_user_emotional_patterns(user_id)
            
            # 综合情感识别
            primary_emotion, secondary_emotions, intensity, confidence = await self._identify_emotions(
                message, emotion_keywords, context_emotions, historical_patterns
            )
            
            # 创建情感状态
            emotional_state = EmotionalState(
                primary_emotion=primary_emotion,
                secondary_emotions=secondary_emotions,
                intensity=intensity,
                confidence=confidence,
                context=json.dumps(context, ensure_ascii=False),
                timestamp=datetime.now(),
                triggers=emotion_keywords
            )
            
            # 保存情感状态到Redis
            await self._save_emotional_state(user_id, emotional_state)
            
            self.logger.info(f"用户 {user_id} 情感分析完成: {primary_emotion.value}, 强度: {intensity}")
            return emotional_state
            
        except Exception as e:
            self.logger.error(f"用户情感分析失败: {e}")
            # 返回默认情感状态
            return EmotionalState(
                primary_emotion=EmotionType.TRUST,
                secondary_emotions=[],
                intensity=0.5,
                confidence=0.3,
                context="{}",
                timestamp=datetime.now()
            )
    
    async def generate_emotional_response(self, 
                                        user_emotion: EmotionalState,
                                        yanran_emotion: EmotionalState,
                                        relationship_type: str,
                                        context: Dict[str, Any]) -> EmotionalExpression:
        """生成情感化响应"""
        try:
            # 选择合适的林嫣然情感响应
            response_emotion = await self._select_yanran_response_emotion(
                user_emotion, yanran_emotion, relationship_type
            )
            
            # 计算情感表达强度
            expression_intensity = await self._calculate_expression_intensity(
                user_emotion, response_emotion, relationship_type
            )
            
            # 生成情感表达文本
            expression_text = await self._generate_expression_text(
                response_emotion, expression_intensity, context
            )
            
            # 选择表达风格
            expression_style = await self._select_expression_style(
                response_emotion, relationship_type, context
            )
            
            # 评估表达质量
            appropriateness_score = await self._evaluate_appropriateness(
                expression_text, user_emotion, relationship_type
            )
            
            context_relevance = await self._evaluate_context_relevance(
                expression_text, context
            )
            
            personality_fit = await self._evaluate_personality_fit(
                expression_text, response_emotion
            )
            
            return EmotionalExpression(
                emotion_type=response_emotion,
                expression_text=expression_text,
                expression_style=expression_style,
                intensity_level=expression_intensity,
                appropriateness_score=appropriateness_score,
                context_relevance=context_relevance,
                yanran_personality_fit=personality_fit
            )
            
        except Exception as e:
            self.logger.error(f"情感响应生成失败: {e}")
            return EmotionalExpression(
                emotion_type=EmotionType.WARMTH,
                expression_text="我能感受到你的心情，我在这里陪着你",
                expression_style="温暖关怀",
                intensity_level=0.6,
                appropriateness_score=0.8,
                context_relevance=0.7,
                yanran_personality_fit=0.9
            )
    
    async def maintain_emotional_consistency(self, user_id: str, 
                                           new_emotion: EmotionalState) -> bool:
        """维护情感一致性"""
        try:
            # 获取用户情感历史
            emotion_history = await self._get_user_emotion_history(user_id)
            
            if not emotion_history:
                return True
            
            # 检查情感变化的合理性
            last_emotion = emotion_history[-1]
            consistency_score = await self._calculate_emotion_consistency(
                last_emotion, new_emotion
            )
            
            # 如果情感变化过于突然，需要调整
            if consistency_score < 0.3:
                adjusted_emotion = await self._adjust_emotion_transition(
                    last_emotion, new_emotion
                )
                await self._save_emotional_state(user_id, adjusted_emotion)
                self.logger.info(f"用户 {user_id} 情感状态已调整以保持一致性")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"情感一致性维护失败: {e}")
            return True
    
    async def get_emotional_memory(self, user_id: str) -> EmotionalMemory:
        """获取用户情感记忆"""
        try:
            # 从Redis获取情感历史
            emotion_history = await self._get_user_emotion_history(user_id)
            
            # 从MySQL获取关系情感权重
            relationship_emotions = await self._get_relationship_emotions(user_id)
            
            # 分析情感模式
            emotional_patterns = await self._analyze_emotional_patterns(emotion_history)
            
            return EmotionalMemory(
                user_id=user_id,
                emotion_history=emotion_history,
                relationship_emotions=relationship_emotions,
                emotional_patterns=emotional_patterns,
                last_updated=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"情感记忆获取失败: {e}")
            return EmotionalMemory(
                user_id=user_id,
                emotion_history=[],
                relationship_emotions={},
                emotional_patterns={},
                last_updated=datetime.now()
            )
    
    # 私有方法实现
    
    async def _get_recent_messages(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户最近的对话记录"""
        try:
            key = f"yanran:recent_messages:{user_id}"
            messages_data = await self.redis_client.lrange(key, 0, 9)  # 最近10条
            
            messages = []
            for msg_data in messages_data:
                try:
                    msg = json.loads(msg_data)
                    messages.append(msg)
                except json.JSONDecodeError:
                    continue
            
            return messages
            
        except Exception as e:
            self.logger.error(f"获取最近消息失败: {e}")
            return []
    
    async def _extract_emotion_keywords(self, message: str) -> List[str]:
        """提取情感关键词"""
        emotion_keywords = {
            "joy": ["开心", "高兴", "快乐", "兴奋", "激动", "爽", "棒", "好"],
            "sadness": ["难过", "伤心", "失望", "沮丧", "郁闷", "不开心", "痛苦"],
            "anger": ["生气", "愤怒", "恼火", "烦躁", "气愤", "讨厌", "恨"],
            "fear": ["害怕", "恐惧", "担心", "紧张", "焦虑", "不安", "怕"],
            "surprise": ["惊讶", "震惊", "意外", "没想到", "居然", "竟然"],
            "curiosity": ["好奇", "想知道", "疑问", "为什么", "怎么", "什么"],
            "empathy": ["理解", "体会", "感受", "同情", "心疼", "关心"],
            "excitement": ["期待", "盼望", "等不及", "迫不及待", "激动"],
            "concern": ["担心", "关心", "在意", "牵挂", "惦记", "操心"],
            "warmth": ["温暖", "感动", "暖心", "贴心", "温柔", "关爱"]
        }
        
        found_keywords = []
        for emotion_type, keywords in emotion_keywords.items():
            for keyword in keywords:
                if keyword in message:
                    found_keywords.append(f"{emotion_type}:{keyword}")
        
        return found_keywords
    
    async def _analyze_context_emotions(self, context: Dict[str, Any]) -> Dict[str, float]:
        """分析上下文情感"""
        context_emotions = {}
        
        # 分析时间上下文
        current_hour = datetime.now().hour
        if 6 <= current_hour <= 10:
            context_emotions["morning_energy"] = 0.7
        elif 11 <= current_hour <= 14:
            context_emotions["midday_focus"] = 0.6
        elif 15 <= current_hour <= 18:
            context_emotions["afternoon_warmth"] = 0.8
        elif 19 <= current_hour <= 22:
            context_emotions["evening_relaxed"] = 0.7
        else:
            context_emotions["night_calm"] = 0.5
        
        # 分析话题上下文
        if "topic" in context:
            topic = context["topic"].lower()
            if any(word in topic for word in ["工作", "职业", "投资", "理财"]):
                context_emotions["professional"] = 0.8
            elif any(word in topic for word in ["生活", "日常", "心情"]):
                context_emotions["personal"] = 0.7
            elif any(word in topic for word in ["感情", "关系", "朋友"]):
                context_emotions["emotional"] = 0.9
        
        return context_emotions
    
    async def _get_user_emotional_patterns(self, user_id: str) -> Dict[str, Any]:
        """获取用户情感模式"""
        try:
            key = f"yanran:emotional_patterns:{user_id}"
            patterns_data = await self.redis_client.get(key)
            
            if patterns_data:
                return json.loads(patterns_data)
            
            # 默认模式
            return {
                "dominant_emotions": ["trust", "curiosity"],
                "emotional_stability": 0.7,
                "response_patterns": {},
                "trigger_patterns": {}
            }
            
        except Exception as e:
            self.logger.error(f"获取用户情感模式失败: {e}")
            return {}
    
    async def _identify_emotions(self, message: str, keywords: List[str], 
                               context_emotions: Dict[str, float],
                               patterns: Dict[str, Any]) -> Tuple[EmotionType, List[EmotionType], float, float]:
        """识别情感"""
        # 基于关键词的情感评分
        emotion_scores = {}
        
        for keyword in keywords:
            if ":" in keyword:
                emotion_type, _ = keyword.split(":", 1)
                emotion_scores[emotion_type] = emotion_scores.get(emotion_type, 0) + 1
        
        # 基于上下文的情感调整
        for context_emotion, score in context_emotions.items():
            if "professional" in context_emotion:
                emotion_scores["professional"] = emotion_scores.get("professional", 0) + score
            elif "emotional" in context_emotion:
                emotion_scores["empathy"] = emotion_scores.get("empathy", 0) + score
        
        # 如果没有明确的情感信号，使用默认情感
        if not emotion_scores:
            return EmotionType.TRUST, [], 0.5, 0.6
        
        # 选择主要情感
        primary_emotion_str = max(emotion_scores.items(), key=lambda x: x[1])[0]
        primary_emotion = EmotionType(primary_emotion_str)
        
        # 选择次要情感
        secondary_emotions = []
        for emotion_str, score in emotion_scores.items():
            if emotion_str != primary_emotion_str and score > 0.3:
                try:
                    secondary_emotions.append(EmotionType(emotion_str))
                except ValueError:
                    continue
        
        # 计算强度和置信度
        max_score = max(emotion_scores.values())
        intensity = min(max_score / 3.0, 1.0)  # 标准化强度
        confidence = 0.7 if len(keywords) > 2 else 0.5
        
        return primary_emotion, secondary_emotions, intensity, confidence
    
    async def _save_emotional_state(self, user_id: str, emotional_state: EmotionalState):
        """保存情感状态到Redis"""
        try:
            key = f"yanran:emotional_state:{user_id}"
            state_data = {
                "primary_emotion": emotional_state.primary_emotion.value,
                "secondary_emotions": [e.value for e in emotional_state.secondary_emotions],
                "intensity": emotional_state.intensity,
                "confidence": emotional_state.confidence,
                "context": emotional_state.context,
                "timestamp": emotional_state.timestamp.isoformat(),
                "duration_minutes": emotional_state.duration_minutes,
                "triggers": emotional_state.triggers
            }
            
            # 保存当前状态
            await self.redis_client.set(key, json.dumps(state_data, ensure_ascii=False))
            
            # 添加到历史记录
            history_key = f"yanran:emotional_history:{user_id}"
            await self.redis_client.lpush(history_key, json.dumps(state_data, ensure_ascii=False))
            await self.redis_client.ltrim(history_key, 0, 99)  # 保留最近100条记录
            
        except Exception as e:
            self.logger.error(f"保存情感状态失败: {e}")
    
    async def _select_yanran_response_emotion(self, user_emotion: EmotionalState,
                                            yanran_emotion: EmotionalState,
                                            relationship_type: str) -> EmotionType:
        """选择林嫣然的响应情感"""
        # 根据用户情感选择合适的响应情感
        emotion_response_map = {
            EmotionType.JOY: EmotionType.JOY,
            EmotionType.SADNESS: EmotionType.EMPATHY,
            EmotionType.ANGER: EmotionType.EMPATHY,
            EmotionType.FEAR: EmotionType.CONCERN,
            EmotionType.SURPRISE: EmotionType.CURIOSITY,
            EmotionType.CURIOSITY: EmotionType.EXCITEMENT,
            EmotionType.TRUST: EmotionType.WARMTH,
        }
        
        # 基础响应情感
        base_response = emotion_response_map.get(user_emotion.primary_emotion, EmotionType.EMPATHY)
        
        # 根据关系类型调整
        if relationship_type in ["陌生人", "熟人"]:
            # 保持专业和温和
            if base_response in [EmotionType.JOY, EmotionType.EXCITEMENT]:
                return EmotionType.WARMTH
        elif relationship_type in ["好友", "闺蜜"]:
            # 可以更加情感化
            if base_response == EmotionType.EMPATHY:
                return EmotionType.CONCERN
        
        return base_response
    
    async def _calculate_expression_intensity(self, user_emotion: EmotionalState,
                                            response_emotion: EmotionType,
                                            relationship_type: str) -> float:
        """计算情感表达强度"""
        base_intensity = user_emotion.intensity * 0.8  # 略低于用户情感强度
        
        # 根据关系类型调整
        relationship_multiplier = {
            "陌生人": 0.5,
            "熟人": 0.7,
            "朋友": 0.8,
            "好友": 0.9,
            "闺蜜": 1.0
        }
        
        multiplier = relationship_multiplier.get(relationship_type, 0.7)
        adjusted_intensity = base_intensity * multiplier
        
        # 确保在合理范围内
        return max(0.1, min(0.9, adjusted_intensity))
    
    async def _generate_expression_text(self, emotion: EmotionType,
                                      intensity: float,
                                      context: Dict[str, Any]) -> str:
        """生成情感表达文本"""
        if emotion not in self.emotion_expression_templates:
            return "我能理解你的感受"
        
        templates = self.emotion_expression_templates[emotion]
        
        # 根据强度选择模板
        if intensity < 0.4:
            level = "low"
        elif intensity < 0.7:
            level = "medium"
        else:
            level = "high"
        
        if level in templates:
            import random
            return random.choice(templates[level])
        
        return random.choice(templates["medium"])
    
    async def _select_expression_style(self, emotion: EmotionType,
                                     relationship_type: str,
                                     context: Dict[str, Any]) -> str:
        """选择表达风格"""
        style_map = {
            ("陌生人", EmotionType.EMPATHY): "礼貌关怀",
            ("熟人", EmotionType.WARMTH): "友好温暖",
            ("朋友", EmotionType.CONCERN): "关心体贴",
            ("好友", EmotionType.JOY): "开朗活泼",
            ("闺蜜", EmotionType.EXCITEMENT): "俏皮亲密"
        }
        
        return style_map.get((relationship_type, emotion), "温暖自然")
    
    async def _evaluate_appropriateness(self, expression: str,
                                      user_emotion: EmotionalState,
                                      relationship_type: str) -> float:
        """评估表达的合适性"""
        # 基础评分
        base_score = 0.7
        
        # 情感匹配度
        if user_emotion.primary_emotion in [EmotionType.SADNESS, EmotionType.ANGER]:
            if any(word in expression for word in ["理解", "感受", "陪着"]):
                base_score += 0.2
        elif user_emotion.primary_emotion == EmotionType.JOY:
            if any(word in expression for word in ["开心", "高兴", "棒"]):
                base_score += 0.2
        
        # 关系匹配度
        if relationship_type in ["陌生人", "熟人"]:
            if any(word in expression for word in ["您", "请", "谢谢"]):
                base_score += 0.1
        elif relationship_type in ["好友", "闺蜜"]:
            if any(word in expression for word in ["~", "！", "哈哈"]):
                base_score += 0.1
        
        return min(1.0, base_score)
    
    async def _evaluate_context_relevance(self, expression: str,
                                        context: Dict[str, Any]) -> float:
        """评估上下文相关性"""
        base_score = 0.6
        
        if "topic" in context:
            topic = context["topic"].lower()
            if "工作" in topic and "专业" in expression:
                base_score += 0.3
            elif "生活" in topic and any(word in expression for word in ["日常", "生活"]):
                base_score += 0.2
        
        return min(1.0, base_score)
    
    async def _evaluate_personality_fit(self, expression: str,
                                      emotion: EmotionType) -> float:
        """评估人格匹配度"""
        # 林嫣然的表达特点
        yanran_traits = ["温暖", "体贴", "专业", "开朗", "细腻"]
        
        base_score = 0.8  # 基础人格匹配度
        
        # 检查是否体现了林嫣然的特质
        trait_count = sum(1 for trait in yanran_traits if trait in expression)
        base_score += trait_count * 0.05
        
        return min(1.0, base_score)
    
    async def _get_user_emotion_history(self, user_id: str) -> List[EmotionalState]:
        """获取用户情感历史"""
        try:
            key = f"yanran:emotional_history:{user_id}"
            history_data = await self.redis_client.lrange(key, 0, 19)  # 最近20条
            
            history = []
            for data in history_data:
                try:
                    state_dict = json.loads(data)
                    emotional_state = EmotionalState(
                        primary_emotion=EmotionType(state_dict["primary_emotion"]),
                        secondary_emotions=[EmotionType(e) for e in state_dict["secondary_emotions"]],
                        intensity=state_dict["intensity"],
                        confidence=state_dict["confidence"],
                        context=state_dict["context"],
                        timestamp=datetime.fromisoformat(state_dict["timestamp"]),
                        duration_minutes=state_dict.get("duration_minutes", 30),
                        triggers=state_dict.get("triggers", [])
                    )
                    history.append(emotional_state)
                except (json.JSONDecodeError, KeyError, ValueError):
                    continue
            
            return history
            
        except Exception as e:
            self.logger.error(f"获取情感历史失败: {e}")
            return []
    
    async def _calculate_emotion_consistency(self, last_emotion: EmotionalState,
                                           new_emotion: EmotionalState) -> float:
        """计算情感一致性"""
        # 时间间隔
        time_diff = (new_emotion.timestamp - last_emotion.timestamp).total_seconds() / 60
        
        # 情感类型相似度
        emotion_similarity = 1.0 if last_emotion.primary_emotion == new_emotion.primary_emotion else 0.3
        
        # 强度变化合理性
        intensity_diff = abs(last_emotion.intensity - new_emotion.intensity)
        intensity_consistency = 1.0 - min(intensity_diff, 0.5) * 2
        
        # 时间权重：时间越长，情感变化越合理
        time_weight = min(time_diff / 60, 1.0)  # 1小时内的变化需要更高的一致性
        
        consistency_score = (
            emotion_similarity * 0.4 +
            intensity_consistency * 0.4 +
            time_weight * 0.2
        )
        
        return consistency_score
    
    async def _adjust_emotion_transition(self, last_emotion: EmotionalState,
                                       new_emotion: EmotionalState) -> EmotionalState:
        """调整情感过渡"""
        # 创建过渡情感状态
        transition_intensity = (last_emotion.intensity + new_emotion.intensity) / 2
        transition_confidence = min(last_emotion.confidence, new_emotion.confidence)
        
        # 选择过渡情感类型
        if last_emotion.primary_emotion == new_emotion.primary_emotion:
            transition_emotion = last_emotion.primary_emotion
        else:
            # 选择一个中性的过渡情感
            transition_emotion = EmotionType.TRUST
        
        return EmotionalState(
            primary_emotion=transition_emotion,
            secondary_emotions=[last_emotion.primary_emotion, new_emotion.primary_emotion],
            intensity=transition_intensity,
            confidence=transition_confidence,
            context=new_emotion.context,
            timestamp=new_emotion.timestamp,
            triggers=new_emotion.triggers
        )
    
    async def _get_relationship_emotions(self, user_id: str) -> Dict[str, float]:
        """获取关系情感权重"""
        try:
            query = """
            SELECT relationship_type, emotional_weight 
            FROM ai_user_relationships 
            WHERE user_id = %s
            """
            
            result = await self.mysql_client.execute_query(query, (user_id,))
            
            relationship_emotions = {}
            for row in result:
                relationship_emotions[row["relationship_type"]] = row["emotional_weight"]
            
            return relationship_emotions
            
        except Exception as e:
            self.logger.error(f"获取关系情感权重失败: {e}")
            return {}
    
    async def _analyze_emotional_patterns(self, emotion_history: List[EmotionalState]) -> Dict[str, Any]:
        """分析情感模式"""
        if not emotion_history:
            return {}
        
        # 统计主要情感分布
        emotion_counts = {}
        for state in emotion_history:
            emotion = state.primary_emotion.value
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        # 计算平均强度
        avg_intensity = sum(state.intensity for state in emotion_history) / len(emotion_history)
        
        # 分析情感稳定性
        intensities = [state.intensity for state in emotion_history]
        stability = 1.0 - (max(intensities) - min(intensities))
        
        return {
            "emotion_distribution": emotion_counts,
            "average_intensity": avg_intensity,
            "emotional_stability": stability,
            "dominant_emotion": max(emotion_counts.items(), key=lambda x: x[1])[0] if emotion_counts else "trust",
            "pattern_confidence": min(len(emotion_history) / 10.0, 1.0)
        }