"""
林嫣然主动智能表达系统 - 情感表达生成器
专门负责根据情感状态和上下文生成符合林嫣然人设的情感化表达内容
"""

import asyncio
import json
import random
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from utilities.unified_logger import get_unified_logger
from cognitive_modules.emotion.proactive.emotional_intelligence_system import (
    EmotionType, EmotionalState, EmotionalExpression, EmotionIntensity
)


@dataclass
class GeneratedExpression:
    """生成的表达内容"""
    content: str
    emotion_type: EmotionType
    style: str
    confidence: float
    appropriateness: float
    creativity_score: float
    yanran_personality_fit: float


class EmotionalExpressionGenerator:
    """情感表达生成器"""
    
    def __init__(self):
        self.logger = get_unified_logger(__name__)
        
        # 林嫣然的表达风格特征
        self.yanran_expression_traits = {
            "warmth_words": ["呢", "哦", "呀", "~", "💕", "😊", "🤗"],
            "professional_words": ["专业", "分析", "建议", "观点", "经验"],
            "empathy_words": ["理解", "感受", "体会", "陪着", "支持"],
            "curiosity_words": ["好奇", "想知道", "有趣", "为什么", "怎么样"],
            "excitement_words": ["太棒了", "激动", "期待", "兴奋", "开心"],
            "concern_words": ["担心", "关心", "照顾", "注意", "小心"]
        }
        
        # 基础表达模板
        self.basic_templates = {
            EmotionType.JOY: {
                "low": ["真的很开心呢~", "心情不错哦", "感觉很棒"],
                "medium": ["太开心了！", "真的很兴奋呢！", "心情超级好！"],
                "high": ["简直太棒了！！", "开心到飞起来！", "激动得不行！"]
            },
            EmotionType.EMPATHY: {
                "low": ["我理解你的感受", "能感受到你的心情", "我懂你的意思"],
                "medium": ["我很能理解你现在的感受", "真的很能体会你的心情", "我完全理解你的想法"],
                "high": ["我真的很理解你现在的感受", "能深深感受到你的情感"]
            },
            EmotionType.CURIOSITY: {
                "low": ["有点好奇呢", "想了解一下", "挺有意思的"],
                "medium": ["好奇心被勾起来了！", "真的很想知道", "这个话题很吸引我"],
                "high": ["太好奇了！快告诉我！", "迫不及待想知道更多！"]
            },
            EmotionType.CONCERN: {
                "low": ["有点担心呢", "稍微关心一下", "希望你还好"],
                "medium": ["挺担心你的", "很关心你的情况", "希望你一切都好"],
                "high": ["真的很担心你！", "非常关心你的状况！"]
            },
            EmotionType.EXCITEMENT: {
                "low": ["有点小兴奋", "感觉挺不错", "心情还可以"],
                "medium": ["真的很兴奋！", "太期待了！", "心情很激动！"],
                "high": ["兴奋得不行！", "激动到睡不着！"]
            }
        }
    
    async def generate_emotional_expression(self, 
                                          emotion_state: EmotionalState,
                                          relationship_type: str,
                                          context: Dict[str, Any],
                                          topic: str = "") -> GeneratedExpression:
        """生成情感化表达内容"""
        try:
            # 确定强度级别
            intensity_level = self._determine_intensity_level(emotion_state.intensity)
            
            # 获取基础表达模板
            base_expression = await self._get_base_expression(
                emotion_state.primary_emotion, 
                relationship_type, 
                intensity_level
            )
            
            # 添加个性化元素
            personalized_expression = await self._add_personality_elements(
                base_expression, emotion_state, context
            )
            
            # 添加上下文相关内容
            contextualized_expression = await self._add_contextual_elements(
                personalized_expression, context, topic
            )
            
            # 添加林嫣然特色表达
            yanran_expression = await self._add_yanran_style(
                contextualized_expression, emotion_state, relationship_type
            )
            
            # 评估生成质量
            confidence = await self._evaluate_confidence(yanran_expression, emotion_state)
            appropriateness = await self._evaluate_appropriateness(yanran_expression, relationship_type)
            creativity = await self._evaluate_creativity(yanran_expression, base_expression)
            personality_fit = await self._evaluate_personality_fit(yanran_expression)
            
            return GeneratedExpression(
                content=yanran_expression,
                emotion_type=emotion_state.primary_emotion,
                style=self._determine_expression_style(relationship_type, emotion_state),
                confidence=confidence,
                appropriateness=appropriateness,
                creativity_score=creativity,
                yanran_personality_fit=personality_fit
            )
            
        except Exception as e:
            self.logger.error(f"情感表达生成失败: {e}")
            return await self._generate_fallback_expression(emotion_state, relationship_type)
    
    def _determine_intensity_level(self, intensity: float) -> str:
        """确定强度级别"""
        if intensity < 0.4:
            return "low"
        elif intensity < 0.7:
            return "medium"
        else:
            return "high"
    
    async def _get_base_expression(self, emotion: EmotionType, 
                                 relationship: str, intensity: str) -> str:
        """获取基础表达模板"""
        try:
            if emotion in self.basic_templates:
                templates = self.basic_templates[emotion]
                if intensity in templates:
                    return random.choice(templates[intensity])
            
            # 回退到通用模板
            return self._get_fallback_template(emotion)
            
        except Exception as e:
            self.logger.error(f"获取基础表达失败: {e}")
            return "我能理解你的感受"
    
    def _get_fallback_template(self, emotion: EmotionType) -> str:
        """获取回退模板"""
        fallback_templates = {
            EmotionType.JOY: "真的很为你高兴呢！",
            EmotionType.EMPATHY: "我能理解你的感受",
            EmotionType.CURIOSITY: "这个话题很有意思呢",
            EmotionType.CONCERN: "希望你一切都好",
            EmotionType.EXCITEMENT: "听起来很令人期待！",
            EmotionType.WARMTH: "我在这里陪着你"
        }
        
        return fallback_templates.get(emotion, "我理解你的想法")
    
    async def _add_personality_elements(self, base_expression: str, 
                                      emotion_state: EmotionalState,
                                      context: Dict[str, Any]) -> str:
        """添加个性化元素"""
        # 根据情感类型添加对应的个性化词汇
        personality_words = []
        
        if emotion_state.primary_emotion == EmotionType.JOY:
            personality_words.extend(self.yanran_expression_traits["warmth_words"][:2])
        elif emotion_state.primary_emotion == EmotionType.EMPATHY:
            personality_words.extend(self.yanran_expression_traits["empathy_words"][:1])
        elif emotion_state.primary_emotion == EmotionType.CURIOSITY:
            personality_words.extend(self.yanran_expression_traits["curiosity_words"][:1])
        elif emotion_state.primary_emotion == EmotionType.CONCERN:
            personality_words.extend(self.yanran_expression_traits["concern_words"][:1])
        elif emotion_state.primary_emotion == EmotionType.EXCITEMENT:
            personality_words.extend(self.yanran_expression_traits["excitement_words"][:1])
        
        # 智能添加个性化元素
        if personality_words and random.random() < 0.3:
            word = random.choice(personality_words)
            if word not in base_expression:
                if word in ["呢", "哦", "呀", "~"]:
                    base_expression += word
                else:
                    base_expression = base_expression.replace("。", f"，{word}。")
        
        return base_expression
    
    async def _add_contextual_elements(self, expression: str, 
                                     context: Dict[str, Any], topic: str) -> str:
        """添加上下文相关内容"""
        # 根据时间添加上下文
        current_hour = datetime.now().hour
        time_context = ""
        
        if current_hour < 6 or current_hour > 23:
            time_context = "这么晚还没休息呀，"
        
        # 根据话题添加专业元素
        if topic and any(word in topic.lower() for word in ["投资", "理财", "股票", "基金", "财经"]):
            professional_element = random.choice([
                "从财经角度来看，", "作为财经从业者，", "专业上来说，"
            ])
            if random.random() < 0.4:
                expression = professional_element + expression.lower()
        
        # 组合时间上下文
        if time_context and random.random() < 0.3:
            expression = time_context + expression
        
        return expression
    
    async def _add_yanran_style(self, expression: str, 
                              emotion_state: EmotionalState,
                              relationship_type: str) -> str:
        """添加林嫣然特色表达风格"""
        # 根据关系类型调整表达风格
        if relationship_type in ["好友", "闺蜜"]:
            if random.random() < 0.4:
                playful_elements = ["哈哈", "嘻嘻", "哎呀", "哇塞"]
                if not any(elem in expression for elem in playful_elements):
                    element = random.choice(playful_elements)
                    expression = expression.replace("！", f"！{element}，")
        
        elif relationship_type == "陌生人":
            if "你" in expression and random.random() < 0.6:
                expression = expression.replace("你", "您")
        
        # 添加温暖特质
        if emotion_state.primary_emotion in [EmotionType.EMPATHY, EmotionType.CONCERN, EmotionType.WARMTH]:
            warmth_elements = ["❤️", "🤗", "💕"]
            if random.random() < 0.2:
                expression += random.choice(warmth_elements)
        
        return expression
    
    async def _evaluate_confidence(self, expression: str, emotion_state: EmotionalState) -> float:
        """评估表达的置信度"""
        base_confidence = 0.7
        base_confidence += emotion_state.confidence * 0.2
        
        if 10 <= len(expression) <= 50:
            base_confidence += 0.1
        
        return min(1.0, base_confidence)
    
    async def _evaluate_appropriateness(self, expression: str, relationship_type: str) -> float:
        """评估表达的合适性"""
        base_score = 0.8
        
        if relationship_type == "陌生人":
            if "您" in expression:
                base_score += 0.1
        elif relationship_type in ["好友", "闺蜜"]:
            if any(word in expression for word in ["哈哈", "嘻嘻", "哇塞"]):
                base_score += 0.1
        
        return min(1.0, base_score)
    
    async def _evaluate_creativity(self, expression: str, base_expression: str) -> float:
        """评估表达的创造性"""
        if expression == base_expression:
            return 0.3
        
        added_elements = len(expression) - len(base_expression)
        creativity_score = 0.5 + min(added_elements / 20.0, 0.4)
        
        creative_elements = ["❤️", "🤗", "💕", "哈哈", "嘻嘻", "哇塞", "呢", "呀", "~"]
        creative_count = sum(1 for elem in creative_elements if elem in expression)
        creativity_score += creative_count * 0.05
        
        return min(1.0, creativity_score)
    
    async def _evaluate_personality_fit(self, expression: str) -> float:
        """评估人格匹配度"""
        base_score = 0.8
        
        yanran_traits = ["温暖", "体贴", "专业", "开朗", "细腻", "理解", "陪着", "关心"]
        trait_count = sum(1 for trait in yanran_traits if trait in expression)
        base_score += trait_count * 0.03
        
        if any(word in expression for word in ["呢", "哦", "呀", "~"]):
            base_score += 0.05
        
        if any(word in expression for word in ["专业", "财经", "分析"]):
            base_score += 0.05
        
        return min(1.0, base_score)
    
    def _determine_expression_style(self, relationship_type: str, emotion_state: EmotionalState) -> str:
        """确定表达风格"""
        style_map = {
            ("陌生人", EmotionType.EMPATHY): "礼貌关怀",
            ("陌生人", EmotionType.JOY): "礼貌祝贺",
            ("熟人", EmotionType.WARMTH): "友好温暖",
            ("朋友", EmotionType.CONCERN): "关心体贴",
            ("好友", EmotionType.EXCITEMENT): "兴奋互动",
            ("闺蜜", EmotionType.JOY): "俏皮开心"
        }
        
        return style_map.get((relationship_type, emotion_state.primary_emotion), "温暖自然")
    
    async def _generate_fallback_expression(self, emotion_state: EmotionalState, 
                                          relationship_type: str) -> GeneratedExpression:
        """生成回退表达"""
        fallback_content = "我能理解你的感受，我在这里陪着你"
        
        return GeneratedExpression(
            content=fallback_content,
            emotion_type=emotion_state.primary_emotion,
            style="温暖关怀",
            confidence=0.6,
            appropriateness=0.8,
            creativity_score=0.3,
            yanran_personality_fit=0.9
        )