#!/usr/bin/env python3
"""
情感调节模块 - Emotion Regulation

该模块实现了数字生命体的情感调节功能，能够平衡极端情绪，
提供情感稳定性，并根据场景和目标调整情感表达。

作者: Claude
创建日期: 2024-07-15
版本: 1.0
"""

import os
import sys
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import time
import math
from typing import Dict, Any, List, Optional, Tuple, Union
from collections import deque

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase

# 尝试导入事件总线
try:
    from core.enhanced_event_bus import get_instance as get_event_bus
except ImportError:
    try:
        from core.event_bus import get_instance as get_event_bus
    except ImportError:
        logger = get_unified_logger(__name__)
        logger.warning_status("无法导入事件总线，将使用模拟版本")
        
        class DummyEventBus:
            def publish(self, *args, **kwargs): pass
            def subscribe(self, *args, **kwargs): pass
            
        def get_event_bus():
            return DummyEventBus()

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.emotion.regulation")

class EmotionRegulation(CognitiveModuleBase):
    """
    情感调节模块
    
    负责调节和平衡数字生命体的情感状态，提供情感稳定性，
    并根据环境和目标动态调整情感表达。主要功能包括：
    
    1. 情感平衡：防止极端情感状态持续过长
    2. 情感适应：根据环境和情境调整情感反应
    3. 情感控制：根据目标和需要抑制或增强特定情感
    4. 情感恢复：在情感创伤后促进恢复
    """
    
    _instance = None
    
    @classmethod
    def get_instance(cls, config: Dict[str, Any] = None) -> 'EmotionRegulation':
        """
        获取模块单例实例
        
        参数:
            config: 配置信息
            
        返回:
            EmotionRegulation: 模块实例
        """
        if cls._instance is None:
            cls._instance = EmotionRegulation("emotion_regulation", "emotion", config)
        return cls._instance
    
    def __init__(self, module_id: str, module_type: str, config: Dict[str, Any] = None):
        """
        初始化情感调节模块
        
        参数:
            module_id: 模块ID
            module_type: 模块类型
            config: 配置信息
        """
        super().__init__(module_id, module_type, config)
        
        # 设置模块描述
        self.description = "情感调节模块 - 负责平衡情感状态并提供情感适应能力"
        
        # 获取事件总线
        self.event_bus = get_event_bus()
        
        # 默认配置
        self.default_config = {
            "recovery_rate": 0.05,           # 自然恢复率（每次更新）
            "max_intensity": 1.0,            # 最大情感强度
            "min_intensity": 0.0,            # 最小情感强度
            "baseline_update_rate": 0.01,    # 基线更新率
            "regulation_strength": 0.3,      # 调节强度
            "emotion_memory_size": 20,       # 情感记忆大小
            "extreme_threshold": 0.8,        # 极端情感阈值
            "emotion_inertia": 0.7,          # 情感惯性（0-1之间）
            "context_influence": 0.5,        # 环境影响因子
            "personality_influence": 0.4     # 人格影响因子
        }
        
        # 加载配置
        self.regulation_config = self.default_config.copy()
        if config:
            self.regulation_config.update(config)
        
        # 情感状态
        self.current_emotion = {
            "dominant_emotion": "平静",
            "intensity": 0.5,
            "polarity": 0.0,
            "arousal": 0.5,        # 唤起度
            "valence": 0.0         # 效价
        }
        
        # 情感基线（长期平均）
        self.emotion_baseline = {
            "polarity": 0.0,
            "arousal": 0.5,
            "valence": 0.0
        }
        
        # 情感历史记录
        self.emotion_history = deque(maxlen=self.regulation_config["emotion_memory_size"])
        
        # 特殊场景调节规则
        self.context_rules = {
            "formal_meeting": {"regulation_strength": 0.7, "target_arousal": 0.3},
            "social_event": {"regulation_strength": 0.2, "target_arousal": 0.6},
            "crisis": {"regulation_strength": 0.5, "target_arousal": 0.8},
            "relaxation": {"regulation_strength": 0.1, "target_arousal": 0.3}
        }
        
        # 人格特质影响
        self.personality_traits = {
            "emotional_stability": 0.6,    # 情绪稳定性
            "expressiveness": 0.5,         # 表达性
            "sensitivity": 0.4,            # 敏感性
            "resilience": 0.7              # 恢复力
        }
        
        # 标记模块已创建
        logger.info("情感调节模块已创建")
    
    def _do_initialize(self) -> None:
        """
        执行初始化逻辑
        """
        try:
            # 订阅相关事件
            self._subscribe_events()
            
            # 加载历史情感状态
            self._load_emotion_state()
            
            # 从配置中更新人格特质
            if "personality_traits" in self.config:
                self.personality_traits.update(self.config["personality_traits"])
            
            # 从配置中更新场景规则
            if "context_rules" in self.config:
                self.context_rules.update(self.config["context_rules"])
            
            # 发布模块初始化事件
            self.event_bus.publish(
                "emotion.regulation.initialized",
                {
                    "module_id": self.module_id,
                    "time": time.time()
                },
                source=self.module_id
            )
            
            logger.success("情感调节模块初始化完成")
        except Exception as e:
            logger.error_status(f"情感调节模块初始化失败: {str(e)}")
            raise
    
    def _subscribe_events(self) -> None:
        """
        订阅相关事件
        """
        # 情感变化事件
        self.event_bus.subscribe("emotion.state.changed", self._handle_emotion_change)
        
        # 环境变化事件
        self.event_bus.subscribe("environment.context.changed", self._handle_context_change)
        
        # 人格特质更新事件
        self.event_bus.subscribe("personality.traits.updated", self._handle_personality_update)
        
        # 系统关闭事件
        self.event_bus.subscribe("system.shutdown", self._handle_system_shutdown)
    
    def _handle_emotion_change(self, event_data: Dict[str, Any]) -> None:
        """
        处理情感变化事件
        
        参数:
            event_data: 事件数据
        """
        if "emotion_state" in event_data:
            emotion_state = event_data["emotion_state"]
            
            # 记录原始情感状态
            self._record_emotion_state(emotion_state)
            
            # 应用情感调节
            regulated_emotion = self.regulate_emotion(emotion_state)
            
            # 发布调节后的情感状态
            self.event_bus.publish(
                "emotion.regulation.applied",
                {
                    "original_emotion": emotion_state,
                    "regulated_emotion": regulated_emotion,
                    "time": time.time()
                },
                source=self.module_id
            )
    
    def _handle_context_change(self, event_data: Dict[str, Any]) -> None:
        """
        处理环境变化事件
        
        参数:
            event_data: 事件数据
        """
        if "context" in event_data:
            context = event_data["context"]
            
            # 更新情感调节参数
            self._adjust_regulation_for_context(context)
    
    def _handle_personality_update(self, event_data: Dict[str, Any]) -> None:
        """
        处理人格特质更新事件
        
        参数:
            event_data: 事件数据
        """
        if "personality_traits" in event_data:
            traits = event_data["personality_traits"]
            
            # 更新人格特质
            for trait, value in traits.items():
                if trait in self.personality_traits:
                    self.personality_traits[trait] = value
            
            # 根据新的人格特质调整调节参数
            self._adjust_regulation_for_personality()
    
    def _handle_system_shutdown(self, event_data: Dict[str, Any]) -> None:
        """
        处理系统关闭事件
        
        参数:
            event_data: 事件数据
        """
        # 保存情感状态
        self._save_emotion_state()
    
    def _do_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        根据输入的操作类型执行不同的处理。
        
        参数:
            input_data: 输入数据，包含操作类型和相关参数
            
        返回:
            Dict[str, Any]: 处理结果
        """
        operation = input_data.get("operation", "")
        
        if operation == "regulate_emotion":
            if "emotion_state" in input_data:
                return {
                    "result": self.regulate_emotion(input_data["emotion_state"])
                }
        
        elif operation == "update_regulation_params":
            if "params" in input_data:
                success = self.update_regulation_params(input_data["params"])
                return {
                    "success": success
                }
        
        elif operation == "get_current_state":
            return {
                "result": self.get_current_state()
            }
        
        elif operation == "set_context":
            if "context" in input_data:
                success = self._adjust_regulation_for_context(input_data["context"])
                return {
                    "success": success
                }
        
        return {
            "error": "unknown_operation",
            "message": f"未知的操作: {operation}"
        }
    
    def regulate_emotion(self, emotion_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        调节情感状态
        
        根据当前配置和规则，对情感状态进行调节。
        
        参数:
            emotion_state: 原始情感状态
            
        返回:
            Dict[str, Any]: 调节后的情感状态
        """
        # 复制输入状态，避免修改原始数据
        regulated = emotion_state.copy()
        
        # 获取关键情感参数
        intensity = regulated.get("intensity", 0.5)
        polarity = regulated.get("polarity", 0.0)
        arousal = regulated.get("arousal", 0.5)
        valence = regulated.get("valence", 0.0)
        
        # 应用情感惯性（与历史情感混合）
        if self.emotion_history:
            last_emotion = self.emotion_history[-1]
            inertia = self.regulation_config["emotion_inertia"]
            
            intensity = intensity * (1 - inertia) + last_emotion.get("intensity", 0.5) * inertia
            polarity = polarity * (1 - inertia) + last_emotion.get("polarity", 0.0) * inertia
            arousal = arousal * (1 - inertia) + last_emotion.get("arousal", 0.5) * inertia
            valence = valence * (1 - inertia) + last_emotion.get("valence", 0.0) * inertia
        
        # 极端情感调节
        extreme_threshold = self.regulation_config["extreme_threshold"]
        if abs(polarity) > extreme_threshold or abs(arousal - 0.5) > extreme_threshold / 2:
            # 计算调节力度
            regulation_strength = self.regulation_config["regulation_strength"]
            
            # 调节极端情感
            polarity = polarity * (1 - regulation_strength) + self.emotion_baseline["polarity"] * regulation_strength
            arousal = arousal * (1 - regulation_strength) + self.emotion_baseline["arousal"] * regulation_strength
            valence = valence * (1 - regulation_strength) + self.emotion_baseline["valence"] * regulation_strength
            
            # 强度调节
            intensity_regulation = min(regulation_strength, max(0, (intensity - 0.5)) * 2 * regulation_strength)
            intensity = intensity * (1 - intensity_regulation)
        
        # 应用人格特质影响
        emotional_stability = self.personality_traits["emotional_stability"]
        expressiveness = self.personality_traits["expressiveness"]
        
        # 稳定性影响情感波动
        stability_effect = (emotional_stability - 0.5) * 2 * self.regulation_config["personality_influence"]
        polarity = polarity * (1 - stability_effect)
        arousal = 0.5 + (arousal - 0.5) * (1 - stability_effect)
        
        # 表达性影响情感强度
        intensity = intensity * (0.5 + expressiveness / 2)
        
        # 确保参数在有效范围内
        intensity = max(self.regulation_config["min_intensity"], 
                        min(self.regulation_config["max_intensity"], intensity))
        polarity = max(-1.0, min(1.0, polarity))
        arousal = max(0.0, min(1.0, arousal))
        valence = max(-1.0, min(1.0, valence))
        
        # 更新调节后的情感状态
        regulated["intensity"] = intensity
        regulated["polarity"] = polarity
        regulated["arousal"] = arousal
        regulated["valence"] = valence
        
        # 更新当前情感状态
        self.current_emotion = regulated.copy()
        
        # 更新情感基线（缓慢调整）
        self._update_emotion_baseline(regulated)
        
        return regulated
    
    def update_regulation_params(self, params: Dict[str, Any]) -> bool:
        """
        更新情感调节参数
        
        参数:
            params: 新的参数值
            
        返回:
            bool: 更新是否成功
        """
        try:
            # 更新配置
            for key, value in params.items():
                if key in self.regulation_config:
                    self.regulation_config[key] = value
            
            # 更新情感记忆大小
            if "emotion_memory_size" in params:
                new_size = params["emotion_memory_size"]
                old_size = self.emotion_history.maxlen
                
                if new_size != old_size:
                    # 创建新的队列
                    new_history = deque(self.emotion_history, maxlen=new_size)
                    self.emotion_history = new_history
            
            logger.info(f"情感调节参数已更新: {params}")
            return True
        except Exception as e:
            logger.error_status(f"更新情感调节参数失败: {str(e)}")
            return False
    
    def get_current_state(self) -> Dict[str, Any]:
        """
        获取当前情感调节状态
        
        返回:
            Dict[str, Any]: 当前状态
        """
        return {
            "current_emotion": self.current_emotion,
            "emotion_baseline": self.emotion_baseline,
            "regulation_params": self.regulation_config,
            "personality_traits": self.personality_traits
        }
    
    def _record_emotion_state(self, emotion_state: Dict[str, Any]) -> None:
        """
        记录情感状态到历史
        
        参数:
            emotion_state: 情感状态
        """
        # 添加时间戳
        state_with_timestamp = emotion_state.copy()
        state_with_timestamp["timestamp"] = time.time()
        
        # 添加到历史
        self.emotion_history.append(state_with_timestamp)
    
    def _update_emotion_baseline(self, emotion_state: Dict[str, Any]) -> None:
        """
        更新情感基线
        
        参数:
            emotion_state: 当前情感状态
        """
        # 计算基线更新率
        update_rate = self.regulation_config["baseline_update_rate"]
        
        # 更新基线
        self.emotion_baseline["polarity"] = (1 - update_rate) * self.emotion_baseline["polarity"] + \
                                            update_rate * emotion_state.get("polarity", 0.0)
        
        self.emotion_baseline["arousal"] = (1 - update_rate) * self.emotion_baseline["arousal"] + \
                                           update_rate * emotion_state.get("arousal", 0.5)
        
        self.emotion_baseline["valence"] = (1 - update_rate) * self.emotion_baseline["valence"] + \
                                           update_rate * emotion_state.get("valence", 0.0)
    
    def _adjust_regulation_for_context(self, context: Dict[str, Any]) -> bool:
        """
        根据环境调整情感调节参数
        
        参数:
            context: 环境信息
            
        返回:
            bool: 调整是否成功
        """
        try:
            # 获取场景类型
            context_type = context.get("type", "")
            
            # 应用场景规则
            if context_type in self.context_rules:
                rule = self.context_rules[context_type]
                
                # 更新调节参数
                for key, value in rule.items():
                    if key in self.regulation_config:
                        # 计算环境影响因子
                        influence = self.regulation_config["context_influence"]
                        
                        # 应用调整
                        current_value = self.regulation_config[key]
                        new_value = (1 - influence) * current_value + influence * value
                        
                        # 更新参数
                        self.regulation_config[key] = new_value
            
            logger.info(f"已根据环境 '{context_type}' 调整情感调节参数")
            return True
        except Exception as e:
            logger.error_status(f"根据环境调整情感调节参数失败: {str(e)}")
            return False
    
    def _adjust_regulation_for_personality(self) -> None:
        """
        根据人格特质调整情感调节参数
        """
        # 情绪稳定性影响调节强度
        stability = self.personality_traits["emotional_stability"]
        self.regulation_config["regulation_strength"] = 0.3 + (stability - 0.5) * 0.2
        
        # 恢复力影响恢复率
        resilience = self.personality_traits["resilience"]
        self.regulation_config["recovery_rate"] = 0.05 + (resilience - 0.5) * 0.05
    
    def _load_emotion_state(self) -> None:
        """
        加载情感状态
        """
        # 实际实现可能会从文件或数据库加载
        pass
    
    def _save_emotion_state(self) -> None:
        """
        保存情感状态
        """
        # 实际实现可能会保存到文件或数据库
        pass
    
    def _do_update(self, context: Dict[str, Any]) -> None:
        """
        执行状态更新逻辑
        
        参数:
            context: 上下文信息
        """
        # 自然情感恢复过程
        self._natural_emotion_recovery()
        
        # 如果上下文包含环境信息，应用环境调节
        if "environment" in context:
            self._adjust_regulation_for_context(context["environment"])
    
    def _natural_emotion_recovery(self) -> None:
        """
        自然情感恢复过程
        
        随着时间推移，情感会自然地向基线值恢复
        """
        # 获取恢复率
        recovery_rate = self.regulation_config["recovery_rate"]
        
        # 仅当有激活的情感状态时才进行恢复
        if "intensity" in self.current_emotion and self.current_emotion["intensity"] > 0:
            # 逐渐降低情感强度
            self.current_emotion["intensity"] *= (1 - recovery_rate)
            
            # 逐渐恢复到基线值
            self.current_emotion["polarity"] = (1 - recovery_rate) * self.current_emotion["polarity"] + \
                                               recovery_rate * self.emotion_baseline["polarity"]
            
            self.current_emotion["arousal"] = (1 - recovery_rate) * self.current_emotion["arousal"] + \
                                              recovery_rate * self.emotion_baseline["arousal"]
            
            self.current_emotion["valence"] = (1 - recovery_rate) * self.current_emotion["valence"] + \
                                              recovery_rate * self.emotion_baseline["valence"]
    
    def _do_shutdown(self) -> None:
        """
        执行关闭逻辑
        """
        # 保存情感状态
        self._save_emotion_state()
        
        # 发布模块关闭事件
        self.event_bus.publish(
            "emotion.regulation.shutdown",
            {
                "module_id": self.module_id,
                "time": time.time()
            },
            source=self.module_id
        )
        
        logger.info("情感调节模块已关闭")

# 模块单例获取函数
def get_instance(config: Dict[str, Any] = None) -> EmotionRegulation:
    """
    获取模块实例
    
    参数:
        config: 配置信息
        
    返回:
        EmotionRegulation: 模块实例
    """
    return EmotionRegulation.get_instance(config) 