#!/usr/bin/env python3
"""
情感分析模块 - Emotion Analyzer

该模块实现了数字生命体的情感分析功能，能够从文本中识别情感倾向。
采用模型推理和规则分析相结合的方式，实现多维度情感分析。

作者: Claude
创建日期: 2024-07-08
版本: 1.0
"""

import os
import sys
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import time
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from collections import defaultdict

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "..", ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.emotion.analyzer")

class EmotionAnalyzer(CognitiveModuleBase):
    """
    情感分析模块
    
    从文本中分析情感倾向，包括基本情绪、情感强度和情感变化趋势。
    支持中文和英文文本的情感分析。
    """
    
    def __init__(self, module_id: str, module_name: str, module_type: str):
        """
        初始化情感分析模块

        参数:
            module_id: 模块唯一标识符
            module_name: 模块名称
            module_type: 模块类型
        """
        # 🔥 老王修复：基类构造函数参数顺序是 (module_id, module_type, config)
        # 而不是 (module_id, module_name, module_type)
        super().__init__(module_id, module_type, {"module_name": module_name})
        
        # 基本情绪类别及其关键词
        self.emotion_categories = {
            "快乐": ["高兴", "开心", "快乐", "喜悦", "欣喜", "愉快", "兴奋", "欢乐", "满足", "幸福"],
            "悲伤": ["悲伤", "难过", "伤心", "痛苦", "忧伤", "哀愁", "悲痛", "沮丧", "失落", "哀伤"],
            "愤怒": ["愤怒", "生气", "恼怒", "气愤", "暴怒", "恼火", "发火", "不满", "烦躁", "狂怒"],
            "恐惧": ["恐惧", "害怕", "惊恐", "惊吓", "担忧", "忧虑", "紧张", "焦虑", "恐慌", "不安"],
            "厌恶": ["厌恶", "讨厌", "反感", "恶心", "憎恨", "嫌弃", "鄙视", "抵触", "厌烦", "痛恨"],
            "惊讶": ["惊讶", "震惊", "吃惊", "惊异", "意外", "震撼", "惊喜", "惊叹", "诧异", "不可思议"],
            "期待": ["期待", "盼望", "希望", "憧憬", "渴望", "企盼", "期盼", "向往", "期许", "憧憬"],
            "信任": ["信任", "相信", "信赖", "依靠", "依赖", "信赖", "放心", "托付", "信服", "信心"],
        }
        
        # 情感强度词汇
        self.intensity_words = {
            "极强": ["极其", "非常", "十分", "格外", "异常", "极度", "极端", "无比", "极为", "尤其"],
            "较强": ["很", "相当", "特别", "颇为", "尤为", "相应", "比较", "更加", "尤为", "格外"],
            "一般": ["有点", "稍微", "略微", "些许", "有些", "稍稍", "略为", "多少", "几分", "略带"],
            "减弱": ["不太", "不怎么", "不很", "不十分", "不特别", "算不上", "称不上", "谈不上", "不算", "难说"],
            "否定": ["不", "没", "无", "非", "莫", "勿", "毋", "未", "别", "甭"],
        }
        
        # 情感极性映射
        self.polarity_map = {
            "快乐": 1.0,
            "悲伤": -1.0,
            "愤怒": -0.8,
            "恐惧": -0.7,
            "厌恶": -0.9,
            "惊讶": 0.3,
            "期待": 0.7,
            "信任": 0.8,
        }
        
        # 情感表达模式（正则表达式）
        self.emotion_patterns = {
            "快乐": r"(?:我|(?:感[到觉]|觉得).*?)(?:开心|高兴|快乐|兴奋|愉快|欢喜|欣慰)",
            "悲伤": r"(?:我|(?:感[到觉]|觉得).*?)(?:难过|伤心|悲伤|忧伤|痛苦|失落|哀伤)",
            "愤怒": r"(?:我|(?:感[到觉]|觉得).*?)(?:生气|愤怒|恼火|不满|火大|发火|气愤)",
            "恐惧": r"(?:我|(?:感[到觉]|觉得).*?)(?:害怕|恐惧|惊恐|担心|忧虑|焦虑|不安)",
            "厌恶": r"(?:我|(?:感[到觉]|觉得).*?)(?:讨厌|厌恶|恶心|憎恨|反感|厌烦|烦躁)",
            "惊讶": r"(?:我|(?:感[到觉]|觉得).*?)(?:惊讶|吃惊|震惊|意外|没想到|惊异|诧异)",
            "期待": r"(?:我|(?:感[到觉]|觉得).*?)(?:期待|希望|盼望|渴望|憧憬|期盼|向往)",
            "信任": r"(?:我|(?:感[到觉]|觉得).*?)(?:信任|相信|依赖|信赖|信心|可靠|放心)",
        }
        
        # 情感历史记录
        self.emotion_history = []
        self.history_max_length = 100
        
        # 情感状态
        self.current_emotion_state = {
            "dominant_emotion": None,
            "emotion_scores": {},
            "emotion_trend": {},
            "overall_polarity": 0,
            "intensity": 0,
            "last_update_time": 0,
        }
        
        # 模型加载状态
        self.model_loaded = False
        self.model = None

        # 🔥 香草修复：初始化生命上下文和事件总线连接
        self.life_context = None
        self.event_bus = None
        self._initialize_dependencies()

        logger.info("情感分析模块已创建")

    def _initialize_dependencies(self):
        """
        🔥 香草修复：初始化依赖项连接
        """
        try:
            # 初始化生命上下文
            try:
                from core.life_context import get_instance as get_life_context
                self.life_context = get_life_context()
                logger.info("✅ 情感分析器已连接生命上下文")
            except Exception as e:
                logger.warning_status(f"⚠️ 生命上下文连接失败: {e}")
                self.life_context = None

            # 初始化事件总线
            try:
                from core.enhanced_event_bus import get_instance as get_event_bus
                self.event_bus = get_event_bus()
                logger.info("✅ 情感分析器已连接事件总线")
            except Exception as e:
                logger.warning_status(f"⚠️ 事件总线连接失败: {e}")
                self.event_bus = None

        except Exception as e:
            logger.error_status(f"❌ 初始化依赖项失败: {e}")

    def _initialize_module(self) -> bool:
        """
        模块特定的初始化逻辑
        
        加载配置和模型，初始化状态。
        
        返回:
            bool: 初始化成功返回True，否则返回False
        """
        try:
            logger.success("初始化情感分析模块")
            
            # 从配置中加载参数
            if "history_max_length" in self.config:
                self.history_max_length = self.config["history_max_length"]
                
            if "emotion_categories" in self.config:
                self.emotion_categories.update(self.config["emotion_categories"])
                
            # 初始化情感状态
            self._reset_emotion_state()
            
            # 尝试加载NLP模型
            if self.config.get("use_nlp_model", False):
                self._load_nlp_model()
            else:
                logger.info("使用规则基础情感分析模式")
                
            # 加载情感历史记录
            self._load_emotion_history()
            
            logger.success("情感分析模块初始化完成")
            return True
            
        except Exception as e:
            logger.error_status(f"情感分析模块初始化失败: {str(e)}")
            return False
            
    def _load_nlp_model(self) -> bool:
        """
        加载NLP模型
        
        根据配置加载指定的NLP模型，用于增强情感分析。
        
        返回:
            bool: 加载成功返回True，否则返回False
        """
        try:
            # 这里应该根据实际情况加载模型
            # 例如使用transformers库加载预训练模型
            # 简化实现，仅做模拟
            logger.info("模拟加载NLP模型")
            
            # 假设已经成功加载模型
            self.model_loaded = True
            
            logger.success("NLP模型加载成功")
            return True
            
        except Exception as e:
            logger.error_status(f"加载NLP模型失败: {str(e)}")
            return False
            
    def _reset_emotion_state(self) -> None:
        """
        重置情感状态
        
        将情感状态重置为初始值。
        """
        # 初始化所有情绪类别的得分为0
        emotion_scores = {emotion: 0.0 for emotion in self.emotion_categories.keys()}
        
        # 初始化情绪趋势
        emotion_trend = {emotion: 0.0 for emotion in self.emotion_categories.keys()}
        
        self.current_emotion_state = {
            "dominant_emotion": None,
            "emotion_scores": emotion_scores,
            "emotion_trend": emotion_trend,
            "overall_polarity": 0.0,
            "intensity": 0.0,
            "last_update_time": time.time(),
        }
        
    def _load_emotion_history(self) -> bool:
        """
        加载情感历史记录
        
        从文件加载情感历史记录。
        
        返回:
            bool: 加载成功返回True，否则返回False
        """
        try:
            history_file = os.path.join(root_dir, "data", "emotion", "history.json")
            
            if not os.path.exists(history_file):
                logger.info("情感历史记录文件不存在，使用空历史")
                return True
                
            with open(history_file, 'r', encoding='utf-8') as f:
                history_data = json.load(f)
                
            if isinstance(history_data, list):
                self.emotion_history = history_data[-self.history_max_length:]
                logger.info(f"已加载 {len(self.emotion_history)} 条情感历史记录")
                return True
            else:
                logger.error_status("情感历史记录格式错误")
                return False
                
        except Exception as e:
            logger.error_status(f"加载情感历史记录失败: {str(e)}")
            return False 

    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        模块特定的处理逻辑
        
        分析输入文本的情感倾向，返回情感分析结果。
        
        参数:
            input_data: 输入数据，包含要分析的文本
            
        返回:
            Dict[str, Any]: 情感分析结果
        """
        try:
            # 提取输入文本
            text = input_data.get("text", "")
            user_id = input_data.get("user_id", "unknown")
            context = input_data.get("context", {})
            
            if not text:
                return {"error": "输入文本为空"}
                
            # 进行情感分析
            if self.model_loaded and len(text) > 10:
                # 使用NLP模型进行分析
                emotion_result = self._analyze_with_model(text)
            else:
                # 使用规则进行分析
                emotion_result = self._analyze_with_rules(text)
                
            # 融合上下文信息
            if context:
                emotion_result = self._fuse_with_context(emotion_result, context)
                
            # 更新情感状态
            self._update_emotion_state(emotion_result)
            
            # 添加到历史记录
            self._add_to_history(emotion_result, text, user_id)
            
            # 发布情感事件
            self._publish_emotion_event(emotion_result)
            
            return emotion_result
            
        except Exception as e:
            logger.error_status(f"情感分析处理失败: {str(e)}")
            return {"error": f"情感分析失败: {str(e)}"}
            
    def _analyze_with_rules(self, text: str) -> Dict[str, Any]:
        """
        使用规则进行情感分析
        
        基于关键词和模式匹配的情感分析方法。
        
        参数:
            text: 输入文本
            
        返回:
            Dict[str, Any]: 情感分析结果
        """
        # 情感得分
        emotion_scores = defaultdict(float)
        
        # 关键词匹配
        for emotion, keywords in self.emotion_categories.items():
            for keyword in keywords:
                if keyword in text:
                    # 检查是否有强度词修饰
                    for intensity, modifiers in self.intensity_words.items():
                        for modifier in modifiers:
                            if f"{modifier}{keyword}" in text:
                                if intensity == "极强":
                                    emotion_scores[emotion] += 2.0
                                elif intensity == "较强":
                                    emotion_scores[emotion] += 1.5
                                elif intensity == "一般":
                                    emotion_scores[emotion] += 1.0
                                elif intensity == "减弱":
                                    emotion_scores[emotion] += 0.5
                                elif intensity == "否定":
                                    # 否定词可能表示相反情绪
                                    emotion_scores[emotion] -= 0.5
                                break
                        else:
                            # 没有找到强度词，使用默认强度
                            emotion_scores[emotion] += 1.0
                            
        # 模式匹配
        for emotion, pattern in self.emotion_patterns.items():
            matches = re.findall(pattern, text)
            if matches:
                emotion_scores[emotion] += len(matches) * 1.5
                
        # 归一化得分
        total_score = sum(emotion_scores.values()) if sum(emotion_scores.values()) > 0 else 1.0
        normalized_scores = {emotion: score / total_score for emotion, score in emotion_scores.items()}
        
        # 找出主导情绪
        dominant_emotion = max(normalized_scores.items(), key=lambda x: x[1])[0] if normalized_scores else None
        
        # 计算整体极性
        overall_polarity = sum(normalized_scores.get(emotion, 0) * self.polarity_map.get(emotion, 0) 
                              for emotion in self.emotion_categories.keys())
        
        # 计算情感强度
        intensity = sum(normalized_scores.values())
        
        return {
            "dominant_emotion": dominant_emotion,
            "emotion_scores": normalized_scores,
            "overall_polarity": overall_polarity,
            "intensity": intensity,
            "timestamp": time.time(),
        }
        
    def _analyze_with_model(self, text: str) -> Dict[str, Any]:
        """
        使用NLP模型进行情感分析
        
        基于预训练模型的情感分析方法。
        
        参数:
            text: 输入文本
            
        返回:
            Dict[str, Any]: 情感分析结果
        """
        # 实际实现中，这里应该调用加载的NLP模型进行分析
        # 简化实现，使用规则分析结果加上随机扰动模拟
        
        # 先用规则分析
        rule_result = self._analyze_with_rules(text)
        
        # 添加随机扰动模拟模型结果
        import random
        
        # 对每个情绪得分添加小的随机扰动
        model_scores = {}
        for emotion, score in rule_result["emotion_scores"].items():
            # 添加-0.1到0.1之间的随机扰动
            noise = random.uniform(-0.1, 0.1)
            model_scores[emotion] = max(0, min(1, score + noise))
            
        # 归一化得分
        total_score = sum(model_scores.values()) if sum(model_scores.values()) > 0 else 1.0
        model_scores = {emotion: score / total_score for emotion, score in model_scores.items()}
        
        # 找出主导情绪
        dominant_emotion = max(model_scores.items(), key=lambda x: x[1])[0] if model_scores else None
        
        # 计算整体极性
        overall_polarity = sum(model_scores.get(emotion, 0) * self.polarity_map.get(emotion, 0) 
                              for emotion in self.emotion_categories.keys())
        
        # 计算情感强度
        intensity = sum(model_scores.values())
        
        return {
            "dominant_emotion": dominant_emotion,
            "emotion_scores": model_scores,
            "overall_polarity": overall_polarity,
            "intensity": intensity,
            "timestamp": time.time(),
            "model_based": True,
        }
        
    def _fuse_with_context(self, emotion_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        融合上下文信息
        
        将情感分析结果与上下文信息融合，增强分析准确性。
        
        参数:
            emotion_result: 情感分析结果
            context: 上下文信息
            
        返回:
            Dict[str, Any]: 融合后的情感分析结果
        """
        # 如果上下文中有用户已知情绪状态，可以考虑这些信息
        if "known_emotion" in context:
            known_emotion = context["known_emotion"]
            emotion_scores = emotion_result["emotion_scores"]
            
            # 增强已知情绪的权重
            if known_emotion in emotion_scores:
                emotion_scores[known_emotion] = emotion_scores[known_emotion] * 1.2
                
                # 重新归一化
                total = sum(emotion_scores.values())
                if total > 0:
                    emotion_scores = {emotion: score / total for emotion, score in emotion_scores.items()}
                    
                # 更新主导情绪
                dominant_emotion = max(emotion_scores.items(), key=lambda x: x[1])[0] if emotion_scores else None
                
                emotion_result["dominant_emotion"] = dominant_emotion
                emotion_result["emotion_scores"] = emotion_scores
                
        # 考虑时间因素
        if "time_of_day" in context:
            time_of_day = context["time_of_day"]
            # 可以根据一天中的不同时段调整情感极性
            # 例如，晚上可能更容易感到疲倦或平静
            
        return emotion_result
        
    def _update_emotion_state(self, emotion_result: Dict[str, Any]) -> None:
        """
        更新情感状态
        
        根据最新的情感分析结果更新内部情感状态。
        
        参数:
            emotion_result: 情感分析结果
        """
        current_time = time.time()
        
        # 计算时间间隔
        time_diff = current_time - self.current_emotion_state["last_update_time"]
        
        # 时间衰减因子
        decay_factor = max(0, min(1, 0.9 ** (time_diff / 3600)))  # 每小时衰减到90%
        
        # 更新情绪得分
        for emotion, score in emotion_result["emotion_scores"].items():
            # 当前得分 = 旧得分 * 衰减因子 + 新得分 * (1 - 衰减因子)
            old_score = self.current_emotion_state["emotion_scores"].get(emotion, 0.0)
            new_score = old_score * decay_factor + score * (1 - decay_factor)
            self.current_emotion_state["emotion_scores"][emotion] = new_score
            
            # 计算情绪趋势 (变化率)
            if old_score > 0:
                trend = (new_score - old_score) / old_score
            else:
                trend = new_score
                
            self.current_emotion_state["emotion_trend"][emotion] = trend
            
        # 更新主导情绪
        dominant_emotion = max(
            self.current_emotion_state["emotion_scores"].items(), 
            key=lambda x: x[1]
        )[0] if self.current_emotion_state["emotion_scores"] else None
        
        self.current_emotion_state["dominant_emotion"] = dominant_emotion
        
        # 更新整体极性
        overall_polarity = sum(
            self.current_emotion_state["emotion_scores"].get(emotion, 0) * 
            self.polarity_map.get(emotion, 0) 
            for emotion in self.emotion_categories.keys()
        )
        
        self.current_emotion_state["overall_polarity"] = overall_polarity
        
        # 更新情感强度
        intensity = sum(self.current_emotion_state["emotion_scores"].values())
        self.current_emotion_state["intensity"] = intensity
        
        # 更新时间戳
        self.current_emotion_state["last_update_time"] = current_time
        
        # 更新生命上下文中的情感状态
        self.life_context.update_context("emotion.current_state", self.current_emotion_state)
        
    def _add_to_history(self, emotion_result: Dict[str, Any], text: str, user_id: str) -> None:
        """
        添加到情感历史记录
        
        将情感分析结果添加到历史记录中。
        
        参数:
            emotion_result: 情感分析结果
            text: 原始文本
            user_id: 用户ID
        """
        # 创建历史记录条目
        history_item = {
            "timestamp": emotion_result["timestamp"],
            "dominant_emotion": emotion_result["dominant_emotion"],
            "overall_polarity": emotion_result["overall_polarity"],
            "intensity": emotion_result["intensity"],
            "text_snippet": text[:100] + "..." if len(text) > 100 else text,
            "user_id": user_id,
        }
        
        # 添加到历史记录
        self.emotion_history.append(history_item)
        
        # 限制历史记录长度
        if len(self.emotion_history) > self.history_max_length:
            self.emotion_history = self.emotion_history[-self.history_max_length:]
            
        # 保存历史记录到文件
        self._save_emotion_history()
        
    def _save_emotion_history(self) -> bool:
        """
        保存情感历史记录
        
        将情感历史记录保存到文件。
        
        返回:
            bool: 保存成功返回True，否则返回False
        """
        try:
            # 确保目录存在
            save_dir = os.path.join(root_dir, "data", "emotion")
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
                
            # 保存到文件
            history_file = os.path.join(save_dir, "history.json")
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.emotion_history, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            logger.error_status(f"保存情感历史记录失败: {str(e)}")
            return False
            
    def _publish_emotion_event(self, emotion_result: Dict[str, Any]) -> None:
        """
        发布情感事件
        
        将情感分析结果发布为事件，供其他模块订阅。
        
        参数:
            emotion_result: 情感分析结果
        """
        # 获取主导情绪
        dominant_emotion = emotion_result["dominant_emotion"]
        
        if dominant_emotion:
            # 发布情感更新事件
            self.event_bus.publish(
                "emotion.updated",
                {
                    "dominant_emotion": dominant_emotion,
                    "emotion_scores": emotion_result["emotion_scores"],
                    "overall_polarity": emotion_result["overall_polarity"],
                    "intensity": emotion_result["intensity"],
                    "timestamp": emotion_result["timestamp"],
                },
                source=self.module_id
            )
            
            # 如果情感强度较高，可能需要发布特殊事件
            if emotion_result["intensity"] > 0.7:
                self.event_bus.publish(
                    "emotion.high_intensity",
                    {
                        "dominant_emotion": dominant_emotion,
                        "intensity": emotion_result["intensity"],
                    },
                    source=self.module_id
                )
                
    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        模块特定的更新逻辑
        
        根据上下文信息更新模块的内部状态。
        
        参数:
            context: 上下文信息
            
        返回:
            bool: 更新成功返回True，否则返回False
        """
        try:
            # 检查是否需要更新配置
            if "emotion_config" in context:
                emotion_config = context["emotion_config"]
                
                if "history_max_length" in emotion_config:
                    self.history_max_length = emotion_config["history_max_length"]
                    
                if "emotion_categories" in emotion_config:
                    self.emotion_categories.update(emotion_config["emotion_categories"])
                    
            # 检查是否需要重置情感状态
            if context.get("reset_emotion_state", False):
                self._reset_emotion_state()
                logger.info("情感状态已重置")
                
            return True
            
        except Exception as e:
            logger.error_status(f"更新情感分析模块失败: {str(e)}")
            return False
            
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块特定的状态信息
        
        返回模块特定的状态信息。
        
        返回:
            Dict[str, Any]: 模块特定的状态信息
        """
        return {
            "current_emotion_state": self.current_emotion_state,
            "history_length": len(self.emotion_history),
            "model_loaded": self.model_loaded,
            "config": {
                "history_max_length": self.history_max_length,
                "emotion_categories": {k: v for k, v in self.emotion_categories.items()},
            }
        }
        
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        加载模块特定的状态信息
        
        加载模块特定的状态信息。
        
        参数:
            state: 模块特定的状态信息
            
        返回:
            bool: 加载成功返回True，否则返回False
        """
        try:
            # 加载情感状态
            if "current_emotion_state" in state:
                self.current_emotion_state = state["current_emotion_state"]
                
            # 加载配置
            if "config" in state:
                config = state["config"]
                
                if "history_max_length" in config:
                    self.history_max_length = config["history_max_length"]
                    
                if "emotion_categories" in config:
                    self.emotion_categories.update(config["emotion_categories"])
                    
            return True
            
        except Exception as e:
            logger.error_status(f"加载情感分析模块状态失败: {str(e)}")
            return False
            
    def _shutdown_module(self) -> bool:
        """
        模块特定的关闭逻辑
        
        完成模块特定的资源释放工作。
        
        返回:
            bool: 关闭成功返回True，否则返回False
        """
        try:
            # 保存情感历史记录
            self._save_emotion_history()
            
            # 保存模块状态
            self.save_state()
            
            # 释放模型资源
            if self.model_loaded and self.model is not None:
                # 实际情况中可能需要释放模型占用的资源
                self.model = None
                self.model_loaded = False
                
            logger.info("情感分析模块已关闭")
            return True
            
        except Exception as e:
            logger.error_status(f"关闭情感分析模块失败: {str(e)}")
            return False

    def analyze_emotion(self, text: str, user_id: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        🔥 香草修复：分析文本情感

        Args:
            text: 输入文本
            user_id: 用户ID
            context: 上下文信息

        Returns:
            Dict[str, Any]: 情感分析结果
        """
        try:
            # 构建输入数据
            input_data = {
                "text": text,
                "user_id": user_id or "default_user",
                "context": context or {}
            }

            # 调用内部处理方法
            result = self._process_input(input_data)

            # 如果有错误，返回默认结果
            if "error" in result:
                return {
                    "dominant_emotion": "平静",
                    "emotion_scores": {"平静": 1.0},
                    "overall_polarity": 0.0,
                    "intensity": 0.2,
                    "timestamp": time.time(),
                    "error": result["error"]
                }

            return result

        except Exception as e:
            logger.error_status(f"情感分析失败: {e}")
            return {
                "dominant_emotion": "平静",
                "emotion_scores": {"平静": 1.0},
                "overall_polarity": 0.0,
                "intensity": 0.2,
                "timestamp": time.time(),
                "error": str(e)
            }

    def get_emotion_state(self, user_id: str = None) -> Dict[str, Any]:
        """
        🔥 香草修复：获取当前情感状态

        Args:
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 当前情感状态
        """
        try:
            # 返回当前情感状态的副本
            emotion_state = self.current_emotion_state.copy()

            # 添加用户相关信息
            if user_id:
                emotion_state["user_id"] = user_id

                # 尝试从生命上下文获取用户特定的情感状态
                if self.life_context:
                    user_emotion_key = f"emotion.user.{user_id}"
                    user_emotion = self.life_context.get_context(user_emotion_key, {})
                    if user_emotion:
                        emotion_state.update(user_emotion)

            # 添加历史统计信息
            emotion_state["history_length"] = len(self.emotion_history)
            emotion_state["last_analysis_time"] = emotion_state.get("last_update_time", 0)

            return emotion_state

        except Exception as e:
            logger.error_status(f"获取情感状态失败: {e}")
            return {
                "dominant_emotion": "平静",
                "emotion_scores": {"平静": 1.0},
                "overall_polarity": 0.0,
                "intensity": 0.2,
                "last_update_time": time.time(),
                "error": str(e)
            }

    def update_emotion(self, emotion_data: Dict[str, Any], user_id: str = None) -> bool:
        """
        🔥 香草修复：更新情感状态

        Args:
            emotion_data: 情感数据
            user_id: 用户ID

        Returns:
            bool: 更新成功返回True
        """
        try:
            # 验证情感数据格式
            if not isinstance(emotion_data, dict):
                logger.warning_status("情感数据格式错误，必须是字典类型")
                return False

            # 更新当前情感状态
            if "emotion_scores" in emotion_data:
                self.current_emotion_state["emotion_scores"].update(emotion_data["emotion_scores"])

            if "dominant_emotion" in emotion_data:
                self.current_emotion_state["dominant_emotion"] = emotion_data["dominant_emotion"]

            if "overall_polarity" in emotion_data:
                self.current_emotion_state["overall_polarity"] = emotion_data["overall_polarity"]

            if "intensity" in emotion_data:
                self.current_emotion_state["intensity"] = emotion_data["intensity"]

            # 更新时间戳
            self.current_emotion_state["last_update_time"] = time.time()

            # 如果有用户ID，更新用户特定的情感状态
            if user_id and self.life_context:
                user_emotion_key = f"emotion.user.{user_id}"
                self.life_context.update_context(user_emotion_key, emotion_data)

            # 更新生命上下文中的全局情感状态
            if self.life_context:
                self.life_context.update_context("emotion.current_state", self.current_emotion_state)

            # 发布情感更新事件
            if self.event_bus:
                self.event_bus.publish("emotion.state_updated", {
                    "user_id": user_id,
                    "emotion_data": emotion_data,
                    "current_state": self.current_emotion_state.copy(),
                    "timestamp": time.time()
                })

            logger.info(f"情感状态已更新: {emotion_data.get('dominant_emotion', '未知')}")
            return True

        except Exception as e:
            logger.error_status(f"更新情感状态失败: {e}")
            return False


# 🔥 香草修复：添加单例模式支持
_emotion_analyzer_instance = None

def get_instance(module_id: str = "emotion_analyzer", config: Dict[str, Any] = None) -> EmotionAnalyzer:
    """
    🔥 香草修复：获取情感分析器的单例实例，确保实例包含所有必要属性

    参数:
        module_id: 模块ID
        config: 配置参数

    返回:
        情感分析器实例
    """
    global _emotion_analyzer_instance

    # 检查现有实例是否缺少必要属性
    if _emotion_analyzer_instance is not None:
        if not hasattr(_emotion_analyzer_instance, 'life_context') or not hasattr(_emotion_analyzer_instance, 'event_bus'):
            logger.warning_status("检测到旧的情感分析器实例缺少必要属性，重新创建实例")
            _emotion_analyzer_instance = None

    if _emotion_analyzer_instance is None:
        _emotion_analyzer_instance = EmotionAnalyzer(module_id, "情感分析器", "emotion")
        # 初始化实例
        if not _emotion_analyzer_instance.is_initialized:
            _emotion_analyzer_instance.initialize(config)

    return _emotion_analyzer_instance