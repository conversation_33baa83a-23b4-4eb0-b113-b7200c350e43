# 情感引擎模块
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import asyncio
from typing import Dict, Any

# 单例模式
_instance = None

class EmotionEngine:
    """情感引擎类，管理情感状态和情感处理"""
    
    def __init__(self):
        """初始化情感引擎"""
        self.logger = get_unified_logger("emotion_engine")
        self.logger.success("初始化情感引擎...")
        
        # 当前情感状态
        self.current_state = {
            "joy": 0.5,       # 喜悦
            "sadness": 0.0,   # 悲伤
            "anger": 0.0,     # 愤怒
            "fear": 0.0,      # 恐惧
            "surprise": 0.0,  # 惊讶
            "trust": 0.5,     # 信任
            "anticipation": 0.3  # 期待
        }
        
        # 情感模块
        self.modules = {}
        
        # 事件总线
        try:
            from core.enhanced_event_bus import get_instance as get_event_bus
            self.event_bus = get_event_bus()
            self.logger.success("已连接事件总线")
            
            # 注册事件处理器
            self.event_bus.subscribe("perception.processed", self._on_perception)
        except Exception as e:
            self.logger.warning_status(f"连接事件总线失败: {e}，部分功能可能受限")
            self.event_bus = None
        
        # 🔥 香草修复：添加is_initialized属性
        self.is_initialized = True
        self.initialized = True

        # 情感历史记录
        self.emotion_history = []
        self.max_history_length = 100

        # 当前情感
        self.current_emotion = "平静"

        self.logger.success("情感引擎初始化完成")
    
    def _on_perception(self, event_data):
        """处理感知事件"""
        # 在实际实现中，这里会根据感知结果更新情感状态
        pass
    
    def get_current_state(self) -> Dict[str, float]:
        """获取当前情感状态"""
        return self.current_state.copy()

    def process_emotion(self, emotion_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 香草修复：处理情感数据

        Args:
            emotion_data: 情感数据

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.logger.info(f"处理情感数据: {emotion_data}")

            # 提取情感信息
            emotion_type = emotion_data.get("emotion_type", "平静")
            intensity = emotion_data.get("intensity", 0.5)
            user_id = emotion_data.get("user_id", "default")

            # 更新当前情感状态
            if emotion_type in ["joy", "喜悦", "快乐"]:
                self.current_state["joy"] = min(1.0, self.current_state["joy"] + intensity * 0.3)
                self.current_emotion = "快乐"
            elif emotion_type in ["sadness", "悲伤", "难过"]:
                self.current_state["sadness"] = min(1.0, self.current_state["sadness"] + intensity * 0.3)
                self.current_emotion = "悲伤"
            elif emotion_type in ["anger", "愤怒", "生气"]:
                self.current_state["anger"] = min(1.0, self.current_state["anger"] + intensity * 0.3)
                self.current_emotion = "愤怒"
            elif emotion_type in ["fear", "恐惧", "害怕"]:
                self.current_state["fear"] = min(1.0, self.current_state["fear"] + intensity * 0.3)
                self.current_emotion = "恐惧"
            elif emotion_type in ["surprise", "惊讶", "意外"]:
                self.current_state["surprise"] = min(1.0, self.current_state["surprise"] + intensity * 0.3)
                self.current_emotion = "惊讶"
            elif emotion_type in ["trust", "信任", "相信"]:
                self.current_state["trust"] = min(1.0, self.current_state["trust"] + intensity * 0.3)
                self.current_emotion = "信任"
            elif emotion_type in ["anticipation", "期待", "期望"]:
                self.current_state["anticipation"] = min(1.0, self.current_state["anticipation"] + intensity * 0.3)
                self.current_emotion = "期待"

            # 添加到历史记录
            self.emotion_history.append({
                "timestamp": asyncio.get_event_loop().time() if asyncio.get_event_loop().is_running() else 0,
                "emotion_type": emotion_type,
                "intensity": intensity,
                "user_id": user_id,
                "current_state": self.current_state.copy()
            })

            # 限制历史记录长度
            if len(self.emotion_history) > self.max_history_length:
                self.emotion_history = self.emotion_history[-self.max_history_length:]

            # 发布情感更新事件
            if self.event_bus:
                self.event_bus.publish("emotion.processed", {
                    "emotion_type": emotion_type,
                    "intensity": intensity,
                    "current_state": self.current_state.copy(),
                    "current_emotion": self.current_emotion
                })

            return {
                "success": True,
                "current_emotion": self.current_emotion,
                "current_state": self.current_state.copy(),
                "processed_emotion": emotion_type,
                "intensity": intensity
            }

        except Exception as e:
            self.logger.error_status(f"处理情感数据失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "current_emotion": self.current_emotion,
                "current_state": self.current_state.copy()
            }

    def get_emotion_response(self, emotion_type: str, context: Dict[str, Any] = None) -> str:
        """
        🔥 香草修复：获取情感响应

        Args:
            emotion_type: 情感类型
            context: 上下文信息

        Returns:
            str: 情感响应文本
        """
        try:
            # 情感响应模板
            response_templates = {
                "快乐": ["我感到很开心呢！", "这真是太棒了！", "我很高兴～"],
                "悲伤": ["我感到有些难过...", "这让我觉得有点伤心", "我理解这种感受"],
                "愤怒": ["这让我感到不满", "我对此感到愤怒", "这真是太过分了"],
                "恐惧": ["我感到有些担心", "这让我觉得不安", "我有点害怕"],
                "惊讶": ["哇，这真是太意外了！", "我没想到会这样", "这真是令人惊讶"],
                "信任": ["我相信你", "我对此很有信心", "我信任你的判断"],
                "期待": ["我很期待接下来会发生什么", "这让我充满期待", "我对此很有期待"],
                "平静": ["我感到很平静", "一切都很好", "我处于平和的状态"]
            }

            # 获取对应的响应模板
            templates = response_templates.get(emotion_type, response_templates["平静"])

            # 根据当前情感强度选择响应
            import random
            response = random.choice(templates)

            # 如果有上下文，可以进一步定制响应
            if context and context.get("user_name"):
                response = f"{context['user_name']}，{response}"

            return response

        except Exception as e:
            self.logger.error_status(f"获取情感响应失败: {e}")
            return "我现在感到很平静。"

    def trigger_emotion(self, emotion_type: str, intensity: float = 0.5, user_id: str = None) -> bool:
        """
        🔥 香草修复：触发特定情感

        Args:
            emotion_type: 情感类型
            intensity: 情感强度 (0.0-1.0)
            user_id: 用户ID

        Returns:
            bool: 触发成功返回True
        """
        try:
            self.logger.info(f"触发情感: {emotion_type}, 强度: {intensity}")

            # 构建情感数据
            emotion_data = {
                "emotion_type": emotion_type,
                "intensity": max(0.0, min(1.0, intensity)),  # 确保在有效范围内
                "user_id": user_id or "system",
                "trigger_source": "manual"
            }

            # 处理情感
            result = self.process_emotion(emotion_data)

            # 发布情感触发事件
            if self.event_bus:
                self.event_bus.publish("emotion.triggered", {
                    "emotion_type": emotion_type,
                    "intensity": intensity,
                    "user_id": user_id,
                    "result": result
                })

            return result.get("success", False)

        except Exception as e:
            self.logger.error_status(f"触发情感失败: {e}")
            return False

def get_instance():
    """获取情感引擎实例（同步版本）"""
    global _instance
    if _instance is None:
        _instance = EmotionEngine()
    return _instance
