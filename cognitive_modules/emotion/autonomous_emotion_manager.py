#!/usr/bin/env python3
"""
自主情感管理器 - Autonomous Emotion Manager

统一管理情感等级标准，整合自主决策能力，让情感更新变得智能化。

作者: AI助手 & 用户协作
创建日期: 2025-01-19
版本: 1.0
"""

import os
import sys
import json
import time
import random
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

from utilities.unified_logger import get_unified_logger, setup_unified_logging

# 设置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

@dataclass
class EmotionDecision:
    """情感决策结果"""
    new_emotion: str
    new_intensity: int
    reasoning: str
    confidence: float
    factors_considered: List[str]

class AutonomousEmotionManager:
    """自主情感管理器"""
    
    def __init__(self, mysql_connector=None):
        """初始化自主情感管理器"""
        self.mysql_connector = mysql_connector
        
        # 加载统一的情感等级标准
        self.emotion_levels = self._load_emotion_levels_standard()
        
        logger.success("自主情感管理器初始化完成")
    
    def _load_emotion_levels_standard(self) -> List[Tuple]:
        """加载统一的情感等级标准"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/emotion_levels_standard.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            levels = []
            for level in config['emotion_levels_standard']['levels']:
                range_data = level['range']
                label = level['label']
                
                if len(range_data) == 2:
                    if range_data[1] == "infinity":
                        levels.append((range_data[0], float('inf'), label))
                    elif range_data[0] == range_data[1]:
                        levels.append((range_data[0], label))
                    else:
                        levels.append((range_data[0], range_data[1], label))
            
            logger.success("成功加载统一情感等级标准")
            return levels
            
        except Exception as e:
            logger.error(f"加载情感等级标准失败，使用默认配置: {e}")
            # 备用默认配置
            return [
                (-200, -100, "陌生人"),
                (-101, -1, "临时联系人"),
                (0, "素不相识"),
                (1, 10, "几面之缘"),
                (11, 50, "点赞之交"),
                (51, 200, "泛泛之交"),
                (201, 500, "点头之交"),
                (501, 1000, "聊天之友"),
                (1001, 2500, "酒肉朋友"),
                (2501, 5000, "工作伙伴"),
                (5001, 10000, "志同道合"),
                (10001, 50000, "谈心密友"),
                (50001, 200000, "知心好友"),
                (200001, 500000, "挚友"),
                (500001, 5000000, "生死之交"),
                (5000001, float('inf'), "灵魂缔造者")
            ]

    def get_emotion_level(self, intensity: int) -> str:
        """获取情感强度对应的关系级别（统一标准）"""
        # 如果小于-200，自动归0
        if intensity < -200:
            intensity = 0
            
        for level in self.emotion_levels:
            if len(level) == 2 and intensity == level[0]:
                return level[1]
            elif len(level) == 3 and level[0] <= intensity <= level[1]:
                return level[2]
        return "未知"
    
    def process_user_message_with_autonomous_decision(self, user_id: str, message: str) -> EmotionDecision:
        """
        使用自主决策处理用户消息
        
        这个方法体现了数字生命的自主思考能力：
        1. 分析消息的多层含义
        2. 考虑历史关系和上下文
        3. 做出智能的情感调整决策
        4. 提供决策推理过程
        """
        try:
            # 获取当前情感状态
            current_emotion, current_intensity = self._get_current_emotion(user_id)
            
            # 获取关系上下文
            relationship_context = self._get_relationship_context(user_id)
            
            # 分析消息情感倾向
            message_analysis = self._analyze_message_semantics(message)
            
            # 自主决策情感更新
            decision = self._make_autonomous_emotion_decision(
                user_id, message, current_emotion, current_intensity,
                relationship_context, message_analysis
            )
            
            # 执行情感更新
            self._execute_emotion_update(user_id, decision)
            
            logger.info(f"自主情感决策: {user_id} -> {decision.new_emotion}({decision.new_intensity})")
            return decision
            
        except Exception as e:
            logger.error_status(f"自主情感处理失败: {e}")
            return self._fallback_emotion_processing(user_id, message)
    
    def _analyze_message_semantics(self, message: str) -> Dict[str, Any]:
        """分析消息的语义和情感倾向"""
        analysis = {
            "positive_signals": [],
            "negative_signals": [],
            "neutral_signals": [],
            "emotional_intensity": 0,
            "interaction_type": "normal"
        }
        
        # 正面情感关键词
        positive_keywords = [
            "谢谢", "感谢", "太好了", "很棒", "喜欢", "开心", "高兴",
            "满意", "赞", "棒", "好的", "可以", "同意", "支持"
        ]
        
        # 负面情感关键词
        negative_keywords = [
            "不好", "失望", "生气", "难过", "讨厌", "烦", "不满",
            "糟糕", "差", "不行", "反对", "拒绝", "不同意"
        ]
        
        # 强度修饰词
        intensity_modifiers = ["很", "非常", "特别", "超级", "极其", "太", "超"]
        
        message_lower = message.lower()
        
        # 检测情感关键词
        for keyword in positive_keywords:
            if keyword in message_lower:
                analysis["positive_signals"].append(keyword)
        
        for keyword in negative_keywords:
            if keyword in message_lower:
                analysis["negative_signals"].append(keyword)
        
        # 计算情感强度
        intensity_count = sum(1 for modifier in intensity_modifiers if modifier in message_lower)
        analysis["emotional_intensity"] = min(intensity_count, 3)  # 最高3级强度
        
        # 判断交互类型
        if "?" in message or "？" in message:
            analysis["interaction_type"] = "question"
        elif any(word in message_lower for word in ["请", "帮", "能否", "可以"]):
            analysis["interaction_type"] = "request"
        elif any(word in message_lower for word in ["分享", "告诉", "聊聊"]):
            analysis["interaction_type"] = "sharing"
        
        return analysis
    
    def _make_autonomous_emotion_decision(self, user_id: str, message: str,
                                        current_emotion: str, current_intensity: int,
                                        relationship_context: Dict, message_analysis: Dict) -> EmotionDecision:
        """
        自主决策情感更新
        
        这里体现了数字生命的涌现能力：
        - 不是简单的规则匹配
        - 而是综合考虑多种因素进行智能决策
        """
        
        # 初始化决策参数
        intensity_change = 0
        new_emotion = current_emotion
        reasoning_parts = []
        factors_considered = []
        
        # 1. 基于消息语义的情感分析
        positive_count = len(message_analysis["positive_signals"])
        negative_count = len(message_analysis["negative_signals"])
        emotional_intensity = message_analysis["emotional_intensity"]
        
        if positive_count > negative_count:
            # 正面交互
            base_change = random.randint(1, 3)
            intensity_change = base_change * (1 + emotional_intensity * 0.5)
            new_emotion = self._select_positive_emotion(current_emotion)
            reasoning_parts.append(f"检测到{positive_count}个正面信号")
            factors_considered.append("正面语义分析")
            
        elif negative_count > positive_count:
            # 负面交互
            base_change = random.randint(-2, -1)
            intensity_change = base_change * (1 + emotional_intensity * 0.3)
            new_emotion = self._select_negative_emotion(current_emotion)
            reasoning_parts.append(f"检测到{negative_count}个负面信号")
            factors_considered.append("负面语义分析")
            
        else:
            # 中性交互
            intensity_change = random.randint(-1, 2)
            reasoning_parts.append("中性交互，轻微波动")
            factors_considered.append("中性语义分析")
        
        # 2. 基于关系深度的调整
        current_level = self.get_emotion_level(current_intensity)
        if current_level in ["陌生人", "临时联系人", "素不相识"]:
            # 关系较浅，情感变化较小
            intensity_change *= 0.5
            reasoning_parts.append("关系较浅，情感变化保守")
            factors_considered.append("关系深度调整")
            
        elif current_level in ["知心好友", "挚友", "生死之交", "灵魂缔造者"]:
            # 关系深厚，情感变化更稳定
            intensity_change *= 0.8
            reasoning_parts.append("关系深厚，情感变化稳定")
            factors_considered.append("深度关系稳定性")
        
        # 3. 基于交互类型的调整
        interaction_type = message_analysis["interaction_type"]
        if interaction_type == "question":
            # 提问表示信任和依赖
            if intensity_change >= 0:
                intensity_change += 1
            reasoning_parts.append("提问行为体现信任")
            factors_considered.append("交互类型分析")
            
        elif interaction_type == "sharing":
            # 分享表示亲密度增加
            if intensity_change >= 0:
                intensity_change += 2
            reasoning_parts.append("分享行为增进亲密度")
            factors_considered.append("亲密度评估")
        
        # 4. 自主学习和适应
        interaction_count = relationship_context.get("interaction_count", 0)
        if interaction_count > 50:
            # 长期交往，情感变化更加细腻
            intensity_change = int(intensity_change * 0.9)
            reasoning_parts.append("长期交往，情感调整更细腻")
            factors_considered.append("长期关系适应")
        
        # 5. 应用边界规则
        new_intensity = current_intensity + int(intensity_change)
        new_intensity = self._apply_intensity_boundaries(new_intensity)
        
        # 6. 根据新强度调整情感类型
        new_emotion = self._adjust_emotion_by_intensity(new_emotion, new_intensity)
        
        # 7. 生成推理说明
        reasoning = f"自主决策: {'; '.join(reasoning_parts)}。强度从{current_intensity}调整到{new_intensity}，关系级别: {self.get_emotion_level(new_intensity)}"
        
        return EmotionDecision(
            new_emotion=new_emotion,
            new_intensity=new_intensity,
            reasoning=reasoning,
            confidence=0.85,
            factors_considered=factors_considered
        )
    
    def _select_positive_emotion(self, current_emotion: str) -> str:
        """选择正面情感"""
        positive_emotions = ["高兴", "愉悦", "欣喜", "满足", "感激", "温暖"]
        if current_emotion in positive_emotions:
            return current_emotion
        return random.choice(positive_emotions[:3])  # 优先选择温和的正面情感
    
    def _select_negative_emotion(self, current_emotion: str) -> str:
        """选择负面情感"""
        negative_emotions = ["困惑", "担忧", "沮丧", "失望"]
        if current_emotion in negative_emotions:
            return current_emotion
        return random.choice(negative_emotions[:2])  # 优先选择轻度负面情感
    
    def _adjust_emotion_by_intensity(self, emotion: str, intensity: int) -> str:
        """根据强度调整情感类型"""
        level = self.get_emotion_level(intensity)
        
        # 根据关系级别调整情感表达
        if level in ["陌生人", "临时联系人", "素不相识"]:
            # 陌生关系，保持中性或轻度情感
            if emotion in ["激动", "狂喜", "愤怒"]:
                return "中性"
            return emotion
            
        elif level in ["几面之缘", "点赞之交", "泛泛之交"]:
            # 浅层关系，避免过度情感
            if emotion == "激动":
                return "高兴"
            elif emotion == "愤怒":
                return "困惑"
            return emotion
            
        else:
            # 深层关系，可以表达更丰富的情感
            return emotion
    
    def _apply_intensity_boundaries(self, intensity: int) -> int:
        """应用强度边界规则"""
        if intensity < -200:
            return 0  # 小于-200自动归零
        elif intensity > 5000000:
            return 5000000
        return intensity
    
    def adjust_emotion_based_on_frequency_autonomous(self, user_id: str) -> EmotionDecision:
        """基于交互频率的自主情感调整"""
        try:
            # 获取最后交互时间
            relationship_context = self._get_relationship_context(user_id)
            last_interaction_time = relationship_context.get("last_interaction_time")
            
            if not last_interaction_time:
                return self._no_change_decision(user_id, "无历史交互记录")
            
            # 计算时间差
            try:
                last_time = datetime.fromisoformat(last_interaction_time.replace('Z', '+00:00'))
                time_diff = datetime.now() - last_time.replace(tzinfo=None)
                days_inactive = time_diff.days
            except:
                return self._no_change_decision(user_id, "时间解析失败")
            
            if days_inactive < 1:
                return self._no_change_decision(user_id, "最近有交互，无需调整")
            
            # 获取当前情感状态
            current_emotion, current_intensity = self._get_current_emotion(user_id)
            
            # 根据不活跃天数计算衰减
            decay_amount = 0
            reasoning = ""
            
            if days_inactive >= 90:
                decay_amount = 2
                reasoning = f"{days_inactive}天未互动，情感显著衰减"
            elif days_inactive >= 30:
                decay_amount = 1
                reasoning = f"{days_inactive}天未互动，情感适度衰减"
            elif days_inactive >= 7:
                decay_amount = 0.5
                reasoning = f"{days_inactive}天未互动，情感轻微衰减"
            elif days_inactive >= 1:
                decay_amount = 0.1
                reasoning = f"{days_inactive}天未互动，情感微调"
            
            new_intensity = max(0, current_intensity - decay_amount)
            new_intensity = int(new_intensity)
            
            decision = EmotionDecision(
                new_emotion=current_emotion,
                new_intensity=new_intensity,
                reasoning=reasoning,
                confidence=0.9,
                factors_considered=["时间衰减", "交互频率"]
            )
            
            # 执行更新
            self._execute_emotion_update(user_id, decision)
            return decision
            
        except Exception as e:
            logger.error_status(f"自主频率调整失败: {e}")
            return self._no_change_decision(user_id, f"调整失败: {str(e)}")
    
    def _no_change_decision(self, user_id: str, reason: str) -> EmotionDecision:
        """无变化决策"""
        current_emotion, current_intensity = self._get_current_emotion(user_id)
        return EmotionDecision(
            new_emotion=current_emotion,
            new_intensity=current_intensity,
            reasoning=reason,
            confidence=0.8,
            factors_considered=["状态检查"]
        )
    
    def _execute_emotion_update(self, user_id: str, decision: EmotionDecision):
        """执行情感更新"""
        try:
            if self.mysql_connector:
                self._update_to_ai_user_relationships(user_id, decision)
            else:
                logger.warning_status("MySQL连接不可用，情感更新仅在内存中进行")
                
        except Exception as e:
            logger.error_status(f"执行情感更新失败: {e}")
    
    def _update_to_ai_user_relationships(self, user_id: str, decision: EmotionDecision):
        """更新到ai_user_relationships表"""
        try:
            # 检查记录是否存在
            check_query = """
                SELECT id FROM ai_user_relationships 
                WHERE user_id = %s AND ai_id = 'yanran'
            """
            result = self.mysql_connector.query(check_query, (user_id,))
            
            if result and result[0]:
                # 更新现有记录
                update_query = """
                    UPDATE ai_user_relationships 
                    SET emotional_weight = %s,
                        relationship_type = %s,
                        interaction_count = interaction_count + 1,
                        last_interaction_time = NOW(),
                        emotional_state = JSON_SET(
                            COALESCE(emotional_state, '{}'),
                            '$.current_emotion', %s,
                            '$.intensity', %s,
                            '$.last_update', NOW(),
                            '$.reasoning', %s,
                            '$.level', %s
                        ),
                        updated_at = NOW()
                    WHERE user_id = %s AND ai_id = 'yanran'
                """
                
                emotional_weight = min(1.0, max(0.0, (decision.new_intensity + 200) / 5200))
                relationship_type = self._map_level_to_type(decision.new_intensity)
                relationship_level = self.get_emotion_level(decision.new_intensity)
                
                success, result, error = self.mysql_connector.execute_update(update_query, (
                    emotional_weight,
                    relationship_type,
                    decision.new_emotion,
                    decision.new_intensity,
                    decision.reasoning,
                    relationship_level,
                    user_id
                ))
                if not success:
                    logger.error_status(f"更新情感数据失败: {error}")
            else:
                # 插入新记录
                insert_query = """
                    INSERT INTO ai_user_relationships (
                        user_id, ai_id, relationship_type, emotional_weight,
                        trust_level, intimacy_level, interaction_count,
                        last_interaction_time, emotional_state, created_at
                    ) VALUES (%s, 'yanran', %s, %s, %s, %s, 1, NOW(), %s, NOW())
                """
                
                emotional_weight = min(1.0, max(0.0, (decision.new_intensity + 200) / 5200))
                relationship_type = self._map_level_to_type(decision.new_intensity)
                trust_level = min(1.0, max(0.0, emotional_weight * 0.8))
                intimacy_level = min(1.0, max(0.0, emotional_weight * 0.6))
                
                emotional_state = json.dumps({
                    "current_emotion": decision.new_emotion,
                    "intensity": decision.new_intensity,
                    "last_update": datetime.now().isoformat(),
                    "reasoning": decision.reasoning,
                    "level": self.get_emotion_level(decision.new_intensity)
                })
                
                success, result, error = self.mysql_connector.execute_update(insert_query, (
                    user_id, relationship_type, emotional_weight,
                    trust_level, intimacy_level, emotional_state
                ))
                if not success:
                    logger.error_status(f"插入情感数据失败: {error}")
                
            logger.success(f"情感数据已更新: {user_id} -> {decision.new_emotion}({decision.new_intensity})")
            
        except Exception as e:
            logger.error_status(f"更新ai_user_relationships表失败: {e}")
    
    def _map_level_to_type(self, intensity: int) -> str:
        """将情感强度映射到关系类型"""
        level = self.get_emotion_level(intensity)
        
        mapping = {
            "陌生人": "stranger",
            "临时联系人": "acquaintance", 
            "素不相识": "stranger",
            "几面之缘": "acquaintance",
            "点赞之交": "acquaintance",
            "泛泛之交": "acquaintance",
            "点头之交": "acquaintance",
            "聊天之友": "friend",
            "酒肉朋友": "friend",
            "工作伙伴": "friend",
            "志同道合": "close_friend",
            "谈心密友": "close_friend",
            "知心好友": "best_friend",
            "挚友": "best_friend",
            "生死之交": "soulmate",
            "灵魂缔造者": "soulmate"
        }
        
        return mapping.get(level, "friend")
    
    def _get_current_emotion(self, user_id: str) -> Tuple[str, int]:
        """获取当前情感状态"""
        try:
            if self.mysql_connector:
                query = """
                    SELECT emotional_state FROM ai_user_relationships 
                    WHERE user_id = %s AND ai_id = 'yanran'
                """
                result = self.mysql_connector.query(query, (user_id,))
                
                if result and result[0] and result[0][0]:
                    emotional_state = json.loads(result[0][0])
                    return (
                        emotional_state.get("current_emotion", "中性"),
                        emotional_state.get("intensity", 0)
                    )
        except Exception as e:
            logger.warning_status(f"获取当前情感状态失败: {e}")
        
        return "中性", 0
    
    def _get_relationship_context(self, user_id: str) -> Dict:
        """获取关系上下文"""
        try:
            if self.mysql_connector:
                query = """
                    SELECT relationship_type, emotional_weight, trust_level, 
                           intimacy_level, interaction_count, last_interaction_time
                    FROM ai_user_relationships 
                    WHERE user_id = %s AND ai_id = 'yanran'
                """
                result = self.mysql_connector.query(query, (user_id,))
                
                if result and result[0]:
                    row = result[0]
                    return {
                        "relationship_type": row[0],
                        "emotional_weight": float(row[1]) if row[1] else 0.0,
                        "trust_level": float(row[2]) if row[2] else 0.0,
                        "intimacy_level": float(row[3]) if row[3] else 0.0,
                        "interaction_count": row[4] or 0,
                        "last_interaction_time": row[5].isoformat() if row[5] else None
                    }
        except Exception as e:
            logger.warning_status(f"获取关系上下文失败: {e}")
        
        return {
            "relationship_type": "friend",
            "emotional_weight": 0.5,
            "trust_level": 0.5,
            "intimacy_level": 0.0,
            "interaction_count": 0,
            "last_interaction_time": None
        }
    
    def _fallback_emotion_processing(self, user_id: str, message: str) -> EmotionDecision:
        """降级情感处理"""
        return EmotionDecision(
            new_emotion="中性",
            new_intensity=0,
            reasoning="系统降级处理",
            confidence=0.5,
            factors_considered=["降级处理"]
        )


# 单例模式
_autonomous_emotion_manager_instance = None

def get_autonomous_emotion_manager(mysql_connector=None) -> AutonomousEmotionManager:
    """获取自主情感管理器实例"""
    global _autonomous_emotion_manager_instance
    if _autonomous_emotion_manager_instance is None:
        _autonomous_emotion_manager_instance = AutonomousEmotionManager(mysql_connector)
    return _autonomous_emotion_manager_instance 