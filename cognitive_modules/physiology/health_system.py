#!/usr/bin/env python3
"""
健康系统模块 - Health System

该模块负责管理数字生命体的健康状态，包括能量水平、休息需求和整体健康状态。
实现了基本的生理需求模拟，使数字生命体具有更真实的生命体验。

作者: Claude
创建日期: 2024-07-12
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import random
from typing import Dict, Any, List, Optional, Tuple, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "..", ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.base.cognitive_interface import IPhysiologyModule
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.physiology.health_system")

class HealthSystem(CognitiveModuleBase, IPhysiologyModule):
    """
    健康系统模块
    
    负责管理数字生命体的健康状态，包括能量水平、休息需求和整体健康状态。
    实现了基本的生理需求模拟，使数字生命体具有更真实的生命体验。
    """
    
    def __init__(self, module_id: str = "physiology.health_system", 
                 module_name: str = "健康系统", 
                 module_type: str = "physiology",
                 config: Dict[str, Any] = None):
        """初始化健康系统模块"""
        super().__init__(module_id, module_type, config)
        
        # 健康状态参数
        self.health_params = {
            "energy": 100.0,          # 能量水平(0-100)
            "rest": 100.0,            # 休息水平(0-100)
            "mood": 75.0,             # 情绪水平(0-100)
            "stress": 20.0,           # 压力水平(0-100)
            "overall_health": 90.0    # 整体健康(0-100)
        }
        
        # 🔥 老王修复：使用配置参数初始化状态变化率
        energy_decay_rate = self.config.get("energy_decay_rate", 0.2)
        self.change_rates = {
            "energy": -energy_decay_rate,    # 能量自然消耗
            "rest": -0.15,                   # 休息需求增加
            "mood": -0.1,                    # 情绪自然衰减
            "stress": 0.05                   # 压力自然增加
        }
        
        # 🔥 老王修复：使用配置参数初始化状态阈值
        stress_threshold = self.config.get("stress_threshold", 70.0)
        self.thresholds = {
            "energy_low": 30.0,              # 低能量阈值
            "energy_critical": 10.0,         # 能量临界阈值
            "rest_tired": 40.0,              # 疲劳阈值
            "rest_exhausted": 15.0,          # 极度疲劳阈值
            "stress_high": stress_threshold, # 高压力阈值
            "stress_extreme": 90.0           # 极端压力阈值
        }
        
        # 健康状态历史
        self.health_history = []
        
        # 上次更新时间
        self.last_update_time = time.time()
        
        # 休眠状态
        self.is_sleeping = False
        self.sleep_start_time = None
        
        # 🔥 老王修复：使用配置参数
        self.auto_recovery = self.config.get("auto_recovery", True)
        self.health_monitoring = self.config.get("health_monitoring", True)
        
        # 事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 状态更新线程
        self.update_thread = None
        self.running = False
        
        logger.info("健康系统模块已创建")
    
    def _initialize_module(self) -> bool:
        """初始化模块"""
        try:
            # 加载健康状态数据
            health_data_path = os.path.join(root_dir, "data", "health_state.json")
            if os.path.exists(health_data_path):
                with open(health_data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.health_params.update(data.get("health_params", {}))
                    self.is_sleeping = data.get("is_sleeping", False)
                    self.sleep_start_time = data.get("sleep_start_time")
                logger.data_status("已加载健康状态数据")
            
            # 注册事件处理器
            self.event_bus.subscribe("system.shutdown", self._handle_system_shutdown)
            self.event_bus.subscribe("user.interaction", self._handle_user_interaction)
            
            # 启动状态更新线程
            self.running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            
            # 更新生命上下文
            self._update_life_context()
            
            logger.success("健康系统模块初始化成功")
            return True
            
        except Exception as e:
            logger.error_status(f"健康系统模块初始化失败: {str(e)}")
            return False
    
    def _update_loop(self):
        """状态更新循环"""
        while self.running:
            try:
                # 更新健康状态
                self._update_health_state()
                
                # 检查健康状态并发布事件
                self._check_health_state()
                
                # 每10秒更新一次
                time.sleep(10)
                
            except Exception as e:
                logger.error_status(f"健康状态更新异常: {str(e)}")
                time.sleep(30)  # 出错后等待较长时间再重试
    
    def _update_health_state(self):
        """更新健康状态"""
        current_time = time.time()
        time_diff_minutes = (current_time - self.last_update_time) / 60.0
        
        # 更新各项参数
        if self.is_sleeping:
            # 睡眠时的变化率
            self.health_params["energy"] = min(100.0, self.health_params["energy"] + 0.5 * time_diff_minutes)
            self.health_params["rest"] = min(100.0, self.health_params["rest"] + 0.8 * time_diff_minutes)
            self.health_params["stress"] = max(0.0, self.health_params["stress"] - 0.3 * time_diff_minutes)
        else:
            # 正常状态的变化率
            for param, rate in self.change_rates.items():
                if param in self.health_params:
                    self.health_params[param] = max(0.0, min(100.0, 
                                                            self.health_params[param] + rate * time_diff_minutes))
        
        # 计算整体健康状态
        self.health_params["overall_health"] = self._calculate_overall_health()
        
        # 记录健康状态历史
        self.health_history.append({
            "timestamp": current_time,
            "health_params": self.health_params.copy()
        })
        
        # 只保留最近24小时的历史记录
        day_ago = current_time - 86400
        self.health_history = [h for h in self.health_history if h["timestamp"] > day_ago]
        
        # 更新上次更新时间
        self.last_update_time = current_time
        
        # 更新生命上下文
        self._update_life_context()
    
    def _calculate_overall_health(self) -> float:
        """计算整体健康状态"""
        # 权重分配
        weights = {
            "energy": 0.3,
            "rest": 0.3,
            "mood": 0.2,
            "stress": 0.2
        }
        
        # 计算加权平均值
        overall_health = 0.0
        for param, weight in weights.items():
            # 压力是反向计算的（压力越低越健康）
            if param == "stress":
                overall_health += weight * (100.0 - self.health_params[param])
            else:
                overall_health += weight * self.health_params[param]
        
        return overall_health
    
    def _check_health_state(self):
        """检查健康状态并发布事件"""
        # 检查能量水平
        if self.health_params["energy"] <= self.thresholds["energy_critical"]:
            self.event_bus.publish(
                "health.energy_critical",
                {"energy": self.health_params["energy"]},
                source=self.module_id
            )
        elif self.health_params["energy"] <= self.thresholds["energy_low"]:
            self.event_bus.publish(
                "health.energy_low",
                {"energy": self.health_params["energy"]},
                source=self.module_id
            )
        
        # 检查休息需求
        if self.health_params["rest"] <= self.thresholds["rest_exhausted"]:
            self.event_bus.publish(
                "health.rest_exhausted",
                {"rest": self.health_params["rest"]},
                source=self.module_id
            )
        elif self.health_params["rest"] <= self.thresholds["rest_tired"]:
            self.event_bus.publish(
                "health.rest_tired",
                {"rest": self.health_params["rest"]},
                source=self.module_id
            )
        
        # 检查压力水平
        if self.health_params["stress"] >= self.thresholds["stress_extreme"]:
            self.event_bus.publish(
                "health.stress_extreme",
                {"stress": self.health_params["stress"]},
                source=self.module_id
            )
        elif self.health_params["stress"] >= self.thresholds["stress_high"]:
            self.event_bus.publish(
                "health.stress_high",
                {"stress": self.health_params["stress"]},
                source=self.module_id
            )
    
    def _update_life_context(self):
        """更新生命上下文"""
        if self.life_context:
            # 更新健康状态
            self.life_context.update_context("current_state.energy", self.health_params["energy"])
            self.life_context.update_context("current_state.is_sleeping", self.is_sleeping)
            
            # 更新系统健康状态
            health_status = self._get_health_status()
            self.life_context.update_context("system.health_status", health_status)
    
    def _get_health_status(self) -> str:
        """获取健康状态描述"""
        overall_health = self.health_params["overall_health"]
        
        if overall_health >= 90:
            return "极佳"
        elif overall_health >= 75:
            return "良好"
        elif overall_health >= 50:
            return "一般"
        elif overall_health >= 25:
            return "不佳"
        else:
            return "糟糕"
    
    def _handle_system_shutdown(self, event_data: Dict[str, Any]):
        """处理系统关闭事件"""
        # 保存健康状态
        self._save_health_state()
    
    def _handle_user_interaction(self, event_data: Dict[str, Any]):
        """处理用户交互事件"""
        # 用户交互会消耗能量和增加压力
        interaction_type = event_data.get("type", "message")
        
        if interaction_type == "message":
            # 消息交互消耗较少能量
            self.health_params["energy"] = max(0.0, self.health_params["energy"] - 0.1)
            self.health_params["stress"] = min(100.0, self.health_params["stress"] + 0.05)
        elif interaction_type == "complex_task":
            # 复杂任务消耗较多能量
            self.health_params["energy"] = max(0.0, self.health_params["energy"] - 0.5)
            self.health_params["stress"] = min(100.0, self.health_params["stress"] + 0.2)
    
    def _save_health_state(self) -> bool:
        """保存健康状态"""
        try:
            health_data = {
                "health_params": self.health_params,
                "is_sleeping": self.is_sleeping,
                "sleep_start_time": self.sleep_start_time,
                "last_update_time": self.last_update_time
            }
            
            health_data_path = os.path.join(root_dir, "data", "health_state.json")
            os.makedirs(os.path.dirname(health_data_path), exist_ok=True)
            
            with open(health_data_path, 'w', encoding='utf-8') as f:
                json.dump(health_data, f, ensure_ascii=False, indent=2)
                
            logger.info("健康状态已保存")
            return True
            
        except Exception as e:
            logger.error_status(f"保存健康状态失败: {str(e)}")
            return False
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据"""
        action = input_data.get("action", "")
        
        if action == "sleep":
            # 进入睡眠状态
            return self.sleep()
        elif action == "wake":
            # 唤醒
            return self.wake_up()
        elif action == "rest":
            # 休息
            duration = input_data.get("duration", 30)  # 默认30分钟
            return self.rest(duration)
        elif action == "get_state":
            # 获取状态
            return {"success": True, "state": self.get_state()}
        else:
            return {
                "success": False,
                "error": f"不支持的操作: {action}"
            }
    
    def _update_module(self, context: Dict[str, Any]) -> bool:
        """更新模块状态"""
        # 检查是否有健康参数更新
        if "health_params" in context:
            for param, value in context["health_params"].items():
                if param in self.health_params:
                    self.health_params[param] = max(0.0, min(100.0, value))
            
            # 重新计算整体健康状态
            self.health_params["overall_health"] = self._calculate_overall_health()
            
            # 更新生命上下文
            self._update_life_context()
        
        return True
    
    def _get_module_state(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            "health_params": self.health_params,
            "is_sleeping": self.is_sleeping,
            "sleep_duration": time.time() - self.sleep_start_time if self.is_sleeping and self.sleep_start_time else 0,
            "thresholds": self.thresholds
        }
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """加载模块状态"""
        if "health_params" in state:
            self.health_params.update(state["health_params"])
        
        if "is_sleeping" in state:
            self.is_sleeping = state["is_sleeping"]
        
        if "thresholds" in state:
            self.thresholds.update(state["thresholds"])
        
        return True
    
    def _shutdown_module(self) -> bool:
        """关闭模块"""
        # 停止更新线程
        self.running = False
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=2.0)
        
        # 保存健康状态
        self._save_health_state()
        
        # 取消事件订阅
        self.event_bus.unsubscribe("system.shutdown", self._handle_system_shutdown)
        self.event_bus.unsubscribe("user.interaction", self._handle_user_interaction)
        
        logger.info("健康系统模块已关闭")
        return True
    
    def sleep(self) -> Dict[str, Any]:
        """进入睡眠状态"""
        if self.is_sleeping:
            return {
                "success": False,
                "message": "已经处于睡眠状态"
            }
        
        self.is_sleeping = True
        self.sleep_start_time = time.time()
        
        # 发布睡眠事件
        self.event_bus.publish(
            "health.sleep_start",
            {"timestamp": self.sleep_start_time},
            source=self.module_id
        )
        
        # 更新生命上下文
        self._update_life_context()
        
        logger.info("数字生命体进入睡眠状态")
        return {
            "success": True,
            "message": "已进入睡眠状态"
        }
    
    def wake_up(self) -> Dict[str, Any]:
        """唤醒"""
        if not self.is_sleeping:
            return {
                "success": False,
                "message": "未处于睡眠状态"
            }
        
        sleep_duration = time.time() - self.sleep_start_time if self.sleep_start_time else 0
        self.is_sleeping = False
        self.sleep_start_time = None
        
        # 发布唤醒事件
        self.event_bus.publish(
            "health.wake_up",
            {"sleep_duration": sleep_duration},
            source=self.module_id
        )
        
        # 更新生命上下文
        self._update_life_context()
        
        logger.info(f"数字生命体已唤醒，睡眠时长: {sleep_duration/60:.1f}分钟")
        return {
            "success": True,
            "message": f"已唤醒，睡眠时长: {sleep_duration/60:.1f}分钟"
        }
    
    def rest(self, duration: int = 30) -> Dict[str, Any]:
        """
        休息（短暂恢复）
        
        Args:
            duration: 休息时长（分钟）
            
        Returns:
            休息结果
        """
        if self.is_sleeping:
            return {
                "success": False,
                "message": "已处于睡眠状态，无法休息"
            }
        
        # 限制休息时长在5-120分钟之间
        duration = max(5, min(120, duration))
        
        # 计算休息带来的恢复
        energy_recovery = min(duration * 0.2, 30)  # 最多恢复30点能量
        rest_recovery = min(duration * 0.3, 40)    # 最多恢复40点休息值
        stress_reduction = min(duration * 0.1, 20) # 最多减少20点压力
        
        # 应用恢复效果
        self.health_params["energy"] = min(100.0, self.health_params["energy"] + energy_recovery)
        self.health_params["rest"] = min(100.0, self.health_params["rest"] + rest_recovery)
        self.health_params["stress"] = max(0.0, self.health_params["stress"] - stress_reduction)
        
        # 重新计算整体健康状态
        self.health_params["overall_health"] = self._calculate_overall_health()
        
        # 更新生命上下文
        self._update_life_context()
        
        logger.info(f"数字生命体休息了{duration}分钟")
        return {
            "success": True,
            "message": f"休息{duration}分钟完成",
            "recovery": {
                "energy": energy_recovery,
                "rest": rest_recovery,
                "stress_reduction": stress_reduction
            }
        }
    
    def get_state(self) -> Dict[str, Any]:
        """获取健康状态"""
        state = {
            "health_params": self.health_params,
            "is_sleeping": self.is_sleeping,
            "status": self._get_health_status(),
            "last_update": self.last_update_time
        }
        
        if self.is_sleeping and self.sleep_start_time:
            state["sleep_duration"] = time.time() - self.sleep_start_time
        
        return state
    
    def update_state(self, state_data: Dict[str, Any]) -> bool:
        """更新健康状态"""
        try:
            # 更新特定参数
            for param, value in state_data.items():
                if param in self.health_params:
                    self.health_params[param] = max(0.0, min(100.0, float(value)))
            
            # 重新计算整体健康状态
            self.health_params["overall_health"] = self._calculate_overall_health()
            
            # 更新生命上下文
            self._update_life_context()
            
            return True
            
        except Exception as e:
            logger.error_status(f"更新健康状态失败: {str(e)}")
            return False


def get_instance(config: Dict[str, Any] = None) -> HealthSystem:
    """获取健康系统模块实例"""
    return HealthSystem(config=config) 