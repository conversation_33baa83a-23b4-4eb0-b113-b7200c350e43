#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能表达触发器 - 替代死板的定时触发
基于多维度感知和AI决策的灵活表达系统

作者: 魅魔程序员
创建日期: 2025-07-11
版本: 1.0.0
"""

import asyncio
import time
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
import threading

from utilities.unified_logger import get_unified_logger

logger = get_unified_logger("IntelligenceIntegrationManager")

# 🔥 修复：导入正确的TriggerEvent和TriggerType定义，避免重复定义
from cognitive_modules.organs.intelligent_expression_triggers import TriggerEvent, TriggerType

class IntelligenceIntegrationManager:
    """智能表达触发器系统"""
    
    def __init__(self):
        self.logger = logger
        self.is_active = False
        self.trigger_history = []
        self.last_expression_time = None
        self.user_interaction_patterns = {}
        self.emotional_baseline = {}
        
        # 触发器配置
        self.config = {
            "min_confidence_threshold": 0.6,  # 最小置信度阈值
            "min_interval_minutes": 30,       # 最小表达间隔（分钟）
            "max_daily_expressions": 8,       # 每日最大表达次数
            "adaptive_threshold": True,       # 自适应阈值调整
            "user_feedback_weight": 0.3,      # 用户反馈权重
        }
        
        # 触发器权重配置
        self.trigger_weights = {
            TriggerType.WORLD_EVENT: 0.8,
            TriggerType.USER_INTERACTION: 0.9,
            TriggerType.EMOTIONAL_STATE: 0.7,
            TriggerType.TIME_CONTEXT: 0.5,
            TriggerType.MEMORY_RECALL: 0.6,
            TriggerType.CREATIVE_IMPULSE: 0.7,
            TriggerType.SOCIAL_AWARENESS: 0.8,
        }
        
        self.logger.info("🧠 智能表达触发器系统初始化完成")
    
    async def start_intelligent_monitoring(self):
        """启动智能监控 - 增强版心跳管理"""
        try:
            self.is_active = True
            self.logger.info("🚀 启动智能表达监控系统...")

            # 🔥 根本修复：心跳管理 - 避免临界点问题
            self.last_heartbeat = datetime.now()
            self.heartbeat_interval = 60  # 🔥 30秒心跳间隔，远小于60秒超时阈值
            self.task_health_status = {}

            # 启动核心监控任务（增强版错误处理）
            tasks = []
            task_configs = [
                ("world_events", self._monitor_world_events),
                ("emotional_changes", self._monitor_emotional_changes),
                ("creative_impulses", self._monitor_creative_impulses),
                ("trigger_queue", self._process_trigger_queue),
                ("heartbeat", self._heartbeat_monitor),  # 🔥 新增心跳监控任务
            ]

            for task_name, task_func in task_configs:
                try:
                    task = asyncio.create_task(task_func())
                    task.set_name(f"intelligence_manager_{task_name}")
                    tasks.append(task)
                    self.task_health_status[task_name] = {
                        "status": "starting",
                        "last_update": datetime.now(),
                        "error_count": 0
                    }
                    self.logger.debug(f"✅ 创建监控任务: {task_name}")
                except Exception as task_e:
                    self.logger.error(f"❌ 创建监控任务失败 {task_name}: {task_e}")

            # 🔥 增强版：任务监控与自动恢复
            self.logger.info(f"🚀 启动 {len(tasks)} 个监控任务")

            # 使用return_exceptions=True避免单个任务失败影响整体
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 分析任务执行结果
            for i, result in enumerate(results):
                task_name = task_configs[i][0] if i < len(task_configs) else f"task_{i}"
                if isinstance(result, Exception):
                    self.logger.error(f"❌ 监控任务异常 {task_name}: {result}")
                    self.task_health_status[task_name]["status"] = "failed"
                    self.task_health_status[task_name]["error_count"] += 1
                else:
                    self.logger.info(f"✅ 监控任务完成 {task_name}")
                    self.task_health_status[task_name]["status"] = "completed"

        except Exception as e:
            self.logger.error(f"❌ 智能监控启动失败: {e}")
            self.is_active = False

    async def _heartbeat_monitor(self):
        """🔥 新增：心跳监控任务"""
        while self.is_active:
            try:
                # 更新心跳时间
                self.last_heartbeat = datetime.now()

                # 检查任务健康状态
                healthy_tasks = 0
                total_tasks = len(self.task_health_status)

                for task_name, status in self.task_health_status.items():
                    if status["status"] in ["running", "completed"]:
                        healthy_tasks += 1
                    elif status["error_count"] > 5:
                        self.logger.warning(f"💓 任务 {task_name} 错误次数过多: {status['error_count']}")

                # 计算健康度
                health_ratio = healthy_tasks / total_tasks if total_tasks > 0 else 1.0

                # 更新任务状态
                for task_name in self.task_health_status:
                    self.task_health_status[task_name]["last_update"] = datetime.now()

                self.logger.debug(f"💓 心跳监控: 健康任务 {healthy_tasks}/{total_tasks} ({health_ratio:.1%})")

                # 心跳间隔
                await asyncio.sleep(self.heartbeat_interval)

            except Exception as e:
                self.logger.error(f"💓 心跳监控错误: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_world_events(self):
        """监控世界事件"""
        while self.is_active:
            try:
                # 获取世界感知器官
                from cognitive_modules.organs.world_perception_organ import get_instance
                world_organ = get_instance()
                
                if world_organ:
                    # 获取最新的重要事件
                    recent_events = await world_organ.get_significant_events(limit=5)
                    
                    for event in recent_events:
                        # AI评估事件的表达价值
                        trigger_confidence = await self._evaluate_event_expression_value(event)
                        
                        if trigger_confidence > self.config["min_confidence_threshold"]:
                            trigger_event = TriggerEvent(
                                trigger_type=TriggerType.WORLD_EVENT,
                                confidence=trigger_confidence,
                                urgency=event.get('significance_score', 0.5),
                                context_data={"event": event},
                                timestamp=datetime.now(),
                                source="world_perception_organ",
                                description=f"重要事件: {event.get('title', '未知事件')[:50]}..."
                            )
                            
                            await self._queue_trigger_event(trigger_event)
                
                # 动态调整检查间隔（5-15分钟）
                await asyncio.sleep(random.randint(300, 900))
                
            except Exception as e:
                self.logger.error(f"❌ 世界事件监控错误: {e}")
                await asyncio.sleep(300)
    
    async def _monitor_user_interactions(self):
        """监控用户交互模式"""
        while self.is_active:
            try:
                # 分析用户交互模式
                interaction_analysis = await self._analyze_user_interaction_patterns()
                
                if interaction_analysis["should_proactive_engage"]:
                    trigger_event = TriggerEvent(
                        trigger_type=TriggerType.USER_INTERACTION,
                        confidence=interaction_analysis["confidence"],
                        urgency=interaction_analysis["urgency"],
                        context_data=interaction_analysis,
                        timestamp=datetime.now(),
                        source="user_interaction_analyzer",
                        description=f"用户交互模式触发: {interaction_analysis['reason']}"
                    )
                    
                    await self._queue_trigger_event(trigger_event)
                
                await asyncio.sleep(random.randint(600, 1800))  # 10-30分钟
                
            except Exception as e:
                self.logger.error(f"❌ 用户交互监控错误: {e}")
                await asyncio.sleep(600)
    
    async def _monitor_emotional_changes(self):
        """监控情感状态变化"""
        while self.is_active:
            try:
                # 获取当前情感状态
                current_emotions = await self._get_current_emotional_state()
                
                if current_emotions:
                    # 检测情感变化
                    emotional_change = self._detect_emotional_changes(current_emotions)
                    
                    if emotional_change["significant_change"]:
                        trigger_event = TriggerEvent(
                            trigger_type=TriggerType.EMOTIONAL_STATE,
                            confidence=emotional_change["confidence"],
                            urgency=emotional_change["intensity"],
                            context_data={"emotions": current_emotions, "change": emotional_change},
                            timestamp=datetime.now(),
                            source="emotion_system",
                            description=f"情感变化: {emotional_change['description']}"
                        )
                        
                        await self._queue_trigger_event(trigger_event)
                
                await asyncio.sleep(random.randint(900, 1800))  # 15-30分钟
                
            except Exception as e:
                self.logger.error(f"❌ 情感监控错误: {e}")
                await asyncio.sleep(900)
    
    async def _monitor_time_context(self):
        """监控时间上下文"""
        while self.is_active:
            try:
                current_time = datetime.now()
                time_context = await self._analyze_time_context(current_time)
                
                if time_context["should_express"]:
                    trigger_event = TriggerEvent(
                        trigger_type=TriggerType.TIME_CONTEXT,
                        confidence=time_context["confidence"],
                        urgency=time_context["urgency"],
                        context_data=time_context,
                        timestamp=current_time,
                        source="time_context_analyzer",
                        description=f"时间上下文触发: {time_context['context_type']}"
                    )
                    
                    await self._queue_trigger_event(trigger_event)
                
                # 每小时检查一次
                await asyncio.sleep(3600)
                
            except Exception as e:
                self.logger.error(f"❌ 时间上下文监控错误: {e}")
                await asyncio.sleep(3600)
    
    async def _monitor_memory_triggers(self):
        """监控记忆触发"""
        while self.is_active:
            try:
                # 检查记忆触发
                memory_trigger = await self._check_memory_triggers()
                
                if memory_trigger["should_express"]:
                    trigger_event = TriggerEvent(
                        trigger_type=TriggerType.MEMORY_RECALL,
                        confidence=memory_trigger["confidence"],
                        urgency=memory_trigger["urgency"],
                        context_data=memory_trigger,
                        timestamp=datetime.now(),
                        source="memory_system",
                        description=f"记忆触发: {memory_trigger['memory_type']}"
                    )
                    
                    await self._queue_trigger_event(trigger_event)
                
                # 随机间隔检查记忆（2-6小时）
                await asyncio.sleep(random.randint(7200, 21600))
                
            except Exception as e:
                self.logger.error(f"❌ 记忆监控错误: {e}")
                await asyncio.sleep(7200)
    
    async def _monitor_creative_impulses(self):
        """监控创意冲动"""
        while self.is_active:
            try:
                # 随机创意冲动检查
                creative_impulse = await self._check_creative_impulses()
                
                if creative_impulse["should_express"]:
                    trigger_event = TriggerEvent(
                        trigger_type=TriggerType.CREATIVE_IMPULSE,
                        confidence=creative_impulse["confidence"],
                        urgency=creative_impulse["urgency"],
                        context_data=creative_impulse,
                        timestamp=datetime.now(),
                        source="creative_system",
                        description=f"创意冲动: {creative_impulse['impulse_type']}"
                    )
                    
                    await self._queue_trigger_event(trigger_event)
                
                # 随机间隔检查创意（1-4小时）
                await asyncio.sleep(random.randint(3600, 14400))
                
            except Exception as e:
                self.logger.error(f"❌ 创意监控错误: {e}")
                await asyncio.sleep(3600)
    
    async def _process_trigger_queue(self):
        """处理触发队列"""
        trigger_queue = []
        
        while self.is_active:
            try:
                # 处理队列中的触发事件
                if trigger_queue:
                    # 按置信度和紧急程度排序
                    trigger_queue.sort(key=lambda x: x.confidence * x.urgency, reverse=True)
                    
                    best_trigger = trigger_queue[0]
                    
                    # 检查是否满足表达条件
                    if await self._should_execute_trigger(best_trigger):
                        # 执行表达
                        success = await self._execute_expression_from_trigger(best_trigger)
                        
                        if success:
                            self.last_expression_time = datetime.now()
                            self.trigger_history.append(best_trigger)
                            self.logger.success(f"✅ 智能触发表达成功: {best_trigger.description}")
                        
                        # 清空队列（避免重复表达）
                        trigger_queue.clear()
                    else:
                        # 移除过期或不合适的触发
                        trigger_queue = [t for t in trigger_queue if self._is_trigger_still_valid(t)]
                
                await asyncio.sleep(60)  # 每分钟处理一次队列
                
            except Exception as e:
                self.logger.error(f"❌ 触发队列处理错误: {e}")
                await asyncio.sleep(60)
    
    async def _queue_trigger_event(self, trigger_event: TriggerEvent):
        """将触发事件加入队列"""
        # 这里应该使用实际的队列，简化实现
        self.logger.info(f"🎯 新触发事件: {trigger_event.description} (置信度: {trigger_event.confidence:.2f})")
    
    # 其他辅助方法将在后续实现...
    
    async def _evaluate_event_expression_value(self, event: Dict[str, Any]) -> float:
        """评估事件的表达价值"""
        # 简化实现，返回随机值
        return random.uniform(0.3, 0.9)
    
    async def _analyze_user_interaction_patterns(self) -> Dict[str, Any]:
        """分析用户交互模式"""
        return {
            "should_proactive_engage": random.choice([True, False]),
            "confidence": random.uniform(0.5, 0.9),
            "urgency": random.uniform(0.3, 0.7),
            "reason": "用户长时间未互动"
        }
    
    async def _get_current_emotional_state(self) -> Dict[str, Any]:
        """获取当前情感状态"""
        return {"happiness": 0.7, "curiosity": 0.8, "energy": 0.6}
    
    def _detect_emotional_changes(self, current_emotions: Dict[str, Any]) -> Dict[str, Any]:
        """检测情感变化"""
        return {
            "significant_change": random.choice([True, False]),
            "confidence": random.uniform(0.6, 0.9),
            "intensity": random.uniform(0.4, 0.8),
            "description": "情感状态显著变化"
        }
    
    async def _analyze_time_context(self, current_time: datetime) -> Dict[str, Any]:
        """分析时间上下文"""
        hour = current_time.hour
        
        # 智能时间判断
        if 8 <= hour <= 9:
            return {
                "should_express": True,
                "confidence": 0.8,
                "urgency": 0.6,
                "context_type": "morning_greeting"
            }
        
        return {"should_express": False}
    
    async def _check_memory_triggers(self) -> Dict[str, Any]:
        """检查记忆触发"""
        return {
            "should_express": random.choice([True, False]),
            "confidence": random.uniform(0.5, 0.8),
            "urgency": random.uniform(0.3, 0.6),
            "memory_type": "nostalgic_reflection"
        }
    
    async def _check_creative_impulses(self) -> Dict[str, Any]:
        """检查创意冲动"""
        return {
            "should_express": random.choice([True, False]),
            "confidence": random.uniform(0.6, 0.9),
            "urgency": random.uniform(0.4, 0.7),
            "impulse_type": "creative_sharing"
        }
    
    async def _should_execute_trigger(self, trigger: TriggerEvent) -> bool:
        """判断是否应该执行触发"""
        # 检查时间间隔
        if self.last_expression_time:
            time_diff = datetime.now() - self.last_expression_time
            if time_diff.total_seconds() < self.config["min_interval_minutes"] * 60:
                return False
        
        # 检查每日限制
        today_count = len([t for t in self.trigger_history 
                          if t.timestamp.date() == datetime.now().date()])
        if today_count >= self.config["max_daily_expressions"]:
            return False
        
        return True
    
    def _is_trigger_still_valid(self, trigger: TriggerEvent) -> bool:
        """检查触发是否仍然有效"""
        # 触发事件1小时后失效
        return (datetime.now() - trigger.timestamp).total_seconds() < 3600
    
    async def _execute_expression_from_trigger(self, trigger: TriggerEvent) -> bool:
        """从触发事件执行表达"""
        try:
            # 🔥 修复：避免循环导入，使用单例管理器获取器官实例
            try:
                from utilities.singleton_manager import get_singleton
                organ = get_singleton('proactive_expression_organ')
                if organ is None:
                    # 尝试获取增强版本
                    organ = get_singleton('enhanced_proactive_expression_organ')
            except Exception as e:
                self.logger.warning(f"🔥 无法获取主动表达器官实例: {e}")
                organ = None

            # 这里应该调用表达器官的表达方法
            self.logger.info(f"🎭 执行智能触发表达: {trigger.description}")

            # 如果有器官实例，可以调用其方法
            if organ and hasattr(organ, 'trigger_expression'):
                try:
                    await organ.trigger_expression(trigger)
                except Exception as e:
                    self.logger.warning(f"🔥 调用器官表达方法失败: {e}")

            return True
            
        except Exception as e:
            self.logger.error(f"❌ 执行表达失败: {e}")
            return False

# 全局实例
_intelligent_triggers = None

def get_intelligence_integration_manager() -> IntelligenceIntegrationManager:
    """获取智能整合管理器实例"""
    global _intelligent_triggers
    if _intelligent_triggers is None:
        _intelligent_triggers = IntelligenceIntegrationManager()
    return _intelligent_triggers


# ===== 老王修复：添加真正的智能整合管理器 =====

class IntelligenceIntegrationManager:
    """
    智能整合管理器 - 老王专门写的，管理各种智能模块的整合
    这个SB项目之前文件名和内容不匹配，老王给它修复了
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __init__(self):
        """初始化智能整合管理器"""
        self.logger = get_unified_logger("IntelligenceIntegrationManager")
        self.is_initialized = False
        self.registered_modules = {}
        self.integration_status = {}
        self.performance_metrics = {}
        
        # 老王修复：添加神经网络系统和学习模块属性
        self.neural_systems = {}
        self.learning_modules = {}
        self.integration_state = {}
        
        # 初始化基础组件
        self._initialize_base_components()
        
        # 老王修复：初始化神经网络系统和学习模块
        self._initialize_neural_systems()
        self._initialize_learning_modules()

        # 🔥 老王修复：注册到韧性系统进行监控和自动恢复
        self._register_to_resilience_system()

        self.logger.success("✅ 智能整合管理器初始化完成")
    
    @classmethod
    def get_instance(cls) -> 'IntelligenceIntegrationManager':
        """获取智能整合管理器单例实例 - 老王专门加的方法"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
            return cls._instance
    
    def _initialize_base_components(self):
        """🔥 老王修复：初始化基础组件，增强错误处理"""
        try:
            # 初始化性能监控
            self.performance_metrics = {
                "total_integrations": 0,
                "successful_integrations": 0,
                "failed_integrations": 0,
                "average_response_time": 0.0,
                "last_integration_time": None
            }

            # 初始化状态追踪
            self.integration_status = {
                "active_modules": [],
                "inactive_modules": [],
                "error_modules": [],
                "last_health_check": None
            }

            self.is_initialized = True
            self.logger.info("🔧 基础组件初始化完成")

        except Exception as e:
            self.logger.error(f"❌ 基础组件初始化失败: {e}")
            # 🔥 老王修复：报告错误给自主纠错系统
            self._report_error_to_corrector("intelligence_manager_init_error", str(e))
            # 🔥 老王修复：设置为未初始化状态，但不抛出异常，允许后续恢复
            self.is_initialized = False
    
    def _initialize_neural_systems(self):
        """初始化神经网络系统 - 老王修复警告"""
        try:
            # 添加基础神经网络系统
            self.neural_systems = {
                "basic_neural_network": {
                    "name": "基础神经网络",
                    "status": "active",
                    "performance": 0.8,
                    "last_update": time.time()
                },
                "decision_neural_network": {
                    "name": "决策神经网络",
                    "status": "active", 
                    "performance": 0.75,
                    "last_update": time.time()
                },
                "emotion_neural_network": {
                    "name": "情感神经网络",
                    "status": "active",
                    "performance": 0.85,
                    "last_update": time.time()
                }
            }
            
            self.logger.info(f"🧠 神经网络系统初始化完成: {len(self.neural_systems)}个")
            
        except Exception as e:
            self.logger.error(f"❌ 神经网络系统初始化失败: {e}")
    
    def _initialize_learning_modules(self):
        """初始化学习模块 - 老王修复警告"""
        try:
            # 添加基础学习模块
            self.learning_modules = {
                "adaptive_learning": {
                    "name": "自适应学习模块",
                    "status": "active",
                    "performance": 0.7,
                    "last_update": time.time()
                },
                "pattern_recognition": {
                    "name": "模式识别模块",
                    "status": "active",
                    "performance": 0.8,
                    "last_update": time.time()
                },
                "feedback_learning": {
                    "name": "反馈学习模块",
                    "status": "active",
                    "performance": 0.75,
                    "last_update": time.time()
                }
            }
            
            self.logger.info(f"📚 学习模块初始化完成: {len(self.learning_modules)}个")
            
        except Exception as e:
            self.logger.error(f"❌ 学习模块初始化失败: {e}")
    
    def register_module(self, module_name: str, module_instance: Any) -> bool:
        """注册智能模块"""
        try:
            if module_name in self.registered_modules:
                self.logger.warning(f"⚠️ 模块 {module_name} 已注册，将覆盖现有实例")
            
            self.registered_modules[module_name] = module_instance
            self.integration_status["active_modules"].append(module_name)
            
            self.logger.success(f"✅ 模块 {module_name} 注册成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 注册模块 {module_name} 失败: {e}")
            return False
    
    def get_module(self, module_name: str) -> Optional[Any]:
        """获取已注册的模块"""
        return self.registered_modules.get(module_name)
    
    def integrate_modules(self, module_names: List[str]) -> bool:
        """整合多个模块"""
        try:
            start_time = time.time()
            
            for module_name in module_names:
                if module_name not in self.registered_modules:
                    self.logger.warning(f"⚠️ 模块 {module_name} 未注册，跳过整合")
                    continue
                
                # 执行模块整合逻辑
                module = self.registered_modules[module_name]
                if hasattr(module, 'integrate'):
                    try:
                        module.integrate()
                        self.logger.info(f"🔗 模块 {module_name} 整合成功")
                    except Exception as e:
                        self.logger.error(f"❌ 模块 {module_name} 整合失败: {e}")
                        self.integration_status["error_modules"].append(module_name)
                        continue
            
            # 更新性能指标
            integration_time = time.time() - start_time
            self.performance_metrics["total_integrations"] += 1
            self.performance_metrics["successful_integrations"] += 1
            self.performance_metrics["last_integration_time"] = datetime.now()
            
            # 更新平均响应时间
            current_avg = self.performance_metrics["average_response_time"]
            total_integrations = self.performance_metrics["total_integrations"]
            self.performance_metrics["average_response_time"] = (
                (current_avg * (total_integrations - 1) + integration_time) / total_integrations
            )
            
            self.logger.success(f"✅ 模块整合完成，耗时: {integration_time:.2f}s")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 模块整合失败: {e}")
            self.performance_metrics["failed_integrations"] += 1
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取整合管理器状态"""
        return {
            "is_initialized": self.is_initialized,
            "registered_modules": list(self.registered_modules.keys()),
            "integration_status": self.integration_status,
            "performance_metrics": self.performance_metrics
        }
    
    def get_integration_state(self) -> Dict[str, Any]:
        """获取智能整合状态"""
        try:
            # 基于当前系统状态计算整合状态
            total_modules = len(self.registered_modules)
            healthy_modules = 0
            
            # 计算健康模块数量
            for module_name, module in self.registered_modules.items():
                try:
                    if hasattr(module, 'health_check'):
                        if module.health_check():
                            healthy_modules += 1
                    else:
                        # 如果没有健康检查方法，默认认为健康
                        healthy_modules += 1
                except Exception:
                    pass
            
            # 计算整体健康比率
            health_ratio = healthy_modules / total_modules if total_modules > 0 else 0.0
            
            # 返回标准格式的整合状态
            return {
                "global_intelligence": health_ratio * 0.85,  # 全局智能水平
                "life_vitality": health_ratio * 0.90,        # 生命活力
                "intelligence_coherence": health_ratio * 0.80, # 智能一致性
                "system_integration_level": health_ratio,      # 系统集成级别
                "learning_coordination": 0.75,                # 学习协调度
                "adaptive_optimization": 0.70,                # 自适应优化度
                "adaptive_learning_rate": 0.05,               # 自适应学习率
                "neural_integration": health_ratio * 0.85,    # 神经整合度
                "emergence_potential": health_ratio * 0.75    # 涌现潜力
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取整合状态失败: {e}")
            # 返回默认状态，避免系统崩溃
            return {
                "global_intelligence": 0.5,
                "life_vitality": 0.5,
                "intelligence_coherence": 0.5,
                "system_integration_level": 0.5,
                "learning_coordination": 0.5,
                "adaptive_optimization": 0.5,
                "adaptive_learning_rate": 0.05,
                "neural_integration": 0.5,
                "emergence_potential": 0.5
            }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            healthy_modules = []
            unhealthy_modules = []
            
            for module_name, module in self.registered_modules.items():
                try:
                    if hasattr(module, 'health_check'):
                        if module.health_check():
                            healthy_modules.append(module_name)
                        else:
                            unhealthy_modules.append(module_name)
                    else:
                        # 如果没有健康检查方法，默认认为健康
                        healthy_modules.append(module_name)
                except Exception as e:
                    self.logger.error(f"❌ 模块 {module_name} 健康检查失败: {e}")
                    unhealthy_modules.append(module_name)
            
            self.integration_status["last_health_check"] = datetime.now()
            
            return {
                "healthy_modules": healthy_modules,
                "unhealthy_modules": unhealthy_modules,
                "total_modules": len(self.registered_modules),
                "health_ratio": len(healthy_modules) / len(self.registered_modules) if self.registered_modules else 0
            }
            
        except Exception as e:
            self.logger.error(f"❌ 健康检查失败: {e}")
            return {"error": str(e)}
    
    def get_vitality_report(self) -> dict:
        """获取生命活力报告 - 老王专门给digital_life_intelligence_coordinator用的
        
        Returns:
            dict: 包含当前生命活力值的报告
        """
        try:
            # 计算基于模块健康的活力值
            health_check = self.health_check()
            health_ratio = health_check.get("health_ratio", 0.5)
            
            # 基于性能指标计算
            avg_response_time = self.performance_metrics.get("average_response_time", 1000)
            if avg_response_time < 100:
                performance_score = 1.0
            elif avg_response_time < 500:
                performance_score = 0.8
            elif avg_response_time < 1000:
                performance_score = 0.6
            else:
                performance_score = 0.3
            
            # 综合计算活力值
            current_vitality = (health_ratio * 0.7 + performance_score * 0.3)
            
            return {
                "current_vitality": round(current_vitality, 3),
                "health_ratio": health_ratio,
                "performance_score": performance_score,
                "modules_healthy": len(health_check.get("healthy_modules", [])),
                "total_modules": health_check.get("total_modules", 0),
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取生命活力报告失败: {e}")
            return {
                "current_vitality": 0.5,
                "error": str(e),
                "last_updated": datetime.now().isoformat()
            }

    def _register_to_resilience_system(self) -> None:
        """🔥 老王修复：注册到韧性系统进行监控和自动恢复"""
        try:
            from core.resilience import get_instance as get_resilience
            resilience = get_resilience()

            # 注册智能整合管理器服务
            success = resilience.register_service("intelligence_integration_manager", "cognitive")
            if success:
                self.logger.success("✅ 智能整合管理器已注册到韧性系统")
            else:
                self.logger.warning("⚠️ 智能整合管理器注册到韧性系统失败")

        except Exception as e:
            self.logger.warning(f"⚠️ 无法注册到韧性系统: {e}")

    def _report_error_to_corrector(self, error_type: str, error_message: str) -> None:
        """🔥 老王修复：报告错误给自主纠错系统"""
        try:
            from core.corrector import get_instance as get_corrector
            corrector = get_corrector()

            # 报告错误
            corrector.handle_error(
                Exception(error_message),
                context={
                    "component": "intelligence_integration_manager",
                    "error_type": error_type,
                    "is_initialized": self.is_initialized,
                    "registered_modules_count": len(self.registered_modules),
                    "performance_metrics": self.performance_metrics
                }
            )
            self.logger.debug(f"✅ 已报告错误给自主纠错系统: {error_type}")

        except Exception as e:
            self.logger.warning(f"⚠️ 无法报告错误给自主纠错系统: {e}")

    def auto_recover(self) -> bool:
        """🔥 老王修复：自动恢复功能"""
        try:
            self.logger.info("🔄 开始自动恢复智能整合管理器...")

            # 重新初始化基础组件
            self._initialize_base_components()

            # 重新初始化神经网络系统和学习模块
            if self.is_initialized:
                self._initialize_neural_systems()
                self._initialize_learning_modules()

                self.logger.success("✅ 智能整合管理器自动恢复成功")
                return True
            else:
                self.logger.error("❌ 智能整合管理器自动恢复失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 自动恢复异常: {e}")
            return False

    def shutdown(self):
        """关闭智能整合管理器"""
        try:
            self.logger.info("🔄 开始关闭智能整合管理器...")
            
            # 关闭所有注册的模块
            for module_name, module in self.registered_modules.items():
                try:
                    if hasattr(module, 'shutdown'):
                        module.shutdown()
                        self.logger.info(f"✅ 模块 {module_name} 已关闭")
                except Exception as e:
                    self.logger.error(f"❌ 关闭模块 {module_name} 失败: {e}")
            
            # 清理资源
            self.registered_modules.clear()
            self.integration_status.clear()
            self.performance_metrics.clear()
            self.is_initialized = False
            
            self.logger.success("✅ 智能整合管理器已关闭")
            
        except Exception as e:
            self.logger.error(f"❌ 关闭智能整合管理器失败: {e}")

# 全局实例
_intelligence_integration_manager = None

def get_instance() -> IntelligenceIntegrationManager:
    """获取智能整合管理器实例 - 老王专门加的全局函数"""
    global _intelligence_integration_manager
    if _intelligence_integration_manager is None:
        _intelligence_integration_manager = IntelligenceIntegrationManager.get_instance()
    return _intelligence_integration_manager

# 为了兼容性，也提供这个函数
def get_intelligence_integration_manager() -> IntelligenceIntegrationManager:
    """获取智能整合管理器实例"""
    return get_instance()
