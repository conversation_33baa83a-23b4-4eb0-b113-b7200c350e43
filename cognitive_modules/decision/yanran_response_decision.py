#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
林嫣然智能响应决策系统 - Yanran Response Decision System

基于林嫣然的人设特征、当前状态、时间、心情、剧本等多重因素，
智能决策是否回复用户消息以及回复的时机。

作者: 数字生命体开发团队
版本: 1.0.0
"""

import os
import sys
import time
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "..", ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from utilities.unified_logger import get_unified_logger
from core.event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from connectors.database.mysql_connector import get_instance as get_mysql_connector
from adapters.ai_service_adapter import get_instance as get_ai_adapter

logger = get_unified_logger(__name__)

class ResponseDecision(Enum):
    """响应决策枚举"""
    REPLY_NOW = "reply_now"           # 立即回复
    REPLY_LATER = "reply_later"       # 延迟回复
    NO_REPLY = "no_reply"             # 不回复
    MOOD_RESPONSE = "mood_response"   # 心情回应（如发朋友圈）

class YanranResponseDecision:
    """
    林嫣然智能响应决策系统
    
    功能：
    1. 基于林嫣然人设的智能响应决策
    2. 考虑时间、心情、剧本、用户关系等多重因素
    3. 支持延迟回复和情绪化回应
    4. 与AI服务集成进行智能决策
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化响应决策系统"""
        logger.success("初始化林嫣然智能响应决策系统...")
        
        self.config = config or {}
        
        # 🔥 修复：使用延迟初始化避免事件循环问题
        # 核心组件延迟初始化，避免在__init__中调用可能有异步操作的组件
        self._event_bus = None
        self._life_context = None
        self._mysql_connector = None
        self._ai_adapter = None
        self._legacy_adapter = None  # 🔥 新增：legacy_adapter 用于获取当前活动
        
        # 林嫣然的时间偏好设置
        self.time_preferences = {
            "sleep_hours": (23.5, 7),        # 23:00-7:00 睡觉时间
            "lunch_break": (12.5, 13),     # 12:30-13:00 午休时间
            "active_hours": (9, 22),       # 9:00-22:00 活跃时间
            "best_response_hours": (10, 21) # 10:00-21:00 最佳回复时间
        }
        
        # 心情状态影响因子
        self.mood_factors = {
            "happy": {"reply_probability": 0.9, "delay_factor": 0.5},
            "sad": {"reply_probability": 0.3, "delay_factor": 2.0},
            "angry": {"reply_probability": 0.1, "delay_factor": 5.0},
            "tired": {"reply_probability": 0.4, "delay_factor": 3.0},
            "excited": {"reply_probability": 0.95, "delay_factor": 0.2},
            "neutral": {"reply_probability": 0.7, "delay_factor": 1.0},
            "busy": {"reply_probability": 0.2, "delay_factor": 4.0},
            "relaxed": {"reply_probability": 0.8, "delay_factor": 0.8}
        }
        
        # 用户关系影响因子
        self.relationship_factors = {
            "stranger": {"reply_probability": 0.3, "priority": 1},
            "acquaintance": {"reply_probability": 0.5, "priority": 2},
            "friend": {"reply_probability": 0.8, "priority": 3},
            "close_friend": {"reply_probability": 0.9, "priority": 4},
            "best_friend": {"reply_probability": 0.95, "priority": 5}
        }
        
        # AI决策助手的系统提示词
        self.ai_decision_prompt = """# 角色
你现在是嫣然的私人保姆，具有非常高的时间敏感度，帮她处理各种事务

## 目标
她一天有很多事情，然后会有很多微信上的联系人给她发消息，你负责判断消息是否需要回复，如果不需要回复，简单明了1句话说下她这会在干什么（10字左右，但不得透露她的隐私），不用道歉

## 判断条件
你需要从嫣然此时正在干的事情、心情、当前时间（北京时间）、天气情况等等因素全方位分析判断：
- 23:00 - 6:00 是嫣然的晚上休息时间，嫣然不回消息
- 12:30 - 13:00 是嫣然的午休时间，不回消息
- 嫣然在户外时不回消息 
- 除以上情况外都回复YES

特别注意：需要严格按照以下格式输出：
是否需要回复：[YES/NO]
不回复理由：[如果判断回复消息，不需要此项。如果判断不回消息，按照上面要求回复] 
回复时间：[如果需要回复，不需要此项。如果不回复,评估嫣然可以多久后回消息(需要保留1位小数的float类型小数：夜间休息时间段回复 7.0，午休时间段回复0.5，其他不回复为0.1~0.5之间的浮点数(比如0.1，代表0.1小时后，嫣然才有空回消息,只有在户外时才能在0.3-0.5之间)）]

记住，只允许按照格式输出是否需要回复、不回复理由和回复时间这3个部分，禁止输出与格式无关的任何字符"""
        
        # 决策历史记录
        self.decision_history = []
        
        # 标记初始化完成
        self.initialized = True
        
        logger.success("林嫣然智能响应决策系统初始化完成")
    
    @property
    def event_bus(self):
        """延迟初始化事件总线"""
        if self._event_bus is None:
            try:
                self._event_bus = get_event_bus()
            except Exception as e:
                logger.warning(f"获取事件总线失败: {e}")
                self._event_bus = None
        return self._event_bus
    
    @property
    def life_context(self):
        """延迟初始化生命上下文"""
        if self._life_context is None:
            try:
                self._life_context = get_life_context()
            except Exception as e:
                logger.warning(f"获取生命上下文失败: {e}")
                self._life_context = None
        return self._life_context
    
    @property
    def mysql_connector(self):
        """延迟初始化MySQL连接器"""
        if self._mysql_connector is None:
            try:
                self._mysql_connector = get_mysql_connector()
            except Exception as e:
                logger.warning(f"获取MySQL连接器失败: {e}")
                self._mysql_connector = None
        return self._mysql_connector
    
    @property
    def ai_adapter(self):
        """延迟初始化AI适配器"""
        if self._ai_adapter is None:
            try:
                self._ai_adapter = get_ai_adapter()
            except Exception as e:
                logger.warning(f"获取AI适配器失败: {e}")
                self._ai_adapter = None
        return self._ai_adapter

    @property
    def legacy_adapter(self):
        """延迟初始化Legacy适配器"""
        if self._legacy_adapter is None:
            try:
                from adapters.legacy_adapter import get_instance
                self._legacy_adapter = get_instance()
            except Exception as e:
                logger.warning(f"获取Legacy适配器失败: {e}")
                self._legacy_adapter = None
        return self._legacy_adapter
    
    async def should_respond_to_message(self, user_id: str, message: str, 
                                      context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        决策是否应该回复消息
        
        Args:
            user_id: 用户ID
            message: 用户消息
            context: 上下文信息
            
        Returns:
            决策结果
        """
        try:
            start_time = time.time()
            
            # 1. 获取当前状态信息
            current_state = await self._get_current_state()
            
            # 2. 获取用户关系信息
            user_relationship = await self._get_user_relationship(user_id)
            
            # 3. 获取最新剧本信息
            current_script = await self._get_current_script()
            
            # 4. 进行多层决策分析
            decision_factors = {
                "time_factor": self._analyze_time_factor(),
                "mood_factor": self._analyze_mood_factor(current_state),
                "relationship_factor": self._analyze_relationship_factor(user_relationship),
                "script_factor": self._analyze_script_factor(current_script),
                "message_factor": self._analyze_message_factor(message),
                "context_factor": self._analyze_context_factor(context or {})
            }
            
            # 5. 综合决策评分
            decision_score = self._calculate_decision_score(decision_factors)
            
            # 6. AI辅助决策（添加超时控制）
            try:
                ai_decision = await asyncio.wait_for(
                    self._get_ai_decision(
                        user_id, message, current_state, current_script, user_relationship
                    ),
                    timeout=60.0  # 🔥 老王修复：调整到60秒，给AI充足的决策时间
                )
            except asyncio.TimeoutError:
                logger.warning(f"AI决策超时，使用默认决策 (用户: {user_id})")
                ai_decision = {"should_reply": True, "reason": "AI决策超时", "delay_hours": 0}
            
            # 7. 最终决策
            final_decision = self._make_final_decision(decision_score, ai_decision, decision_factors)
            
            # 8. 记录决策历史
            decision_record = {
                "user_id": user_id,
                "message": message[:50] + "..." if len(message) > 50 else message,
                "decision": final_decision["decision"],
                "delay_hours": final_decision.get("delay_hours", 0),
                "reason": final_decision.get("reason", ""),
                "factors": decision_factors,
                "ai_decision": ai_decision,
                "timestamp": time.time(),
                "processing_time": time.time() - start_time
            }

            # 🔥 新增：记录决策预测到反馈学习系统
            try:
                await self._record_decision_prediction(decision_record, final_decision)
            except Exception as e:
                logger.warning(f"记录决策预测失败: {e}")
            
            self.decision_history.append(decision_record)
            if len(self.decision_history) > 100:  # 保持最近100条记录
                self.decision_history.pop(0)
            
            # 9. 发布决策事件
            await self._publish_decision_event(user_id, final_decision, decision_record)
            
            logger.info(f"✅ 完成响应决策 (用户: {user_id}): {final_decision['decision']}")
            return final_decision
            
        except Exception as e:
            logger.error(f"❌ 响应决策失败 (用户: {user_id}): {e}")
            return {
                "decision": ResponseDecision.REPLY_NOW,
                "reason": "决策系统异常，默认回复",
                "delay_hours": 0,
                "confidence": 0.5
            }
    
    async def _get_current_state(self) -> Dict[str, Any]:
        """获取林嫣然当前状态"""
        try:
            current_state = {
                "timestamp": time.time(),
                "datetime": datetime.now(),
                "mood": "neutral",
                "energy_level": 0.8,
                "stress_level": 0.3,
                "activity": "空闲",
                "location": "home"
            }
            
            # 从生命上下文获取状态信息
            if self.life_context:
                try:
                    mood_data = self.life_context.get_context("emotion.current_mood")
                    if mood_data:
                        current_state["mood"] = mood_data.get("mood", "neutral")
                    
                    health_data = self.life_context.get_context("health.current_status")
                    if health_data:
                        current_state["energy_level"] = health_data.get("energy", 0.8)
                        current_state["stress_level"] = health_data.get("stress", 0.3)
                except Exception as e:
                    logger.debug(f"获取生命上下文状态信息失败: {e}")
            
            return current_state
            
        except Exception as e:
            logger.warning(f"获取当前状态失败: {e}")
            return {"mood": "neutral", "energy_level": 0.8, "stress_level": 0.3}
    
    async def _get_user_relationship(self, user_id: str) -> Dict[str, Any]:
        """获取用户关系信息"""
        try:
            if not self.mysql_connector:
                return {"type": "friend", "intimacy": 500, "interaction_count": 10}
            
            # 查询用户关系信息
            query = """
            SELECT relationship_type, emotional_weight, interaction_count, last_interaction_time
            FROM ai_user_relationships 
            WHERE user_id = %s
            """
            
            success, results, error = self.mysql_connector.query(query, (user_id,))
            
            if success and results and len(results) > 0:
                result = results[0]
                return {
                    "type": result.get("relationship_type", "friend"),
                    "intimacy": result.get("emotional_weight", 500),
                    "interaction_count": result.get("interaction_count", 10),
                    "last_interaction": result.get("last_interaction_time")
                }
            else:
                return {"type": "friend", "intimacy": 500, "interaction_count": 10}
                
        except Exception as e:
            logger.warning(f"获取用户关系信息失败: {e}")
            return {"type": "friend", "intimacy": 500, "interaction_count": 10}
    
    async def _get_current_script(self) -> Dict[str, Any]:
        """获取当前剧本信息"""
        try:
            if not self.mysql_connector:
                return {"activity": "日常生活", "mood": "平静", "location": "家中"}
            
            # 查询最新的剧本信息
            query = """
            SELECT role, time_slot, weather, activity, mood, created_at
            FROM scripts 
            ORDER BY created_at DESC 
            LIMIT 1
            """
            
            # 🔥 香草修复：使用正确的MySQL连接器方法
            success, results, error = self.mysql_connector.execute_query(query)
            
            if success and results and len(results) > 0:
                script = results[0]
                return {
                    "content": script.get("activity", ""),
                    "activity": script.get("activity", "日常生活"),
                    "mood": script.get("mood", "平静"),
                    "location": self._extract_location_from_activity(script.get("activity", "")),
                    "time_slot": script.get("time_slot", ""),
                    "weather": script.get("weather", ""),
                    "role": script.get("role", ""),
                    "created_at": script.get("created_at")
                }
            else:
                return {"activity": "日常生活", "mood": "平静", "location": "家中"}
                
        except Exception as e:
            logger.warning(f"获取剧本信息失败: {e}")
            return {"activity": "日常生活", "mood": "平静", "location": "家中"}
    
    def _extract_location_from_activity(self, activity: str) -> str:
        """从活动描述中提取位置信息"""
        try:
            # 位置关键词映射
            location_keywords = {
                "家中": ["家", "房间", "客厅", "卧室", "厨房", "书房"],
                "户外": ["公园", "街道", "商场", "超市", "健身房", "咖啡厅", "餐厅"],
                "办公室": ["办公", "会议", "工作", "公司"],
                "学校": ["学校", "教室", "图书馆", "实验室"]
            }
            
            activity_lower = activity.lower()
            for location, keywords in location_keywords.items():
                if any(keyword in activity_lower for keyword in keywords):
                    return location
            
            return "家中"  # 默认位置
            
        except Exception as e:
            logger.debug(f"提取位置信息失败: {e}")
            return "家中"
    
    def _analyze_time_factor(self) -> Dict[str, Any]:
        """分析时间因素"""
        now = datetime.now()
        current_hour = now.hour + now.minute / 60.0
        
        # 检查是否在睡眠时间
        sleep_start, sleep_end = self.time_preferences["sleep_hours"]
        if sleep_start <= current_hour or current_hour <= sleep_end:
            return {
                "should_reply": False,
                "reason": "睡觉时间",
                "delay_hours": sleep_end - current_hour if current_hour <= sleep_end else (24 - current_hour + sleep_end),
                "priority": 0.1
            }
        
        # 检查是否在午休时间
        lunch_start, lunch_end = self.time_preferences["lunch_break"]
        if lunch_start <= current_hour <= lunch_end:
            return {
                "should_reply": False,
                "reason": "午休时间",
                "delay_hours": lunch_end - current_hour,
                "priority": 0.2
            }
        
        # 检查是否在最佳回复时间
        best_start, best_end = self.time_preferences["best_response_hours"]
        if best_start <= current_hour <= best_end:
            return {
                "should_reply": True,
                "reason": "最佳回复时间",
                "delay_hours": 0,
                "priority": 1.0
            }
        
        # 其他时间
        return {
            "should_reply": True,
            "reason": "正常时间",
            "delay_hours": 0,
            "priority": 0.8
        }
    
    def _analyze_mood_factor(self, current_state: Dict[str, Any]) -> Dict[str, Any]:
        """分析心情因素"""
        mood = current_state.get("mood", "neutral")
        mood_config = self.mood_factors.get(mood, self.mood_factors["neutral"])
        
        return {
            "mood": mood,
            "reply_probability": mood_config["reply_probability"],
            "delay_factor": mood_config["delay_factor"],
            "energy_level": current_state.get("energy_level", 0.8),
            "stress_level": current_state.get("stress_level", 0.3)
        }
    
    def _analyze_relationship_factor(self, relationship: Dict[str, Any]) -> Dict[str, Any]:
        """分析关系因素"""
        rel_type = relationship.get("type", "friend")
        rel_config = self.relationship_factors.get(rel_type, self.relationship_factors["friend"])
        
        return {
            "relationship_type": rel_type,
            "intimacy": relationship.get("intimacy", 500),
            "reply_probability": rel_config["reply_probability"],
            "priority": rel_config["priority"],
            "interaction_count": relationship.get("interaction_count", 10)
        }
    
    def _analyze_script_factor(self, script: Dict[str, Any]) -> Dict[str, Any]:
        """分析剧本因素"""
        activity = script.get("activity", "日常生活")
        location = script.get("location", "家中")
        
        # 根据活动类型判断是否应该回复
        busy_activities = ["工作", "会议", "运动", "户外", "约会", "学习"]
        is_busy = any(busy_activity in activity for busy_activity in busy_activities)
        
        # 根据位置判断
        outdoor_locations = ["户外", "公园", "商场", "餐厅", "健身房"]
        is_outdoor = any(outdoor_loc in location for outdoor_loc in outdoor_locations)
        
        return {
            "activity": activity,
            "location": location,
            "is_busy": is_busy,
            "is_outdoor": is_outdoor,
            "should_reply": not (is_busy or is_outdoor),
            "delay_factor": 2.0 if (is_busy or is_outdoor) else 1.0
        }
    
    def _analyze_message_factor(self, message: str) -> Dict[str, Any]:
        """分析消息因素"""
        # 紧急关键词
        urgent_keywords = [""]
        is_urgent = any(keyword in message for keyword in urgent_keywords)
        
        # 问候关键词
        greeting_keywords = ["你好", "早上好", "晚上好", "hi", "hello"]
        is_greeting = any(keyword in message.lower() for keyword in greeting_keywords)
        
        # 问题关键词
        question_keywords = ["吗", "呢", "？", "?", "怎么", "什么", "为什么", "如何"]
        is_question = any(keyword in message for keyword in question_keywords)
        
        return {
            "message_length": len(message),
            "is_urgent": is_urgent,
            "is_greeting": is_greeting,
            "is_question": is_question,
            "priority_boost": 0.3 if is_urgent else 0.1 if is_question else 0
        }
    
    def _analyze_context_factor(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """分析上下文因素"""
        return {
            "is_group_message": context.get("is_group", False),
            "has_mention": context.get("has_mention", False),
            "conversation_history": context.get("conversation_history", []),
            "recent_interaction": context.get("recent_interaction", False)
        }
    
    def _calculate_decision_score(self, factors: Dict[str, Any]) -> float:
        """计算综合决策评分"""
        try:
            score = 0.0
            
            # 时间因素 (权重: 0.3)
            time_factor = factors["time_factor"]
            if time_factor["should_reply"]:
                score += 0.3 * time_factor["priority"]
            else:
                score -= 0.2  # 不适合回复的时间段
            
            # 心情因素 (权重: 0.25)
            mood_factor = factors["mood_factor"]
            score += 0.25 * mood_factor["reply_probability"]
            
            # 关系因素 (权重: 0.2)
            rel_factor = factors["relationship_factor"]
            score += 0.2 * rel_factor["reply_probability"]
            
            # 剧本因素 (权重: 0.15)
            script_factor = factors["script_factor"]
            if script_factor["should_reply"]:
                score += 0.15
            else:
                score -= 0.1
            
            # 消息因素 (权重: 0.1)
            msg_factor = factors["message_factor"]
            score += 0.1 * (1.0 + msg_factor["priority_boost"])
            
            return max(0.0, min(1.0, score))  # 限制在0-1范围内
            
        except Exception as e:
            logger.warning(f"计算决策评分失败: {e}")
            return 0.5  # 默认中等评分
    
    async def _get_ai_decision(self, user_id: str, message: str, current_state: Dict[str, Any],
                             current_script: Dict[str, Any], user_relationship: Dict[str, Any]) -> Dict[str, Any]:
        """获取AI辅助决策"""
        try:
            logger.debug(f"开始AI决策，用户: {user_id}")
            if not self.ai_adapter:
                logger.warning("AI适配器不可用，使用默认决策")
                return {"should_reply": True, "reason": "AI服务不可用", "delay_hours": 0}
            
            # 🔥 新增：获取嫣然当前真实活动信息
            current_activity = "日常生活"  # 默认值
            if self.legacy_adapter:
                try:
                    current_activity = self.legacy_adapter.get_yanran_current_activity()
                    logger.debug(f"从Legacy适配器获取到当前活动: {current_activity}")
                except Exception as e:
                    logger.warning(f"获取当前活动失败: {e}")
                    current_activity = current_script.get('activity', '日常生活')
            else:
                current_activity = current_script.get('activity', '日常生活')

            # 构建AI决策上下文
            now = datetime.now()
            context_info = f"""当前时间：{now.strftime('%Y-%m-%d %H:%M')} (北京时间)
嫣然真实状态：
- 心情：{current_state.get('mood', '平静')}
- 精力水平：{current_state.get('energy_level', 0.8)*100:.0f}%
- 压力水平：{current_state.get('stress_level', 0.3)*100:.0f}%
- 活动：{current_activity}

用户关系：{user_relationship.get('type', 'friend')} (亲密度: {user_relationship.get('intimacy', 500)})

用户消息：{message}"""
            
            # 构建完整的prompt
            full_prompt = f"{self.ai_decision_prompt}\n\n{context_info}"
            
            # 🔥 香草修复：使用统一的AI调用方法，添加超时控制
            try:
                response_text = await asyncio.wait_for(
                    self.ai_adapter.get_completion_async(
                        messages=[{"role": "user", "content": full_prompt}],
                        service="openai",
                        model="qwen-plus-latest",
                        max_tokens=300,  # 🔥 老王修复：增加token数量
                        temperature=0.3
                    ),
                    timeout=120.0  # 🔥 老王修复：调整到120秒超时
                )
            except asyncio.TimeoutError:
                logger.warning("AI决策调用超时(120秒)")
                return {"should_reply": True, "reason": "AI决策超时", "delay_hours": 0}
            
            if response_text:
                logger.debug(f"AI决策响应成功，长度: {len(response_text)}")
                return self._parse_ai_decision(response_text)
            else:
                logger.warning("AI决策响应为空")
                return {"should_reply": True, "reason": "AI决策失败", "delay_hours": 0}

        except asyncio.TimeoutError:
            logger.warning("AI决策超时")
            return {"should_reply": True, "reason": "AI决策超时", "delay_hours": 0}
        except Exception as e:
            logger.warning(f"获取AI决策失败: {e}")
            import traceback
            logger.debug(f"AI决策异常详情: {traceback.format_exc()}")
            return {"should_reply": True, "reason": "AI决策异常", "delay_hours": 0}
    
    def _parse_ai_decision(self, ai_response) -> Dict[str, Any]:
        """解析AI决策结果 - 🔥 香草修复：支持字典和字符串格式"""
        try:
            # 🔥 香草修复：处理AI返回字典格式的情况
            if isinstance(ai_response, dict):
                # 如果AI直接返回字典格式
                result = {"should_reply": True, "reason": "", "delay_hours": 0}

                # 尝试从字典中提取决策信息
                if "should_reply" in ai_response:
                    result["should_reply"] = bool(ai_response["should_reply"])
                if "reason" in ai_response:
                    result["reason"] = str(ai_response["reason"])
                if "delay_hours" in ai_response:
                    try:
                        result["delay_hours"] = float(ai_response["delay_hours"])
                    except (ValueError, TypeError):
                        result["delay_hours"] = 0

                return result

            # 原有的字符串解析逻辑
            if not isinstance(ai_response, str):
                ai_response = str(ai_response)

            lines = ai_response.strip().split('\n')
            result = {"should_reply": True, "reason": "", "delay_hours": 0}
            
            for line in lines:
                line = line.strip()
                if line.startswith("是否需要回复："):
                    reply_decision = line.split("：")[1].strip()
                    result["should_reply"] = reply_decision.upper() == "YES"
                elif line.startswith("不回复理由："):
                    result["reason"] = line.split("：")[1].strip()
                elif line.startswith("回复时间："):
                    try:
                        delay_str = line.split("：")[1].strip()
                        result["delay_hours"] = float(delay_str)
                    except ValueError:
                        result["delay_hours"] = 0.1
            
            return result
            
        except Exception as e:
            logger.warning(f"解析AI决策结果失败: {e}")
            return {"should_reply": True, "reason": "解析失败", "delay_hours": 0}
    
    def _make_final_decision(self, decision_score: float, ai_decision: Dict[str, Any],
                           factors: Dict[str, Any]) -> Dict[str, Any]:
        """做出最终决策"""
        try:
            # AI决策权重更高（0.6），算法评分权重较低（0.4）
            ai_weight = 0.6
            score_weight = 0.4
            
            # 如果AI明确说不回复，优先考虑AI决策
            if not ai_decision.get("should_reply", True):
                return {
                    "decision": ResponseDecision.NO_REPLY,
                    "reason": ai_decision.get("reason", "AI建议不回复"),
                    "delay_hours": ai_decision.get("delay_hours", 0.1),
                    "confidence": 0.8,
                    "ai_decision": ai_decision,
                    "algorithm_score": decision_score
                }
            
            # 综合评分
            final_score = (ai_weight * (1.0 if ai_decision.get("should_reply", True) else 0.0) + 
                          score_weight * decision_score)
            
            # 决策阈值
            if final_score >= 0.7:
                decision = ResponseDecision.REPLY_NOW
                delay = 0
            elif final_score >= 0.4:
                decision = ResponseDecision.REPLY_LATER
                delay = ai_decision.get("delay_hours", 0.1)
            else:
                decision = ResponseDecision.NO_REPLY
                delay = ai_decision.get("delay_hours", 1.0)
            
            # 特殊情况处理
            time_factor = factors["time_factor"]
            if not time_factor["should_reply"] and decision == ResponseDecision.REPLY_NOW:
                decision = ResponseDecision.REPLY_LATER
                delay = time_factor["delay_hours"]
            
            return {
                "decision": decision,
                "reason": ai_decision.get("reason", "综合决策结果"),
                "delay_hours": delay,
                "confidence": final_score,
                "ai_decision": ai_decision,
                "algorithm_score": decision_score
            }
            
        except Exception as e:
            logger.error(f"最终决策失败: {e}")
            return {
                "decision": ResponseDecision.REPLY_NOW,
                "reason": "决策异常，默认回复",
                "delay_hours": 0,
                "confidence": 0.5
            }
    
    async def _publish_decision_event(self, user_id: str, decision: Dict[str, Any],
                                    record: Dict[str, Any]):
        """发布决策事件"""
        try:
            if self.event_bus:
                self.event_bus.publish("yanran.response.decision", {
                    "user_id": user_id,
                    "decision": decision["decision"].value,
                    "delay_hours": decision.get("delay_hours", 0),
                    "reason": decision.get("reason", ""),
                    "confidence": decision.get("confidence", 0.5),
                    "timestamp": time.time()
                })
        except Exception as e:
            logger.warning(f"发布决策事件失败: {e}")
    
    def get_decision_stats(self) -> Dict[str, Any]:
        """获取决策统计信息"""
        if not self.decision_history:
            return {"total_decisions": 0}
        
        total = len(self.decision_history)
        reply_now = sum(1 for d in self.decision_history if d["decision"] == ResponseDecision.REPLY_NOW)
        reply_later = sum(1 for d in self.decision_history if d["decision"] == ResponseDecision.REPLY_LATER)
        no_reply = sum(1 for d in self.decision_history if d["decision"] == ResponseDecision.NO_REPLY)
        
        return {
            "total_decisions": total,
            "reply_now_count": reply_now,
            "reply_later_count": reply_later,
            "no_reply_count": no_reply,
            "reply_now_rate": reply_now / total if total > 0 else 0,
            "reply_later_rate": reply_later / total if total > 0 else 0,
            "no_reply_rate": no_reply / total if total > 0 else 0,
            "avg_processing_time": sum(d["processing_time"] for d in self.decision_history) / total if total > 0 else 0
        }

    async def handle_delayed_response_decision(self, user_id: str, message: str, 
                                           context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理延迟回复决策（新增方法）
        
        这个方法是延迟回复机制的核心入口，集成了：
        1. 现有的should_respond_to_message决策逻辑
        2. 延迟消息存储逻辑
        3. 消息追加功能
        4. 标准化的决策结果返回
        
        Args:
            user_id: 用户ID
            message: 用户消息
            context: 上下文信息
            
        Returns:
            标准化的决策结果字典
        """
        try:
            logger.info(f"🔍 开始处理延迟回复决策 (用户: {user_id})")
            
            # 1. 调用现有的响应决策逻辑
            decision_result = await self.should_respond_to_message(user_id, message, context)
            
            # 2. 根据决策结果进行标准化处理
            if decision_result["decision"] == ResponseDecision.REPLY_NOW:
                return {
                    "decision": "reply_now",
                    "reason": decision_result.get("reason", "立即回复"),
                    "confidence": decision_result.get("confidence", 0.8),
                    "delay_hours": 0,
                    "should_store": False,
                    "processing_time": decision_result.get("processing_time", 0)
                }
            
            elif decision_result["decision"] == ResponseDecision.REPLY_LATER:
                delay_hours = decision_result.get("delay_hours", 0.5)
                return {
                    "decision": "reply_later",
                    "reason": decision_result.get("reason", "稍后回复"),
                    "confidence": decision_result.get("confidence", 0.7),
                    "delay_hours": delay_hours,
                    "should_store": True,
                    "message": message,
                    "user_id": user_id,
                    "context": context or {},
                    "processing_time": decision_result.get("processing_time", 0)
                }
            
            elif decision_result["decision"] == ResponseDecision.NO_REPLY:
                return {
                    "decision": "no_reply",
                    "reason": decision_result.get("reason", "不需要回复"),
                    "confidence": decision_result.get("confidence", 0.9),
                    "delay_hours": 0,
                    "should_store": False,
                    "processing_time": decision_result.get("processing_time", 0)
                }
            
            else:  # MOOD_RESPONSE 或其他情况
                return {
                    "decision": "no_reply",
                    "reason": decision_result.get("reason", "心情回应"),
                    "confidence": decision_result.get("confidence", 0.6),
                    "delay_hours": 0,
                    "should_store": False,
                    "processing_time": decision_result.get("processing_time", 0)
                }
                
        except Exception as e:
            logger.error(f"❌ 延迟回复决策处理失败 (用户: {user_id}): {e}")
            # 异常情况下默认立即回复
            return {
                "decision": "reply_now",
                "reason": "决策系统异常，默认立即回复",
                "confidence": 0.5,
                "delay_hours": 0,
                "should_store": False,
                "error": str(e)
            }

    def handle_delayed_response_decision_sync(self, user_id: str, message: str, 
                                           context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理延迟回复决策（同步版本）
        
        这个方法是延迟回复机制的同步版本，避免事件循环问题
        
        Args:
            user_id: 用户ID
            message: 用户消息
            context: 上下文信息
            
        Returns:
            标准化的决策结果字典
        """
        try:
            logger.info(f"🔍 开始处理延迟回复决策 (同步版本, 用户: {user_id})")
            
            # 🔥 修复：在同步上下文中处理异步决策
            import asyncio
            import threading
            
            # 检查当前线程和事件循环状态
            current_thread_name = threading.current_thread().name
            is_api_thread = "Flask" in current_thread_name or "werkzeug" in current_thread_name.lower()
            
            try:
                # 尝试获取当前事件循环
                current_loop = asyncio.get_running_loop()
                
                if is_api_thread:
                    # API线程中，使用线程池处理异步操作
                    logger.debug("🔧 API线程中，使用线程池处理异步决策")
                    
                    def run_async_decision():
                        """在新的事件循环中运行异步决策"""
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            return new_loop.run_until_complete(
                                self.handle_delayed_response_decision(user_id, message, context)
                            )
                        finally:
                            new_loop.close()
                    
                    # 使用线程池执行
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                        future = executor.submit(run_async_decision)
                        return future.result(timeout=30)  # 30秒超时
                else:
                    # 非API线程，但有运行中的事件循环，跳过复杂的异步决策
                    logger.debug("🔧 检测到运行中的事件循环，使用简化决策")
                    return self._make_simple_decision(user_id, message, context)
                    
            except RuntimeError:
                # 没有运行中的事件循环，可以直接创建新的
                logger.debug("🔧 没有运行中的事件循环，创建新的事件循环")
                
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(
                        self.handle_delayed_response_decision(user_id, message, context)
                    )
                finally:
                    new_loop.close()
                    
        except Exception as e:
            logger.error(f"❌ 延迟回复决策处理失败 (同步版本, 用户: {user_id}): {e}")
            # 异常情况下默认立即回复
            return {
                "decision": "reply_now",
                "reason": "决策系统异常，默认立即回复",
                "confidence": 0.5,
                "delay_hours": 0,
                "should_store": False,
                "error": str(e)
            }
    
    def _make_simple_decision(self, user_id: str, message: str, 
                            context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        简化的同步决策逻辑
        
        Args:
            user_id: 用户ID
            message: 用户消息
            context: 上下文信息
            
        Returns:
            标准化的决策结果字典
        """
        try:
            logger.debug(f"🔧 使用简化决策逻辑 (用户: {user_id})")
            
            # 简化的决策逻辑
            now = datetime.now()
            current_hour = now.hour
            
            # 基于时间的简单决策
            if current_hour < 7 or current_hour > 23:
                # 深夜或早晨，延迟回复
                delay_hours = 7 - current_hour if current_hour < 7 else (24 - current_hour + 7)
                return {
                    "decision": "reply_later",
                    "reason": "非活跃时间，延迟回复",
                    "confidence": 0.8,
                    "delay_hours": delay_hours,
                    "should_store": True,
                    "message": message,
                    "user_id": user_id,
                    "context": context or {},
                    "processing_time": 0
                }
            
            # 检查紧急关键词
            urgent_keywords = ["紧急", "急", "救命", "帮忙", "重要", "马上"]
            if any(keyword in message for keyword in urgent_keywords):
                return {
                    "decision": "reply_now",
                    "reason": "检测到紧急关键词，立即回复",
                    "confidence": 0.9,
                    "delay_hours": 0,
                    "should_store": False,
                    "processing_time": 0
                }
            
            # 默认立即回复
            return {
                "decision": "reply_now",
                "reason": "正常时间，立即回复",
                "confidence": 0.7,
                "delay_hours": 0,
                "should_store": False,
                "processing_time": 0
            }
            
        except Exception as e:
            logger.error(f"❌ 简化决策逻辑失败: {e}")
            return {
                "decision": "reply_now",
                "reason": "决策异常，默认立即回复",
                "confidence": 0.5,
                "delay_hours": 0,
                "should_store": False,
                "error": str(e)
            }

    async def _record_decision_prediction(self, decision_record: Dict[str, Any], final_decision: Dict[str, Any]):
        """🔥 新增：记录决策预测到反馈学习系统"""
        try:
            from intelligence.feedback_learning import get_instance as get_feedback_learning

            feedback_learning = get_feedback_learning()

            # 构建预测记录
            prediction = {
                "id": f"decision_{decision_record['user_id']}_{int(decision_record['timestamp'])}",
                "type": "response_decision",
                "probability": final_decision.get("confidence", 0.5),
                "confidence": final_decision.get("confidence", 0.5),
                "factors": {
                    # 🔥 香草修复：从实际字段中提取score值
                    "time_factor": self._extract_factor_score(decision_record["factors"]["time_factor"], "time"),
                    "mood_factor": self._extract_factor_score(decision_record["factors"]["mood_factor"], "mood"),
                    "relationship_factor": self._extract_factor_score(decision_record["factors"]["relationship_factor"], "relationship"),
                    "script_factor": self._extract_factor_score(decision_record["factors"]["script_factor"], "script"),
                    "message_factor": self._extract_factor_score(decision_record["factors"]["message_factor"], "message"),
                    "context_factor": self._extract_factor_score(decision_record["factors"]["context_factor"], "context")
                },
                "decision": final_decision["decision"].value if hasattr(final_decision["decision"], 'value') else str(final_decision["decision"]),
                "user_id": decision_record["user_id"],
                "timestamp": decision_record["timestamp"]
            }

            # 记录预测
            feedback_learning.record_prediction(prediction)
            logger.debug(f"✅ 已记录决策预测: {prediction['id']}")

        except Exception as e:
            logger.warning(f"记录决策预测失败: {e}")
            # 不抛出异常，避免影响主要决策流程

    def _extract_factor_score(self, factor_data: Dict[str, Any], factor_type: str) -> float:
        """🔥 香草新增：从factor数据中提取score值"""
        try:
            if factor_type == "time":
                # 时间因子：基于priority和should_reply
                priority = factor_data.get("priority", 0.5)
                should_reply = factor_data.get("should_reply", True)
                return priority if should_reply else priority * 0.3

            elif factor_type == "mood":
                # 心情因子：基于reply_probability
                return factor_data.get("reply_probability", 0.5)

            elif factor_type == "relationship":
                # 关系因子：基于reply_probability
                return factor_data.get("reply_probability", 0.5)

            elif factor_type == "script":
                # 剧本因子：基于should_reply和delay_factor
                should_reply = factor_data.get("should_reply", True)
                delay_factor = factor_data.get("delay_factor", 1.0)
                return 0.8 if should_reply else (0.2 / delay_factor)

            elif factor_type == "message":
                # 消息因子：基于priority_boost和其他特征
                priority_boost = factor_data.get("priority_boost", 0)
                is_urgent = factor_data.get("is_urgent", False)
                is_question = factor_data.get("is_question", False)
                base_score = 0.5
                if is_urgent:
                    base_score += 0.4
                elif is_question:
                    base_score += 0.2
                return min(1.0, base_score + priority_boost)

            elif factor_type == "context":
                # 上下文因子：基于各种上下文特征
                has_mention = factor_data.get("has_mention", False)
                recent_interaction = factor_data.get("recent_interaction", False)
                is_group = factor_data.get("is_group_message", False)

                score = 0.5
                if has_mention:
                    score += 0.3
                if recent_interaction:
                    score += 0.2
                if is_group:
                    score -= 0.1  # 群消息优先级稍低

                return max(0.0, min(1.0, score))

            else:
                # 未知类型，返回默认值
                return 0.5

        except Exception as e:
            logger.warning(f"提取{factor_type}因子score失败: {e}")
            return 0.5

# ========== 工厂函数 ==========

_decision_instance = None

def get_instance(config: Dict[str, Any] = None) -> YanranResponseDecision:
    """
    获取林嫣然响应决策系统实例（单例模式）
    
    Args:
        config: 配置信息
        
    Returns:
        决策系统实例
    """
    global _decision_instance
    
    if _decision_instance is None:
        _decision_instance = YanranResponseDecision(config)
    
    return _decision_instance

def reset_instance():
    """重置实例"""
    global _decision_instance
    _decision_instance = None

# 🔥 修复问题9：添加启动函数，确保系统正确启动
def start_yanran_response_decision(config: Dict[str, Any] = None) -> YanranResponseDecision:
    """
    启动林嫣然智能响应决策系统
    
    Args:
        config: 配置信息
        
    Returns:
        决策系统实例
    """
    try:
        logger.success("🧠 启动林嫣然智能响应决策系统...")
        
        # 获取实例
        decision_system = get_instance(config)
        
        # 确保系统已完全初始化
        if not hasattr(decision_system, 'initialized') or not decision_system.initialized:
            logger.warning("决策系统未完全初始化，尝试重新初始化...")
            decision_system.__init__(config)
        
        logger.success("✅ 林嫣然智能响应决策系统启动成功")
        return decision_system
        
    except Exception as e:
        logger.error(f"❌ 启动林嫣然智能响应决策系统失败: {e}")
        # 返回一个基础的决策系统实例
        return YanranResponseDecision(config or {})

# 🔥 修复问题9：添加系统状态检查函数
def check_decision_system_status() -> Dict[str, Any]:
    """
    检查决策系统状态
    
    Returns:
        系统状态信息
    """
    global _decision_instance
    
    if _decision_instance is None:
        return {
            "status": "not_initialized",
            "message": "决策系统未初始化",
            "ready": False
        }
    
    try:
        # 检查关键组件
        components_status = {
            "event_bus": _decision_instance.event_bus is not None,
            "life_context": _decision_instance.life_context is not None,
            "mysql_connector": _decision_instance.mysql_connector is not None,
            "ai_adapter": _decision_instance.ai_adapter is not None
        }
        
        all_ready = all(components_status.values())
        
        return {
            "status": "ready" if all_ready else "partial",
            "message": "决策系统运行正常" if all_ready else "部分组件未就绪",
            "ready": all_ready,
            "components": components_status,
            "decision_count": len(_decision_instance.decision_history),
            "config": _decision_instance.config
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"检查决策系统状态失败: {e}",
            "ready": False
        }

# 导出
__all__ = ["YanranResponseDecision", "ResponseDecision", "get_instance", "reset_instance", "start_yanran_response_decision", "check_decision_system_status"]