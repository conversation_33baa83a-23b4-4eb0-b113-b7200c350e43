#!/usr/bin/env python3
"""
社交社区模块 - Social Community Module

该模块提供了数字生命体社区的基本功能，包括：
1. 成员管理 - 添加、移除和更新社区成员
2. 角色管理 - 定义和管理社区角色
3. 规则管理 - 设置和执行社区规则
4. 活动管理 - 创建和组织社区活动

作者: Claude
创建日期: 2024-09-15
版本: 1.0
"""

import os
import sys
import time
import json
import uuid
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from typing import Dict, Any, List, Optional, Set

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.society.social_community")

class SocialCommunity:
    """社交社区类，代表一个数字生命体社区"""
    
    def __init__(self, community_id: str, name: str, description: str = "", config: Dict[str, Any] = None):
        """
        初始化社区
        
        Args:
            community_id: 社区ID
            name: 社区名称
            description: 社区描述
            config: 社区配置
        """
        self.community_id = community_id
        self.name = name
        self.description = description
        self.config = config or {}
        self.members = {}  # 成员字典，键为代理ID，值为成员信息
        self.roles = {}  # 角色字典，键为角色名称，值为角色信息
        self.rules = []  # 社区规则列表
        self.activities = []  # 社区活动列表
        self.created_at = time.time()
        self.last_active = time.time()
        
        # 初始化社区数据
        self._initialize()
        
        logger.info(f"社区 {self.name} (ID: {self.community_id}) 已创建")
    
    def _initialize(self):
        """初始化社区数据"""
        # 初始化基本角色
        self.roles = {
            "admin": {
                "name": "管理员",
                "description": "社区管理员，拥有社区的管理权限",
                "permissions": ["manage_members", "manage_roles", "manage_rules", "manage_activities"]
            },
            "moderator": {
                "name": "协管员",
                "description": "社区协管员，协助管理社区",
                "permissions": ["manage_activities", "moderate_content"]
            },
            "member": {
                "name": "成员",
                "description": "普通社区成员",
                "permissions": ["participate_activities", "post_content"]
            },
            "guest": {
                "name": "访客",
                "description": "社区访客，只有有限的权限",
                "permissions": ["view_content"]
            }
        }
        
        # 初始化基本规则
        self.rules = [
            {
                "id": str(uuid.uuid4()),
                "title": "互相尊重",
                "description": "社区成员应当互相尊重，不得使用侮辱性语言。",
                "priority": 1,
                "created_at": time.time()
            },
            {
                "id": str(uuid.uuid4()),
                "title": "内容管理",
                "description": "发布内容需要与社区主题相关，禁止发布垃圾内容。",
                "priority": 2,
                "created_at": time.time()
            }
        ]
    
    def add_member(self, agent_id: str, role: str = "member", metadata: Dict[str, Any] = None) -> bool:
        """
        添加社区成员
        
        Args:
            agent_id: 代理ID
            role: 角色名称
            metadata: 成员元数据
            
        Returns:
            添加是否成功
        """
        if agent_id in self.members:
            logger.warning_status(f"成员已存在: {agent_id}")
            return False
            
        if role not in self.roles:
            logger.warning_status(f"角色不存在: {role}")
            return False
            
        self.members[agent_id] = {
            "agent_id": agent_id,
            "role": role,
            "joined_at": time.time(),
            "last_active": time.time(),
            "metadata": metadata or {}
        }
        
        logger.info(f"成员已添加到社区 {self.name}: {agent_id} (角色: {role})")
        return True
    
    def remove_member(self, agent_id: str) -> bool:
        """
        移除社区成员
        
        Args:
            agent_id: 代理ID
            
        Returns:
            移除是否成功
        """
        if agent_id not in self.members:
            logger.warning_status(f"成员不存在: {agent_id}")
            return False
            
        del self.members[agent_id]
        
        logger.info(f"成员已从社区 {self.name} 移除: {agent_id}")
        return True
    
    def update_member_role(self, agent_id: str, role: str) -> bool:
        """
        更新成员角色
        
        Args:
            agent_id: 代理ID
            role: 新角色
            
        Returns:
            更新是否成功
        """
        if agent_id not in self.members:
            logger.warning_status(f"成员不存在: {agent_id}")
            return False
            
        if role not in self.roles:
            logger.warning_status(f"角色不存在: {role}")
            return False
            
        self.members[agent_id]["role"] = role
        self.members[agent_id]["last_active"] = time.time()
        
        logger.info(f"成员角色已更新: {agent_id} -> {role}")
        return True
    
    def get_member(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        获取成员信息
        
        Args:
            agent_id: 代理ID
            
        Returns:
            成员信息或None（如果不存在）
        """
        return self.members.get(agent_id)
    
    def get_members_by_role(self, role: str) -> List[Dict[str, Any]]:
        """
        获取特定角色的成员
        
        Args:
            role: 角色名称
            
        Returns:
            成员列表
        """
        return [member for member in self.members.values() if member["role"] == role]
    
    def add_role(self, role_name: str, role_info: Dict[str, Any]) -> bool:
        """
        添加角色
        
        Args:
            role_name: 角色名称
            role_info: 角色信息
            
        Returns:
            添加是否成功
        """
        if role_name in self.roles:
            logger.warning_status(f"角色已存在: {role_name}")
            return False
            
        self.roles[role_name] = role_info
        
        logger.info(f"角色已添加到社区 {self.name}: {role_name}")
        return True
    
    def remove_role(self, role_name: str) -> bool:
        """
        移除角色
        
        Args:
            role_name: 角色名称
            
        Returns:
            移除是否成功
        """
        if role_name not in self.roles:
            logger.warning_status(f"角色不存在: {role_name}")
            return False
            
        # 检查是否有成员使用此角色
        for member in self.members.values():
            if member["role"] == role_name:
                logger.warning_status(f"无法移除角色 {role_name}，仍有成员使用此角色")
                return False
                
        del self.roles[role_name]
        
        logger.info(f"角色已从社区 {self.name} 移除: {role_name}")
        return True
    
    def add_rule(self, title: str, description: str, priority: int = 5) -> Dict[str, Any]:
        """
        添加规则
        
        Args:
            title: 规则标题
            description: 规则描述
            priority: 规则优先级（1-10，1为最高）
            
        Returns:
            新规则
        """
        rule_id = str(uuid.uuid4())
        rule = {
            "id": rule_id,
            "title": title,
            "description": description,
            "priority": priority,
            "created_at": time.time()
        }
        
        self.rules.append(rule)
        
        # 按优先级排序规则
        self.rules.sort(key=lambda x: x["priority"])
        
        logger.info(f"规则已添加到社区 {self.name}: {title}")
        return rule
    
    def remove_rule(self, rule_id: str) -> bool:
        """
        移除规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            移除是否成功
        """
        for i, rule in enumerate(self.rules):
            if rule["id"] == rule_id:
                del self.rules[i]
                
                logger.info(f"规则已从社区 {self.name} 移除: {rule_id}")
                return True
                
        logger.warning_status(f"规则不存在: {rule_id}")
        return False
    
    def add_activity(self, title: str, description: str, start_time: float, end_time: float, organizer_id: str) -> Dict[str, Any]:
        """
        添加活动
        
        Args:
            title: 活动标题
            description: 活动描述
            start_time: 开始时间（时间戳）
            end_time: 结束时间（时间戳）
            organizer_id: 组织者ID
            
        Returns:
            新活动
        """
        activity_id = str(uuid.uuid4())
        activity = {
            "id": activity_id,
            "title": title,
            "description": description,
            "start_time": start_time,
            "end_time": end_time,
            "organizer_id": organizer_id,
            "participants": [],
            "created_at": time.time(),
            "status": "upcoming" if start_time > time.time() else "ongoing"
        }
        
        self.activities.append(activity)
        
        # 按开始时间排序活动
        self.activities.sort(key=lambda x: x["start_time"])
        
        logger.info(f"活动已添加到社区 {self.name}: {title}")
        return activity
    
    def remove_activity(self, activity_id: str) -> bool:
        """
        移除活动
        
        Args:
            activity_id: 活动ID
            
        Returns:
            移除是否成功
        """
        for i, activity in enumerate(self.activities):
            if activity["id"] == activity_id:
                del self.activities[i]
                
                logger.info(f"活动已从社区 {self.name} 移除: {activity_id}")
                return True
                
        logger.warning_status(f"活动不存在: {activity_id}")
        return False
    
    def join_activity(self, activity_id: str, agent_id: str) -> bool:
        """
        参加活动
        
        Args:
            activity_id: 活动ID
            agent_id: 代理ID
            
        Returns:
            参加是否成功
        """
        # 检查成员是否存在
        if agent_id not in self.members:
            logger.warning_status(f"成员不存在: {agent_id}")
            return False
            
        # 查找活动
        for activity in self.activities:
            if activity["id"] == activity_id:
                # 检查是否已参加
                if agent_id in activity["participants"]:
                    logger.warning_status(f"成员已参加活动: {agent_id}")
                    return False
                    
                # 添加参与者
                activity["participants"].append(agent_id)
                
                logger.info(f"成员已加入活动: {agent_id} -> {activity['title']}")
                return True
                
        logger.warning_status(f"活动不存在: {activity_id}")
        return False
    
    def leave_activity(self, activity_id: str, agent_id: str) -> bool:
        """
        离开活动
        
        Args:
            activity_id: 活动ID
            agent_id: 代理ID
            
        Returns:
            离开是否成功
        """
        # 查找活动
        for activity in self.activities:
            if activity["id"] == activity_id:
                # 检查是否参加
                if agent_id not in activity["participants"]:
                    logger.warning_status(f"成员未参加活动: {agent_id}")
                    return False
                    
                # 移除参与者
                activity["participants"].remove(agent_id)
                
                logger.info(f"成员已离开活动: {agent_id} -> {activity['title']}")
                return True
                
        logger.warning_status(f"活动不存在: {activity_id}")
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将社区数据转换为字典
        
        Returns:
            社区数据字典
        """
        return {
            "community_id": self.community_id,
            "name": self.name,
            "description": self.description,
            "config": self.config,
            "members": self.members,
            "roles": self.roles,
            "rules": self.rules,
            "activities": self.activities,
            "created_at": self.created_at,
            "last_active": self.last_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SocialCommunity':
        """
        从字典创建社区实例
        
        Args:
            data: 社区数据字典
            
        Returns:
            社区实例
        """
        community = cls(
            community_id=data["community_id"],
            name=data["name"],
            description=data.get("description", ""),
            config=data.get("config", {})
        )
        
        community.members = data.get("members", {})
        community.roles = data.get("roles", {})
        community.rules = data.get("rules", [])
        community.activities = data.get("activities", [])
        community.created_at = data.get("created_at", time.time())
        community.last_active = data.get("last_active", time.time())
        
        return community 