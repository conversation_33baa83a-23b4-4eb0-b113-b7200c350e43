#!/usr/bin/env python3
"""
社交互动模块 - Social Interaction Module

该模块负责处理数字生命体之间的社交互动，包括：
1. 社交事件 - 定义和管理社交事件
2. 互动规则 - 定义社交互动的规则
3. 互动效果 - 处理互动对关系的影响
4. 社交模式 - 识别和管理社交模式

作者: Claude
创建日期: 2024-09-15
版本: 1.0
"""

import os
import sys
import time
import json
import uuid
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import random
from typing import Dict, Any, List, Optional, Tuple, Union, Set
from datetime import datetime

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
society_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(society_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from cognitive_modules.society.social_network import get_instance as get_social_network
from cognitive_modules.society.multi_agent_system import get_instance as get_multi_agent_system

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.society.social_interaction")

class SocialInteraction(CognitiveModuleBase):
    """社交互动模块，处理数字生命体之间的社交互动"""
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化社交互动模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "social_interaction", "society", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取社交网络和多代理系统
        self.social_network = get_social_network()
        self.multi_agent_system = get_multi_agent_system()
        
        # 互动类型定义
        self.interaction_types = {
            "greeting": {
                "description": "问候互动",
                "relationship_effect": 0.01,
                "requires_response": True,
                "emotional_impact": {"happiness": 0.05}
            },
            "conversation": {
                "description": "一般对话",
                "relationship_effect": 0.02,
                "requires_response": True,
                "emotional_impact": {"happiness": 0.03}
            },
            "sharing": {
                "description": "分享信息或资源",
                "relationship_effect": 0.05,
                "requires_response": False,
                "emotional_impact": {"happiness": 0.08, "trust": 0.1}
            },
            "helping": {
                "description": "提供帮助",
                "relationship_effect": 0.1,
                "requires_response": False,
                "emotional_impact": {"happiness": 0.15, "trust": 0.2}
            },
            "conflict": {
                "description": "冲突或争论",
                "relationship_effect": -0.1,
                "requires_response": True,
                "emotional_impact": {"anger": 0.2, "trust": -0.15}
            },
            "collaboration": {
                "description": "合作完成任务",
                "relationship_effect": 0.08,
                "requires_response": True,
                "emotional_impact": {"happiness": 0.1, "trust": 0.15}
            }
        }
        
        # 互动历史
        self.interaction_history = []
        self.max_history = 1000
        
        # 社交事件
        self.social_events = []
        
        # 互动规则
        self.interaction_rules = []
        
        # 初始化互动规则
        self._initialize_rules()
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"社交互动模块 {self.module_id} 已创建")
    
    def _initialize_rules(self):
        """初始化互动规则"""
        self.interaction_rules = [
            {
                "id": "rule_reciprocity",
                "name": "互惠原则",
                "description": "如果一个代理向另一个代理提供价值，另一个代理倾向于回报",
                "condition": lambda source, target, interaction: interaction.get("type") in ["sharing", "helping"],
                "effect": lambda source, target, interaction: {"relationship_effect": 0.15}
            },
            {
                "id": "rule_similarity",
                "name": "相似吸引",
                "description": "相似的代理更容易相互吸引",
                "condition": lambda source, target, interaction: self._check_similarity(source, target) > 0.7,
                "effect": lambda source, target, interaction: {"relationship_effect": 0.05}
            },
            {
                "id": "rule_familiarity",
                "name": "熟悉度增强",
                "description": "互动次数增加，关系强度增加",
                "condition": lambda source, target, interaction: self._get_interaction_count(source, target) > 5,
                "effect": lambda source, target, interaction: {"relationship_effect": 0.02 * min(self._get_interaction_count(source, target) / 10, 1)}
            }
        ]
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("agent_message", self._on_agent_message)
        self.event_bus.subscribe("relationship_updated", self._on_relationship_updated)
        self.event_bus.subscribe("social_event_created", self._on_social_event_created)
        
        logger.debug("已订阅相关事件")
    
    def _on_agent_message(self, data: Dict[str, Any]):
        """处理代理消息事件"""
        try:
            source_id = data.get("source_id")
            target_id = data.get("target_id")
            message_id = data.get("message_id")
            timestamp = data.get("timestamp", time.time())
            
            if not source_id or not target_id:
                return
                
            # 创建互动记录
            interaction = {
                "id": str(uuid.uuid4()),
                "type": "conversation",
                "source_id": source_id,
                "target_id": target_id,
                "message_id": message_id,
                "timestamp": timestamp,
                "processed": False
            }
            
            # 添加到互动历史
            self.interaction_history.append(interaction)
            if len(self.interaction_history) > self.max_history:
                self.interaction_history = self.interaction_history[-self.max_history:]
            
            # 处理互动效果
            self._process_interaction(interaction)
            
            logger.debug(f"处理代理消息事件: {source_id} -> {target_id}")
        except Exception as e:
            logger.error_status(f"处理代理消息事件失败: {str(e)}")
    
    def _on_relationship_updated(self, data: Dict[str, Any]):
        """处理关系更新事件"""
        try:
            source_id = data.get("source_id")
            target_id = data.get("target_id")
            relationship = data.get("relationship")
            
            if not source_id or not target_id or not relationship:
                return
                
            # 检查是否需要创建社交事件
            relationship_type = relationship.get("type")
            relationship_strength = relationship.get("strength", 0)
            
            if relationship_type == "friend" and relationship_strength > 0.7:
                self._create_social_event("strong_friendship", source_id, target_id)
            elif relationship_type == "romantic" and relationship_strength > 0.8:
                self._create_social_event("romantic_relationship", source_id, target_id)
                
            logger.debug(f"处理关系更新事件: {source_id} -> {target_id}")
        except Exception as e:
            logger.error_status(f"处理关系更新事件失败: {str(e)}")
    
    def _on_social_event_created(self, data: Dict[str, Any]):
        """处理社交事件创建事件"""
        try:
            event_id = data.get("event_id")
            event_type = data.get("event_type")
            
            if not event_id or not event_type:
                return
                
            # 将事件添加到社交事件列表
            for event in self.social_events:
                if event["id"] == event_id:
                    return
                    
            self.social_events.append(data)
            
            logger.debug(f"处理社交事件创建事件: {event_type} (ID: {event_id})")
        except Exception as e:
            logger.error_status(f"处理社交事件创建事件失败: {str(e)}")
    
    def _process_interaction(self, interaction: Dict[str, Any]):
        """
        处理互动效果
        
        Args:
            interaction: 互动记录
        """
        try:
            source_id = interaction.get("source_id")
            target_id = interaction.get("target_id")
            interaction_type = interaction.get("type")
            
            if not source_id or not target_id or not interaction_type:
                return
                
            # 获取互动类型定义
            type_def = self.interaction_types.get(interaction_type)
            if not type_def:
                return
                
            # 基础关系影响
            relationship_effect = type_def.get("relationship_effect", 0)
            
            # 应用互动规则
            for rule in self.interaction_rules:
                condition = rule.get("condition")
                effect = rule.get("effect")
                
                if condition and effect:
                    if condition(source_id, target_id, interaction):
                        rule_effect = effect(source_id, target_id, interaction)
                        relationship_effect += rule_effect.get("relationship_effect", 0)
            
            # 更新关系强度
            self.social_network._strengthen_relationship(source_id, target_id, relationship_effect)
            
            # 标记互动为已处理
            interaction["processed"] = True
            
            logger.debug(f"处理互动效果: {source_id} -> {target_id}, 类型: {interaction_type}, 效果: {relationship_effect}")
        except Exception as e:
            logger.error_status(f"处理互动效果失败: {str(e)}")
    
    def _create_social_event(self, event_type: str, source_id: str, target_id: str, metadata: Dict[str, Any] = None):
        """
        创建社交事件
        
        Args:
            event_type: 事件类型
            source_id: 源代理ID
            target_id: 目标代理ID
            metadata: 事件元数据
        """
        try:
            event_id = str(uuid.uuid4())
            timestamp = time.time()
            
            event = {
                "id": event_id,
                "type": event_type,
                "source_id": source_id,
                "target_id": target_id,
                "timestamp": timestamp,
                "metadata": metadata or {}
            }
            
            # 添加到社交事件列表
            self.social_events.append(event)
            
            # 发布社交事件创建事件
            self.event_bus.publish("social_event_created", {
                "event_id": event_id,
                "event_type": event_type,
                "source_id": source_id,
                "target_id": target_id,
                "timestamp": timestamp
            })
            
            logger.info(f"创建社交事件: {event_type}, {source_id} -> {target_id}")
            return event
        except Exception as e:
            logger.error_status(f"创建社交事件失败: {str(e)}")
            return None
    
    def create_interaction(self, source_id: str, target_id: str, interaction_type: str, metadata: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        创建互动
        
        Args:
            source_id: 源代理ID
            target_id: 目标代理ID
            interaction_type: 互动类型
            metadata: 互动元数据
            
        Returns:
            创建的互动记录或None（如果创建失败）
        """
        try:
            # 检查互动类型是否有效
            if interaction_type not in self.interaction_types:
                logger.warning_status(f"无效的互动类型: {interaction_type}")
                return None
                
            # 创建互动记录
            interaction_id = str(uuid.uuid4())
            timestamp = time.time()
            
            interaction = {
                "id": interaction_id,
                "type": interaction_type,
                "source_id": source_id,
                "target_id": target_id,
                "timestamp": timestamp,
                "metadata": metadata or {},
                "processed": False
            }
            
            # 添加到互动历史
            self.interaction_history.append(interaction)
            if len(self.interaction_history) > self.max_history:
                self.interaction_history = self.interaction_history[-self.max_history:]
            
            # 处理互动效果
            self._process_interaction(interaction)
            
            logger.info(f"创建互动: {interaction_type}, {source_id} -> {target_id}")
            return interaction
        except Exception as e:
            logger.error_status(f"创建互动失败: {str(e)}")
            return None
    
    def get_interactions(self, agent_id: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取互动记录
        
        Args:
            agent_id: 代理ID，如果为None则返回所有互动
            limit: 返回记录数量限制
            
        Returns:
            互动记录列表
        """
        try:
            if agent_id:
                # 获取指定代理的互动
                agent_interactions = [interaction for interaction in self.interaction_history 
                                     if interaction["source_id"] == agent_id or interaction["target_id"] == agent_id]
                return sorted(agent_interactions, key=lambda x: x["timestamp"], reverse=True)[:limit]
            else:
                # 获取所有互动
                return sorted(self.interaction_history, key=lambda x: x["timestamp"], reverse=True)[:limit]
        except Exception as e:
            logger.error_status(f"获取互动记录失败: {str(e)}")
            return []
    
    def get_social_events(self, agent_id: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取社交事件
        
        Args:
            agent_id: 代理ID，如果为None则返回所有事件
            limit: 返回记录数量限制
            
        Returns:
            社交事件列表
        """
        try:
            if agent_id:
                # 获取与指定代理相关的事件
                agent_events = [event for event in self.social_events 
                               if event["source_id"] == agent_id or event["target_id"] == agent_id]
                return sorted(agent_events, key=lambda x: x["timestamp"], reverse=True)[:limit]
            else:
                # 获取所有事件
                return sorted(self.social_events, key=lambda x: x["timestamp"], reverse=True)[:limit]
        except Exception as e:
            logger.error_status(f"获取社交事件失败: {str(e)}")
            return []
    
    def _check_similarity(self, agent_id1: str, agent_id2: str) -> float:
        """
        检查两个代理的相似度
        
        Args:
            agent_id1: 第一个代理ID
            agent_id2: 第二个代理ID
            
        Returns:
            相似度（0-1）
        """
        try:
            # 获取代理
            agent1 = self.multi_agent_system.get_agent(agent_id1)
            agent2 = self.multi_agent_system.get_agent(agent_id2)
            
            if not agent1 or not agent2:
                return 0
                
            # 简单计算能力相似度
            common_capabilities = set(agent1.capabilities).intersection(set(agent2.capabilities))
            total_capabilities = set(agent1.capabilities).union(set(agent2.capabilities))
            
            if not total_capabilities:
                return 0
                
            return len(common_capabilities) / len(total_capabilities)
        except Exception as e:
            logger.error_status(f"检查代理相似度失败: {str(e)}")
            return 0
    
    def _get_interaction_count(self, agent_id1: str, agent_id2: str) -> int:
        """
        获取两个代理之间的互动次数
        
        Args:
            agent_id1: 第一个代理ID
            agent_id2: 第二个代理ID
            
        Returns:
            互动次数
        """
        try:
            count = 0
            
            for interaction in self.interaction_history:
                source_id = interaction.get("source_id")
                target_id = interaction.get("target_id")
                
                if (source_id == agent_id1 and target_id == agent_id2) or (source_id == agent_id2 and target_id == agent_id1):
                    count += 1
            
            return count
        except Exception as e:
            logger.error_status(f"获取互动次数失败: {str(e)}")
            return 0
    
    def suggest_interaction(self, agent_id1: str, agent_id2: str) -> Optional[Dict[str, Any]]:
        """
        建议两个代理之间的互动
        
        Args:
            agent_id1: 第一个代理ID
            agent_id2: 第二个代理ID
            
        Returns:
            建议的互动类型和描述，或None（如果无法建议）
        """
        try:
            # 获取关系
            relationship = self.social_network.social_graph.get_edge_data(agent_id1, agent_id2) or {}
            relationship_type = relationship.get("type", "stranger")
            relationship_strength = relationship.get("strength", 0)
            
            # 获取互动次数
            interaction_count = self._get_interaction_count(agent_id1, agent_id2)
            
            # 根据关系类型和强度建议互动
            if relationship_type == "stranger" or relationship_strength < 0.2:
                return {
                    "type": "greeting",
                    "description": "建议进行初次问候，建立基础关系"
                }
            elif relationship_type == "acquaintance" or relationship_strength < 0.5:
                return {
                    "type": "conversation",
                    "description": "建议进行一般对话，增进了解"
                }
            elif relationship_type == "friend" or relationship_strength < 0.8:
                if interaction_count > 10:
                    return {
                        "type": "collaboration",
                        "description": "建议进行合作活动，加深友谊"
                    }
                else:
                    return {
                        "type": "sharing",
                        "description": "建议分享信息或资源，增强信任"
                    }
            else:
                return {
                    "type": "helping",
                    "description": "建议提供帮助，巩固深厚关系"
                }
        except Exception as e:
            logger.error_status(f"建议互动失败: {str(e)}")
            return None
    
    def _initialize_module(self) -> bool:
        """
        初始化模块
        
        Returns:
            初始化是否成功
        """
        try:
            # 初始化互动规则
            self._initialize_rules()
            
            # 订阅事件
            self._subscribe_events()
            
            return True
        except Exception as e:
            logger.error_status(f"初始化社交互动模块失败: {str(e)}")
            return False

    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        try:
            action = input_data.get("action")
            params = input_data.get("params", {})
            
            if action == "create_interaction":
                source_id = params.get("source_id")
                target_id = params.get("target_id")
                interaction_type = params.get("interaction_type")
                metadata = params.get("metadata", {})
                
                if not source_id or not target_id or not interaction_type:
                    return {"success": False, "error": "缺少必要参数"}
                    
                interaction = self.create_interaction(source_id, target_id, interaction_type, metadata)
                if interaction:
                    return {"success": True, "interaction": interaction}
                else:
                    return {"success": False, "error": "创建互动失败"}
                    
            elif action == "get_interactions":
                agent_id = params.get("agent_id")
                limit = params.get("limit", 10)
                
                interactions = self.get_interactions(agent_id, limit)
                return {"success": True, "interactions": interactions}
                
            elif action == "get_social_events":
                agent_id = params.get("agent_id")
                limit = params.get("limit", 10)
                
                events = self.get_social_events(agent_id, limit)
                return {"success": True, "events": events}
                
            elif action == "suggest_interaction":
                agent_id1 = params.get("agent_id1")
                agent_id2 = params.get("agent_id2")
                
                if not agent_id1 or not agent_id2:
                    return {"success": False, "error": "缺少必要参数"}
                    
                suggestion = self.suggest_interaction(agent_id1, agent_id2)
                if suggestion:
                    return {"success": True, "suggestion": suggestion}
                else:
                    return {"success": False, "error": "无法提供互动建议"}
                    
            else:
                return {"success": False, "error": f"未知操作: {action}"}
                
        except Exception as e:
            logger.error_status(f"处理输入数据失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _update_module(self) -> bool:
        """
        更新模块
        
        Returns:
            更新是否成功
        """
        try:
            # 处理未处理的互动
            for interaction in self.interaction_history:
                if not interaction.get("processed", False):
                    self._process_interaction(interaction)
            
            return True
        except Exception as e:
            logger.error_status(f"更新社交互动模块失败: {str(e)}")
            return False

    def _load_module_state(self) -> Dict[str, Any]:
        """
        加载模块状态
        
        Returns:
            模块状态
        """
        try:
            state = {
                "interaction_types": self.interaction_types,
                "interaction_rules": self.interaction_rules,
                "interaction_history": self.interaction_history[:100],  # 只返回最近100条互动以节省空间
                "social_events": self.social_events
            }
            
            return state
        except Exception as e:
            logger.error_status(f"加载模块状态失败: {str(e)}")
            return {}

    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        return self._load_module_state()

    def _shutdown_module(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        try:
            # 处理未处理的互动
            for interaction in self.interaction_history:
                if not interaction.get("processed", False):
                    self._process_interaction(interaction)
            
            return True
        except Exception as e:
            logger.error_status(f"关闭社交互动模块失败: {str(e)}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        try:
            status = {
                "module_id": self.module_id,
                "module_type": "society",
                "interaction_count": len(self.interaction_history),
                "event_count": len(self.social_events),
                "rule_count": len(self.interaction_rules),
                "last_updated": time.time()
            }
            
            return status
        except Exception as e:
            logger.error_status(f"获取模块状态失败: {str(e)}")
            return {"error": str(e)}

def get_instance(config: Dict[str, Any] = None) -> SocialInteraction:
    """
    获取社交互动模块实例
    
    Args:
        config: 配置信息
        
    Returns:
        社交互动模块实例
    """
    return SocialInteraction(config=config) 