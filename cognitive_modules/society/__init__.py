#!/usr/bin/env python3
"""
社交互动模块 - Social Interaction Module

该目录包含与数字生命体社交互动相关的模块，用于支持多个数字生命体之间的协作和互动。

主要功能：
1. 多代理系统 - 支持多个数字生命体实例之间的交互
2. 社交网络 - 构建和管理数字生命体之间的社交关系网络
3. 社交社区 - 管理数字生命体社区
4. 社交互动 - 处理数字生命体之间的社交互动
5. 群体行为 - 模拟群体互动和集体行为
6. 社交协议 - 定义数字生命体之间的通信和互动协议
7. 社区形成 - 支持数字生命体社区的自然形成和演化

作者: Claude
创建日期: 2024-09-10
版本: 1.1
""" 

# 导出模块
from cognitive_modules.society.social_network import get_instance as get_social_network
from cognitive_modules.society.multi_agent_system import get_instance as get_multi_agent_system
from cognitive_modules.society.social_interaction import get_instance as get_social_interaction 