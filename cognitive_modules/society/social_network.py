#!/usr/bin/env python3
"""
社交网络模块 - Social Network Module

该模块负责构建和管理数字生命体社会关系网络，包括：
1. 社交关系管理 - 构建和维护社交关系网络
2. 群体形成 - 管理群体的形成和演化
3. 社会角色 - 定义和管理社会角色
4. 社会规范 - 定义和管理社会规范
5. 社区建设 - 支持数字生命体社区的构建

作者: Claude
创建日期: 2024-09-15
版本: 1.0
"""

import os
import sys
import time
import json
import uuid
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import networkx as nx
from typing import Dict, Any, List, Optional, Tuple, Union, Set
from datetime import datetime

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
society_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(society_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from cognitive_modules.society.multi_agent_system import get_instance as get_multi_agent_system

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.society.social_network")

class SocialCommunity:
    """社交社区类，代表一个数字生命体社区"""
    
    def __init__(self, community_id: str, name: str, description: str = "", config: Dict[str, Any] = None):
        """
        初始化社区
        
        Args:
            community_id: 社区ID
            name: 社区名称
            description: 社区描述
            config: 社区配置
        """
        self.community_id = community_id
        self.name = name
        self.description = description
        self.config = config or {}
        self.members = {}  # 成员字典，键为代理ID，值为成员信息
        self.roles = {}  # 角色字典，键为角色名称，值为角色信息
        self.rules = []  # 社区规则列表
        self.activities = []  # 社区活动列表
        self.created_at = time.time()
        self.last_active = time.time()
        
        # 初始化社区数据
        self._initialize()
        
        logger.info(f"社区 {self.name} (ID: {self.community_id}) 已创建")
    
    def _initialize(self):
        """初始化社区数据"""
        # 初始化基本角色
        self.roles = {
            "admin": {
                "name": "管理员",
                "description": "社区管理员，拥有社区的管理权限",
                "permissions": ["manage_members", "manage_roles", "manage_rules", "manage_activities"]
            },
            "moderator": {
                "name": "协管员",
                "description": "社区协管员，协助管理社区",
                "permissions": ["manage_activities", "moderate_content"]
            },
            "member": {
                "name": "成员",
                "description": "普通社区成员",
                "permissions": ["participate_activities", "post_content"]
            },
            "guest": {
                "name": "访客",
                "description": "社区访客，只有有限的权限",
                "permissions": ["view_content"]
            }
        }
        
        # 初始化基本规则
        self.rules = [
            {
                "id": str(uuid.uuid4()),
                "title": "互相尊重",
                "description": "社区成员应当互相尊重，不得使用侮辱性语言。",
                "priority": 1,
                "created_at": time.time()
            },
            {
                "id": str(uuid.uuid4()),
                "title": "内容管理",
                "description": "发布内容需要与社区主题相关，禁止发布垃圾内容。",
                "priority": 2,
                "created_at": time.time()
            }
        ]
    
    def add_member(self, agent_id: str, role: str = "member", metadata: Dict[str, Any] = None) -> bool:
        """
        添加社区成员
        
        Args:
            agent_id: 代理ID
            role: 角色名称
            metadata: 成员元数据
            
        Returns:
            添加是否成功
        """
        if agent_id in self.members:
            logger.warning_status(f"成员已存在: {agent_id}")
            return False
            
        if role not in self.roles:
            logger.warning_status(f"角色不存在: {role}")
            return False
            
        self.members[agent_id] = {
            "agent_id": agent_id,
            "role": role,
            "joined_at": time.time(),
            "last_active": time.time(),
            "metadata": metadata or {}
        }
        
        logger.info(f"成员已添加到社区 {self.name}: {agent_id} (角色: {role})")
        return True
    
    def remove_member(self, agent_id: str) -> bool:
        """
        移除社区成员
        
        Args:
            agent_id: 代理ID
            
        Returns:
            移除是否成功
        """
        if agent_id not in self.members:
            logger.warning_status(f"成员不存在: {agent_id}")
            return False
            
        del self.members[agent_id]
        
        logger.info(f"成员已从社区 {self.name} 移除: {agent_id}")
        return True
    
    def update_member_role(self, agent_id: str, role: str) -> bool:
        """
        更新成员角色
        
        Args:
            agent_id: 代理ID
            role: 新角色
            
        Returns:
            更新是否成功
        """
        if agent_id not in self.members:
            logger.warning_status(f"成员不存在: {agent_id}")
            return False
            
        if role not in self.roles:
            logger.warning_status(f"角色不存在: {role}")
            return False
            
        self.members[agent_id]["role"] = role
        self.members[agent_id]["last_active"] = time.time()
        
        logger.info(f"成员角色已更新: {agent_id} -> {role}")
        return True
    
    def get_member(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        获取成员信息
        
        Args:
            agent_id: 代理ID
            
        Returns:
            成员信息或None
        """
        return self.members.get(agent_id)
    
    def get_members_by_role(self, role: str) -> List[Dict[str, Any]]:
        """
        获取指定角色的成员
        
        Args:
            role: 角色名称
            
        Returns:
            成员列表
        """
        return [member for member in self.members.values() if member["role"] == role]
    
    def add_role(self, role_name: str, role_info: Dict[str, Any]) -> bool:
        """
        添加社区角色
        
        Args:
            role_name: 角色名称
            role_info: 角色信息
            
        Returns:
            添加是否成功
        """
        if role_name in self.roles:
            logger.warning_status(f"角色已存在: {role_name}")
            return False
            
        self.roles[role_name] = role_info
        
        logger.info(f"角色已添加到社区 {self.name}: {role_name}")
        return True
    
    def remove_role(self, role_name: str) -> bool:
        """
        移除社区角色
        
        Args:
            role_name: 角色名称
            
        Returns:
            移除是否成功
        """
        if role_name not in self.roles:
            logger.warning_status(f"角色不存在: {role_name}")
            return False
            
        if role_name in ["admin", "member", "guest"]:
            logger.warning_status(f"无法移除基本角色: {role_name}")
            return False
            
        # 检查是否有成员使用该角色
        members_with_role = [member for member in self.members.values() if member["role"] == role_name]
        if members_with_role:
            logger.warning_status(f"无法移除正在使用的角色: {role_name}")
            return False
            
        del self.roles[role_name]
        
        logger.info(f"角色已从社区 {self.name} 移除: {role_name}")
        return True
    
    def add_rule(self, title: str, description: str, priority: int = 5) -> Dict[str, Any]:
        """
        添加社区规则
        
        Args:
            title: 规则标题
            description: 规则描述
            priority: 规则优先级（1-10，1为最高）
            
        Returns:
            规则信息
        """
        rule = {
            "id": str(uuid.uuid4()),
            "title": title,
            "description": description,
            "priority": max(1, min(10, priority)),  # 确保优先级在1-10之间
            "created_at": time.time()
        }
        
        self.rules.append(rule)
        
        # 按优先级排序
        self.rules.sort(key=lambda x: x["priority"])
        
        logger.info(f"规则已添加到社区 {self.name}: {title}")
        return rule
    
    def remove_rule(self, rule_id: str) -> bool:
        """
        移除社区规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            移除是否成功
        """
        for i, rule in enumerate(self.rules):
            if rule["id"] == rule_id:
                self.rules.pop(i)
                logger.info(f"规则已从社区 {self.name} 移除: {rule_id}")
                return True
                
        logger.warning_status(f"规则不存在: {rule_id}")
        return False
    
    def add_activity(self, title: str, description: str, start_time: float, end_time: float, organizer_id: str) -> Dict[str, Any]:
        """
        添加社区活动
        
        Args:
            title: 活动标题
            description: 活动描述
            start_time: 开始时间戳
            end_time: 结束时间戳
            organizer_id: 组织者ID
            
        Returns:
            活动信息
        """
        activity = {
            "id": str(uuid.uuid4()),
            "title": title,
            "description": description,
            "start_time": start_time,
            "end_time": end_time,
            "organizer_id": organizer_id,
            "participants": [],
            "status": "upcoming",
            "created_at": time.time()
        }
        
        self.activities.append(activity)
        
        # 按开始时间排序
        self.activities.sort(key=lambda x: x["start_time"])
        
        logger.info(f"活动已添加到社区 {self.name}: {title}")
        return activity
    
    def remove_activity(self, activity_id: str) -> bool:
        """
        移除社区活动
        
        Args:
            activity_id: 活动ID
            
        Returns:
            移除是否成功
        """
        for i, activity in enumerate(self.activities):
            if activity["id"] == activity_id:
                self.activities.pop(i)
                logger.info(f"活动已从社区 {self.name} 移除: {activity_id}")
                return True
                
        logger.warning_status(f"活动不存在: {activity_id}")
        return False
    
    def join_activity(self, activity_id: str, agent_id: str) -> bool:
        """
        加入社区活动
        
        Args:
            activity_id: 活动ID
            agent_id: 代理ID
            
        Returns:
            加入是否成功
        """
        if agent_id not in self.members:
            logger.warning_status(f"成员不存在: {agent_id}")
            return False
            
        for activity in self.activities:
            if activity["id"] == activity_id:
                if agent_id in activity["participants"]:
                    logger.warning_status(f"成员已加入活动: {agent_id}")
                    return False
                    
                activity["participants"].append(agent_id)
                logger.info(f"成员已加入活动: {agent_id} -> {activity_id}")
                return True
                
        logger.warning_status(f"活动不存在: {activity_id}")
        return False
    
    def leave_activity(self, activity_id: str, agent_id: str) -> bool:
        """
        离开社区活动
        
        Args:
            activity_id: 活动ID
            agent_id: 代理ID
            
        Returns:
            离开是否成功
        """
        for activity in self.activities:
            if activity["id"] == activity_id:
                if agent_id not in activity["participants"]:
                    logger.warning_status(f"成员未加入活动: {agent_id}")
                    return False
                    
                activity["participants"].remove(agent_id)
                logger.info(f"成员已离开活动: {agent_id} -> {activity_id}")
                return True
                
        logger.warning_status(f"活动不存在: {activity_id}")
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将社区数据转换为字典
        
        Returns:
            社区数据字典
        """
        return {
            "community_id": self.community_id,
            "name": self.name,
            "description": self.description,
            "members": self.members,
            "roles": self.roles,
            "rules": self.rules,
            "activities": self.activities,
            "created_at": self.created_at,
            "last_active": self.last_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SocialCommunity':
        """
        从字典创建社区
        
        Args:
            data: 社区数据字典
            
        Returns:
            社区实例
        """
        community = cls(
            community_id=data.get("community_id", str(uuid.uuid4())),
            name=data.get("name", "未命名社区"),
            description=data.get("description", ""),
            config={}
        )
        
        community.members = data.get("members", {})
        community.roles = data.get("roles", {})
        community.rules = data.get("rules", [])
        community.activities = data.get("activities", [])
        community.created_at = data.get("created_at", time.time())
        community.last_active = data.get("last_active", time.time())
        
        return community 

class SocialNetwork(CognitiveModuleBase):
    """社交网络模块，管理数字生命体的社会关系网络"""
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化社交网络模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "social_network", "society", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取多代理系统
        self.multi_agent_system = get_multi_agent_system()
        
        # 社交关系网络图
        self.social_graph = nx.DiGraph()
        
        # 社区字典
        self.communities: Dict[str, SocialCommunity] = {}
        
        # 社会规范
        self.social_norms = []
        
        # 社会角色
        self.social_roles = {}
        
        # 配置
        self.storage_path = self.config.get("storage_path", os.path.join(root_dir, "data", "society", "social_network"))
        
        # 创建存储目录
        os.makedirs(self.storage_path, exist_ok=True)
        
        # 加载数据
        self._load_data()
        
        # 订阅事件
        self._subscribe_events()
        
        logger.info(f"社交网络模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅事件"""
        self.event_bus.subscribe("agent_message", self._on_agent_message)
        self.event_bus.subscribe("agent_created", self._on_agent_created)
        self.event_bus.subscribe("agent_updated", self._on_agent_updated)
        self.event_bus.subscribe("community_created", self._on_community_created)
        self.event_bus.subscribe("community_updated", self._on_community_updated)
        self.event_bus.subscribe("relationship_updated", self._on_relationship_updated)
        
        logger.info("社交网络模块已订阅事件")
    
    def _on_agent_message(self, data: Dict[str, Any]):
        """处理代理消息事件"""
        source_id = data.get("source_id")
        target_id = data.get("target_id")
        
        if not (source_id and target_id):
            return
            
        # 更新代理活跃度
        self._update_agent_activity(source_id)
        
        # 强化代理关系
        self._strengthen_relationship(source_id, target_id)
        
        logger.debug(f"处理代理消息事件: {source_id} -> {target_id}")
    
    def _on_agent_created(self, data: Dict[str, Any]):
        """处理代理创建事件"""
        agent_id = data.get("agent_id")
        name = data.get("name")
        
        if not (agent_id and name):
            return
            
        # 添加代理节点
        self._add_agent_node(agent_id, name)
        
        logger.debug(f"处理代理创建事件: {agent_id}")
    
    def _on_agent_updated(self, data: Dict[str, Any]):
        """处理代理更新事件"""
        agent_id = data.get("agent_id")
        updates = data.get("updates", {})
        
        if not agent_id:
            return
            
        # 更新代理节点
        if agent_id in self.social_graph.nodes:
            for key, value in updates.items():
                self.social_graph.nodes[agent_id][key] = value
            
            # 更新最后活跃时间
            self.social_graph.nodes[agent_id]["last_active"] = time.time()
            
        logger.debug(f"处理代理更新事件: {agent_id}")
    
    def _on_community_created(self, data: Dict[str, Any]):
        """处理社区创建事件"""
        community_id = data.get("community_id")
        name = data.get("name")
        description = data.get("description", "")
        
        if not (community_id and name):
            return
            
        # 创建社区
        self.create_community(community_id, name, description)
        
        logger.debug(f"处理社区创建事件: {community_id}")
    
    def _on_community_updated(self, data: Dict[str, Any]):
        """处理社区更新事件"""
        community_id = data.get("community_id")
        updates = data.get("updates", {})
        
        if not community_id:
            return
            
        # 更新社区
        if community_id in self.communities:
            community = self.communities[community_id]
            
            if "name" in updates:
                community.name = updates["name"]
            
            if "description" in updates:
                community.description = updates["description"]
            
            # 更新最后活跃时间
            community.last_active = time.time()
            
        logger.debug(f"处理社区更新事件: {community_id}")
    
    def _on_relationship_updated(self, data: Dict[str, Any]):
        """处理关系更新事件"""
        source_id = data.get("source_id")
        target_id = data.get("target_id")
        relationship = data.get("relationship", {})
        
        if not (source_id and target_id and relationship):
            return
            
        # 更新代理关系
        self._update_relationship(source_id, target_id, relationship)
        
        logger.debug(f"处理关系更新事件: {source_id} -> {target_id}")
    
    def _load_data(self):
        """加载社交网络数据"""
        try:
            # 创建存储目录
            communities_dir = os.path.join(self.storage_path, "communities")
            os.makedirs(communities_dir, exist_ok=True)
            
            # 加载社交图
            self._load_social_graph()
            
            # 加载社区
            self._load_communities()
            
            # 加载社会规范
            self._load_social_norms()
            
            # 加载社会角色
            self._load_social_roles()
            
            logger.success("社交网络数据加载完成")
        except Exception as e:
            logger.error_status(f"加载社交网络数据失败: {str(e)}")
    
    def _load_social_graph(self):
        """加载社交网络图"""
        nodes_file = os.path.join(self.storage_path, "nodes.json")
        edges_file = os.path.join(self.storage_path, "edges.json")
        
        if os.path.exists(nodes_file):
            try:
                with open(nodes_file, "r", encoding="utf-8") as f:
                    nodes_data = json.load(f)
                    
                for node_id, attrs in nodes_data.items():
                    self.social_graph.add_node(node_id, **attrs)
                    
                logger.info(f"已加载 {len(nodes_data)} 个节点")
            except Exception as e:
                logger.error_status(f"加载节点数据失败: {e}")
        
        if os.path.exists(edges_file):
            try:
                with open(edges_file, "r", encoding="utf-8") as f:
                    edges_data = json.load(f)
                    
                for edge in edges_data:
                    source = edge.pop("source")
                    target = edge.pop("target")
                    self.social_graph.add_edge(source, target, **edge)
                    
                logger.info(f"已加载 {len(edges_data)} 个边")
            except Exception as e:
                logger.error_status(f"加载边数据失败: {e}")
    
    def _load_communities(self):
        """加载社区数据"""
        communities_dir = os.path.join(self.storage_path, "communities")
        os.makedirs(communities_dir, exist_ok=True)
        
        try:
            for filename in os.listdir(communities_dir):
                if filename.endswith(".json"):
                    file_path = os.path.join(communities_dir, filename)
                    with open(file_path, "r", encoding="utf-8") as f:
                        community_data = json.load(f)
                        
                    community = SocialCommunity.from_dict(community_data)
                    self.communities[community.community_id] = community
                    
            logger.info(f"已加载 {len(self.communities)} 个社区")
        except Exception as e:
            logger.error_status(f"加载社区数据失败: {e}")
    
    def _load_social_norms(self):
        """加载社会规范"""
        norms_file = os.path.join(self.storage_path, "social_norms.json")
        
        if os.path.exists(norms_file):
            try:
                with open(norms_file, "r", encoding="utf-8") as f:
                    self.social_norms = json.load(f)
                    
                logger.info(f"已加载 {len(self.social_norms)} 个社会规范")
            except Exception as e:
                logger.error_status(f"加载社会规范失败: {e}")
    
    def _load_social_roles(self):
        """加载社会角色"""
        roles_file = os.path.join(self.storage_path, "social_roles.json")
        
        if os.path.exists(roles_file):
            try:
                with open(roles_file, "r", encoding="utf-8") as f:
                    self.social_roles = json.load(f)
                    
                logger.info(f"已加载 {len(self.social_roles)} 个社会角色")
            except Exception as e:
                logger.error_status(f"加载社会角色失败: {e}")
    
    def _save_data(self):
        """保存数据"""
        self._save_social_graph()
        self._save_communities()
        self._save_social_norms()
        self._save_social_roles()
        
        logger.data_status("社交网络数据已保存")
    
    def _save_social_graph(self):
        """保存社交网络图"""
        nodes_file = os.path.join(self.storage_path, "nodes.json")
        edges_file = os.path.join(self.storage_path, "edges.json")
        
        try:
            # 保存节点
            nodes_data = {node: data for node, data in self.social_graph.nodes(data=True)}
            with open(nodes_file, "w", encoding="utf-8") as f:
                json.dump(nodes_data, f, ensure_ascii=False, indent=2)
                
            # 保存边
            edges_data = []
            for source, target, data in self.social_graph.edges(data=True):
                edge_data = data.copy()
                edge_data["source"] = source
                edge_data["target"] = target
                edges_data.append(edge_data)
                
            with open(edges_file, "w", encoding="utf-8") as f:
                json.dump(edges_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已保存社交网络图: {len(nodes_data)} 个节点, {len(edges_data)} 个边")
        except Exception as e:
            logger.error_status(f"保存社交网络图失败: {e}")
    
    def _save_communities(self):
        """保存社区数据"""
        communities_dir = os.path.join(self.storage_path, "communities")
        os.makedirs(communities_dir, exist_ok=True)
        
        try:
            for community_id, community in self.communities.items():
                file_path = os.path.join(communities_dir, f"{community_id}.json")
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(community.to_dict(), f, ensure_ascii=False, indent=2)
                    
            logger.info(f"已保存 {len(self.communities)} 个社区")
        except Exception as e:
            logger.error_status(f"保存社区数据失败: {e}")
    
    def _save_social_norms(self):
        """保存社会规范"""
        norms_file = os.path.join(self.storage_path, "social_norms.json")
        
        try:
            with open(norms_file, "w", encoding="utf-8") as f:
                json.dump(self.social_norms, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已保存 {len(self.social_norms)} 个社会规范")
        except Exception as e:
            logger.error_status(f"保存社会规范失败: {e}")
    
    def _save_social_roles(self):
        """保存社会角色"""
        roles_file = os.path.join(self.storage_path, "social_roles.json")
        
        try:
            with open(roles_file, "w", encoding="utf-8") as f:
                json.dump(self.social_roles, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已保存 {len(self.social_roles)} 个社会角色")
        except Exception as e:
            logger.error_status(f"保存社会角色失败: {e}")
    
    def _add_agent_node(self, agent_id: str, name: str):
        """
        添加代理节点
        
        Args:
            agent_id: 代理ID
            name: 代理名称
        """
        if agent_id in self.social_graph.nodes:
            return
            
        self.social_graph.add_node(
            agent_id,
            name=name,
            type="agent",
            created_at=time.time(),
            last_active=time.time()
        )
        
        logger.debug(f"添加代理节点: {agent_id}")
    
    def _update_agent_activity(self, agent_id: str):
        """
        更新代理活跃度
        
        Args:
            agent_id: 代理ID
        """
        if agent_id in self.social_graph.nodes:
            self.social_graph.nodes[agent_id]["last_active"] = time.time()
    
    def _strengthen_relationship(self, source_id: str, target_id: str, strength: float = 0.01):
        """
        强化代理关系
        
        Args:
            source_id: 源代理ID
            target_id: 目标代理ID
            strength: 强化强度
        """
        if source_id not in self.social_graph.nodes:
            self._add_agent_node(source_id, source_id)
            
        if target_id not in self.social_graph.nodes:
            self._add_agent_node(target_id, target_id)
            
        if self.social_graph.has_edge(source_id, target_id):
            # 更新边权重
            current_weight = self.social_graph[source_id][target_id].get("weight", 0.0)
            new_weight = min(1.0, current_weight + strength)
            self.social_graph[source_id][target_id]["weight"] = new_weight
            self.social_graph[source_id][target_id]["last_updated"] = time.time()
        else:
            # 添加新边
            self.social_graph.add_edge(
                source_id,
                target_id,
                type="interaction",
                weight=strength,
                created_at=time.time(),
                last_updated=time.time()
            )
    
    def _update_relationship(self, source_id: str, target_id: str, relationship: Dict[str, Any]):
        """
        更新代理关系
        
        Args:
            source_id: 源代理ID
            target_id: 目标代理ID
            relationship: 关系数据
        """
        if source_id not in self.social_graph.nodes:
            self._add_agent_node(source_id, source_id)
            
        if target_id not in self.social_graph.nodes:
            self._add_agent_node(target_id, target_id)
            
        # 更新或添加边
        if self.social_graph.has_edge(source_id, target_id):
            # 更新现有边
            for key, value in relationship.items():
                self.social_graph[source_id][target_id][key] = value
                
            self.social_graph[source_id][target_id]["last_updated"] = time.time()
        else:
            # 添加新边
            edge_data = relationship.copy()
            edge_data["created_at"] = time.time()
            edge_data["last_updated"] = time.time()
            self.social_graph.add_edge(source_id, target_id, **edge_data)
    
    def create_community(self, community_id: str, name: str, description: str = "", config: Dict[str, Any] = None) -> Optional[SocialCommunity]:
        """
        创建社区
        
        Args:
            community_id: 社区ID
            name: 社区名称
            description: 社区描述
            config: 社区配置
            
        Returns:
            社区对象或None
        """
        if community_id in self.communities:
            logger.warning_status(f"社区已存在: {community_id}")
            return None
            
        community = SocialCommunity(community_id, name, description, config)
        self.communities[community_id] = community
        
        # 保存社区数据
        self._save_communities()
        
        logger.info(f"创建社区: {name} (ID: {community_id})")
        return community
    
    def delete_community(self, community_id: str) -> bool:
        """
        删除社区
        
        Args:
            community_id: 社区ID
            
        Returns:
            删除是否成功
        """
        if community_id not in self.communities:
            logger.warning_status(f"社区不存在: {community_id}")
            return False
            
        del self.communities[community_id]
        
        # 删除社区文件
        file_path = os.path.join(self.storage_path, "communities", f"{community_id}.json")
        if os.path.exists(file_path):
            os.remove(file_path)
            
        logger.info(f"删除社区: {community_id}")
        return True
    
    def get_community(self, community_id: str) -> Optional[SocialCommunity]:
        """
        获取社区
        
        Args:
            community_id: 社区ID
            
        Returns:
            社区对象或None
        """
        return self.communities.get(community_id)
    
    def get_all_communities(self) -> List[SocialCommunity]:
        """
        获取所有社区
        
        Returns:
            社区列表
        """
        return list(self.communities.values())
    
    def get_agent_communities(self, agent_id: str) -> List[SocialCommunity]:
        """
        获取代理所在的社区
        
        Args:
            agent_id: 代理ID
            
        Returns:
            社区列表
        """
        return [
            community for community in self.communities.values()
            if agent_id in community.members
        ]
    
    def add_social_norm(self, title: str, description: str, scope: str = "global", priority: int = 5) -> Dict[str, Any]:
        """
        添加社会规范
        
        Args:
            title: 规范标题
            description: 规范描述
            scope: 规范范围（global, community, group）
            priority: 规范优先级（1-10，1为最高）
            
        Returns:
            规范信息
        """
        norm = {
            "id": str(uuid.uuid4()),
            "title": title,
            "description": description,
            "scope": scope,
            "priority": max(1, min(10, priority)),
            "created_at": time.time()
        }
        
        self.social_norms.append(norm)
        
        # 按优先级排序
        self.social_norms.sort(key=lambda x: x["priority"])
        
        # 保存社会规范
        self._save_social_norms()
        
        logger.info(f"添加社会规范: {title}")
        return norm
    
    def remove_social_norm(self, norm_id: str) -> bool:
        """
        移除社会规范
        
        Args:
            norm_id: 规范ID
            
        Returns:
            移除是否成功
        """
        for i, norm in enumerate(self.social_norms):
            if norm["id"] == norm_id:
                self.social_norms.pop(i)
                
                # 保存社会规范
                self._save_social_norms()
                
                logger.info(f"移除社会规范: {norm_id}")
                return True
                
        logger.warning_status(f"社会规范不存在: {norm_id}")
        return False
    
    def add_social_role(self, role_name: str, role_info: Dict[str, Any]) -> bool:
        """
        添加社会角色
        
        Args:
            role_name: 角色名称
            role_info: 角色信息
            
        Returns:
            添加是否成功
        """
        if role_name in self.social_roles:
            logger.warning_status(f"社会角色已存在: {role_name}")
            return False
            
        self.social_roles[role_name] = role_info
        
        # 保存社会角色
        self._save_social_roles()
        
        logger.info(f"添加社会角色: {role_name}")
        return True
    
    def remove_social_role(self, role_name: str) -> bool:
        """
        移除社会角色
        
        Args:
            role_name: 角色名称
            
        Returns:
            移除是否成功
        """
        if role_name not in self.social_roles:
            logger.warning_status(f"社会角色不存在: {role_name}")
            return False
            
        del self.social_roles[role_name]
        
        # 保存社会角色
        self._save_social_roles()
        
        logger.info(f"移除社会角色: {role_name}")
        return True
    
    def get_relationships(self, agent_id: str = None) -> List[Dict[str, Any]]:
        """
        获取关系
        
        Args:
            agent_id: 代理ID，如果为None则返回所有关系
            
        Returns:
            关系列表
        """
        relationships = []
        
        if agent_id:
            # 获取特定代理的关系
            if agent_id in self.social_graph.nodes:
                # 出边（代理 -> 其他）
                for _, target, data in self.social_graph.out_edges(agent_id, data=True):
                    rel = data.copy()
                    rel["source"] = agent_id
                    rel["target"] = target
                    relationships.append(rel)
                
                # 入边（其他 -> 代理）
                for source, _, data in self.social_graph.in_edges(agent_id, data=True):
                    rel = data.copy()
                    rel["source"] = source
                    rel["target"] = agent_id
                    relationships.append(rel)
        else:
            # 获取所有关系
            for source, target, data in self.social_graph.edges(data=True):
                rel = data.copy()
                rel["source"] = source
                rel["target"] = target
                relationships.append(rel)
                
        return relationships
    
    def get_network_stats(self) -> Dict[str, Any]:
        """
        获取网络统计信息
        
        Returns:
            统计信息
        """
        stats = {
            "node_count": len(self.social_graph.nodes),
            "edge_count": len(self.social_graph.edges),
            "community_count": len(self.communities),
            "norm_count": len(self.social_norms),
            "role_count": len(self.social_roles),
            "last_updated": time.time()
        }
        
        return stats
    
    def get_community_stats(self, community_id: str = None) -> Dict[str, Any]:
        """
        获取社区统计信息
        
        Args:
            community_id: 社区ID，如果为None则返回所有社区的统计信息
            
        Returns:
            统计信息
        """
        if community_id:
            if community_id not in self.communities:
                return {}
                
            community = self.communities[community_id]
            return {
                "community_id": community_id,
                "name": community.name,
                "member_count": len(community.members),
                "role_count": len(community.roles),
                "rule_count": len(community.rules),
                "activity_count": len(community.activities),
                "created_at": community.created_at,
                "last_active": community.last_active
            }
        else:
            stats = {}
            for community_id, community in self.communities.items():
                stats[community_id] = {
                    "name": community.name,
                    "member_count": len(community.members),
                    "role_count": len(community.roles),
                    "rule_count": len(community.rules),
                    "activity_count": len(community.activities),
                    "created_at": community.created_at,
                    "last_active": community.last_active
                }
            return stats
    
    def shutdown(self):
        """关闭社交网络模块"""
        # 保存数据
        self._save_data()
        
        logger.info(f"社交网络模块 {self.module_id} 已关闭")

    def _initialize_module(self) -> bool:
        """
        初始化模块
        
        Returns:
            初始化是否成功
        """
        try:
            # 创建存储目录
            os.makedirs(self.storage_path, exist_ok=True)
            
            # 加载数据
            self._load_data()
            
            # 订阅事件
            self._subscribe_events()
            
            return True
        except Exception as e:
            logger.error_status(f"初始化社交网络模块失败: {str(e)}")
            return False

    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        try:
            action = input_data.get("action")
            params = input_data.get("params", {})
            
            if action == "create_community":
                community_id = params.get("community_id", str(uuid.uuid4()))
                name = params.get("name")
                description = params.get("description", "")
                config = params.get("config", {})
                
                if not name:
                    return {"success": False, "error": "缺少社区名称"}
                    
                community = self.create_community(community_id, name, description, config)
                if community:
                    return {"success": True, "community": community.to_dict()}
                else:
                    return {"success": False, "error": "创建社区失败"}
                    
            elif action == "delete_community":
                community_id = params.get("community_id")
                
                if not community_id:
                    return {"success": False, "error": "缺少社区ID"}
                    
                success = self.delete_community(community_id)
                return {"success": success}
                
            elif action == "get_community":
                community_id = params.get("community_id")
                
                if not community_id:
                    return {"success": False, "error": "缺少社区ID"}
                    
                community = self.get_community(community_id)
                if community:
                    return {"success": True, "community": community.to_dict()}
                else:
                    return {"success": False, "error": "社区不存在"}
                    
            elif action == "get_all_communities":
                communities = self.get_all_communities()
                return {"success": True, "communities": [c.to_dict() for c in communities]}
                
            elif action == "add_social_norm":
                title = params.get("title")
                description = params.get("description", "")
                scope = params.get("scope", "global")
                priority = params.get("priority", 5)
                
                if not title:
                    return {"success": False, "error": "缺少规范标题"}
                    
                norm = self.add_social_norm(title, description, scope, priority)
                return {"success": True, "norm": norm}
                
            elif action == "add_social_role":
                role_name = params.get("role_name")
                role_info = params.get("role_info", {})
                
                if not role_name:
                    return {"success": False, "error": "缺少角色名称"}
                    
                success = self.add_social_role(role_name, role_info)
                return {"success": success}
                
            elif action == "get_network_stats":
                stats = self.get_network_stats()
                return {"success": True, "stats": stats}
                
            else:
                return {"success": False, "error": f"未知操作: {action}"}
                
        except Exception as e:
            logger.error_status(f"处理输入数据失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _update_module(self) -> bool:
        """
        更新模块
        
        Returns:
            更新是否成功
        """
        try:
            # 保存数据
            self._save_data()
            
            return True
        except Exception as e:
            logger.error_status(f"更新社交网络模块失败: {str(e)}")
            return False

    def _load_module_state(self) -> Dict[str, Any]:
        """
        加载模块状态
        
        Returns:
            模块状态
        """
        try:
            state = {
                "social_graph": {
                    "nodes": [{"id": node, "data": data} for node, data in self.social_graph.nodes(data=True)],
                    "edges": [{"source": s, "target": t, "data": data} for s, t, data in self.social_graph.edges(data=True)]
                },
                "communities": {community_id: community.to_dict() for community_id, community in self.communities.items()},
                "social_norms": self.social_norms,
                "social_roles": self.social_roles
            }
            
            return state
        except Exception as e:
            logger.error_status(f"加载模块状态失败: {str(e)}")
            return {}

    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        return self._load_module_state()

    def _shutdown_module(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        try:
            # 保存数据
            self._save_data()
            
            return True
        except Exception as e:
            logger.error_status(f"关闭社交网络模块失败: {str(e)}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        try:
            status = {
                "module_id": self.module_id,
                "module_type": "society",
                "node_count": len(self.social_graph.nodes),
                "edge_count": len(self.social_graph.edges),
                "community_count": len(self.communities),
                "norm_count": len(self.social_norms),
                "role_count": len(self.social_roles),
                "last_updated": time.time()
            }
            
            return status
        except Exception as e:
            logger.error_status(f"获取模块状态失败: {str(e)}")
            return {"error": str(e)}

def get_instance(config: Dict[str, Any] = None) -> SocialNetwork:
    """获取社交网络模块实例"""
    return SocialNetwork(config=config) 