#!/usr/bin/env python3
"""
多代理系统模块 - Multi-Agent System Module

该模块提供了多个数字生命体之间交互的框架，包括：
1. 代理管理 - 创建、跟踪和管理多个数字生命体实例
2. 消息传递 - 代理之间的通信机制
3. 协作行为 - 代理之间的协作和任务分配
4. 资源共享 - 代理之间的知识和资源共享
5. 冲突解决 - 处理代理之间可能出现的冲突

作者: Claude
创建日期: 2024-09-10
版本: 1.0
"""

import os
import sys
import time
import json
import uuid
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
society_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(society_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.society.multi_agent_system")

class Agent:
    """代理类，代表一个数字生命体实例"""
    
    def __init__(self, agent_id: str, name: str, config: Dict[str, Any] = None):
        """
        初始化代理
        
        Args:
            agent_id: 代理ID
            name: 代理名称
            config: 代理配置
        """
        self.agent_id = agent_id
        self.name = name
        self.config = config or {}
        self.status = "inactive"  # 状态: inactive, active, busy, offline
        self.capabilities = []  # 代理能力列表
        self.relationships = {}  # 与其他代理的关系
        self.message_queue = []  # 消息队列
        self.knowledge_base = {}  # 代理知识库
        self.created_at = time.time()
        self.last_active = time.time()
        
        # 初始化代理数据
        self._initialize()
        
        logger.info(f"代理 {self.name} (ID: {self.agent_id}) 已创建")
    
    def _initialize(self):
        """初始化代理数据"""
        # 加载代理能力
        self.capabilities = self.config.get("capabilities", [
            "chat", "knowledge_sharing"
        ])
        
        # 初始化关系数据
        self.relationships = self.config.get("relationships", {})
    
    def update_status(self, status: str) -> bool:
        """
        更新代理状态
        
        Args:
            status: 新状态
            
        Returns:
            更新是否成功
        """
        if status not in ["inactive", "active", "busy", "offline"]:
            logger.warning_status(f"无效的代理状态: {status}")
            return False
            
        self.status = status
        self.last_active = time.time()
        return True
    
    def add_message(self, message: Dict[str, Any]) -> bool:
        """
        添加消息到队列
        
        Args:
            message: 消息数据
            
        Returns:
            添加是否成功
        """
        if not isinstance(message, dict) or "content" not in message:
            logger.warning_status("无效的消息格式")
            return False
            
        # 确保消息有ID和时间戳
        if "message_id" not in message:
            message["message_id"] = str(uuid.uuid4())
        if "timestamp" not in message:
            message["timestamp"] = time.time()
            
        self.message_queue.append(message)
        return True
    
    def get_messages(self, count: int = 10) -> List[Dict[str, Any]]:
        """
        获取消息队列
        
        Args:
            count: 要获取的消息数量
            
        Returns:
            消息列表
        """
        return self.message_queue[-count:] if self.message_queue else []
    
    def add_capability(self, capability: str) -> bool:
        """
        添加代理能力
        
        Args:
            capability: 能力名称
            
        Returns:
            添加是否成功
        """
        if capability in self.capabilities:
            return False
            
        self.capabilities.append(capability)
        return True
    
    def has_capability(self, capability: str) -> bool:
        """
        检查代理是否有某种能力
        
        Args:
            capability: 能力名称
            
        Returns:
            是否具有该能力
        """
        return capability in self.capabilities
    
    def update_relationship(self, target_agent_id: str, relationship: Dict[str, Any]) -> bool:
        """
        更新与其他代理的关系
        
        Args:
            target_agent_id: 目标代理ID
            relationship: 关系数据
            
        Returns:
            更新是否成功
        """
        if not isinstance(relationship, dict):
            return False
            
        if target_agent_id not in self.relationships:
            self.relationships[target_agent_id] = {}
            
        self.relationships[target_agent_id].update(relationship)
        return True
    
    def get_relationship(self, target_agent_id: str) -> Optional[Dict[str, Any]]:
        """
        获取与特定代理的关系
        
        Args:
            target_agent_id: 目标代理ID
            
        Returns:
            关系数据或None（如果不存在）
        """
        return self.relationships.get(target_agent_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将代理数据转换为字典
        
        Returns:
            代理数据字典
        """
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status,
            "capabilities": self.capabilities,
            "relationships": self.relationships,
            "created_at": self.created_at,
            "last_active": self.last_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Agent':
        """
        从字典创建代理
        
        Args:
            data: 代理数据字典
            
        Returns:
            代理实例
        """
        agent = cls(
            agent_id=data.get("agent_id", str(uuid.uuid4())),
            name=data.get("name", "未命名代理"),
            config={}
        )
        
        agent.status = data.get("status", "inactive")
        agent.capabilities = data.get("capabilities", [])
        agent.relationships = data.get("relationships", {})
        agent.created_at = data.get("created_at", time.time())
        agent.last_active = data.get("last_active", time.time())
        
        return agent

class MultiAgentSystem(CognitiveModuleBase):
    """多代理系统，管理多个数字生命体实例之间的交互"""
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化多代理系统
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "multi_agent_system", "society", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 配置参数
        self.max_agents = self.config.get("max_agents", 100)
        self.max_history = self.config.get("max_history", 1000)
        
        # 代理字典，键为代理ID，值为代理实例
        self.agents: Dict[str, Agent] = {}
        
        # 任务字典，键为任务ID，值为任务信息
        self.tasks: Dict[str, Dict[str, Any]] = {}
        
        # 消息历史
        self.message_history: List[Dict[str, Any]] = []
        
        # 存储路径
        self.storage_path = self.config.get("storage_path", os.path.join(root_dir, "data", "society", "agents"))
        
        # 创建存储目录
        os.makedirs(self.storage_path, exist_ok=True)
        
        # 加载代理数据
        self._load_agents()
        
        # 订阅事件
        self._subscribe_events()
        
        logger.info(f"多代理系统 {self.module_id} 已创建，当前代理数量: {len(self.agents)}")

    def _initialize_module(self) -> bool:
        """
        初始化模块
        
        Returns:
            初始化是否成功
        """
        try:
            # 创建存储目录
            os.makedirs(self.storage_path, exist_ok=True)
            
            # 加载代理数据
            self._load_agents()
            
            # 订阅事件
            self._subscribe_events()
            
            return True
        except Exception as e:
            logger.error_status(f"初始化多代理系统失败: {str(e)}")
            return False
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        try:
            action = input_data.get("action")
            params = input_data.get("params", {})
            
            if action == "create_agent":
                agent_id = params.get("agent_id", str(uuid.uuid4()))
                name = params.get("name")
                config = params.get("config", {})
                
                if not name:
                    return {"success": False, "error": "缺少代理名称"}
                    
                agent = self.create_agent(agent_id, name, config)
                if agent:
                    return {"success": True, "agent": agent.to_dict()}
                else:
                    return {"success": False, "error": "创建代理失败"}
                    
            elif action == "delete_agent":
                agent_id = params.get("agent_id")
                
                if not agent_id:
                    return {"success": False, "error": "缺少代理ID"}
                    
                success = self.delete_agent(agent_id)
                return {"success": success}
                
            elif action == "get_agent":
                agent_id = params.get("agent_id")
                
                if not agent_id:
                    return {"success": False, "error": "缺少代理ID"}
                    
                agent = self.get_agent(agent_id)
                if agent:
                    return {"success": True, "agent": agent.to_dict()}
                else:
                    return {"success": False, "error": "代理不存在"}
                    
            elif action == "get_all_agents":
                agents = self.get_all_agents()
                return {"success": True, "agents": [a.to_dict() for a in agents]}
                
            elif action == "send_message":
                source_id = params.get("source_id")
                target_id = params.get("target_id")
                content = params.get("content")
                metadata = params.get("metadata", {})
                
                if not source_id or not target_id or not content:
                    return {"success": False, "error": "缺少必要参数"}
                    
                success = self.send_message(source_id, target_id, content, metadata)
                return {"success": success}
                
            elif action == "create_task":
                task_type = params.get("task_type")
                participants = params.get("participants", [])
                details = params.get("details", {})
                
                if not task_type or not participants:
                    return {"success": False, "error": "缺少必要参数"}
                    
                task = self.create_task(task_type, participants, details)
                return {"success": True, "task": task}
                
            else:
                return {"success": False, "error": f"未知操作: {action}"}
                
        except Exception as e:
            logger.error_status(f"处理输入数据失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _update_module(self) -> bool:
        """
        更新模块
        
        Returns:
            更新是否成功
        """
        try:
            # 保存代理数据
            self._save_agents()
            
            return True
        except Exception as e:
            logger.error_status(f"更新多代理系统失败: {str(e)}")
            return False
    
    def _load_module_state(self) -> Dict[str, Any]:
        """
        加载模块状态
        
        Returns:
            模块状态
        """
        try:
            state = {
                "agents": {agent_id: agent.to_dict() for agent_id, agent in self.agents.items()},
                "tasks": self.tasks,
                "message_history": self.message_history[:100]  # 只返回最近100条消息以节省空间
            }
            
            return state
        except Exception as e:
            logger.error_status(f"加载模块状态失败: {str(e)}")
            return {}
    
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        return self._load_module_state()
    
    def _shutdown_module(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        try:
            # 保存代理数据
            self._save_agents()
            
            return True
        except Exception as e:
            logger.error_status(f"关闭多代理系统失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        try:
            status = {
                "module_id": self.module_id,
                "module_type": "society",
                "agent_count": len(self.agents),
                "task_count": len(self.tasks),
                "message_count": len(self.message_history),
                "last_updated": time.time()
            }
            
            return status
        except Exception as e:
            logger.error_status(f"获取模块状态失败: {str(e)}")
            return {"error": str(e)}
    
    def _subscribe_events(self):
        """订阅事件"""
        self.event_bus.subscribe("agent_message", self._on_agent_message)
        self.event_bus.subscribe("agent_created", self._on_agent_created)
        self.event_bus.subscribe("agent_updated", self._on_agent_updated)
        self.event_bus.subscribe("task_created", self._on_task_created)
        
        logger.info("多代理系统已订阅事件")
    
    def _on_agent_message(self, data: Dict[str, Any]):
        """处理代理消息事件"""
        source_id = data.get("source_id")
        target_id = data.get("target_id")
        content = data.get("content")
        
        if not (source_id and target_id and content):
            logger.warning_status("无效的代理消息")
            return
            
        if target_id not in self.agents:
            logger.warning_status(f"目标代理不存在: {target_id}")
            return
            
        message = {
            "message_id": data.get("message_id", str(uuid.uuid4())),
            "source_id": source_id,
            "target_id": target_id,
            "content": content,
            "timestamp": data.get("timestamp", time.time()),
            "metadata": data.get("metadata", {})
        }
        
        # 添加到目标代理的消息队列
        self.agents[target_id].add_message(message)
        
        # 添加到消息历史
        self.message_history.append(message)
        if len(self.message_history) > self.max_history:
            self.message_history = self.message_history[-self.max_history:]
            
        logger.info(f"处理代理消息: {source_id} -> {target_id}")
    
    def _on_agent_created(self, data: Dict[str, Any]):
        """处理代理创建事件"""
        agent_id = data.get("agent_id")
        name = data.get("name")
        config = data.get("config", {})
        
        if not (agent_id and name):
            logger.warning_status("无效的代理创建事件")
            return
            
        self.create_agent(agent_id, name, config)
        logger.info(f"处理代理创建事件: {name} (ID: {agent_id})")
    
    def _on_agent_updated(self, data: Dict[str, Any]):
        """处理代理更新事件"""
        agent_id = data.get("agent_id")
        updates = data.get("updates", {})
        
        if not (agent_id and updates):
            logger.warning_status("无效的代理更新事件")
            return
            
        if agent_id not in self.agents:
            logger.warning_status(f"代理不存在: {agent_id}")
            return
            
        # 更新代理状态
        if "status" in updates:
            self.agents[agent_id].update_status(updates["status"])
            
        # 更新关系
        if "relationships" in updates:
            for target_id, relationship in updates["relationships"].items():
                self.agents[agent_id].update_relationship(target_id, relationship)
                
        logger.info(f"处理代理更新事件: {agent_id}")
    
    def _on_task_created(self, data: Dict[str, Any]):
        """处理任务创建事件"""
        task_id = data.get("task_id")
        task_type = data.get("task_type")
        participants = data.get("participants", [])
        details = data.get("details", {})
        
        if not (task_id and task_type):
            logger.warning_status("无效的任务创建事件")
            return
            
        task = {
            "task_id": task_id,
            "task_type": task_type,
            "participants": participants,
            "details": details,
            "status": "pending",
            "created_at": time.time()
        }
        
        self.tasks[task_id] = task
        logger.info(f"处理任务创建事件: {task_id} (类型: {task_type})")
    
    def _load_agents(self):
        """加载已有代理"""
        agents_file = os.path.join(self.storage_path, "agents.json")
        if not os.path.exists(agents_file):
            return
            
        try:
            with open(agents_file, "r", encoding="utf-8") as f:
                agents_data = json.load(f)
                
            for agent_data in agents_data:
                agent = Agent.from_dict(agent_data)
                self.agents[agent.agent_id] = agent
                
            logger.info(f"已加载 {len(self.agents)} 个代理")
        except Exception as e:
            logger.error_status(f"加载代理失败: {e}")
    
    def _save_agents(self):
        """保存代理数据"""
        agents_file = os.path.join(self.storage_path, "agents.json")
        
        try:
            agents_data = [agent.to_dict() for agent in self.agents.values()]
            
            with open(agents_file, "w", encoding="utf-8") as f:
                json.dump(agents_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已保存 {len(self.agents)} 个代理")
        except Exception as e:
            logger.error_status(f"保存代理失败: {e}")
    
    def create_agent(self, agent_id: str, name: str, config: Dict[str, Any] = None) -> Optional[Agent]:
        """
        创建新代理
        
        Args:
            agent_id: 代理ID
            name: 代理名称
            config: 代理配置
            
        Returns:
            新创建的代理实例或None（如果创建失败）
        """
        try:
            # 检查代理ID是否已存在
            if agent_id in self.agents:
                logger.warning_status(f"代理ID已存在: {agent_id}")
                return None
            
            # 创建新代理
            new_agent = Agent(agent_id, name, config)
            
            # 添加到代理字典
            self.agents[agent_id] = new_agent
            
            # 发布代理创建事件
            self.event_bus.publish("agent_created", {
                "agent_id": agent_id,
                "name": name,
                "timestamp": time.time()
            })
            
            # 保存代理数据
            self._save_agents()
            
            logger.info(f"代理已创建: {name} (ID: {agent_id})")
            return new_agent
            
        except Exception as e:
            logger.error_status(f"创建代理失败: {str(e)}")
            return None
    
    def delete_agent(self, agent_id: str) -> bool:
        """
        删除代理
        
        Args:
            agent_id: 代理ID
            
        Returns:
            删除是否成功
        """
        if agent_id not in self.agents:
            logger.warning_status(f"代理不存在: {agent_id}")
            return False
            
        agent_name = self.agents[agent_id].name
        del self.agents[agent_id]
        
        # 保存代理数据
        self._save_agents()
        
        # 发布代理删除事件
        self.event_bus.publish("agent_deleted", {
            "agent_id": agent_id,
            "name": agent_name
        })
        
        logger.info(f"删除代理: {agent_name} (ID: {agent_id})")
        return True
    
    def get_agent(self, agent_id: str) -> Optional[Agent]:
        """
        获取代理
        
        Args:
            agent_id: 代理ID
            
        Returns:
            代理或None（如果不存在）
        """
        return self.agents.get(agent_id)
    
    def get_all_agents(self) -> List[Agent]:
        """
        获取所有代理
        
        Returns:
            代理列表
        """
        return list(self.agents.values())
    
    def send_message(self, source_id: str, target_id: str, content: str, metadata: Dict[str, Any] = None) -> bool:
        """
        发送消息
        
        Args:
            source_id: 发送者ID
            target_id: 接收者ID
            content: 消息内容
            metadata: 消息元数据
            
        Returns:
            发送是否成功
        """
        try:
            # 检查发送者和接收者是否存在
            if source_id not in self.agents:
                logger.warning_status(f"发送者不存在: {source_id}")
                return False
            
            if target_id not in self.agents:
                logger.warning_status(f"接收者不存在: {target_id}")
                return False
            
            # 获取发送者和接收者
            source_agent = self.agents[source_id]
            target_agent = self.agents[target_id]
            
            # 创建消息
            message_id = str(uuid.uuid4())
            timestamp = time.time()
            
            message = {
                "message_id": message_id,
                "source_id": source_id,
                "target_id": target_id,
                "content": content,
                "metadata": metadata or {},
                "timestamp": timestamp,
                "status": "delivered"
            }
            
            # 将消息添加到接收者的消息队列
            success = target_agent.add_message(message)
            
            if success:
                # 发布消息事件
                self.event_bus.publish("agent_message", {
                    "message_id": message_id,
                    "source_id": source_id,
                    "target_id": target_id,
                    "timestamp": timestamp
                })
                
                # 更新发送者和接收者的最后活动时间
                source_agent.last_active = timestamp
                target_agent.last_active = timestamp
                
                logger.info(f"消息已发送: {source_id} -> {target_id}")
                
            return success
            
        except Exception as e:
            logger.error_status(f"发送消息失败: {str(e)}")
            return False
    
    def get_messages(self, agent_id: str, count: int = 10) -> List[Dict[str, Any]]:
        """
        获取代理的消息
        
        Args:
            agent_id: 代理ID
            count: 要获取的消息数量
            
        Returns:
            消息列表
        """
        if agent_id not in self.agents:
            logger.warning_status(f"代理不存在: {agent_id}")
            return []
            
        return self.agents[agent_id].get_messages(count)
    
    def get_conversation(self, agent_id1: str, agent_id2: str, count: int = 20) -> List[Dict[str, Any]]:
        """
        获取两个代理之间的对话
        
        Args:
            agent_id1: 第一个代理ID
            agent_id2: 第二个代理ID
            count: 要获取的消息数量
            
        Returns:
            对话消息列表
        """
        conversation = []
        
        for message in reversed(self.message_history):
            source_id = message.get("source_id")
            target_id = message.get("target_id")
            
            if (source_id == agent_id1 and target_id == agent_id2) or \
               (source_id == agent_id2 and target_id == agent_id1):
                conversation.insert(0, message)
                
            if len(conversation) >= count:
                break
                
        return conversation
    
    def create_task(self, task_type: str, participants: List[str], details: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        创建任务
        
        Args:
            task_type: 任务类型
            participants: 参与者代理ID列表
            details: 任务详情
            
        Returns:
            任务数据
        """
        # 验证参与者
        for agent_id in participants:
            if agent_id not in self.agents:
                logger.warning_status(f"代理不存在: {agent_id}")
                return {}
                
        task_id = str(uuid.uuid4())
        task = {
            "task_id": task_id,
            "task_type": task_type,
            "participants": participants,
            "details": details or {},
            "status": "pending",
            "created_at": time.time()
        }
        
        self.tasks[task_id] = task
        
        # 发布任务创建事件
        self.event_bus.publish("task_created", task)
        
        logger.info(f"创建任务: {task_id} (类型: {task_type})")
        return task
    
    def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新任务
        
        Args:
            task_id: 任务ID
            updates: 更新内容
            
        Returns:
            更新是否成功
        """
        if task_id not in self.tasks:
            logger.warning_status(f"任务不存在: {task_id}")
            return False
            
        task = self.tasks[task_id]
        task.update(updates)
        self.tasks[task_id] = task
        
        # 发布任务更新事件
        self.event_bus.publish("task_updated", {
            "task_id": task_id,
            "updates": updates
        })
        
        logger.info(f"更新任务: {task_id}")
        return True
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务数据或None（如果不存在）
        """
        return self.tasks.get(task_id)
    
    def get_agent_tasks(self, agent_id: str) -> List[Dict[str, Any]]:
        """
        获取代理的任务
        
        Args:
            agent_id: 代理ID
            
        Returns:
            任务列表
        """
        return [task for task in self.tasks.values() if agent_id in task["participants"]]

def get_instance(config: Dict[str, Any] = None) -> MultiAgentSystem:
    """获取多代理系统实例"""
    return MultiAgentSystem(config=config) 