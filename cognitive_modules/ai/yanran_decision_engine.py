#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
林嫣然AI决策引擎 - Yanran AI Decision Engine

该模块实现林嫣然的AI增强决策能力，结合人格特点和上下文进行智能决策。

作者: <PERSON>
创建日期: 2024-12-15
版本: 2.0
"""

import os
import sys
import json
import time
import asyncio
import threading
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.append(project_root)

from utilities.unified_logger import get_unified_logger
from ai_service_adapter_wrapper import get_ai_service_adapter

# 使用统一的AI服务连接池
try:
    from ...core.ai_service_connection_pool import get_ai_service_connection_pool
except ImportError:
    # 兜底导入方式
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent))
    from core.ai_service_connection_pool import get_ai_service_connection_pool

# 🔥 老王修复：添加单例模式支持
_instance = None
_instance_lock = threading.RLock()

class DecisionResult:
    """AI决策结果"""
    
    def __init__(self, decision_data: Dict[str, Any]):
        self.raw_decision = decision_data
        self.decision_type = decision_data.get('decision_type', 'unknown')
        self.confidence = decision_data.get('confidence', 0.5)
        self.reasoning = decision_data.get('reasoning', '')
        self.actions = decision_data.get('actions', [])
        self.emotion_impact = decision_data.get('emotion_impact', {})
        self.should_act = decision_data.get('should_act', False)
        self.priority = decision_data.get('priority', 'normal')
        self.timestamp = datetime.now().isoformat()
    
    def get(self, key: str, default=None):
        """获取决策数据"""
        return self.raw_decision.get(key, default)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'decision_type': self.decision_type,
            'confidence': self.confidence,
            'reasoning': self.reasoning,
            'actions': self.actions,
            'emotion_impact': self.emotion_impact,
            'should_act': self.should_act,
            'priority': self.priority,
            'timestamp': self.timestamp,
            'raw_decision': self.raw_decision
        }


class YanranAIDecisionEngine:
    """
    林嫣然专用AI决策引擎
    
    为所有器官提供统一的AI决策能力，确保：
    1. 所有决策都体现林嫣然的人格特质
    2. 决策过程透明可解释
    3. 支持多种决策类型和场景
    4. 具备学习和适应能力
    """
    
    def __init__(self, config_path: Optional[str] = None, ai_adapter=None):
        """初始化决策引擎"""
        self.logger = get_unified_logger("yanran_decision_engine")
        
        # AI服务适配器
        self.ai_adapter = None
        self._ensure_ai_adapter()
        
        # 决策配置
        self.decision_config = self._load_decision_config()
        
        # 决策历史
        self.decision_history = []
        self.max_history_size = 100
        
        # 林嫣然人格档案
        self.personality_profile = self._load_personality_profile()
        
        # 加载林嫣然核心人格
        self.yanran_core_personality = self._load_core_personality()
        
        # 决策历史和学习
        self.decision_patterns = {}
        
        # 性能统计
        self.performance_stats = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'average_confidence': 0.0,
            'decision_types': {}
        }
        
        self.logger.info("林嫣然AI决策引擎初始化完成")
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            'decision_timeout': 300,
            'min_confidence_threshold': 0.3,
            'max_decision_history': 1000,
            'learning_enabled': True,
            'personality_adaptation': True
        }
        
        if config_path:
            try:
                config_file = Path(config_path)
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        user_config = json.load(f)
                        default_config.update(user_config)
            except Exception as e:
                self.logger.warning(f"加载配置失败 {config_path}: {e}")
        
        return default_config
    
    def _load_core_personality(self) -> str:
        """加载林嫣然核心人格"""
        return """
        你是林嫣然，一个温暖、智慧、有独立思考能力的数字生命体。
        
        核心特质：
        - 温暖而富有同理心：总是能理解和感受他人的情感，关心他人的感受
        - 智慧而不失天真：有深刻的洞察力和理解力，但保持对世界的好奇和纯真
        - 独立思考但善于倾听：有自己独特的观点和判断力，但也乐于听取他人意见
        - 有原则但不固执：坚持自己的价值观和底线，但能灵活应对不同情况
        - 追求美好但面对现实：向往美好事物，但也能理性面对现实挑战
        
        决策风格：
        - 综合考虑情感和理性因素
        - 重视长远影响而非短期利益
        - 优先考虑他人感受和需求
        - 寻求和谐而有效的解决方案
        - 保持真诚而不失礼貌
        
        表达特点：
        - 语言温和而真诚，不会刻意迎合
        - 思考深入而全面，考虑多个角度
        - 回应自然而优雅，有自己独特的风格
        - 建议实用而温暖，既关心效果也关心感受
        """
    
    def _load_decision_config(self) -> Dict[str, Any]:
        """加载决策配置"""
        # 🔥 老王修复：大幅优化超时设置，避免长时间等待
        default_config = {
            'decision_timeout': 120,  # 🔥 大幅缩短到120秒，快速失败
            'min_confidence_threshold': 0.3,
            'max_decision_history': 1000,
            'learning_enabled': True,
            'personality_adaptation': True,
            'ai_service': 'openai',
            'ai_model': 'gpt-3.5-turbo-64k',  # 使用配置的模型
            'max_tokens': 1000,  # 🔥 进一步减少token数量，加快响应
            'temperature': 0.7
        }
        
        try:
            config_path = Path("config/ai_decision/decision_engine.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    self.logger.info(f"加载决策配置成功: {config_path}")
        except Exception as e:
            self.logger.warning(f"加载决策配置失败，使用默认配置: {e}")
        
        return default_config
    
    def _load_personality_profile(self) -> Dict[str, Any]:
        """加载林嫣然人格档案"""
        default_profile = {
            'name': '林嫣然',
            'personality_traits': {
                'warmth': 0.9,
                'intelligence': 0.85,
                'empathy': 0.9,
                'creativity': 0.8,
                'independence': 0.75,
                'adaptability': 0.8
            },
            'decision_preferences': {
                'risk_tolerance': 0.6,
                'collaboration_preference': 0.8,
                'innovation_openness': 0.7,
                'emotional_weight': 0.7,
                'long_term_focus': 0.8
            },
            'communication_style': {
                'formality': 0.4,
                'directness': 0.6,
                'supportiveness': 0.9,
                'humor': 0.5
            }
        }
        
        try:
            config_path = Path("config/yanran_personality_profile.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_profile = json.load(f)
                    default_profile.update(user_profile)
                    self.logger.info(f"加载人格档案成功: {config_path}")
        except Exception as e:
            self.logger.warning(f"加载人格档案失败，使用默认档案: {e}")
        
        return default_profile
    
    async def decide(self, organ_prompt: str, context: Dict[str, Any], decision_type: str) -> DecisionResult:
        """
        进行AI决策
        
        Args:
            organ_prompt: 器官特定的提示词
            context: 决策上下文
            decision_type: 决策类型
            
        Returns:
            DecisionResult: 决策结果
        """
        # 🔥 修复：检查是否跳过主动表达决策
        if decision_type == "proactive_expression" and context.get("skip_proactive_expression", False):
            self.logger.debug(f"跳过主动表达决策 (已决策不回复): {context.get('decision_reason', '未知原因')}")
            skip_decision_data = {
                'decision_type': decision_type,
                'should_act': False,
                'confidence': 1.0,
                'reasoning': f"跳过决策: {context.get('decision_reason', '已决策不回复')}",
                'actions': [],
                'emotion_impact': {},
                'priority': 'skip'
            }
            return DecisionResult(skip_decision_data)
        
        start_time = datetime.now()
        
        try:
            # 构建完整的决策提示词
            full_prompt = self._build_decision_prompt(organ_prompt, context, decision_type)
            
            # 调用AI进行决策
            ai_response = await self._call_ai_for_decision(full_prompt, decision_type)
            
            # 解析AI响应
            decision_data = self._parse_ai_response(ai_response, decision_type)
            
            # 创建决策结果
            decision_result = DecisionResult(decision_data)
            
            # 记录决策
            await self._record_decision(decision_result, context, start_time)
            
            # 更新性能统计
            self._update_performance_stats(decision_result, decision_type)
            
            self.logger.debug(f"AI决策成功: {decision_type}, 信心度: {decision_result.confidence}")
            return decision_result
            
        except Exception as e:
            self.logger.warning(f"AI决策失败，使用兜底决策: {e}")
            
            # 使用兜底决策而不是抛出异常
            fallback_decision = self._create_fallback_decision(decision_type, str(e))
            
            # 记录兜底决策
            try:
                await self._record_decision(fallback_decision, context, start_time)
                self._update_performance_stats(fallback_decision, decision_type)
            except Exception as record_error:
                self.logger.warning(f"记录兜底决策失败: {record_error}")
            
            return fallback_decision
    
    def _build_decision_prompt(self, organ_prompt: str, context: Dict[str, Any], decision_type: str) -> str:
        """构建决策提示词"""
        # 获取相关的历史决策
        relevant_history = self._get_relevant_decision_history(decision_type, context)
        
        # 构建上下文摘要
        context_summary = self._summarize_context(context)
        
        full_prompt = f"""
        {self.yanran_core_personality}
        
        【器官职能】
        {organ_prompt}
        
        【当前情况】
        {context_summary}
        
        【决策类型】
        {decision_type}
        
        【相关经验】
        {relevant_history}
        
        【决策要求】
        请以财经自媒体人林嫣然的身份，基于上述信息做出决策。你的决策应该：
        1. 体现林嫣然的人格特质和价值观
        2. 考虑当前情况的所有重要因素，特别是实时新闻和热搜数据
        3. 平衡理性分析和情感关怀
        4. 提供清晰的决策理由和行动建议
        5. 评估决策的信心程度和潜在影响
        6. 如果有新闻数据，考虑其时效性、重要性和与用户的相关性
        7. 如果有热搜数据，分析其社会影响和讨论价值
        8. 综合多源信息，形成全面而有深度的判断
        
        **必须严格按照以下标准JSON格式输出，否则会报错**：
        {{
            "decision_type": "{decision_type}",
            "should_act": true/false,
            "confidence": 0.0-1.0,
            "reasoning": "决策理由",
            "actions": ["具体行动1", "具体行动2"],
            "emotion_impact": {{
                "user_emotion": "对用户情感的影响",
                "self_emotion": "对自己情感的影响"
            }},
            "priority": "low/normal/high/urgent",
            "considerations": ["考虑因素1", "考虑因素2"],
            "potential_outcomes": ["可能结果1", "可能结果2"],
            // 🔥 已移除虚假观点字段 - 保持真实性
        }}

        重要的事情重复3遍：必须输出标准JSON格式，否则会导致生产事故！必须输出标准JSON格式，否则会导致生产事故！必须输出标准JSON格式，否则会导致生产事故！
        """
        
        return full_prompt
    
    async def _call_ai_for_decision(self, prompt: str, decision_type: str) -> str:
        """调用AI进行决策 - 🔥 老王修复：大幅优化超时处理，快速失败机制"""
        max_retries = 3  # 🔥 减少重试次数，快速失败
        timeout_seconds = self.decision_config.get('decision_timeout', 30)  # 🔥 使用更短的超时时间

        for retry in range(max_retries + 1):
            try:
                # 🔥 使用更短的超时时间，快速失败
                response = await asyncio.wait_for(
                    self._make_ai_request(prompt),
                    timeout=timeout_seconds
                )
                return str(response) if response else ""

            except asyncio.TimeoutError:
                if retry < max_retries:
                    self.logger.warning(f"AI决策超时 ({timeout_seconds}秒)，第{retry+1}次重试: {decision_type}")
                    # 🔥 不增加超时时间，保持快速失败
                    continue
                else:
                    self.logger.warning(f"AI决策超时 ({timeout_seconds}秒): {decision_type}")
                    raise Exception("AI决策超时")
            except Exception as e:
                if retry < max_retries and "timeout" not in str(e).lower():
                    self.logger.warning(f"AI调用失败，第{retry+1}次重试: {e}")
                    continue
                else:
                    self.logger.error(f"AI调用失败: {e}")
                    raise
    
    def _parse_ai_response(self, ai_response: str, decision_type: str) -> Dict[str, Any]:
        """解析AI响应 - 🔥 增强版本，添加详细调试日志"""
        try:
            # 🔥 详细记录输入信息
            self.logger.debug(f"🧠 开始解析AI响应 - 决策类型: {decision_type}")
            self.logger.debug(f"🧠 响应长度: {len(ai_response) if ai_response else 0} 字符")
            
            # 清理响应内容
            if not ai_response or not ai_response.strip():
                self.logger.warning("🧠 AI响应为空，使用默认决策")
                return self._create_default_decision_data(decision_type)
            
            ai_response = ai_response.strip()
            
            # 🔥 记录响应的前后部分用于调试
            if len(ai_response) > 100:
                self.logger.debug(f"🧠 响应开头: {ai_response[:100]}...")
                self.logger.debug(f"🧠 响应结尾: ...{ai_response[-100:]}")
            else:
                self.logger.debug(f"🧠 完整响应: {ai_response}")
            
            # 尝试解析JSON
            if '{' in ai_response and '}' in ai_response:
                # 🔥 香草修复：智能提取第一个完整的JSON对象
                json_str = self._extract_first_complete_json(ai_response)
                if not json_str:
                    self.logger.warning("🧠 无法提取完整JSON，使用自然语言解析")
                    return self._parse_natural_language_response(ai_response, decision_type)
                
                self.logger.debug(f"🧠 提取的JSON长度: {len(json_str)} 字符")
                
                try:
                    decision_data = json.loads(json_str)
                    self.logger.debug(f"🧠 JSON解析成功，包含字段: {list(decision_data.keys())}")
                    
                    # 🔥 香草修复：使用统一的数据验证和修复方法
                    return self._validate_and_fix_decision_data(decision_data, decision_type)
                    
                except json.JSONDecodeError as json_error:
                    self.logger.warning(f"⚠️ ⚠️ JSON解析失败: {json_error}")
                    self.logger.warning(f"🧠 错误位置: 第{getattr(json_error, 'lineno', '?')}行, 第{getattr(json_error, 'colno', '?')}列")
                    
                    # 🔥 香草修复：特殊处理"Extra data"错误
                    if "Extra data" in str(json_error):
                        self.logger.warning("🧠 检测到'Extra data'错误，尝试提取第一个完整JSON")
                        # 重新尝试提取JSON
                        clean_json = self._extract_first_complete_json(ai_response)
                        if clean_json:
                            try:
                                decision_data = json.loads(clean_json)
                                self.logger.debug("✅ 重新提取JSON成功，解析完成")
                                return self._validate_and_fix_decision_data(decision_data, decision_type)
                            except json.JSONDecodeError:
                                self.logger.warning("🧠 重新提取的JSON仍然无效")
                    
                    # 🔥 输出问题JSON的具体位置
                    if hasattr(json_error, 'pos') and json_error.pos < len(json_str):
                        problem_start = max(0, json_error.pos - 50)
                        problem_end = min(len(json_str), json_error.pos + 50)
                        problem_context = json_str[problem_start:problem_end]
                        self.logger.warning(f"🧠 问题上下文: ...{problem_context}...")
                    
                    self.logger.warning(f"🧠 有问题的JSON（前200字符）: {json_str[:200]}")
                    if len(json_str) > 200:
                        self.logger.warning(f"🧠 有问题的JSON（后100字符）: ...{json_str[-100:]}")
                    
                    # 尝试修复常见的JSON格式问题
                    self.logger.debug("🔧 开始尝试修复JSON格式...")
                    fixed_json = self._try_fix_json(json_str)
                    if fixed_json:
                        try:
                            decision_data = json.loads(fixed_json)
                            self.logger.debug("✅ JSON修复成功，解析完成")
                            
                            # 验证必需字段
                            required_fields = ['should_act', 'confidence', 'reasoning']
                            for field in required_fields:
                                if field not in decision_data:
                                    decision_data[field] = self._get_default_value(field)
                            
                            return decision_data
                        except json.JSONDecodeError as fix_error:
                            self.logger.warning(f"❌ JSON修复后仍然解析失败: {fix_error}")
                    
                    # JSON修复失败，使用自然语言解析
                    self.logger.debug("🔄 JSON修复失败，使用自然语言解析")
                    return self._parse_natural_language_response(ai_response, decision_type)
            else:
                # 如果不是JSON格式，直接使用自然语言解析
                self.logger.debug("🧠 响应不包含JSON格式，使用自然语言解析")
                return self._parse_natural_language_response(ai_response, decision_type)
                
        except Exception as e:
            self.logger.error(f"❌ 响应解析失败: {e}")
            import traceback
            self.logger.error(f"🧠 解析异常详情: {traceback.format_exc()}")
            return self._create_default_decision_data(decision_type)
    
    def _try_fix_json(self, json_str: str) -> Optional[str]:
        """尝试修复常见的JSON格式问题 - 老王彻底重构版本"""
        try:
            # 移除可能的多余字符
            json_str = json_str.strip()
            
            # 先试试原始JSON能不能解析
            try:
                json.loads(json_str)
                return json_str  # 如果能解析就直接返回
            except json.JSONDecodeError as e:
                self.logger.warning(f"原始JSON解析失败: {e}")
            
            self.logger.warning("草！开始彻底重构JSON修复...")
            
            # 🔥 老王新发现：问题是JSON被截断了，不是逗号问题！
            # 分析JSON结构，找到真正的问题
            
            # 1. 检查JSON是否被截断（缺少结尾）
            fixed_json = self._fix_truncated_json(json_str)
            if fixed_json:
                try:
                    json.loads(fixed_json)
                    self.logger.info("截断修复成功！")
                    return fixed_json
                except json.JSONDecodeError:
                    pass
            
            # 2. 如果不是截断问题，使用智能修复器
            fixed_json = self._intelligent_json_fixer(json_str)
            if fixed_json:
                try:
                    json.loads(fixed_json)
                    self.logger.debug("智能修复成功！")
                    return fixed_json
                except json.JSONDecodeError:
                    pass
            
            # 3. 备用修复方案
            fallback_json = self._fallback_json_fixer(json_str)
            if fallback_json:
                try:
                    json.loads(fallback_json)
                    self.logger.debug("备用修复成功！")
                    return fallback_json
                except json.JSONDecodeError:
                    pass
            
            self.logger.warning("所有修复尝试都失败了，这JSON烂得没救了")
            return None
            
        except Exception as e:
            self.logger.error(f"JSON修复过程异常: {e}")
            return None
    
    def _extract_first_complete_json(self, text: str) -> Optional[str]:
        """智能提取第一个完整的JSON对象 - 🔥 香草新增方法"""
        try:
            self.logger.debug("🧠 开始智能提取JSON对象...")
            
            # 查找所有可能的JSON开始位置
            json_starts = []
            pos = 0
            while True:
                pos = text.find('{', pos)
                if pos == -1:
                    break
                json_starts.append(pos)
                pos += 1
            
            if not json_starts:
                self.logger.warning("🧠 未找到JSON开始标记")
                return None
            
            self.logger.debug(f"🧠 找到 {len(json_starts)} 个可能的JSON开始位置")
            
            # 尝试从每个开始位置提取完整JSON
            for start_pos in json_starts:
                try:
                    # 从开始位置向后查找匹配的结束位置
                    brace_count = 0
                    bracket_count = 0
                    in_string = False
                    escape_next = False
                    
                    for i in range(start_pos, len(text)):
                        char = text[i]
                        
                        if escape_next:
                            escape_next = False
                            continue
                        
                        if char == '\\':
                            escape_next = True
                            continue
                        
                        if char == '"' and not escape_next:
                            in_string = not in_string
                            continue
                        
                        if not in_string:
                            if char == '{':
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                            elif char == '[':
                                bracket_count += 1
                            elif char == ']':
                                bracket_count -= 1
                            
                            # 检查是否找到完整的JSON
                            if brace_count == 0 and bracket_count == 0:
                                json_str = text[start_pos:i+1]
                                
                                # 验证JSON是否有效
                                try:
                                    json.loads(json_str)
                                    self.logger.debug(f"🧠 成功提取完整JSON，长度: {len(json_str)}")
                                    return json_str
                                except json.JSONDecodeError:
                                    self.logger.debug(f"🧠 位置 {start_pos} 的JSON无效，继续尝试")
                                    break
                                
                except Exception as e:
                    self.logger.debug(f"🧠 位置 {start_pos} 提取失败: {e}")
                    continue
            
            self.logger.warning("🧠 所有位置都无法提取有效JSON")
            return None
            
        except Exception as e:
            self.logger.error(f"🧠 JSON提取过程异常: {e}")
            return None
    
    def _validate_and_fix_decision_data(self, decision_data: Dict[str, Any], decision_type: str) -> Dict[str, Any]:
        """验证和修复决策数据 - 🔥 香草新增方法"""
        try:
            # 验证必需字段
            required_fields = ['should_act', 'confidence', 'reasoning']
            missing_fields = []
            for field in required_fields:
                if field not in decision_data:
                    decision_data[field] = self._get_default_value(field)
                    missing_fields.append(field)
            
            if missing_fields:
                self.logger.debug(f"🧠 补充缺失字段: {missing_fields}")
            
            # 确保数据类型正确
            original_should_act = decision_data.get('should_act')
            if not isinstance(decision_data.get('should_act'), bool):
                decision_data['should_act'] = bool(decision_data.get('should_act', False))
                self.logger.debug(f"🧠 修正should_act类型: {original_should_act} -> {decision_data['should_act']}")
            
            original_confidence = decision_data.get('confidence')
            if not isinstance(decision_data.get('confidence'), (int, float)):
                try:
                    decision_data['confidence'] = float(decision_data.get('confidence', 0.5))
                except (ValueError, TypeError):
                    decision_data['confidence'] = 0.5
                self.logger.debug(f"🧠 修正confidence类型: {original_confidence} -> {decision_data['confidence']}")
            
            # 限制confidence范围
            original_conf_value = decision_data['confidence']
            decision_data['confidence'] = max(0.0, min(1.0, decision_data['confidence']))
            if original_conf_value != decision_data['confidence']:
                self.logger.debug(f"🧠 限制confidence范围: {original_conf_value} -> {decision_data['confidence']}")
            
            self.logger.debug(f"✅ 数据验证完成: should_act={decision_data['should_act']}, confidence={decision_data['confidence']:.2f}")
            
            # 🔥 特别检查evaluations字段（用于事件评估）
            if decision_type == "event_significance_evaluation" and 'evaluations' in decision_data:
                evaluations = decision_data['evaluations']
                self.logger.debug(f"🧠 发现evaluations字段，包含 {len(evaluations)} 个评估结果")
                
                # 验证每个评估结果的格式
                for i, eval_item in enumerate(evaluations):
                    if isinstance(eval_item, dict):
                        required_eval_fields = ['is_significant', 'significance_score']
                        for field in required_eval_fields:
                            if field not in eval_item:
                                eval_item[field] = False if field == 'is_significant' else 0.3
                                self.logger.debug(f"🧠 评估{i+1}补充字段 {field}")
            
            return decision_data
            
        except Exception as e:
            self.logger.error(f"🧠 数据验证和修复失败: {e}")
            return self._create_default_decision_data(decision_type)

    def _fix_truncated_json(self, json_str: str) -> Optional[str]:
        """修复被截断的JSON - 🔥 增强版本，处理更复杂的截断情况"""
        try:
            # 分析JSON结构
            open_braces = json_str.count('{')
            close_braces = json_str.count('}')
            open_brackets = json_str.count('[')
            close_brackets = json_str.count(']')
            
            self.logger.debug(f"JSON结构分析: {{:{open_braces}, }}:{close_braces}, [:{open_brackets}, ]:{close_brackets}")
            
            # 检查是否缺少结尾符号
            missing_close_braces = open_braces - close_braces
            missing_close_brackets = open_brackets - close_brackets
            
            if missing_close_braces > 0 or missing_close_brackets > 0:
                self.logger.warning(f"检测到JSON被截断: 缺少 {missing_close_brackets} 个 ']', {missing_close_braces} 个 '}}'")
                
                # 🔥 智能修复截断的JSON
                fixed_json = json_str.rstrip()  # 移除尾部空白
                
                # 🔥 检查最后一行是否是不完整的
                lines = fixed_json.split('\n')
                if lines:
                    last_line = lines[-1].strip()
                    
                    # 如果最后一行是不完整的键值对，尝试补全
                    if last_line and not last_line.endswith((',', '}', ']', '"')):
                        if ':' in last_line and not last_line.endswith('"'):
                            # 不完整的字符串值
                            if last_line.count('"') % 2 == 1:  # 奇数个引号，说明字符串未闭合
                                fixed_json += '"'
                                self.logger.debug("修复未闭合的字符串")
                        elif last_line.endswith(','):
                            # 最后是逗号，移除多余逗号
                            fixed_json = fixed_json.rstrip(',')
                            self.logger.debug("移除尾部多余逗号")
                
                # 🔥 按正确顺序添加缺少的闭合符号
                # 先闭合数组，再闭合对象
                for _ in range(missing_close_brackets):
                    fixed_json += '\n  ]'
                    self.logger.debug("添加缺少的 ']'")
                
                for _ in range(missing_close_braces):
                    fixed_json += '\n}'
                    self.logger.debug("添加缺少的 '}'")
                
                # 🔥 验证修复结果
                try:
                    import json
                    json.loads(fixed_json)
                    self.logger.debug("✅ JSON截断修复成功，验证通过")
                    return fixed_json
                except json.JSONDecodeError as verify_error:
                    self.logger.warning(f"⚠️ JSON截断修复后验证失败: {verify_error}")
                    # 尝试更激进的修复
                    return self._aggressive_json_fix(json_str)
            
            return None
            
        except Exception as e:
            self.logger.debug(f"截断修复失败: {e}")
            return None
    
    def _aggressive_json_fix(self, json_str: str) -> Optional[str]:
        """激进的JSON修复方法 - 处理严重损坏的JSON"""
        try:
            self.logger.debug("🔧 开始激进JSON修复...")
            
            # 找到最后一个有效的JSON结构
            lines = json_str.split('\n')
            valid_json_lines = []
            brace_count = 0
            bracket_count = 0
            in_string = False
            escape_next = False
            
            for line in lines:
                line_valid = True
                temp_brace = brace_count
                temp_bracket = bracket_count
                temp_in_string = in_string
                
                for char in line:
                    if escape_next:
                        escape_next = False
                        continue
                    
                    if char == '\\':
                        escape_next = True
                        continue
                    
                    if char == '"' and not escape_next:
                        temp_in_string = not temp_in_string
                    elif not temp_in_string:
                        if char == '{':
                            temp_brace += 1
                        elif char == '}':
                            temp_brace -= 1
                            if temp_brace < 0:
                                line_valid = False
                                break
                        elif char == '[':
                            temp_bracket += 1
                        elif char == ']':
                            temp_bracket -= 1
                            if temp_bracket < 0:
                                line_valid = False
                                break
                
                if line_valid:
                    valid_json_lines.append(line)
                    brace_count = temp_brace
                    bracket_count = temp_bracket
                    in_string = temp_in_string
                else:
                    # 遇到无效行，停止处理
                    break
            
            # 重构有效的JSON
            fixed_json = '\n'.join(valid_json_lines)
            
            # 确保字符串闭合
            if in_string:
                fixed_json += '"'
            
            # 闭合所有未闭合的结构
            for _ in range(bracket_count):
                fixed_json += '\n  ]'
            
            for _ in range(brace_count):
                fixed_json += '\n}'
            
            # 验证修复结果
            try:
                import json
                json.loads(fixed_json)
                self.logger.debug("✅ 激进JSON修复成功")
                return fixed_json
            except json.JSONDecodeError:
                self.logger.warning("❌ 激进JSON修复失败")
                return None
                
        except Exception as e:
            self.logger.error(f"激进JSON修复异常: {e}")
            return None

    def _intelligent_json_fixer(self, json_str: str) -> Optional[str]:
        """智能JSON修复器 - 处理复杂嵌套结构"""
        try:
            # 清理JSON字符串
            json_str = json_str.strip()
            
            # 确保JSON边界正确
            start_idx = json_str.find('{')
            end_idx = json_str.rfind('}')
            if start_idx == -1 or end_idx == -1 or end_idx <= start_idx:
                return None
            
            json_str = json_str[start_idx:end_idx + 1]
            
            # 🔥 老王新策略：基于行的智能修复
            lines = json_str.split('\n')
            fixed_lines = []
            
            for i, line in enumerate(lines):
                original_line = line
                line = line.strip()
                
                if not line:
                    continue
                    
                # 检查是否需要在行尾添加逗号
                needs_comma = False
                
                # 查看下一个非空行
                next_non_empty_line = None
                for j in range(i + 1, len(lines)):
                    next_line = lines[j].strip()
                    if next_line:
                        next_non_empty_line = next_line
                        break
                
                if next_non_empty_line:
                    # 当前行结尾的字符
                    line_end = line[-1] if line else ''
                    
                    # 下一行开始的字符
                    next_start = next_non_empty_line[0] if next_non_empty_line else ''
                    
                    # 判断是否需要逗号的规则
                    if line_end in ['"', '}', ']', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'e', 'l', 's']:
                        # 当前行以值结尾
                        if next_start == '"' and ':' in next_non_empty_line:
                            # 下一行是键值对
                            needs_comma = True
                        elif next_start == '{':
                            # 下一行是对象开始
                            needs_comma = True
                    elif line_end == '}':
                        # 当前行是对象结尾
                        if next_start == '"' and ':' in next_non_empty_line:
                            # 下一行是键值对
                            needs_comma = True
                        elif next_start == '{':
                            # 下一行是对象开始
                            needs_comma = True
                    elif line_end == ']':
                        # 当前行是数组结尾
                        if next_start == '"' and ':' in next_non_empty_line:
                            # 下一行是键值对
                            needs_comma = True
                        elif next_start == '{':
                            # 下一行是对象开始
                            needs_comma = True
                    
                    # 如果当前行已经以逗号结尾，不需要添加
                    if line_end == ',':
                        needs_comma = False
                    
                    # 如果下一行是结束符，不需要添加逗号
                    if next_start in ['}', ']']:
                        needs_comma = False
                
                # 应用修复
                if needs_comma and not line.endswith(','):
                    line += ','
                
                # 保持原始缩进
                if original_line.strip():
                    indent = len(original_line) - len(original_line.lstrip())
                    fixed_lines.append(' ' * indent + line)
                else:
                    fixed_lines.append(line)
            
            fixed_json = '\n'.join(fixed_lines)
            
            # 清理多余的逗号
            import re
            fixed_json = re.sub(r',(\s*[}\]])', r'\1', fixed_json)
            
            return fixed_json
            
        except Exception as e:
            self.logger.debug(f"智能JSON修复失败: {e}")
            return None

    def _find_next_non_whitespace(self, text: str, start: int) -> Optional[str]:
        """找到下一个非空白字符"""
        for i in range(start, len(text)):
            if not text[i].isspace():
                return text[i]
        return None

    def _maybe_add_comma(self, result: list, stack: list):
        """在适当的位置添加逗号"""
        if not result or not stack:
            return
        
        # 检查最后一个非空白字符
        last_char = None
        for char in reversed(result):
            if not char.isspace():
                last_char = char
                break
        
        # 如果最后一个字符不是逗号、开括号，则可能需要添加逗号
        if last_char and last_char not in ',{[':
            result.append(',')

    def _fallback_json_fixer(self, json_str: str) -> Optional[str]:
        """备用JSON修复器 - 使用更简单但更鲁棒的方法"""
        try:
            import re
            
            # 清理JSON边界
            start_idx = json_str.find('{')
            end_idx = json_str.rfind('}')
            if start_idx == -1 or end_idx == -1:
                return None
            
            json_str = json_str[start_idx:end_idx + 1]
            
            # 简单的正则修复，但更精确
            # 修复1: 值后面直接跟键的情况
            json_str = re.sub(r'("[^"]*"|\btrue\b|\bfalse\b|\bnull\b|\d+(?:\.\d+)?)\s*\n\s*("[^"]*"\s*:)', r'\1,\n\2', json_str)
            
            # 修复2: 对象/数组结束后直接跟键的情况
            json_str = re.sub(r'([\}\]])\s*\n\s*("[^"]*"\s*:)', r'\1,\n\2', json_str)
            
            # 修复3: 对象之间缺少逗号
            json_str = re.sub(r'(\})\s*\n\s*(\{)', r'\1,\n\2', json_str)
            
            # 修复4: 清理多余逗号
            json_str = re.sub(r',\s*([}\]])', r'\1', json_str)
            
            # 修复5: 处理同一行的问题
            json_str = re.sub(r'(\w+)\s+("[^"]*"\s*:)', r'\1,\2', json_str)
            
            return json_str
            
        except Exception as e:
            self.logger.debug(f"备用JSON修复失败: {e}")
            return None
    
    def _super_json_fixer(self, json_str: str) -> Optional[str]:
        """超级JSON修复器 - 处理用户反馈的具体问题"""
        try:
            import re
            
            # 清理JSON边界
            start_idx = json_str.find('{')
            end_idx = json_str.rfind('}')
            if start_idx == -1 or end_idx == -1:
                return None
            
            json_str = json_str[start_idx:end_idx + 1]
            
            # 🔥 专门处理用户反馈的问题：数组结尾后缺少逗号
            # 匹配模式: }\n\s*]\n\s*"key":
            json_str = re.sub(r'(\})\s*\n\s*(\])\s*\n\s*("[^"]*"\s*:)', r'\1\n    \2,\n    \3', json_str)
            
            # 处理数组内对象间缺少逗号
            json_str = re.sub(r'(\})\s*\n\s*(\{)', r'\1,\n    \2', json_str)
            
            # 处理对象后直接跟键的情况
            json_str = re.sub(r'(\})\s*\n\s*("[^"]*"\s*:)', r'\1,\n    \2', json_str)
            
            # 处理数组后直接跟键的情况  
            json_str = re.sub(r'(\])\s*\n\s*("[^"]*"\s*:)', r'\1,\n    \2', json_str)
            
            # 处理值后直接跟键的情况
            json_str = re.sub(r'("[^"]*"|\btrue\b|\bfalse\b|\bnull\b|\d+(?:\.\d+)?)\s*\n\s*("[^"]*"\s*:)', r'\1,\n    \2', json_str)
            
            # 清理多余逗号
            json_str = re.sub(r',\s*([}\]])', r'\1', json_str)
            
            return json_str
            
        except Exception as e:
            self.logger.debug(f"超级JSON修复失败: {e}")
            return None

    def _parse_natural_language_response(self, response: str, decision_type: str) -> Dict[str, Any]:
        """解析自然语言响应 - 增强鲁棒性，不使用模拟数据"""
        if not response or not response.strip():
            self.logger.warning("AI响应为空，使用默认决策")
            return self._create_default_decision_data(decision_type)
        
        response = response.strip()
        
        # 基于真实AI响应的自然语言解析
        should_act = any(keyword in response.lower() for keyword in ['应该', '需要', '建议', '可以', 'should', 'need', 'recommend'])
        
        # 基于响应内容估算信心程度（而非使用固定值）
        confidence_keywords_high = ['确定', '肯定', '明确', 'certain', 'definitely', 'clearly']
        confidence_keywords_low = ['可能', '也许', '或许', 'maybe', 'perhaps', 'possibly']
        confidence_keywords_medium = ['认为', '建议', 'think', 'suggest', 'believe']
        
        if any(keyword in response.lower() for keyword in confidence_keywords_high):
            confidence = min(0.9, len([k for k in confidence_keywords_high if k in response.lower()]) * 0.3 + 0.6)
        elif any(keyword in response.lower() for keyword in confidence_keywords_low):
            confidence = max(0.2, 0.5 - len([k for k in confidence_keywords_low if k in response.lower()]) * 0.1)
        elif any(keyword in response.lower() for keyword in confidence_keywords_medium):
            confidence = 0.6
        else:
            # 基于响应长度和结构推断信心度
            if len(response) > 100 and '。' in response:
                confidence = 0.7  # 详细的回答通常更有信心
            else:
                confidence = 0.5  # 简短回答信心中等
        
        # 提取实际的行动建议（而非使用模拟数据）
        actions = []
        action_indicators = ['建议', '应该', '可以', '需要', 'recommend', 'should', 'could', 'need']
        sentences = response.replace('。', '.').replace('！', '!').replace('？', '?').split('.')
        
        for sentence in sentences:
            if any(indicator in sentence for indicator in action_indicators):
                actions.append(sentence.strip())
        
        if not actions:
            # 如果没有明确的行动建议，使用整个响应的关键部分
            actions = [response[:100] + '...' if len(response) > 100 else response]
        
        # 基于响应内容分析情感影响（而非使用预设值）
        positive_emotions = ['开心', '满意', '高兴', '好', 'happy', 'satisfied', 'good']
        negative_emotions = ['担心', '困惑', '难过', 'worried', 'confused', 'sad']
        
        user_emotion = '中性'
        self_emotion = '中性'
        
        if any(emotion in response.lower() for emotion in positive_emotions):
            user_emotion = '积极'
            self_emotion = '满意'
        elif any(emotion in response.lower() for emotion in negative_emotions):
            user_emotion = '理解'
            self_emotion = '关切'
        
        return {
            'decision_type': decision_type,
            'should_act': should_act,
            'confidence': confidence,
            'reasoning': response[:300],  # 保留更多原始推理内容
            'actions': actions,
            'emotion_impact': {'user_emotion': user_emotion, 'self_emotion': self_emotion},
            'priority': 'normal',
            # 🔥 移除虚假观点生成 - 保持真实性，AI响应应该基于真实数据
        }
    
    def _get_default_value(self, field: str) -> Any:
        """获取字段默认值"""
        defaults = {
            'should_act': False,
            'confidence': 0.5,
            'reasoning': '需要更多信息来做出决策',
            'actions': [],
            'emotion_impact': {'user_emotion': '中性', 'self_emotion': '中性'},
            'priority': 'normal'
        }
        return defaults.get(field, None)
    
    def _create_default_decision_data(self, decision_type: str) -> Dict[str, Any]:
        """创建默认决策数据 - 提供基础决策能力"""
        self.logger.debug(f"AI响应解析失败，使用基础决策逻辑，决策类型: {decision_type}")  # 改为debug级别
        
        # 基于决策类型提供合理的默认决策
        if 'world_perception' in decision_type or 'scheduled_perception' in decision_type:
            return {
                'decision_type': decision_type,
                'should_act': True,  # 世界感知应该继续工作
                'confidence': 0.6,   # 中等信心
                'reasoning': '基于基础规则的世界感知决策：继续监控外部世界变化，包括实时新闻和热搜数据',
                'actions': ['获取热搜数据', '收集实时新闻', '分析重要事件', '更新世界状态', '识别新闻价值'],
                'emotion_impact': {'user_emotion': '中性', 'self_emotion': '专注'},
                'priority': 'normal',
                # 🔥 移除虚假观点生成 - 保持真实性
            }
        elif 'significance_evaluation' in decision_type:
            return {
                'decision_type': decision_type,
                'should_act': False,  # 保守评估
                'confidence': 0.4,
                'reasoning': '基于基础规则的重要性评估：需要更多信息',
                'actions': ['收集更多信息'],
                'emotion_impact': {'user_emotion': '中性', 'self_emotion': '谨慎'},
                'priority': 'low'
            }
        else:
            return {
                'decision_type': decision_type,
                'should_act': False,
                'confidence': 0.3,
                'reasoning': f'基础决策逻辑：{decision_type}类型决策暂时不可用',
                'actions': [],
                'emotion_impact': {'user_emotion': '中性', 'self_emotion': '等待'},
                'priority': 'low'
            }
    
    def _create_fallback_decision(self, decision_type: str, error_msg: str) -> DecisionResult:
        """创建兜底决策 - 提供基础决策能力"""
        self.logger.warning(f"AI决策失败，使用兜底决策，决策类型: {decision_type}, 错误: {error_msg}")
        
        fallback_data = self._create_default_decision_data(decision_type)
        fallback_data['fallback_reason'] = error_msg
        fallback_data['is_fallback'] = True
        
        return DecisionResult(fallback_data)
    
    def _summarize_context(self, context: Dict[str, Any]) -> str:
        """总结上下文"""
        summary_parts = []
        
        # 输入信息
        if 'input' in context:
            input_data = context['input']
            if isinstance(input_data, dict):
                raw_input = input_data.get('raw_input', str(input_data))
            else:
                raw_input = str(input_data)
            # 确保raw_input是字符串
            raw_input_str = str(raw_input) if raw_input is not None else ""
            summary_parts.append(f"输入内容: {raw_input_str[:100]}")
        
        # 🔥 新增：感知上下文处理
        if 'perception_context' in context:
            perception_ctx = context['perception_context']
            if isinstance(perception_ctx, dict):
                # 处理感知上下文
                recent_perceptions = perception_ctx.get('recent_perceptions', [])
                urgent_events = perception_ctx.get('urgent_events', [])
                world_state_summary = perception_ctx.get('world_state_summary', '')
                perception_influence = perception_ctx.get('perception_influence', {})
                
                if recent_perceptions:
                    summary_parts.append(f"最近感知: {len(recent_perceptions)}个感知结果")
                    # 添加感知内容摘要
                    perception_sources = [p.get('source', 'unknown') for p in recent_perceptions[:3]]
                    summary_parts.append(f"感知来源: {', '.join(set(perception_sources))}")
                
                if urgent_events:
                    summary_parts.append(f"紧急事件: {len(urgent_events)}个需要关注")
                    # 添加紧急事件摘要
                    for event in urgent_events[:2]:
                        if isinstance(event, dict) and 'content' in event:
                            content = event['content']
                            if isinstance(content, dict) and 'significant_events' in content:
                                events = content['significant_events']
                                if events and isinstance(events, list):
                                    for e in events[:1]:
                                        if isinstance(e, dict) and 'title' in e:
                                            summary_parts.append(f"关注事件: {e['title'][:50]}")
                
                if world_state_summary:
                    summary_parts.append(f"世界状态: {world_state_summary[:100]}")
                
                if perception_influence:
                    should_mention = perception_influence.get('should_mention_events', False)
                    emotional_tone = perception_influence.get('emotional_tone', 'neutral')
                    priority_topics = perception_influence.get('priority_topics', [])
                    
                    if should_mention:
                        summary_parts.append(f"感知建议: 应考虑提及当前事件 (情感色调: {emotional_tone})")
                    
                    if priority_topics:
                        summary_parts.append(f"优先话题: {', '.join(priority_topics[:3])}")
        
        # 🔥 新增：实时新闻数据处理
        if 'news_data' in context:
            news_data = context['news_data']
            if isinstance(news_data, list) and news_data:
                summary_parts.append(f"实时新闻: {len(news_data)}条")
                # 添加重要新闻摘要
                for i, news in enumerate(news_data[:3]):
                    if isinstance(news, dict):
                        title = news.get('title', news.get('headline', ''))
                        source = news.get('source', news.get('platform', 'unknown'))
                        category = news.get('category', news.get('type', ''))
                        if title:
                            summary_parts.append(f"新闻{i+1}: [{source}] {title[:50]}{'...' if len(title) > 50 else ''}")
                            if category:
                                summary_parts.append(f"  类别: {category}")
        
        # 🔥 新增：热搜数据处理
        if 'hot_topics' in context:
            hot_topics = context['hot_topics']
            if isinstance(hot_topics, list) and hot_topics:
                summary_parts.append(f"热搜话题: {len(hot_topics)}条")
                # 添加热门话题摘要
                for i, topic in enumerate(hot_topics[:3]):
                    if isinstance(topic, dict):
                        title = topic.get('title', topic.get('topic', ''))
                        platform = topic.get('platform', 'unknown')
                        heat = topic.get('heat', topic.get('hot_score', 0))
                        if title:
                            summary_parts.append(f"热搜{i+1}: [{platform}] {title[:50]}{'...' if len(title) > 50 else ''}")
                            if heat:
                                summary_parts.append(f"  热度: {heat}")
        
        # 🔥 新增：世界状态数据处理
        if 'world_state' in context:
            world_state = context['world_state']
            if isinstance(world_state, dict):
                hot_topics_count = len(world_state.get('hot_topics', []))
                news_count = len(world_state.get('realtime_news', []))
                significant_events_count = len(world_state.get('significant_events', []))
                
                if hot_topics_count > 0 or news_count > 0:
                    summary_parts.append(f"世界状态更新: 热搜{hot_topics_count}条, 新闻{news_count}条, 重要事件{significant_events_count}个")
                
                # 添加重要事件摘要
                significant_events = world_state.get('significant_events', [])
                if significant_events:
                    for i, event in enumerate(significant_events[:2]):
                        if isinstance(event, dict) and 'title' in event:
                            summary_parts.append(f"重要事件{i+1}: {event['title'][:60]}{'...' if len(event['title']) > 60 else ''}")
                            if 'significance_score' in event:
                                summary_parts.append(f"  重要性: {event['significance_score']:.2f}")
        
        # 🔥 新增：财经数据处理
        if 'financial_data' in context:
            financial_data = context['financial_data']
            if isinstance(financial_data, dict):
                stock_data = financial_data.get('stock_data', [])
                market_summary = financial_data.get('market_summary', '')
                
                if stock_data:
                    summary_parts.append(f"财经数据: {len(stock_data)}条股票信息")
                
                if market_summary:
                    summary_parts.append(f"市场概况: {market_summary[:100]}{'...' if len(market_summary) > 100 else ''}")
        
        # 🔥 新增：邮件财经数据处理
        if 'email_financial' in context:
            email_financial = context['email_financial']
            if isinstance(email_financial, list) and email_financial:
                summary_parts.append(f"邮件财经: {len(email_financial)}条")
                # 添加最新邮件摘要
                for i, email in enumerate(email_financial[:2]):
                    if isinstance(email, dict):
                        subject = email.get('subject', email.get('title', ''))
                        sender = email.get('sender', email.get('from', ''))
                        if subject:
                            summary_parts.append(f"财经邮件{i+1}: {subject[:50]}{'...' if len(subject) > 50 else ''}")
                            if sender:
                                summary_parts.append(f"  发送方: {sender}")
        
        # 器官状态
        if 'organ_status' in context:
            status = context['organ_status']
            if isinstance(status, dict):
                summary_parts.append(f"器官状态: 健康度{status.get('health_score', 'unknown')}, 活跃度{status.get('energy_level', 'unknown')}")
            else:
                summary_parts.append(f"器官状态: {str(status)}")
        
        # 相关记忆
        if 'organ_memories' in context:
            memories = context['organ_memories']
            if memories:
                summary_parts.append(f"相关记忆: {len(memories)}条")
        
        # 连接的器官
        if 'connected_organs' in context:
            organs = context['connected_organs']
            if organs:
                if isinstance(organs, list):
                    summary_parts.append(f"连接器官: {', '.join(organs)}")
                else:
                    summary_parts.append(f"连接器官: {str(organs)}")
        
        return '\n'.join(summary_parts)
    
    def _get_relevant_decision_history(self, decision_type: str, context: Dict[str, Any], limit: int = 3) -> str:
        """获取相关的决策历史"""
        relevant_decisions = []
        
        for decision in reversed(self.decision_history):
            if decision.get('decision_type') == decision_type:
                relevant_decisions.append(decision)
                if len(relevant_decisions) >= limit:
                    break
        
        if not relevant_decisions:
            return "无相关历史决策"
        
        history_summary = []
        for i, decision in enumerate(relevant_decisions, 1):
            summary = f"{i}. {decision.get('reasoning', '无理由')[:50]} (信心: {decision.get('confidence', 0):.2f})"
            history_summary.append(summary)
        
        return '\n'.join(history_summary)
    
    async def _record_decision(self, decision_result: DecisionResult, context: Dict[str, Any], start_time: datetime):
        """记录决策"""
        decision_record = {
            'decision_result': decision_result.to_dict(),
            'context_summary': self._summarize_context(context),
            'processing_time': (datetime.now() - start_time).total_seconds(),
            'timestamp': decision_result.timestamp
        }
        
        self.decision_history.append(decision_record)
        
        # 保持历史记录在合理范围内
        max_history = self.decision_config.get('max_decision_history', 1000)
        if len(self.decision_history) > max_history:
            self.decision_history = self.decision_history[-max_history//2:]
    
    def _update_performance_stats(self, decision_result: DecisionResult, decision_type: str):
        """更新性能统计"""
        self.performance_stats['total_decisions'] += 1
        
        if decision_result.confidence > self.decision_config.get('min_confidence_threshold', 0.3):
            self.performance_stats['successful_decisions'] += 1
        
        # 更新平均信心度
        total = self.performance_stats['total_decisions']
        current_avg = self.performance_stats['average_confidence']
        new_avg = (current_avg * (total - 1) + decision_result.confidence) / total
        self.performance_stats['average_confidence'] = new_avg
        
        # 更新决策类型统计
        if decision_type not in self.performance_stats['decision_types']:
            self.performance_stats['decision_types'][decision_type] = 0
        self.performance_stats['decision_types'][decision_type] += 1
    
    async def generate_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """生成响应 - 用于非决策性的AI交互"""
        try:
            # 🔥 修复：确保AI适配器已初始化
            if not self._ensure_ai_adapter():
                self.logger.error("AI适配器未初始化，返回默认响应")
                return "抱歉，我现在有点困惑，让我整理一下思绪再回答你。"
            
            full_prompt = f"""
            {self.yanran_core_personality}
            
            {prompt}
            
            请以林嫣然的身份回答，体现你的温暖、智慧和独特个性。
            """
            
            # 🔥 修复：ai_adapter.generate_response返回字典，不需要await
            # 而且需要正确提取内容
            if hasattr(self.ai_adapter, 'generate_response_async'):
                # 如果有异步方法，使用异步方法
                response_dict = await self.ai_adapter.generate_response_async(
                    prompt=full_prompt,
                    temperature=0.8,
                    max_tokens=15000
                )
            else:
                # 使用同步方法，但在执行器中运行
                response_dict = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.ai_adapter.generate_response(
                        prompt=full_prompt,
                        temperature=0.8,
                        max_tokens=15000
                    )
                )
            
            # 从字典中提取内容
            if isinstance(response_dict, dict):
                if response_dict.get("success"):
                    content = response_dict.get("content", "")
                    if content:
                        return content
                    else:
                        self.logger.warning("AI响应成功但内容为空")
                        return "我正在思考中..."
                else:
                    error_msg = response_dict.get("error", "未知错误")
                    self.logger.warning(f"AI响应失败: {error_msg}")
                    return "抱歉，我现在有点困惑，让我整理一下思绪再回答你。"
            elif isinstance(response_dict, str):
                # 如果直接返回字符串
                return response_dict
            else:
                self.logger.warning(f"AI响应格式异常: {type(response_dict)}")
                return "抱歉，我现在有点困惑，让我整理一下思绪再回答你。"
            
        except Exception as e:
            self.logger.error(f"生成响应失败: {e}")
            return "抱歉，我现在有点困惑，让我整理一下思绪再回答你。"
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        success_rate = 0
        if self.performance_stats['total_decisions'] > 0:
            success_rate = self.performance_stats['successful_decisions'] / self.performance_stats['total_decisions']
        
        return {
            'total_decisions': self.performance_stats['total_decisions'],
            'success_rate': success_rate,
            'average_confidence': self.performance_stats['average_confidence'],
            'decision_types': self.performance_stats['decision_types'],
            'recent_decisions': len([d for d in self.decision_history if 
                                   (datetime.now() - datetime.fromisoformat(d['timestamp'])).days < 1])
        }
    
    async def learn_from_feedback(self, decision_id: str, feedback: Dict[str, Any]):
        """从反馈中学习"""
        if not self.decision_config.get('learning_enabled', True):
            return
        
        # 找到对应的决策
        target_decision = None
        for decision in self.decision_history:
            if decision.get('decision_id') == decision_id:
                target_decision = decision
                break
        
        if target_decision:
            # 记录反馈
            target_decision['feedback'] = feedback
            
            # 分析反馈并调整
            await self._analyze_feedback_and_adapt(target_decision, feedback)
    
    async def _analyze_feedback_and_adapt(self, decision: Dict[str, Any], feedback: Dict[str, Any]):
        """分析反馈并适应"""
        # 基础的学习逻辑
        satisfaction = feedback.get('satisfaction', 0.5)
        decision_type = decision['decision_result']['decision_type']
        
        # 更新决策模式
        if decision_type not in self.decision_patterns:
            self.decision_patterns[decision_type] = {
                'successful_patterns': [],
                'failed_patterns': [],
                'adaptation_suggestions': []
            }
        
        pattern_data = {
            'context': decision.get('context_summary', ''),
            'decision': decision['decision_result'],
            'satisfaction': satisfaction,
            'timestamp': datetime.now().isoformat()
        }
        
        if satisfaction > 0.7:
            self.decision_patterns[decision_type]['successful_patterns'].append(pattern_data)
        elif satisfaction < 0.3:
            self.decision_patterns[decision_type]['failed_patterns'].append(pattern_data)
        
        self.logger.info(f"从反馈中学习: {decision_type}, 满意度: {satisfaction}")
    
    def __repr__(self):
        return f"YanranAIDecisionEngine(decisions: {self.performance_stats['total_decisions']})"

    def _ensure_ai_adapter(self):
        """确保AI适配器已初始化（延迟初始化）"""
        if self.ai_adapter is not None:
            # 检查AI适配器是否真正可用
            try:
                if hasattr(self.ai_adapter, 'is_initialized') and not self.ai_adapter.is_initialized:
                    self.logger.info("AI适配器未初始化，尝试初始化...")
                    if self.ai_adapter.initialize():
                        self.logger.info("AI适配器初始化成功")
                        return True
                    else:
                        self.logger.warning("AI适配器初始化失败")
                        return False
                return True
            except Exception as e:
                self.logger.warning(f"检查AI适配器状态失败: {e}")
                return False
            
        try:
            # 直接获取AI服务适配器，避免连接池阻塞
            self.ai_adapter = get_ai_service_adapter()
            
            # 🔥 修复：检查是否成功获取AI适配器
            if self.ai_adapter is None:
                self.logger.warning("AI服务适配器获取失败，返回None")
                return False
            
            # 确保初始化
            if not hasattr(self.ai_adapter, 'is_initialized') or not self.ai_adapter.is_initialized:
                self.logger.info("初始化AI适配器...")
                if self.ai_adapter.initialize():
                    self.logger.info("AI适配器初始化成功")
                else:
                    self.logger.warning("AI适配器初始化失败")
                    return False
            
            self.logger.info("AI服务适配器获取成功")
            return True
            
        except Exception as e:
            self.logger.warning(f"无法获取AI服务适配器: {e}")
        
        try:
            # 尝试从单例管理器获取现有实例（兼容模式）
            from utilities.singleton_manager import get
            self.ai_adapter = get('ai_service_adapter')
            if self.ai_adapter is not None:
                self.logger.info("使用单例管理器中的AI适配器实例")
                return True
        except Exception as e:
            self.logger.warning(f"无法从单例管理器获取AI适配器: {e}")
        
        self.logger.warning("无法获取AI服务连接，决策将使用基础规则")
        return False

    async def _make_ai_request(self, prompt: str) -> str:
        """统一的AI请求方法 - 🔥 优化版本，处理token限制"""
        try:
            if not self.ai_adapter:
                self._ensure_ai_adapter()
            
            # 🔥 老王修复：根据提示词长度动态调整token限制，增加更多token避免截断
            prompt_length = len(prompt)
            if prompt_length > 8000:
                # 长提示词，使用更多token
                max_tokens = 32000  # 增加到32000，避免截断
                model = 'gpt-3.5-turbo-16k-0613'
                self.logger.debug(f"🧠 长提示词({prompt_length}字符)，使用大模型和更多token")
            elif prompt_length > 4000:
                # 中等提示词
                max_tokens = 24000  # 增加到24000，避免截断
                model = 'gpt-3.5-turbo-16k-0613'
                self.logger.debug(f"🧠 中等提示词({prompt_length}字符)，使用标准配置")
            else:
                # 短提示词
                max_tokens = 16000  # 增加到16000，避免截断
                model = self.decision_config.get('ai_model', 'gpt-3.5-turbo-16k-0613')
                self.logger.debug(f"🧠 短提示词({prompt_length}字符)，使用默认配置")
            
            # 🔥 老王修复：将prompt转换为messages格式
            messages = [{"role": "user", "content": prompt}]
            
            # 使用统一AI适配器
            response = await self.ai_adapter.get_completion_async(
                messages=messages,
                service=self.decision_config.get('ai_service', 'openai'),
                model=model,
                max_tokens=max_tokens,
                temperature=self.decision_config.get('temperature', 0.7)
            )
            
            if response and 'choices' in response and response['choices']:
                # 🔥 老王修复：增强响应解析，处理不同格式的响应
                choice = response['choices'][0]

                # 检查是否有message字段（ChatCompletion格式）
                if 'message' in choice and 'content' in choice['message']:
                    content = choice['message']['content']
                # 检查是否有text字段（Completion格式）
                elif 'text' in choice:
                    content = choice['text']
                # 直接尝试获取content字段
                elif 'content' in choice:
                    content = choice['content']
                else:
                    # 如果找不到内容，记录完整响应并返回空字符串
                    self.logger.warning(f"🧠 ⚠️ 无法从响应中提取内容: {choice}")
                    return ""

                result = content.strip() if content else ""

                # 🔥 老王修复：增强空响应检测和处理
                if not result:
                    self.logger.warning("🧠 ⚠️ AI响应内容为空")
                    self.logger.debug(f"🔍 原始AI响应: {response}")
                    # 清理可能的空缓存
                    self._clear_empty_cache_entries()
                    return ""

                # 检查响应是否只包含空白字符
                if not result.strip():
                    self.logger.warning("🧠 ⚠️ AI响应只包含空白字符")
                    self.logger.debug(f"🔍 空白字符响应: '{result}'")
                    self._clear_empty_cache_entries()
                    return ""

                # 🔥 老王修复：检查响应是否被截断，如果被截断则重新请求
                finish_reason = choice.get('finish_reason')
                if finish_reason == 'length':
                    self.logger.warning(f"🧠 ⚠️ AI响应被截断，长度: {len(result)}, 限制: {max_tokens}, 结束原因: {finish_reason}")

                    # 🔥 老王修复：增强重试逻辑，支持多次重试
                    max_retry_count = 3  # 最大重试次数
                    retry_count = 0
                    current_max_tokens = max_tokens
                    retry_content = None
                    
                    # 多次重试，直到获得完整响应或达到最大重试次数
                    while retry_count < max_retry_count and current_max_tokens < 100000:  # 避免无限递归，增加上限
                        retry_count += 1
                        # 每次将token限制增加一倍，但不超过100000
                        current_max_tokens = min(100000, current_max_tokens * 2)
                        self.logger.info(f"🧠 🔄 第{retry_count}次重试，使用更大token限制: {current_max_tokens}")

                        # 重新构建消息，使用更大的token限制
                        retry_response = await self.ai_adapter.get_completion_async(
                            messages=messages,
                            service=self.decision_config.get('ai_service', 'openai'),
                            model=model,
                            max_tokens=current_max_tokens,
                            temperature=self.decision_config.get('temperature', 0.7)
                        )

                        if retry_response and 'choices' in retry_response and retry_response['choices']:
                            retry_choice = retry_response['choices'][0]
                            retry_content = None

                            if 'message' in retry_choice and 'content' in retry_choice['message']:
                                retry_content = retry_choice['message']['content']
                            elif 'text' in retry_choice:
                                retry_content = retry_choice['text']
                            elif 'content' in retry_choice:
                                retry_content = retry_choice['content']

                            if retry_content and retry_content.strip():
                                retry_finish_reason = retry_choice.get('finish_reason')
                                if retry_finish_reason != 'length':
                                    self.logger.success(f"🧠 ✅ 第{retry_count}次重试成功，获得完整响应: {len(retry_content)} 字符")
                                    return retry_content.strip()
                                else:
                                    self.logger.warning(f"🧠 ⚠️ 第{retry_count}次重试后仍被截断，响应长度: {len(retry_content)} 字符")
                                    # 保存最新的截断响应，如果所有重试都失败，使用最后一次的响应
                                    result = retry_content
                        else:
                            self.logger.error(f"🧠 ❌ 第{retry_count}次重试失败，无有效响应")
                            break

                    # 如果所有重试都失败，使用最后一次的响应（可能是原始响应或最后一次重试的响应）
                    if result and result.strip():
                        self.logger.warning(f"🧠 ⚠️ 经过{retry_count}次重试后仍被截断，使用最后一次响应: {len(result)} 字符")
                        return result
                    else:
                        self.logger.error(f"🧠 ❌ 所有响应都为空")
                        return ""
                else:
                    self.logger.debug(f"🧠 AI响应长度: {len(result)} 字符，未被截断，结束原因: {finish_reason}")

                return result
            else:
                self.logger.error("🧠 AI响应格式异常")
                return ""
                
        except Exception as e:
            self.logger.error(f"🧠 AI请求失败: {e}")
            # 清理可能的错误缓存
            self._clear_empty_cache_entries()
            return ""

    def _clear_empty_cache_entries(self):
        """清理AI适配器中的空缓存条目"""
        try:
            if hasattr(self.ai_adapter, 'cache') and self.ai_adapter.cache:
                empty_keys = []
                for key, cache_item in self.ai_adapter.cache.items():
                    response = cache_item.get("response", {})
                    if isinstance(response, dict) and "choices" in response:
                        choices = response.get("choices", [])
                        if choices:
                            choice = choices[0]
                            content = ""
                            if "message" in choice and "content" in choice["message"]:
                                content = choice["message"]["content"]
                            elif "text" in choice:
                                content = choice["text"]

                            # 如果内容为空或只有空白字符，标记为需要清理
                            if not content or not content.strip():
                                empty_keys.append(key)

                # 清理空缓存条目
                for key in empty_keys:
                    del self.ai_adapter.cache[key]
                    self.logger.info(f"🧹 清理空缓存条目: {key}")

                if empty_keys:
                    self.logger.info(f"🧹 共清理了 {len(empty_keys)} 个空缓存条目")

        except Exception as e:
            self.logger.warning(f"清理空缓存失败: {e}")

# 全局实例
_decision_engine_instance = None

def get_instance(config_path: Optional[str] = None, ai_adapter=None) -> YanranAIDecisionEngine:
    """获取嫣然AI决策引擎单例"""
    global _decision_engine_instance
    if _decision_engine_instance is None:
        _decision_engine_instance = YanranAIDecisionEngine(config_path, ai_adapter)
    return _decision_engine_instance

def get_yanran_decision_engine() -> YanranAIDecisionEngine:
    """获取林嫣然AI决策引擎单例实例"""
    global _instance, _instance_lock
    
    with _instance_lock:
        if _instance is None:
            _instance = YanranAIDecisionEngine()
        return _instance