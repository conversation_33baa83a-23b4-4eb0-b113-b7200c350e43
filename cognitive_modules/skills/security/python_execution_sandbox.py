#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Python执行安全沙箱
为Python执行技能提供多层安全防护机制

作者: 隔壁老王 (暴躁的代码架构师)
版本: 1.0.0

安全特性:
- 代码静态分析和危险操作检测
- 资源使用限制 (CPU、内存、时间)
- 文件系统访问控制
- 网络访问限制
- 模块导入白名单
- 执行环境隔离
"""

import re
import ast
import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass

from utilities.logger import get_logger
logger = get_logger(__name__)


@dataclass
class SecurityConfig:
    """安全配置"""
    # 时间限制
    max_execution_time: int = 300  # 最大执行时间(秒)
    
    # 资源限制
    max_memory_mb: int = 1024      # 最大内存使用(MB)
    max_cpu_percent: int = 80      # 最大CPU使用率(%)
    max_file_size_mb: int = 100    # 最大文件大小(MB)
    
    # 模块白名单
    allowed_modules: List[str] = None
    
    # 禁止的操作
    blocked_operations: List[str] = None
    
    # 禁止的文件路径模式
    blocked_file_patterns: List[str] = None
    
    # 网络访问控制
    allow_network_access: bool = True
    allowed_domains: List[str] = None
    
    def __post_init__(self):
        if self.allowed_modules is None:
            self.allowed_modules = [
                "pandas", "numpy", "matplotlib", "seaborn", "plotly",
                "requests", "beautifulsoup4", "json", "csv", "sqlite3",
                "openpyxl", "scipy", "sklearn", "yfinance", "feedparser",
                "datetime", "time", "re", "math", "statistics", "urllib",
                "collections", "itertools", "functools", "operator"
            ]
        
        if self.blocked_operations is None:
            self.blocked_operations = [
                "os.system", "subprocess.run", "subprocess.call", "subprocess.Popen",
                "exec", "eval", "__import__", "compile", "globals", "locals",
                "open('/etc", "open('/root", "open('/home", "open('~",
                "rm -rf", "sudo", "chmod", "chown", "kill", "killall",
                "format", "input", "raw_input"
            ]
        
        if self.blocked_file_patterns is None:
            self.blocked_file_patterns = [
                r"/etc/.*", r"/root/.*", r"/home/<USER>", r"~/.ssh/.*",
                r"/var/log/.*", r"/proc/.*", r"/sys/.*", r"/dev/.*",
                r"\.\.\/.*", r".*\.sh$", r".*\.exe$", r".*\.bat$"
            ]
        
        if self.allowed_domains is None:
            self.allowed_domains = [
                "api.github.com", "pypi.org", "finance.yahoo.com",
                "sina.com.cn", "163.com", "qq.com", "baidu.com"
            ]


class SecurityViolationError(Exception):
    """安全违规异常"""
    pass


class PythonExecutionSandbox:
    """
    Python执行安全沙箱
    
    艹！这个沙箱比监狱还严格，任何想搞破坏的代码都别想逃脱老王的法眼！
    """
    
    def __init__(self, config: Optional[SecurityConfig] = None):
        """初始化安全沙箱"""
        self.config = config or SecurityConfig()
        self.execution_stats = {}
        
        logger.debug("🔒 Python执行安全沙箱初始化完成")
    
    def validate_code_safety(self, code_or_instruction: str) -> Tuple[bool, str]:
        """
        验证代码安全性
        
        Args:
            code_or_instruction: 代码或指令
            
        Returns:
            (是否安全, 错误信息)
        """
        try:
            # 1. 基本文本检查
            is_safe, error = self._check_text_patterns(code_or_instruction)
            if not is_safe:
                return False, error
            
            # 2. 尝试AST解析检查（如果是Python代码）
            if self._looks_like_python_code(code_or_instruction):
                is_safe, error = self._check_ast_safety(code_or_instruction)
                if not is_safe:
                    return False, error
            
            # 3. 文件路径检查
            is_safe, error = self._check_file_paths(code_or_instruction)
            if not is_safe:
                return False, error
            
            # 4. 网络访问检查
            is_safe, error = self._check_network_access(code_or_instruction)
            if not is_safe:
                return False, error
            
            return True, ""
            
        except Exception as e:
            logger.warning(f"代码安全检查异常: {e}")
            return False, f"安全检查异常: {str(e)}"
    
    def _check_text_patterns(self, text: str) -> Tuple[bool, str]:
        """检查文本中的危险模式"""
        text_lower = text.lower()
        
        # 检查被禁止的操作
        for blocked_op in self.config.blocked_operations:
            if blocked_op.lower() in text_lower:
                return False, f"包含被禁止的操作: {blocked_op}"
        
        # 检查危险关键词
        dangerous_keywords = [
            "delete", "remove", "destroy", "format", "wipe",
            "password", "secret", "token", "key", "credential"
        ]
        
        for keyword in dangerous_keywords:
            if keyword in text_lower and any(danger in text_lower for danger in ["system", "file", "disk"]):
                return False, f"包含潜在危险操作: {keyword}"
        
        return True, ""
    
    def _looks_like_python_code(self, text: str) -> bool:
        """判断文本是否像Python代码"""
        python_indicators = [
            "import ", "from ", "def ", "class ", "if ", "for ", "while ",
            "try:", "except:", "with ", "lambda ", "yield ", "return "
        ]
        
        return any(indicator in text for indicator in python_indicators)
    
    def _check_ast_safety(self, code: str) -> Tuple[bool, str]:
        """使用AST检查Python代码安全性"""
        try:
            tree = ast.parse(code)
            
            # 检查危险的AST节点
            for node in ast.walk(tree):
                # 检查函数调用
                if isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Name):
                        func_name = node.func.id
                        if func_name in ["exec", "eval", "compile", "__import__"]:
                            return False, f"禁止使用函数: {func_name}"
                    
                    elif isinstance(node.func, ast.Attribute):
                        if hasattr(node.func.value, 'id'):
                            module_name = node.func.value.id
                            func_name = node.func.attr
                            full_name = f"{module_name}.{func_name}"
                            
                            if full_name in self.config.blocked_operations:
                                return False, f"禁止使用方法: {full_name}"
                
                # 检查导入语句
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name not in self.config.allowed_modules:
                            return False, f"禁止导入模块: {alias.name}"
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module and node.module not in self.config.allowed_modules:
                        return False, f"禁止从模块导入: {node.module}"
            
            return True, ""
            
        except SyntaxError:
            # 不是有效的Python代码，跳过AST检查
            return True, ""
        except Exception as e:
            logger.warning(f"AST安全检查异常: {e}")
            return True, ""  # 检查异常时允许通过，由其他检查兜底
    
    def _check_file_paths(self, text: str) -> Tuple[bool, str]:
        """检查文件路径安全性"""
        for pattern in self.config.blocked_file_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return False, f"包含被禁止的文件路径模式: {pattern}"
        
        return True, ""
    
    def _check_network_access(self, text: str) -> Tuple[bool, str]:
        """检查网络访问安全性"""
        if not self.config.allow_network_access:
            network_keywords = ["requests.", "urllib.", "http", "https", "ftp"]
            for keyword in network_keywords:
                if keyword in text.lower():
                    return False, "网络访问被禁止"
        
        # 检查域名白名单
        url_pattern = r'https?://([^/\s]+)'
        urls = re.findall(url_pattern, text, re.IGNORECASE)
        
        for url in urls:
            domain = url.lower()
            if not any(allowed in domain for allowed in self.config.allowed_domains):
                return False, f"访问未授权域名: {domain}"
        
        return True, ""
    
    def create_execution_monitor(self, execution_id: str) -> 'ExecutionMonitor':
        """创建执行监控器"""
        return ExecutionMonitor(execution_id, self.config)
    
    def get_security_report(self) -> Dict[str, Any]:
        """获取安全报告"""
        return {
            "config": {
                "max_execution_time": self.config.max_execution_time,
                "max_memory_mb": self.config.max_memory_mb,
                "max_cpu_percent": self.config.max_cpu_percent,
                "allowed_modules_count": len(self.config.allowed_modules),
                "blocked_operations_count": len(self.config.blocked_operations)
            },
            "stats": self.execution_stats,
            "timestamp": time.time()
        }


class ExecutionMonitor:
    """执行监控器 - 监控运行时资源使用"""
    
    def __init__(self, execution_id: str, config: SecurityConfig):
        self.execution_id = execution_id
        self.config = config
        self.start_time = None
        self.monitoring = False
        self.monitor_thread = None
        self.violations = []
        
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        logger.debug(f"🔍 [{self.execution_id}] 开始资源监控")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        
        execution_time = time.time() - self.start_time if self.start_time else 0
        logger.debug(f"🔍 [{self.execution_id}] 停止资源监控，执行时间: {execution_time:.2f}s")
        
        return {
            "execution_time": execution_time,
            "violations": self.violations
        }
    
    def _monitor_resources(self):
        """监控资源使用"""
        process = psutil.Process()
        
        while self.monitoring:
            try:
                # 检查执行时间
                if self.start_time:
                    execution_time = time.time() - self.start_time
                    if execution_time > self.config.max_execution_time:
                        self.violations.append({
                            "type": "execution_timeout",
                            "message": f"执行时间超限: {execution_time:.2f}s > {self.config.max_execution_time}s",
                            "timestamp": time.time()
                        })
                        break
                
                # 检查内存使用
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                if memory_mb > self.config.max_memory_mb:
                    self.violations.append({
                        "type": "memory_limit",
                        "message": f"内存使用超限: {memory_mb:.2f}MB > {self.config.max_memory_mb}MB",
                        "timestamp": time.time()
                    })
                
                # 检查CPU使用
                cpu_percent = process.cpu_percent()
                if cpu_percent > self.config.max_cpu_percent:
                    self.violations.append({
                        "type": "cpu_limit",
                        "message": f"CPU使用超限: {cpu_percent:.2f}% > {self.config.max_cpu_percent}%",
                        "timestamp": time.time()
                    })
                
                time.sleep(1)  # 每秒检查一次
                
            except psutil.NoSuchProcess:
                # 进程已结束
                break
            except Exception as e:
                logger.warning(f"资源监控异常: {e}")
                break


def create_sandbox(config: Optional[Dict[str, Any]] = None) -> PythonExecutionSandbox:
    """创建安全沙箱实例"""
    if config:
        security_config = SecurityConfig(**config)
    else:
        security_config = SecurityConfig()
    
    return PythonExecutionSandbox(security_config)


# 测试代码
if __name__ == "__main__":
    # 创建沙箱
    sandbox = create_sandbox()
    
    # 测试用例
    test_cases = [
        ("import pandas as pd", True),
        ("os.system('rm -rf /')", False),
        ("exec('print(1)')", False),
        ("open('/etc/passwd')", False),
        ("import requests; requests.get('http://evil.com')", False),
        ("df = pd.DataFrame({'a': [1,2,3]})", True),
    ]
    
    print("🔒 安全沙箱测试")
    print("=" * 50)
    
    for code, expected_safe in test_cases:
        is_safe, error = sandbox.validate_code_safety(code)
        status = "✅" if is_safe == expected_safe else "❌"
        print(f"{status} {code[:50]:<50} {'安全' if is_safe else '危险'}")
        if not is_safe:
            print(f"   错误: {error}")
    
    print("\n📊 安全报告:")
    report = sandbox.get_security_report()
    print(f"允许模块数: {report['config']['allowed_modules_count']}")
    print(f"禁止操作数: {report['config']['blocked_operations_count']}")
    print(f"最大执行时间: {report['config']['max_execution_time']}秒")
    print(f"最大内存限制: {report['config']['max_memory_mb']}MB")
