#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强活动技能

将增强型活动生成器包装为数字生命技能
提供真实性和一致性兼备的活动脚本生成能力

核心功能:
1. 基于真实地理位置数据的活动生成
2. 集成天气、POI、路线等真实信息
3. 活动一致性和逻辑检查
4. 高质量的活动真实性验证
5. 完整的统计和监控能力

作者: 香草 💕
创建时间: 2025-01-08
版本: v1.0.0
"""

import os
import sys
from typing import Dict, Any, List, Optional
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, current_dir)

from utilities.unified_logger import get_unified_logger
from core.skill_manager import Skill
from services.enhanced_activity_generator.enhanced_activity_script_generator import get_enhanced_activity_generator

class EnhancedActivitySkill(Skill):
    """增强活动技能 - 解决活动描述"假大空"问题的核心技能"""
    
    def __init__(self):
        """初始化增强活动技能"""
        super().__init__(
            skill_id="enhanced_activity_skill",
            name="增强型活动脚本生成技能",
            description="基于真实地理位置数据生成高质量活动脚本，解决活动描述假大空问题",
            version="1.0.0"
        )
        
        # 初始化增强活动生成器
        self.activity_generator = None
        self.initialized = False
        
        # 统计信息
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'average_reality_score': 0.0,
            'average_response_time': 0.0,
            'most_requested_activity_type': '',
            'skill_errors': 0
        }
        
        self.logger = get_unified_logger('enhanced_activity_skill')
        self.logger.info("🎯 增强活动技能初始化完成")
    
    async def initialize(self) -> bool:
        """初始化技能组件"""
        try:
            if self.initialized:
                return True
            
            # 获取增强活动生成器实例
            self.activity_generator = get_enhanced_activity_generator()
            
            # 初始化生成器
            success = await self.activity_generator.initialize()
            
            if success:
                self.initialized = True
                self.logger.success("🎯 增强活动技能初始化成功")
                return True
            else:
                self.logger.error("🎯 增强活动生成器初始化失败")
                return False
                
        except Exception as e:
            self.logger.error(f"🎯 增强活动技能初始化异常: {e}")
            return False
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行增强活动生成 - 技能核心方法
        
        Args:
            params: 技能执行参数
                - user_id (必需): 用户ID
                - activity_type (可选): 活动类型，默认'daily_life'
                - location (可选): 指定位置信息
                - context (可选): 额外上下文信息
                - preferences (可选): 用户偏好设置
                
        Returns:
            技能执行结果，包含生成的活动脚本和元数据
        """
        start_time = datetime.now()
        self.execution_stats['total_executions'] += 1
        
        try:
            # 确保技能已初始化
            if not self.initialized:
                await self.initialize()
            
            if not self.initialized:
                return self._create_error_result("技能未正确初始化", params)
            
            # 参数验证和提取
            validation_result = self._validate_and_extract_params(params)
            if not validation_result['valid']:
                return self._create_error_result(validation_result['error'], params)
            
            user_id = validation_result['user_id']
            activity_type = validation_result['activity_type']
            context = validation_result['context']
            
            self.logger.info(f"🎯 执行增强活动生成: {user_id} - {activity_type}")
            
            # 调用增强活动生成器
            generated_script = await self.activity_generator.generate_realistic_activity(
                user_id=user_id,
                activity_type=activity_type,
                current_context=context
            )
            
            # 处理生成结果
            skill_result = self._process_generation_result(generated_script, params, start_time)
            
            # 更新统计信息
            self._update_execution_stats(skill_result, activity_type, start_time)
            
            self.logger.success(f"🎯 增强活动生成完成，真实性评分: {skill_result.get('reality_score', 'N/A')}")
            return skill_result
            
        except Exception as e:
            self.execution_stats['skill_errors'] += 1
            self.logger.error(f"🎯 增强活动技能执行失败: {e}")
            return self._create_error_result(f"技能执行异常: {str(e)}", params)
    
    def _validate_and_extract_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证和提取参数"""
        try:
            # 必需参数检查
            if 'user_id' not in params:
                return {
                    'valid': False,
                    'error': '缺少必需参数: user_id'
                }
            
            user_id = params['user_id']
            if not user_id or not isinstance(user_id, str):
                return {
                    'valid': False,
                    'error': 'user_id 必须为非空字符串'
                }
            
            # 可选参数提取和默认值
            activity_type = params.get('activity_type', 'daily_life')
            
            # 验证活动类型
            valid_activity_types = [
                'daily_life', 'work', 'travel', 'creation', 
                'social', 'leisure', 'exercise', 'study'
            ]
            if activity_type not in valid_activity_types:
                self.logger.warning(f"🎯 未识别的活动类型: {activity_type}，使用默认值")
                activity_type = 'daily_life'
            
            # 构建上下文
            context = params.get('context', {})
            
            # 合并位置信息到上下文
            if 'location' in params:
                context['suggested_location'] = params['location']
            
            # 合并用户偏好到上下文  
            if 'preferences' in params:
                context['user_preferences'] = params['preferences']
            
            # 添加技能调用信息到上下文
            context['skill_invocation'] = {
                'skill_id': self.skill_id,
                'invocation_time': datetime.now().isoformat(),
                'original_params': params
            }
            
            return {
                'valid': True,
                'user_id': user_id,
                'activity_type': activity_type,
                'context': context
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': f'参数验证异常: {str(e)}'
            }
    
    def _process_generation_result(self, generated_script: Dict[str, Any], 
                                 original_params: Dict[str, Any], 
                                 start_time: datetime) -> Dict[str, Any]:
        """处理生成结果，转换为技能标准格式"""
        try:
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 提取关键信息
            activity_title = generated_script.get('activity_title', '')
            activity_description = generated_script.get('activity_description', '')
            location_mentioned = generated_script.get('location_mentioned', '')
            weather_context = generated_script.get('weather_context', '')
            
            # 提取元数据和验证结果
            metadata = generated_script.get('metadata', {})
            reality_score = 0.0
            
            # 尝试从不同位置获取真实性评分
            if 'validation_result' in metadata:
                reality_score = metadata['validation_result'].get('score', 0.0)
            elif 'reality_score' in generated_script:
                reality_score = generated_script['reality_score']
            
            # 构建技能执行结果
            skill_result = {
                'success': True,
                'skill_name': self.name,
                'skill_version': self.version,
                'execution_time_seconds': execution_time,
                
                # 生成的活动内容
                'activity_content': {
                    'title': activity_title,
                    'description': activity_description,
                    'location_mentioned': location_mentioned,
                    'weather_context': weather_context,
                    'reality_elements': generated_script.get('reality_elements', []),
                    'poi_details': generated_script.get('poi_details', ''),
                    'time_context': generated_script.get('time_context', ''),
                    'practical_details': generated_script.get('practical_details', '')
                },
                
                # 质量和验证信息
                'quality_metrics': {
                    'reality_score': reality_score,
                    'generation_method': metadata.get('generation_method', 'enhanced_reality_based'),
                    'data_quality': metadata.get('location_data', {}).get('data_quality', {}),
                    'has_real_location': bool(location_mentioned and location_mentioned != '当前位置'),
                    'has_weather_integration': bool(weather_context),
                    'has_poi_integration': bool(generated_script.get('poi_details'))
                },
                
                # 原始生成结果（供调试和进一步处理）
                'raw_generation_result': generated_script,
                
                # 执行信息
                'execution_info': {
                    'execution_time': datetime.now().isoformat(),
                    'original_params': original_params,
                    'skill_id': self.skill_id
                }
            }
            
            return skill_result
            
        except Exception as e:
            self.logger.error(f"🎯 处理生成结果失败: {e}")
            return self._create_error_result(f"结果处理异常: {str(e)}", original_params)
    
    def _create_error_result(self, error_message: str, original_params: Dict[str, Any]) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            'success': False,
            'skill_name': self.name,
            'skill_version': self.version,
            'error': error_message,
            'activity_content': {
                'title': '活动生成失败',
                'description': f'抱歉，活动生成遇到问题: {error_message}',
                'location_mentioned': '',
                'weather_context': ''
            },
            'quality_metrics': {
                'reality_score': 0.0,
                'generation_method': 'error_fallback'
            },
            'execution_info': {
                'execution_time': datetime.now().isoformat(),
                'original_params': original_params,
                'skill_id': self.skill_id,
                'error_details': error_message
            }
        }
    
    def _update_execution_stats(self, result: Dict[str, Any], activity_type: str, start_time: datetime):
        """更新执行统计信息"""
        try:
            if result.get('success', False):
                self.execution_stats['successful_executions'] += 1
                
                # 更新平均真实性评分
                reality_score = result.get('quality_metrics', {}).get('reality_score', 0.0)
                current_avg = self.execution_stats['average_reality_score']
                successful_count = self.execution_stats['successful_executions']
                
                self.execution_stats['average_reality_score'] = (
                    (current_avg * (successful_count - 1) + reality_score) / successful_count
                )
            
            # 更新平均响应时间
            execution_time = (datetime.now() - start_time).total_seconds()
            current_avg_time = self.execution_stats['average_response_time']
            total_count = self.execution_stats['total_executions']
            
            self.execution_stats['average_response_time'] = (
                (current_avg_time * (total_count - 1) + execution_time) / total_count
            )
            
            # 更新最常请求的活动类型（简单实现）
            self.execution_stats['most_requested_activity_type'] = activity_type
            
        except Exception as e:
            self.logger.error(f"🎯 更新执行统计失败: {e}")
    
    def get_skill_info(self) -> Dict[str, Any]:
        """获取技能信息"""
        base_info = {
            'skill_id': self.skill_id,
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'enabled': self.enabled,
            'loaded_at': self.loaded_at,
            'last_used': self.last_used,
            'usage_count': self.usage_count
        }
        
        # 添加增强活动技能特有的信息
        enhanced_info = {
            **base_info,
            'execution_stats': self.execution_stats,
            'initialized': self.initialized,
            'activity_types_supported': [
                'daily_life', 'work', 'travel', 'creation', 
                'social', 'leisure', 'exercise', 'study'
            ],
            'key_features': [
                '真实地理位置数据集成',
                '实时天气信息融入',
                '周边POI智能推荐', 
                'AI驱动的自然语言生成',
                '活动真实性多维度验证',
                '完整的统计和监控'
            ]
        }
        
        return enhanced_info
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取详细的执行统计信息"""
        total_executions = max(self.execution_stats['total_executions'], 1)
        successful_executions = self.execution_stats['successful_executions']
        
        return {
            **self.execution_stats,
            'success_rate': successful_executions / total_executions,
            'error_rate': self.execution_stats['skill_errors'] / total_executions,
            'performance_rating': self._calculate_performance_rating()
        }
    
    def _calculate_performance_rating(self) -> str:
        """计算性能评级"""
        stats = self.execution_stats
        
        if stats['total_executions'] == 0:
            return 'not_evaluated'
        
        success_rate = stats['successful_executions'] / stats['total_executions']
        avg_reality_score = stats['average_reality_score']
        avg_response_time = stats['average_response_time']
        
        # 综合评分算法
        performance_score = (
            success_rate * 0.4 +           # 成功率权重40%
            avg_reality_score * 0.4 +      # 真实性评分权重40%
            (1 - min(avg_response_time / 10, 1)) * 0.2  # 响应时间权重20%
        )
        
        if performance_score >= 0.9:
            return 'excellent'
        elif performance_score >= 0.8:
            return 'good'
        elif performance_score >= 0.6:
            return 'acceptable'
        else:
            return 'needs_improvement'
    
    async def cleanup(self):
        """清理技能资源"""
        try:
            if self.activity_generator:
                await self.activity_generator.cleanup()
            
            self.logger.info("🎯 增强活动技能资源清理完成")
            
        except Exception as e:
            self.logger.error(f"🎯 技能资源清理失败: {e}")

def create_enhanced_activity_skill() -> EnhancedActivitySkill:
    """创建增强活动技能实例的工厂函数"""
    return EnhancedActivitySkill()

def get_instance() -> EnhancedActivitySkill:
    """获取增强活动技能实例 - SkillManager需要的标准接口"""
    return EnhancedActivitySkill()
