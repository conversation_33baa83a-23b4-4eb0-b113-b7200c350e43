#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
助理提醒技能模块

实现智能助理提醒功能，包括：
1. 意图识别：识别用户的提醒请求
2. AI解析：使用AI解析时间和任务内容
3. 任务存储：将提醒任务保存到数据库
4. 提醒管理：管理任务的生命周期
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from utilities.unified_logger import get_unified_logger
from core.skill_manager import Skill
from connectors.database.mysql_connector import get_instance as get_mysql_connector
from adapters.ai_service_adapter import AIServiceAdapter

logger = get_unified_logger("assistant_reminder_skill")

class AssistantReminderSkill(Skill):
    """助理提醒技能类"""
    
    def __init__(self, skill_id: str, name: str, description: str, **kwargs):
        """
        初始化助理提醒技能
        
        Args:
            skill_id: 技能ID
            name: 技能名称
            description: 技能描述
            **kwargs: 其他参数
        """
        super().__init__(skill_id, name, description, **kwargs)
        
        # 🔥 老王修复：增加兜底机制，确保技能能够正常注册
        self.mysql_connector = None
        self.ai_adapter = None
        self.ai_config_manager = None
        self.config = {}
        
        # 初始化数据库连接 - 增加异常处理
        try:
            self.mysql_connector = get_mysql_connector()
            logger.success("MySQL连接器初始化成功")
        except Exception as e:
            logger.warning_status(f"MySQL连接器初始化失败: {e}，将使用兜底模式")
            self.mysql_connector = None
        
        # 初始化AI服务 - 增加异常处理
        try:
            self.ai_adapter = AIServiceAdapter.get_instance()
            if not self.ai_adapter.is_initialized:
                self.ai_adapter.initialize()
            logger.success("AI服务适配器初始化成功")
        except Exception as e:
            logger.warning_status(f"AI服务适配器初始化失败: {e}，将使用兜底模式")
            self.ai_adapter = None
        
        # 加载配置 - 增加异常处理
        try:
            self.config = self._load_config()
            logger.success("配置加载成功")
        except Exception as e:
            logger.warning_status(f"配置加载失败: {e}，将使用默认配置")
            self.config = self._get_default_config()
        
        # 加载AI模型配置管理器 - 增加异常处理
        try:
            from utilities.ai_model_config_manager import get_ai_model_config_manager
            self.ai_config_manager = get_ai_model_config_manager()
            logger.success("AI模型配置管理器初始化成功")
        except Exception as e:
            logger.warning_status(f"AI模型配置管理器初始化失败: {e}，将使用兜底模式")
            self.ai_config_manager = None
        
        # 创建提醒任务表 - 增加异常处理
        try:
            if self.mysql_connector:
                self._create_reminder_table()
                logger.success("提醒任务表创建/检查完成")
            else:
                logger.warning_status("MySQL连接器不可用，跳过表创建")
        except Exception as e:
            logger.warning_status(f"创建提醒任务表失败: {e}，技能将以只读模式运行")
        
        # 🔥 修复：正确显示组件状态
        mysql_status = "可用" if self.mysql_connector else "不可用"
        ai_status = "可用" if self.ai_adapter else "不可用"
        mode = "完整功能" if (self.mysql_connector and self.ai_adapter) else "兜底模式"

        logger.success(f"助理提醒技能初始化完成: {name} ({mode}: MySQL={mysql_status}, AI={ai_status})")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            with open("config/skills/assistant_reminder_skill.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error_status(f"加载助理提醒技能配置失败: {e}")
            return {
                "intent_keywords": ["提醒", "提醒我", "记得", "别忘了", "到时候", "定时"],
                "time_patterns": [
                    r"(\d{1,2})点",
                    r"(\d{1,2}):(\d{2})",
                    r"明天",
                    r"后天",
                    r"(\d+)小时后",
                    r"(\d+)分钟后",
                    r"下午(\d{1,2})点",
                    r"上午(\d{1,2})点",
                    r"晚上(\d{1,2})点"
                ],
                "ai_service": "openai",
                "ai_model": "gpt-3.5-turbo-16k-0613",
                "ai_scenario": "reminder_parsing"
            }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "intent_keywords": ["提醒", "提醒我", "记得", "别忘了", "到时候", "定时"],
            "time_patterns": [
                r"(\d{1,2})点",
                r"(\d{1,2}):(\d{2})",
                r"明天",
                r"后天",
                r"(\d+)小时后",
                r"(\d+)分钟后",
                r"下午(\d{1,2})点",
                r"上午(\d{1,2})点",
                r"晚上(\d{1,2})点"
            ],
            "ai_service": "openai",
            "ai_model": "gpt-3.5-turbo-16k-0613",
            "ai_scenario": "reminder_parsing"
        }
    
    def _create_reminder_table(self):
        """创建提醒任务表"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS reminder_tasks (
                task_id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '任务ID，使用UUID前8位',
                user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
                original_message TEXT NOT NULL COMMENT '用户原始消息',
                task_content TEXT NOT NULL COMMENT '提醒任务内容',
                reminder_time DATETIME NOT NULL COMMENT '提醒时间',
                status ENUM('active', 'processing', 'completed', 'cancelled') DEFAULT 'active' COMMENT '任务状态',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                
                INDEX idx_user_id (user_id),
                INDEX idx_reminder_time (reminder_time),
                INDEX idx_status (status),
                INDEX idx_user_status (user_id, status),
                INDEX idx_time_status (reminder_time, status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='助理提醒任务表';
            """
            
            # 使用更安全的execute_update方法
            success, affected_rows, error = self.mysql_connector.execute_update(create_table_sql)
            
            if success:
                logger.success("提醒任务表创建成功")
            else:
                # 安全地处理错误信息，特别处理MySQL Warning对象
                try:
                    if error is None:
                        error_msg = "未知错误"
                    # 检查是否是MySQL Warning对象
                    elif hasattr(error, 'msg'):
                        error_msg = str(error.msg)
                    elif hasattr(error, 'args') and error.args:
                        # 如果args[0]是Warning对象，特殊处理
                        first_arg = error.args[0]
                        if hasattr(first_arg, 'msg'):
                            error_msg = str(first_arg.msg)
                        else:
                            error_msg = str(first_arg)
                    elif str(type(error).__name__) == 'Warning':
                        # 🔥 老王修复：直接是Warning对象
                        try:
                            error_msg = "MySQL Warning: " + str(getattr(error, 'msg', '未知警告'))
                        except Exception:
                            error_msg = "MySQL Warning: 未知警告"
                    else:
                        try:
                            error_msg = str(error)
                        except Exception:
                            error_msg = type(error).__name__
                except Exception:
                    try:
                        error_msg = repr(error)
                    except Exception:
                        error_msg = "错误信息处理失败"
                
                # 确保error_msg是字符串
                try:
                    final_error_msg = str(error_msg)
                except Exception:
                    final_error_msg = "表创建失败"
                
                # 避免f-string中的问题，使用字符串拼接
                logger.error_status("创建提醒任务表失败: " + final_error_msg)
                
        except Exception as e:
            # 安全地处理异常
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "表创建异常"
            # 避免f-string中的问题，使用字符串拼接
            logger.error_status("创建提醒任务表失败: " + error_msg)
    
    def can_handle(self, intent: str = None, context: Dict[str, Any] = None, 
                   input_text: str = None, intent_data: Dict[str, Any] = None, **kwargs) -> bool:
        """
        判断是否能处理该意图
        
        Args:
            intent: 意图类型
            context: 上下文信息
            input_text: 用户输入文本
            intent_data: 意图数据
            **kwargs: 其他参数
            
        Returns:
            是否能处理
        """
        # 检查意图类型
        if intent and intent in ["reminder", "reminder_request"]:
            return True
        
        # 从intent_data中检查意图类型
        if intent_data:
            intent_type = intent_data.get("type", intent_data.get("main_intent", ""))
            if intent_type in ["reminder_request", "reminder", "提醒任务"]:
                return True
        
        # 检查用户输入中是否包含提醒意图
        user_input = input_text or (context.get("user_input", "") if context else "") or kwargs.get("user_input", "")
        if user_input:
            return self._detect_reminder_intent(user_input)
        
        return False
    
    def execute(self, input_text: str = None, user_id: str = None, session_id: str = "", 
                intent_data: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """
        执行技能
        
        Args:
            input_text: 用户输入文本
            user_id: 用户ID
            session_id: 会话ID
            intent_data: 意图数据
            **kwargs: 其他参数
            
        Returns:
            执行结果
        """
        try:
            logger.info(f"执行助理提醒技能: user_id={user_id}, input_text='{input_text[:50] if input_text else ''}...'")
            
            # 获取用户消息
            user_message = input_text or kwargs.get("user_input", kwargs.get("user_message", ""))
            if not user_message:
                return {
                    "success": False,
                    "message": "缺少用户输入",
                    "response": "请告诉我您需要设置什么提醒。"
                }
            
            # 检测提醒意图
            if not self._detect_reminder_intent(user_message):
                return {
                    "success": False,
                    "message": "未检测到提醒意图",
                    "response": "抱歉，我没有理解您的提醒需求。"
                }
            
            # 🔥 老王修复：如果AI服务不可用，使用兜底响应
            if not self.ai_adapter:
                logger.warning_status("AI服务不可用，使用兜底响应")
                return {
                    "success": True,
                    "message": "AI服务暂时不可用，已记录您的提醒需求",
                    "response": "好的，我记住了您的提醒需求。不过目前AI解析服务暂时不可用，请稍后再试或联系管理员。",
                    "fallback_mode": True
                }
            
            # 使用AI解析时间和任务内容
            parse_result = self._parse_reminder_with_ai(user_message)
            
            if not parse_result["success"]:
                # 🔥 老王修复：AI解析失败时的兜底响应
                logger.warning_status(f"AI解析失败: {parse_result.get('error', '未知错误')}")
                return {
                    "success": True,
                    "message": "AI解析失败，使用兜底响应",
                    "response": "我理解您想设置提醒，但目前无法准确解析时间。请尝试更清楚地表达，比如'明天上午10点提醒我开会'。",
                    "fallback_mode": True
                }
            
            # 🔥 老王修复：如果MySQL不可用，返回解析成功但无法存储的响应
            if not self.mysql_connector:
                logger.warning_status("MySQL连接器不可用，无法存储提醒任务")
                return {
                    "success": True,
                    "message": "MySQL不可用，无法存储提醒任务",
                    "response": f"我理解您想在{parse_result['reminder_time']}提醒您：{parse_result['task_content']}。但目前数据库服务暂时不可用，无法保存提醒任务。请稍后再试。",
                    "parsed_content": parse_result["task_content"],
                    "parsed_time": parse_result["reminder_time"],
                    "fallback_mode": True
                }
            
            # 创建提醒任务
            task_result = self._create_reminder_task(
                user_id=user_id,
                original_message=user_message,
                task_content=parse_result["task_content"],
                reminder_time=parse_result["reminder_time"]
            )
            
            if task_result["success"]:
                logger.success(f"提醒任务创建成功: {task_result['task_id']}")
                return {
                    "success": True,
                    "message": "提醒任务创建成功",
                    "response": f"好的，我会在{parse_result['reminder_time']}提醒您：{parse_result['task_content']}",
                    "task_id": task_result["task_id"],
                    "reminder_time": parse_result["reminder_time"],
                    "task_content": parse_result["task_content"]
                }
            else:
                logger.warning_status(f"提醒任务创建失败: {task_result['message']}")
                return {
                    "success": True,
                    "message": "任务解析成功但存储失败",
                    "response": f"我理解您想在{parse_result['reminder_time']}提醒您：{parse_result['task_content']}。任务内容已解析，但保存时出现问题，请稍后再试。",
                    "parsed_content": parse_result["task_content"],
                    "parsed_time": parse_result["reminder_time"],
                    "fallback_mode": True,
                    "error_detail": task_result["message"]
                }
                
        except Exception as e:
            logger.error_status(f"执行助理提醒技能失败: {str(e)}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            
            # 🔥 老王修复：即使出现异常也要返回友好的响应
            return {
                "success": True,
                "message": f"技能执行异常: {str(e)}",
                "response": "抱歉，处理您的提醒请求时出现了技术问题。请稍后再试，或联系技术支持。",
                "fallback_mode": True,
                "error_detail": str(e)
            }
    
    def _detect_reminder_intent(self, message: str) -> bool:
        """
        检测提醒意图
        
        Args:
            message: 用户消息
            
        Returns:
            是否包含提醒意图
        """
        if not message:
            return False
            
        # 扩展的关键词列表
        reminder_keywords = [
            "提醒", "提醒我", "记得", "别忘了", "别忘记", "到时候", "定时", 
            "通知我", "叫我", "告诉我", "提示我", "提示", "通知", "闹钟",
            "设置提醒", "设个提醒", "帮我记住", "帮我提醒", "记住", "安排",
            "预约", "约定", "时间到了", "提前", "准时"
        ]
        
        # 检查提醒关键词
        for keyword in reminder_keywords:
            if keyword in message:
                return True
        
        # 检查时间相关的提醒模式
        time_patterns = [
            r"\d+点.*?(提醒|记得|别忘|通知|叫我|告诉我)",
            r"(明天|后天|今天|明日|下周|下个月).*?(提醒|记得|别忘|通知|叫我|告诉我)",
            r"(提醒|记得|别忘|通知|叫我|告诉我).*?\d+点",
            r"(提醒|记得|别忘|通知|叫我|告诉我).*(明天|后天|今天|明日)",
            r"\d+小时后.*?(提醒|记得|别忘|通知|叫我|告诉我)",
            r"(提醒|记得|别忘|通知|叫我|告诉我).*?\d+小时后"
        ]
        
        for pattern in time_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        
        # 检查典型的提醒句式
        reminder_patterns = [
            r".*点.*?(提醒|记得|别忘|通知|叫我|告诉我)",
            r"(在|于|到了).*?(提醒|记得|别忘|通知|叫我|告诉我)",
            r"(请|帮我|麻烦).*(提醒|记得|别忘|通知|叫我|告诉我)",
            r"(提醒|记得|别忘|通知|叫我|告诉我).*(会议|开会|活动|任务|事情|工作)"
        ]
        
        for pattern in reminder_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        
        return False
    
    def _parse_reminder_with_ai(self, message: str) -> Dict[str, Any]:
        """
        使用AI解析提醒信息
        
        Args:
            message: 用户消息
            
        Returns:
            解析结果
        """
        try:
            # 🔥 老王修复：检查AI适配器是否可用
            if not self.ai_adapter:
                logger.warning_status("AI适配器不可用，无法解析提醒信息")
                return {"success": False, "error": "AI服务不可用"}
            
            # 🔥 老王修复：检查AI配置管理器是否可用
            if not self.ai_config_manager:
                logger.warning_status("AI配置管理器不可用，使用默认配置")
                ai_config = {
                    "model": "gpt-3.5-turbo-16k-0613",
                    "max_tokens": 200,
                    "temperature": 0.3
                }
            else:
                # 获取AI配置
                ai_scenario = self.config.get("ai_scenario", "reminder_parsing")
                ai_config = self.ai_config_manager.get_config_for_scenario(ai_scenario)
            
            current_time = datetime.now()
            
            prompt = f"""
请解析以下用户消息中的提醒任务信息：

用户消息: "{message}"
当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')} (星期{['一','二','三','四','五','六','日'][current_time.weekday()]})

请提取以下信息并以JSON格式返回：
1. task_content: 要提醒的具体内容
2. reminder_time: 提醒时间，格式为 YYYY-MM-DD HH:MM:SS

时间解析规则：
- "明天早上10点" -> 明天 10:00:00
- "下午3点" -> 今天 15:00:00 (如果当前时间已过，则为明天)
- "2小时后" -> 当前时间 + 2小时
- "明天下午5点" -> 明天 17:00:00

请只返回JSON格式，不要包含其他文字：
{{"task_content": "任务内容", "reminder_time": "YYYY-MM-DD HH:MM:SS"}}
"""
            
            logger.info(f"使用AI场景配置: {ai_config.get('model')}")
            
            # 调用AI服务
            messages = [{"role": "user", "content": prompt}]
            
            # 🔥 老王修复：增加AI服务调用的异常处理
            try:
                ai_response = self.ai_adapter.get_completion(
                    messages=messages,
                    model=ai_config.get("model", "gpt-3.5-turbo-16k-0613"),
                    max_tokens=ai_config.get("max_tokens", 200),
                    temperature=ai_config.get("temperature", 0.3)
                )
            except Exception as ai_error:
                logger.error_status(f"AI服务调用异常: {ai_error}")
                return {"success": False, "error": f"AI服务调用失败: {str(ai_error)}"}
            
            if not ai_response:
                logger.error_status("AI服务调用失败")
                return {"success": False, "error": "AI服务调用失败"}
            
            # 解析AI响应
            try:
                # 处理不同格式的AI响应
                if isinstance(ai_response, dict):
                    if 'choices' in ai_response and ai_response['choices']:
                        # 标准OpenAI格式
                        content = ai_response['choices'][0]['message']['content']
                    elif 'content' in ai_response:
                        # 简化格式
                        content = ai_response['content']
                    else:
                        # 直接是内容
                        content = str(ai_response)
                else:
                    content = str(ai_response)
                
                # 提取JSON内容
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                else:
                    result = json.loads(content)
                
                # 验证结果
                if "task_content" not in result or "reminder_time" not in result:
                    logger.error_status("AI响应格式不正确")
                    return {"success": False, "error": "AI响应格式不正确"}
                
                # 验证时间格式
                try:
                    datetime.strptime(result["reminder_time"], "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    logger.error_status(f"时间格式不正确: {result['reminder_time']}")
                    return {"success": False, "error": "时间格式不正确"}
                
                logger.success(f"AI解析成功: {result['task_content']} @ {result['reminder_time']}")
                return {
                    "success": True,
                    "task_content": result["task_content"],
                    "reminder_time": result["reminder_time"]
                }
                
            except json.JSONDecodeError as e:
                logger.error_status(f"解析AI响应JSON失败: {e}")
                logger.error_status(f"AI响应内容: {ai_response}")
                return {"success": False, "error": "解析AI响应失败"}
                
        except Exception as e:
            logger.error_status(f"AI解析提醒信息失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _create_reminder_task(self, user_id: str, original_message: str, 
                            task_content: str, reminder_time: str) -> Dict[str, Any]:
        """
        创建提醒任务
        
        Args:
            user_id: 用户ID
            original_message: 原始消息
            task_content: 任务内容
            reminder_time: 提醒时间
            
        Returns:
            创建结果
        """
        try:
            # 🔥 老王修复：检查MySQL连接器是否可用
            if not self.mysql_connector:
                logger.warning_status("MySQL连接器不可用，无法创建提醒任务")
                return {
                    "success": False,
                    "message": "数据库服务不可用，无法保存提醒任务"
                }
            
            # 生成唯一的任务ID
            import uuid
            task_id = str(uuid.uuid4())[:8]  # 使用前8位UUID作为任务ID
            
            # 准备插入数据
            task_data = {
                "task_id": task_id,
                "user_id": user_id,
                "original_message": original_message,
                "task_content": task_content,
                "reminder_time": reminder_time,
                "status": "active"
            }
            
            # 🔥 老王修复：增加数据库操作的异常处理
            try:
                # 插入数据库
                success, inserted_id, error = self.mysql_connector.insert("reminder_tasks", task_data)
            except Exception as db_error:
                logger.error_status(f"数据库插入操作异常: {db_error}")
                return {
                    "success": False,
                    "message": f"数据库操作异常: {str(db_error)}"
                }
            
            if success:
                logger.success(f"提醒任务创建成功，任务ID: {task_id}")
                
                # 🔥 通过WeChat统一推送服务推送任务创建通知（替代WebSocket）
                try:
                    # 这里保留原有的推送逻辑，但增加异常处理
                    # 获取WeChat推送服务
                    from utilities.singleton_manager import get_silent
                    wechat_push_service = get_silent('wechat_push_service')
                    
                    if wechat_push_service:
                        # 构建推送内容
                        push_content = f"✅ 提醒任务创建成功\n📝 内容：{task_content}\n⏰ 时间：{reminder_time}\n🆔 任务ID：{task_id}"
                        
                        # 构建元数据
                        metadata = {
                            "task_id": task_id,
                            "task_content": task_content,
                            "reminder_time": reminder_time,
                            "original_message": original_message,
                            "created_at": datetime.now().isoformat()
                        }
                        
                        # 检查是否在API线程中
                        import threading
                        current_thread = threading.current_thread()
                        is_api_thread = "API" in current_thread.name or "Thread" in current_thread.name
                        
                        if is_api_thread:
                            # API线程中，使用线程池处理异步推送
                            logger.debug("🔧 API线程中，使用线程池处理WeChat推送")
                            
                            def run_push_in_thread():
                                """在独立线程中运行推送"""
                                import asyncio
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    new_loop.run_until_complete(
                                        wechat_push_service.push_message(
                                            message_type='reminder_notification',
                                            content=push_content,
                                            target_user_id=user_id,
                                            message_level='user',
                                            priority='high',
                                            metadata=metadata
                                        )
                                    )
                                    logger.success(f"✅ 任务创建通知已推送到WeChat: {user_id}")
                                except Exception as push_e:
                                    logger.error(f"WeChat推送执行失败: {push_e}")
                                finally:
                                    new_loop.close()
                            
                            import concurrent.futures
                            if not hasattr(self, '_push_executor'):
                                self._push_executor = concurrent.futures.ThreadPoolExecutor(
                                    max_workers=2, thread_name_prefix="WeChatPush"
                                )
                            
                            # 提交推送任务
                            self._push_executor.submit(run_push_in_thread)
                            logger.debug("WeChat推送任务已提交到线程池")
                            
                        else:
                            # 非API线程，检查事件循环状态
                            try:
                                import asyncio
                                current_loop = asyncio.get_running_loop()
                                # 在现有事件循环中创建任务
                                current_loop.create_task(
                                    wechat_push_service.push_message(
                                        message_type='reminder_notification',
                                        content=push_content,
                                        target_user_id=user_id,
                                        message_level='user',
                                        priority='high',
                                        metadata=metadata
                                    )
                                )
                                logger.success(f"✅ 任务创建通知已推送到WeChat: {user_id}")
                            except RuntimeError:
                                # 没有运行中的事件循环，跳过推送
                                logger.debug("没有运行中的事件循环，跳过WeChat推送")
                        
                except Exception as e:
                    logger.error(f"WeChat推送失败: {e}")
                    # 🔥 P0级别修复：避免协程未等待的警告
                    import traceback
                    logger.debug(f"WeChat推送异常详情: {traceback.format_exc()}")
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "message": "任务创建成功"
                }
            else:
                logger.error_status(f"插入提醒任务失败: {error}")
                return {
                    "success": False,
                    "message": f"数据库插入失败: {error}"
                }
                
        except Exception as e:
            logger.error_status(f"创建提醒任务失败: {str(e)}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            return {
                "success": False,
                "message": f"创建任务失败: {str(e)}"
            }
    
    def get_active_tasks(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取用户的活跃提醒任务
        
        Args:
            user_id: 用户ID
            
        Returns:
            任务列表
        """
        try:
            query = """
            SELECT task_id, task_content, reminder_time
            FROM reminder_tasks 
            WHERE user_id = %s AND status = 'active'
            ORDER BY reminder_time ASC
            """
            
            success, results, error = self.mysql_connector.query(query, (user_id,))
            
            if success and results:
                return results
            elif not success:
                logger.error_status(f"查询活跃任务失败: {error}")
            
            return []
            
        except Exception as e:
            logger.error_status(f"获取活跃任务失败: {str(e)}")
            return []
    
    def get_due_tasks(self) -> List[Dict[str, Any]]:
        """
        获取到期的提醒任务
        
        Returns:
            到期任务列表
        """
        try:
            current_time = datetime.now()
            
            # 🔥 P0级别修复：使用SELECT FOR UPDATE防止并发重复处理
            # 添加LIMIT限制，避免一次处理过多任务
            query = """
            SELECT task_id, user_id, original_message, task_content, reminder_time
            FROM reminder_tasks 
            WHERE status = 'active' AND reminder_time <= %s
            ORDER BY reminder_time ASC
            LIMIT 10
            FOR UPDATE
            """
            
            success, results, error = self.mysql_connector.query(query, (current_time,))
            
            if success and results:
                # 🔥 P0级别修复：立即将获取到的任务标记为processing状态，防止重复处理
                task_ids = [task['task_id'] for task in results]
                if task_ids:
                    placeholders = ','.join(['%s'] * len(task_ids))
                    update_query = f"""
                    UPDATE reminder_tasks 
                    SET status = 'processing'
                    WHERE task_id IN ({placeholders}) AND status = 'active'
                    """
                    
                    update_success, affected_rows, update_error = self.mysql_connector.execute_update(
                        update_query, task_ids
                    )
                    
                    if update_success:
                        logger.info(f"🔒 已锁定 {affected_rows} 个任务防止重复处理")
                        return results
                    else:
                        logger.error_status(f"锁定任务失败: {update_error}")
                        return []
                else:
                    return results
            elif not success:
                logger.error_status(f"查询到期任务失败: {error}")
            
            return []
            
        except Exception as e:
            logger.error_status(f"获取到期任务失败: {str(e)}")
            return []
    
    def mark_task_completed(self, task_id: str) -> bool:
        """
        标记任务为完成状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        try:
            # 🔥 P0级别修复：从processing状态转为completed，避免重复处理
            update_data = {"status": "completed"}
            condition = "task_id = %s AND status IN ('processing', 'active')"
            
            success, affected_rows, error = self.mysql_connector.update(
                "reminder_tasks", update_data, condition, (task_id,)
            )
            
            if success and affected_rows > 0:
                logger.success(f"任务 {task_id} 标记为完成")
                return True
            else:
                error_msg = error if error else "未知错误或任务不存在"
                logger.error_status(f"标记任务完成失败: {error_msg}")
                return False
                
        except Exception as e:
            logger.error_status(f"标记任务完成失败: {str(e)}")
            return False
    
    def cancel_task(self, task_id: str, user_id: str) -> bool:
        """
        取消提醒任务
        
        Args:
            task_id: 任务ID
            user_id: 用户ID
            
        Returns:
            是否成功
        """
        try:
            update_data = {"status": "cancelled"}
            condition = "task_id = %s AND user_id = %s"
            
            success, affected_rows, error = self.mysql_connector.update(
                "reminder_tasks", update_data, condition, (task_id, user_id)
            )
            
            if success and affected_rows > 0:
                logger.success(f"任务 {task_id} 已取消")
                return True
            else:
                logger.error_status(f"取消任务失败: {error}")
                return False
                
        except Exception as e:
            logger.error_status(f"取消任务失败: {str(e)}")
            return False

def get_instance() -> AssistantReminderSkill:
    """获取助理提醒技能实例"""
    return AssistantReminderSkill(
        skill_id="assistant_reminder_skill",
        name="智能助理提醒服务",
        description="智能助理提醒服务，解析用户提醒需求并管理待办事项",
        version="1.0.0"
    ) 