#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Aipy技能模块 - 数字生命的"手和脚"

严格按照aipyapp HTTP API规范实现，为数字生命提供强大的Python代码执行能力。
执行结果通过数据流转到chat skill，由统一的数字生命形象对外提供服务。

核心特性:
- 严格遵循aipyapp HTTP API规范 (http://127.0.0.1:8848/docs)
- 与chat skill无缝数据流转
- 文件生成路径自动处理
- 完整的错误处理和监控

作者: 隔壁老王 (暴躁的代码架构师)
版本: 2.0.0
创建日期: 2025-09-12
更新日期: 2025-09-12 (重构为aipy技能)
"""

import json
import time
import uuid
import requests
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin
from pathlib import Path

import logging
logger = logging.getLogger(__name__)
from core.skill_manager import Skill


class AipySkill(Skill):
    """
    Aipy技能 - 数字生命的Python执行能力
    
    严格按照aipyapp HTTP API规范实现：
    - POST /tasks - 提交任务
    - GET /tasks/{task_id} - 获取任务状态
    - GET /tasks/{task_id}/result - 获取任务结果
    
    执行结果自动流转到chat skill进行统一对外服务。
    """
    
    def __init__(self):
        """初始化Aipy技能"""
        logger.info("🚀 初始化Aipy技能...")
        
        # 调用父类初始化
        super().__init__(
            skill_id="aipy_skill",
            name="Aipy Python执行技能",
            description="集成aipyapp提供Python代码执行能力，结果流转到chat skill",
            version="2.0.0"
        )
        
        # 加载配置
        self.config = self._load_config()
        self.api_base_url = self.config.get("api_config", {}).get("api_base_url", "http://127.0.0.1:8848")
        self.timeout = self.config.get("api_config", {}).get("timeout", 300)
        self.max_retries = self.config.get("api_config", {}).get("max_retries", 3)
        
        logger.info(f"✅ Aipy技能初始化完成，API地址: {self.api_base_url}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载技能配置"""
        try:
            config_path = Path(__file__).parent.parent.parent / "config" / "skills" / "aipy_skill.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"⚠️ 配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return {}
    
    def can_handle(self, input_text: str, intent_data: Optional[Dict] = None) -> bool:
        """判断是否能处理该输入"""
        if not input_text:
            return False
        
        # 检查关键词
        keywords = self.config.get("intent_mapping", {}).get("keywords", [])
        text_lower = input_text.lower()
        
        for keyword in keywords:
            if keyword in text_lower:
                logger.info(f"🎯 Aipy技能匹配关键词: {keyword}")
                return True
        
        # 检查意图类型
        if intent_data:
            intent_type = intent_data.get("type", "")
            supported_types = self.config.get("intent_mapping", {}).get("intent_types", [])
            if intent_type in supported_types:
                logger.info(f"🎯 Aipy技能匹配意图类型: {intent_type}")
                return True
        
        return False
    
    def execute(self, input_text: str, user_id: str, session_id: str, 
                intent_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        执行Aipy任务
        
        严格按照aipyapp HTTP API规范：
        1. POST /tasks 提交任务
        2. 轮询 GET /tasks/{task_id} 获取状态
        3. GET /tasks/{task_id}/result 获取结果
        4. 处理文件生成情况
        5. 流转结果到chat skill
        """
        logger.info(f"🚀 开始执行Aipy任务，用户: {user_id}")
        
        try:
            # 1. 提交任务到aipyapp
            task_result = self._submit_task(input_text, user_id, session_id, intent_data)
            
            if not task_result.get("success"):
                return self._create_error_response(task_result.get("error", "任务提交失败"))
            
            task_id = task_result.get("task_id")
            logger.info(f"✅ 任务提交成功，任务ID: {task_id}")
            
            # 2. 轮询任务状态直到完成
            execution_result = self._poll_task_completion(task_id)
            
            if not execution_result.get("success"):
                return self._create_error_response(execution_result.get("error", "任务执行失败"))
            
            # 3. 处理执行结果
            processed_result = self._process_execution_result(execution_result.get("data", {}))
            
            # 4. 准备流转到chat skill的数据
            chat_data = self._prepare_chat_data(processed_result, user_id, session_id)
            
            logger.info("🎉 Aipy任务执行完成，准备流转到chat skill")
            
            return {
                "success": True,
                "skill_id": self.skill_id,
                "execution_time": processed_result.get("execution_time", 0),
                "data": chat_data,
                "next_skill": "chat_skill",  # 指定下一个技能
                "flow_type": "aipy_to_chat"   # 标识数据流转类型
            }
            
        except Exception as e:
            logger.error(f"❌ Aipy技能执行异常: {e}")
            return self._create_error_response(f"技能执行异常: {str(e)}")
    
    def _submit_task(self, instruction: str, user_id: str, session_id: str, 
                     intent_data: Optional[Dict] = None) -> Dict[str, Any]:
        """提交任务到aipyapp API"""
        try:
            # 构建符合API规范的请求
            api_url = urljoin(self.api_base_url, "/tasks")
            
            # 按照TaskRequest schema构建payload
            payload = {
                "instruction": instruction,
                "metadata": {
                    "user_id": user_id,
                    "session_id": session_id,
                    "skill_id": self.skill_id,
                    "timestamp": time.time(),
                    "intent_data": intent_data or {}
                }
            }
            
            logger.info(f"📤 提交任务到API: {api_url}")
            
            response = requests.post(
                api_url,
                json=payload,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                # 按照TaskResponse schema解析响应
                return {
                    "success": True,
                    "task_id": result.get("task_id"),
                    "status": result.get("status"),
                    "message": result.get("message")
                }
            else:
                logger.error(f"❌ API请求失败: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"API请求失败: {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"❌ 提交任务异常: {e}")
            return {"success": False, "error": str(e)}
    
    def _poll_task_completion(self, task_id: str) -> Dict[str, Any]:
        """轮询任务完成状态"""
        max_polls = 120  # 最多轮询120次 (10分钟)
        poll_interval = 5  # 每5秒轮询一次
        
        logger.info(f"⏳ 开始轮询任务状态: {task_id}")
        
        for i in range(max_polls):
            try:
                # 按照API规范查询任务状态
                status_url = urljoin(self.api_base_url, f"/tasks/{task_id}")
                response = requests.get(status_url, timeout=30)
                
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data.get("status")
                    
                    logger.debug(f"📊 轮询 {i+1}/{max_polls}: 状态 = {status}")
                    
                    if status == "completed":
                        # 获取任务结果
                        return self._get_task_result(task_id)
                    elif status == "failed":
                        error = status_data.get("error", "任务执行失败")
                        return {"success": False, "error": error}
                    elif status in ["pending", "running"]:
                        time.sleep(poll_interval)
                        continue
                    else:
                        return {"success": False, "error": f"未知任务状态: {status}"}
                else:
                    logger.warning(f"⚠️ 状态查询失败: {response.status_code}")
                    time.sleep(poll_interval)
                    
            except Exception as e:
                logger.warning(f"⚠️ 轮询异常: {e}")
                time.sleep(poll_interval)
        
        return {"success": False, "error": "任务执行超时"}
    
    def _get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务执行结果"""
        try:
            # 按照API规范获取任务结果
            result_url = urljoin(self.api_base_url, f"/tasks/{task_id}/result")
            response = requests.get(result_url, timeout=30)
            
            if response.status_code == 200:
                result_data = response.json()
                return {
                    "success": True,
                    "data": result_data
                }
            else:
                return {
                    "success": False,
                    "error": f"获取结果失败: {response.status_code}"
                }
                
        except Exception as e:
            return {"success": False, "error": f"获取结果异常: {str(e)}"}
    
    def _process_execution_result(self, result_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理执行结果，提取关键信息"""
        processed = {
            "task_id": result_data.get("task_id"),
            "instruction": result_data.get("instruction", ""),
            "status": result_data.get("status"),
            "created_at": result_data.get("created_at"),
            "completed_at": result_data.get("completed_at"),
            "execution_time": 0,
            "output_text": "",
            "generated_files": [],
            "errors": []
        }
        
        # 计算执行时间
        if result_data.get("created_at") and result_data.get("completed_at"):
            try:
                from datetime import datetime
                created = datetime.fromisoformat(result_data["created_at"].replace('Z', '+00:00'))
                completed = datetime.fromisoformat(result_data["completed_at"].replace('Z', '+00:00'))
                processed["execution_time"] = (completed - created).total_seconds()
            except:
                pass
        
        # 处理输出结果
        output = result_data.get("output", {})
        if output:
            # 提取文本输出
            processed["output_text"] = self._extract_output_text(output)
            
            # 提取生成的文件路径
            processed["generated_files"] = self._extract_generated_files(output)
            
            # 提取错误信息
            processed["errors"] = self._extract_errors(output)
        
        return processed

    def _extract_output_text(self, output: Dict[str, Any]) -> str:
        """提取输出文本"""
        text_parts = []

        # 从results中提取stdout
        results = output.get("results", [])
        for result in results:
            if isinstance(result, dict):
                result_data = result.get("result", {})
                if isinstance(result_data, dict):
                    stdout = result_data.get("stdout", "")
                    if stdout:
                        text_parts.append(stdout)

        return "\n".join(text_parts)

    def _extract_generated_files(self, output: Dict[str, Any]) -> List[str]:
        """提取生成的文件路径"""
        files = []

        # 从输出文本中查找文件路径模式
        text = self._extract_output_text(output)

        # 常见的文件路径模式
        file_patterns = [
            r'保存到[：:]?\s*([^\s\n]+\.(?:png|jpg|jpeg|gif|pdf|csv|xlsx|json|html))',
            r'生成文件[：:]?\s*([^\s\n]+\.(?:png|jpg|jpeg|gif|pdf|csv|xlsx|json|html))',
            r'文件已保存[：:]?\s*([^\s\n]+\.(?:png|jpg|jpeg|gif|pdf|csv|xlsx|json|html))',
            r'输出文件[：:]?\s*([^\s\n]+\.(?:png|jpg|jpeg|gif|pdf|csv|xlsx|json|html))',
            r'([^\s\n]+\.(?:png|jpg|jpeg|gif|pdf|csv|xlsx|json|html))\s*已生成',
        ]

        for pattern in file_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            files.extend(matches)

        # 去重并返回
        return list(set(files))

    def _extract_errors(self, output: Dict[str, Any]) -> List[str]:
        """提取错误信息"""
        errors = []

        # 从results中提取stderr
        results = output.get("results", [])
        for result in results:
            if isinstance(result, dict):
                result_data = result.get("result", {})
                if isinstance(result_data, dict):
                    stderr = result_data.get("stderr", "")
                    if stderr:
                        errors.append(stderr)

        # 从errors字段提取
        output_errors = output.get("errors", [])
        if output_errors:
            errors.extend(output_errors)

        return errors

    def _prepare_chat_data(self, processed_result: Dict[str, Any],
                          user_id: str, session_id: str) -> Dict[str, Any]:
        """准备流转到chat skill的数据"""

        # 构建给用户的响应文本
        response_text = self._build_response_text(processed_result)

        # 准备chat skill需要的数据格式
        chat_data = {
            "response_text": response_text,
            "aipy_result": {
                "task_id": processed_result.get("task_id"),
                "execution_time": processed_result.get("execution_time", 0),
                "generated_files": processed_result.get("generated_files", []),
                "has_files": len(processed_result.get("generated_files", [])) > 0,
                "errors": processed_result.get("errors", []),
                "has_errors": len(processed_result.get("errors", [])) > 0
            },
            "metadata": {
                "source_skill": "aipy_skill",
                "user_id": user_id,
                "session_id": session_id,
                "timestamp": time.time()
            }
        }

        return chat_data

    def _build_response_text(self, processed_result: Dict[str, Any]) -> str:
        """构建给用户的响应文本"""
        parts = []

        # 添加执行结果
        output_text = processed_result.get("output_text", "")
        if output_text:
            parts.append("🎉 Python代码执行完成！")
            parts.append("")
            parts.append("📊 执行结果：")
            parts.append(output_text)

        # 添加生成的文件信息
        generated_files = processed_result.get("generated_files", [])
        if generated_files:
            parts.append("")
            parts.append("📁 生成的文件：")
            for file_path in generated_files:
                parts.append(f"  • {file_path}")

        # 添加执行时间
        execution_time = processed_result.get("execution_time", 0)
        if execution_time > 0:
            parts.append("")
            parts.append(f"⏱️ 执行时间：{execution_time:.2f}秒")

        # 添加错误信息（如果有）
        errors = processed_result.get("errors", [])
        if errors:
            parts.append("")
            parts.append("⚠️ 执行过程中的警告：")
            for error in errors[:3]:  # 最多显示3个错误
                parts.append(f"  • {error}")

        return "\n".join(parts) if parts else "✅ 任务执行完成"

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "success": False,
            "skill_id": self.skill_id,
            "error": error_message,
            "data": {
                "response_text": f"❌ Aipy执行失败：{error_message}",
                "aipy_result": {
                    "has_errors": True,
                    "errors": [error_message]
                }
            },
            "next_skill": "chat_skill",
            "flow_type": "aipy_to_chat"
        }
