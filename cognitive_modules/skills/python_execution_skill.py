#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Python执行技能 - 数字生命的"手和脚"
集成aipyapp项目，提供强大的Python代码执行和深度调研能力

作者: 隔壁老王 (暴躁的代码架构师)
版本: 1.0.0
创建时间: 2025-09-12

功能特性:
- 通过HTTP API调用远程aipyapp服务
- 支持自然语言转Python代码执行
- 专门优化深度调研工作流
- 完整的安全沙箱机制
- 异步任务处理和状态监控
"""

import json
import time
import uuid
import asyncio
import requests
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin

from utilities.logger import get_logger
logger = get_logger(__name__)
from core.skill_manager import Skill
from utilities.singleton_manager import get_singleton
from cognitive_modules.skills.security.python_execution_sandbox import create_sandbox, SecurityConfig


class PythonExecutionSkill(Skill):
    """
    Python执行技能 - 让数字生命拥有"手和脚"
    
    这个技能通过集成aipyapp项目，让AI能够：
    1. 执行Python代码进行数据分析
    2. 自动化网络爬取和数据收集
    3. 生成可视化图表和报告
    4. 处理各种文件格式
    5. 进行深度调研和分析
    
    艹！这就是老王想要的真正的AI执行能力！
    """
    
    def __init__(self):
        """初始化Python执行技能"""
        logger.debug("🔥 老王正在初始化Python执行技能...")
        
        super().__init__(
            skill_id="python_execution_skill",
            name="Python代码执行技能",
            description="集成aipyapp，提供强大的Python代码执行和深度调研能力",
            version="1.0.0"
        )
        
        # 技能能力定义
        self.capabilities = [
            "python_execution",      # Python代码执行
            "data_analysis",         # 数据分析
            "web_scraping",          # 网络爬取
            "file_processing",       # 文件处理
            "visualization",         # 数据可视化
            "deep_research",         # 深度调研
            "automation"             # 自动化任务
        ]
        
        # 延迟初始化依赖
        self.event_bus = None
        self.life_context = None
        
        # aipyapp服务配置
        self.config = self._load_config()
        self.api_base_url = self.config.get("api_base_url", "http://localhost:8848")
        self.timeout = self.config.get("timeout", 300)  # 5分钟超时
        self.max_retries = self.config.get("max_retries", 3)
        
        # 安全配置
        self.security_config = self.config.get("security", {})
        self.allowed_modules = self.security_config.get("allowed_modules", [
            "pandas", "numpy", "matplotlib", "seaborn", "requests", 
            "beautifulsoup4", "json", "csv", "sqlite3", "openpyxl"
        ])
        self.blocked_operations = self.security_config.get("blocked_operations", [
            "os.system", "subprocess", "exec", "eval", "__import__"
        ])
        
        # 任务状态跟踪
        self.active_tasks = {}

        # 初始化安全沙箱
        sandbox_config = self.security_config.copy()
        sandbox_config.update({
            "max_execution_time": self.timeout,
            "allowed_modules": self.allowed_modules,
            "blocked_operations": self.blocked_operations
        })
        self.sandbox = create_sandbox(sandbox_config)

        logger.success(f"🚀 Python执行技能初始化完成: {self.name} v{self.version}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载技能配置"""
        try:
            config_path = "config/skills/python_execution_skill.json"
            # 这里应该从配置文件加载，暂时使用默认配置
            return {
                "api_base_url": "http://localhost:8848",
                "timeout": 300,
                "max_retries": 3,
                "security": {
                    "allowed_modules": [
                        "pandas", "numpy", "matplotlib", "seaborn", "requests",
                        "beautifulsoup4", "json", "csv", "sqlite3", "openpyxl",
                        "plotly", "scipy", "sklearn", "yfinance", "feedparser"
                    ],
                    "blocked_operations": [
                        "os.system", "subprocess.run", "exec", "eval", 
                        "__import__", "open('/etc", "rm -rf", "sudo"
                    ]
                }
            }
        except Exception as e:
            logger.warning(f"加载配置失败，使用默认配置: {e}")
            return {}
    
    def can_handle(self, context: Dict[str, Any]) -> bool:
        """
        判断是否能处理当前请求
        
        Args:
            context: 请求上下文
            
        Returns:
            是否能处理
        """
        text = context.get("text", "").lower()
        
        # 深度调研相关关键词
        research_keywords = [
            "调研", "研究", "分析", "数据", "爬取", "抓取", "统计",
            "可视化", "图表", "报告", "excel", "csv", "json",
            "python", "代码", "执行", "自动化", "批处理"
        ]
        
        # 检查是否包含相关关键词
        for keyword in research_keywords:
            if keyword in text:
                return True
        
        # 检查意图类型
        intent_data = context.get("intent_data", {})
        intent_type = intent_data.get("type", "")
        
        if intent_type in ["data_analysis", "research", "automation", "python_execution"]:
            return True
        
        return False
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """
        执行Python代码任务
        
        Args:
            **kwargs: 包含以下参数
                input_text: 用户输入的任务描述
                user_id: 用户ID
                session_id: 会话ID
                intent_data: 意图数据
                
        Returns:
            执行结果
        """
        execution_id = f"py_exec_{uuid.uuid4().hex[:8]}"
        start_time = time.time()
        
        try:
            # 提取参数
            input_text = kwargs.get("input_text", "")
            user_id = kwargs.get("user_id", "unknown")
            session_id = kwargs.get("session_id", "default")
            intent_data = kwargs.get("intent_data", {})
            
            logger.info(f"🐍 [{execution_id}] 开始执行Python任务: {input_text[:100]}...")
            
            # 确保依赖项
            self._ensure_dependencies()
            
            # 安全检查 - 使用沙箱进行全面安全验证
            is_safe, security_error = self.sandbox.validate_code_safety(input_text)
            if not is_safe:
                logger.warning(f"🚫 [{execution_id}] 安全检查失败: {security_error}")
                return self._create_error_result(f"安全检查失败: {security_error}")
            
            # 调用aipyapp API执行任务
            result = self._execute_python_task(input_text, execution_id)
            
            if result.get("success"):
                execution_time = time.time() - start_time
                logger.success(f"🎉 [{execution_id}] Python任务执行成功，耗时: {execution_time:.2f}s")
                
                return self._create_success_result({
                    "execution_id": execution_id,
                    "task_description": input_text,
                    "execution_time": execution_time,
                    "output": result.get("output"),
                    "files_generated": result.get("files_generated", []),
                    "summary": result.get("summary", "任务执行完成")
                })
            else:
                error_msg = result.get("error", "未知错误")
                logger.error(f"❌ [{execution_id}] Python任务执行失败: {error_msg}")
                return self._create_error_result(f"执行失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"💥 [{execution_id}] Python执行技能异常: {e}")
            return self._create_error_result(f"技能执行异常: {str(e)}")
    
    def _security_check(self, code_or_instruction: str) -> bool:
        """
        安全检查：防止恶意代码执行
        
        Args:
            code_or_instruction: 代码或指令
            
        Returns:
            是否通过安全检查
        """
        text_lower = code_or_instruction.lower()
        
        # 检查被禁止的操作
        for blocked_op in self.blocked_operations:
            if blocked_op.lower() in text_lower:
                logger.warning(f"🚫 安全检查失败：包含被禁止的操作 '{blocked_op}'")
                return False
        
        # 检查危险的文件路径
        dangerous_paths = ["/etc/", "/root/", "/home/", "~/.ssh/", "/var/"]
        for path in dangerous_paths:
            if path in text_lower:
                logger.warning(f"🚫 安全检查失败：包含危险路径 '{path}'")
                return False
        
        return True
    
    def _execute_python_task(self, instruction: str, execution_id: str) -> Dict[str, Any]:
        """
        通过HTTP API执行Python任务
        
        Args:
            instruction: 任务指令
            execution_id: 执行ID
            
        Returns:
            执行结果
        """
        try:
            # 构建API请求
            api_url = urljoin(self.api_base_url, "/api/v1/tasks")
            
            payload = {
                "instruction": instruction,
                "metadata": {
                    "execution_id": execution_id,
                    "source": "digital_life",
                    "timestamp": time.time()
                }
            }
            
            # 发送任务请求
            response = requests.post(
                api_url,
                json=payload,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get("task_id")
                
                # 轮询任务状态直到完成
                return self._poll_task_status(task_id, execution_id)
            else:
                return {
                    "success": False,
                    "error": f"API请求失败: {response.status_code} - {response.text}"
                }
                
        except requests.exceptions.Timeout:
            return {"success": False, "error": "请求超时"}
        except requests.exceptions.ConnectionError:
            return {"success": False, "error": "无法连接到aipyapp服务"}
        except Exception as e:
            return {"success": False, "error": f"API调用异常: {str(e)}"}
    
    def _poll_task_status(self, task_id: str, execution_id: str) -> Dict[str, Any]:
        """
        轮询任务执行状态
        
        Args:
            task_id: 任务ID
            execution_id: 执行ID
            
        Returns:
            最终执行结果
        """
        max_polls = 60  # 最多轮询60次
        poll_interval = 5  # 每5秒轮询一次
        
        for i in range(max_polls):
            try:
                # 查询任务状态
                status_url = urljoin(self.api_base_url, f"/api/v1/tasks/{task_id}/status")
                response = requests.get(status_url, timeout=30)
                
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data.get("status")
                    
                    if status == "completed":
                        # 获取任务结果
                        result_url = urljoin(self.api_base_url, f"/api/v1/tasks/{task_id}/result")
                        result_response = requests.get(result_url, timeout=30)
                        
                        if result_response.status_code == 200:
                            result_data = result_response.json()
                            return {
                                "success": True,
                                "output": result_data.get("output"),
                                "files_generated": result_data.get("files_generated", []),
                                "summary": "任务执行完成"
                            }
                    elif status == "failed":
                        error = status_data.get("error", "任务执行失败")
                        return {"success": False, "error": error}
                    elif status in ["pending", "running"]:
                        # 继续等待
                        logger.debug(f"⏳ [{execution_id}] 任务状态: {status}, 继续等待...")
                        time.sleep(poll_interval)
                        continue
                    else:
                        return {"success": False, "error": f"未知任务状态: {status}"}
                else:
                    return {"success": False, "error": f"状态查询失败: {response.status_code}"}
                    
            except Exception as e:
                logger.warning(f"⚠️ [{execution_id}] 状态轮询异常: {e}")
                time.sleep(poll_interval)
        
        return {"success": False, "error": "任务执行超时"}
    
    def _ensure_dependencies(self):
        """确保依赖项已初始化"""
        if not self.event_bus:
            self.event_bus = get_singleton("event_bus")
        
        if not self.life_context:
            self.life_context = get_singleton("life_context")
    
    def _create_success_result(self, data: Any) -> Dict[str, Any]:
        """创建成功结果"""
        return {
            "success": True,
            "skill_id": self.skill_id,
            "skill_name": self.name,
            "data_type": "python_execution_result",
            "data": data,
            "metadata": {
                "timestamp": time.time(),
                "version": self.version,
                "capabilities": self.capabilities
            }
        }
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            "success": False,
            "skill_id": self.skill_id,
            "skill_name": self.name,
            "error": error_message,
            "metadata": {
                "timestamp": time.time(),
                "version": self.version
            }
        }


# 技能实例化
def create_skill():
    """创建技能实例"""
    return PythonExecutionSkill()


if __name__ == "__main__":
    # 测试代码
    skill = PythonExecutionSkill()
    
    test_context = {
        "text": "帮我分析一下最近的股票数据，生成可视化图表",
        "intent_data": {"type": "data_analysis"}
    }
    
    if skill.can_handle(test_context):
        print("✅ 技能可以处理该请求")
        
        result = skill.execute(
            input_text="分析苹果公司最近30天的股价走势，生成图表",
            user_id="test_user",
            session_id="test_session"
        )
        
        print(f"执行结果: {result}")
    else:
        print("❌ 技能无法处理该请求")
