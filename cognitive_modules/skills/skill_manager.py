#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
技能管理器模块 - Skill Manager

负责管理和调用各种技能，如搜索、绘画等。
作为意图识别和实际执行之间的桥梁。
"""

import os
import sys
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import importlib
import traceback
from typing import Dict, List, Any, Callable, Optional
import time
import uuid

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 导入工具和依赖
from utilities.unified_logger import get_unified_logger
from core.enhanced_event_bus import get_instance as get_event_bus, EventPriority
from core.life_context import get_instance as get_life_context

# 初始化日志
logger = get_unified_logger("skill_manager")

class SkillManager:
    """技能管理器类，负责管理和协调各种技能模块"""
    
    def __init__(self):
        """初始化技能管理器"""
        # 避免重复初始化
        if hasattr(self, 'initialized') and self.initialized:
            logger.debug("技能管理器已初始化，跳过重复初始化")
            return
            
        # 设置初始化标志
        self.initialized = False
        
        # 配置日志记录器（使用已全局定义的logger）
        self.logger = logger
        
        logger.success("初始化技能管理器...")
        
        # 技能注册表
        self.skills = {}
        
        # 意图到技能的映射
        self.intent_to_skill_map = {}
        
        # 事件总线
        try:
            from core.enhanced_event_bus import get_instance as get_event_bus
            self.event_bus = get_event_bus()
        except ImportError:
            try:
                from core.event_bus import get_instance as get_event_bus
                self.event_bus = get_event_bus()
            except ImportError:
                self.logger.warning_status("找不到事件总线模块，部分功能可能不可用")
                self.event_bus = None
        
        # 加载配置
        self.config = {}
        try:
            from utilities.config_loader import load_config
            self.config = load_config("skills/skill_manager.json")
            self.logger.debug(f"已加载技能管理器配置: {os.path.abspath('config/skills/skill_manager.json')}")
        except Exception as e:
            self.logger.warning_status(f"加载技能管理器配置失败: {e}")
        
        # 注册事件处理器
        if self.event_bus:
            # 检查是否已订阅
            if hasattr(self.event_bus, 'get_subscribers'):
                handlers = self.event_bus.get_subscribers("skill.register")
                if not any(h.__self__ == self for h in handlers if hasattr(h, '__self__')):
                    self.event_bus.subscribe("skill.register", self.handle_skill_register)
                    self.event_bus.subscribe("skill.request", self.handle_skill_request)
                    self.event_bus.subscribe("intent.recognized", self.handle_intent_recognized)
                    self.logger.info("技能管理器已注册事件处理器")
            else:
                # 简单订阅（无法检查是否重复）
                self.event_bus.subscribe("skill.register", self.handle_skill_register)
                self.event_bus.subscribe("skill.request", self.handle_skill_request)
                self.event_bus.subscribe("intent.recognized", self.handle_intent_recognized)
                self.logger.info("技能管理器已注册事件处理器")
        
        # 加载意图到技能的映射
        self._load_intent_to_skill_map()
        
        # 加载优先技能
        self._load_priority_skills()
        
        # 🔥 新增：加载技能编排配置
        self.orchestration_config = self._load_orchestration_config()
        
        # 注册助理提醒技能
        self._register_assistant_reminder_skill()
        
        # 标记为已初始化
        self.initialized = True
        logger.success("技能管理器初始化完成")
    
    def shutdown(self):
        """
        关闭技能管理器，清理资源
        """
        try:
            logger.debug("开始关闭技能管理器...")
            
            # 清理已注册的技能
            if hasattr(self, 'skills'):
                for skill_name, skill_instance in self.skills.items():
                    try:
                        # 如果技能有关闭方法，调用它
                        if hasattr(skill_instance, 'shutdown'):
                            skill_instance.shutdown()
                        elif hasattr(skill_instance, 'on_unload'):
                            skill_instance.on_unload()
                    except Exception as e:
                        logger.warning_status(f"技能 {skill_name} 关闭失败: {e}")
                
                self.skills.clear()
            
            # 清理配置
            if hasattr(self, 'config'):
                self.config.clear()
            
            # 清理意图映射
            if hasattr(self, 'intent_to_skill_map'):
                self.intent_to_skill_map.clear()
            
            # 清理编排配置
            if hasattr(self, 'orchestration_config'):
                self.orchestration_config.clear()
            
            logger.success("✅ 技能管理器已成功关闭")
            
        except Exception as e:
            logger.error_status(f"❌ 技能管理器关闭失败: {e}")
    
    def process(self, context):
        """
        处理思维链路上下文，调用相应技能
        支持配置化技能编排和技能组合使用
        
        Args:
            context: 思维链路上下文对象或字典
            
        Returns:
            处理结果
        """
        logger.debug("执行技能处理步骤 - 支持配置化编排和技能组合")
        
        try:
            # 处理字典形式的context
            if isinstance(context, dict):
                # 🔥 P0级别修复：移除硬编码default_user
                user_id = context.get("user_id")
                if not user_id:
                    logger.error("❌ P0级别错误：技能管理器未获取到用户ID")
                    return {"skill_executed": False, "error": "用户ID缺失"}
                session_id = context.get("session_id", f"{user_id}_{int(time.time())}")
                input_text = context.get("text", "")
                intent_result = context.get("intent", {})
                logger.debug(f"字典形式context - user_id: {user_id}, input_text: '{input_text}'")
            else:
                # 🔥 P0级别修复：移除硬编码default_user
                # 获取用户ID和会话ID，用户ID必须从context获取
                user_id = None
                session_id = None
                
                if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
                    user_id = context.input_data.get("user_id")
                    session_id = context.session_id if hasattr(context, 'session_id') else None
                elif hasattr(context, 'get_shared_data'):
                    user_id = context.get_shared_data("user_id")
                    session_id = context.get_shared_data("session_id")
                elif hasattr(context, 'metadata') and isinstance(context.metadata, dict):
                    user_id = context.metadata.get("user_id")
                
                # 🔥 P0级别修复：用户ID验证
                if not user_id:
                    logger.error("❌ P0级别错误：未获取到用户ID，无法执行技能")
                    return {
                        "skill_executed": False,
                        "error": "用户ID缺失，无法执行技能"
                    }
                
                # 生成会话ID
                if not session_id:
                    session_id = f"{user_id}_{int(time.time())}"
                
                # 🔥 增强输入文本提取逻辑，支持多种来源
                input_text = ""
                
                # 方法1：从input_data获取
                if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
                    # 🔥 老王调试：详细记录input_data内容
                    logger.debug(f"🔍 context.input_data内容: {context.input_data}")

                    input_text = (
                        context.input_data.get("text", "") or
                        context.input_data.get("user_input", "") or
                        context.input_data.get("message", "") or
                        context.input_data.get("content", "")
                    )
                    logger.debug(f"从input_data提取输入文本: '{input_text}' (长度: {len(input_text) if input_text else 0})")

                    # 🔥 调试：如果为空，记录详细信息
                    if not input_text:
                        logger.warning(f"⚠️ 从input_data提取的输入文本为空！")
                        logger.debug(f"   - text: '{context.input_data.get('text', 'N/A')}'")
                        logger.debug(f"   - user_input: '{context.input_data.get('user_input', 'N/A')}'")
                        logger.debug(f"   - message: '{context.input_data.get('message', 'N/A')}'")
                        logger.debug(f"   - content: '{context.input_data.get('content', 'N/A')}'")
                else:
                    logger.warning(f"⚠️ context没有input_data属性或input_data不是字典")
                
                # 方法2：如果input_data没有获取到，从shared_data获取
                if not input_text and hasattr(context, 'get_shared_data'):
                    input_text = (
                        context.get_shared_data("input_text", "") or
                        context.get_shared_data("text", "") or
                        context.get_shared_data("user_input", "") or
                        context.get_shared_data("message", "")
                    )
                    logger.debug(f"从shared_data提取输入文本: '{input_text}'")
                
                # 方法3：如果都没有获取到，从metadata获取
                if not input_text and hasattr(context, 'metadata') and isinstance(context.metadata, dict):
                    input_text = (
                        context.metadata.get("text", "") or
                        context.metadata.get("user_input", "") or
                        context.metadata.get("message", "")
                    )
                    logger.debug(f"从metadata提取输入文本: '{input_text}'")
                
                # 🔥 P0级别修复：从intent_data中提取用户输入
                intent_result = {}
                if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
                    if "intent" in context.input_data:
                        intent_result = context.input_data["intent"]
                    elif "intent_data" in context.input_data:
                        intent_result = context.input_data["intent_data"]
                elif hasattr(context, 'get_shared_data'):
                    intent_result = context.get_shared_data("intent", {})
                
                # 🔥 P0级别修复：如果input_text为空，尝试从intent_data中获取
                if not input_text and intent_result and isinstance(intent_result, dict):
                    input_text = intent_result.get("content", "")
                    if input_text:
                        logger.debug(f"✅ 从intent_data.content提取到输入文本: '{input_text}'")
                
                # 🔥 最终检查：如果仍然为空，记录详细的调试信息
                if not input_text:
                    logger.warning_status("⚠️ 输入文本提取失败，进行详细调试")
                    if hasattr(context, 'input_data'):
                        logger.debug(f"context.input_data: {context.input_data}")
                    if hasattr(context, 'shared_data'):
                        logger.debug(f"context.shared_data: {context.shared_data}")
                    if hasattr(context, 'metadata'):
                        logger.debug(f"context.metadata: {context.metadata}")
                    logger.debug(f"intent_result: {intent_result}")
                    
                    # 🔥 尝试直接从context对象的属性获取
                    for attr_name in ['text', 'user_input', 'message', 'content']:
                        if hasattr(context, attr_name):
                            attr_value = getattr(context, attr_name, "")
                            if attr_value and isinstance(attr_value, str):
                                input_text = attr_value
                                logger.debug(f"从context.{attr_name}获取输入文本: '{input_text}'")
                                break
                
                # 获取意图结果
                intent_result = {}
                if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
                    if "intent" in context.input_data:
                        intent_result = context.input_data["intent"]
                    elif "intent_data" in context.input_data:
                        intent_result = context.input_data["intent_data"]
                elif hasattr(context, 'get_shared_data'):
                    intent_result = context.get_shared_data("intent", {})
            
            # 🔥 如果输入文本仍然为空，直接返回错误
            if not input_text or not input_text.strip():
                logger.error_status(f"❌ 技能处理失败：输入文本为空")
                return {
                    "skill_executed": False,
                    "error": "输入内容为空"
                }
            
            # 从input_data获取意图结果
            input_data = context.input_data if hasattr(context, 'input_data') else {}
            intent_result = input_data.get("intent", {})
            
            logger.debug(f"从input_data获取意图结果: {intent_result}")
            
            # 🔥 如果仍然找不到意图分析结果，使用默认的聊天意图
            if not intent_result or "type" not in intent_result:
                logger.debug(f"未找到明确意图，使用默认聊天意图处理用户输入: {input_text[:50]}...")
                intent_result = {
                    "type": "chat",
                    "content": input_text,
                    "confidence": 0.8,
                    "main_intent": "聊天"
                }
                
                # 🔥 重要：将默认意图设置到上下文共享数据中，避免重复识别
                if hasattr(context, 'set_shared_data'):
                    context.set_shared_data("intent", intent_result)
                    context.set_shared_data("intent_recognized", True)
                    logger.debug("已将默认意图设置到共享数据中")
            
            # 提取意图类型
            intent_type = intent_result.get("type", "chat")
            
            # 🔥 老王修复：尝试使用配置化技能编排，包含完整的metadata
            orchestration_context = {
                "text": input_text,
                "user_input": input_text,
                "user_id": user_id,
                "session_id": session_id,
                "intent": intent_result
            }

            # 🔥 调试：记录orchestration_context的构建
            logger.debug(f"🔧 构建orchestration_context:")
            logger.debug(f"   - text: '{input_text}' (长度: {len(input_text) if input_text else 0})")
            logger.debug(f"   - user_id: '{user_id}'")
            logger.debug(f"   - session_id: '{session_id}'")

            # 🔥 关键修复：传递完整的metadata，包括安全标识
            if hasattr(context, 'metadata') and isinstance(context.metadata, dict):
                orchestration_context["metadata"] = context.metadata.copy()
                logger.debug(f"🛡️ 传递metadata到技能编排: {list(context.metadata.keys())}")
            else:
                orchestration_context["metadata"] = {}
            
            # 尝试选择编排策略
            if hasattr(self, 'orchestration_config'):
                strategy = self._select_orchestration_strategy(orchestration_context)
                if strategy:
                    logger.debug(f"使用编排策略: {strategy['name']}")
                    orchestration_result = self._execute_orchestration_strategy(strategy, orchestration_context)
                    if orchestration_result.get("skill_executed"):
                        return orchestration_result
                    else:
                        logger.warning(f"编排策略执行失败: {orchestration_result.get('error', '未知错误')}")
            
            # 如果配置化编排不可用或失败，使用原有逻辑
            logger.debug("使用默认技能调度逻辑")
            
            # 🔥 原有：技能组合使用逻辑
            # 检查是否需要搜索技能
            requires_search = False
            search_keywords = ["搜索", "查一下", "找一找", "了解一下", "查询", "百度", "谷歌", "最新", "最近", "实时"]
            if intent_type in ["query", "search", "lookup"] or intent_result.get("requires_realtime_data", False):
                requires_search = True
            elif any(keyword in input_text for keyword in search_keywords):
                requires_search = True
            
            search_result = None
            search_context = ""
            
            # 如果需要搜索，先执行搜索技能
            if requires_search:
                logger.debug("🔍 检测到需要搜索，先执行搜索技能")
                
                # 构建搜索技能参数
                search_params = {
                    "input_text": input_text,
                    "user_id": user_id,
                    "session_id": session_id,
                    "intent_data": intent_result
                }
                
                # 执行搜索技能
                try:
                    search_result = self.execute_skill("search_skill", **search_params)
                    logger.debug(f"🔍 搜索技能返回结果类型: {type(search_result)}, 内容: {search_result}")
                    
                    if search_result and search_result.get("success"):
                        # 🔥 优先检查是否是上下文数据格式
                        if search_result.get("is_context_data"):
                            # 新格式：从data字段获取搜索结果
                            search_data = search_result.get("data", {})
                            search_context = search_data.get("formatted_context", "")
                            if search_context:
                                logger.debug(f"✅ ✅ 搜索技能执行成功(新格式)，获得搜索结果: {len(search_context)} 字符")
                            else:
                                # 如果formatted_context为空，尝试从results中构建
                                results = search_data.get("results", [])
                                if results and isinstance(results, list) and len(results) > 0:
                                    # 使用第一个搜索结果的snippet作为上下文
                                    first_result = results[0]
                                    if isinstance(first_result, dict):
                                        search_context = first_result.get("snippet", "") or first_result.get("content", "") or str(first_result)
                                    else:
                                        search_context = str(first_result)
                                    logger.debug(f"✅ ✅ 从搜索结果中重构上下文: {len(search_context)} 字符")
                        else:
                            # 兼容旧格式：直接从result字段获取
                            search_context = search_result.get("result", "")
                            if search_context:
                                logger.debug(f"✅ ✅ 搜索技能执行成功(旧格式)，获得搜索结果: {len(search_context)} 字符")
                        
                        # 🔥 最后的兜底处理：如果search_context仍然为空，强制提取
                        if not search_context and search_result.get("data"):
                            data = search_result.get("data", {})
                            # 尝试多种方式提取搜索内容
                            search_context = (
                                data.get("formatted_context", "") or
                                data.get("search_content", "") or
                                data.get("content", "") or
                                str(data.get("results", [{}])[0] if data.get("results") else "")
                            )
                            if search_context and search_context != "{}":
                                logger.debug(f"✅ ✅ 兜底提取搜索结果: {len(search_context)} 字符")
                            else:
                                logger.warning(f"⚠️ 搜索结果提取失败，data内容: {data}")
                        
                        # 最终检查
                        if not search_context:
                            logger.warning(f"⚠️ 搜索结果为空，完整结果: {search_result}")
                    else:
                        logger.warning_status(f"⚠️ 搜索技能执行失败: {search_result.get('error', '未知错误') if search_result else '无返回结果'}")
                except Exception as e:
                    logger.error_status(f"❌ 搜索技能执行异常: {e}")
            
            # 构建技能参数，包含搜索结果作为动态上下文
            skill_params = {
                "input_text": input_text,
                "user_id": user_id,
                "session_id": session_id,
                "intent_data": intent_result
            }
            
            # 🔥 调试：详细记录技能参数
            # logger.debug(f"🔍 构建技能参数:")
            # logger.debug(f"   - input_text: '{input_text}' (长度: {len(input_text) if input_text else 0})")
            # logger.debug(f"   - user_id: '{user_id}'")
            # logger.debug(f"   - session_id: '{session_id}'")
            # logger.debug(f"   - intent_data: {intent_result}")
            
            # 如果有搜索结果，添加到技能参数中
            if search_context:
                skill_params["search_context"] = search_context
                skill_params["search_result"] = search_result
                logger.debug("🔗 将搜索结果作为动态上下文传递给聊天技能")
            
            # 查找对应的技能
            skill_name = self.intent_to_skill_map.get(intent_type)
            
            # 🔥 修复：如果intent_type没有找到，尝试使用main_intent
            if not skill_name and intent_result.get("main_intent"):
                main_intent = intent_result.get("main_intent")
                skill_name = self.intent_to_skill_map.get(main_intent)
                logger.debug(f"使用main_intent查找技能: {main_intent} -> {skill_name}")
            
            if not skill_name:
                logger.warning_status(f"未找到对应意图的技能: intent_type={intent_type}, main_intent={intent_result.get('main_intent')}")
                # 默认使用聊天技能
                skill_name = "chat_skill"
                logger.debug("使用默认聊天技能")
            else:
                logger.debug(f"找到对应技能: {skill_name}")
            
            # 🔥 如果已经执行了搜索，强制使用聊天技能进行AI响应生成
            if requires_search:
                skill_name = "chat_skill"
                logger.success("🎯 搜索完成后，使用聊天技能生成AI响应")
            
            # 执行技能
            logger.debug(f"执行技能 {skill_name} 处理意图 {intent_type}")
            logger.debug(f"🔍 最终技能参数: {skill_params}")
            
            # 🔧 修复：确保参数正确传递
            logger.debug(f"🚀 调用 execute_skill({skill_name}, **{skill_params})")
            skill_result = self.execute_skill(skill_name, **skill_params)
            
            # 检查技能执行结果
            if not skill_result.get("success"):
                logger.warning_status(f"技能 {skill_name} 执行失败: {skill_result.get('error', '未知错误')}")
                return {
                    "skill_executed": False,
                    "skill_name": skill_name,
                    "intent_type": intent_type,
                    "error": skill_result.get("error", "未知错误"),
                    "search_executed": requires_search,
                    "search_success": search_result.get("success", False) if search_result else False
                }
            
            # 构建成功结果
            result = {
                "skill_executed": True,
                "skill_name": skill_name,
                "intent_type": intent_type,
                "skill_result": skill_result,
                "search_executed": requires_search,
                "search_success": search_result.get("success", False) if search_result else False,
                "should_skip_reasoning": True  # 技能已经提供了完整的处理结果
            }
            
            # 🔥 记录技能组合使用情况
            if requires_search and search_result and search_result.get("success"):
                logger.success("🎉 搜索+聊天技能组合执行成功")
            
            return result
            
        except Exception as e:
            logger.error_status(f"❌ 技能处理异常: {e}")
            import traceback
            logger.error_status(traceback.format_exc())
            return {
                "skill_executed": False,
                "error": str(e),
                "search_executed": False,
                "search_success": False
            }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "skill_manager.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.debug(f"已加载技能管理器配置: {config_path}")
                return config
            except Exception as e:
                logger.error_status(f"加载技能管理器配置失败: {e}")
        
        # 返回默认配置
        return {
            "auto_load_skills": True,
            "skills_path": "cognitive_modules.skills",
            "intent_to_skill_map": {
                "query": "search_skill",
                "recommendation": "search_skill",
                "navigation": "search_skill",
                "drawing": "drawing_skill",
                "url": "search_skill",
                "视觉内容分析": "chat_skill",
                "行为内容分析": "chat_skill",
                "内容分析": "chat_skill"
            }
        }
    
    def _register_event_handlers(self):
        """注册事件处理器"""
        # 订阅意图识别事件
        self.event_bus.subscribe("intent.recognized", self._handle_intent)
        
        # 订阅决策事件
        self.event_bus.subscribe("decision.made", self._handle_decision)
        
        # 🔥 老王修复：移除绘画意图事件监听，避免重复调用绘画技能
        # 绘画技能应该在技能执行阶段通过关键词匹配统一调用
        # self.event_bus.subscribe("drawing.intent.detected", self._handle_drawing_intent)
        
        logger.debug("技能管理器已注册事件处理器")
    
    def _validate_intent_mapping(self):
        """验证意图映射配置的完整性"""
        required_mappings = {
            "视觉内容分析": "chat_skill",
            "行为内容分析": "chat_skill",
            "内容分析": "chat_skill"
        }
        
        missing_mappings = []
        for intent, expected_skill in required_mappings.items():
            if self.intent_to_skill_map.get(intent) != expected_skill:
                missing_mappings.append(f"{intent} -> {expected_skill}")
        
        if missing_mappings:
            logger.warning_status(f"检测到缺失的意图映射: {missing_mappings}")
            # 自动补充缺失的映射
            for intent, skill in required_mappings.items():
                if intent not in self.intent_to_skill_map:
                    self.intent_to_skill_map[intent] = skill
                    logger.info(f"已补充意图映射: {intent} -> {skill}")
        else:
            logger.success("✅ 所有必需的意图映射配置完整")
    
    def refresh_intent_mapping(self):
        """强制刷新意图映射配置"""
        logger.info("🔄 强制刷新意图映射配置...")
        
        # 重新加载配置
        self.config = self._load_config()
        
        # 更新意图映射
        self.intent_to_skill_map = self.config.get("intent_to_skill_map", {})
        
        # 验证并补充缺失的映射
        self._validate_intent_mapping()
        
        logger.success(f"✅ 意图映射配置已刷新: {self.intent_to_skill_map}")
        return self.intent_to_skill_map
    
    def _load_skills(self):
        """加载技能模块"""
        if not self.config.get("auto_load_skills", True):
            logger.debug("自动加载技能已禁用")
            return
        
        # 加载意图到技能的映射
        self.intent_to_skill_map = self.config.get("intent_to_skill_map", {})
        
        # 🔥 香草修复：确保新添加的意图映射被正确加载
        if not self.intent_to_skill_map.get("视觉内容分析"):
            logger.warning_status("检测到意图映射不完整，强制更新配置")
            # 强制更新配置
            self.intent_to_skill_map.update({
                "视觉内容分析": "chat_skill",
                "行为内容分析": "chat_skill",
                "内容分析": "chat_skill"
            })
            logger.info(f"已强制更新意图映射: {self.intent_to_skill_map}")
        else:
            logger.debug(f"已加载意图到技能映射: {self.intent_to_skill_map}")
        
        # 获取技能目录
        skills_path = self.config.get("skills_path", "cognitive_modules.skills")
        skills_dir = os.path.join(PROJECT_ROOT, *skills_path.split('.'))
        
        if not os.path.exists(skills_dir):
            logger.warning_status(f"技能目录不存在: {skills_dir}")
            return
        
        # 首先确保加载关键技能
        priority_skills = ["drawing_skill", "search_skill", "chat_skill"]
        
        # 先尝试加载优先技能
        for skill_name in priority_skills:
            skill_file = f"{skill_name}.py"
            skill_path = os.path.join(skills_dir, skill_file)
            
            if os.path.exists(skill_path):
                try:
                    # 动态导入技能模块
                    module_path = f"{skills_path}.{skill_name}"
                    logger.debug(f"优先加载技能: {module_path}")
                    
                    module = importlib.import_module(module_path)
                    
                    # 检查模块是否有get_instance函数
                    if hasattr(module, "get_instance"):
                        skill_instance = module.get_instance()
                        self.register_skill(skill_name, skill_instance)
                        logger.debug(f"已加载优先技能: {skill_name}")
                    else:
                        logger.warning_status(f"优先技能模块 {skill_name} 没有get_instance函数")
                except Exception as e:
                    logger.error_status(f"加载优先技能 {skill_name} 失败: {e}")
                    import traceback
                    logger.error_status(traceback.format_exc())
        
        # 遍历目录加载其他技能
        loaded_skills = set(self.skills.keys())
        logger.success(f"优先技能加载完成，已加载: {loaded_skills}")
        
        # 🔥 香草修复：验证意图映射配置
        self._validate_intent_mapping()
        
        for item in os.listdir(skills_dir):
            if item.endswith("_skill.py") and not item.startswith("__"):
                skill_name = item[:-3]  # 去除.py后缀
                
                # 跳过已经加载的优先技能
                if skill_name in loaded_skills:
                    logger.debug(f"跳过已加载的技能: {skill_name}")
                    continue
                
                try:
                    # 动态导入技能模块
                    module_path = f"{skills_path}.{skill_name}"
                    logger.debug(f"加载技能模块: {module_path}")
                    
                    module = importlib.import_module(module_path)
                    
                    # 检查模块是否有get_instance函数
                    if hasattr(module, "get_instance"):
                        skill_instance = module.get_instance()
                        self.register_skill(skill_name, skill_instance)
                        logger.debug(f"已加载技能: {skill_name}")
                    else:
                        logger.warning_status(f"技能模块 {skill_name} 没有get_instance函数")
                except Exception as e:
                    logger.error_status(f"加载技能 {skill_name} 失败: {e}")
                    traceback.print_exc()
        
        # 检查关键技能是否加载成功
        for skill_name in priority_skills:
            if skill_name not in self.skills:
                logger.warning_status(f"关键技能 {skill_name} 未能成功加载")
            
        # 打印最终加载的所有技能
        logger.debug(f"技能加载完成，共加载 {len(self.skills)} 个技能: {list(self.skills.keys())}")
    
    def register_skill(self, skill_name: str, skill_instance: Any):
        """
        注册技能
        
        Args:
            skill_name: 技能名称
            skill_instance: 技能实例
        """
        self.skills[skill_name] = skill_instance
        logger.debug(f"已注册技能: {skill_name}")
    
    def get_skill(self, skill_name: str) -> Optional[Any]:
        """
        获取技能实例

        Args:
            skill_name: 技能名称

        Returns:
            技能实例或None
        """
        return self.skills.get(skill_name)

    def get_available_skills(self) -> Dict[str, Any]:
        """
        🔥 香草修复：获取所有可用技能

        Returns:
            技能字典 {技能名称: 技能实例}
        """
        return self.skills.copy()
    
    def execute_skill(self, skill_name: str, **kwargs) -> Dict[str, Any]:
        """
        执行技能
        
        Args:
            skill_name: 技能名称
            **kwargs: 技能参数
            
        Returns:
            执行结果
        """
        skill = self.get_skill(skill_name)
        if not skill:
            logger.warning_status(f"技能 {skill_name} 不存在")
            
            # 记录所有已注册的技能
            available_skills = list(self.skills.keys())
            logger.warning_status(f"当前已注册的技能: {available_skills}")
            
            # 尝试动态加载技能
            logger.debug(f"尝试动态加载技能: {skill_name}")
            try:
                # 根据技能名称构建模块路径
                module_path = f"cognitive_modules.skills.{skill_name}"
                
                # 尝试导入模块
                module = importlib.import_module(module_path)
                
                # 检查模块是否有get_instance函数
                if hasattr(module, "get_instance"):
                    skill_instance = module.get_instance()
                    self.register_skill(skill_name, skill_instance)
                    logger.success(f"成功动态加载技能: {skill_name}")
                    skill = skill_instance
                else:
                    logger.error_status(f"技能模块 {skill_name} 没有get_instance函数")
                    return {"success": False, "error": f"技能 {skill_name} 没有get_instance函数"}
            except Exception as e:
                logger.error_status(f"动态加载技能 {skill_name} 失败: {e}")
                import traceback
                logger.error_status(traceback.format_exc())
                return {"success": False, "error": f"技能 {skill_name} 不存在且无法动态加载: {str(e)}"}
        
        if not skill:
            return {"success": False, "error": f"技能 {skill_name} 不存在"}
            
        try:
            logger.debug(f"开始执行技能 {skill_name}，参数: {str(kwargs)[:100]}...")
            
            # 🔥 调试：详细记录传递给技能的参数
            # logger.debug(f"🔍 传递给技能 {skill_name} 的参数详情:")
            # logger.debug(f"   - kwargs长度: {len(kwargs)}")
            # for key, value in kwargs.items():
            #     if isinstance(value, str):
            #         logger.debug(f"   - {key}: '{value}' (长度: {len(value)})")
            #     else:
            #         logger.debug(f"   - {key}: {type(value).__name__} = {str(value)[:100]}...")
            
            # 🔧 调试技能实例
            # logger.debug(f"🔍 技能实例调试:")
            # logger.debug(f"   - skill类型: {type(skill)}")
            # logger.debug(f"   - skill是否有execute方法: {hasattr(skill, 'execute')}")
            # if hasattr(skill, "execute"):
            #     logger.debug(f"   - execute方法类型: {type(skill.execute)}")
            
            if hasattr(skill, "execute"):
                # 记录执行开始时间
                start_time = time.time()
                
                # 🔧 修复：统一使用关键字参数调用所有技能
                logger.debug(f"🚀 调用 {skill_name}.execute(**kwargs) 参数数量: {len(kwargs)}")
                
                # 🔥 调试：在调用前再次验证参数
                # logger.debug(f"🔍 调用前最后检查参数:")
                # for key, value in kwargs.items():
                #     logger.debug(f"   - 调用前 {key}: '{value}' (类型: {type(value)})")
                
                # 🔥 特别调试：如果是聊天技能，添加额外的调试信息
                # if skill_name == "chat_skill":
                #     logger.debug(f"🔍 聊天技能特别调试:")
                #     logger.debug(f"   - kwargs id: {id(kwargs)}")
                #     logger.debug(f"   - kwargs keys: {list(kwargs.keys())}")
                #     logger.debug(f"   - kwargs values: {list(kwargs.values())}")
                #     logger.debug(f"   - skill实例: {skill}")
                #     logger.debug(f"   - skill.execute方法: {skill.execute}")
                    
                    # 尝试手动调用看看结果
                    # logger.debug(f"🔍 手动调用测试:")
                    # try:
                    #     test_kwargs = kwargs.copy()
                    #     logger.debug(f"   - 复制的kwargs: {test_kwargs}")
                    #     logger.debug(f"   - 即将调用 skill.execute(**test_kwargs)")
                    # except Exception as test_e:
                    #     logger.error_status(f"   - 测试失败: {test_e}")
                
                result = skill.execute(**kwargs)
                
                # 计算执行时间
                execution_time = time.time() - start_time
                logger.debug(f"技能 {skill_name} 执行完成，耗时: {execution_time:.2f}秒")
                
                # 确保结果是字典类型
                if not isinstance(result, dict):
                    logger.warning_status(f"技能 {skill_name} 返回非字典结果: {result}")
                    result = {"success": True, "result": result}
                
                # 如果没有success字段，添加一个
                if "success" not in result:
                    result["success"] = True
                
                # 发布技能执行结果事件
                try:
                    self.event_bus.publish("skill.execution.completed", {
                        "skill_name": skill_name,
                        "success": result.get("success", True),
                        "execution_time": execution_time,
                        "result": result,  # 传递完整的结果对象，而不是只传递result字段
                        "image_url": result.get("image_url", ""),
                        "timestamp": time.time()
                    })
                except Exception as event_error:
                    logger.error_status(f"发布技能执行结果事件失败: {event_error}")
                
                return result
            else:
                logger.warning_status(f"技能 {skill_name} 没有execute方法")
                return {"success": False, "error": f"技能 {skill_name} 没有execute方法"}
        except Exception as e:
            logger.error_status(f"执行技能 {skill_name} 失败: {e}")
            import traceback
            error_trace = traceback.format_exc()
            logger.error_status(error_trace)
            
            # 发布技能执行失败事件
            try:
                self.event_bus.publish("skill.execution.failed", {
                    "skill_name": skill_name,
                    "error": str(e),
                    "timestamp": time.time()
                })
            except Exception as event_error:
                logger.error_status(f"发布技能执行失败事件失败: {event_error}")
            
            return {"success": False, "error": str(e), "details": error_trace}
    
    def _handle_decision(self, data: Dict[str, Any]):
        """
        处理决策事件
        
        Args:
            data: 事件数据
        """
        # 从决策中提取意图类型
        intent_type = data.get("type", "chat")
        requires_realtime_data = data.get("requires_realtime_data", False)
        
        # 根据意图类型找到对应的技能
        skill_name = self.intent_to_skill_map.get(intent_type)
        
        if not skill_name:
            logger.debug(f"意图类型 {intent_type} 没有对应的技能")
            return
        
        # 执行技能
        result = self.execute_skill(
            skill_name,
            input_text=data.get("content", ""),
            user_id=data.get("user_id") or "unknown_user",  # 🔥 P0级别修复
            session_id=data.get("session_id", ""),
            requires_realtime_data=requires_realtime_data,
            intent_data=data
        )
        
        # 发布技能执行结果事件
        self.event_bus.publish_enhanced(
            "skills.execution.result",
            {
                "skill_name": skill_name,
                "intent_type": intent_type,
                "success": result.get("success", False),
                "result": result.get("result"),
                "error": result.get("error"),
                "user_id": data.get("user_id"),
                "session_id": data.get("session_id", "")
            },
            source="skills.skill_manager",
            priority=EventPriority.HIGH
        )
    
    def _handle_intent(self, data: Dict[str, Any]):
        """
        处理意图事件
        
        Args:
            data: 事件数据
        """
        try:
            logger.debug(f"处理意图事件: {data}")
            
            # 获取意图数据
            intent = data.get("intent", {})
            user_id = data.get("user_id") or "unknown_user"  # 🔥 P0级别修复
            
            if not intent:
                logger.warning_status("事件中没有意图数据")
                return
                
            # 获取意图类型
            intent_type = intent.get("type", "chat")
            intent_name = data.get("intent_name", intent_type)
            
            logger.debug(f"意图类型: {intent_type}")
            
            # 获取输入文本
            text = data.get("text", "")
            if not text:
                text = intent.get("content", "")
                
            if not text:
                logger.warning_status("没有输入文本，跳过处理")
                return
                
            # 构建会话ID
            session_id = data.get("session_id", f"{user_id}_{int(time.time())}")
            
            # 如果是绘画意图，特殊处理
            if intent_type == "drawing":
                logger.debug("检测到绘画意图，执行绘画技能")
                
                # 执行绘画技能
                result = self.execute_skill(
                    "drawing_skill",
                    input_text=text,
                    user_id=user_id,
                    session_id=session_id,
                    intent_data=intent,
                    confidence=intent.get("confidence", 0.8),
                    main_intent="绘画"
                )
                
                # 判断是否成功执行
                if result.get("success"):
                    # 获取图像URL
                    image_url = result.get("image_url", "")
                    
                    # 发布响应事件
                    self.event_bus.publish("agent.response", {
                        "response_text": result.get("result", "我已经为你画好了"),
                        "image_url": image_url,
                        "user_id": user_id,
                        "session_id": session_id,
                        "intent": "drawing",
                        "is_final": True
                    })
                    
                    logger.debug(f"绘画结果: {result.get('result', '')}，图像URL: {image_url}")
                else:
                    # 发送错误响应
                    self.event_bus.publish("agent.response", {
                        "response_text": f"抱歉，绘画失败: {result.get('error', '未知错误')}",
                        "user_id": user_id,
                        "session_id": session_id,
                        "intent": "drawing",
                        "is_final": True
                    })
                    
                    logger.error_status(f"绘画失败: {result.get('error', '未知错误')}")
                
                return
                
            # 根据意图类型找到对应的技能
            skill_name = self.intent_to_skill_map.get(intent_type)
            
            if not skill_name:
                logger.warning_status(f"意图类型 {intent_type} 没有对应的技能，使用默认聊天技能")
                skill_name = "chat_skill"
            
            # 执行技能
            result = self.execute_skill(
                skill_name,
                input_text=text,
                user_id=user_id,
                session_id=session_id,
                requires_realtime_data=intent.get("requires_realtime_data", False),
                confidence=intent.get("confidence", 0.6),
                intent_data=intent,
                main_intent=intent.get("main_intent", "聊天")
            )
            
            # 更新生命上下文
            context_key = f"skills.intent.{user_id}"
            self.life_context.update_context(context_key, intent)
            
            # 发布响应事件
            self.event_bus.publish("agent.response", {
                "response_text": result.get("result", "我不知道该如何回答"),
                "user_id": user_id,
                "session_id": session_id,
                "intent": intent_type,
                "is_final": True
            })
            
            logger.debug(f"技能 {skill_name} 执行结果: {result.get('result', '')[:50]}...")
            
        except Exception as e:
            logger.error_status(f"处理意图事件异常: {e}")
            import traceback
            logger.error_status(traceback.format_exc())

    def _handle_drawing_intent(self, event_data: Dict[str, Any]):
        """
        🔥 老王修复：移除绘画意图事件处理，避免重复调用绘画技能

        原来的逻辑会在意图识别阶段就调用绘画技能，导致重复调用
        现在绘画技能只在技能执行阶段通过关键词匹配统一调用

        Args:
            event_data: 事件数据
        """
        logger.debug("🔥 老王修复：绘画意图事件处理已移除，避免重复调用绘画技能")
        logger.debug("绘画技能将在技能执行阶段通过关键词匹配统一调用")
        return
            
        # 判断是否成功执行
        if result.get("success"):
            # 获取图像URL
            image_url = result.get("image_url", "")

            # 发布响应事件
            self.event_bus.publish("agent.response", {
                "response_text": result.get("result", "我已经为你画好了"),
                "image_url": image_url,
                "user_id": user_id,
                "session_id": session_id,
                "intent": "drawing",
                "is_final": True
            })

            logger.debug(f"绘画结果: {result.get('result', '')}，图像URL: {image_url}")
        else:
            # 发送错误响应
            self.event_bus.publish("agent.response", {
                "response_text": f"抱歉，绘画失败: {result.get('error', '未知错误')}",
                "user_id": user_id,
                "session_id": session_id,
                "intent": "drawing",
                "is_final": True
            })

            logger.error_status(f"绘画失败: {result.get('error', '未知错误')}")
                
        # except Exception as e:
        #     logger.error_status(f"处理绘画意图检测事件异常: {e}")
        #     import traceback
        #     logger.error_status(traceback.format_exc())

    def handle_skill_register(self, data: Dict[str, Any]) -> None:
        """
        处理技能注册事件
        
        Args:
            data: 事件数据
        """
        try:
            skill_name = data.get("skill_name")
            skill_instance = data.get("skill_instance")
            
            if not skill_name or not skill_instance:
                logger.warning_status("技能注册事件缺少必要参数")
                return
                
            # 注册技能
            self.register_skill(skill_name, skill_instance)
            logger.debug(f"通过事件注册技能: {skill_name}")
        except Exception as e:
            logger.error_status(f"处理技能注册事件异常: {e}")
            
    def handle_skill_request(self, data: Dict[str, Any]) -> None:
        """
        处理技能请求事件
        
        Args:
            data: 事件数据
        """
        try:
            skill_name = data.get("skill_name")
            request_data = data.get("request_data", {})
            session_id = data.get("session_id", "")
            user_id = data.get("user_id") or "unknown_user"  # 🔥 P0级别修复
            request_id = data.get("request_id", str(uuid.uuid4()))
            
            if not skill_name:
                logger.warning_status("技能请求事件缺少必要参数")
                return
                
            # 获取技能
            skill = self.get_skill(skill_name)
            if not skill:
                logger.warning_status(f"请求的技能不存在: {skill_name}")
                
                # 发布技能请求失败事件
                if self.event_bus:
                    self.event_bus.publish("skill.request.failed", {
                        "skill_name": skill_name,
                        "request_id": request_id,
                        "error": f"技能不存在: {skill_name}",
                        "session_id": session_id,
                        "user_id": user_id
                    })
                return
                
            # 执行技能
            try:
                # 检查技能是否有execute方法
                if hasattr(skill, "execute") and callable(skill.execute):
                    # 执行技能
                    result = skill.execute(
                        **request_data,
                        session_id=session_id,
                        user_id=user_id
                    )
                    
                    # 发布技能执行成功事件
                    if self.event_bus:
                        self.event_bus.publish("skill.request.completed", {
                            "skill_name": skill_name,
                            "request_id": request_id,
                            "result": result,
                            "session_id": session_id,
                            "user_id": user_id
                        })
                else:
                    logger.warning_status(f"技能 {skill_name} 没有execute方法")
                    
                    # 发布技能请求失败事件
                    if self.event_bus:
                        self.event_bus.publish("skill.request.failed", {
                            "skill_name": skill_name,
                            "request_id": request_id,
                            "error": f"技能 {skill_name} 没有execute方法",
                            "session_id": session_id,
                            "user_id": user_id
                        })
            except Exception as e:
                logger.error_status(f"执行技能 {skill_name} 异常: {e}")
                import traceback
                logger.error_status(f"详细错误信息: {traceback.format_exc()}")
                
                # 发布技能请求失败事件
                if self.event_bus:
                    self.event_bus.publish("skill.request.failed", {
                        "skill_name": skill_name,
                        "request_id": request_id,
                        "error": str(e),
                        "traceback": traceback.format_exc(),
                        "session_id": session_id,
                        "user_id": user_id
                    })
        except Exception as e:
            logger.error_status(f"处理技能请求事件异常: {e}")
            
    def handle_intent_recognized(self, data: Dict[str, Any]) -> None:
        """
        处理意图识别事件
        
        Args:
            data: 事件数据
        """
        try:
            # 获取意图信息
            intent = data.get("intent", {})
            main_intent = intent.get("main_intent", "")
            
            # 如果没有主意图，则退出
            if not main_intent:
                logger.warning_status("意图识别事件缺少主意图")
                return
                
            # 获取会话信息
            session_id = data.get("session_id", "")
            user_id = data.get("user_id") or "unknown_user"  # 🔥 P0级别修复
            
            # 根据意图选择技能
            skill_name = self.intent_to_skill_map.get(main_intent)
            if not skill_name:
                logger.warning_status(f"未找到对应意图的技能: {main_intent}")
                return
                
            # 获取技能
            skill = self.get_skill(skill_name)
            if not skill:
                logger.warning_status(f"意图对应的技能不存在: {skill_name}")
                return
                
            # 执行技能
            try:
                # 检查技能是否有execute方法
                if hasattr(skill, "execute") and callable(skill.execute):
                    # 准备输入数据
                    input_text = data.get("input_text", "")
                    
                    # 执行技能
                    result = skill.execute(
                        input_text=input_text,
                        intent_data=intent,
                        session_id=session_id,
                        user_id=user_id
                    )
                    
                    # 发布技能执行完成事件
                    if self.event_bus:
                        self.event_bus.publish("skill.executed", {
                            "skill_name": skill_name,
                            "intent": main_intent,
                            "result": result,
                            "session_id": session_id,
                            "user_id": user_id
                        })
                else:
                    logger.warning_status(f"技能 {skill_name} 没有execute方法")
            except Exception as e:
                logger.error_status(f"执行技能 {skill_name} 异常: {e}")
        except Exception as e:
            logger.error_status(f"处理意图识别事件异常: {e}")

    def _load_intent_to_skill_map(self):
        """
        加载意图到技能的映射
        """
        try:
            # 从配置中加载意图到技能的映射
            self.intent_to_skill_map = self.config.get("intent_to_skill_map", {})
            self.logger.debug(f"已加载意图到技能映射: {self.intent_to_skill_map}")
        except Exception as e:
            self.logger.error_status(f"加载意图到技能映射失败: {e}")
            # 设置默认映射
            self.intent_to_skill_map = {
                "query": "search_skill",
                "recommendation": "search_skill",
                "navigation": "search_skill",
                "drawing": "drawing_skill",
                "url": "search_skill",
                # 🔥 新增：金融相关意图映射到OpenBB金融技能
                "financial_query": "openbb_financial_skill",
                "stock_query": "openbb_financial_skill",
                "market_analysis": "openbb_financial_skill",
                "investment_advice": "openbb_financial_skill",
                "portfolio_analysis": "openbb_financial_skill",
                "crypto_query": "openbb_financial_skill",
                "forex_query": "openbb_financial_skill",
                "economic_data": "openbb_financial_skill",
                "financial_analysis": "openbb_financial_skill",
                "金融查询": "openbb_financial_skill",
                "股票查询": "openbb_financial_skill",
                "市场分析": "openbb_financial_skill",
                "投资建议": "openbb_financial_skill",
                "投资组合分析": "openbb_financial_skill"
            }
            self.logger.debug(f"使用默认意图到技能映射: {self.intent_to_skill_map}")

    def _load_priority_skills(self):
        """
        加载优先技能
        """
        try:
            # 获取优先技能列表
            priority_skills = self.config.get("priority_skills", ["drawing_skill", "search_skill", "openbb_financial_skill", "chat_skill"])
            self.logger.debug(f"优先加载技能: {priority_skills}")
            
            # 逐个加载优先技能
            for skill_name in priority_skills:
                try:
                    # 构建模块路径
                    module_path = f"cognitive_modules.skills.{skill_name}"
                    
                    # 导入模块
                    self.logger.debug(f"尝试导入技能模块: {module_path}")
                    module = importlib.import_module(module_path)
                    
                    # 获取技能实例
                    if hasattr(module, "get_instance"):
                        skill_instance = module.get_instance()
                        self.register_skill(skill_name, skill_instance)
                        self.logger.debug(f"已加载优先技能: {skill_name}")
                    else:
                        self.logger.warning_status(f"技能模块 {skill_name} 没有get_instance函数")
                except Exception as e:
                    self.logger.error_status(f"加载优先技能 {skill_name} 失败: {e}")
                    import traceback
                    self.logger.error_status(traceback.format_exc())
        except Exception as e:
            self.logger.error_status(f"加载优先技能失败: {e}")

    def _load_orchestration_config(self) -> Dict[str, Any]:
        """
        加载技能编排配置
        """
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "skill_orchestration.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.debug("已加载技能编排配置")
                return config.get("orchestration", {})
            except Exception as e:
                logger.error_status(f"加载技能编排配置失败: {e}")
        
        # 返回默认配置
        return {
            "enabled": False,
            "default_strategy": "auto_detect",
            "strategies": {},
            "fallback_strategy": "direct_chat"
        }

    def _select_orchestration_strategy(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        选择技能编排策略
        """
        if not self.orchestration_config.get("enabled", False):
            return None
        
        strategies = self.orchestration_config.get("strategies", {})
        intent_data = context.get("intent", {})
        user_input = context.get("text", "") or context.get("user_input", "")
        
        # 按优先级排序策略
        sorted_strategies = sorted(
            strategies.items(), 
            key=lambda x: x[1].get("priority", 0), 
            reverse=True
        )
        
        # 遍历策略，找到匹配的
        for strategy_name, strategy in sorted_strategies:
            conditions = strategy.get("trigger_conditions", {})
            match_score = 0
            total_conditions = 0
            
            # 检查意图类型
            intent_types = conditions.get("intent_types", [])
            if intent_types:
                total_conditions += 1
                if intent_data.get("type") in intent_types:
                    match_score += 1
                    logger.debug(f"策略 {strategy_name} 意图类型匹配")
            
            # 检查关键词
            keywords = conditions.get("keywords", [])
            if keywords:
                total_conditions += 1
                if any(keyword in user_input for keyword in keywords):
                    match_score += 1
                    logger.debug(f"策略 {strategy_name} 关键词匹配")
            
            # 检查外部数据需求
            requires_external = conditions.get("requires_external_data")
            if requires_external is not None:
                total_conditions += 1
                if requires_external == self._requires_search(intent_data, user_input):
                    match_score += 1
                    logger.debug(f"策略 {strategy_name} 外部数据需求匹配")
            
            # 检查上下文模式
            context_patterns = conditions.get("context_patterns", [])
            if context_patterns:
                total_conditions += 1
                import re
                if any(re.search(pattern, user_input) for pattern in context_patterns):
                    match_score += 1
                    logger.debug(f"策略 {strategy_name} 上下文模式匹配")
            
            # 计算匹配度
            if total_conditions > 0:
                match_ratio = match_score / total_conditions
                threshold = 0.5  # 匹配阈值
                
                if match_ratio >= threshold:
                    logger.debug(f"选择策略: {strategy_name} (匹配度: {match_ratio:.2f})")
                    return strategy
        
        # 返回默认策略
        fallback_name = self.orchestration_config.get("fallback_strategy", "direct_chat")
        fallback_strategy = strategies.get(fallback_name)
        if fallback_strategy:
            logger.debug(f"使用回退策略: {fallback_name}")
            return fallback_strategy
        
        return None

    def _execute_orchestration_strategy(self, strategy: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行技能编排策略
        """
        skill_sequence = strategy.get("skill_sequence", [])
        execution_context = context.copy()
        results = {}
        
        logger.debug(f"执行编排策略: {strategy.get('name', '未命名策略')}")
        
        for step in skill_sequence:
            skill_id = step["skill_id"]
            role = step["role"]
            required = step.get("required", False)
            timeout = step.get("timeout", 30)
            condition = step.get("condition")
            
            # 检查条件
            if condition:
                if condition == "requires_search" and not self._requires_search(execution_context.get("intent", {}), execution_context.get("text", "")):
                    logger.debug(f"跳过技能 {skill_id}: 不满足条件 {condition}")
                    continue
                elif condition == "requires_drawing" and not self._requires_drawing(execution_context.get("text", "")):
                    logger.debug(f"跳过技能 {skill_id}: 不满足条件 {condition}")
                    continue
            
            # 获取技能
            skill = self.skills.get(skill_id)
            if not skill:
                if required:
                    return {"success": False, "error": f"必需技能不可用: {skill_id}"}
                logger.warning(f"技能不可用，跳过: {skill_id}")
                continue
            
            # 检查技能是否可以处理
            if hasattr(skill, 'can_handle') and not skill.can_handle(execution_context):
                if required:
                    return {"success": False, "error": f"技能无法处理请求: {skill_id}"}
                logger.warning(f"技能无法处理请求，跳过: {skill_id}")
                continue
            
            # 执行技能
            try:
                logger.debug(f"执行技能: {skill_id} (角色: {role})")
                
                if skill_id == "search_skill":
                    result = skill.execute(
                        input_text=execution_context.get("text") or execution_context.get("user_input", ""),
                        user_id=execution_context.get("user_id", "default_user"),
                        session_id=execution_context.get("session_id", ""),
                        intent_data=execution_context.get("intent", {})
                    )
                elif skill_id == "chat_skill":
                    # 🔥 老王修复：使用handle方法而不是execute，确保完整上下文传递
                    # 构建完整的上下文，包括metadata中的安全标识
                    chat_context = {
                        "text": execution_context.get("text") or execution_context.get("user_input", ""),
                        "user_id": execution_context.get("user_id"),
                        "session_id": execution_context.get("session_id", ""),
                        "timestamp": time.time()
                    }

                    # 🔥 关键修复：传递完整的metadata，包括安全标识
                    metadata = execution_context.get("metadata", {})
                    if metadata:
                        chat_context.update(metadata)

                    # 如果有搜索结果，添加到上下文中
                    search_result = results.get("search_skill")
                    if search_result and search_result.get("success") and search_result.get("is_context_data"):
                        chat_context["search_context"] = search_result["data"].get("formatted_context")
                        chat_context["search_result"] = search_result["data"]

                    # 使用handle方法，确保完整上下文传递
                    result = skill.handle(chat_context)
                else:
                    # 其他技能使用通用参数
                    result = skill.execute(
                        input_text=execution_context.get("text") or execution_context.get("user_input", ""),
                        user_id=execution_context.get("user_id", "default_user"),
                        session_id=execution_context.get("session_id", "")
                    )
                
                # 保存结果
                results[skill_id] = result
                
                if result and result.get("success"):
                    logger.success(f"技能 {skill_id} 执行成功")
                    
                    # 如果是最后一个技能且成功，返回结果
                    if step == skill_sequence[-1]:
                        return {
                            "skill_executed": True,
                            "strategy": strategy["name"],
                            "primary_skill": skill_id,
                            "search_executed": "search_skill" in results,
                            "search_success": bool(results.get("search_skill", {}).get("success")),
                            "response": result.get("result", "处理完成"),
                            "metadata": {
                                "strategy_name": strategy["name"],
                                "executed_skills": list(results.keys()),
                                "results": results
                            }
                        }
                else:
                    logger.warning(f"技能 {skill_id} 执行失败: {result.get('error', '未知错误') if result else '无返回结果'}")
                    if required:
                        return {"success": False, "error": f"必需技能执行失败: {skill_id}"}
                
            except Exception as e:
                logger.error_status(f"执行技能 {skill_id} 时出错: {e}")
                if required:
                    return {"success": False, "error": f"技能执行异常: {skill_id}"}
        
        return {"success": False, "error": "策略执行失败"}

    def _requires_search(self, intent_data: Dict[str, Any], user_input: str) -> bool:
        """
        判断是否需要搜索 - 最小化改动的判断逻辑
        """
        # 基于意图类型判断
        intent_type = intent_data.get("type", "")
        if intent_type in ["query", "search", "navigation", "recommendation"]:
            return True
        
        # 基于关键词判断
        search_keywords = ["搜索", "查询", "帮我找", "最新", "实时", "新闻", "信息", "天气", "股票"]
        if any(keyword in user_input for keyword in search_keywords):
            return True
        
        # 基于意图数据判断
        if intent_data.get("requires_search", False) or intent_data.get("requires_realtime_data", False):
            return True
        
        return False

    def _requires_drawing(self, user_input: str) -> bool:
        """
        判断是否需要绘画
        """
        drawing_keywords = ["画一张", "给我画", "帮我画", "帮我生成"]
        return any(keyword in user_input for keyword in drawing_keywords)
    
    def _register_assistant_reminder_skill(self):
        """注册助理提醒技能"""
        try:
            logger.debug("开始注册助理提醒技能...")
            
            # 🔥 老王修复：增加详细的导入和初始化日志
            try:
                from cognitive_modules.skills.assistant_reminder_skill import get_instance
                logger.success("助理提醒技能模块导入成功")
            except ImportError as import_error:
                logger.error_status(f"助理提醒技能模块导入失败: {import_error}")
                logger.error_status("请检查cognitive_modules/skills/assistant_reminder_skill.py文件是否存在")
                return
            except Exception as import_error:
                logger.error_status(f"助理提醒技能模块导入异常: {import_error}")
                import traceback
                logger.error_status(f"详细错误: {traceback.format_exc()}")
                return
            
            # 创建技能实例
            try:
                logger.debug("正在创建助理提醒技能实例...")
                reminder_skill = get_instance()
                logger.success(f"助理提醒技能实例创建成功: {type(reminder_skill).__name__}")
            except Exception as instance_error:
                logger.error_status(f"创建助理提醒技能实例失败: {instance_error}")
                import traceback
                logger.error_status(f"详细错误: {traceback.format_exc()}")
                # 🔥 老王修复：即使实例创建失败，也要记录并继续，不影响系统启动
                logger.warning_status("助理提醒技能实例创建失败，但系统将继续运行")
                return
            
            # 注册技能
            try:
                logger.debug("正在注册助理提醒技能到技能管理器...")
                self.register_skill("assistant_reminder_skill", reminder_skill)
                logger.success("助理提醒技能注册到技能管理器成功")
            except Exception as register_error:
                logger.error_status(f"注册助理提醒技能到技能管理器失败: {register_error}")
                import traceback
                logger.error_status(f"详细错误: {traceback.format_exc()}")
                return
            
            # 添加意图映射
            try:
                logger.debug("正在添加助理提醒技能的意图映射...")
                reminder_intents = [
                    "reminder_request",
                    "schedule_task", 
                    "set_reminder",
                    "create_todo",
                    "time_based_task",
                    "提醒任务",  # 🔥 P0级别修复：添加中文意图映射
                    "reminder",
                    "提醒"
                ]
                
                for intent in reminder_intents:
                    self.intent_to_skill_map[intent] = "assistant_reminder_skill"
                
                logger.success(f"助理提醒技能意图映射添加成功，共{len(reminder_intents)}个意图")
                logger.debug(f"映射的意图: {reminder_intents}")
            except Exception as mapping_error:
                logger.error_status(f"添加助理提醒技能意图映射失败: {mapping_error}")
                import traceback
                logger.error_status(f"详细错误: {traceback.format_exc()}")
                # 意图映射失败不影响技能注册，继续执行
                logger.warning_status("意图映射失败，但技能已注册成功")
            
            logger.success("✅ 助理提醒技能注册完成")
            
            # 🔥 老王修复：验证技能是否真正注册成功
            if "assistant_reminder_skill" in self.skills:
                logger.success("✅ 验证确认：助理提醒技能已在技能列表中")
                
                # 测试技能是否可以执行
                try:
                    skill_instance = self.skills["assistant_reminder_skill"]
                    if hasattr(skill_instance, 'execute') and callable(skill_instance.execute):
                        logger.debug("✅ 验证确认：助理提醒技能execute方法可用")
                    else:
                        logger.warning_status("⚠️ 助理提醒技能缺少execute方法")
                        
                    if hasattr(skill_instance, 'can_handle') and callable(skill_instance.can_handle):
                        logger.debug("✅ 验证确认：助理提醒技能can_handle方法可用")
                    else:
                        logger.warning_status("⚠️ 助理提醒技能缺少can_handle方法")
                        
                except Exception as verify_error:
                    logger.warning_status(f"验证助理提醒技能时出错: {verify_error}")
            else:
                logger.error_status("❌ 验证失败：助理提醒技能未在技能列表中找到")
            
        except Exception as e:
            logger.error_status(f"注册助理提醒技能总体失败: {e}")
            import traceback
            logger.error_status(f"详细错误: {traceback.format_exc()}")
            # 🔥 老王修复：即使注册失败，也不应该影响整个系统的启动
            logger.warning_status("助理提醒技能注册失败，但系统将继续运行，其他功能不受影响")


# 单例模式
_instance = None

def get_instance(module_config=None) -> SkillManager:
    """
    获取技能管理器实例（单例模式）
    
    Args:
        module_config: 模块配置（可选）
        
    Returns:
        SkillManager: 技能管理器实例
    """
    global _instance
    
    # 优先使用单例管理器
    try:
        # 尝试导入单例管理器
        from utilities.singleton_manager import get, register, get_or_create
        
        # 使用get_or_create获取或创建实例
        def create_instance():
            return SkillManager()
        
        return get_or_create('skill_manager', create_instance)
    except ImportError:
        # 如果单例管理器不可用，使用模块级单例
        if _instance is None:
            _instance = SkillManager()
        return _instance

def process(context):
    """
    处理思维链路上下文，调用相应技能
    支持技能组合使用，特别是搜索+聊天技能的组合
    
    Args:
        context: 思维链路上下文对象
        
    Returns:
        处理结果
    """
    logger.debug("执行技能处理步骤 - 支持技能组合")
    
    try:
        # 获取技能管理器实例
        skill_manager = get_instance()
        
        # 🔥 生产环境修复：严格验证用户ID，不允许default_user
        if hasattr(context, 'get'):
            user_id = context.get("user_id")
        elif hasattr(context, 'input_data') and context.input_data:
            user_id = context.input_data.get("user_id")
        elif hasattr(context, 'get_shared_data'):
            user_id = context.get_shared_data("user_id")
        elif hasattr(context, 'metadata') and context.metadata:
            user_id = context.metadata.get("user_id")
        else:
            user_id = None
        
        # 🔥 严格验证：生产环境不允许default_user或空值
        if not user_id or user_id == "default_user":
            error_msg = "❌ 技能执行失败：生产环境要求提供有效的user_id，不允许default_user或空值"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        session_id = "default_session"
        if hasattr(context, 'session_id'):
            session_id = context.session_id
        
        # 获取输入文本
        input_text = ""
        
        # 🔥 调试：详细记录context的数据结构
        # logger.debug(f"🔍 Context调试信息:")
        # logger.debug(f"   - context类型: {type(context)}")
        # logger.debug(f"   - hasattr(context, 'input_data'): {hasattr(context, 'input_data')}")
        # if hasattr(context, 'input_data'):
        #     logger.debug(f"   - context.input_data类型: {type(context.input_data)}")
        #     logger.debug(f"   - context.input_data内容: {context.input_data}")
        # logger.debug(f"   - hasattr(context, 'get_shared_data'): {hasattr(context, 'get_shared_data')}")
        
        # 🔥 老王修复：优化输入文本提取逻辑，确保不丢失用户输入
        if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            # 尝试多个字段获取输入文本
            for field in ["text", "input_text", "user_input", "message", "content"]:
                input_text = context.input_data.get(field, "")
                if input_text and input_text.strip():
                    logger.debug(f"🔍 从input_data.{field}获取到输入: '{input_text}' (长度: {len(input_text)})")
                    break

        elif hasattr(context, 'get_shared_data'):
            # 尝试多个共享数据字段
            for field in ["input_text", "text", "user_input", "message"]:
                input_text = context.get_shared_data(field, "")
                if input_text and input_text.strip():
                    logger.debug(f"🔍 从get_shared_data.{field}获取到输入: '{input_text}' (长度: {len(input_text)})")
                    break

        # 🔥 如果还是没有获取到，尝试从context的直接属性获取
        if not input_text or not input_text.strip():
            for attr in ["text", "input_text", "user_input", "message"]:
                if hasattr(context, attr):
                    attr_value = getattr(context, attr, "")
                    if attr_value and attr_value.strip():
                        input_text = attr_value
                        logger.debug(f"🔍 从context.{attr}获取到输入: '{input_text}' (长度: {len(input_text)})")
                        break
            
        # 🔥 老王修复：如果仍然没有获取到输入文本，记录详细的调试信息
        if not input_text or not input_text.strip():
            logger.warning(f"⚠️ 未能从context中提取到有效的输入文本")
            logger.debug(f"🔍 Context类型: {type(context)}")
            logger.debug(f"🔍 Context属性: {[attr for attr in dir(context) if not attr.startswith('_')]}")
            if hasattr(context, 'input_data'):
                logger.debug(f"🔍 input_data内容: {context.input_data}")
            if hasattr(context, 'metadata'):
                logger.debug(f"🔍 metadata内容: {context.metadata}")

        logger.debug(f"🔍 最终提取的input_text: '{input_text}' (长度: {len(input_text)}, 是否为空: {not input_text or not input_text.strip()})")
        
        # 检查关键词匹配
        keyword_match = False
        matched_skill = None
        
        # 从配置中获取关键词到技能的映射
        keyword_to_skill_map = skill_manager.config.get("keyword_to_skill_map", {})
        
        # 如果存在关键词映射，尝试匹配
        if keyword_to_skill_map and input_text:
            input_lower = input_text.lower()
            for keyword, skill_name in keyword_to_skill_map.items():
                if keyword in input_lower:
                    logger.debug(f"通过关键词 '{keyword}' 匹配到技能: {skill_name}")
                    keyword_match = True
                    matched_skill = skill_name
                    break
        
        # 如果通过关键词匹配到了技能，处理不同类型技能
        if keyword_match and matched_skill:
            # 🔥 特殊处理：如果匹配到搜索技能，不直接执行，而是标记需要搜索+chat组合
            if matched_skill == "search_skill":
                logger.debug(f"关键词匹配到搜索技能，转换为搜索+chat技能组合")
                # 设置搜索需求标志，让后续的搜索+chat组合逻辑处理
                requires_search = True
                # 继续执行后续的搜索+chat组合逻辑，不在这里直接返回
            else:
                # 对于非搜索技能（如绘画技能），直接执行
                # 构建技能参数
                skill_params = {
                    "input_text": input_text,
                    "user_id": user_id,
                    "session_id": session_id,
                    "intent_data": {"type": "drawing", "content": input_text}
                }
                
                # 执行技能
                logger.debug(f"执行关键词匹配的技能: {matched_skill}")
                skill_result = skill_manager.execute_skill(matched_skill, **skill_params)
                
                # 检查技能执行结果
                if skill_result.get("success"):
                    logger.debug(f"关键词匹配的技能 {matched_skill} 执行成功")
                    
                    # 更新思维链路上下文中的技能执行结果
                    if hasattr(context, 'update_shared_data'):
                        context.update_shared_data("skill_result", skill_result)
                        context.update_shared_data("skill_executed", True)
                        context.update_shared_data("skill_name", matched_skill)
                    
                    # 更新生命上下文中的技能执行结果
                    try:
                        from core.life_context import get_instance as get_life_context
                        life_context = get_life_context()
                        
                        skill_result_key = f"skills.result.{user_id}"
                        life_context.update_context(skill_result_key, skill_result)
                    except Exception as e:
                        logger.error_status(f"更新生命上下文中的技能执行结果异常: {e}")
                    
                    # 构建结果对象
                    # 只有绘画技能才跳过认知推理，其他技能保留推理链路
                    skip_reasoning = matched_skill == "drawing_skill"
                    
                    if skip_reasoning:
                        logger.debug(f"绘画技能 {matched_skill} 执行完成，跳过后续推理")
                    else:
                        logger.debug(f"技能 {matched_skill} 执行完成，保留后续推理链路")
                    
                    result = {
                        "skill_executed": True,
                        "skill_name": matched_skill,
                        "intent_type": "drawing" if matched_skill == "drawing_skill" else "search",
                        "skill_result": skill_result,  # 传递完整的技能结果对象
                        "should_skip_reasoning": skip_reasoning,
                        "search_executed": matched_skill == "search_skill",  # 🔥 添加搜索执行标志
                        "search_success": skill_result.get("success", False) if matched_skill == "search_skill" else False
                    }
                    
                    # 返回成功执行的技能结果
                    return result
                else:
                    logger.warning_status(f"关键词匹配的技能 {matched_skill} 执行失败: {skill_result.get('error', '未知错误')}")
        
        # 获取感知步骤的意图分析结果
        intent_result = {}
        
        # 🔥 优先从context的input_data中获取意图数据
        if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            if "intent" in context.input_data:
                intent_result = context.input_data["intent"]
                logger.debug(f"从input_data获取意图结果: {intent_result}")
            elif "intent_data" in context.input_data:
                intent_result = context.input_data["intent_data"]
                logger.data_status(f"从input_data获取意图数据: {intent_result}")
        
        # 如果还没有获取到意图，尝试从步骤结果中获取
        if not intent_result or "type" not in intent_result:
            if hasattr(context, 'get_step_result'):
                perception_result = context.get_step_result("perception", {})
                if perception_result and isinstance(perception_result, dict):
                    intent_result = perception_result
                    logger.debug(f"从步骤结果获取意图: {intent_result}")
        
        # 如果没有从步骤结果中获取到意图，尝试从生命上下文中获取
        if not intent_result or "type" not in intent_result:
            try:
                # 获取生命上下文
                from core.life_context import get_instance as get_life_context
                life_context = get_life_context()
                
                # 从生命上下文中获取意图
                intent_context_key = f"perception.intent.{user_id}"
                intent_result = life_context.get_context(intent_context_key, {})
                if intent_result and "type" in intent_result:
                    logger.debug(f"从生命上下文获取意图: {intent_result}")
            except Exception as e:
                logger.warning_status(f"从生命上下文获取意图失败: {e}")
        
        # 🔥 如果仍然找不到意图分析结果，使用默认的聊天意图
        if not intent_result or "type" not in intent_result:
            logger.debug(f"未找到明确意图，使用默认聊天意图处理用户输入: {input_text[:50]}...")
            intent_result = {
                "type": "chat",
                "content": input_text,
                "confidence": 0.8,
                "main_intent": "聊天"
            }
            
            # 🔥 重要：将默认意图设置到上下文共享数据中，避免重复识别
            if hasattr(context, 'set_shared_data'):
                context.set_shared_data("intent", intent_result)
                context.set_shared_data("intent_recognized", True)
                logger.debug("已将默认意图设置到共享数据中")
        
        # 提取意图类型
        intent_type = intent_result.get("type", "chat")
        
        # 🧠 接受自主决策的技能组合指示
        # 检查自主决策是否要求搜索+chat技能组合
        # 🔥 如果关键词匹配已经设置了requires_search，则保持该值，否则初始化为False
        if 'requires_search' not in locals():
            requires_search = False
        
        # 从自主决策模块获取技能组合指示（如果有的话）
        if hasattr(context, 'get_shared_data'):
            decision_skill_combo = context.get_shared_data("decision_skill_combo", None)
            logger.debug(f"🔍 技能管理器获取决策指示: decision_skill_combo = {decision_skill_combo}")
            if decision_skill_combo == "search_chat":
                requires_search = True
                logger.debug("🧠 接受自主决策指示：执行搜索+chat技能组合")
            else:
                logger.debug(f"🔍 决策指示不是search_chat，获取到: {decision_skill_combo}")
        else:
            logger.warning("🔍 技能管理器无法获取共享数据，context没有get_shared_data方法")
        
        # 如果没有明确的决策指示，使用原有的启发式判断（兼容性）
        if not requires_search:
            search_keywords = ["搜索", "查一下", "找一找", "了解一下", "查询", "百度", "谷歌", "最新", "最近", "实时"]

            # 🔥 老王修复：对于chat意图，更严格的搜索判断逻辑
            if intent_type == "chat":
                # 对于聊天意图，只有明确包含搜索关键词才触发搜索
                if any(keyword in input_text for keyword in search_keywords):
                    requires_search = True
                    logger.debug(f"🔍 聊天意图包含搜索关键词，触发搜索: {[kw for kw in search_keywords if kw in input_text]}")
                else:
                    logger.debug(f"🔍 聊天意图且无搜索关键词，不触发搜索")
            elif intent_type in ["query", "search", "lookup"] or intent_result.get("requires_realtime_data", False):
                requires_search = True
                logger.debug(f"🔍 非聊天意图或需要实时数据，触发搜索: intent_type={intent_type}, requires_realtime_data={intent_result.get('requires_realtime_data', False)}")
            elif any(keyword in input_text for keyword in search_keywords):
                requires_search = True
                logger.debug(f"🔍 包含搜索关键词，触发搜索: {[kw for kw in search_keywords if kw in input_text]}")
        
        search_result = None
        search_context = ""
        
        # 如果需要搜索，先执行搜索技能
        if requires_search:
            logger.debug("🔍 检测到需要搜索，先执行搜索技能")
            
            # 构建搜索技能参数
            search_params = {
                "input_text": input_text,
                "user_id": user_id,
                "session_id": session_id,
                "intent_data": intent_result
            }
            
            # 执行搜索技能
            try:
                search_result = skill_manager.execute_skill("search_skill", **search_params)
                logger.debug(f"🔍 搜索技能返回结果: {search_result}")
                
                if search_result and search_result.get("success"):
                    # 🔥 修复搜索结果提取逻辑：从data.formatted_context获取
                    if search_result.get("is_context_data") and search_result.get("data"):
                        data = search_result["data"]
                        search_context = data.get("formatted_context", "")
                        logger.debug(f"✅ 搜索技能执行成功(新格式)，获得搜索结果: {len(search_context)} 字符")
                        logger.debug(f"🔍 搜索上下文内容: {search_context[:300]}...")
                    else:
                        # 兼容旧格式
                        search_context = search_result.get("result", "")
                        logger.debug(f"✅ 搜索技能执行成功(旧格式)，获得搜索结果: {len(search_context)} 字符")
                        logger.debug(f"🔍 搜索上下文内容: {search_context[:300]}...")
                else:
                    logger.warning_status(f"⚠️ 搜索技能执行失败: {search_result.get('error', '未知错误') if search_result else '无返回结果'}")
            except Exception as e:
                logger.error_status(f"❌ 搜索技能执行异常: {e}")
        
        # 🔥 老王修复：从context中提取用户名
        user_name = None
        if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            user_name = context.input_data.get("user_name") or context.input_data.get("name")
        elif hasattr(context, 'metadata') and isinstance(context.metadata, dict):
            user_name = context.metadata.get("user_name") or context.metadata.get("name")
        
        # 构建技能参数，包含搜索结果作为动态上下文
        skill_params = {
            "input_text": input_text,
            "user_id": user_id,
            "session_id": session_id,
            "intent_data": intent_result
        }
        
        # 🔥 老王修复：数据完整性检查
        # logger.debug(f"🔍 数据完整性检查:")
        # logger.debug(f"   - input_text: {'✅' if input_text else '❌'} (长度: {len(input_text) if input_text else 0})")
        # logger.debug(f"   - user_id: {'✅' if user_id else '❌'} (值: {user_id})")
        # logger.debug(f"   - session_id: {'✅' if session_id else '❌'} (值: {session_id})")
        # logger.debug(f"   - user_name: {'✅' if user_name else '❌'} (值: {user_name})")
        # logger.debug(f"   - intent_data: {'✅' if intent_result else '❌'} (类型: {type(intent_result)})")
        
        # 🔥 老王修复：如果有用户名，添加到技能参数中
        if user_name:
            skill_params["user_name"] = user_name
            logger.debug(f"🔥 已添加用户名到技能参数: {user_name}")
        else:
            logger.warning("⚠️ 未获取到用户名，技能执行可能使用降级方案")
        
        # 🔥 调试：详细记录技能参数
        # logger.debug(f"🔍 构建技能参数:")
        # logger.debug(f"   - input_text: '{input_text}' (长度: {len(input_text) if input_text else 0})")
        # logger.debug(f"   - user_id: '{user_id}'")
        # logger.debug(f"   - session_id: '{session_id}'")
        # logger.debug(f"   - intent_data: {intent_result}")
        
        # 如果有搜索结果，添加到技能参数中
        if search_context:
            skill_params["search_context"] = search_context
            skill_params["search_result"] = search_result
            logger.debug(f"🔗 将搜索结果作为动态上下文传递给聊天技能，长度: {len(search_context)}字符")
            logger.debug(f"🔗 搜索上下文预览: {search_context}...")
        else:
            logger.debug("没有搜索上下文，使用纯聊天模式")
        
        # 获取意图到技能的映射
        if not hasattr(skill_manager, 'intent_to_skill_map') or not skill_manager.intent_to_skill_map:
            logger.warning_status("技能管理器缺少intent_to_skill_map属性，尝试重新加载")
            
            # 尝试调用_load_intent_to_skill_map方法重新加载
            try:
                if hasattr(skill_manager, '_load_intent_to_skill_map'):
                    skill_manager._load_intent_to_skill_map()
                    logger.debug("✅ 已重新加载意图到技能映射")
                else:
                    logger.warning_status("技能管理器没有_load_intent_to_skill_map方法")
            except Exception as e:
                logger.warning_status(f"重新加载意图映射失败: {e}")
            
            # 如果还是没有，创建默认映射
            if not hasattr(skill_manager, 'intent_to_skill_map') or not skill_manager.intent_to_skill_map:
                default_mapping = {
                    "query": "search_skill",
                    "search": "search_skill", 
                    "information": "search_skill",
                    "recommendation": "search_skill",
                    "navigation": "search_skill",
                    "drawing": "drawing_skill",
                    "绘画": "drawing_skill",
                    "绘画创作": "drawing_skill",
                    "image_generation": "drawing_skill",
                    "url": "search_skill",
                    "chat": "chat_skill",
                    "聊天": "chat_skill",
                    "交互或对话": "chat_skill",
                    "task": "chat_skill",
                    "视觉内容分析": "chat_skill",
                    "行为内容分析": "chat_skill",
                    "内容分析": "chat_skill"
                }
                # 设置属性
                skill_manager.intent_to_skill_map = default_mapping
                logger.debug(f"已设置默认意图到技能映射: {list(default_mapping.keys())}")
                intent_to_skill_map = default_mapping
            else:
                intent_to_skill_map = skill_manager.intent_to_skill_map
                logger.debug(f"✅ 已加载意图到技能映射，包含 {len(intent_to_skill_map)} 个映射")
        else:
            intent_to_skill_map = skill_manager.intent_to_skill_map
            logger.debug(f"已加载意图到技能映射，包含 {len(intent_to_skill_map)} 个映射")
        
        # 查找对应的技能
        skill_name = intent_to_skill_map.get(intent_type)
        
        # 🔥 修复：如果intent_type没有找到，尝试使用main_intent
        if not skill_name and intent_result.get("main_intent"):
            main_intent = intent_result.get("main_intent")
            skill_name = intent_to_skill_map.get(main_intent)
            logger.debug(f"使用main_intent查找技能: {main_intent} -> {skill_name}")
        
        if not skill_name:
            logger.warning_status(f"未找到对应意图的技能: intent_type={intent_type}, main_intent={intent_result.get('main_intent')}")
            # 默认使用聊天技能
            skill_name = "chat_skill"
            logger.debug("使用默认聊天技能")
        else:
            logger.debug(f"找到对应技能: {skill_name}")
        
        # 🔥 如果已经执行了搜索，强制使用聊天技能进行AI响应生成
        if requires_search:
            skill_name = "chat_skill"
            logger.success("🎯 搜索完成后，使用聊天技能生成AI响应")
        
        # 🔥 修复：确保传递给技能的参数中包含用户输入
        if not skill_params.get("input_text") and input_text:
            skill_params["input_text"] = input_text
            logger.debug(f"🔥 修复技能参数中的input_text: {input_text[:50]}...")
        
        # 执行技能
        logger.debug(f"执行技能 {skill_name} 处理意图 {intent_type}")
        logger.debug(f"🔍 最终技能参数验证: input_text={'✅' if skill_params.get('input_text') else '❌'}, user_id={'✅' if skill_params.get('user_id') else '❌'}")
        skill_result = skill_manager.execute_skill(skill_name, **skill_params)
        
        # 检查技能执行结果
        if not skill_result.get("success"):
            logger.warning_status(f"技能 {skill_name} 执行失败: {skill_result.get('error', '未知错误')}")
            return {
                "skill_executed": False,
                "skill_name": skill_name,
                "intent_type": intent_type,
                "error": skill_result.get("error", "未知错误"),
                "search_executed": requires_search,
                "search_success": search_result.get("success", False) if search_result else False
            }
        
        # 更新思维链路上下文中的技能执行结果
        if hasattr(context, 'update_shared_data'):
            context.update_shared_data("skill_result", skill_result)
            context.update_shared_data("skill_executed", True)
            context.update_shared_data("skill_name", skill_name)
            if requires_search:
                context.update_shared_data("search_executed", True)
                context.update_shared_data("search_result", search_result)
        
        # 更新生命上下文中的技能执行结果
        try:
            from core.life_context import get_instance as get_life_context
            life_context = get_life_context()
            
            skill_result_key = f"skills.result.{user_id}"
            life_context.update_context(skill_result_key, skill_result)
            
            if requires_search and search_result:
                search_result_key = f"skills.search.{user_id}"
                life_context.update_context(search_result_key, search_result)
        except Exception as e:
            logger.error_status(f"更新生命上下文中的技能执行结果异常: {e}")
        
        # 如果技能成功执行并产生了结果，设置标志让后续步骤可以选择跳过
        if skill_result.get("success") and skill_result.get("result"):
            # 构建结果对象
            # 只有绘画技能等特殊技能才跳过认知推理，搜索+聊天技能需要保留推理链路
            skip_reasoning = False  # 默认不跳过认知推理
            
            # 只有纯粹的绘画技能才跳过推理
            if skill_name == "drawing_skill" and not requires_search:
                skip_reasoning = True
                logger.debug(f"绘画技能 {skill_name} 执行完成，跳过后续推理")
            else:
                logger.debug(f"技能 {skill_name} 执行完成，保留后续推理链路以进行AI响应")
            
            result = {
                "skill_executed": True,
                "skill_name": skill_name,
                "intent_type": intent_type,
                "skill_result": skill_result,  # 传递完整的技能结果对象
                "should_skip_reasoning": skip_reasoning,
                "search_executed": requires_search,
                "search_success": search_result.get("success", False) if search_result else False,
                "combined_skills": ["search_skill", "chat_skill"] if requires_search else [skill_name]
            }
            
            # 返回成功执行的技能结果
            return result
        
        # 如果技能没有产生结果，返回执行状态
        return {
            "skill_executed": skill_result.get("success", False),
            "skill_name": skill_name,
            "intent_type": intent_type,
            "search_executed": requires_search,
            "search_success": search_result.get("success", False) if search_result else False
        }
        
    except Exception as e:
        logger.error_status(f"技能处理步骤异常: {e}")
        import traceback
        logger.error_status(f"详细错误信息: {traceback.format_exc()}")
        
        # 返回错误结果
        return {
            "skill_executed": False,
            "error": str(e)
        }