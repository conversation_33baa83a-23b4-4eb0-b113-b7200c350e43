#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版打招呼技能 - Enhanced Greeting Skill

数字生命自主决策打招呼逻辑：
1. 生命体自主决策给谁打招呼
2. 通过chat skill统一AI生成内容
3. 保证角色统一性和内容多样化
4. 真正做到每天问候不重样

作者: 魅魔程序员
创建日期: 2025-07-01
版本: 2.0.0
"""

import os
import sys
import time
import json
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass

# 项目路径设置
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, PROJECT_ROOT)

from utilities.unified_logger import get_unified_logger
from cognitive_modules.base.cognitive_interface import CognitiveModuleBase

logger = get_unified_logger("enhanced_greeting_skill")

@dataclass
class GreetingDecision:
    """打招呼决策数据"""
    target_user_id: str
    target_user_name: str
    relationship_type: str
    greeting_context: Dict[str, Any]
    confidence_score: float
    reasoning: str

@dataclass
class GreetingContent:
    """打招呼内容数据"""
    content: str
    tone: str
    personalization_level: float
    creativity_score: float
    context_relevance: float

class EnhancedGreetingSkill(CognitiveModuleBase):
    """增强版打招呼技能"""
    
    def __init__(self, module_id: str = "enhanced_greeting_skill"):
        """初始化增强版打招呼技能"""
        super().__init__(module_id)
        
        self.skill_name = "增强版打招呼技能"
        self.version = "2.0.0"
        
        # 核心组件
        self.chat_skill = None
        self.decision_engine = None
        
        # 决策配置
        self.greeting_config = {
            "decision_threshold": 0.7,
            "max_daily_greetings": 3,
            "greeting_intervals": {
                "same_user_min_hours": 18,
                "different_user_min_minutes": 30
            },
            "relationship_weights": {
                "best_friend": 0.9,
                "close_friend": 0.8,
                "friend": 0.6,
                "acquaintance": 0.4,
                "stranger": 0.1
            }
        }
        
        # 打招呼历史记录
        self.greeting_history = {}
        self.last_greeting_time = {}
        
        logger.info(f"🌅 {self.skill_name} v{self.version} 初始化完成")
    
    async def initialize(self) -> bool:
        """初始化技能组件"""
        try:
            # 初始化chat skill - ChatSkill在__init__中已完成初始化
            from cognitive_modules.skills.chat_skill import ChatSkill
            self.chat_skill = ChatSkill()
            
            # ChatSkill在__init__中已完成所有初始化，不需要额外调用initialize
            if self.chat_skill and hasattr(self.chat_skill, 'enabled') and self.chat_skill.enabled:
                logger.info("✅ Chat skill已集成")
            else:
                logger.warning("⚠️ Chat skill集成失败或未启用")
                # 不返回False，允许继续初始化，使用默认回复
            
            # 获取其他组件
            from utilities.singleton_manager import get_silent
            self.decision_engine = get_silent("yanran_decision_engine")
            
            # 加载历史记录
            await self._load_greeting_history()
            
            logger.success(f"✅ {self.skill_name} 初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ {self.skill_name} 初始化失败: {e}")
            # 即使初始化失败，也返回True，使用默认功能
            logger.info("🔄 将使用默认打招呼功能")
            return True
    
    async def autonomous_greeting_decision(self) -> Optional[GreetingDecision]:
        """数字生命自主决策打招呼"""
        try:
            logger.info("🤔 开始自主决策打招呼...")
            
            # 获取候选用户并计算得分
            candidate_users = await self._get_candidate_users()
            if not candidate_users:
                return None
            
            user_scores = []
            for user in candidate_users:
                score = await self._calculate_greeting_score(user)
                if score["total_score"] > self.greeting_config["decision_threshold"]:
                    user_scores.append((user, score))
            
            if not user_scores:
                return None
            
            # 选择最佳候选用户
            user_scores.sort(key=lambda x: x[1]["total_score"], reverse=True)
            best_user, best_score = user_scores[0]
            
            decision = GreetingDecision(
                target_user_id=best_user["user_id"],
                target_user_name=best_user["user_name"],
                relationship_type=best_user["relationship_type"],
                greeting_context=await self._build_greeting_context(best_user),
                confidence_score=best_score["total_score"],
                reasoning=best_score["reasoning"]
            )
            
            logger.success(f"✅ 自主决策打招呼: {decision.target_user_name}")
            return decision
            
        except Exception as e:
            logger.error(f"❌ 自主决策打招呼失败: {e}")
            return None
    
    def generate_greeting_content(self, user_name: str, time_period: str = "morning") -> str:
        """通过chat skill生成AI打招呼内容（同步版本）"""
        try:
            logger.info(f"🎨 为 {user_name} 生成AI打招呼内容...")
            
            # 检查Chat Skill是否可用
            if not self.chat_skill:
                logger.error("Chat skill未初始化")
                return f"早上好，{user_name}！今天过得怎么样？"
            
            # 构建个性化打招呼提示词
            greeting_prompt = f"""请为用户"{user_name}"生成一个个性化的打招呼内容。
当前时间段：{time_period}

要求：
1. 内容要自然、温暖、有亲和力
2. 体现数字生命林嫣然的个性
3. 不要过于正式，要有温度
4. 长度控制在30字以内

请直接生成打招呼内容，不要额外说明。"""
            
            # 调用chat skill（使用同步方式）
            try:
                chat_result = self.chat_skill.execute(
                    input_text=greeting_prompt,
                    user_id=f"greeting_user_{int(time.time())}",
                    session_id=f"greeting_session_{int(time.time())}"
                )
                
                # 提取生成内容
                generated_content = ""
                if isinstance(chat_result, dict):
                    generated_content = (
                        chat_result.get("response", "") or 
                        chat_result.get("content", "") or
                        chat_result.get("result", "") or
                        chat_result.get("text", "")
                    )
                elif isinstance(chat_result, str):
                    generated_content = chat_result
                
                if generated_content and generated_content.strip():
                    logger.success(f"✅ AI打招呼内容生成成功: {generated_content}")
                    return generated_content.strip()
                else:
                    logger.warning(f"Chat skill返回空内容，使用默认内容")
                    
            except Exception as chat_error:
                logger.error(f"Chat skill调用失败: {chat_error}")
            
            # 如果AI生成失败，返回默认内容
            default_greetings = {
                "morning": f"早上好，{user_name}！新的一天开始了，希望你今天心情愉快～",
                "afternoon": f"下午好，{user_name}！今天过得怎么样？要不要聊聊天放松一下？",
                "evening": f"晚上好，{user_name}！忙碌了一天，记得好好休息哦～"
            }
            
            return default_greetings.get(time_period, default_greetings["morning"])
            
        except Exception as e:
            logger.error(f"❌ 生成AI打招呼内容失败: {e}")
            return f"你好，{user_name}！很高兴和你聊天～"
    
    async def execute_greeting(self, decision: GreetingDecision, content: GreetingContent) -> bool:
        """执行打招呼"""
        try:
            logger.info(f"📤 执行打招呼: {decision.target_user_name}")
            
            # 🔥 P0级别修复：直接使用WeChat统一推送服务，修复事件循环问题
            try:
                from services.wechat_unified_push_service import get_wechat_unified_push_service
                wechat_push_service = get_wechat_unified_push_service()
                
                # 🔥 P0级别调试：添加详细的WeChat推送调试信息
                logger.info(f"🔍 早安问候WeChat推送服务状态: {'可用' if wechat_push_service else '不可用'}")
                
                if not wechat_push_service:
                    logger.error("早安问候WeChat推送服务不可用")
                    return False
                
                # 构建推送元数据
                metadata = {
                    "greeting_decision": decision.__dict__,
                    "greeting_content": content.__dict__,
                    "generation_info": {
                        "skill_name": self.skill_name,
                        "version": self.version,
                        "ai_generated": True
                    },
                    "disable_humanized_delay": True,  # 🔥 修复：禁用人性化延迟
                    "immediate_send": True  # 🔥 修复：立即发送
                }
                
                # 🔥 P0级别调试：记录推送参数
                logger.info(f"🔍 早安问候WeChat推送参数:")
                logger.info(f"   消息类型: morning_greeting")
                logger.info(f"   目标用户: {decision.target_user_id}")
                logger.info(f"   消息内容: {content.content[:50]}...")
                logger.info(f"   优先级: normal")
                logger.info(f"   元数据: {metadata}")
                
                # 🔥 老王修复：检查事件循环状态，避免"no running event loop"错误
                import threading
                import asyncio
                current_thread_name = threading.current_thread().name
                logger.info(f"🔍 早安问候当前线程: {current_thread_name}")
                
                # 检查是否是调度器线程或其他非事件循环线程
                if "scheduler" in current_thread_name.lower() or "universal" in current_thread_name.lower():
                    # 调度器线程中，没有运行中的事件循环，创建新的事件循环处理WeChat推送
                    logger.info("没有运行中的事件循环，创建新的事件循环处理WeChat推送")
                    
                    def run_push_in_new_loop():
                        """在新的事件循环中运行推送"""
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            logger.info(f"🚀 在新事件循环中开始WeChat推送执行...")
                            
                            result = new_loop.run_until_complete(
                                wechat_push_service.push_message(
                                    message_type="morning_greeting",
                                    content=content.content,
                                    target_user_id=decision.target_user_id,
                                    message_level="user",
                                    priority="normal",
                                    metadata=metadata
                                )
                            )
                            
                            logger.info(f"🔍 WeChat推送结果: {result}")
                            return result
                            
                        except Exception as push_e:
                            import traceback
                            logger.error(f"早安问候WeChat推送执行失败: {push_e}")
                            logger.error(f"早安问候WeChat推送错误详情: {traceback.format_exc()}")
                            return False
                        finally:
                            new_loop.close()
                    
                    # 使用线程池执行
                    import concurrent.futures
                    if not hasattr(self, '_push_executor'):
                        self._push_executor = concurrent.futures.ThreadPoolExecutor(
                            max_workers=2, thread_name_prefix="WeChatPush"
                        )
                    
                    future = self._push_executor.submit(run_push_in_new_loop)
                    logger.info("早安问候WeChat推送任务已提交到线程池（新事件循环）")
                    
                    # 等待结果
                    try:
                        success = future.result(timeout=30)
                        if success:
                            logger.success(f"✅ 早安问候已推送到WeChat: {decision.target_user_name}")
                            await self._record_greeting_history(decision, content)
                        else:
                            logger.warning(f"⚠️ 早安问候WeChat推送失败: {decision.target_user_name}")
                        return success
                    except Exception as e:
                        logger.error(f"早安问候WeChat推送等待结果失败: {e}")
                        return False
                        
                else:
                    # 非调度器线程，检查事件循环状态
                    try:
                        current_loop = asyncio.get_running_loop()
                        # 在现有事件循环中创建任务
                        task = current_loop.create_task(
                            wechat_push_service.push_message(
                                message_type="morning_greeting",
                                content=content.content,
                                target_user_id=decision.target_user_id,
                                message_level="user",
                                priority="normal",
                                metadata=metadata
                            )
                        )
                        # 等待任务完成
                        result = await task
                        if result:
                            logger.success(f"✅ 早安问候已推送到WeChat: {decision.target_user_name}")
                            await self._record_greeting_history(decision, content)
                        else:
                            logger.warning(f"⚠️ 早安问候WeChat推送失败: {decision.target_user_name}")
                        return result
                    except RuntimeError:
                        # 没有运行中的事件循环，跳过推送
                        logger.debug("没有运行中的事件循环，跳过WeChat推送")
                        return False
                    
            except Exception as wechat_e:
                logger.error(f"❌ 早安问候WeChat推送失败: {wechat_e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 执行打招呼失败: {e}")
            return False
    
    async def run_autonomous_greeting_cycle(self) -> Dict[str, Any]:
        """运行完整的自主打招呼周期"""
        try:
            logger.info("🌅 开始自主打招呼周期...")
            start_time = time.time()
            
            # 1. 自主决策
            decision = await self.autonomous_greeting_decision()
            if not decision:
                return {
                    "success": True,
                    "action": "no_greeting_needed",
                    "message": "当前不需要打招呼",
                    "execution_time": time.time() - start_time
                }
            
            # 2. 生成内容
            content = await self.generate_greeting_content(decision.target_user_name, self._get_time_period(datetime.now().hour))
            if not content:
                return {
                    "success": False,
                    "action": "content_generation_failed",
                    "execution_time": time.time() - start_time
                }
            
            # 3. 执行打招呼
            execution_success = await self.execute_greeting(decision, GreetingContent(content=content, tone="warm", personalization_level=0.8, creativity_score=0.7, context_relevance=0.9))
            
            return {
                "success": execution_success,
                "action": "greeting_executed" if execution_success else "greeting_failed",
                "decision": decision.__dict__,
                "content": GreetingContent(content=content, tone="warm", personalization_level=0.8, creativity_score=0.7, context_relevance=0.9).__dict__,
                "execution_time": time.time() - start_time
            }
            
        except Exception as e:
            logger.error(f"❌ 自主打招呼周期异常: {e}")
            return {
                "success": False,
                "action": "cycle_exception",
                "error": str(e),
                "execution_time": time.time() - start_time
            }
    
    # ========== 私有辅助方法 ==========
    
    async def _get_candidate_users(self) -> List[Dict[str, Any]]:
        """获取候选用户列表"""
        return [
            {
                "user_id": "user_001",
                "user_name": "测试用户1", 
                "relationship_type": "friend",
                "last_interaction": time.time() - 86400,
                "activity_level": 0.8
            }
        ]
    
    async def _calculate_greeting_score(self, user: Dict[str, Any]) -> Dict[str, Any]:
        """计算用户的打招呼得分"""
        try:
            # 关系权重
            relationship_score = self.greeting_config["relationship_weights"].get(
                user["relationship_type"], 0.5
            )
            
            # 时间因子
            time_since_last = time.time() - user.get("last_interaction", 0)
            time_score = min(time_since_last / 86400, 1.0)
            
            # 活跃度因子
            activity_score = user.get("activity_level", 0.5)
            
            # 打招呼间隔因子
            user_id = user["user_id"]
            last_greeting = self.last_greeting_time.get(user_id, 0)
            greeting_interval = time.time() - last_greeting
            min_interval = self.greeting_config["greeting_intervals"]["same_user_min_hours"] * 3600
            
            if greeting_interval < min_interval:
                interval_score = 0.0
            else:
                interval_score = min(greeting_interval / (min_interval * 2), 1.0)
            
            # 综合得分
            total_score = (
                relationship_score * 0.3 +
                time_score * 0.2 +
                activity_score * 0.2 +
                interval_score * 0.3
            )
            
            reasoning = f"关系:{relationship_score:.2f}, 时间:{time_score:.2f}, 活跃:{activity_score:.2f}, 间隔:{interval_score:.2f}"
            
            return {
                "total_score": total_score,
                "reasoning": reasoning
            }
            
        except Exception as e:
            logger.error(f"计算打招呼得分失败: {e}")
            return {"total_score": 0.0, "reasoning": "计算失败"}
    
    async def _build_greeting_context(self, user: Dict[str, Any]) -> Dict[str, Any]:
        """构建打招呼上下文"""
        current_time = datetime.now()
        return {
            "user_info": user,
            "time_context": {
                "current_time": current_time.isoformat(),
                "hour": current_time.hour,
                "time_period": self._get_time_period(current_time.hour)
            },
            "relationship_context": {
                "type": user["relationship_type"]
            }
        }
    
    async def _build_chat_context(self, decision: GreetingDecision) -> Dict[str, Any]:
        """构建chat skill的输入上下文"""
        return {
            "skill_type": "greeting",
            "task": "generate_personalized_greeting",
            "target_user": {
                "user_id": decision.target_user_id,
                "user_name": decision.target_user_name,
                "relationship": decision.relationship_type
            },
            "greeting_context": decision.greeting_context,
            "requirements": {
                "tone": "warm_and_natural",
                "personalization": "high",
                "creativity": "moderate",
                "avoid_repetition": True
            },
            "yanran_personality": {
                "traits": ["温暖", "聪慧", "体贴", "有趣"],
                "speaking_style": "自然亲切"
            }
        }
    
    async def _analyze_content_quality(self, content: str, decision: GreetingDecision) -> Dict[str, Any]:
        """分析生成内容的质量"""
        analysis = {
            "tone": "warm",
            "personalization_level": 0.8,
            "creativity_score": 0.7,
            "context_relevance": 0.9
        }
        
        if decision.target_user_name in content:
            analysis["personalization_level"] += 0.1
        
        return analysis
    
    async def _record_greeting_history(self, decision: GreetingDecision, content: GreetingContent):
        """记录打招呼历史"""
        try:
            user_id = decision.target_user_id
            self.last_greeting_time[user_id] = time.time()
            
            if user_id not in self.greeting_history:
                self.greeting_history[user_id] = []
            
            history_record = {
                "timestamp": time.time(),
                "content": content.content,
                "decision_confidence": decision.confidence_score
            }
            
            self.greeting_history[user_id].append(history_record)
            
            # 保持历史记录不超过10条
            if len(self.greeting_history[user_id]) > 10:
                self.greeting_history[user_id] = self.greeting_history[user_id][-10:]
            
            await self._save_greeting_history()
            
        except Exception as e:
            logger.error(f"记录打招呼历史失败: {e}")
    
    async def _load_greeting_history(self):
        """加载打招呼历史"""
        try:
            history_file = os.path.join(PROJECT_ROOT, "data", "greeting_history.json")
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.greeting_history = data.get("history", {})
                    self.last_greeting_time = data.get("last_times", {})
            
        except Exception as e:
            logger.error(f"加载打招呼历史失败: {e}")
    
    async def _save_greeting_history(self):
        """保存打招呼历史"""
        try:
            history_file = os.path.join(PROJECT_ROOT, "data", "greeting_history.json")
            os.makedirs(os.path.dirname(history_file), exist_ok=True)
            
            data = {
                "history": self.greeting_history,
                "last_times": self.last_greeting_time,
                "last_updated": time.time()
            }
            
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存打招呼历史失败: {e}")
    
    def _get_time_period(self, hour: int) -> str:
        """获取时间段"""
        if 5 <= hour < 9:
            return "early_morning"
        elif 9 <= hour < 12:
            return "morning"
        elif 12 <= hour < 14:
            return "noon"
        elif 14 <= hour < 18:
            return "afternoon"
        elif 18 <= hour < 22:
            return "evening"
        else:
            return "night"


def get_enhanced_greeting_skill(module_id: str = "enhanced_greeting_skill") -> EnhancedGreetingSkill:
    """获取增强版打招呼技能实例"""
    return EnhancedGreetingSkill(module_id)


# 🔥 新增：添加get_instance函数以匹配测试脚本期望的接口
def get_instance(module_id: str = "enhanced_greeting_skill") -> EnhancedGreetingSkill:
    """获取增强版打招呼技能实例 - 标准接口"""
    return EnhancedGreetingSkill(module_id)


# ========== 测试代码 ==========

async def test_enhanced_greeting():
    """测试增强版打招呼功能"""
    print("🧪 测试增强版打招呼技能...")
    
    skill = get_enhanced_greeting_skill()
    await skill.initialize()
    
    # 测试自主打招呼周期
    result = await skill.run_autonomous_greeting_cycle()
    print(f"测试结果: {result}")

if __name__ == "__main__":
    asyncio.run(test_enhanced_greeting()) 