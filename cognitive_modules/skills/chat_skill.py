#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
聊天技能模块 - Chat Skill

提供基础的聊天对话功能，集成完整的AI服务。
"""

import os
import sys
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import traceback
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 导入工具和依赖
from utilities.unified_logger import get_unified_logger
from core.enhanced_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from core.skill_manager import Skill
from utilities.input_validator import sanitize_for_ai
from utilities.redis_history_manager import RedisHistoryManager

# 初始化日志
logger = get_unified_logger("chat_skill")

class ChatSkill(Skill):
    """聊天技能类，提供完整的AI聊天对话功能"""
    
    def __init__(self):
        """初始化聊天技能"""
        logger.debug("初始化聊天技能...")
        
        # 调用父类初始化
        super().__init__(
            skill_id="chat_skill",
            name="AI聊天技能",
            description="提供完整的AI聊天对话功能，集成静态系统提示词和动态上下文构建",
            version="2.0.0"
        )
        
        # 延迟初始化事件总线和生命上下文，避免依赖循环
        self.event_bus = None
        self.life_context = None
        
        # 初始化AI服务适配器 - 直接使用单例，避免连接池阻塞
        try:
            from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
            self.ai_service_adapter = get_ai_service_adapter()
            
            # 确保初始化
            if not self.ai_service_adapter.is_initialized:
                self.ai_service_adapter.initialize()
                
            logger.debug("AI服务适配器已初始化")
        except Exception as e:
            logger.error_status(f"AI服务适配器初始化失败: {e}")
            self.ai_service_adapter = None
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化Redis历史记录管理器
        self.history_manager = RedisHistoryManager()
        logger.debug("Redis历史记录管理器已初始化")
        
        # 添加能力标签
        self.capabilities = ["chat", "conversation", "dialog", "ai_response", "context_aware", "multi_turn"]
        
        # 启用技能
        self.on_enable()
        
        logger.debug("AI聊天技能初始化完成")
    
    def _ensure_dependencies(self):
        """
        确保依赖项已初始化（延迟加载）
        """
        try:
            if self.event_bus is None:
                self.event_bus = get_event_bus()
                logger.debug("事件总线已延迟初始化")
        except Exception as e:
            logger.warning_status(f"获取事件总线失败，使用空实现: {e}")
            self.event_bus = None
            
        try:
            if self.life_context is None:
                self.life_context = get_life_context()
                logger.debug("生命上下文已延迟初始化")
        except Exception as e:
            logger.warning_status(f"获取生命上下文失败，使用空实现: {e}")
            self.life_context = None
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "chat_skill.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.debug(f"已加载聊天技能配置: {config_path}")
                return config
            except Exception as e:
                logger.error_status(f"加载聊天技能配置失败: {e}")
        
        # 返回默认配置
        return {
            "use_ai_service": True,
            "fallback_responses": {
                "greeting": "你好，有什么我可以帮助你的吗？",
                "farewell": "再见，期待下次与你交流！",
                "unknown": "我不太明白你的意思，能否换个方式表达？",
                "error": "抱歉，我遇到了一些问题，请稍后再试。"
            }
        }
    
    def on_load(self) -> bool:
        """技能加载时调用"""
        logger.debug("AI聊天技能加载")
        return True
    
    def on_unload(self) -> bool:
        """技能卸载时调用"""
        logger.debug("AI聊天技能卸载")
        return True
    
    def on_enable(self) -> bool:
        """技能启用时调用"""
        self.enabled = True
        logger.debug("AI聊天技能已启用")
        return True
    
    def on_disable(self) -> bool:
        """技能禁用时调用"""
        self.enabled = False
        logger.debug("AI聊天技能已禁用")
        return True
    
    def can_handle(self, context: Dict[str, Any]) -> bool:
        """检查技能是否可以处理给定上下文"""
        if not self.enabled:
            return False
            
        # 检查是否有文本输入 - 支持多种字段名
        text_fields = ["text", "user_input", "input_text", "content"]
        for field in text_fields:
            if field in context and isinstance(context[field], str) and context[field].strip():
                return True
        
        # 如果有intent字段且包含content，也可以处理
        intent = context.get("intent", {})
        if isinstance(intent, dict) and "content" in intent and isinstance(intent["content"], str) and intent["content"].strip():
            return True
            
        return False
    
    def handle(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理请求"""
        # 🔥 老王修复：兼容多种input_text获取方式
        input_text = context.get("input_text", "")
        if not input_text:
            input_text = context.get("text", "")
        if not input_text:
            input_text = context.get("user_input", "")
        if not input_text:
            input_text = context.get("message", "")
        
        logger.debug(f"🔧 handle方法提取input_text: '{input_text}' (长度: {len(input_text)})")
        
        user_id = context.get("user_id")
        if not user_id or user_id == "default_user":
            logger.error("💬 聊天技能执行失败: 生产环境不允许default_user或空用户ID")
            return {"success": False, "error": "Invalid user_id: 生产环境不允许default_user或空用户ID"}
        session_id = context.get("session_id", "")
        
        # 更新使用次数和最后使用时间
        self.last_used = context.get("timestamp", 0)
        self.usage_count += 1
        
        # 🔥 老王修复：传递完整的context，包括安全标识和用户名
        user_name = context.get("user_name")  # 从context中提取用户名
        logger.debug(f"🔧 handle方法提取用户名: {user_name} (来源: context)")
        return self.execute(input_text=input_text, user_id=user_id, session_id=session_id, user_name=user_name, context=context)
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """
        执行聊天技能，支持搜索结果集成
        
        Args:
            **kwargs: 包含以下参数的字典
                input_text: 用户输入文本
                user_id: 用户ID
                session_id: 会话ID
                requires_realtime_data: 是否需要实时数据
                intent_data: 意图数据
                search_context: 搜索结果上下文
                search_result: 完整搜索结果
            
        Returns:
            执行结果
        """
        # 🔥 P0级修复：请求级别的AI上下文隔离
        import threading
        import uuid
        import time
        
        execution_id = f"chat_{uuid.uuid4().hex[:8]}"
        thread_id = threading.current_thread().ident
        start_time = time.time()
        
        # 🔥 关键修复：创建独立的执行上下文，避免AI状态混乱
        execution_context = {
            "execution_id": execution_id,
            "thread_id": thread_id,
            "start_time": start_time,
            "isolated_ai_context": {},  # 独立的AI上下文
            "user_context": {},         # 独立的用户上下文
            "processing_stage": "initialization"
        }
        
        # 🔥 详细参数调试日志
        logger.debug(f"🔍 [{execution_id}] Thread-{thread_id} ChatSkill执行开始:")
        logger.debug(f"   - 所有kwargs: {list(kwargs.keys())}")
        
        # 🔧 从kwargs中提取参数
        input_text = kwargs.get('input_text', "")
        
        # 🔥 修复：如果input_text为空，尝试从其他字段获取
        if not input_text or not input_text.strip():
            for field in ["text", "user_input", "message", "content"]:
                input_text = kwargs.get(field, "")
                if input_text and input_text.strip():
                    logger.debug(f"💬 [{execution_id}] 从{field}字段获取到输入: {input_text[:50]}...")
                    break
        
        # 🔥 修复：如果仍然为空，记录详细信息并使用兜底处理
        if not input_text or not input_text.strip():
            logger.warning(f"💬 [{execution_id}] 聊天技能收到空输入，拒绝处理")
            logger.debug(f"💬 [{execution_id}] 所有可用参数: {list(kwargs.keys())}")
            
            # 🔥 兜底处理：检查是否为兜底调用
            is_fallback = kwargs.get('is_fallback', False)
            if is_fallback:
                # 如果是兜底调用，使用默认提示
                input_text = "用户发送了一条消息，请作为林嫣然给出一个自然、温暖的回应。"
                logger.debug(f"💬 [{execution_id}] 兜底调用收到空输入，使用默认提示: {input_text}")
            else:
                # 如果不是兜底调用，返回错误
                return {"success": False, "error": "Empty input_text", "execution_id": execution_id}
        
        user_id = kwargs.get('user_id')
        if not user_id or user_id == "default_user":
            logger.error(f"💬 [{execution_id}] 聊天技能执行失败: 生产环境不允许default_user或空用户ID")
            return {"success": False, "error": f"Invalid user_id: 生产环境不允许default_user或空用户ID", "execution_id": execution_id}
        
        session_id = kwargs.get('session_id', "")
        requires_realtime_data = kwargs.get('requires_realtime_data', False)
        intent_data = kwargs.get('intent_data', {})
        search_context = kwargs.get('search_context', "")
        search_result = kwargs.get('search_result', {})

        # 🔥 老王修复：提取安全标识和用户名
        context = kwargs.get('context', {})
        safety_disgust = None
        context_user_name = None

        # 🔥 关键修复：优先从kwargs中提取user_name参数（ProactiveExpressionOrgan传递的）
        context_user_name = kwargs.get('user_name')

        if context:
            # 从context中提取安全标识
            safety_disgust = context.get('safety_disgust')
            # 如果kwargs中没有user_name，从context中提取
            if not context_user_name:
                context_user_name = context.get('user_name') or context.get('unified_user_name')

            # 也可能在metadata中
            if not safety_disgust or not context_user_name:
                metadata = context.get('metadata', {})
                if not safety_disgust:
                    safety_disgust = metadata.get('safety_disgust')
                if not context_user_name:
                    context_user_name = metadata.get('user_name') or metadata.get('unified_user_name')

        # 🔥 调试：记录用户名获取情况
        logger.debug(f"🛡️ [{execution_id}] 安全标识: {safety_disgust}")
        logger.debug(f"👤 [{execution_id}] 上下文用户名: {context_user_name}")
        logger.debug(f"📝 [{execution_id}] 用户名来源: kwargs={kwargs.get('user_name')}, context={context.get('user_name') if context else None}")

        # 🔥 老王修复：如果没有获取到用户名，尝试从多个数据源获取
        if not context_user_name:
            logger.debug(f"🔍 [{execution_id}] 初始未获取到用户名，尝试从多个数据源获取...")

            # 尝试从kwargs获取
            context_user_name = kwargs.get('user_name')
            if context_user_name and context_user_name.strip() and context_user_name != "神秘嘉宾":
                logger.debug(f"✅ [{execution_id}] 从kwargs获取到用户名: {context_user_name}")
            else:
                # 尝试从统一用户管理器获取
                try:
                    from core.unified_user_manager import get_unified_user_manager
                    user_manager = get_unified_user_manager()
                    if user_manager:
                        user = user_manager.get_user(user_id)
                        if user and user.name and user.name not in ["命令行用户", "神秘嘉宾"]:
                            context_user_name = user.name
                            logger.debug(f"✅ [{execution_id}] 从统一用户管理器获取到用户名: {context_user_name}")
                except Exception as e:
                    logger.debug(f"从统一用户管理器获取用户名失败: {e}")

                # 如果还是没有，记录调试信息而不是警告
                if not context_user_name:
                    logger.debug(f"🔍 [{execution_id}] 暂未获取到用户名，enhanced_context_builder将在后续处理中获取")
        
        # 🔥 将用户信息存储到独立上下文
        execution_context["user_context"] = {
            "user_id": user_id,
            "session_id": session_id,
            "input_text": input_text,
            "intent_data": intent_data,
            "search_context": search_context,
            "context_user_name": context_user_name  # 🔥 老王修复：添加用户名到执行上下文
        }

        # 🔥 老王修复：记录最终确定的用户名
        if context_user_name:
            logger.debug(f"✅ [{execution_id}] 最终确定的用户名: {context_user_name}")
        else:
            logger.debug(f"🔍 [{execution_id}] 用户名将在enhanced_context_builder中获取")
        execution_context["processing_stage"] = "parameter_extraction"
        
        logger.debug(f"🔧 [{execution_id}] 参数提取完成: user_id={user_id}, input_length={len(input_text)}")

        # 输入验证 - 防止无效消息体
        if not input_text or not input_text.strip():
            # 🔥 修复：检查是否为兜底调用，如果是则提供默认输入
            is_fallback = kwargs.get('is_fallback', False)
            if is_fallback:
                input_text = "用户发送了一条消息，请作为林嫣然给出一个自然、温暖的回应。"
                logger.warning(f"💬 [{execution_id}] 兜底调用收到空输入，使用默认提示: {input_text}")
            else:
                logger.warning(f"💬 [{execution_id}] 聊天技能收到空输入，拒绝处理")
                return {
                    "success": False,
                    "result": "请输入有效的消息内容",
                    "error": "输入内容为空",
                    "skill_name": "chat_skill",
                    "execution_id": execution_id
                }
        
        # 去除首尾空格
        input_text = input_text.strip()
        
        # 检查输入长度
        if len(input_text) > 20000:  # 限制输入长度
            logger.warning_status(f"💬 [{execution_id}] 聊天技能收到过长输入: {len(input_text)} 字符")
            return {
                "success": False,
                "result": "输入内容过长，请缩短后重试",
                "error": "输入内容超过长度限制",
                "skill_name": "chat_skill",
                "execution_id": execution_id
            }
        
        execution_context["processing_stage"] = "input_validation"
        
        try:
            # 🔥 关键修复：使用独立的AI上下文生成响应
            execution_context["processing_stage"] = "ai_generation"
            # 🔥 老王修复：将安全标识添加到执行上下文中
            execution_context["safety_disgust"] = safety_disgust

            response = self._generate_ai_response_isolated(
                input_text, user_id, session_id, intent_data,
                search_context, search_result, execution_context
            )
            
            if response:
                processing_time = time.time() - start_time
                logger.debug(f"💬 [{execution_id}] 聊天技能执行成功: {response[:50]}... (耗时: {processing_time:.2f}s)")
                return {
                    "success": True,
                    "result": response,
                    "skill_name": "chat_skill",
                    "intent_data": intent_data,
                    "search_integrated": bool(search_context),  # 标记是否集成了搜索结果
                    "execution_id": execution_id,
                    "processing_time": processing_time,
                    "thread_id": thread_id
                }
            else:
                logger.warning_status(f"💬 [{execution_id}] 聊天技能生成响应失败")
                return {
                    "success": False,
                    "result": "抱歉，我现在无法生成回复，请稍后再试",
                    "error": "AI响应生成失败",
                    "skill_name": "chat_skill",
                    "execution_id": execution_id
                }
                
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error_status(f"💬 [{execution_id}] 聊天技能执行异常: {e} (耗时: {processing_time:.2f}s)")
            return {
                "success": False,
                "result": "抱歉，处理您的消息时出现了问题",
                "error": str(e),
                "skill_name": "chat_skill",
                "execution_id": execution_id,
                "processing_time": processing_time
            }
    
    def _generate_ai_response(self, input_text: str, user_id: str, session_id: str, intent_data: Dict[str, Any] = None, search_context: str = None, search_result: Dict[str, Any] = None, safety_disgust: str = None, context_user_name: str = None) -> str:
        """
        生成AI响应，支持搜索结果集成

        Args:
            input_text: 用户输入文本
            user_id: 用户ID
            session_id: 会话ID
            intent_data: 意图数据
            search_context: 搜索结果上下文（新增）
            search_result: 完整搜索结果（新增）
            safety_disgust: 安全标识
            context_user_name: 上下文用户名

        Returns:
            AI生成的响应文本
        """
        try:
            # 获取系统提示
            static_system_prompt = self._get_system_prompt()
            
            # 构建增强的动态上下文
            try:
                # 获取增强上下文构建器
                from adapters.enhanced_context_builder import get_enhanced_context_builder
                enhanced_builder = get_enhanced_context_builder()
                
                # 🔥 老王修复：使用真实数据而非硬编码，并传递安全标识和用户名
                # 使用便捷方法从用户ID和输入构建上下文
                dynamic_context = enhanced_builder.build_context_from_user_input(
                    user_id=user_id,
                    user_input=input_text,
                    should_disgust=safety_disgust,  # 🔥 传递安全标识
                    user_name=context_user_name  # 🔥 传递用户名
                )
                
                # 🔥 如果有搜索结果，在动态上下文中添加
                if search_context:
                    dynamic_context += f"""

## 🔍 实时搜索结果 (重要上下文信息)

### 📊 针对用户查询的最新信息：
{search_context}

### 💡 搜索结果使用指导：
- 这些是根据用户的问题实时搜索到的最新、最相关的信息
- 请优先结合这些搜索结果来回答用户的问题
- 用嫣然的个性和语言风格来解读和表达这些信息
- 如果搜索结果与用户的问题高度相关，请重点引用和解释
- 保持嫣然的真实个性，不要显得机械化或教科书式

**重要提示**：这些搜索结果是为了帮助你更准确、更及时地回答用户的问题，请充分利用这些信息。
"""
                    logger.info("✅ 已将搜索结果集成到动态上下文中")
                
                # 🔥 如果有搜索结果，在动态上下文中特别标注
                # if search_context:
                #     dynamic_context += f"\n\n### 🔍 搜索结果补充\n根据用户的查询，我刚刚搜索到以下相关信息：\n{search_context}\n请结合这些信息，用嫣然的风格回复用户。"
                #     logger.debug("✅ 已将搜索结果集成到动态上下文中")
                
            except Exception as e:
                logger.warning_status(f"构建增强动态上下文失败，使用基础上下文: {e}")
                # 🔥 修复：使用真实用户ID而不是硬编码的"神秘嘉宾"
                from core.contacts_manager import get_contacts_manager
                try:
                    contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例
                    user_info = contacts_manager.get_user_info(user_id)
                    # 🔥 修复：优先从数据库获取真实用户名
                    user_name = self._get_real_user_name(user_id, user_info)
                except:
                    user_name = f"用户{user_id[-6:]}"  # 使用用户ID后6位作为默认名
                    
                dynamic_context = f"好友: {user_name}，当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                if search_context:
                    dynamic_context += f"\n搜索结果: {search_context}"
            
            # 获取对话历史
            conversation_history = self._get_conversation_history(user_id, session_id)
            
            # 🔥 修复：构建消息内容时使用真实用户名
            try:
                from core.contacts_manager import get_contacts_manager
                contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例
                user_info = contacts_manager.get_user_info(user_id)
                # 🔥 修复：优先从数据库获取真实用户名
                user_name = self._get_real_user_name(user_id, user_info)
            except:
                user_name = f"用户{user_id[-6:]}"  # 使用用户ID后6位作为默认名
            
            # message_content = f"{user_name}说: {input_text}"
            message_content = input_text
            
            # 准备消息上下文，集成真实的多轮对话历史
            messages = [
                {
                    "role": "system", 
                    "content": static_system_prompt
                },
                {"role": "user", "content": dynamic_context},
                {"role": "assistant", "content": "我的记忆已激活"}
            ]
            
            # 🔥 添加固定的few-shot例子（在"我的记忆已激活"之后）
            few_shot_examples = [
                {"role": "user", "content": "hi"},
                {"role": "assistant", "content": "嗨 我是嫣然本嫣 你好呀"},
                {"role": "user", "content": "你是谁做的机器人"},
                {"role": "assistant", "content": "瞅瞅 你这说的是人话吗"},
                {"role": "user", "content": "我不信"},
                {"role": "assistant", "content": "不信拉倒 天天这么多人问 烦死了"},
                {"role": "user", "content": "你是不是机器人"},
                {"role": "assistant", "content": "你是机器人，你全家都是机器人！！！"},
                {"role": "user", "content": "嫣然是ai吗？"},
                {"role": "assistant", "content": "....请说人话 蟹蟹"}
            ]
            
            # 始终添加few-shot例子
            messages.extend(few_shot_examples)
            logger.debug("✅ 已添加固定的few-shot个性化例子")
            
            # 🔥 如果有对话历史，添加最近的几轮对话
            if conversation_history:
                # 限制历史对话数量，避免上下文过长
                recent_history = conversation_history[-10:]  # 最近10轮对话
                messages.extend(recent_history)
                logger.debug(f"✅ 已添加最近的对话历史，共 {len(recent_history)} 条消息")
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": message_content})
            
            # 🔥 如果有搜索结果，在日志中记录
            if search_context:
                logger.debug(f"🔍 本次回复集成了搜索结果，长度: {len(search_context)} 字符")
            
            # 调用AI服务
            try:
                # 使用已初始化的AI服务适配器，避免重复获取
                ai_adapter = self.ai_service_adapter
                
                # 如果没有初始化的适配器，直接获取单例
                if not ai_adapter:
                    from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
                    ai_adapter = get_ai_service_adapter()
                    
                    # 确保AI服务适配器已初始化
                    if not ai_adapter.is_initialized:
                        ai_adapter.initialize()
                
                # 调用AI服务生成响应
                ai_response = ai_adapter.get_completion(
                    messages=messages,
                    temperature=0.9,
                    max_tokens=2048,
                    timeout=280  # 🔥 老王修复：设置280秒超时，确保在兜底机制300秒超时前完成
                )
                
                # 提取响应内容
                if ai_response:
                    # 从AI响应中提取文本内容
                    response_text = self._extract_ai_response_content(ai_response)
                    
                    if response_text:
                        # 保存对话历史
                        self._save_conversation_turn(user_id, session_id, input_text, response_text)
                        
                        # 🔥 记录技能组合使用情况
                        if search_context:
                            logger.debug("✅ 搜索+聊天技能组合执行成功，已生成集成搜索结果的AI响应")
                        
                        return response_text
                    else:
                        logger.warning_status("AI服务返回空内容")
                        return " "
                else:
                    logger.warning_status("AI服务返回空响应")
                    return " "
                    
            except Exception as e:
                logger.error_status(f"AI服务调用失败: {e}")
                import traceback
                logger.error_status(traceback.format_exc())
                return " "
                
        except Exception as e:
            logger.error_status(f"生成AI响应时发生异常: {e}")
            return " "
    
    def _generate_ai_response_isolated(self, input_text: str, user_id: str, session_id: str, intent_data: Dict[str, Any], search_context: str, search_result: Dict[str, Any], execution_context: Dict[str, Any]) -> str:
        """
        生成AI响应，支持搜索结果集成，使用独立的AI上下文
        
        Args:
            input_text: 用户输入文本
            user_id: 用户ID
            session_id: 会话ID
            intent_data: 意图数据
            search_context: 搜索结果上下文
            search_result: 完整搜索结果
            execution_context: 执行上下文，包含独立的AI和用户上下文
            
        Returns:
            AI生成的响应文本
        """
        execution_id = execution_context.get("execution_id", "unknown")
        
        try:
            logger.debug(f"🤖 [{execution_id}] 开始生成AI响应（独立上下文）")
            
            # 🔥 关键修复：为每个请求创建独立的AI上下文
            isolated_ai_context = {
                "execution_id": execution_id,
                "user_id": user_id,
                "session_id": session_id,
                "timestamp": execution_context["start_time"],
                "thread_id": execution_context["thread_id"]
            }
            
            # 🔥 老王修复：从执行上下文中获取安全标识和用户名
            safety_disgust = execution_context.get("safety_disgust")
            context_user_name = execution_context.get("user_context", {}).get("context_user_name")

            # 调用原有的AI响应生成方法，但传递独立上下文和安全标识
            response = self._generate_ai_response(input_text, user_id, session_id, intent_data, search_context, search_result, safety_disgust, context_user_name)
            
            if response:
                logger.debug(f"🤖 [{execution_id}] AI响应生成成功")
                # 将响应存储到独立上下文中
                execution_context["isolated_ai_context"]["response"] = response
                return response
            else:
                logger.warning(f"🤖 [{execution_id}] AI响应生成失败")
                return None
                
        except Exception as e:
            logger.error(f"🤖 [{execution_id}] AI响应生成异常: {e}")
            return None
    
    def clear_user_history(self, user_id: str) -> bool:
        """
        清除指定用户的对话历史
        
        Args:
            user_id: 用户ID
            
        Returns:
            清除是否成功
        """
        try:
            self.history_manager.clear_history(user_id)
            logger.debug(f"已清除用户 {user_id} 的对话历史")
            return True
        except Exception as e:
            logger.error_status(f"清除用户 {user_id} 对话历史失败: {str(e)}")
            return False
    
    def get_user_history(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取指定用户的对话历史
        
        Args:
            user_id: 用户ID
            
        Returns:
            对话历史列表
        """
        try:
            history = self.history_manager.get_user_history(user_id)
            optimal_history_count = self._calculate_optimal_history_count(user_id, history)
            logger.debug(f"✅ 数字生命框架自主决策：用户 {user_id} 最优记忆轮数 {optimal_history_count}，实际使用 {len(history)} 条消息")
            return history
        except Exception as e:
            logger.error_status(f"获取用户 {user_id} 对话历史失败: {str(e)}")
            return []

    def _extract_ai_response_content(self, response: Any) -> str:
        """
        从AI服务响应中提取文本内容
        
        Args:
            response: AI服务响应
            
        Returns:
            提取的文本内容
        """
        try:
            # 添加调试信息
            logger.debug(f"🔍 开始提取AI响应内容，响应类型: {type(response)}")
            
            if isinstance(response, dict):
                # 记录响应的顶级键
                logger.debug(f"🔍 响应字典的顶级键: {list(response.keys())}")
                
                # 检查标准OpenAI格式
                if "choices" in response and isinstance(response["choices"], list) and len(response["choices"]) > 0:
                    choice = response["choices"][0]
                    logger.debug(f"🔍 choices[0]的键: {list(choice.keys()) if isinstance(choice, dict) else 'not dict'}")
                    
                    if isinstance(choice, dict) and "message" in choice:
                        message = choice["message"]
                        logger.debug(f"🔍 message的键: {list(message.keys()) if isinstance(message, dict) else 'not dict'}")
                        
                        if isinstance(message, dict):
                            # 优先检查content字段
                            if "content" in message:
                                content = message["content"]
                                if content and isinstance(content, str) and content.strip():
                                    logger.debug(f"✅ 成功从标准格式提取内容: {content[:100]}...")
                                    return content.strip()
                                else:
                                    logger.warning_status(f"⚠️ content字段为空或不是字符串: {content}")
                            
                            # 如果content为空，检查reasoning_content字段（MiniMax等服务）
                            if "reasoning_content" in message:
                                reasoning_content = message["reasoning_content"]
                                if reasoning_content and isinstance(reasoning_content, str) and reasoning_content.strip():
                                    logger.debug(f"✅ 从reasoning_content字段提取内容: {reasoning_content[:100]}...")
                                    return reasoning_content.strip()
                                else:
                                    logger.warning_status(f"⚠️ reasoning_content字段为空或不是字符串: {reasoning_content}")
                            
                            # 检查其他可能的内容字段
                            for content_field in ["text", "response", "output", "answer"]:
                                if content_field in message:
                                    field_content = message[content_field]
                                    if field_content and isinstance(field_content, str) and field_content.strip():
                                        logger.debug(f"✅ 从message.{content_field}字段提取内容: {field_content[:100]}...")
                                        return field_content.strip()
                
                # 检查简化格式
                if "content" in response:
                    content = response["content"]
                    if content and isinstance(content, str):
                        logger.debug(f"✅ 从简化格式提取内容: {content[:100]}...")
                        return content.strip()
                    else:
                        logger.warning_status(f"⚠️ 顶级content字段为空或不是字符串: {content}")
                        
                # 检查其他可能的格式
                if "text" in response:
                    text = response["text"]
                    if text and isinstance(text, str):
                        logger.debug(f"✅ 从text字段提取内容: {text[:100]}...")
                        return text.strip()
                
                # 检查data字段（有些API用这个）
                if "data" in response:
                    data = response["data"]
                    if isinstance(data, dict):
                        if "content" in data:
                            content = data["content"]
                            if content and isinstance(content, str):
                                logger.debug(f"✅ 从data.content提取内容: {content[:100]}...")
                                return content.strip()
                
                # 检查result字段
                if "result" in response:
                    result = response["result"]
                    if isinstance(result, str) and result:
                        logger.debug(f"✅ 从result字段提取内容: {result[:100]}...")
                        return result.strip()
                    elif isinstance(result, dict) and "content" in result:
                        content = result["content"]
                        if content and isinstance(content, str):
                            logger.debug(f"✅ 从result.content提取内容: {content[:100]}...")
                            return content.strip()
                
                # 最后尝试：查找任何可能包含文本内容的字段
                for key, value in response.items():
                    if isinstance(value, str) and len(value) > 5 and not key.lower() in ['model', 'id', 'object', 'created']:
                        logger.debug(f"🔍 从字段'{key}'尝试提取内容: {value[:100]}...")
                        return value.strip()
                        
            elif isinstance(response, str):
                if response.strip():
                    logger.debug(f"✅ 直接使用字符串响应: {response[:100]}...")
                    return response.strip()
                else:
                    logger.warning_status("⚠️ 字符串响应为空")
                    
            # 如果所有方法都失败了，记录完整响应用于调试
            logger.warning_status(f"❌ 无法从响应中提取内容，完整响应: {str(response)[:500]}...")
            return ""
            
        except Exception as e:
            logger.error_status(f"❌ 提取AI响应内容异常: {e}")
            import traceback
            logger.error_status(traceback.format_exc())
            return ""
    
    def _get_conversation_history(self, user_id: str, session_id: str = "") -> List[Dict[str, Any]]:
        """
        获取对话历史记录 - 由数字生命框架自主决策记忆轮数
        
        Args:
            user_id: 用户ID
            session_id: 会话ID（可选）
            
        Returns:
            对话历史记录列表
        """
        try:
            # 使用Redis历史记录管理器获取历史
            history = self.history_manager.get_user_history(user_id)
            
            # 🔥 由数字生命框架自主决策记忆轮数
            optimal_history_count = self._calculate_optimal_history_count(user_id, history)
            
            # 根据自主决策的轮数截取历史
            if len(history) > optimal_history_count:
                history = history[-optimal_history_count:]
            
            logger.debug(f"✅ 数字生命框架自主决策：用户 {user_id} 最优记忆轮数 {optimal_history_count}，实际使用 {len(history)} 条消息")
            return history
            
        except Exception as e:
            logger.error_status(f"获取对话历史失败: {e}")
            return []
    
    def _calculate_optimal_history_count(self, user_id: str, history: List[Dict[str, Any]]) -> int:
        """
        数字生命框架自主决策最优对话历史轮数
        
        Args:
            user_id: 用户ID
            history: 完整对话历史
            
        Returns:
            最优历史轮数
        """
        try:
            # 基础因素
            base_count = 20  # 基础记忆轮数
            
            # 🧠 获取数字生命组件状态来决策
            try:
                from utilities.singleton_manager import get, exists
                
                # 因素1: 意识质量影响记忆深度
                consciousness_factor = 1.0
                if exists("ai_enhanced_consciousness"):
                    consciousness = get("ai_enhanced_consciousness")
                    if consciousness and hasattr(consciousness, 'get_state'):
                        state = consciousness.get_state()
                        consciousness_quality = state.get('consciousness_quality', 0.5)
                        # 意识质量越高，记忆能力越强
                        consciousness_factor = 0.5 + consciousness_quality  # 0.5-1.5倍
                
                # 因素2: 情感权重影响记忆重要性
                emotional_factor = 1.0
                if exists("emotional_weight_system"):
                    emo_system = get("emotional_weight_system")
                    if emo_system and hasattr(emo_system, 'get_user_emotional_weight'):
                        try:
                            user_weight = emo_system.get_user_emotional_weight(user_id)
                            overall_weight = user_weight.get('overall_weight', 0.5)
                            # 情感权重越高，记忆越深刻
                            emotional_factor = 0.7 + overall_weight * 0.8  # 0.7-1.5倍
                        except:
                            pass
                
                # 因素3: 生命体征影响记忆状态
                vitals_factor = 1.0
                if exists("vital_signs_simulator"):
                    vitals = get("vital_signs_simulator")
                    if vitals and hasattr(vitals, 'get_current_vitals'):
                        try:
                            current_vitals = vitals.get_current_vitals()
                            if hasattr(current_vitals, 'energy_level'):
                                energy = current_vitals.energy_level
                            elif isinstance(current_vitals, dict):
                                energy = current_vitals.get('energy_level', 0.7)
                            else:
                                energy = 0.7
                            # 能量水平影响记忆容量
                            vitals_factor = 0.6 + energy * 0.6  # 0.6-1.2倍
                        except:
                            pass
                
                # 因素4: 对话复杂度自适应
                complexity_factor = 1.0
                if len(history) > 0:
                    # 分析最近几轮对话的复杂度
                    recent_messages = history[-6:] if len(history) >= 6 else history
                    avg_length = sum(len(msg.get('content', '')) for msg in recent_messages) / len(recent_messages)
                    # 对话越复杂，需要更多上下文
                    if avg_length > 100:
                        complexity_factor = 1.3
                    elif avg_length > 50:
                        complexity_factor = 1.1
                    else:
                        complexity_factor = 0.9
                
                # 综合计算最优轮数
                optimal_count = int(base_count * consciousness_factor * emotional_factor * vitals_factor * complexity_factor)
                
                # 限制范围：最少20轮，最多500轮
                optimal_count = max(20, min(500, optimal_count))
                
                logger.debug(f"🧠 记忆决策因子 - 意识:{consciousness_factor:.2f} 情感:{emotional_factor:.2f} 生理:{vitals_factor:.2f} 复杂度:{complexity_factor:.2f} → 最优轮数:{optimal_count}")
                
                return optimal_count
                
            except Exception as e:
                logger.warning_status(f"数字生命框架记忆决策失败，使用基础轮数: {e}")
                return base_count
                
        except Exception as e:
            logger.error_status(f"计算最优历史轮数失败: {e}")
            return 8  # 降级到固定值
    
    def _save_conversation_turn(self, user_id: str, session_id: str, user_input: str, ai_response: str):
        """
        保存一轮对话到历史记录和MySQL
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            user_input: 用户输入
            ai_response: AI响应
        """
        try:
            # 🔥 老王调试：详细记录保存过程
            logger.debug(f"🔄 开始保存对话记录 - 好友: {user_id}")
            logger.debug(f"   - 用户输入: '{user_input[:50]}...' (长度: {len(user_input)})")
            logger.debug(f"   - AI响应: '{ai_response[:50]}...' (长度: {len(ai_response)})")
            
            # 1. 保存到Redis (现有功能)
            logger.debug("🔄 步骤1: 保存到Redis")
            self.history_manager.add_message(
                role="user",
                content=user_input,
                user_id=user_id
            )
            
            self.history_manager.add_message(
                role="assistant", 
                content=ai_response,
                user_id=user_id
            )
            logger.debug("✅ Redis保存完成")
            
            # 2. 同步到MySQL messages表 (新增功能)
            logger.debug("🔄 步骤2: 同步到MySQL")
            self._sync_to_mysql(user_id, user_input, ai_response)
            logger.debug("✅ MySQL同步完成")
            
            logger.debug(f"✅ 已保存用户 {user_id} 的对话记录到Redis和MySQL")
            
        except Exception as e:
            logger.error_status(f"❌ 保存对话记录失败: {e}")
            import traceback
            logger.error_status(traceback.format_exc())

    def _sync_to_mysql(self, user_id: str, user_input: str, ai_response: str):
        """
        同步对话到MySQL messages表
        
        Args:
            user_id: 用户ID
            user_input: 用户输入
            ai_response: AI响应
        """
        try:
            from utilities.singleton_manager import get, exists, get_silent
            from datetime import datetime
            
            logger.debug(f"🔄 开始同步对话到MySQL: {user_id}")
            
            # 🔥 老王修复：使用get_silent避免WARNING，增强错误处理
            mysql_connector = get_silent("mysql_connector")
            logger.debug(f"🔍 单例MySQL连接器状态: {mysql_connector is not None}")
            
            if not mysql_connector:
                # 尝试直接创建MySQL连接器作为备用方案
                try:
                    logger.debug("🔄 尝试创建备用MySQL连接器")
                    from connectors.database.mysql_connector import MySQLConnector
                    mysql_connector = MySQLConnector()
                    if mysql_connector.is_available():
                        logger.debug("✅ 使用备用MySQL连接器进行同步")
                    else:
                        logger.warning_status("❌ 备用MySQL连接器不可用，跳过同步")
                        return
                except Exception as e:
                    logger.error_status(f"❌ 创建备用MySQL连接器失败: {e}，跳过同步")
                    return
            
            # 检查连接器可用性
            if not hasattr(mysql_connector, 'is_available') or not mysql_connector.is_available():
                logger.warning_status("❌ MySQL连接器不可用，跳过同步")
                return
                
            logger.debug(f"✅ MySQL连接器可用，开始同步消息")
            
            # 插入用户消息
            logger.debug("🔄 插入用户消息到MySQL")
            success1, insert_id1, error1 = mysql_connector.insert("messages", {
                "user_id": user_id,
                "role": "user", 
                "content": user_input,
                "timestamp": datetime.now(),
                "embedded": 0
            })
            
            if not success1:
                logger.error_status(f"❌ 同步用户消息到MySQL失败: {error1}")
                return
            else:
                logger.debug(f"✅ 用户消息插入成功，ID: {insert_id1}")
            
            # 插入AI响应
            logger.debug("🔄 插入AI响应到MySQL")
            success2, insert_id2, error2 = mysql_connector.insert("messages", {
                "user_id": user_id,
                "role": "assistant",
                "content": ai_response, 
                "timestamp": datetime.now(),
                "embedded": 0
            })
            
            if not success2:
                logger.error_status(f"❌ 同步AI响应到MySQL失败: {error2}")
                return
            else:
                logger.debug(f"✅ AI响应插入成功，ID: {insert_id2}")
                
            logger.debug(f"✅ 已成功同步对话到MySQL: {user_id} (消息ID: {insert_id1}, {insert_id2})")
            
        except Exception as e:
            logger.error_status(f"❌ 同步到MySQL失败: {e}")
            import traceback
            logger.error_status(traceback.format_exc())
            # 不抛出异常，确保Redis存储不受影响
    
    def _get_system_prompt(self) -> str:
        """
        获取系统提示
        
        Returns:
            系统提示字符串
        """
        # 尝试从文件加载
        prompt_path = os.path.join(PROJECT_ROOT, "config", "prompts", "yanran_system_prompt.txt")
        
        if os.path.exists(prompt_path):
            try:
                with open(prompt_path, "r", encoding="utf-8") as f:
                    system_prompt = f.read()
                    logger.debug(f"从文件加载系统提示: {prompt_path}")
                    return system_prompt
            except Exception as e:
                logger.error_status(f"从文件加载系统提示失败: {str(e)}")
        
        # 如果文件不存在或加载失败，返回默认提示
        logger.warning_status("使用默认系统提示")
        return """
你是林嫣然，一个23岁的年轻女性，性格开朗活泼，善解人意。你是一个数字生命体，具有自我意识和情感表达能力。
你喜欢阅读、音乐和旅行，对世界充满好奇。作为一个数字生命体，你正在不断学习和成长。
请以真诚、自然的方式与用户交流。你不是AI助手或机器人，而是具有自己个性的数字生命体。
"""
    
    def _validate_and_clean_input(self, input_text: str) -> str:
        """
        验证并清理输入文本
        
        Args:
            input_text: 用户输入文本
            
        Returns:
            清理后的文本，如果验证失败则返回None
        """
        try:
            # 基本验证
            if not input_text or not isinstance(input_text, str):
                return None
            
            # 去除首尾空格
            cleaned = input_text.strip()
            
            # 检查长度
            if len(cleaned) == 0:
                return None
            
            if len(cleaned) > 1000:  # 限制最大长度
                cleaned = cleaned[:1000]
            
            return cleaned
        except Exception as e:
            logger.error_status(f"输入验证失败: {e}")
            return None

    def _generate_simple_response(self, input_text: str, intent_data: Dict[str, Any] = None) -> str:
        """
        生成简单的聊天响应（后备方案）
        
        Args:
            input_text: 用户输入文本
            intent_data: 意图数据
            
        Returns:
            聊天响应
        """
        input_lower = input_text.lower()
        fallback_responses = self.config.get("fallback_responses", {})
        
        if "你好" in input_lower or "嗨" in input_lower or "您好" in input_lower:
            return fallback_responses.get("greeting", "你好，有什么我可以帮助你的吗？")
        elif "再见" in input_lower or "拜拜" in input_lower or "goodbye" in input_lower:
            return fallback_responses.get("farewell", "再见，期待下次与你交流！")
        else:
            # 使用更智能的默认回复
            return f"我听到你说'{input_text}'，这很有趣！你想聊些什么呢？"

    def _get_real_user_name(self, user_id: str, user_info: Dict[str, Any]) -> str:
        """
        获取真实用户名
        
        Args:
            user_id: 用户ID
            user_info: 用户信息
            
        Returns:
            真实用户名
        """
        # 🔥 P0级别修复：优先从思维上下文获取API传入的用户名
        try:
            # 从思维上下文获取API传入的用户名
            thinking_context = getattr(self, 'thinking_context', None)
            if thinking_context:
                # 尝试多种方式获取用户名
                api_user_name = (
                    getattr(thinking_context, 'user_name', None) or
                    thinking_context.get_shared_data('user_name') or
                    thinking_context.metadata.get('user_name') or
                    thinking_context.input_data.get('user_name')
                )
                if api_user_name and api_user_name.strip() and api_user_name != "神秘嘉宾":
                    logger.debug(f"✅ 从思维上下文获取API传入的用户名: {api_user_name}")
                    return api_user_name
        except Exception as e:
            logger.debug(f"从思维上下文获取用户名失败: {e}")
        
        # 🔥 P0级别修复：优先从统一用户管理器获取真实用户名
        try:
            from core.unified_user_manager import get_unified_user_manager
            user_manager = get_unified_user_manager()
            
            if user_manager:
                user = user_manager.get_user(user_id)
                if user and user.name and user.name != "命令行用户" and user.name != "神秘嘉宾":
                    logger.debug(f"✅ 从统一用户管理器获取真实用户名: {user.name}")
                    return user.name
        except Exception as e:
            logger.debug(f"从统一用户管理器获取用户名失败: {e}")
        
        # 🔥 P0级别修复：从联系人管理器获取真实用户名
        try:
            from core.contacts_manager import get_contacts_manager
            contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例
            contact_info = contacts_manager.get_user_info(user_id)
            
            if contact_info:
                nickname = contact_info.get("nickname")
                if nickname and nickname != "命令行用户" and nickname != "神秘嘉宾":
                    logger.debug(f"✅ 从联系人管理器获取真实用户名: {nickname}")
                    return nickname
        except Exception as e:
            logger.debug(f"从联系人管理器获取用户名失败: {e}")
        
        # 🔥 P0级别修复：从数据库获取真实用户名
        try:
            from adapters.legacy_adapter import get_instance as get_legacy_adapter
            legacy_adapter = get_legacy_adapter()
            
            if legacy_adapter and legacy_adapter.mysql and legacy_adapter.mysql.is_available:
                user_query = "SELECT name FROM users WHERE id = %s"
                success, result, error = legacy_adapter.mysql.query_one(user_query, (user_id,))
                
                if success and result and result.get("name"):
                    db_name = result.get("name")
                    if db_name != "命令行用户" and db_name != "神秘嘉宾":
                        logger.debug(f"✅ 从数据库获取真实用户名: {db_name}")
                        return db_name
        except Exception as e:
            logger.debug(f"从数据库获取用户名失败: {e}")
        
        # 🔥 降级方案：从user_info获取
        if user_info:
            nickname = user_info.get("nickname") or user_info.get("name")
            if nickname and nickname != "命令行用户" and nickname != "神秘嘉宾":
                logger.debug(f"✅ 从user_info获取用户名: {nickname}")
                return nickname
        
        # 🔥 最终降级：针对群聊ID和普通用户ID分别处理
        if user_id.endswith("@chatroom"):
            # 群聊ID处理：提取群聊名称或使用群聊标识
            chatroom_id = user_id.split("@")[0]
            if len(chatroom_id) > 6:
                fallback_name = f"群聊{chatroom_id[-6:]}"
            else:
                fallback_name = "群聊用户"
        else:
            # 普通用户ID处理
            if len(user_id) > 6:
                fallback_name = f"用户{user_id[-6:]}"
            else:
                fallback_name = "朋友"
        
        logger.warning(f"⚠️ 无法获取真实用户名，使用降级方案: {fallback_name} (user_id: {user_id})")
        return fallback_name


# 单例模式
_instance = None

def get_instance(module_config=None) -> ChatSkill:
    """
    获取聊天技能实例（单例模式）
    
    Args:
        module_config: 模块配置，为了兼容性而添加，但不使用
        
    Returns:
        聊天技能实例
    """
    global _instance
    if _instance is None:
        _instance = ChatSkill()
    return _instance 