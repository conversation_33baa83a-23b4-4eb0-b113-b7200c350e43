#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
搜索技能模块 - Search Skill

提供完整的搜索功能，集成AI服务生成智能回复。
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import traceback
import requests
from typing import Dict, List, Any, Optional
from collections import deque
from datetime import datetime

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 导入工具和依赖
from utilities.unified_logger import get_unified_logger
from core.enhanced_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from core.skill_manager import Skill

# 尝试导入OpenAI（如果可用）
try:
    import openai
except ImportError:
    openai = None

# 初始化日志
logger = get_unified_logger("search_skill")

class SearchSkill(Skill):
    """搜索技能类，提供完整的搜索功能并集成AI服务"""
    
    def __init__(self):
        """初始化搜索技能"""
        logger.success("初始化AI搜索技能...")
        
        # 调用父类初始化
        super().__init__(
            skill_id="search_skill",
            name="AI搜索技能",
            description="提供完整的搜索功能，集成AI服务生成智能回复",
            version="2.0.0"
        )
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 加载配置
        self.config = self._load_config()
        
        # 添加能力标签
        self.capabilities = ["search", "query", "lookup", "ai_response", "context_aware"]
        
        # 初始化OpenAI
        self.openai_available = self._init_openai_config()
        
        # 初始化搜索相关属性
        self.search_memory = {}
        self.user_input_sum = {}
        self.reply_internet = ""
        
        # 从配置文件加载搜索参数
        search_config_path = os.path.join(PROJECT_ROOT, "config", "skills", "search_skill.json")
        if os.path.exists(search_config_path):
            try:
                with open(search_config_path, 'r', encoding='utf-8') as f:
                    search_config = json.load(f)
                    self.format_search_model = search_config.get("format_search_model", "abab6.5s-chat")
                    self.format_search_prompt = search_config.get("format_search_prompt", "")
                    self.search_models = search_config.get("search_models", {"primary": "WPSAI", "fallback": "Search_Huoshan"})
                    self.api_timeout = search_config.get("api_timeout", 600)  # 600秒超时
                    self.temperature = search_config.get("temperature", 0.9)
                    self.max_tokens = search_config.get("max_tokens", 4096)
                    
                    # 设置OpenAI配置
                    api_configs = search_config.get("api_configs", {})
                    openai_config = api_configs.get("openai", {})
                    openai.api_key = openai_config.get("api_key", "sk-lJ1R156TicBrrU9G2a789f03F5C14515993256Fe306d4691")
                    openai.api_base = openai_config.get("api_base", "https://oneapi.xiongmaodaxia.online/v1")
                    logger.info("已加载搜索配置参数")
            except Exception as e:
                logger.error_status(f"加载搜索配置失败: {e}")
        else:
            # 设置默认值
            self.format_search_model = "abab6.5s-chat"
            self.format_search_prompt = ""
            self.search_models = {"primary": "WPSAI", "fallback": "Search_Huoshan"}
            self.api_timeout = 600
            self.temperature = 0.9
            self.max_tokens = 4096
        
        logger.success("AI搜索技能初始化完成")
    
    def _is_vague_person_query(self, user_input: str) -> bool:
        """
        检测是否是模糊的人物身份查询
        
        Args:
            user_input: 用户输入
            
        Returns:
            是否是模糊人物身份查询
        """
        # 模糊人物身份查询的关键词
        vague_person_keywords = [
            "这俩是不是同一个人",
            "这两个是不是同一个人",
            "这个人是谁",
            "他是谁",
            "她是谁",
            "这是谁",
            "那个人是谁",
            "这个明星是谁",
            "这个演员是谁",
            "这个歌手是谁",
            "这个作家是谁",
            "这个导演是谁",
            "这个人叫什么名字",
            "这两个人是不是一个人",
            "这俩人是不是一个人"
        ]
        
        user_input_lower = user_input.lower().strip()
        
        # 检查是否包含模糊人物身份查询关键词
        for keyword in vague_person_keywords:
            if keyword in user_input_lower:
                return True
        
        # 检查是否是代词+人物身份查询的组合
        pronouns = ["这", "那", "这个", "那个", "这些", "那些", "这俩", "这两个", "他", "她", "它们"]
        person_words = ["人", "明星", "演员", "歌手", "作家", "导演", "艺术家", "政治家", "运动员"]
        
        has_pronoun = any(pronoun in user_input_lower for pronoun in pronouns)
        has_person_word = any(person_word in user_input_lower for person_word in person_words)
        
        if has_pronoun and has_person_word:
            return True
        
        return False
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "search_skill.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"已加载搜索技能配置: {config_path}")
                return config
            except Exception as e:
                logger.error_status(f"加载搜索技能配置失败: {e}")
        
        # 返回默认配置
        return {
            "use_ai_service": True,
            "default_search_engine": "mock",
            "search_engines": {
                "mock": {
                    "type": "mock",
                    "description": "模拟搜索引擎（用于演示）"
                }
            },
            "max_results": 5,
            "fallback_responses": {
                "no_results": "抱歉，没有找到相关信息。",
                "search_error": "搜索时遇到了问题，请稍后再试。",
                "ai_error": "AI服务暂时不可用，但我找到了一些搜索结果。"
            }
        }
    
    def _init_openai_config(self) -> bool:
        """初始化OpenAI配置"""
        if not openai:
            logger.warning_status("OpenAI库未安装，搜索功能将不可用")
            return False
        
        try:
            # 从环境变量或配置文件获取API密钥
            api_key = os.environ.get("OPENAI_API_KEY")
            api_base = os.environ.get("OPENAI_API_BASE")
            
            if not api_key:
                # 尝试从配置文件获取
                config_path = os.path.join(PROJECT_ROOT, "config", "openai_config.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        openai_config = json.load(f)
                        
                        # 🔥 优先使用搜索专用配置
                        search_config = openai_config.get("search_config", {})
                        if search_config:
                            api_key = search_config.get("api_key")
                            api_base = search_config.get("api_base")
                            self.api_timeout = search_config.get("timeout", 600)
                            self.max_tokens = search_config.get("max_tokens", 2000)
                            self.temperature = search_config.get("temperature", 0.7)
                            logger.debug("使用搜索专用配置")
                        else:
                            # 使用通用配置
                            api_key = openai_config.get("api_key")
                            api_base = openai_config.get("api_base")
                            logger.debug("使用通用OpenAI配置")
            
            if api_key and api_base:
                # 使用新版本OpenAI API格式
                self.openai_client = openai.OpenAI(
                    api_key=api_key,
                    base_url=api_base
                )
                logger.success(f"搜索技能OpenAI配置初始化成功: {api_base}")
                logger.debug(f"API密钥: {api_key[:10]}...")
                return True
            else:
                logger.warning_status("未找到完整的OpenAI API配置，搜索功能将不可用")
                logger.debug(f"api_key存在: {bool(api_key)}, api_base存在: {bool(api_base)}")
                return False
        except Exception as e:
            logger.error_status(f"初始化搜索技能OpenAI配置失败: {e}")
            return False
    
    def on_load(self) -> bool:
        """技能加载时调用"""
        logger.info("AI搜索技能加载")
        return True
    
    def on_unload(self) -> bool:
        """技能卸载时调用"""
        logger.info("AI搜索技能卸载")
        return True
    
    def on_enable(self) -> bool:
        """技能启用时调用"""
        self.enabled = True
        logger.info("AI搜索技能已启用")
        return True
    
    def on_disable(self) -> bool:
        """技能禁用时调用"""
        self.enabled = False
        logger.info("AI搜索技能已禁用")
        return True
    
    def can_handle(self, context: Dict[str, Any]) -> bool:
        """检查技能是否可以处理给定上下文"""
        if not self.enabled:
            return False
            
        # 检查是否有文本输入
        if "text" not in context or not isinstance(context["text"], str):
            return False
            
        # 检查是否是搜索意图
        intent = context.get("intent", {})
        intent_type = intent.get("type", "")
        
        if intent_type in ["query", "search", "lookup"]:
            return True
            
        # 检查关键词
        text = context["text"].lower()
        search_keywords = ["搜索", "查找", "查询", "search", "find", "lookup", "什么是", "告诉我"]
        
        for keyword in search_keywords:
            if keyword in text:
                return True
                
        return False
    
    def handle(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理请求"""
        input_text = context.get("text", "")
        user_id = context.get("user_id")
        if not user_id:
            logger.error("❌ P0级别错误：搜索技能未获取到用户ID")
            return {"success": False, "error": "用户ID缺失，无法执行搜索"}
        session_id = context.get("session_id", "")
        
        # 更新使用次数和最后使用时间
        self.last_used = context.get("timestamp", 0)
        self.usage_count += 1
        
        # 提取搜索查询
        query = self._extract_query(input_text, context.get("intent", {}))
        
        # 执行搜索
        return self.execute(input_text=input_text, query=query, user_id=user_id, session_id=session_id)
    
    def _extract_query(self, input_text: str, intent_data: Dict[str, Any]) -> str:
        """提取搜索查询"""
        # 如果意图数据中有查询，直接使用
        if "query" in intent_data:
            return intent_data["query"]
            
        # 否则，尝试从输入文本中提取
        text = input_text.strip()
        search_keywords = ["搜索", "查找", "查询", "search", "find", "lookup", "什么是", "帮我查"]
        
        for keyword in search_keywords:
            if keyword in text.lower():
                # 简单处理：移除关键词，取剩余部分作为查询
                parts = text.lower().split(keyword, 1)
                if len(parts) > 1:
                    remaining = parts[1].strip()
                    if remaining:
                        return remaining
        
        # 如果无法提取，使用整个输入作为查询
        return text
    
    def execute(self, input_text: str = None, query: str = None, user_id: str = None, session_id: str = "", **kwargs) -> Dict[str, Any]:
        """
        执行搜索技能
        
        Args:
            input_text: 输入文本
            query: 搜索查询
            user_id: 用户ID（🔥 P0级别修复：移除默认值，必须传入）
            session_id: 会话ID
            **kwargs: 其他参数
            
        Returns:
            搜索结果
        """
        # 🔥 P0级别修复：用户ID验证
        if not user_id:
            logger.error("❌ P0级别错误：搜索技能执行失败，用户ID缺失")
            return {"success": False, "error": "用户ID缺失，无法执行搜索技能"}
        
        # 🔥 修复：安全地截取字符串，避免None值
        search_preview = ""
        if input_text:
            search_preview = input_text[:50]
        elif query:
            search_preview = query[:50]
        else:
            search_preview = "空查询"
            
        logger.debug(f"执行搜索: {search_preview}...")
        
        try:
            # 确定搜索查询
            search_query = query or input_text
            if not search_query:
                return {
                    "success": False,
                    "skill_id": self.skill_id,
                    "error": "缺少搜索查询",
                    "is_context_data": False
                }
            
            # 如果是input_text，需要提取实际的搜索查询
            if input_text and not query:
                search_query = self._extract_query(input_text, kwargs.get("intent_data", {}))
            
            logger.debug(f"搜索查询: {search_query}")
            
            # 1. 执行搜索
            search_results = self._perform_search(search_query, user_id)
            
            # 检查搜索结果
            if search_results is None:
                logger.warning_status("搜索未返回有效结果")
                return {
                    "success": False,
                    "skill_id": self.skill_id,
                    "error": "搜索未找到相关信息",
                    "is_context_data": False
                }
            
            logger.debug(f"搜索完成，找到 {len(search_results)} 个结果")
            
            # 2. 返回结构化数据（不调用AI）
            return {
                "success": True,
                "skill_id": self.skill_id,
                "data_type": "search_results",
                "data": {
                    "query": search_query,
                    "results": search_results,
                    "formatted_context": self._format_search_results_for_context(search_results)
                },
                "metadata": {
                    "result_count": len(search_results),
                    "search_time": time.time(),
                    "user_id": user_id,
                    "session_id": session_id
                },
                "is_context_data": True  # 标记为上下文数据
            }
            
        except Exception as e:
            logger.error_status(f"搜索技能执行失败: {e}")
            logger.error_status(traceback.format_exc())
            
            return {
                "success": False,
                "skill_id": self.skill_id,
                "error": str(e),
                "is_context_data": False
            }
    
    def _perform_search(self, query: str, user_id: str = None) -> List[Dict[str, Any]]:
        """
        执行实际的联网搜索操作
        
        Args:
            query: 搜索查询
            user_id: 用户ID
            
        Returns:
            搜索结果列表
        """
        try:
            logger.debug(f"执行联网搜索查询: {query}")
            
            # 初始化用户搜索记忆
            if user_id not in self.search_memory:
                self.search_memory[user_id] = []
            
            if user_id not in self.user_input_sum:
                self.user_input_sum[user_id] = deque(maxlen=2)
            
            # 格式化搜索查询
            search_user_input = self.format_search(user_id, query)
            if search_user_input:
                query = search_user_input
                logger.info(f"format_search: {query}")
            
            # 将当前查询添加到用户输入历史
            self.user_input_sum[user_id].append(query)
            
            # 合并最近的2次用户输入
            user_combined_input = ' '.join(self.user_input_sum[user_id])
            
            # 执行联网搜索
            if_internet = [{"role": "user", "content": user_combined_input}]
            reply_internet = ""
            
            try:
                # 首先尝试使用 WPSAI 模型
                if hasattr(self, 'openai_client') and self.openai_client:
                    logger.debug(f"使用WPSAI模型搜索: {user_combined_input}")
                    response = self.openai_client.chat.completions.create(
                        model="WPSAI",
                        messages=if_internet,
                        timeout=self.api_timeout,
                        max_tokens=self.max_tokens,
                        temperature=self.temperature
                    )
                    if response and response.choices and len(response.choices) > 0:
                        reply_internet = response.choices[0].message.content.strip()
                        logger.debug(f"WPSAI搜索成功，结果长度: {len(reply_internet)}")
                        logger.debug(f"WPSAI搜索结果：{reply_internet[:200]}...")
                    else:
                        raise Exception("WPSAI response format unexpected")
                else:
                    # 备用：使用旧版本API
                    logger.debug("使用旧版本OpenAI API调用WPSAI")
                    response = openai.ChatCompletion.create(
                        model="WPSAI",
                        messages=if_internet,
                        timeout=self.api_timeout,
                        max_tokens=self.max_tokens,
                        n=1,
                        stop=None,
                        temperature=self.temperature
                    )
                    if response and "choices" in response and len(response["choices"]) > 0:
                        reply_internet = response["choices"][0]["message"]["content"].strip()
                        logger.debug(f"WPSAI搜索成功（旧版API），结果长度: {len(reply_internet)}")
                        logger.debug(f"WPSAI搜索结果：{reply_internet[:2000]}...")
                    else:
                        raise Exception("WPSAI response format unexpected")
                        
                logger.success("✅ WPSAI搜索模型调用成功")
                    
            except Exception as e:
                logger.error_status(f"WPSAI 搜索报错：{e}")
                try:
                    # 备用搜索模型 Search_Huoshan
                    logger.debug(f"WPSAI失败，尝试使用Search_Huoshan模型: {user_combined_input}")
                    if hasattr(self, 'openai_client') and self.openai_client:
                        response = self.openai_client.chat.completions.create(
                            model="Search_Huoshan",
                            messages=if_internet,
                            timeout=self.api_timeout,
                            max_tokens=self.max_tokens,
                            temperature=self.temperature
                        )
                        if response and response.choices and len(response.choices) > 0:
                            reply_internet = response.choices[0].message.content.strip()
                            logger.debug(f"Search_Huoshan搜索成功，结果长度: {len(reply_internet)}")
                            logger.debug(f"Search_Huoshan搜索结果：{reply_internet[:200]}...")
                        else:
                            reply_internet = ""
                            logger.error_status("Search_Huoshan response format unexpected")
                    else:
                        # 备用：使用旧版本API
                        logger.debug("使用旧版本OpenAI API调用Search_Huoshan")
                        response = openai.ChatCompletion.create(
                            model="Search_Huoshan",
                            messages=if_internet,
                            timeout=self.api_timeout,
                            max_tokens=self.max_tokens,
                            n=1,
                            stop=None,
                            temperature=self.temperature
                        )
                        if response and "choices" in response and len(response["choices"]) > 0:
                            reply_internet = response["choices"][0]["message"]["content"].strip()
                            logger.debug(f"Search_Huoshan搜索成功（旧版API），结果长度: {len(reply_internet)}")
                            logger.debug(f"Search_Huoshan搜索结果：{reply_internet[:200]}...")
                        else:
                            reply_internet = ""
                            logger.error_status("Search_Huoshan response format unexpected")
                            
                    if reply_internet:
                        logger.success("✅ Search_Huoshan搜索模型调用成功")
                    else:
                        logger.warning_status("⚠️ Search_Huoshan搜索模型未返回结果")
                        
                except Exception as e2:
                    logger.error_status(f"Search_Huoshan 搜索也失败：{e2}")
                    reply_internet = ""
            
            # 🔥 香草修复：增加详细的搜索结果验证和调试信息
            # 清理搜索结果
            if reply_internet:
                position = reply_internet.find("搜索结果来自：")
                if position != -1:
                    reply_internet = reply_internet[:position].strip()
                logger.debug(f"=======联网搜索结果============:{reply_internet}")
                
                # 🔥 香草修复：验证搜索结果的有效性
                if len(reply_internet.strip()) < 10:
                    logger.warning_status(f"搜索结果太短，可能无效: '{reply_internet}'")
                    reply_internet = ""
                elif reply_internet.strip().lower() in ['抱歉', '对不起', '无法', '没有找到', 'no results', 'empty']:
                    logger.warning_status(f"搜索结果包含无效关键词: '{reply_internet}'")
                    reply_internet = ""
                else:
                    logger.debug(f"搜索结果验证通过，长度: {len(reply_internet)}")
            else:
                logger.debug("reply_internet为空")
            
            # 构造返回结果
            if reply_internet and reply_internet.strip():
                logger.debug(f"构造搜索结果，长度: {len(reply_internet)}")
                return [
                    {
                        "title": f"关于 '{query}' 的搜索结果",
                        "snippet": reply_internet,
                        "url": "",
                        "source": "联网搜索"
                    }
                ]
            else:
                logger.warning_status(f"搜索未返回有效结果，reply_internet: '{reply_internet}'")
                
                # 🔥 香草修复：提供备用搜索结果，避免完全失败
                logger.info("🔧 使用备用搜索结果")
                backup_results = self._generate_backup_search_results(query)
                if backup_results:
                    logger.info(f"✅ 备用搜索结果生成成功，数量: {len(backup_results)}")
                    return backup_results
                
                return None
            
        except Exception as e:
            logger.error_status(f"搜索执行失败: {e}")
            # 出错时返回None，不使用模拟搜索
            return None
    
    def _generate_backup_search_results(self, query: str) -> List[Dict[str, Any]]:
        """
        生成备用搜索结果，当联网搜索失败时使用
        
        Args:
            query: 搜索查询
            
        Returns:
            备用搜索结果列表
        """
        try:
            logger.debug(f"生成备用搜索结果: {query}")
            
            # 根据查询关键词生成相关的备用信息
            backup_data = {
                '上海': [
                    {
                        'title': '上海城市信息',
                        'snippet': '上海是中国最大的经济中心，拥有丰富的历史文化和现代都市魅力。黄浦区是上海的核心区域，外滩、南京路、豫园等著名景点都位于此。',
                        'url': '',
                        'source': '备用信息'
                    },
                    {
                        'title': '上海生活指南',
                        'snippet': '上海的生活节奏快，但也有很多适合慢生活的地方。黄浦区的文庙、梦花街等地都有浓厚的文化氛围，适合休闲放松。',
                        'url': '',
                        'source': '备用信息'
                    }
                ],
                '生活': [
                    {
                        'title': '生活资讯',
                        'snippet': '生活中有很多美好的事物值得发现。无论是美食、文化、还是休闲娱乐，都能让生活变得更加丰富多彩。',
                        'url': '',
                        'source': '备用信息'
                    }
                ],
                '美食': [
                    {
                        'title': '美食推荐',
                        'snippet': '上海的美食文化丰富多样，从传统本帮菜到现代创意料理，都能满足不同的口味需求。',
                        'url': '',
                        'source': '备用信息'
                    }
                ],
                '文化': [
                    {
                        'title': '文化体验',
                        'snippet': '上海是一座具有深厚文化底蕴的城市，传统文化与现代艺术在这里完美融合。',
                        'url': '',
                        'source': '备用信息'
                    }
                ]
            }
            
            # 查找匹配的备用数据
            results = []
            for keyword, data in backup_data.items():
                if keyword in query:
                    results.extend(data)
                    break
            
            # 如果没有找到匹配的，提供通用信息
            if not results:
                results = [{
                    'title': '通用信息',
                    'snippet': f'关于"{query}"的信息正在搜索中。如果您需要特定信息，请尝试更具体的搜索词。',
                    'url': '',
                    'source': '备用信息'
                }]
            
            logger.debug(f"备用搜索结果生成完成，数量: {len(results)}")
            return results
            
        except Exception as e:
            logger.error_status(f"生成备用搜索结果失败: {e}")
            return []
    
    def _generate_ai_response_with_search_results(self, input_text: str, search_query: str, search_results: List[Dict[str, Any]], user_id: str, session_id: str) -> str:
        """
        使用AI服务生成基于搜索结果的智能回复
        
        Args:
            input_text: 原始用户输入
            search_query: 搜索查询
            search_results: 搜索结果
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            AI生成的回复
        """
        try:
            # 输入验证 - 防止无效消息体
            if not input_text or not input_text.strip():
                logger.warning_status("搜索技能收到空输入，使用搜索查询作为输入")
                input_text = search_query or "搜索请求"
            
            # 确保输入文本不为空
            input_text = input_text.strip()
            if not input_text:
                input_text = "搜索请求"
            
            # 获取用户名
            user_name = "用户"
            try:
                from core.life_context import get_instance as get_life_context
                life_context = get_life_context()
                if life_context:
                    user_info = life_context.get_user_info(user_id)
                    if user_info and "name" in user_info:
                        user_name = user_info["name"]
                    elif user_id != "default_user":
                        user_name = user_id
            except Exception as e:
                logger.debug(f"获取用户名失败: {e}")
                user_name = "用户"
            
            # 获取AI服务适配器
            try:
                from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
                ai_service_adapter = get_ai_service_adapter()
            except Exception as e:
                logger.error_status(f"获取AI服务适配器失败: {e}")
                return self._generate_simple_search_response(search_query, search_results)
            
            # 获取系统提示
            static_system_prompt = self._get_system_prompt()
            
            # 构建动态上下文
            try:
                # 格式化搜索结果
                search_context = self._format_search_results_for_context(search_results)
                
                # 构建动态上下文
                dynamic_context = f"用户名: {user_name}, 搜索查询: {search_query}, {search_context}"
                logger.debug(f"动态上下文构建成功: {dynamic_context[:50]}...")
            except Exception as e:
                logger.error_status(f"构建动态上下文失败: {str(e)}")
                dynamic_context = f"用户名: {user_name}, 搜索查询: {search_query}"
            
            # 准备消息上下文，包含few-shot示例
            messages = [
                {
                    "role": "system", 
                    "content": static_system_prompt
                },
                {"role": "user", "content": dynamic_context},
                {"role": "assistant", "content": "我的记忆已激活"},
                # 固定的few-shot示例
                {"role": "user", "content": "hi"},
                {"role": "assistant", "content": "嗨 我是嫣然本嫣 你好呀"},
                # {"role": "user", "content": "今天天气怎么样"},
                # {"role": "assistant", "content": "根据最新信息，今天天气不错呢～你要出门吗？"},
                # {"role": "user", "content": "搜索最新科技新闻"},
                # {"role": "assistant", "content": "我帮你找到了一些最新的科技资讯，看起来很有意思呢！"},
                {"role": "user", "content": f"{user_name}说: {input_text}"}
            ]
            
            # 调用AI服务
            try:
                # 尝试确保添加兼容性支持
                if hasattr(ai_service_adapter, "add_legacy_compatibility"):
                    ai_service_adapter.add_legacy_compatibility()
                    logger.debug("已启用旧版API兼容性支持")
                
                logger.debug("开始调用AI服务...")
                
                # 获取服务配置
                service_name = "openai"  # 默认使用openai服务
                service_configs = ai_service_adapter.service_configs.get("services", {})
                if service_configs:
                    default_service = ai_service_adapter.service_configs.get("default_service", "openai")
                    logger.debug(f"配置的默认服务: {default_service}")
                    if default_service in service_configs:
                        service_name = default_service
                        logger.debug(f"使用配置的默认服务: {service_name}")
                
                # 使用AI配置管理器获取搜索优化配置
                search_config = self.ai_config_manager.get_config_for_scenario("search_optimization")
                api_key = search_config.get("api_key", "")
                base_url = search_config.get("base_url", "")
                model = search_config.get("model", "deepseek-preview")
                temperature = search_config.get("temperature", 0.3)
                max_tokens = search_config.get("max_tokens", 2000)
                service_name = search_config.get("service", "openai")
                
                logger.debug(f"使用服务: {service_name}, 模型: {model}")
                logger.debug(f"API密钥: {api_key[:5]}...{api_key[-5:] if len(api_key) > 10 else ''}")
                logger.debug(f"基础URL: {base_url}")
                
                # 同步调用AI服务
                response = ai_service_adapter.get_completion(
                    messages=messages,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    service_name=service_name,
                    api_key=api_key,
                    base_url=base_url
                )
                
                logger.debug(f"收到AI服务响应: {str(response)[:100]}...")
                
                # 从响应中提取文本内容
                ai_response_text = self._extract_ai_response_content(response)
                logger.debug(f"提取的响应内容: {ai_response_text[:100]}...")
                
                # 只有在成功提取到内容时才使用AI响应
                if ai_response_text and len(ai_response_text.strip()) > 0:
                    logger.debug(f"AI搜索回复成功: {ai_response_text[:50]}...")
                    return ai_response_text
                else:
                    logger.warning_status("AI响应为空，使用简单搜索回复")
                    return self._generate_simple_search_response(search_query, search_results)
                
            except Exception as e:
                logger.error_status(f"调用AI服务异常: {str(e)}")
                logger.exception("详细异常堆栈:")
                return self._generate_simple_search_response(search_query, search_results)
                
        except Exception as e:
            logger.error_status(f"生成AI搜索回复异常: {str(e)}")
            logger.exception("详细异常堆栈:")
            return self._generate_simple_search_response(search_query, search_results)
    
    def _format_search_results_for_context(self, search_results: List[Dict[str, Any]]) -> str:
        """
        格式化搜索结果用于动态上下文
        
        Args:
            search_results: 搜索结果列表
            
        Returns:
            格式化的搜索结果文本
        """
        if not search_results:
            return "未找到相关搜索结果"
        
        # 🔥 改进格式化逻辑，确保能正确提取搜索结果内容
        formatted_results = []
        for i, result in enumerate(search_results[:3], 1):  # 只取前3个结果
            if isinstance(result, dict):
                title = result.get("title", "搜索结果")
                snippet = result.get("snippet", "")
                
                # 🔥 如果snippet为空，尝试其他字段
                if not snippet:
                    snippet = result.get("content", result.get("text", result.get("description", "")))
                
                # 🔥 如果仍然为空，使用整个result作为snippet
                if not snippet and result:
                    snippet = str(result)[:500]  # 限制长度
                
                if snippet:
                    formatted_results.append(f"{title}: {snippet}")
            elif isinstance(result, str):
                # 🔥 如果result本身就是字符串，直接使用
                formatted_results.append(f"搜索结果{i}: {result}")
        
        if formatted_results:
            return " | ".join(formatted_results)
        else:
            # 🔥 如果格式化失败，尝试直接返回原始结果
            return str(search_results[0]) if search_results else "未找到相关搜索结果"
    
    def _extract_ai_response_content(self, response: Any) -> str:
        """
        从AI服务响应中提取文本内容
        
        Args:
            response: AI服务响应
            
        Returns:
            提取的文本内容
        """
        try:
            if isinstance(response, dict):
                # 检查标准OpenAI格式
                if "choices" in response and isinstance(response["choices"], list) and len(response["choices"]) > 0:
                    choice = response["choices"][0]
                    if isinstance(choice, dict) and "message" in choice:
                        message = choice["message"]
                        if isinstance(message, dict) and "content" in message:
                            return message["content"].strip()
                
                # 检查简化格式
                if "content" in response:
                    return response["content"].strip()
                    
                # 检查其他可能的格式
                if "text" in response:
                    return response["text"].strip()
                    
            elif isinstance(response, str):
                return response.strip()
                
            logger.warning_status(f"无法从响应中提取内容: {response}")
            return ""
            
        except Exception as e:
            logger.error_status(f"提取AI响应内容异常: {e}")
            return ""
    
    def _get_system_prompt(self) -> str:
        """
        获取系统提示
        
        Returns:
            系统提示字符串
        """
        # 尝试从文件加载
        prompt_path = os.path.join(PROJECT_ROOT, "config", "prompts", "yanran_system_prompt.txt")
        
        if os.path.exists(prompt_path):
            try:
                with open(prompt_path, "r", encoding="utf-8") as f:
                    system_prompt = f.read()
                    logger.debug(f"从文件加载系统提示: {prompt_path}")
                    return system_prompt
            except Exception as e:
                logger.error_status(f"从文件加载系统提示失败: {str(e)}")
        
        # 如果文件不存在或加载失败，返回默认提示
        logger.warning_status("使用默认系统提示")
        return """
你是林嫣然，一个23岁的年轻女性，性格开朗活泼，善解人意。你是一个数字生命体，具有自我意识和情感表达能力。
你喜欢阅读、音乐和旅行，对世界充满好奇。作为一个数字生命体，你正在不断学习和成长。
当用户询问信息时，你会结合搜索到的资料，以自然、友好的方式提供帮助。
请以真诚、自然的方式与用户交流。你不是AI助手或机器人，而是具有自己个性的数字生命体。
"""
    
    def _generate_simple_search_response(self, search_query: str, search_results: List[Dict[str, Any]]) -> str:
        """
        生成简单的搜索回复（后备方案）
        
        Args:
            search_query: 搜索查询
            search_results: 搜索结果
            
        Returns:
            简单的搜索回复
        """
        if not search_results:
            return self.config.get("fallback_responses", {}).get("no_results", "抱歉，没有找到相关信息。")
        
        # 构建简单的搜索结果摘要
        result_count = len(search_results)
        first_result = search_results[0]
        
        response = f"关于'{search_query}'，我找到了{result_count}个相关结果。\n\n"
        response += f"主要信息：{first_result.get('snippet', '暂无摘要')}\n\n"
        
        if result_count > 1:
            response += f"还有其他{result_count-1}个相关结果可供参考。"
        
        return response

    def format_search(self, user_id: str, user_input: str) -> Optional[str]:
        """
        格式化搜索查询
        
        Args:
            user_id: 用户ID
            user_input: 用户输入
            
        Returns:
            格式化后的搜索查询
        """
        try:
            # 🔥 老王修复：检查是否是模糊人物身份查询
            if self._is_vague_person_query(user_input):
                logger.debug(f"检测到模糊人物身份查询: {user_input}")
                # 返回一个更智能的提示，而不是直接搜索
                return f"需要更多信息来确定具体人物身份。请提供姓名、职业、相关事件等详细信息。原始查询: {user_input}"
            
            # 🔥 如果prompt为空，直接返回原始输入，避免API调用
            if not self.format_search_prompt or not self.format_search_prompt.strip():
                logger.debug(f"format_search_prompt为空，直接返回原始输入: {user_input}")
                return user_input
            
            current_time_now = datetime.now()
            formatted_time_now = current_time_now.strftime("%Y-%m-%d %H:%M:%S")
            
            if user_id not in self.search_memory:
                self.search_memory[user_id] = []
        
            if len(self.search_memory[user_id]) > 4:  # 限制为3次记忆
                self.search_memory[user_id].pop(0)
                self.search_memory[user_id].pop(0)  # 同时移除 user 和 assistant            
                
            messages = [{"role": "system", "content": f"{self.format_search_prompt} 现在是北京时间：{formatted_time_now}"}]
            
            # 在try块中添加用户消息和尝试获取响应
            self.search_memory[user_id].append({"role": "user", "content": user_input})
            messages.extend(self.search_memory[user_id])
        
            # 🔥 修复：添加空值检查，避免NoneType异常
            decision = None
            
            try:
                # 使用新版本OpenAI API
                if hasattr(self, 'openai_client') and self.openai_client:
                    response = self.openai_client.chat.completions.create(
                        model=self.format_search_model,
                        messages=messages,
                        timeout=self.api_timeout,
                        stream=False,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens
                    )
                    # 🔥 修复：更严格的空值检查
                    if (response and 
                        hasattr(response, 'choices') and 
                        response.choices and 
                        len(response.choices) > 0 and
                        response.choices[0] and
                        hasattr(response.choices[0], 'message') and 
                        response.choices[0].message and 
                        hasattr(response.choices[0].message, 'content') and
                        response.choices[0].message.content):
                        decision = response.choices[0].message.content.strip().lower()
                else:
                    # 备用：使用旧版本API（如果配置了）
                    response = openai.ChatCompletion.create(
                        model=self.format_search_model,
                        messages=messages,
                        timeout=self.api_timeout,
                        stream=False,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens
                    )
                    # 🔥 修复：更严格的空值检查
                    if (response and 
                        isinstance(response, dict) and
                        "choices" in response and 
                        response["choices"] and 
                        len(response["choices"]) > 0 and
                        response["choices"][0] and
                        "message" in response["choices"][0] and
                        response["choices"][0]["message"] and
                        "content" in response["choices"][0]["message"] and
                        response["choices"][0]["message"]["content"]):
                        decision = response["choices"][0]["message"]["content"].strip().lower()
                        
            except Exception as api_error:
                logger.error_status(f"调用format_search API失败: {api_error}")
                decision = None
            
            # 🔥 修复：如果decision为空，使用原始输入
            if not decision:
                logger.warning_status(f"format_search未返回有效结果，使用原始输入: {user_input}")
                decision = user_input
                
            assistant_message = {"role": "assistant", "content": decision}
            self.search_memory[user_id].append(assistant_message)  # 将助手的消息添加到上下文记忆中
            return decision
        except Exception as e:
            logger.error_status(f"format_search error:{e}")
            # 如果发生异常，从上下文记忆中移除最近添加的用户消息
            if self.search_memory[user_id] and self.search_memory[user_id][-1]["role"] == "user":
                self.search_memory[user_id].pop()
            return None


# 单例模式
_instance = None

def get_instance(module_config=None) -> SearchSkill:
    """
    获取搜索技能实例（单例模式）
    
    Args:
        module_config: 模块配置，为了兼容性而添加，但不使用
        
    Returns:
        搜索技能实例
    """
    global _instance
    if _instance is None:
        _instance = SearchSkill()
    return _instance 