"""OpenBB金融数据技能插件

这个插件为林嫣然数字生命体系统提供金融数据查询和分析功能，
基于OpenBB平台实现股票、期权、加密货币、外汇、宏观经济、固定收益等市场数据接入。

主要功能：
- 多种金融工具数据查询
- AI增强的金融分析
- 实时数据监控和预警
- 个性化投资建议
"""

from .openbb_financial_skill import OpenBBFinancialSkill

__version__ = "1.0.0"
__author__ = "Yanran Digital Life Team"

# 全局实例
_openbb_financial_skill_instance = None

def get_instance(module_config=None):
    """获取OpenBB金融技能实例（单例模式）
    
    Args:
        module_config: 模块配置，为了兼容性而添加，但不使用
        
    Returns:
        OpenBB金融技能实例
    """
    global _openbb_financial_skill_instance
    if _openbb_financial_skill_instance is None:
        _openbb_financial_skill_instance = OpenBBFinancialSkill()
    return _openbb_financial_skill_instance

__all__ = [
    "OpenBBFinancialSkill",
    "get_instance",
]