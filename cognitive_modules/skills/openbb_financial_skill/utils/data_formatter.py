"""数据格式化工具

提供金融数据的格式化、转换和展示功能。
"""

import json
import pandas as pd
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union
from decimal import Decimal, ROUND_HALF_UP


class DataFormatter:
    """金融数据格式化器"""
    
    def __init__(self):
        self.currency_symbols = {
            'USD': '$',
            'CNY': '¥',
            'EUR': '€',
            'GBP': '£',
            'JPY': '¥'
        }
        
        self.market_status_emoji = {
            'open': '🟢',
            'closed': '🔴',
            'pre_market': '🟡',
            'after_hours': '🟠'
        }
        
        self.trend_emoji = {
            'up': '📈',
            'down': '📉',
            'neutral': '➡️'
        }
    
    def format_price(self, price: Union[float, Decimal], currency: str = 'USD', 
                    decimal_places: int = 2) -> str:
        """格式化价格显示
        
        Args:
            price: 价格数值
            currency: 货币类型
            decimal_places: 小数位数
            
        Returns:
            格式化后的价格字符串
        """
        if price is None:
            return "N/A"
            
        try:
            # 转换为Decimal确保精度
            if isinstance(price, (int, float)):
                price = Decimal(str(price))
            
            # 四舍五入到指定小数位
            rounded_price = price.quantize(
                Decimal('0.' + '0' * decimal_places), 
                rounding=ROUND_HALF_UP
            )
            
            # 获取货币符号
            symbol = self.currency_symbols.get(currency.upper(), currency)
            
            # 格式化数字（添加千分位分隔符）
            formatted_number = f"{rounded_price:,}"
            
            return f"{symbol}{formatted_number}"
            
        except Exception as e:
            return f"Error formatting price: {str(e)}"
    
    def format_percentage(self, value: Union[float, Decimal], 
                         decimal_places: int = 2, 
                         show_sign: bool = True) -> str:
        """格式化百分比显示
        
        Args:
            value: 百分比数值
            decimal_places: 小数位数
            show_sign: 是否显示正负号
            
        Returns:
            格式化后的百分比字符串
        """
        if value is None:
            return "N/A"
            
        try:
            if isinstance(value, (int, float)):
                value = Decimal(str(value))
            
            rounded_value = value.quantize(
                Decimal('0.' + '0' * decimal_places),
                rounding=ROUND_HALF_UP
            )
            
            sign = "+" if show_sign and rounded_value > 0 else ""
            return f"{sign}{rounded_value}%"
            
        except Exception as e:
            return f"Error formatting percentage: {str(e)}"
    
    def format_volume(self, volume: Union[int, float]) -> str:
        """格式化交易量显示
        
        Args:
            volume: 交易量
            
        Returns:
            格式化后的交易量字符串
        """
        if volume is None:
            return "N/A"
            
        try:
            volume = float(volume)
            
            if volume >= 1_000_000_000:
                return f"{volume / 1_000_000_000:.1f}B"
            elif volume >= 1_000_000:
                return f"{volume / 1_000_000:.1f}M"
            elif volume >= 1_000:
                return f"{volume / 1_000:.1f}K"
            else:
                return f"{volume:,.0f}"
                
        except Exception as e:
            return f"Error formatting volume: {str(e)}"
    
    def format_market_cap(self, market_cap: Union[int, float], 
                         currency: str = 'USD') -> str:
        """格式化市值显示
        
        Args:
            market_cap: 市值
            currency: 货币类型
            
        Returns:
            格式化后的市值字符串
        """
        if market_cap is None:
            return "N/A"
            
        try:
            market_cap = float(market_cap)
            symbol = self.currency_symbols.get(currency.upper(), currency)
            
            if market_cap >= 1_000_000_000_000:
                return f"{symbol}{market_cap / 1_000_000_000_000:.2f}T"
            elif market_cap >= 1_000_000_000:
                return f"{symbol}{market_cap / 1_000_000_000:.2f}B"
            elif market_cap >= 1_000_000:
                return f"{symbol}{market_cap / 1_000_000:.2f}M"
            else:
                return f"{symbol}{market_cap:,.0f}"
                
        except Exception as e:
            return f"Error formatting market cap: {str(e)}"
    
    def format_timestamp(self, timestamp: Union[str, datetime, int], 
                        timezone_name: str = 'UTC',
                        format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """格式化时间戳显示
        
        Args:
            timestamp: 时间戳
            timezone_name: 时区名称
            format_str: 格式字符串
            
        Returns:
            格式化后的时间字符串
        """
        if timestamp is None:
            return "N/A"
            
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            elif isinstance(timestamp, int):
                dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
            elif isinstance(timestamp, datetime):
                dt = timestamp
            else:
                return "Invalid timestamp format"
            
            return dt.strftime(format_str)
            
        except Exception as e:
            return f"Error formatting timestamp: {str(e)}"
    
    def format_stock_summary(self, stock_data: Dict[str, Any]) -> str:
        """格式化股票摘要信息
        
        Args:
            stock_data: 股票数据字典
            
        Returns:
            格式化后的股票摘要字符串
        """
        try:
            symbol = stock_data.get('symbol', 'N/A')
            name = stock_data.get('name', 'N/A')
            price = stock_data.get('price', 0)
            change = stock_data.get('change', 0)
            change_percent = stock_data.get('change_percent', 0)
            volume = stock_data.get('volume', 0)
            market_cap = stock_data.get('market_cap', 0)
            
            # 确定趋势
            trend = 'up' if change > 0 else 'down' if change < 0 else 'neutral'
            trend_emoji = self.trend_emoji[trend]
            
            # 格式化各项数据
            formatted_price = self.format_price(price)
            formatted_change = self.format_price(change)
            formatted_change_percent = self.format_percentage(change_percent)
            formatted_volume = self.format_volume(volume)
            formatted_market_cap = self.format_market_cap(market_cap)
            
            summary = f"{trend_emoji} {name} ({symbol})\n"
            summary += f"💰 当前价格：{formatted_price}\n"
            summary += f"📊 涨跌：{formatted_change} ({formatted_change_percent})\n"
            summary += f"📈 成交量：{formatted_volume}\n"
            summary += f"🏢 市值：{formatted_market_cap}"
            
            return summary
            
        except Exception as e:
            return f"Error formatting stock summary: {str(e)}"
    
    def format_crypto_summary(self, crypto_data: Dict[str, Any]) -> str:
        """格式化加密货币摘要信息
        
        Args:
            crypto_data: 加密货币数据字典
            
        Returns:
            格式化后的加密货币摘要字符串
        """
        try:
            symbol = crypto_data.get('symbol', 'N/A')
            name = crypto_data.get('name', 'N/A')
            price = crypto_data.get('price', 0)
            change_24h = crypto_data.get('change_24h', 0)
            change_percent_24h = crypto_data.get('change_percent_24h', 0)
            volume_24h = crypto_data.get('volume_24h', 0)
            market_cap = crypto_data.get('market_cap', 0)
            
            # 确定趋势
            trend = 'up' if change_24h > 0 else 'down' if change_24h < 0 else 'neutral'
            trend_emoji = self.trend_emoji[trend]
            
            # 格式化各项数据
            formatted_price = self.format_price(price)
            formatted_change = self.format_price(change_24h)
            formatted_change_percent = self.format_percentage(change_percent_24h)
            formatted_volume = self.format_volume(volume_24h)
            formatted_market_cap = self.format_market_cap(market_cap)
            
            summary = f"{trend_emoji} {name} ({symbol})\n"
            summary += f"💰 当前价格：{formatted_price}\n"
            summary += f"📊 24h涨跌：{formatted_change} ({formatted_change_percent})\n"
            summary += f"📈 24h成交量：{formatted_volume}\n"
            summary += f"🏢 市值：{formatted_market_cap}"
            
            return summary
            
        except Exception as e:
            return f"Error formatting crypto summary: {str(e)}"
    
    def format_technical_indicators(self, indicators: Dict[str, Any]) -> str:
        """格式化技术指标信息
        
        Args:
            indicators: 技术指标数据字典
            
        Returns:
            格式化后的技术指标字符串
        """
        try:
            formatted_indicators = "📊 技术指标:\n"
            
            # RSI
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                rsi_status = "超买" if rsi > 70 else "超卖" if rsi < 30 else "中性"
                formatted_indicators += f"• RSI: {rsi:.1f} ({rsi_status})\n"
            
            # MACD
            if 'macd' in indicators:
                macd = indicators['macd']
                macd_signal = indicators.get('macd_signal', 0)
                macd_trend = "看涨" if macd > macd_signal else "看跌"
                formatted_indicators += f"• MACD: {macd:.3f} ({macd_trend})\n"
            
            # 移动平均线
            if 'sma_20' in indicators:
                sma_20 = indicators['sma_20']
                formatted_indicators += f"• SMA(20): {self.format_price(sma_20)}\n"
            
            if 'sma_50' in indicators:
                sma_50 = indicators['sma_50']
                formatted_indicators += f"• SMA(50): {self.format_price(sma_50)}\n"
            
            # 布林带
            if 'bollinger_upper' in indicators and 'bollinger_lower' in indicators:
                bb_upper = indicators['bollinger_upper']
                bb_lower = indicators['bollinger_lower']
                formatted_indicators += f"• 布林带: {self.format_price(bb_lower)} - {self.format_price(bb_upper)}\n"
            
            return formatted_indicators.rstrip('\n')
            
        except Exception as e:
            return f"Error formatting technical indicators: {str(e)}"
    
    def dataframe_to_table(self, df: pd.DataFrame, max_rows: int = 10) -> str:
        """将DataFrame转换为表格字符串
        
        Args:
            df: pandas DataFrame
            max_rows: 最大显示行数
            
        Returns:
            格式化后的表格字符串
        """
        try:
            if df.empty:
                return "暂无数据"
            
            # 限制显示行数
            display_df = df.head(max_rows)
            
            # 转换为字符串表格
            table_str = display_df.to_string(index=False, max_cols=6)
            
            if len(df) > max_rows:
                table_str += f"\n... 还有 {len(df) - max_rows} 行数据"
            
            return table_str
            
        except Exception as e:
            return f"Error converting dataframe to table: {str(e)}"
    
    def format_error_message(self, error: Exception, context: str = "") -> str:
        """格式化错误信息
        
        Args:
            error: 异常对象
            context: 错误上下文
            
        Returns:
            格式化后的错误信息
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        formatted_error = f"❌ 发生错误\n"
        if context:
            formatted_error += f"上下文：{context}\n"
        formatted_error += f"错误类型：{error_type}\n"
        formatted_error += f"错误信息：{error_message}"
        
        return formatted_error