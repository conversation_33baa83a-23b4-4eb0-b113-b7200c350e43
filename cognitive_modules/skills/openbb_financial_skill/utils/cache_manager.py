"""缓存管理器

提供金融数据的缓存管理功能，包括内存缓存和Redis缓存。
"""

import json
import time
import hashlib
from typing import Any, Dict, Optional, Union
from datetime import datetime, timedelta
import logging

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, redis_client: Optional[redis.Redis] = None, 
                 default_ttl: int = 300, max_memory_cache_size: int = 1000):
        """
        初始化缓存管理器
        
        Args:
            redis_client: Redis客户端实例
            default_ttl: 默认缓存过期时间（秒）
            max_memory_cache_size: 内存缓存最大条目数
        """
        self.redis_client = redis_client
        self.default_ttl = default_ttl
        self.max_memory_cache_size = max_memory_cache_size
        
        # 内存缓存
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_access_times: Dict[str, float] = {}
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'memory_evictions': 0
        }
        
        self.logger = logging.getLogger(__name__)
    
    def _generate_cache_key(self, prefix: str, **kwargs) -> str:
        """生成缓存键
        
        Args:
            prefix: 键前缀
            **kwargs: 键参数
            
        Returns:
            生成的缓存键
        """
        # 将参数排序并序列化
        sorted_params = sorted(kwargs.items())
        params_str = json.dumps(sorted_params, sort_keys=True)
        
        # 生成哈希值以避免键过长
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        
        return f"openbb_financial:{prefix}:{params_hash}"
    
    def _is_expired(self, cache_entry: Dict[str, Any]) -> bool:
        """检查缓存条目是否过期
        
        Args:
            cache_entry: 缓存条目
            
        Returns:
            是否过期
        """
        if 'expires_at' not in cache_entry:
            return False
        
        return time.time() > cache_entry['expires_at']
    
    def _evict_memory_cache(self):
        """清理内存缓存中的过期条目和最少使用的条目"""
        current_time = time.time()
        
        # 清理过期条目
        expired_keys = []
        for key, entry in self._memory_cache.items():
            if self._is_expired(entry):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._memory_cache[key]
            if key in self._cache_access_times:
                del self._cache_access_times[key]
        
        # 如果仍然超过最大大小，清理最少使用的条目
        if len(self._memory_cache) > self.max_memory_cache_size:
            # 按访问时间排序，删除最旧的条目
            sorted_keys = sorted(
                self._cache_access_times.items(),
                key=lambda x: x[1]
            )
            
            num_to_remove = len(self._memory_cache) - self.max_memory_cache_size
            for i in range(num_to_remove):
                key_to_remove = sorted_keys[i][0]
                if key_to_remove in self._memory_cache:
                    del self._memory_cache[key_to_remove]
                    del self._cache_access_times[key_to_remove]
                    self.stats['memory_evictions'] += 1
    
    def get(self, prefix: str, **kwargs) -> Optional[Any]:
        """获取缓存数据
        
        Args:
            prefix: 缓存键前缀
            **kwargs: 缓存键参数
            
        Returns:
            缓存的数据，如果不存在或过期则返回None
        """
        cache_key = self._generate_cache_key(prefix, **kwargs)
        
        try:
            # 首先尝试从内存缓存获取
            if cache_key in self._memory_cache:
                entry = self._memory_cache[cache_key]
                if not self._is_expired(entry):
                    self._cache_access_times[cache_key] = time.time()
                    self.stats['hits'] += 1
                    return entry['data']
                else:
                    # 过期，删除
                    del self._memory_cache[cache_key]
                    if cache_key in self._cache_access_times:
                        del self._cache_access_times[cache_key]
            
            # 尝试从Redis获取
            if self.redis_client and REDIS_AVAILABLE:
                try:
                    cached_data = self.redis_client.get(cache_key)
                    if cached_data:
                        data = json.loads(cached_data)
                        self.stats['hits'] += 1
                        
                        # 同时存储到内存缓存
                        self._set_memory_cache(cache_key, data, self.default_ttl)
                        return data
                except Exception as e:
                    self.logger.warning(f"Redis cache get error: {e}")
            
            self.stats['misses'] += 1
            return None
            
        except Exception as e:
            self.logger.error(f"Cache get error: {e}")
            self.stats['misses'] += 1
            return None
    
    def set(self, prefix: str, data: Any, ttl: Optional[int] = None, **kwargs):
        """设置缓存数据
        
        Args:
            prefix: 缓存键前缀
            data: 要缓存的数据
            ttl: 过期时间（秒），如果为None则使用默认值
            **kwargs: 缓存键参数
        """
        cache_key = self._generate_cache_key(prefix, **kwargs)
        ttl = ttl or self.default_ttl
        
        try:
            # 存储到内存缓存
            self._set_memory_cache(cache_key, data, ttl)
            
            # 存储到Redis
            if self.redis_client and REDIS_AVAILABLE:
                try:
                    serialized_data = json.dumps(data, default=str)
                    self.redis_client.setex(cache_key, ttl, serialized_data)
                except Exception as e:
                    self.logger.warning(f"Redis cache set error: {e}")
            
            self.stats['sets'] += 1
            
        except Exception as e:
            self.logger.error(f"Cache set error: {e}")
    
    def _set_memory_cache(self, cache_key: str, data: Any, ttl: int):
        """设置内存缓存
        
        Args:
            cache_key: 缓存键
            data: 数据
            ttl: 过期时间
        """
        expires_at = time.time() + ttl
        
        self._memory_cache[cache_key] = {
            'data': data,
            'expires_at': expires_at,
            'created_at': time.time()
        }
        
        self._cache_access_times[cache_key] = time.time()
        
        # 检查是否需要清理缓存
        if len(self._memory_cache) > self.max_memory_cache_size:
            self._evict_memory_cache()
    
    def delete(self, prefix: str, **kwargs):
        """删除缓存数据
        
        Args:
            prefix: 缓存键前缀
            **kwargs: 缓存键参数
        """
        cache_key = self._generate_cache_key(prefix, **kwargs)
        
        try:
            # 从内存缓存删除
            if cache_key in self._memory_cache:
                del self._memory_cache[cache_key]
            if cache_key in self._cache_access_times:
                del self._cache_access_times[cache_key]
            
            # 从Redis删除
            if self.redis_client and REDIS_AVAILABLE:
                try:
                    self.redis_client.delete(cache_key)
                except Exception as e:
                    self.logger.warning(f"Redis cache delete error: {e}")
            
            self.stats['deletes'] += 1
            
        except Exception as e:
            self.logger.error(f"Cache delete error: {e}")
    
    def clear_prefix(self, prefix: str):
        """清除指定前缀的所有缓存
        
        Args:
            prefix: 缓存键前缀
        """
        try:
            # 清理内存缓存
            keys_to_delete = []
            prefix_pattern = f"openbb_financial:{prefix}:"
            
            for key in self._memory_cache.keys():
                if key.startswith(prefix_pattern):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                if key in self._memory_cache:
                    del self._memory_cache[key]
                if key in self._cache_access_times:
                    del self._cache_access_times[key]
            
            # 清理Redis缓存
            if self.redis_client and REDIS_AVAILABLE:
                try:
                    pattern = f"{prefix_pattern}*"
                    keys = self.redis_client.keys(pattern)
                    if keys:
                        self.redis_client.delete(*keys)
                except Exception as e:
                    self.logger.warning(f"Redis cache clear prefix error: {e}")
            
        except Exception as e:
            self.logger.error(f"Cache clear prefix error: {e}")
    
    def clear_all(self):
        """清除所有缓存"""
        try:
            # 清理内存缓存
            self._memory_cache.clear()
            self._cache_access_times.clear()
            
            # 清理Redis缓存
            if self.redis_client and REDIS_AVAILABLE:
                try:
                    pattern = "openbb_financial:*"
                    keys = self.redis_client.keys(pattern)
                    if keys:
                        self.redis_client.delete(*keys)
                except Exception as e:
                    self.logger.warning(f"Redis cache clear all error: {e}")
            
        except Exception as e:
            self.logger.error(f"Cache clear all error: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            'hit_rate_percent': round(hit_rate, 2),
            'memory_cache_size': len(self._memory_cache),
            'max_memory_cache_size': self.max_memory_cache_size,
            'redis_available': self.redis_client is not None and REDIS_AVAILABLE
        }
    
    def get_cache_info(self, prefix: str, **kwargs) -> Optional[Dict[str, Any]]:
        """获取缓存条目信息
        
        Args:
            prefix: 缓存键前缀
            **kwargs: 缓存键参数
            
        Returns:
            缓存条目信息
        """
        cache_key = self._generate_cache_key(prefix, **kwargs)
        
        if cache_key in self._memory_cache:
            entry = self._memory_cache[cache_key]
            return {
                'key': cache_key,
                'created_at': datetime.fromtimestamp(entry['created_at']),
                'expires_at': datetime.fromtimestamp(entry['expires_at']),
                'is_expired': self._is_expired(entry),
                'last_accessed': datetime.fromtimestamp(
                    self._cache_access_times.get(cache_key, entry['created_at'])
                ),
                'source': 'memory'
            }
        
        return None
    
    def cleanup_expired(self):
        """清理所有过期的缓存条目"""
        try:
            # 清理内存缓存中的过期条目
            expired_keys = []
            for key, entry in self._memory_cache.items():
                if self._is_expired(entry):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._memory_cache[key]
                if key in self._cache_access_times:
                    del self._cache_access_times[key]
            
            self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
            
        except Exception as e:
            self.logger.error(f"Cache cleanup error: {e}")