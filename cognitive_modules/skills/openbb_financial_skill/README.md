# OpenBB金融数据技能插件

## 概述

OpenBB金融数据技能插件是为数字生命体系统设计的专业金融数据分析技能，基于OpenBB Platform构建，提供全面的金融市场数据查询、技术分析和投资建议功能。

## 功能特性

### 🔍 数据查询
- **股票数据**：实时报价、历史价格、基本面数据
- **加密货币**：主流数字货币价格和市场数据
- **外汇市场**：主要货币对汇率信息
- **宏观经济**：经济指标和政策数据

### 📊 技术分析
- **技术指标**：SMA、EMA、RSI、MACD、布林带等
- **趋势分析**：多时间框架趋势识别
- **交易信号**：买卖点提示和风险警告
- **图表分析**：价格走势和成交量分析

### 🧠 AI增强分析
- **智能解读**：自动解释技术指标含义
- **风险评估**：多维度风险等级评定
- **投资建议**：基于分析结果的个性化建议
- **情绪分析**：市场情绪和新闻影响评估

### 💼 投资组合管理
- **组合分析**：收益率和风险指标计算
- **资产配置**：多样化建议和优化
- **业绩跟踪**：历史表现和基准比较

## 安装配置

### 1. 环境要求

```bash
# Python版本要求
Python >= 3.8

# 系统要求
- 内存：至少4GB RAM
- 存储：至少1GB可用空间
- 网络：稳定的互联网连接
```

### 2. 依赖安装

```bash
# 进入技能目录
cd cognitive_modules/skills/openbb_financial_skill

# 安装依赖
pip install -r requirements.txt

# 安装TA-Lib（技术分析库）
# macOS
brew install ta-lib
pip install TA-Lib

# Ubuntu/Debian
sudo apt-get install libta-lib-dev
pip install TA-Lib

# Windows
# 下载预编译包：https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
pip install TA_Lib-0.4.25-cp39-cp39-win_amd64.whl
```

### 3. API密钥配置

```bash
# 复制配置模板
cp config/api_keys.json.example config/api_keys.json

# 编辑配置文件，添加你的API密钥
vim config/api_keys.json
```

配置示例：
```json
{
  "openbb": {
    "api_key": "your_openbb_api_key",
    "base_url": "https://api.openbb.co"
  },
  "alpha_vantage": {
    "api_key": "your_alpha_vantage_key"
  },
  "financial_modeling_prep": {
    "api_key": "your_fmp_key"
  }
}
```

### 4. 缓存配置（可选）

```bash
# 安装Redis（推荐用于生产环境）
# macOS
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# 配置Redis连接
# 编辑 config/skill_config.json
{
  "caching": {
    "backend": "redis",
    "redis_url": "redis://localhost:6379/0"
  }
}
```

## 使用方法

### 基本查询

```python
# 股票查询示例
用户："查询苹果股票价格"
用户："AAPL最新报价如何？"
用户："帮我看看特斯拉的股票表现"

# 加密货币查询
用户："比特币价格多少？"
用户："ETH最新价格走势如何？"
用户："查询狗狗币行情"

# 市场分析
用户："分析一下苹果股票的技术面"
用户："TSLA有什么投资建议？"
用户："当前市场趋势如何？"
```

### 高级功能

```python
# 投资组合分析
用户："分析我的投资组合表现"
用户："我的持仓风险如何？"
用户："给我一些资产配置建议"

# 技术分析
用户："AAPL的RSI指标如何？"
用户："分析比特币的MACD信号"
用户："当前市场有哪些交易机会？"
```

## 技术架构

### 核心组件

```
OpenBBFinancialSkill/
├── openbb_financial_skill.py    # 主技能类
├── data_manager.py              # 数据管理器
├── analysis_engine.py           # 分析引擎
├── utils/
│   ├── data_formatter.py        # 数据格式化
│   └── cache_manager.py         # 缓存管理
├── config/
│   ├── skill_config.json        # 技能配置
│   ├── data_sources.json        # 数据源配置
│   └── api_keys.json           # API密钥
└── tests/
    └── test_openbb_skill.py     # 单元测试
```

### 数据流程

```
用户查询 → 意图识别 → 参数提取 → 数据获取 → 分析处理 → 结果格式化 → 响应用户
    ↓           ↓           ↓           ↓           ↓           ↓
自然语言   →  意图类型   →  交易品种   →  API调用   →  技术分析   →  用户友好
处理         分类         参数         缓存策略     风险评估     格式输出
```

### 缓存策略

- **实时数据**：缓存60秒，适用于股票报价
- **历史数据**：缓存1小时，适用于K线数据
- **基本面数据**：缓存24小时，适用于财务指标
- **分析结果**：缓存5分钟，适用于技术分析

## API参考

### 主要方法

#### `can_handle(query: str) -> bool`
检查技能是否能处理用户查询

```python
# 示例
skill.can_handle("查询苹果股票价格")  # True
skill.can_handle("今天天气如何")      # False
```

#### `handle(query: str) -> Dict[str, Any]`
处理用户查询并返回结果

```python
# 返回格式
{
    "success": True,
    "data": {
        "symbol": "AAPL",
        "price": 150.25,
        "change": 2.15,
        "change_percent": 1.45
    },
    "analysis": {
        "trend": "bullish",
        "signals": ["价格突破短期均线"],
        "recommendations": ["技术面看涨，可考虑买入"]
    },
    "message": "苹果公司(AAPL)当前价格$150.25，上涨$2.15(+1.45%)..."
}
```

### 数据管理器API

#### `get_stock_data(symbol: str, data_type: str) -> Dict[str, Any]`
获取股票数据

```python
# 获取实时报价
data = await data_manager.get_stock_data("AAPL", "quote")

# 获取历史数据
data = await data_manager.get_stock_data("AAPL", "historical", period="1y")

# 获取基本面数据
data = await data_manager.get_stock_data("AAPL", "fundamentals")
```

### 分析引擎API

#### `analyze_stock(symbol: str, data: Dict) -> AnalysisResult`
分析股票数据

```python
# 综合分析
result = await engine.analyze_stock(
    "AAPL", 
    stock_data, 
    analysis_types=["technical", "fundamental", "sentiment"]
)

# 仅技术分析
result = await engine.analyze_stock(
    "AAPL", 
    stock_data, 
    analysis_types=["technical"]
)
```

## 测试

### 运行单元测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_openbb_skill.py::TestOpenBBFinancialSkill -v

# 生成覆盖率报告
python -m pytest tests/ --cov=. --cov-report=html
```

### 手动测试

```bash
# 进入Python环境
python

# 导入并测试技能
from openbb_financial_skill import OpenBBFinancialSkill

skill = OpenBBFinancialSkill()
print(skill.can_handle("查询苹果股票价格"))  # 应该返回True

# 异步测试
import asyncio

async def test():
    result = await skill.handle("查询苹果股票价格")
    print(result)

asyncio.run(test())
```

## 性能优化

### 缓存优化
- 启用Redis缓存以提高响应速度
- 合理设置缓存TTL避免数据过期
- 使用缓存预热策略

### 并发处理
- 使用异步编程提高并发性能
- 实现连接池管理API请求
- 合理设置请求超时和重试机制

### 内存管理
- 定期清理过期缓存
- 限制历史数据加载量
- 使用数据分页减少内存占用

## 故障排除

### 常见问题

#### 1. OpenBB安装失败
```bash
# 解决方案：使用conda安装
conda install -c conda-forge openbb

# 或者使用pip安装特定版本
pip install openbb==4.1.0
```

#### 2. TA-Lib安装失败
```bash
# macOS解决方案
brew install ta-lib
export TA_INCLUDE_PATH=$(brew --prefix ta-lib)/include
export TA_LIBRARY_PATH=$(brew --prefix ta-lib)/lib
pip install TA-Lib

# Linux解决方案
sudo apt-get install build-essential
wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
tar -xzf ta-lib-0.4.0-src.tar.gz
cd ta-lib/
./configure --prefix=/usr
make
sudo make install
pip install TA-Lib
```

#### 3. API密钥错误
- 检查API密钥是否正确配置
- 确认API密钥权限和配额
- 查看API提供商的使用限制

#### 4. 缓存连接失败
```bash
# 检查Redis服务状态
redis-cli ping

# 重启Redis服务
sudo systemctl restart redis-server

# 检查连接配置
redis-cli -h localhost -p 6379
```

### 日志调试

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看技能日志
from utilities.unified_logger import get_unified_logger
logger = get_unified_logger("openbb_financial")
logger.setLevel(logging.DEBUG)
```

## 贡献指南

### 开发环境设置

```bash
# 克隆项目
git clone <repository_url>
cd yanran_digital_life

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -r cognitive_modules/skills/openbb_financial_skill/requirements.txt
pip install -e .
```

### 代码规范

```bash
# 代码格式化
black cognitive_modules/skills/openbb_financial_skill/

# 代码检查
flake8 cognitive_modules/skills/openbb_financial_skill/

# 类型检查
mypy cognitive_modules/skills/openbb_financial_skill/
```

### 提交流程

1. Fork项目仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 支持

如有问题或建议，请通过以下方式联系：

- 提交Issue：[GitHub Issues](https://github.com/your-repo/issues)
- 邮件支持：<EMAIL>
- 文档网站：[https://docs.example.com](https://docs.example.com)

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持股票和加密货币数据查询
- 实现基础技术分析功能
- 集成OpenBB Platform
- 添加缓存和错误处理机制

### 计划功能
- [ ] 新闻情绪分析
- [ ] 社交媒体情绪监控
- [ ] 更多技术指标支持
- [ ] 投资组合优化算法
- [ ] 实时价格预警
- [ ] 移动端适配
- [ ] 多语言支持