{"_note": "这是API密钥配置示例文件，请复制为api_keys.json并填入真实的API密钥", "_security_warning": "请勿将包含真实API密钥的文件提交到版本控制系统", "openbb": {"api_key": "your_openbb_api_key_here", "base_url": "http://localhost:8000", "timeout": 30}, "data_providers": {"alpha_vantage": {"api_key": "OEDV4AQLTVXQN1WX", "base_url": "https://www.alphavantage.co/query", "rate_limit": 5}, "financial_modeling_prep": {"api_key": "UMBcWUWglJHXOQaRSi5cTCRcBfOW21cX", "base_url": "https://financialmodelingprep.com/api", "rate_limit": 250}, "polygon": {"api_key": "********************************", "base_url": "https://api.polygon.io", "rate_limit": 5}, "quandl": {"api_key": "Bxqw381biYKazqHX39sK", "base_url": "https://www.quandl.com/api/v3", "rate_limit": 50}, "fred": {"api_key": "3ad4bf2b06ea616b37e2e4cb6b8e79d1", "base_url": "https://api.stlouisfed.org/fred", "rate_limit": 120}}, "news_providers": {"news_api": {"api_key": "22882e66ac894982943350653a63feed", "base_url": "https://newsapi.org/v2", "rate_limit": 1000}}, "ai_services": {"openai": {"api_key": "your_openai_api_key", "model": "gpt-4", "max_tokens": 2000}, "anthropic": {"api_key": "your_anthropic_api_key", "model": "claude-3-sonnet", "max_tokens": 2000}}, "encryption": {"enabled": true, "algorithm": "AES-256-GCM", "key_derivation": "PBKDF2", "salt_length": 32}, "security": {"rotate_keys_days": 90, "log_api_usage": true, "mask_keys_in_logs": true, "validate_ssl": true}}