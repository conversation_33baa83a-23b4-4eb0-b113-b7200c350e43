{"skill_info": {"skill_id": "openbb_financial_skill", "name": "OpenBB金融数据技能", "description": "基于OpenBB平台的金融数据查询和分析技能，支持股票、期权、加密货币、外汇、宏观经济、固定收益等多种金融工具", "version": "1.0.0", "author": "Yanran Digital Life Team", "category": "financial", "tags": ["finance", "stock", "crypto", "forex", "analysis", "openbb"]}, "capabilities": {"data_sources": ["stocks", "options", "crypto", "forex", "economy", "fixed_income", "etf", "mutual_funds"], "analysis_types": ["technical_analysis", "fundamental_analysis", "risk_assessment", "trend_prediction", "portfolio_analysis"], "supported_languages": ["zh", "en"], "real_time_data": true, "historical_data": true, "ai_enhanced": true}, "intent_patterns": {"stock_query": ["查看.*股价", ".*股票.*信息", ".*公司.*股价", "stock price.*", "查询.*股票"], "crypto_query": ["比特币.*价格", ".*加密货币.*", "crypto.*price", "数字货币.*"], "market_analysis": ["市场分析", "技术分析", "基本面分析", "market analysis", "investment advice"], "portfolio_analysis": ["投资组合.*", "portfolio.*", "资产配置.*"]}, "response_settings": {"max_response_length": 2000, "include_charts": true, "include_analysis": true, "personalized_advice": true, "risk_warnings": true}, "cache_settings": {"enable_cache": true, "cache_ttl_seconds": 300, "max_cache_size": 1000}, "rate_limiting": {"requests_per_minute": 60, "requests_per_hour": 1000}, "dependencies": {"required": ["openbb>=4.0.0", "pandas>=1.5.0", "numpy>=1.21.0", "requests>=2.28.0"], "optional": ["matplotlib>=3.5.0", "plotly>=5.0.0", "yfinance>=0.2.0"]}}