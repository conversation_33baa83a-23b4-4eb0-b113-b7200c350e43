"""OpenBB金融数据技能

基于OpenBB平台的金融数据查询和分析技能，支持股票、期权、加密货币、
外汇、宏观经济、固定收益等多种金融工具的数据获取和智能分析。
"""

import os
import json
import re
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import logging

# 导入核心技能基类
try:
    from core.skill_manager import Skill
except ImportError:
    # 如果无法导入，创建一个基础的Skill类
    class Skill:
        def __init__(self, skill_id: str, name: str, description: str, version: str = "1.0.0"):
            self.skill_id = skill_id
            self.name = name
            self.description = description
            self.version = version
            self.enabled = False
            self.capabilities = []
            self.metadata = {}
            self.dependencies = []
            self.usage_count = 0
            self.last_used = None
            self.loaded_at = None
        
        def can_handle(self, context: Dict[str, Any]) -> bool:
            return False
        
        def handle(self, context: Dict[str, Any]) -> Dict[str, Any]:
            return {"success": False, "message": "未实现的技能处理方法"}

# 导入核心组件
try:
    from .data_manager import OpenBBDataManager
    from .analysis_engine import FinancialAnalysisEngine
    from .utils.data_formatter import DataFormatter
    from .utils.cache_manager import CacheManager
except ImportError:
    from data_manager import OpenBBDataManager
    from analysis_engine import FinancialAnalysisEngine
    from utils.data_formatter import DataFormatter
    from utils.cache_manager import CacheManager

# 导入配置和日志
try:
    from utilities.unified_logger import get_unified_logger
except ImportError:
    import logging
    def get_unified_logger(name):
        return logging.getLogger(name)


class OpenBBFinancialSkill(Skill):
    """OpenBB金融数据技能类"""
    
    def __init__(self):
        """初始化OpenBB金融技能"""
        super().__init__(
            skill_id="openbb_financial_skill",
            name="OpenBB金融数据技能",
            description="基于OpenBB平台的金融数据查询和分析技能，支持多种金融工具",
            version="1.0.0"
        )
        
        # 设置技能能力
        self.capabilities = [
            "financial_data_query",
            "stock_analysis",
            "crypto_analysis",
            "forex_analysis",
            "economic_data",
            "technical_analysis",
            "fundamental_analysis",
            "risk_assessment",
            "portfolio_analysis",
            "market_sentiment"
        ]
        
        # 设置依赖
        self.dependencies = [
            "openbb>=4.0.0",
            "pandas>=1.5.0",
            "numpy>=1.21.0",
            "requests>=2.28.0"
        ]
        
        # 初始化日志
        self.logger = get_unified_logger(f"skill.{self.skill_id}")
        
        # 配置文件路径
        self.config_dir = os.path.join(os.path.dirname(__file__), "config")
        
        # 初始化组件（延迟加载）
        self._data_manager = None
        self._analysis_engine = None
        self._data_formatter = None
        self._cache_manager = None
        
        # 配置信息
        self._skill_config = None
        self._data_sources_config = None
        self._api_keys_config = None
        
        # 意图识别模式
        self._intent_patterns = {
            "stock_query": [
                r"(股票|股价|股份)",
                r"([A-Z]{1,5}|\d{6})",
                r"查询.*(股票|股价|价格)",
                r"(苹果|微软|谷歌|特斯拉|亚马逊|AAPL|MSFT|GOOGL|TSLA|AMZN)",
                r".*股票.*",
                r".*股价.*",
                r".*报价.*"
            ],
            "crypto_query": [
                r"(比特币|BTC|以太坊|ETH|加密货币)",
                r"(数字货币|虚拟货币|币价)"
            ],
            "portfolio_analysis": [
                r"(投资组合|组合|持仓)",
                r".*投资组合.*表现.*",
                r".*组合.*分析.*"
            ],
            "market_analysis": [
                r"(市场分析|行情分析|技术分析|基本面分析)",
                r"(趋势|走势|分析|预测)"
            ]
        }
        
        # 技能状态
        self._initialized = False
        self._loading = False
        
        # 默认启用技能
        self.enabled = True
        
        self.logger.info(f"OpenBB金融技能已创建: {self.name}")
    
    @property
    def data_manager(self) -> OpenBBDataManager:
        """获取数据管理器（延迟加载）"""
        if self._data_manager is None:
            self._data_manager = OpenBBDataManager(
                config=self._get_data_sources_config(),
                api_keys=self._get_api_keys_config(),
                cache_manager=self.cache_manager
            )
        return self._data_manager
    
    @property
    def analysis_engine(self) -> FinancialAnalysisEngine:
        """获取分析引擎（延迟加载）"""
        if self._analysis_engine is None:
            self._analysis_engine = FinancialAnalysisEngine(
                data_manager=self.data_manager,
                config=self._get_skill_config()
            )
        return self._analysis_engine
    
    @property
    def data_formatter(self) -> DataFormatter:
        """获取数据格式化器（延迟加载）"""
        if self._data_formatter is None:
            self._data_formatter = DataFormatter()
        return self._data_formatter
    
    @property
    def cache_manager(self) -> CacheManager:
        """获取缓存管理器（延迟加载）"""
        if self._cache_manager is None:
            # 尝试获取Redis客户端
            redis_client = self._get_redis_client()
            cache_config = self._get_skill_config().get("cache_settings", {})
            
            self._cache_manager = CacheManager(
                redis_client=redis_client,
                default_ttl=cache_config.get("cache_ttl_seconds", 300),
                max_memory_cache_size=cache_config.get("max_cache_size", 1000)
            )
        return self._cache_manager
    
    def _get_redis_client(self):
        """获取Redis客户端"""
        try:
            import redis
            # 这里可以从系统配置中获取Redis连接信息
            # 暂时使用默认配置
            return redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        except Exception as e:
            self.logger.warning(f"无法连接Redis: {e}")
            return None
    
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件
        
        Args:
            config_file: 配置文件名
            
        Returns:
            配置字典
        """
        config_path = os.path.join(self.config_dir, config_file)
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {config_path}, 错误: {e}")
            return {}
    
    def _get_skill_config(self) -> Dict[str, Any]:
        """获取技能配置"""
        if self._skill_config is None:
            self._skill_config = self._load_config("skill_config.json")
        return self._skill_config
    
    def _get_data_sources_config(self) -> Dict[str, Any]:
        """获取数据源配置"""
        if self._data_sources_config is None:
            self._data_sources_config = self._load_config("data_sources.json")
        return self._data_sources_config
    
    def _get_api_keys_config(self) -> Dict[str, Any]:
        """获取API密钥配置"""
        if self._api_keys_config is None:
            # 首先尝试加载真实的API密钥文件
            api_keys_path = os.path.join(self.config_dir, "api_keys.json")
            if os.path.exists(api_keys_path):
                self._api_keys_config = self._load_config("api_keys.json")
            else:
                self.logger.warning("API密钥文件不存在，请根据api_keys.json.example创建")
                self._api_keys_config = {}
        return self._api_keys_config
    
    def on_load(self) -> bool:
        """技能加载时调用"""
        try:
            self.logger.info("开始加载OpenBB金融技能...")
            
            # 加载配置
            skill_config = self._get_skill_config()
            
            # 加载意图识别模式（保留默认模式，合并配置文件中的模式）
            config_patterns = skill_config.get("intent_patterns", {})
            if config_patterns:
                # 合并配置文件中的模式
                for intent_type, patterns in config_patterns.items():
                    if intent_type in self._intent_patterns:
                        self._intent_patterns[intent_type].extend(patterns)
                    else:
                        self._intent_patterns[intent_type] = patterns
            
            # 设置元数据
            self.metadata = {
                "supported_markets": skill_config.get("capabilities", {}).get("data_sources", []),
                "analysis_types": skill_config.get("capabilities", {}).get("analysis_types", []),
                "real_time_data": skill_config.get("capabilities", {}).get("real_time_data", False),
                "ai_enhanced": skill_config.get("capabilities", {}).get("ai_enhanced", False)
            }
            
            self._initialized = True
            self.logger.info("OpenBB金融技能加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"OpenBB金融技能加载失败: {e}")
            return False
    
    def on_unload(self) -> bool:
        """技能卸载时调用"""
        try:
            self.logger.info("开始卸载OpenBB金融技能...")
            
            # 清理缓存
            if self._cache_manager:
                self._cache_manager.clear_all()
            
            # 重置组件
            self._data_manager = None
            self._analysis_engine = None
            self._data_formatter = None
            self._cache_manager = None
            
            self._initialized = False
            self.logger.info("OpenBB金融技能卸载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"OpenBB金融技能卸载失败: {e}")
            return False
    
    def on_enable(self) -> bool:
        """技能启用时调用"""
        try:
            if not self._initialized:
                if not self.on_load():
                    return False
            
            self.enabled = True
            self.logger.info("OpenBB金融技能已启用")
            return True
            
        except Exception as e:
            self.logger.error(f"启用OpenBB金融技能失败: {e}")
            return False
    
    def on_disable(self) -> bool:
        """技能禁用时调用"""
        try:
            self.enabled = False
            self.logger.info("OpenBB金融技能已禁用")
            return True
            
        except Exception as e:
            self.logger.error(f"禁用OpenBB金融技能失败: {e}")
            return False
    
    def can_handle(self, context: Dict[str, Any]) -> bool:
        """检查技能是否可以处理给定上下文
        
        Args:
            context: 上下文信息，包含用户输入、意图等
            
        Returns:
            是否可以处理
        """
        if not self.enabled or not self._initialized:
            return False
        
        try:
            # 获取用户输入文本
            user_input = context.get("user_input", "")
            if not user_input:
                user_input = context.get("text", "")
            
            if not user_input:
                return False
            
            # 检查意图模式匹配
            for intent_type, patterns in self._intent_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, user_input, re.IGNORECASE):
                        self.logger.debug(f"匹配到意图: {intent_type}, 模式: {pattern}")
                        return True
            
            # 检查是否包含金融相关关键词
            financial_keywords = [
                "股票", "股价", "股市", "投资", "基金", "债券",
                "比特币", "加密货币", "数字货币", "外汇", "汇率",
                "期权", "期货", "市场", "分析", "财经", "金融",
                "stock", "crypto", "bitcoin", "forex", "market",
                "investment", "trading", "finance", "analysis"
            ]
            
            for keyword in financial_keywords:
                if keyword.lower() in user_input.lower():
                    self.logger.debug(f"匹配到金融关键词: {keyword}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查处理能力时发生错误: {e}")
            return False
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """执行金融数据技能（技能管理器调用接口）
        
        Args:
            **kwargs: 技能参数，包含:
                - input_text: 用户输入文本
                - user_id: 用户ID
                - session_id: 会话ID
                - intent_data: 意图数据
                - 其他参数...
                
        Returns:
            执行结果
        """
        # 🔥 老王修复：技能管理器调用的是execute方法，不是handle方法
        # 将kwargs转换为context格式，然后调用handle方法
        context = {
            "user_input": kwargs.get("input_text", ""),
            "text": kwargs.get("input_text", ""),
            "user_id": kwargs.get("user_id", ""),
            "session_id": kwargs.get("session_id", ""),
            "intent_data": kwargs.get("intent_data", {}),
            "requires_realtime_data": kwargs.get("requires_realtime_data", False)
        }
        
        # 添加其他kwargs参数到context中
        for key, value in kwargs.items():
            if key not in context:
                context[key] = value
        
        self.logger.info(f"💰 OpenBB金融技能execute调用，转换context: {context}")
        
        # 调用原有的handle方法
        return self.handle(context)
    
    def handle(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理金融数据请求
        
        Args:
            context: 上下文信息
            
        Returns:
            处理结果
        """
        if not self.enabled or not self._initialized:
            return {
                "success": False,
                "message": "OpenBB金融技能未启用或未初始化",
                "skill_id": self.skill_id
            }
        
        try:
            # 更新使用统计
            self.last_used = datetime.now()
            self.usage_count += 1
            
            # 获取用户输入
            user_input = context.get("user_input", "")
            if not user_input:
                user_input = context.get("text", "")
            
            if not user_input:
                return {
                    "success": False,
                    "message": "未提供有效的用户输入",
                    "skill_id": self.skill_id
                }
            
            self.logger.info(f"处理金融数据请求: {user_input}")
            
            # 识别意图和提取参数
            intent_result = self._recognize_intent(user_input)
            
            if not intent_result["success"]:
                return {
                    "success": False,
                    "message": "无法识别金融数据请求意图",
                    "skill_id": self.skill_id
                }
            
            intent_type = intent_result["intent_type"]
            parameters = intent_result["parameters"]
            
            # 根据意图类型处理请求
            if intent_type == "stock_query":
                result = self._handle_stock_query(parameters, context)
            elif intent_type == "crypto_query":
                result = self._handle_crypto_query(parameters, context)
            elif intent_type == "forex_query":
                result = self._handle_forex_query(parameters, context)
            elif intent_type == "market_analysis":
                result = self._handle_market_analysis(parameters, context)
            elif intent_type == "portfolio_analysis":
                result = self._handle_portfolio_analysis(parameters, context)
            else:
                result = self._handle_general_financial_query(parameters, context)
            
            # 添加技能标识
            result["skill_id"] = self.skill_id
            result["skill_name"] = self.name
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理金融数据请求时发生错误: {e}")
            return {
                "success": False,
                "message": f"处理请求时发生错误: {str(e)}",
                "skill_id": self.skill_id
            }
    
    def _recognize_intent(self, user_input: str) -> Dict[str, Any]:
        """识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        try:
            # 检查各种意图模式
            for intent_type, patterns in self._intent_patterns.items():
                for pattern in patterns:
                    match = re.search(pattern, user_input, re.IGNORECASE)
                    if match:
                        # 提取参数
                        parameters = self._extract_parameters(user_input, intent_type)
                        
                        return {
                            "success": True,
                            "intent_type": intent_type,
                            "parameters": parameters,
                            "confidence": 0.8  # 基于模式匹配的置信度
                        }
            
            # 如果没有匹配到特定模式，尝试通用金融查询
            parameters = self._extract_parameters(user_input, "general")
            if parameters:
                return {
                    "success": True,
                    "intent_type": "general_financial_query",
                    "parameters": parameters,
                    "confidence": 0.5
                }
            
            return {
                "success": False,
                "message": "无法识别意图"
            }
            
        except Exception as e:
            self.logger.error(f"意图识别失败: {e}")
            return {
                "success": False,
                "message": f"意图识别失败: {str(e)}"
            }
    
    def _extract_parameters(self, user_input: str, intent_type: str) -> Dict[str, Any]:
        """从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            intent_type: 意图类型
            
        Returns:
            提取的参数字典
        """
        parameters = {}
        
        try:
            # 根据意图类型提取不同的符号
            if intent_type == "stock_query":
                # 提取股票代码
                stock_patterns = [
                    r'([A-Z]{1,5})(?=[\s\u4e00-\u9fa5]|$)',  # 大写字母股票代码，后面跟空格、中文或结尾
                    r'(\d{6})(?=[\s\u4e00-\u9fa5]|$)',       # 6位数字股票代码（A股）
                ]
                
                for pattern in stock_patterns:
                    matches = re.findall(pattern, user_input.upper())
                    if matches:
                        parameters["symbol"] = matches[0]
                        break
            
            elif intent_type == "crypto_query":
                # 提取加密货币符号
                crypto_mapping = {
                    "比特币": "BTC",
                    "以太坊": "ETH", 
                    "以太币": "ETH",
                    "莱特币": "LTC",
                    "瑞波币": "XRP"
                }
                
                # 先检查中文名称映射
                for crypto_name, symbol in crypto_mapping.items():
                    if crypto_name in user_input:
                        parameters["symbol"] = symbol
                        break
                
                # 如果没有找到中文名称，尝试提取英文符号
                if "symbol" not in parameters:
                    crypto_patterns = [
                        r'([A-Z]{2,5})(?=[\s\u4e00-\u9fa5]|$)',  # 大写字母加密货币符号
                    ]
                    
                    for pattern in crypto_patterns:
                        matches = re.findall(pattern, user_input.upper())
                        if matches:
                            parameters["symbol"] = matches[0]
                            break
            
            # 提取公司名称
            company_patterns = [
                r'([\u4e00-\u9fa5]+)(?:公司|股份|集团|科技|有限)',  # 中文公司名
                r'(Apple|Microsoft|Google|Tesla|Amazon|Meta|Netflix)',  # 常见英文公司名
            ]
            
            for pattern in company_patterns:
                matches = re.findall(pattern, user_input, re.IGNORECASE)
                if matches:
                    parameters["company_name"] = matches[0]
                    break
            
            # 特殊处理常见公司名称（只在股票查询时应用）
            if intent_type == "stock_query":
                company_mapping = {
                    "苹果": "AAPL",
                    "微软": "MSFT", 
                    "谷歌": "GOOGL",
                    "特斯拉": "TSLA",
                    "亚马逊": "AMZN"
                }
                
                for company_name, symbol in company_mapping.items():
                    if company_name in user_input:
                        parameters["company_name"] = company_name
                        parameters["symbol"] = symbol  # 直接设置symbol，不检查是否已存在
                        break
            

            
            # 提取时间范围
            time_patterns = [
                r'(\d+)天',
                r'(\d+)周',
                r'(\d+)月',
                r'(\d+)年',
                r'今天',
                r'昨天',
                r'本周',
                r'本月',
                r'今年'
            ]
            
            for pattern in time_patterns:
                matches = re.findall(pattern, user_input)
                if matches:
                    parameters["time_range"] = matches[0] if isinstance(matches[0], str) else f"{matches[0]}天"
                    break
            
            # 提取分析类型
            analysis_keywords = {
                "技术分析": "technical",
                "基本面分析": "fundamental",
                "风险分析": "risk",
                "趋势分析": "trend",
                "投资建议": "recommendation"
            }
            
            for keyword, analysis_type in analysis_keywords.items():
                if keyword in user_input:
                    parameters["analysis_type"] = analysis_type
                    break
            
            return parameters
            
        except Exception as e:
            self.logger.error(f"参数提取失败: {e}")
            return {}
    
    def _handle_stock_query(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理股票查询请求"""
        # 这里将在第三阶段实现具体的股票查询逻辑
        return {
            "success": True,
            "message": "股票查询功能正在开发中",
            "data_type": "stock",
            "parameters": parameters
        }
    
    def _handle_crypto_query(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理加密货币查询请求"""
        # 这里将在第三阶段实现具体的加密货币查询逻辑
        return {
            "success": True,
            "message": "加密货币查询功能正在开发中",
            "data_type": "crypto",
            "parameters": parameters
        }
    
    def _handle_forex_query(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理外汇查询请求"""
        # 这里将在第三阶段实现具体的外汇查询逻辑
        return {
            "success": True,
            "message": "外汇查询功能正在开发中",
            "data_type": "forex",
            "parameters": parameters
        }
    
    def _handle_market_analysis(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理市场分析请求"""
        # 这里将在第四阶段实现具体的市场分析逻辑
        return {
            "success": True,
            "message": "市场分析功能正在开发中",
            "data_type": "analysis",
            "parameters": parameters
        }
    
    def _handle_portfolio_analysis(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理投资组合分析请求"""
        # 这里将在第四阶段实现具体的投资组合分析逻辑
        return {
            "success": True,
            "message": "投资组合分析功能正在开发中",
            "data_type": "portfolio",
            "parameters": parameters
        }
    
    def _handle_general_financial_query(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理通用金融查询请求"""
        return {
            "success": True,
            "message": "通用金融查询功能正在开发中",
            "data_type": "general",
            "parameters": parameters
        }
    
    def get_supported_capabilities(self) -> List[str]:
        """获取支持的能力列表"""
        return self.capabilities.copy()
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取技能健康状态"""
        try:
            status = {
                "skill_id": self.skill_id,
                "name": self.name,
                "enabled": self.enabled,
                "initialized": self._initialized,
                "usage_count": self.usage_count,
                "last_used": self.last_used.isoformat() if self.last_used else None,
                "components": {
                    "data_manager": self._data_manager is not None,
                    "analysis_engine": self._analysis_engine is not None,
                    "cache_manager": self._cache_manager is not None
                }
            }
            
            # 添加缓存统计
            if self._cache_manager:
                status["cache_stats"] = self._cache_manager.get_stats()
            
            return status
            
        except Exception as e:
            return {
                "skill_id": self.skill_id,
                "error": f"获取健康状态失败: {str(e)}"
            }