"""OpenBB金融技能测试

测试OpenBB金融技能的各个组件和功能。
"""

import unittest
import asyncio
import json
import os
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

# 添加项目路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from openbb_financial_skill import OpenBBFinancialSkill
from data_manager import OpenBBDataManager
from analysis_engine import FinancialAnalysisEngine
from utils.data_formatter import DataFormatter
from utils.cache_manager import CacheManager


class TestOpenBBFinancialSkill(unittest.TestCase):
    """OpenBB金融技能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.skill = OpenBBFinancialSkill()
        
        # 初始化技能
        self.skill.on_load()
        
        # 模拟配置
        self.mock_config = {
            "skill_id": "openbb_financial_skill",
            "name": "OpenBB金融数据技能",
            "version": "1.0.0",
            "capabilities": ["stock_query", "crypto_query", "market_analysis"]
        }
        
        # 模拟股票数据
        self.mock_stock_data = {
            "success": True,
            "symbol": "AAPL",
            "name": "Apple Inc.",
            "price": 150.25,
            "change": 2.15,
            "change_percent": 1.45,
            "volume": 50000000,
            "market_cap": 2500000000000,
            "timestamp": datetime.now().isoformat(),
            "source": "test"
        }
    
    def test_skill_initialization(self):
        """测试技能初始化"""
        self.assertEqual(self.skill.skill_id, "openbb_financial_skill")
        self.assertEqual(self.skill.name, "OpenBB金融数据技能")
        self.assertEqual(self.skill.version, "1.0.0")
        self.assertIsInstance(self.skill.capabilities, list)
        self.assertTrue(self.skill.enabled)
    
    def test_can_handle_stock_query(self):
        """测试股票查询能力检查"""
        # 正面测试
        test_cases = [
            "查询苹果股票价格",
            "AAPL股价如何",
            "帮我看看特斯拉的股票",
            "TSLA最新报价"
        ]
        
        for query in test_cases:
            with self.subTest(query=query):
                context = {"text": query}
                self.assertTrue(self.skill.can_handle(context))
    
    def test_can_handle_crypto_query(self):
        """测试加密货币查询能力检查"""
        test_cases = [
            "比特币价格多少",
            "BTC最新价格",
            "以太坊行情如何",
            "ETH价格走势"
        ]
        
        for query in test_cases:
            with self.subTest(query=query):
                context = {"text": query}
                self.assertTrue(self.skill.can_handle(context))
    
    def test_can_handle_market_analysis(self):
        """测试市场分析能力检查"""
        test_cases = [
            "分析一下苹果股票",
            "AAPL技术分析",
            "市场趋势如何",
            "投资建议"
        ]
        
        for query in test_cases:
            with self.subTest(query=query):
                context = {"text": query}
                self.assertTrue(self.skill.can_handle(context))
    
    def test_cannot_handle_irrelevant_query(self):
        """测试无关查询的拒绝"""
        test_cases = [
            "今天天气如何",
            "帮我写一首诗",
            "什么是人工智能",
            "推荐一部电影"
        ]
        
        for query in test_cases:
            with self.subTest(query=query):
                context = {"text": query}
                self.assertFalse(self.skill.can_handle(context))
    
    def test_recognize_intent(self):
        """测试意图识别"""
        test_cases = [
            ("查询苹果股票价格", "stock_query"),
            ("比特币价格多少", "crypto_query"),
            ("分析AAPL股票", "stock_query"),
            ("我的投资组合表现如何", "portfolio_analysis")
        ]
        
        for query, expected_intent in test_cases:
            with self.subTest(query=query):
                result = self.skill._recognize_intent(query)
                self.assertTrue(result["success"])
                self.assertEqual(result["intent_type"], expected_intent)
    
    def test_extract_parameters(self):
        """测试参数提取"""
        test_cases = [
            ("查询苹果股票价格", "stock_query", {"symbol": "AAPL"}),
            ("TSLA股价如何", "stock_query", {"symbol": "TSLA"}),
            ("比特币价格多少", "crypto_query", {"symbol": "BTC"}),
            ("ETH最新价格", "crypto_query", {"symbol": "ETH"})
        ]
        
        for query, intent, expected_params in test_cases:
            with self.subTest(query=query):
                params = self.skill._extract_parameters(query, intent)
                for key, value in expected_params.items():
                    self.assertIn(key, params)
                    self.assertEqual(params[key], value)
    
    @patch('openbb_financial_skill.OpenBBFinancialSkill._get_data_manager')
    async def test_handle_stock_query(self, mock_get_data_manager):
        """测试股票查询处理"""
        # 模拟数据管理器
        mock_data_manager = AsyncMock()
        mock_data_manager.get_stock_data.return_value = self.mock_stock_data
        mock_get_data_manager.return_value = mock_data_manager
        
        # 测试股票查询
        context = {"text": "查询苹果股票价格"}
        response = await self.skill.handle(context)
        
        self.assertIsInstance(response, dict)
        self.assertTrue(response.get("success", False))
        self.assertIn("data", response)
        self.assertIn("message", response)
    
    @patch('openbb_financial_skill.OpenBBFinancialSkill._get_data_manager')
    async def test_handle_crypto_query(self, mock_get_data_manager):
        """测试加密货币查询处理"""
        # 模拟数据管理器
        mock_data_manager = AsyncMock()
        mock_crypto_data = {
            "success": True,
            "symbol": "BTC",
            "name": "Bitcoin",
            "price": 45000.00,
            "change": 1200.00,
            "change_percent": 2.74
        }
        mock_data_manager.get_crypto_data.return_value = mock_crypto_data
        mock_get_data_manager.return_value = mock_data_manager
        
        # 测试加密货币查询
        context = {"text": "比特币价格多少"}
        response = await self.skill.handle(context)
        
        self.assertIsInstance(response, dict)
        self.assertTrue(response.get("success", False))
        self.assertIn("data", response)
        self.assertIn("message", response)
    
    def test_lifecycle_methods(self):
        """测试生命周期方法"""
        # 测试加载
        self.skill.on_load()
        self.assertTrue(self.skill.enabled)
        
        # 测试启用
        self.skill.on_enable()
        self.assertTrue(self.skill.enabled)
        
        # 测试禁用
        self.skill.on_disable()
        self.assertFalse(self.skill.enabled)
        
        # 测试卸载
        self.skill.on_unload()
        # 卸载后应该清理资源


class TestOpenBBDataManager(unittest.TestCase):
    """数据管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            "data_providers": {
                "openbb": {"rate_limit": 60},
                "yahoo_finance": {"rate_limit": 100}
            }
        }
        self.api_keys = {
            "openbb": {"api_key": "test_key"}
        }
        self.data_manager = OpenBBDataManager(self.config, self.api_keys)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.data_manager)
        self.assertEqual(self.data_manager.config, self.config)
        self.assertEqual(self.data_manager.api_keys, self.api_keys)
    
    def test_rate_limit_check(self):
        """测试速率限制检查"""
        # 第一次请求应该通过
        self.assertTrue(self.data_manager._check_rate_limit("openbb"))
        
        # 模拟大量请求
        for _ in range(60):
            self.data_manager._check_rate_limit("openbb")
        
        # 超过限制后应该被拒绝
        self.assertFalse(self.data_manager._check_rate_limit("openbb"))
    
    @patch('aiohttp.ClientSession.get')
    async def test_get_stock_quote_fallback(self, mock_get):
        """测试备用股票报价获取"""
        # 模拟Yahoo Finance API响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "chart": {
                "result": [{
                    "meta": {
                        "regularMarketPrice": 150.25,
                        "previousClose": 148.10,
                        "longName": "Apple Inc.",
                        "regularMarketVolume": 50000000,
                        "marketCap": 2500000000000
                    }
                }]
            }
        }
        mock_get.return_value.__aenter__.return_value = mock_response
        
        result = await self.data_manager._get_stock_quote_fallback("AAPL")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["symbol"], "AAPL")
        self.assertEqual(result["price"], 150.25)
        self.assertEqual(result["source"], "yahoo_finance")
    
    def test_format_stock_quote(self):
        """测试股票报价格式化"""
        import pandas as pd
        
        # 创建模拟数据
        data = pd.DataFrame({
            "name": ["Apple Inc."],
            "price": [150.25],
            "change": [2.15],
            "change_percent": [1.45],
            "volume": [50000000],
            "market_cap": [2500000000000]
        })
        
        result = self.data_manager._format_stock_quote(data, "AAPL")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["symbol"], "AAPL")
        self.assertEqual(result["price"], 150.25)
        self.assertEqual(result["change"], 2.15)
        self.assertEqual(result["source"], "openbb")
    
    def test_get_supported_symbols(self):
        """测试获取支持的交易品种"""
        # 添加测试配置
        self.data_manager.supported_markets = {
            "stocks": {"symbols": ["AAPL", "GOOGL", "MSFT"]},
            "crypto": {"symbols": ["BTC", "ETH", "ADA"]}
        }
        
        stock_symbols = self.data_manager.get_supported_symbols("stocks")
        crypto_symbols = self.data_manager.get_supported_symbols("crypto")
        
        self.assertEqual(stock_symbols, ["AAPL", "GOOGL", "MSFT"])
        self.assertEqual(crypto_symbols, ["BTC", "ETH", "ADA"])
    
    def test_get_health_status(self):
        """测试健康状态获取"""
        status = self.data_manager.get_health_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn("openbb_available", status)
        self.assertIn("supported_markets", status)
        self.assertIn("data_providers", status)


class TestFinancialAnalysisEngine(unittest.TestCase):
    """金融分析引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            "technical_analysis": {
                "indicators": {
                    "sma": {"periods": [5, 10, 20]},
                    "rsi": {"period": 14}
                }
            }
        }
        self.engine = FinancialAnalysisEngine(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.engine)
        self.assertEqual(self.engine.config, self.config)
    
    def test_calculate_rsi(self):
        """测试RSI计算"""
        import numpy as np
        
        # 创建测试价格数据
        prices = np.array([100, 102, 101, 103, 105, 104, 106, 108, 107, 109, 111, 110, 112, 114, 113])
        
        rsi = self.engine._calculate_rsi(prices, 14)
        
        self.assertIsInstance(rsi, float)
        self.assertGreaterEqual(rsi, 0)
        self.assertLessEqual(rsi, 100)
    
    def test_calculate_ema(self):
        """测试EMA计算"""
        import numpy as np
        
        prices = np.array([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
        
        ema = self.engine._calculate_ema(prices, 5)
        
        self.assertIsInstance(ema, float)
        self.assertGreater(ema, 0)
    
    async def test_technical_analysis(self):
        """测试技术分析"""
        # 创建模拟股票数据
        stock_data = {
            "quote": {
                "price": 150.25,
                "change": 2.15,
                "change_percent": 1.45
            },
            "historical": [
                {"date": "2023-01-01", "open": 148, "high": 152, "low": 147, "close": 150, "volume": 1000000},
                {"date": "2023-01-02", "open": 150, "high": 154, "low": 149, "close": 152, "volume": 1200000},
                {"date": "2023-01-03", "open": 152, "high": 155, "low": 151, "close": 154, "volume": 1100000}
            ]
        }
        
        result = await self.engine._technical_analysis("AAPL", stock_data)
        
        self.assertIsInstance(result, dict)
        self.assertIn("indicators", result)
        self.assertIn("signals", result)
        self.assertIn("recommendations", result)
        self.assertIn("trend", result)
    
    def test_assess_risk(self):
        """测试风险评估"""
        analysis_data = {
            "technical": {
                "indicators": {"rsi": 85},  # 超买
                "trend": "bearish"
            },
            "fundamental": {
                "valuation": {"market_cap": 500000000}  # 小盘股
            },
            "sentiment": {
                "sentiment_score": 0.2  # 悲观情绪
            }
        }
        
        risk_assessment = self.engine._assess_risk(analysis_data)
        
        self.assertIsInstance(risk_assessment, dict)
        self.assertIn("level", risk_assessment)
        self.assertIn("score", risk_assessment)
        self.assertIn("factors", risk_assessment)
        self.assertIn("confidence", risk_assessment)
        
        # 由于多个风险因素，风险等级应该较高
        self.assertIn(risk_assessment["level"], ["medium-high", "high"])
    
    def test_generate_recommendations(self):
        """测试建议生成"""
        analysis_data = {
            "technical": {
                "recommendations": ["技术面看涨"]
            },
            "fundamental": {
                "recommendations": ["基本面良好"]
            }
        }
        
        risk_assessment = {
            "level": "medium",
            "factors": ["市场波动"]
        }
        
        recommendations = self.engine._generate_recommendations(analysis_data, risk_assessment)
        
        self.assertIsInstance(recommendations, list)
        self.assertGreater(len(recommendations), 0)
        
        # 应该包含风险等级建议
        risk_recommendation = next((r for r in recommendations if "中等风险" in r), None)
        self.assertIsNotNone(risk_recommendation)
        
        # 应该包含免责声明
        disclaimer = next((r for r in recommendations if "免责声明" in r), None)
        self.assertIsNotNone(disclaimer)
    
    def test_get_analysis_capabilities(self):
        """测试获取分析能力"""
        capabilities = self.engine.get_analysis_capabilities()
        
        self.assertIsInstance(capabilities, dict)
        self.assertIn("technical_analysis", capabilities)
        self.assertIn("fundamental_analysis", capabilities)
        self.assertIn("sentiment_analysis", capabilities)
        self.assertIn("portfolio_analysis", capabilities)


class TestDataFormatter(unittest.TestCase):
    """数据格式化器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.formatter = DataFormatter()
    
    def test_format_price(self):
        """测试价格格式化"""
        self.assertEqual(self.formatter.format_price(150.25), "$150.25")
        self.assertEqual(self.formatter.format_price(1500.5), "$1,500.50")
        self.assertEqual(self.formatter.format_price(0), "$0.00")
    
    def test_format_percentage(self):
        """测试百分比格式化"""
        self.assertEqual(self.formatter.format_percentage(1.45), "+1.45%")
        self.assertEqual(self.formatter.format_percentage(-2.35), "-2.35%")
        self.assertEqual(self.formatter.format_percentage(0), "0.00%")
    
    def test_format_volume(self):
        """测试交易量格式化"""
        self.assertEqual(self.formatter.format_volume(1500000), "1.5M")
        self.assertEqual(self.formatter.format_volume(2500000000), "2.5B")
        self.assertEqual(self.formatter.format_volume(500), "500")
    
    def test_format_market_cap(self):
        """测试市值格式化"""
        self.assertEqual(self.formatter.format_market_cap(2500000000000), "$2.50T")
        self.assertEqual(self.formatter.format_market_cap(150000000000), "$150.00B")
        self.assertEqual(self.formatter.format_market_cap(500000000), "$500.00M")
    
    def test_format_stock_summary(self):
        """测试股票摘要格式化"""
        stock_data = {
            "symbol": "AAPL",
            "name": "Apple Inc.",
            "price": 150.25,
            "change": 2.15,
            "change_percent": 1.45,
            "volume": 50000000,
            "market_cap": 2500000000000
        }
        
        summary = self.formatter.format_stock_summary(stock_data)
        
        self.assertIn("AAPL", summary)
        self.assertIn("Apple Inc.", summary)
        self.assertIn("$150.25", summary)
        self.assertIn("+1.45%", summary)
        self.assertIn("50.0M", summary)  # 交易量
        self.assertIn("$2.50T", summary)  # 市值


class TestCacheManager(unittest.TestCase):
    """缓存管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.cache_manager = CacheManager()
    
    def test_set_and_get(self):
        """测试缓存设置和获取"""
        key = "test_key"
        data = {"test": "data"}
        
        # 设置缓存
        self.cache_manager.set(key, data, ttl=60)
        
        # 获取缓存
        cached_data = self.cache_manager.get(key)
        
        self.assertEqual(cached_data, data)
    
    def test_cache_expiration(self):
        """测试缓存过期"""
        import time
        
        key = "test_expiry"
        data = {"test": "expiry"}
        
        # 设置短期缓存
        self.cache_manager.set(key, data, ttl=1)
        
        # 立即获取应该成功
        self.assertEqual(self.cache_manager.get(key), data)
        
        # 等待过期
        time.sleep(2)
        
        # 过期后应该返回None
        self.assertIsNone(self.cache_manager.get(key))
    
    def test_delete_cache(self):
        """测试缓存删除"""
        key = "test_delete"
        data = {"test": "delete"}
        
        # 设置缓存
        self.cache_manager.set(key, data)
        
        # 确认缓存存在
        self.assertEqual(self.cache_manager.get(key), data)
        
        # 删除缓存
        self.cache_manager.delete(key)
        
        # 确认缓存已删除
        self.assertIsNone(self.cache_manager.get(key))
    
    def test_clear_by_prefix(self):
        """测试按前缀清除缓存"""
        # 设置多个缓存
        self.cache_manager.set("stock", {"symbol": "AAPL"}, symbol="AAPL")
        self.cache_manager.set("stock", {"symbol": "GOOGL"}, symbol="GOOGL")
        self.cache_manager.set("crypto", {"symbol": "BTC"}, symbol="BTC")
        
        # 按前缀清除
        self.cache_manager.clear_prefix("stock")
        
        # 股票缓存应该被清除
        self.assertIsNone(self.cache_manager.get("stock", symbol="AAPL"))
        self.assertIsNone(self.cache_manager.get("stock", symbol="GOOGL"))
        
        # 加密货币缓存应该保留
        self.assertIsNotNone(self.cache_manager.get("crypto", symbol="BTC"))
    
    def test_get_stats(self):
        """测试获取缓存统计"""
        # 设置一些缓存
        self.cache_manager.set("key1", "data1")
        self.cache_manager.set("key2", "data2")
        
        # 获取统计信息
        stats = self.cache_manager.get_stats()
        
        self.assertIsInstance(stats, dict)
        self.assertIn("hits", stats)
        self.assertIn("misses", stats)
        self.assertIn("sets", stats)
        self.assertIn("deletes", stats)
        self.assertIn("memory_evictions", stats)
        self.assertGreaterEqual(stats["sets"], 2)


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)