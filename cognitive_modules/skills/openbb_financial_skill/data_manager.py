"""OpenBB数据管理器

封装OpenBB Platform API调用，管理数据源配置和API密钥，
实现数据缓存和错误处理机制。
"""

import os
import json
import time
import asyncio
import aiohttp
import requests
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import logging
import pandas as pd

# 导入缓存管理器
try:
    from .utils.cache_manager import CacheManager
except ImportError:
    from utils.cache_manager import CacheManager

# 尝试导入OpenBB
try:
    import openbb
    OPENBB_AVAILABLE = True
except ImportError:
    OPENBB_AVAILABLE = False
    openbb = None

# 导入日志
try:
    from utilities.unified_logger import get_unified_logger
except ImportError:
    import logging
    def get_unified_logger(name):
        return logging.getLogger(name)


class OpenBBDataManager:
    """OpenBB数据管理器"""
    
    def __init__(self, config: Dict[str, Any] = None, 
                 api_keys: Dict[str, Any] = None,
                 cache_manager: Optional[CacheManager] = None):
        """
        初始化数据管理器
        
        Args:
            config: 数据源配置
            api_keys: API密钥配置
            cache_manager: 缓存管理器
        """
        self.config = config or {}
        self.api_keys = api_keys or {}
        self.cache_manager = cache_manager
        
        # 初始化日志
        self.logger = get_unified_logger("openbb_data_manager")
        
        # 数据提供商配置
        self.data_providers = self.config.get("data_providers", {})
        self.supported_markets = self.config.get("supported_markets", {})
        
        # API客户端
        self._openbb_client = None
        self._http_session = None
        
        # 错误处理配置
        self.error_config = self.config.get("error_handling", {})
        self.retry_config = self.error_config.get("retry_strategy", {})
        
        # 速率限制
        self._rate_limiters = {}
        self._last_request_times = {}
        
        # 初始化OpenBB客户端
        self._initialize_openbb()
        
        self.logger.info("OpenBB数据管理器初始化完成")
    
    def _initialize_openbb(self):
        """初始化OpenBB客户端"""
        try:
            if OPENBB_AVAILABLE:
                # 配置OpenBB
                openbb_config = self.api_keys.get("openbb", {})
                
                # 设置API密钥
                if "api_key" in openbb_config:
                    os.environ["OPENBB_API_KEY"] = openbb_config["api_key"]
                
                # 初始化客户端
                self._openbb_client = openbb
                self.logger.info("OpenBB客户端初始化成功")
            else:
                self.logger.warning("OpenBB未安装，将使用备用数据源")
                
        except Exception as e:
            self.logger.error(f"OpenBB客户端初始化失败: {e}")
    
    async def _get_http_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self._http_session is None or self._http_session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self._http_session = aiohttp.ClientSession(timeout=timeout)
        return self._http_session
    
    def _check_rate_limit(self, provider: str) -> bool:
        """检查速率限制
        
        Args:
            provider: 数据提供商名称
            
        Returns:
            是否可以发送请求
        """
        try:
            provider_config = self.data_providers.get(provider, {})
            rate_limit = provider_config.get("rate_limit", 60)  # 默认每分钟60次
            
            current_time = time.time()
            
            # 初始化速率限制器
            if provider not in self._rate_limiters:
                self._rate_limiters[provider] = []
            
            # 清理过期的请求记录
            self._rate_limiters[provider] = [
                req_time for req_time in self._rate_limiters[provider]
                if current_time - req_time < 60  # 保留最近1分钟的记录
            ]
            
            # 检查是否超过速率限制
            if len(self._rate_limiters[provider]) >= rate_limit:
                return False
            
            # 记录当前请求时间
            self._rate_limiters[provider].append(current_time)
            return True
            
        except Exception as e:
            self.logger.error(f"检查速率限制失败: {e}")
            return True  # 出错时允许请求
    
    async def _make_request_with_retry(self, request_func, *args, **kwargs) -> Any:
        """带重试机制的请求
        
        Args:
            request_func: 请求函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            请求结果
        """
        max_retries = self.retry_config.get("max_retries", 3)
        backoff_factor = self.retry_config.get("backoff_factor", 2)
        
        for attempt in range(max_retries + 1):
            try:
                result = await request_func(*args, **kwargs)
                return result
                
            except Exception as e:
                if attempt == max_retries:
                    raise e
                
                # 计算等待时间
                wait_time = backoff_factor ** attempt
                self.logger.warning(f"请求失败，{wait_time}秒后重试 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                await asyncio.sleep(wait_time)
    
    async def get_stock_data(self, symbol: str, data_type: str = "quote", 
                           period: str = "1d", **kwargs) -> Dict[str, Any]:
        """获取股票数据
        
        Args:
            symbol: 股票代码
            data_type: 数据类型 (quote, historical, fundamentals)
            period: 时间周期
            **kwargs: 其他参数
            
        Returns:
            股票数据
        """
        try:
            # 检查缓存
            cache_key = f"stock_{data_type}"
            cached_data = None
            if self.cache_manager:
                cached_data = self.cache_manager.get(
                    cache_key, symbol=symbol, period=period, **kwargs
                )
            
            if cached_data:
                self.logger.debug(f"从缓存获取股票数据: {symbol}")
                return cached_data
            
            # 获取新数据
            data = await self._fetch_stock_data(symbol, data_type, period, **kwargs)
            
            # 缓存数据
            if self.cache_manager and data.get("success"):
                cache_ttl = self._get_cache_ttl(data_type)
                self.cache_manager.set(
                    cache_key, data, ttl=cache_ttl,
                    symbol=symbol, period=period, **kwargs
                )
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取股票数据失败: {symbol}, {e}")
            return {
                "success": False,
                "error": str(e),
                "symbol": symbol,
                "data_type": data_type
            }
    
    async def _fetch_stock_data(self, symbol: str, data_type: str, 
                              period: str, **kwargs) -> Dict[str, Any]:
        """获取股票数据的具体实现"""
        try:
            if data_type == "quote":
                return await self._get_stock_quote(symbol)
            elif data_type == "historical":
                return await self._get_stock_historical(symbol, period, **kwargs)
            elif data_type == "fundamentals":
                return await self._get_stock_fundamentals(symbol)
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
                
        except Exception as e:
            raise Exception(f"获取股票数据失败: {e}")
    
    async def _get_stock_quote(self, symbol: str) -> Dict[str, Any]:
        """获取股票实时报价"""
        try:
            if OPENBB_AVAILABLE and self._openbb_client:
                # 使用OpenBB获取数据
                try:
                    data = self._openbb_client.equity.price.quote(symbol)
                    return self._format_stock_quote(data, symbol)
                except Exception as e:
                    self.logger.warning(f"OpenBB获取股票报价失败: {e}")
            
            # 使用备用数据源
            return await self._get_stock_quote_fallback(symbol)
            
        except Exception as e:
            raise Exception(f"获取股票报价失败: {e}")
    
    async def _get_stock_quote_fallback(self, symbol: str) -> Dict[str, Any]:
        """备用股票报价数据源"""
        try:
            # 使用Yahoo Finance API作为备用
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}"
            
            session = await self._get_http_session()
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_yahoo_quote(data, symbol)
                else:
                    raise Exception(f"HTTP错误: {response.status}")
                    
        except Exception as e:
            raise Exception(f"备用数据源获取失败: {e}")
    
    def _format_stock_quote(self, data: Any, symbol: str) -> Dict[str, Any]:
        """格式化股票报价数据"""
        try:
            if isinstance(data, pd.DataFrame) and not data.empty:
                row = data.iloc[0]
                return {
                    "success": True,
                    "symbol": symbol,
                    "name": row.get("name", symbol),
                    "price": float(row.get("price", 0)),
                    "change": float(row.get("change", 0)),
                    "change_percent": float(row.get("change_percent", 0)),
                    "volume": int(row.get("volume", 0)),
                    "market_cap": float(row.get("market_cap", 0)),
                    "timestamp": datetime.now().isoformat(),
                    "source": "openbb"
                }
            else:
                raise ValueError("无效的数据格式")
                
        except Exception as e:
            self.logger.error(f"格式化股票报价数据失败: {e}")
            return {
                "success": False,
                "error": f"数据格式化失败: {str(e)}",
                "symbol": symbol
            }
    
    def _format_yahoo_quote(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """格式化Yahoo Finance数据"""
        try:
            result = data.get("chart", {}).get("result", [])
            if not result:
                raise ValueError("无效的Yahoo Finance数据")
            
            chart_data = result[0]
            meta = chart_data.get("meta", {})
            
            current_price = meta.get("regularMarketPrice", 0)
            previous_close = meta.get("previousClose", 0)
            change = current_price - previous_close
            change_percent = (change / previous_close * 100) if previous_close > 0 else 0
            
            return {
                "success": True,
                "symbol": symbol,
                "name": meta.get("longName", symbol),
                "price": current_price,
                "change": change,
                "change_percent": change_percent,
                "volume": meta.get("regularMarketVolume", 0),
                "market_cap": meta.get("marketCap", 0),
                "timestamp": datetime.now().isoformat(),
                "source": "yahoo_finance"
            }
            
        except Exception as e:
            self.logger.error(f"格式化Yahoo Finance数据失败: {e}")
            return {
                "success": False,
                "error": f"Yahoo Finance数据格式化失败: {str(e)}",
                "symbol": symbol
            }
    
    async def _get_stock_historical(self, symbol: str, period: str, **kwargs) -> Dict[str, Any]:
        """获取股票历史数据"""
        try:
            if OPENBB_AVAILABLE and self._openbb_client:
                # 使用OpenBB获取历史数据
                try:
                    data = self._openbb_client.equity.price.historical(
                        symbol, period=period, **kwargs
                    )
                    return self._format_historical_data(data, symbol, period)
                except Exception as e:
                    self.logger.warning(f"OpenBB获取历史数据失败: {e}")
            
            # 使用备用数据源
            return await self._get_historical_fallback(symbol, period, **kwargs)
            
        except Exception as e:
            raise Exception(f"获取历史数据失败: {e}")
    
    async def _get_historical_fallback(self, symbol: str, period: str, **kwargs) -> Dict[str, Any]:
        """备用历史数据源"""
        # 这里可以实现备用的历史数据获取逻辑
        return {
            "success": False,
            "error": "历史数据备用源暂未实现",
            "symbol": symbol,
            "period": period
        }
    
    def _format_historical_data(self, data: Any, symbol: str, period: str) -> Dict[str, Any]:
        """格式化历史数据"""
        try:
            if isinstance(data, pd.DataFrame) and not data.empty:
                # 转换为字典格式
                historical_data = []
                for index, row in data.iterrows():
                    historical_data.append({
                        "date": index.isoformat() if hasattr(index, 'isoformat') else str(index),
                        "open": float(row.get("open", 0)),
                        "high": float(row.get("high", 0)),
                        "low": float(row.get("low", 0)),
                        "close": float(row.get("close", 0)),
                        "volume": int(row.get("volume", 0))
                    })
                
                return {
                    "success": True,
                    "symbol": symbol,
                    "period": period,
                    "data": historical_data,
                    "count": len(historical_data),
                    "timestamp": datetime.now().isoformat(),
                    "source": "openbb"
                }
            else:
                raise ValueError("无效的历史数据格式")
                
        except Exception as e:
            self.logger.error(f"格式化历史数据失败: {e}")
            return {
                "success": False,
                "error": f"历史数据格式化失败: {str(e)}",
                "symbol": symbol,
                "period": period
            }
    
    async def _get_stock_fundamentals(self, symbol: str) -> Dict[str, Any]:
        """获取股票基本面数据"""
        try:
            if OPENBB_AVAILABLE and self._openbb_client:
                # 使用OpenBB获取基本面数据
                try:
                    # 这里需要根据OpenBB的实际API调整
                    data = self._openbb_client.equity.fundamental.overview(symbol)
                    return self._format_fundamentals_data(data, symbol)
                except Exception as e:
                    self.logger.warning(f"OpenBB获取基本面数据失败: {e}")
            
            # 返回占位符数据
            return {
                "success": False,
                "error": "基本面数据暂未实现",
                "symbol": symbol
            }
            
        except Exception as e:
            raise Exception(f"获取基本面数据失败: {e}")
    
    def _format_fundamentals_data(self, data: Any, symbol: str) -> Dict[str, Any]:
        """格式化基本面数据"""
        # 这里需要根据实际的OpenBB基本面数据格式进行实现
        return {
            "success": True,
            "symbol": symbol,
            "fundamentals": {},
            "timestamp": datetime.now().isoformat(),
            "source": "openbb"
        }
    
    async def get_crypto_data(self, symbol: str, data_type: str = "quote", **kwargs) -> Dict[str, Any]:
        """获取加密货币数据"""
        try:
            # 检查缓存
            cache_key = f"crypto_{data_type}"
            cached_data = None
            if self.cache_manager:
                cached_data = self.cache_manager.get(
                    cache_key, symbol=symbol, **kwargs
                )
            
            if cached_data:
                self.logger.debug(f"从缓存获取加密货币数据: {symbol}")
                return cached_data
            
            # 获取新数据
            data = await self._fetch_crypto_data(symbol, data_type, **kwargs)
            
            # 缓存数据
            if self.cache_manager and data.get("success"):
                cache_ttl = self._get_cache_ttl(data_type)
                self.cache_manager.set(
                    cache_key, data, ttl=cache_ttl,
                    symbol=symbol, **kwargs
                )
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取加密货币数据失败: {symbol}, {e}")
            return {
                "success": False,
                "error": str(e),
                "symbol": symbol,
                "data_type": data_type
            }
    
    async def _fetch_crypto_data(self, symbol: str, data_type: str, **kwargs) -> Dict[str, Any]:
        """获取加密货币数据的具体实现"""
        # 这里将在第三阶段实现具体的加密货币数据获取逻辑
        return {
            "success": False,
            "error": "加密货币数据获取暂未实现",
            "symbol": symbol,
            "data_type": data_type
        }
    
    async def get_forex_data(self, pair: str, data_type: str = "quote", **kwargs) -> Dict[str, Any]:
        """获取外汇数据"""
        # 这里将在第三阶段实现具体的外汇数据获取逻辑
        return {
            "success": False,
            "error": "外汇数据获取暂未实现",
            "pair": pair,
            "data_type": data_type
        }
    
    async def get_economic_data(self, indicator: str, region: str = "US", **kwargs) -> Dict[str, Any]:
        """获取宏观经济数据"""
        # 这里将在第三阶段实现具体的宏观经济数据获取逻辑
        return {
            "success": False,
            "error": "宏观经济数据获取暂未实现",
            "indicator": indicator,
            "region": region
        }
    
    def _get_cache_ttl(self, data_type: str) -> int:
        """获取缓存TTL
        
        Args:
            data_type: 数据类型
            
        Returns:
            缓存TTL（秒）
        """
        cache_config = self.config.get("caching_strategy", {})
        
        if data_type in ["quote", "real_time"]:
            return cache_config.get("real_time_data", {}).get("ttl_seconds", 60)
        elif data_type in ["historical", "fundamentals"]:
            return cache_config.get("fundamental_data", {}).get("ttl_seconds", 3600)
        else:
            return 300  # 默认5分钟
    
    async def close(self):
        """关闭数据管理器"""
        try:
            if self._http_session and not self._http_session.closed:
                await self._http_session.close()
            
            self.logger.info("OpenBB数据管理器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭数据管理器失败: {e}")
    
    def get_supported_symbols(self, market: str) -> List[str]:
        """获取支持的交易品种列表
        
        Args:
            market: 市场类型
            
        Returns:
            支持的交易品种列表
        """
        market_config = self.supported_markets.get(market, {})
        return market_config.get("symbols", [])
    
    def get_data_freshness(self, symbol: str, data_type: str) -> Optional[datetime]:
        """获取数据新鲜度
        
        Args:
            symbol: 交易品种
            data_type: 数据类型
            
        Returns:
            数据最后更新时间
        """
        if self.cache_manager:
            cache_key = f"{data_type}"
            cache_info = self.cache_manager.get_cache_info(
                cache_key, symbol=symbol
            )
            if cache_info:
                return cache_info.get("created_at")
        
        return None
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取数据管理器健康状态"""
        try:
            status = {
                "openbb_available": OPENBB_AVAILABLE,
                "openbb_client_initialized": self._openbb_client is not None,
                "http_session_active": self._http_session is not None and not self._http_session.closed,
                "cache_manager_available": self.cache_manager is not None,
                "supported_markets": list(self.supported_markets.keys()),
                "data_providers": list(self.data_providers.keys())
            }
            
            # 添加缓存统计
            if self.cache_manager:
                status["cache_stats"] = self.cache_manager.get_stats()
            
            # 添加速率限制状态
            status["rate_limiters"] = {
                provider: len(requests) 
                for provider, requests in self._rate_limiters.items()
            }
            
            return status
            
        except Exception as e:
            return {
                "error": f"获取健康状态失败: {str(e)}"
            }