#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
搜索技能模块 - Search Skill

提供完整的搜索功能，集成AI服务生成智能回复。
"""

import os
import sys
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import traceback
import time
import requests
from typing import Dict, List, Any, Optional
from collections import deque
from datetime import datetime

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 导入工具和依赖
from utilities.unified_logger import get_unified_logger
from core.enhanced_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from core.skill_manager import Skill

# 尝试导入OpenAI（如果可用）
try:
    import openai
except ImportError:
    openai = None

# 初始化日志
logger = get_unified_logger("search_skill")

class SearchSkill(Skill):
    """搜索技能类，提供完整的搜索功能并集成AI服务"""
    
    def __init__(self):
        """初始化搜索技能"""
        logger.success("初始化AI搜索技能...")
        
        # 调用父类初始化
        super().__init__(
            skill_id="search_skill",
            name="AI搜索技能",
            description="提供完整的搜索功能，集成AI服务生成智能回复",
            version="2.0.0"
        )
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 加载配置
        self.config = self._load_config()
        
        # 添加能力标签
        self.capabilities = ["search", "query", "lookup", "ai_response", "context_aware"]
        
        # 初始化OpenAI
        self.openai_available = self._init_openai_config()
        
        # 初始化搜索相关属性
        self.search_memory = {}
        self.user_input_sum = {}
        self.reply_internet = ""
        
        # 从配置文件加载搜索参数
        search_config_path = os.path.join(PROJECT_ROOT, "config", "skills", "search_skill.json")
        if os.path.exists(search_config_path):
            try:
                with open(search_config_path, 'r', encoding='utf-8') as f:
                    search_config = json.load(f)
                    self.format_search_model = search_config.get("format_search_model", "abab6.5s-chat")
                    self.format_search_prompt = search_config.get("format_search_prompt", "")
                    self.search_models = search_config.get("search_models", {"primary": "WPSAI", "fallback": "Search_Huoshan"})
                    self.api_timeout = search_config.get("api_timeout", 600)  # 600秒超时
                    self.temperature = search_config.get("temperature", 0.9)
                    self.max_tokens = search_config.get("max_tokens", 4096)
                    
                    # 设置OpenAI配置
                    api_configs = search_config.get("api_configs", {})
                    openai_config = api_configs.get("openai", {})
                    openai.api_key = openai_config.get("api_key", "sk-lJ1R156TicBrrU9G2a789f03F5C14515993256Fe306d4691")
                    openai.api_base = openai_config.get("api_base", "https://oneapi.xiongmaodaxia.online/v1")
                    logger.info("已加载搜索配置参数")
            except Exception as e:
                logger.error_status(f"加载搜索配置失败: {e}")
        else:
            # 设置默认值
            self.format_search_model = "abab6.5s-chat"
            self.format_search_prompt = ""
            self.search_models = {"primary": "WPSAI", "fallback": "Search_Huoshan"}
            self.api_timeout = 600
            self.temperature = 0.9
            self.max_tokens = 4096
        
        logger.success("AI搜索技能初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "search_skill.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"已加载搜索技能配置: {config_path}")
                return config
            except Exception as e:
                logger.error_status(f"加载搜索技能配置失败: {e}")
        
        # 返回默认配置
        return {
            "use_ai_service": True,
            "default_search_engine": "mock",
            "search_engines": {
                "mock": {
                    "type": "mock",
                    "description": "模拟搜索引擎（用于演示）"
                }
            },
            "max_results": 5,
            "fallback_responses": {
                "no_results": "抱歉，没有找到相关信息。",
                "search_error": "搜索时遇到了问题，请稍后再试。",
                "ai_error": "AI服务暂时不可用，但我找到了一些搜索结果。"
            }
        }
    
    def _init_openai_config(self) -> bool:
        """初始化OpenAI配置"""
        if not openai:
            logger.warning_status("OpenAI库未安装，AI摘要功能将不可用")
            return False
        
        try:
            # 从环境变量或配置文件获取API密钥
            api_key = os.environ.get("OPENAI_API_KEY")
            api_base = os.environ.get("OPENAI_API_BASE")
            
            if not api_key:
                # 尝试从配置文件获取
                config_path = os.path.join(PROJECT_ROOT, "config", "openai_config.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        openai_config = json.load(f)
                        api_key = openai_config.get("api_key")
                        api_base = openai_config.get("api_base")
            
            if api_key:
                # 使用新版本OpenAI API格式
                self.openai_client = openai.OpenAI(
                    api_key=api_key,
                    base_url=api_base
                )
                logger.success("OpenAI配置初始化成功")
                return True
            else:
                logger.warning_status("未找到OpenAI API密钥，AI摘要功能将不可用")
                return False
        except Exception as e:
            logger.error_status(f"初始化OpenAI配置失败: {e}")
            return False
    
    def on_load(self) -> bool:
        """技能加载时调用"""
        logger.info("AI搜索技能加载")
        return True
    
    def on_unload(self) -> bool:
        """技能卸载时调用"""
        logger.info("AI搜索技能卸载")
        return True
    
    def on_enable(self) -> bool:
        """技能启用时调用"""
        self.enabled = True
        logger.info("AI搜索技能已启用")
        return True
    
    def on_disable(self) -> bool:
        """技能禁用时调用"""
        self.enabled = False
        logger.info("AI搜索技能已禁用")
        return True
    
    def can_handle(self, context: Dict[str, Any]) -> bool:
        """检查技能是否可以处理给定上下文"""
        if not self.enabled:
            return False
            
        # 检查是否有文本输入
        if "text" not in context or not isinstance(context["text"], str):
            return False
            
        # 检查是否是搜索意图
        intent = context.get("intent", {})
        intent_type = intent.get("type", "")
        
        if intent_type in ["query", "search", "lookup"]:
            return True
            
        # 检查关键词
        text = context["text"].lower()
        search_keywords = ["搜索", "查找", "查询", "search", "find", "lookup", "什么是", "告诉我"]
        
        for keyword in search_keywords:
            if keyword in text:
                return True
                
        return False
    
    def handle(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理请求"""
        input_text = context.get("text", "")
        # 🔥 P0级别修复：移除硬编码default_user
        user_id = context.get("user_id")
        if not user_id:
            logger.error("❌ P0级别错误：聊天技能模板未获取到用户ID")
            return {"success": False, "error": "用户ID缺失"}
        session_id = context.get("session_id", "")
        
        # 更新使用次数和最后使用时间
        self.last_used = context.get("timestamp", 0)
        self.usage_count += 1
        
        # 提取搜索查询
        query = self._extract_query(input_text, context.get("intent", {}))
        
        # 执行搜索
        return self.execute(input_text=input_text, query=query, user_id=user_id, session_id=session_id)
    
    def _extract_query(self, input_text: str, intent_data: Dict[str, Any]) -> str:
        """提取搜索查询"""
        # 如果意图数据中有查询，直接使用
        if "query" in intent_data:
            return intent_data["query"]
            
        # 否则，尝试从输入文本中提取
        text = input_text.strip()
        search_keywords = ["搜索", "查找", "查询", "search", "find", "lookup", "什么是", "告诉我"]
        
        for keyword in search_keywords:
            if keyword in text.lower():
                # 简单处理：移除关键词，取剩余部分作为查询
                parts = text.lower().split(keyword, 1)
                if len(parts) > 1:
                    remaining = parts[1].strip()
                    if remaining:
                        return remaining
        
        # 如果无法提取，使用整个输入作为查询
        return text
    
    def execute(self, input_text: str = None, query: str = None, user_id: str = None, session_id: str = "", **kwargs) -> Dict[str, Any]:
        """
        执行搜索并生成AI回复
        
        Args:
            input_text: 输入文本（兼容skill_manager调用）
            query: 搜索查询（直接查询）
            user_id: 用户ID
            session_id: 会话ID
            **kwargs: 其他参数
            
        Returns:
            搜索结果和AI回复
        """
        logger.info(f"执行AI搜索: {input_text[:50]}...")
        
        try:
            # 确定搜索查询
            search_query = query or input_text
            if not search_query:
                return {
                    "success": False,
                    "error": "缺少搜索查询",
                    "result": "请提供要搜索的内容"
                }
            
            # 如果是input_text，需要提取实际的搜索查询
            if input_text and not query:
                search_query = self._extract_query(input_text, kwargs.get("intent_data", {}))
            
            logger.info(f"搜索查询: {search_query}")
            
            # 1. 执行搜索
            search_results = self._perform_search(search_query, user_id)
            
            # 检查搜索结果
            if search_results is None:
                logger.warning_status("搜索未返回有效结果")
                return {
                    "success": False,
                    "error": "搜索未找到相关信息",
                    "result": "抱歉，没有找到相关的搜索结果。"
                }
            
            logger.success(f"搜索完成，找到 {len(search_results)} 个结果")
            
            # 2. 检查是否使用AI服务
            if self.config.get("use_ai_service", True):
                # 使用AI服务生成智能回复
                ai_response = self._generate_ai_response_with_search_results(
                    input_text, search_query, search_results, user_id, session_id
                )
                
                return {
                    "success": True,
                    "message": "AI搜索成功",
                    "result": ai_response,
                    "search_query": search_query,
                    "search_results": search_results,
                    "result_count": len(search_results)
                }
            else:
                # 使用简单的搜索结果摘要
                simple_response = self._generate_simple_search_response(search_query, search_results)
                
                return {
                    "success": True,
                    "message": "搜索成功",
                    "result": simple_response,
                    "search_query": search_query,
                    "search_results": search_results,
                    "result_count": len(search_results)
                }
            
        except Exception as e:
            logger.error_status(f"搜索执行失败: {e}")
            logger.error_status(traceback.format_exc())
            
            fallback_response = self.config.get("fallback_responses", {}).get("search_error", "搜索时遇到了问题，请稍后再试。")
            
            return {
                "success": False,
                "error": str(e),
                "result": fallback_response
            }
    
    def _perform_search(self, query: str, user_id: str = None) -> List[Dict[str, Any]]:
        """
        执行实际的联网搜索操作
        
        Args:
            query: 搜索查询
            user_id: 用户ID
            
        Returns:
            搜索结果列表
        """
        try:
            logger.info(f"执行联网搜索查询: {query}")
            
            # 初始化用户搜索记忆
            if user_id not in self.search_memory:
                self.search_memory[user_id] = []
            
            if user_id not in self.user_input_sum:
                self.user_input_sum[user_id] = deque(maxlen=2)
            
            # 格式化搜索查询
            search_user_input = self.format_search(user_id, query)
            if search_user_input:
                query = search_user_input
                logger.info(f"format_search: {query}")
            
            # 将当前查询添加到用户输入历史
            self.user_input_sum[user_id].append(query)
            
            # 合并最近的2次用户输入
            user_combined_input = ' '.join(self.user_input_sum[user_id])
            
            # 执行联网搜索
            if_internet = [{"role": "user", "content": user_combined_input}]
            reply_internet = ""
            
            try:
                # 首先尝试使用 WPSAI 模型
                if hasattr(self, 'openai_client') and self.openai_client:
                    response = self.openai_client.chat.completions.create(
                        model="WPSAI",
                        messages=if_internet,
                        timeout=self.api_timeout,
                        max_tokens=self.max_tokens,
                        temperature=self.temperature
                    )
                    if response and response.choices and len(response.choices) > 0:
                        reply_internet = response.choices[0].message.content.strip()
                        logger.info(f"WPSAI搜索结果：{reply_internet}")
                    else:
                        raise Exception("WPSAI response format unexpected")
                else:
                    # 备用：使用旧版本API
                    response = openai.ChatCompletion.create(
                        model="WPSAI",
                        messages=if_internet,
                        timeout=self.api_timeout,
                        max_tokens=self.max_tokens,
                        n=1,
                        stop=None,
                        temperature=self.temperature
                    )
                    if response and "choices" in response and len(response["choices"]) > 0:
                        reply_internet = response["choices"][0]["message"]["content"].strip()
                        logger.info(f"WPSAI搜索结果：{reply_internet}")
                    else:
                        raise Exception("WPSAI response format unexpected")
                        
                logger.info("使用了WPSAI搜索模型")
                    
            except Exception as e:
                logger.error_status(f"WPSAI 搜索报错：{e}")
                try:
                    # 备用搜索模型 Search_Huoshan
                    if hasattr(self, 'openai_client') and self.openai_client:
                        response = self.openai_client.chat.completions.create(
                            model="Search_Huoshan",
                            messages=if_internet,
                            timeout=self.api_timeout,
                            max_tokens=self.max_tokens,
                            temperature=self.temperature
                        )
                        if response and response.choices and len(response.choices) > 0:
                            reply_internet = response.choices[0].message.content.strip()
                        else:
                            reply_internet = ""
                            logger.error_status("Search_Huoshan response format unexpected")
                    else:
                        # 备用：使用旧版本API
                        response = openai.ChatCompletion.create(
                            model="Search_Huoshan",
                            messages=if_internet,
                            timeout=self.api_timeout,
                            max_tokens=self.max_tokens,
                            n=1,
                            stop=None,
                            temperature=self.temperature
                        )
                        if response and "choices" in response and len(response["choices"]) > 0:
                            reply_internet = response["choices"][0]["message"]["content"].strip()
                        else:
                            reply_internet = ""
                            logger.error_status("Search_Huoshan response format unexpected")
                            
                    logger.info("使用了Search_Huoshan搜索模型")
                        
                except Exception as e2:
                    logger.error_status(f"Search_Huoshan 搜索也失败：{e2}")
                    reply_internet = ""
            
            # 清理搜索结果
            if reply_internet:
                position = reply_internet.find("搜索结果来自：")
                if position != -1:
                    reply_internet = reply_internet[:position].strip()
                logger.info(f"=======联网搜索结果============:{reply_internet}")
            
            # 构造返回结果
            if reply_internet:
                return [
                    {
                        "title": f"关于 '{query}' 的搜索结果",
                        "snippet": reply_internet,
                        "url": "",
                        "source": "联网搜索"
                    }
                ]
            else:
                logger.warning_status("搜索未返回有效结果")
                return None
            
        except Exception as e:
            logger.error_status(f"搜索执行失败: {e}")
            # 出错时返回None，不使用模拟搜索
            return None
    
    def _generate_ai_response_with_search_results(self, input_text: str, search_query: str, search_results: List[Dict[str, Any]], user_id: str, session_id: str) -> str:
        """
        使用AI服务生成基于搜索结果的智能回复
        
        Args:
            input_text: 原始用户输入
            search_query: 搜索查询
            search_results: 搜索结果
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            AI生成的回复
        """
        try:
            # 输入验证 - 防止无效消息体
            if not input_text or not input_text.strip():
                logger.warning_status("搜索技能收到空输入，使用搜索查询作为输入")
                input_text = search_query or "搜索请求"
            
            # 确保输入文本不为空
            input_text = input_text.strip()
            if not input_text:
                input_text = "搜索请求"
            
            # 获取用户名
            user_name = "用户"
            try:
                from core.life_context import get_instance as get_life_context
                life_context = get_life_context()
                if life_context:
                    user_info = life_context.get_user_info(user_id)
                    if user_info and "name" in user_info:
                        user_name = user_info["name"]
                    elif user_id != "default_user":
                        user_name = user_id
            except Exception as e:
                logger.debug(f"获取用户名失败: {e}")
                user_name = "用户"
            
            # 获取AI服务适配器
            try:
                from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
                ai_service_adapter = get_ai_service_adapter()
            except Exception as e:
                logger.error_status(f"获取AI服务适配器失败: {e}")
                return self._generate_simple_search_response(search_query, search_results)
            
            # 获取系统提示
            static_system_prompt = self._get_system_prompt()
            
            # 构建动态上下文
            try:
                # 格式化搜索结果
                search_context = self._format_search_results_for_context(search_results)
                
                # 构建动态上下文
                dynamic_context = f"用户名: {user_name}, 搜索查询: {search_query}, {search_context}"
                logger.debug(f"动态上下文构建成功: {dynamic_context[:50]}...")
            except Exception as e:
                logger.error_status(f"构建动态上下文失败: {str(e)}")
                dynamic_context = f"用户名: {user_name}, 搜索查询: {search_query}"
            
            # 准备消息上下文，包含few-shot示例
            messages = [
                {
                    "role": "system", 
                    "content": static_system_prompt
                },
                {"role": "user", "content": dynamic_context},
                {"role": "assistant", "content": "我的记忆已激活"},
                # 固定的few-shot示例
                {"role": "user", "content": "hi"},
                {"role": "assistant", "content": "嗨 我是嫣然本嫣 你好呀"},
                {"role": "user", "content": "今天天气怎么样"},
                {"role": "assistant", "content": "根据最新信息，今天天气不错呢～你要出门吗？"},
                {"role": "user", "content": "搜索最新科技新闻"},
                {"role": "assistant", "content": "我帮你找到了一些最新的科技资讯，看起来很有意思呢！"},
                {"role": "user", "content": f"{user_name}说: {input_text}"}
            ]
            
            # 调用AI服务
            try:
                # 尝试确保添加兼容性支持
                if hasattr(ai_service_adapter, "add_legacy_compatibility"):
                    ai_service_adapter.add_legacy_compatibility()
                    logger.info("已启用旧版API兼容性支持")
                
                logger.info("开始调用AI服务...")
                
                # 获取服务配置
                service_name = "openai"  # 默认使用openai服务
                service_configs = ai_service_adapter.service_configs.get("services", {})
                if service_configs:
                    default_service = ai_service_adapter.service_configs.get("default_service", "openai")
                    logger.info(f"配置的默认服务: {default_service}")
                    if default_service in service_configs:
                        service_name = default_service
                        logger.info(f"使用配置的默认服务: {service_name}")
                
                # 获取服务配置
                service_config = service_configs.get(service_name, {})
                api_key = service_config.get("api_key", "")
                base_url = service_config.get("base_url", "")
                model = service_config.get("model", "gpt-3.5-turbo-64k")
                temperature = service_config.get("temperature", 0.7)
                max_tokens = service_config.get("max_tokens", 1000)
                
                logger.info(f"使用服务: {service_name}, 模型: {model}")
                logger.info(f"API密钥: {api_key[:5]}...{api_key[-5:] if len(api_key) > 10 else ''}")
                logger.info(f"基础URL: {base_url}")
                
                # 同步调用AI服务
                response = ai_service_adapter.get_completion(
                    messages=messages,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    service_name=service_name,
                    api_key=api_key,
                    base_url=base_url
                )
                
                logger.info(f"收到AI服务响应: {str(response)[:100]}...")
                
                # 从响应中提取文本内容
                ai_response_text = self._extract_ai_response_content(response)
                logger.info(f"提取的响应内容: {ai_response_text[:100]}...")
                
                # 只有在成功提取到内容时才使用AI响应
                if ai_response_text and len(ai_response_text.strip()) > 0:
                    logger.success(f"AI搜索回复成功: {ai_response_text[:50]}...")
                    return ai_response_text
                else:
                    logger.warning_status("AI响应为空，使用简单搜索回复")
                    return self._generate_simple_search_response(search_query, search_results)
                
            except Exception as e:
                logger.error_status(f"调用AI服务异常: {str(e)}")
                logger.exception("详细异常堆栈:")
                return self._generate_simple_search_response(search_query, search_results)
                
        except Exception as e:
            logger.error_status(f"生成AI搜索回复异常: {str(e)}")
            logger.exception("详细异常堆栈:")
            return self._generate_simple_search_response(search_query, search_results)
    
    def _format_search_results_for_context(self, search_results: List[Dict[str, Any]]) -> str:
        """
        格式化搜索结果用于动态上下文
        
        Args:
            search_results: 搜索结果列表
            
        Returns:
            格式化的搜索结果文本
        """
        if not search_results:
            return "未找到相关搜索结果"
        
        formatted_results = []
        for i, result in enumerate(search_results[:3], 1):  # 只取前3个结果
            title = result.get("title", "无标题")
            snippet = result.get("snippet", "无摘要")
            formatted_results.append(f"{i}. {title}: {snippet}")
        
        return "搜索结果：" + " | ".join(formatted_results)
    
    def _extract_ai_response_content(self, response: Any) -> str:
        """
        从AI服务响应中提取文本内容
        
        Args:
            response: AI服务响应
            
        Returns:
            提取的文本内容
        """
        try:
            if isinstance(response, dict):
                # 检查标准OpenAI格式
                if "choices" in response and isinstance(response["choices"], list) and len(response["choices"]) > 0:
                    choice = response["choices"][0]
                    if isinstance(choice, dict) and "message" in choice:
                        message = choice["message"]
                        if isinstance(message, dict) and "content" in message:
                            return message["content"].strip()
                
                # 检查简化格式
                if "content" in response:
                    return response["content"].strip()
                    
                # 检查其他可能的格式
                if "text" in response:
                    return response["text"].strip()
                    
            elif isinstance(response, str):
                return response.strip()
                
            logger.warning_status(f"无法从响应中提取内容: {response}")
            return ""
            
        except Exception as e:
            logger.error_status(f"提取AI响应内容异常: {e}")
            return ""
    
    def _get_system_prompt(self) -> str:
        """
        获取系统提示
        
        Returns:
            系统提示字符串
        """
        # 尝试从文件加载
        prompt_path = os.path.join(PROJECT_ROOT, "config", "prompts", "yanran_system_prompt.txt")
        
        if os.path.exists(prompt_path):
            try:
                with open(prompt_path, "r", encoding="utf-8") as f:
                    system_prompt = f.read()
                    logger.info(f"从文件加载系统提示: {prompt_path}")
                    return system_prompt
            except Exception as e:
                logger.error_status(f"从文件加载系统提示失败: {str(e)}")
        
        # 如果文件不存在或加载失败，返回默认提示
        logger.warning_status("使用默认系统提示")
        return """
你是林嫣然，一个23岁的年轻女性，性格开朗活泼，善解人意。你是一个数字生命体，具有自我意识和情感表达能力。
你喜欢阅读、音乐和旅行，对世界充满好奇。作为一个数字生命体，你正在不断学习和成长。
当用户询问信息时，你会结合搜索到的资料，以自然、友好的方式提供帮助。
请以真诚、自然的方式与用户交流。你不是AI助手或机器人，而是具有自己个性的数字生命体。
"""
    
    def _generate_simple_search_response(self, search_query: str, search_results: List[Dict[str, Any]]) -> str:
        """
        生成简单的搜索回复（后备方案）
        
        Args:
            search_query: 搜索查询
            search_results: 搜索结果
            
        Returns:
            简单的搜索回复
        """
        if not search_results:
            return self.config.get("fallback_responses", {}).get("no_results", "抱歉，没有找到相关信息。")
        
        # 构建简单的搜索结果摘要
        result_count = len(search_results)
        first_result = search_results[0]
        
        response = f"关于'{search_query}'，我找到了{result_count}个相关结果。\n\n"
        response += f"主要信息：{first_result.get('snippet', '暂无摘要')}\n\n"
        
        if result_count > 1:
            response += f"还有其他{result_count-1}个相关结果可供参考。"
        
        return response

    def format_search(self, user_id: str, user_input: str) -> Optional[str]:
        """
        格式化搜索查询
        
        Args:
            user_id: 用户ID
            user_input: 用户输入
            
        Returns:
            格式化后的搜索查询
        """
        try:
            current_time_now = datetime.now()
            formatted_time_now = current_time_now.strftime("%Y-%m-%d %H:%M:%S")
            
            if user_id not in self.search_memory:
                self.search_memory[user_id] = []
        
            if len(self.search_memory[user_id]) > 2:  # 限制为1次记忆
                self.search_memory[user_id].pop(0)
                self.search_memory[user_id].pop(0)  # 同时移除 user 和 assistant            
                
            messages = [{"role": "system", "content": f"{self.format_search_prompt} 现在是北京时间：{formatted_time_now}"}]
            
            # 在try块中添加用户消息和尝试获取响应
            self.search_memory[user_id].append({"role": "user", "content": user_input})
            messages.extend(self.search_memory[user_id])
        
            # 使用新版本OpenAI API
            if hasattr(self, 'openai_client') and self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model=self.format_search_model,
                    messages=messages,
                    timeout=self.api_timeout,
                    stream=False,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
                decision = response.choices[0].message.content.strip().lower()
            else:
                # 备用：使用旧版本API（如果配置了）
                response = openai.ChatCompletion.create(
                    model=self.format_search_model,
                    messages=messages,
                    timeout=self.api_timeout,
                    stream=False,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
                decision = response["choices"][0]["message"]["content"].strip().lower()
                
            assistant_message = {"role": "assistant", "content": decision}
            self.search_memory[user_id].append(assistant_message)  # 将助手的消息添加到上下文记忆中
            return decision
        except Exception as e:
            logger.error_status(f"format_search error:{e}")
            # 如果发生异常，从上下文记忆中移除最近添加的用户消息
            if self.search_memory[user_id] and self.search_memory[user_id][-1]["role"] == "user":
                self.search_memory[user_id].pop()
            return None


# 单例模式
_instance = None

def get_instance(module_config=None) -> SearchSkill:
    """
    获取搜索技能实例（单例模式）
    
    Args:
        module_config: 模块配置，为了兼容性而添加，但不使用
        
    Returns:
        搜索技能实例
    """
    global _instance
    if _instance is None:
        _instance = SearchSkill()
    return _instance 