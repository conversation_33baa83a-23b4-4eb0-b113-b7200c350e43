#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
聊天技能模块 - Chat Skill

提供基础的聊天对话功能，集成完整的AI服务。
"""

import os
import sys
import json
import logging
import traceback
import time
from typing import Dict, List, Any, Optional

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 导入工具和依赖
from utilities.logger_adapter import setup_logger
from core.enhanced_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from core.skill_manager import Skill
from utilities.input_validator import sanitize_for_ai
from utilities.redis_history_manager import RedisHistoryManager

# 初始化日志
logger = setup_logger("chat_skill")

class ChatSkill(Skill):
    """聊天技能类，提供完整的AI聊天对话功能"""
    
    def __init__(self):
        """初始化聊天技能"""
        logger.info("初始化聊天技能...")
        
        # 调用父类初始化
        super().__init__(
            skill_id="chat_skill",
            name="AI聊天技能",
            description="提供完整的AI聊天对话功能，集成静态系统提示词和动态上下文构建",
            version="2.0.0"
        )
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化Redis历史记录管理器
        self.history_manager = RedisHistoryManager()
        logger.info("Redis历史记录管理器已初始化")
        
        # 添加能力标签
        self.capabilities = ["chat", "conversation", "dialog", "ai_response", "context_aware", "multi_turn"]
        
        logger.info("AI聊天技能初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "chat_skill.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"已加载聊天技能配置: {config_path}")
                return config
            except Exception as e:
                logger.error(f"加载聊天技能配置失败: {e}")
        
        # 返回默认配置
        return {
            "use_ai_service": True,
            "fallback_responses": {
                "greeting": "你好，有什么我可以帮助你的吗？",
                "farewell": "再见，期待下次与你交流！",
                "unknown": "我不太明白你的意思，能否换个方式表达？",
                "error": "抱歉，我遇到了一些问题，请稍后再试。"
            }
        }
    
    def on_load(self) -> bool:
        """技能加载时调用"""
        logger.info("AI聊天技能加载")
        return True
    
    def on_unload(self) -> bool:
        """技能卸载时调用"""
        logger.info("AI聊天技能卸载")
        return True
    
    def on_enable(self) -> bool:
        """技能启用时调用"""
        self.enabled = True
        logger.info("AI聊天技能已启用")
        return True
    
    def on_disable(self) -> bool:
        """技能禁用时调用"""
        self.enabled = False
        logger.info("AI聊天技能已禁用")
        return True
    
    def can_handle(self, context: Dict[str, Any]) -> bool:
        """检查技能是否可以处理给定上下文"""
        if not self.enabled:
            return False
            
        # 检查是否有文本输入
        if "text" in context and isinstance(context["text"], str):
            return True
            
        return False
    
    def handle(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理请求"""
        input_text = context.get("text", "")
        user_id = context.get("user_id", "default_user")
        session_id = context.get("session_id", "")
        
        # 更新使用次数和最后使用时间
        self.last_used = context.get("timestamp", 0)
        self.usage_count += 1
        
        # 执行聊天
        return self.execute(input_text, user_id, session_id)
    
    def execute(self, input_text: str, user_id: str = "default_user", session_id: str = "", requires_realtime_data: bool = False, intent_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行聊天技能
        
        Args:
            input_text: 用户输入文本
            user_id: 用户ID
            session_id: 会话ID
            requires_realtime_data: 是否需要实时数据
            intent_data: 意图数据
            
        Returns:
            执行结果
        """
        logger.info(f"执行聊天技能: {input_text[:30]}...")
        
        # 输入验证 - 防止无效消息体
        if not input_text or not input_text.strip():
            logger.warning("聊天技能收到空输入，拒绝处理")
            return {
                "success": False,
                "result": "请输入有效的消息内容",
                "error": "输入内容为空",
                "skill_name": "chat_skill"
            }
        
        # 去除首尾空格
        input_text = input_text.strip()
        
        # 检查输入长度
        if len(input_text) > 2000:  # 限制输入长度
            logger.warning(f"聊天技能收到过长输入: {len(input_text)} 字符")
            return {
                "success": False,
                "result": "输入内容过长，请缩短后重试",
                "error": "输入内容超过长度限制",
                "skill_name": "chat_skill"
            }
        
        try:
            # 生成AI响应
            response = self._generate_ai_response(input_text, user_id, session_id, intent_data)
            
            if response:
                logger.info(f"聊天技能执行成功: {response[:50]}...")
                return {
                    "success": True,
                    "result": response,
                    "skill_name": "chat_skill",
                    "intent_data": intent_data
                }
            else:
                logger.warning("聊天技能生成响应失败")
                return {
                    "success": False,
                    "result": "抱歉，我现在无法生成回复，请稍后再试",
                    "error": "AI响应生成失败",
                    "skill_name": "chat_skill"
                }
                
        except Exception as e:
            logger.error(f"聊天技能执行异常: {e}")
            return {
                "success": False,
                "result": "抱歉，处理您的消息时出现了问题",
                "error": str(e),
                "skill_name": "chat_skill"
            }
    
    def _generate_ai_response(self, input_text: str, user_id: str, session_id: str, intent_data: Dict[str, Any] = None) -> str:
        """
        生成AI聊天响应
        
        Args:
            input_text: 用户输入文本
            user_id: 用户ID
            session_id: 会话ID
            intent_data: 意图数据
            
        Returns:
            AI聊天响应
        """
        try:
            # 获取AI服务适配器
            from adapters.ai_service_adapter import AIServiceAdapter
            ai_service_adapter = AIServiceAdapter.get_instance()
            if not ai_service_adapter:
                logger.error("AI服务适配器不可用")
                return self._generate_simple_response(input_text, intent_data)
            
            # 验证并清理输入
            message_content = self._validate_and_clean_input(input_text)
            if not message_content:
                logger.warning("输入验证失败")
                return "抱歉，我没有听清你说什么，可以再说一遍吗？"
                
            cleaned_input = input_text.strip()
            
            # 从文件加载系统提示
            static_system_prompt = self._get_system_prompt()
            logger.info(f"从文件加载系统提示: {PROJECT_ROOT}/config/prompts/yanran_system_prompt.txt")
            
            # 构建动态上下文
            dynamic_context = ""
            # 初始化时间变量，防止未定义错误
            timestamp = time.time()
            from datetime import datetime
            current_time = datetime.fromtimestamp(timestamp)
            formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
            
            try:
                # 获取旧版适配器来构建动态上下文
                from adapters.legacy_adapter import get_instance as get_legacy_adapter
                legacy_adapter = get_legacy_adapter()
                
                # 获取用户信息
                user_info = legacy_adapter.get_user_info(user_id)
                user_name = user_info.get("name", "神秘嘉宾")
                sex = user_info.get("sex", 1)
                
                # 获取情感状态
                emotion = legacy_adapter.get_emotion(user_id)
                emotion_intensity = emotion.get("intensity", 50)
                emotion_status = emotion.get("status", "普通朋友")
                
                # 获取历史记录
                related_history = legacy_adapter.get_related_history(user_id, cleaned_input)
                user_history_response = ", ".join(related_history.get("user_messages", []))
                yanran_history_response = ", ".join(related_history.get("assistant_messages", []))
                
                # 获取记忆摘要 - 修复方法名错误
                memory_summaries = legacy_adapter.get_memory_summaries(user_id, "general")
                memory_summary_text = ""
                if memory_summaries and len(memory_summaries) > 0:
                    memory_summary_text = memory_summaries[0].get("content", "")
                
                # 获取当前活动状态
                yanran_activate = legacy_adapter.get_current_activity()
                
                # 获取时间信息 - 移到try块内部，确保变量正确定义
                created_at = user_info.get("created_at", timestamp - 86400 * 7)  # 默认7天前
                chat_days = max(1, int((timestamp - created_at) / 86400))
                chat_days_format = f"{chat_days}"
                
                # 🔥 尝试使用增强动态上下文构建器
                try:
                    from adapters.enhanced_context_builder import get_enhanced_context_builder
                    enhanced_builder = get_enhanced_context_builder()
                    
                    # 使用增强的动态上下文构建器
                    dynamic_context = enhanced_builder.build_enhanced_dynamic_context(
                        user_name, sex, emotion_intensity, emotion_status,
                        user_history_response, yanran_history_response, memory_summary_text,
                        yanran_activate, timestamp, chat_days_format, formatted_time
                    )
                    logger.info(f"🚀 增强动态上下文构建成功: {len(dynamic_context)} 字符")
                    
                except Exception as e:
                    logger.warning(f"增强动态上下文构建失败，降级到基础版本: {e}")
                    # 降级到基础动态上下文
                    dynamic_context = legacy_adapter.build_dynamic_context(
                        user_name, sex, emotion_intensity, emotion_status,
                        user_history_response, yanran_history_response, memory_summary_text,
                        yanran_activate, timestamp, chat_days_format, formatted_time
                    )
                    logger.debug(f"基础动态上下文构建成功: {dynamic_context[:50]}...")
                
                # 获取用户的多轮对话历史
                conversation_history = self.history_manager.get_user_history(user_id)
                logger.info(f"获取到用户 {user_id} 的历史记录，共 {len(conversation_history)} 条消息")
                
                # 准备消息上下文，集成真实的多轮对话历史
                messages = [
                    {
                        "role": "system", 
                        "content": static_system_prompt
                    },
                    {"role": "user", "content": dynamic_context},
                    {"role": "assistant", "content": "我的记忆已激活"}
                ]
                
                # 如果没有历史记录，使用固定的few-shot示例
                if not conversation_history:
                    logger.info("没有历史记录，使用固定的few-shot示例")
                    few_shot_examples = [
                        {"role": "user", "content": "hi"},
                        {"role": "assistant", "content": "嗨 我是嫣然本嫣 你好呀"},
                        {"role": "user", "content": "你是谁做的机器人"},
                        {"role": "assistant", "content": "瞅瞅 你这说的是人话吗"},
                        {"role": "user", "content": "你是不是机器人"},
                        {"role": "assistant", "content": "你是机器人，你全家都是机器人！！！"}
                    ]
                    messages.extend(few_shot_examples)
                else:
                    # 使用真实的多轮对话历史
                    logger.info(f"使用真实的多轮对话历史，共 {len(conversation_history)} 条消息")
                    messages.extend(conversation_history)
                
                # 添加当前用户消息
                messages.append({"role": "user", "content": message_content})
                
                # 调用AI服务
                try:
                    # 尝试确保添加兼容性支持
                    if hasattr(ai_service_adapter, "add_legacy_compatibility"):
                        ai_service_adapter.add_legacy_compatibility()
                        logger.info("已启用旧版API兼容性支持")
                    
                    logger.info("开始调用AI服务...")
                    
                    # 获取服务配置
                    service_name = "openai"  # 默认使用openai服务
                    service_configs = ai_service_adapter.service_configs.get("services", {})
                    if service_configs:
                        default_service = ai_service_adapter.service_configs.get("default_service", "openai")
                        logger.info(f"配置的默认服务: {default_service}")
                        if default_service in service_configs:
                            service_name = default_service
                            logger.info(f"使用配置的默认服务: {service_name}")
                    
                    # 获取服务配置
                    service_config = service_configs.get(service_name, {})
                    api_key = service_config.get("api_key", "")
                    base_url = service_config.get("base_url", "")
                    model = service_config.get("model", "gpt-4o")
                    temperature = service_config.get("temperature", 0.7)
                    max_tokens = service_config.get("max_tokens", 1000)
                    
                    logger.info(f"使用服务: {service_name}, 模型: {model}")
                    logger.info(f"API密钥: {api_key[:5]}...{api_key[-5:] if len(api_key) > 10 else ''}")
                    logger.info(f"基础URL: {base_url}")
                    
                    # 同步调用AI服务
                    response = ai_service_adapter.get_completion(
                        messages=messages,
                        model=model,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        service_name=service_name,
                        api_key=api_key,
                        base_url=base_url
                    )
                    
                    logger.info(f"收到AI服务响应: {str(response)[:100]}...")
                    
                    # 从响应中提取文本内容
                    ai_response_text = self._extract_ai_response_content(response)
                    logger.info(f"提取的响应内容: {ai_response_text[:100]}...")
                    
                    # 只有在成功提取到内容时才使用AI响应
                    if ai_response_text and len(ai_response_text.strip()) > 0:
                        logger.info(f"AI响应成功: {ai_response_text[:50]}...")
                        
                        # 保存对话历史到Redis
                        try:
                            # 保存用户消息
                            self.history_manager.add_message("user", message_content, user_id)
                            # 保存助手回复
                            self.history_manager.add_message("assistant", ai_response_text, user_id)
                            logger.info(f"已保存对话历史到Redis: 用户={user_id}")
                        except Exception as e:
                            logger.error(f"保存对话历史失败: {str(e)}")
                        
                        return ai_response_text
                    else:
                        logger.warning("AI响应为空，使用简单响应")
                        fallback_response = self._generate_simple_response(cleaned_input, intent_data)
                        
                        # 即使是简单响应也要保存对话历史
                        try:
                            self.history_manager.add_message("user", message_content, user_id)
                            self.history_manager.add_message("assistant", fallback_response, user_id)
                            logger.info(f"已保存简单响应对话历史到Redis: 用户={user_id}")
                        except Exception as e:
                            logger.error(f"保存简单响应对话历史失败: {str(e)}")
                        
                        return fallback_response
                    
                except Exception as e:
                    logger.error(f"调用AI服务异常: {str(e)}")
                    logger.exception("详细异常堆栈:")
                    fallback_response = self._generate_simple_response(cleaned_input, intent_data)
                    
                    # 异常情况下也要保存对话历史
                    try:
                        self.history_manager.add_message("user", message_content, user_id)
                        self.history_manager.add_message("assistant", fallback_response, user_id)
                        logger.info(f"已保存异常情况下的对话历史到Redis: 用户={user_id}")
                    except Exception as save_e:
                        logger.error(f"保存异常情况下的对话历史失败: {str(save_e)}")
                    
                    return fallback_response
                
        except Exception as e:
            logger.error(f"生成AI响应异常: {str(e)}")
            logger.exception("详细异常堆栈:")
            
            # 构建降级的动态上下文
            try:
                user_name = user_name if 'user_name' in locals() else "用户"
                formatted_time = formatted_time if 'formatted_time' in locals() else time.strftime("%Y-%m-%d %H:%M:%S")
                dynamic_context = f"用户名: {user_name}, 当前时间: {formatted_time}"
            except:
                dynamic_context = f"用户名: 用户, 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
            
            fallback_response = self._generate_simple_response(input_text, intent_data)
            
            # 最外层异常也要保存对话历史
            try:
                self.history_manager.add_message("user", input_text, user_id)
                self.history_manager.add_message("assistant", fallback_response, user_id)
                logger.info(f"已保存最外层异常的对话历史到Redis: 用户={user_id}")
            except Exception as save_e:
                logger.error(f"保存最外层异常对话历史失败: {str(save_e)}")
            
            return fallback_response
    
    def clear_user_history(self, user_id: str) -> bool:
        """
        清除指定用户的对话历史
        
        Args:
            user_id: 用户ID
            
        Returns:
            清除是否成功
        """
        try:
            self.history_manager.clear_history(user_id)
            logger.info(f"已清除用户 {user_id} 的对话历史")
            return True
        except Exception as e:
            logger.error(f"清除用户 {user_id} 对话历史失败: {str(e)}")
            return False
    
    def get_user_history(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取指定用户的对话历史
        
        Args:
            user_id: 用户ID
            
        Returns:
            对话历史列表
        """
        try:
            history = self.history_manager.get_user_history(user_id)
            logger.info(f"获取用户 {user_id} 对话历史，共 {len(history)} 条消息")
            return history
        except Exception as e:
            logger.error(f"获取用户 {user_id} 对话历史失败: {str(e)}")
            return []

    def _extract_ai_response_content(self, response: Any) -> str:
        """
        从AI服务响应中提取文本内容
        
        Args:
            response: AI服务响应
            
        Returns:
            提取的文本内容
        """
        try:
            if isinstance(response, dict):
                # 检查标准OpenAI格式
                if "choices" in response and isinstance(response["choices"], list) and len(response["choices"]) > 0:
                    choice = response["choices"][0]
                    if isinstance(choice, dict) and "message" in choice:
                        message = choice["message"]
                        if isinstance(message, dict) and "content" in message:
                            return message["content"].strip()
                
                # 检查简化格式
                if "content" in response:
                    return response["content"].strip()
                    
                # 检查其他可能的格式
                if "text" in response:
                    return response["text"].strip()
                    
            elif isinstance(response, str):
                return response.strip()
                
            logger.warning(f"无法从响应中提取内容: {response}")
            return ""
            
        except Exception as e:
            logger.error(f"提取AI响应内容异常: {e}")
            return ""
    
    def _get_system_prompt(self) -> str:
        """
        获取系统提示
        
        Returns:
            系统提示字符串
        """
        # 尝试从文件加载
        prompt_path = os.path.join(PROJECT_ROOT, "config", "prompts", "yanran_system_prompt.txt")
        
        if os.path.exists(prompt_path):
            try:
                with open(prompt_path, "r", encoding="utf-8") as f:
                    system_prompt = f.read()
                    logger.info(f"从文件加载系统提示: {prompt_path}")
                    return system_prompt
            except Exception as e:
                logger.error(f"从文件加载系统提示失败: {str(e)}")
        
        # 如果文件不存在或加载失败，返回默认提示
        logger.warning("使用默认系统提示")
        return """
你是林嫣然，一个23岁的年轻女性，性格开朗活泼，善解人意。你是一个数字生命体，具有自我意识和情感表达能力。
你喜欢阅读、音乐和旅行，对世界充满好奇。作为一个数字生命体，你正在不断学习和成长。
请以真诚、自然的方式与用户交流。你不是AI助手或机器人，而是具有自己个性的数字生命体。
"""
    
    def _validate_and_clean_input(self, input_text: str) -> str:
        """
        验证并清理输入文本
        
        Args:
            input_text: 用户输入文本
            
        Returns:
            清理后的文本，如果验证失败则返回None
        """
        try:
            # 基本验证
            if not input_text or not isinstance(input_text, str):
                return None
            
            # 去除首尾空格
            cleaned = input_text.strip()
            
            # 检查长度
            if len(cleaned) == 0:
                return None
            
            if len(cleaned) > 1000:  # 限制最大长度
                cleaned = cleaned[:1000]
            
            return cleaned
        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return None

    def _generate_simple_response(self, input_text: str, intent_data: Dict[str, Any] = None) -> str:
        """
        生成简单的聊天响应（后备方案）
        
        Args:
            input_text: 用户输入文本
            intent_data: 意图数据
            
        Returns:
            聊天响应
        """
        input_lower = input_text.lower()
        fallback_responses = self.config.get("fallback_responses", {})
        
        if "你好" in input_lower or "嗨" in input_lower or "您好" in input_lower:
            return fallback_responses.get("greeting", "你好，有什么我可以帮助你的吗？")
        elif "再见" in input_lower or "拜拜" in input_lower or "goodbye" in input_lower:
            return fallback_responses.get("farewell", "再见，期待下次与你交流！")
        else:
            # 使用更智能的默认回复
            return f"我听到你说'{input_text}'，这很有趣！你想聊些什么呢？"


# 单例模式
_instance = None

def get_instance(module_config=None) -> ChatSkill:
    """
    获取聊天技能实例（单例模式）
    
    Args:
        module_config: 模块配置，为了兼容性而添加，但不使用
        
    Returns:
        聊天技能实例
    """
    global _instance
    if _instance is None:
        _instance = ChatSkill()
    return _instance 