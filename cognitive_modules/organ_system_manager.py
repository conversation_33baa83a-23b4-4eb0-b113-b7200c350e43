#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
器官系统管理器
==============

负责林嫣然数字生命体器官系统的统一调度、状态管理和协调
实现器官系统真正服务于数字生命引擎的架构目标
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from utilities.unified_logger import get_unified_logger
from utilities.singleton_manager import get, register


class OrganPriority(Enum):
    """器官优先级"""
    CRITICAL = 1    # 关键器官（安全防护）
    HIGH = 2        # 高优先级（世界感知、主动表达）
    NORMAL = 3      # 普通优先级（创作表达、技能协调）
    LOW = 4         # 低优先级（财富管理、数据感知）
    BACKGROUND = 5  # 后台优先级（关系协调）


class OrganStatus(Enum):
    """器官状态"""
    INACTIVE = "inactive"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    BUSY = "busy"
    ERROR = "error"
    MAINTENANCE = "maintenance"


@dataclass
class OrganInfo:
    """器官信息"""
    name: str
    instance: Any
    priority: OrganPriority
    status: OrganStatus
    last_activity: datetime
    activity_count: int
    error_count: int
    performance_score: float
    feedback_to_life: Dict[str, Any]


class OrganSystemManager:
    """器官系统管理器"""
    
    def __init__(self):
        self.logger = get_unified_logger("organ_system_manager")
        
        # 器官注册表
        self.organs: Dict[str, OrganInfo] = {}
        
        # 系统状态
        self.system_status = "initializing"
        self.last_coordination_time = None
        self.coordination_interval = 30  # 30秒协调一次
        
        # 🔥 老王修复：在Flask同步上下文中延迟创建asyncio.Queue
        # 避免在构造函数中调用asyncio.get_event_loop()
        self.feedback_queue = None  # 延迟初始化
        self.life_context_updates = {}
        
        # 🔥 修复：添加任务生命周期管理
        self.coordination_task = None  # 协调循环任务
        self.running = False  # 运行状态标志
        
        # 性能统计
        self.system_stats = {
            "total_organs": 0,
            "active_organs": 0,
            "total_activities": 0,
            "coordination_cycles": 0,
            "feedback_processed": 0,
            "life_context_updates": 0
        }
        
        self.logger.info("⚙️ 器官系统管理器初始化完成")
    
    async def initialize_organ_system(self) -> bool:
        """初始化器官系统"""
        try:
            self.logger.info("⚙️ 开始初始化器官系统...")
            
            # 🔥 老王修复：在异步上下文中初始化feedback_queue
            if self.feedback_queue is None:
                try:
                    self.feedback_queue = asyncio.Queue()
                    self.logger.debug("⚙️ 反馈队列初始化完成")
                except Exception as e:
                    self.logger.warning(f"⚙️ 反馈队列初始化失败: {e}")
                    self.feedback_queue = None
            
            # 🔥 老王修复：器官注册是同步操作，不需要await
            self._register_all_organs_sync()
            
            # 🔥 老王修复：协调循环启动也改为同步检查
            self._start_coordination_loop_sync()
            
            self.system_status = "active"
            self.running = True  # 🔥 修复：设置运行状态
            self.logger.info(f"⚙️ 器官系统初始化完成 - 注册了 {len(self.organs)} 个器官")
            
            return True
            
        except Exception as e:
            self.logger.error(f"⚙️ 器官系统初始化失败: {e}")
            self.system_status = "error"
            return False
    
    def _register_all_organs_sync(self):
        """注册所有器官（同步版本）"""
        organ_configs = [
            # 关键器官
            {
                "name": "safety_protection_organ",
                "priority": OrganPriority.CRITICAL,
                "auto_start": True
            },
            
            # 高优先级器官
            {
                "name": "world_perception_organ", 
                "priority": OrganPriority.HIGH,
                "auto_start": True
            },
            {
                "name": "proactive_expression_organ",
                "priority": OrganPriority.HIGH,
                "auto_start": True
            },
            
            # 普通优先级器官
            {
                "name": "creative_expression_organ",
                "priority": OrganPriority.NORMAL,
                "auto_start": True
            },
            
            # 低优先级器官
            {
                "name": "wealth_management_organ",
                "priority": OrganPriority.LOW,
                "auto_start": False
            },
            {
                "name": "data_perception_organ",
                "priority": OrganPriority.LOW,
                "auto_start": False
            }
        ]
        
        for config in organ_configs:
            self._register_organ_sync(config)
    
    def _register_organ_sync(self, config: Dict[str, Any]):
        """注册单个器官（同步版本）"""
        try:
            organ_name = config["name"]
            self.logger.info(f"⚙️ 注册器官: {organ_name}")
            
            # 尝试获取已存在的器官实例
            organ_instance = get(organ_name)
            
            if organ_instance:
                # 🔥 老王修复：尝试激活器官
                activation_success = True
                if hasattr(organ_instance, 'activate') and callable(organ_instance.activate):
                    try:
                        activation_result = organ_instance.activate()
                        if activation_result is False:
                            activation_success = False
                            self.logger.warning(f"⚙️ 器官 {organ_name} 激活失败")
                        else:
                            self.logger.info(f"⚙️ 器官 {organ_name} 激活成功")
                    except Exception as e:
                        activation_success = False
                        self.logger.warning(f"⚙️ 器官 {organ_name} 激活异常: {e}")
                else:
                    self.logger.debug(f"⚙️ 器官 {organ_name} 无activate方法，跳过激活")

                # 注册器官信息
                organ_info = OrganInfo(
                    name=organ_name,
                    instance=organ_instance,
                    priority=config["priority"],
                    status=OrganStatus.ACTIVE if activation_success else OrganStatus.INACTIVE,
                    last_activity=datetime.now(),
                    activity_count=0,
                    error_count=0,
                    performance_score=1.0 if activation_success else 0.5,
                    feedback_to_life={}
                )

                self.organs[organ_name] = organ_info
                self.system_stats["total_organs"] += 1
                if activation_success:
                    self.system_stats["active_organs"] += 1

                status_text = "激活成功" if activation_success else "注册成功但激活失败"
                self.logger.info(f"⚙️ 器官 {organ_name} {status_text} (优先级: {config['priority'].name})")
            else:
                self.logger.warning(f"⚙️ 器官 {organ_name} 未找到实例，跳过注册")
                
        except Exception as e:
            self.logger.error(f"⚙️ 器官注册失败 {config.get('name', 'unknown')}: {e}")
    
    def _start_coordination_loop_sync(self):
        """启动器官协调循环（同步版本）"""
        async def coordination_loop():
            self.logger.info("⚙️ 器官协调循环开始运行")
            try:
                while self.running and self.system_status == "active":
                    try:
                        await self._coordinate_organs()
                        await asyncio.sleep(self.coordination_interval)
                    except asyncio.CancelledError:
                        self.logger.info("⚙️ 器官协调循环被取消")
                        break
                    except Exception as e:
                        self.logger.error(f"⚙️ 器官协调循环异常: {e}")
                        await asyncio.sleep(5)
            finally:
                self.logger.info("⚙️ 器官协调循环已退出")
        
        # 🔥 老王修复：改进任务生命周期管理
        try:
            import asyncio
            try:
                # 尝试获取当前事件循环
                loop = asyncio.get_event_loop()
                if loop and loop.is_running():
                    # 🔥 修复：保存任务引用，便于后续管理
                    self.coordination_task = loop.create_task(coordination_loop())
                    self.logger.info("⚙️ 器官协调循环已在现有事件循环中启动")
                else:
                    self.logger.warning("⚙️ 事件循环未运行，跳过器官协调循环启动")
            except RuntimeError:
                # 没有运行的事件循环，跳过协调循环
                self.logger.warning("⚙️ 无可用事件循环，跳过器官协调循环启动")
        except Exception as e:
            self.logger.warning(f"⚙️ 器官协调循环启动失败: {e}")

    async def _register_organ(self, config: Dict[str, Any]):
        """注册单个器官（保留异步版本以兼容其他调用）"""
        self._register_organ_sync(config)
    
    async def _coordinate_organs(self):
        """协调器官工作"""
        try:
            self.logger.debug("⚙️ 开始器官协调...")
            
            # 更新器官状态
            await self._update_organ_status()
            
            # 收集器官反馈
            await self._collect_organ_feedback()
            
            self.last_coordination_time = datetime.now()
            self.system_stats["coordination_cycles"] += 1
            
            self.logger.debug("⚙️ 器官协调完成")
            
        except Exception as e:
            self.logger.error(f"⚙️ 器官协调失败: {e}")
    
    async def _update_organ_status(self):
        """更新器官状态"""
        for organ_name, organ_info in self.organs.items():
            try:
                # 检查器官健康状态
                if hasattr(organ_info.instance, 'get_organ_status'):
                    status_info = organ_info.instance.get_organ_status()
                    
                    # 更新活动时间
                    if status_info.get('active', False):
                        organ_info.last_activity = datetime.now()
                        organ_info.activity_count += 1
                        organ_info.status = OrganStatus.ACTIVE
                    
                    # 更新性能分数
                    performance = status_info.get('performance_score', 1.0)
                    organ_info.performance_score = performance
                    
            except Exception as e:
                self.logger.debug(f"⚙️ 器官 {organ_name} 状态更新失败: {e}")
                organ_info.error_count += 1
                if organ_info.error_count > 5:
                    organ_info.status = OrganStatus.ERROR
    
    async def _collect_organ_feedback(self):
        """收集器官反馈"""
        for organ_name, organ_info in self.organs.items():
            try:
                # 获取器官反馈
                if hasattr(organ_info.instance, 'get_feedback_for_life'):
                    feedback = organ_info.instance.get_feedback_for_life()
                    
                    if feedback:
                        organ_info.feedback_to_life = feedback
                        self.system_stats["feedback_processed"] += 1
                        
            except Exception as e:
                self.logger.debug(f"⚙️ 器官 {organ_name} 反馈收集失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'system_status': self.system_status,
            'stats': self.system_stats.copy(),
            'organs_count': len(self.organs),
            'active_organs': [name for name, info in self.organs.items() if info.status == OrganStatus.ACTIVE],
            'last_coordination': self.last_coordination_time.isoformat() if self.last_coordination_time else None
        }
    
    def get_organ_status(self, organ_name: str) -> Optional[Dict[str, Any]]:
        """获取特定器官状态"""
        organ_info = self.organs.get(organ_name)
        if organ_info:
            return {
                'name': organ_info.name,
                'status': organ_info.status.value,
                'priority': organ_info.priority.value,
                'last_activity': organ_info.last_activity.isoformat(),
                'activity_count': organ_info.activity_count,
                'error_count': organ_info.error_count,
                'performance_score': organ_info.performance_score
            }
        return None

    def get_all_organs(self) -> Dict[str, Any]:
        """
        🔥 香草修复：获取所有器官实例

        Returns:
            器官字典 {器官名称: 器官实例}
        """
        return {name: info.instance for name, info in self.organs.items()}

    def stop_system(self):
        """停止器官系统"""
        try:
            self.logger.info("⚙️ 开始停止器官系统...")
            
            # 设置停止标志
            self.running = False
            self.system_status = "stopping"
            
            # 取消协调任务
            if self.coordination_task and not self.coordination_task.done():
                self.coordination_task.cancel()
                self.logger.info("⚙️ 器官协调任务已取消")
            
            # 停止所有器官
            for organ_name, organ_info in self.organs.items():
                try:
                    # 🔥 老王修复：优先调用deactivate方法
                    if hasattr(organ_info.instance, 'deactivate') and callable(organ_info.instance.deactivate):
                        organ_info.instance.deactivate()
                        self.logger.debug(f"⚙️ 器官 {organ_name} 已停用")
                    elif hasattr(organ_info.instance, 'stop') and callable(organ_info.instance.stop):
                        organ_info.instance.stop()
                        self.logger.debug(f"⚙️ 器官 {organ_name} 已停止")
                except Exception as e:
                    self.logger.warning(f"⚙️ 停止器官 {organ_name} 失败: {e}")
            
            self.system_status = "stopped"
            self.logger.info("⚙️ 器官系统已停止")
            
        except Exception as e:
            self.logger.error(f"⚙️ 停止器官系统失败: {e}")

    async def stop_system_async(self):
        """异步停止器官系统"""
        try:
            self.logger.info("⚙️ 开始异步停止器官系统...")
            
            # 设置停止标志
            self.running = False
            self.system_status = "stopping"
            
            # 取消协调任务并等待完成
            if self.coordination_task and not self.coordination_task.done():
                self.coordination_task.cancel()
                try:
                    await self.coordination_task
                except asyncio.CancelledError:
                    self.logger.info("⚙️ 器官协调任务已正确取消")
            
            # 异步停止所有器官
            for organ_name, organ_info in self.organs.items():
                try:
                    # 🔥 老王修复：优先调用deactivate方法
                    if hasattr(organ_info.instance, 'deactivate') and callable(organ_info.instance.deactivate):
                        if asyncio.iscoroutinefunction(organ_info.instance.deactivate):
                            await organ_info.instance.deactivate()
                        else:
                            organ_info.instance.deactivate()
                        self.logger.debug(f"⚙️ 器官 {organ_name} 已停用")
                    elif hasattr(organ_info.instance, 'stop'):
                        if asyncio.iscoroutinefunction(organ_info.instance.stop):
                            await organ_info.instance.stop()
                        else:
                            organ_info.instance.stop()
                        self.logger.debug(f"⚙️ 器官 {organ_name} 已停止")
                except Exception as e:
                    self.logger.warning(f"⚙️ 停止器官 {organ_name} 失败: {e}")
            
            self.system_status = "stopped"
            self.logger.info("⚙️ 器官系统已异步停止")
            
        except Exception as e:
            self.logger.error(f"⚙️ 异步停止器官系统失败: {e}")


# 单例获取函数
_organ_system_manager_instance = None

def get_organ_system_manager() -> OrganSystemManager:
    """获取器官系统管理器实例"""
    global _organ_system_manager_instance
    if _organ_system_manager_instance is None:
        _organ_system_manager_instance = OrganSystemManager()
    return _organ_system_manager_instance


# 注册到单例管理器
register("organ_system_manager", get_organ_system_manager) 