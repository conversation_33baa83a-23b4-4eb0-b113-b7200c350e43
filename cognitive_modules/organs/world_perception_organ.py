"""
林嫣然的世界感知器官
==================

作为林嫣然的"眼睛"，感知外部世界的变化，特别是热搜趋势。
具备基于林嫣然人格的AI决策能力，判断哪些事件值得关注和表达。
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from utilities.unified_logger import get_unified_logger


try:
    from .base_organ import LifeOrgan
    from ...HOT_TOPICS_API.hot_topic_api import HotTopicAPI  # 复用现有热搜API
except ImportError:
    # 兜底导入方式
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent))
    from cognitive_modules.organs.base_organ import LifeOrgan
    try:
        from HOT_TOPICS_API.hot_topic_service import hot_topic_service
        
        # 创建适配器类以保持兼容性
        class HotTopicAPI:
            def __init__(self, organ_logger=None):
                self.service = hot_topic_service
                # 保存器官的logger引用
                self.organ_logger = organ_logger
            
            async def get_hot_topics(self, platform: str = None):
                """获取真实热搜数据"""
                try:
                    result = await self.service.get_hot_topics(platform=platform)
                    
                    # 处理API响应
                    if isinstance(result, dict):
                        if 'error' in result:
                            error_msg = result['error']
                            details = result.get('details', '无详情')
                            if self.organ_logger:
                                self.organ_logger.error(f"🌍 热搜API错误: {error_msg}")
                                self.organ_logger.error(f"🌍 错误详情: {details}")
                            return []  # 返回空列表而不是模拟数据
                        
                        if 'data' in result and isinstance(result['data'], list):
                            # 转换为统一格式
                            topics = []
                            for i, item in enumerate(result['data'], 1):
                                topic = {
                                    'title': item.get('title', ''),
                                    'url': item.get('url', ''),
                                    'rank': i,
                                    'heat': item.get('hot', 0),
                                    'desc': item.get('desc', ''),
                                    'platform': result.get('name', 'unknown')
                                }
                                topics.append(topic)
                            return topics
                    
                    return []  # 如果数据格式不正确，返回空列表
                    
                except Exception as e:
                    import traceback
                    if self.organ_logger:
                        self.organ_logger.error(f"🌍 热搜API适配器异常: {e}")
                        self.organ_logger.error(f"🌍 异常详情: {traceback.format_exc()}")
                    return []  # 返回空列表
                
    except ImportError as import_error:
        # 如果热搜API不可用，不使用模拟数据，而是记录错误
        import logging
        error_msg = str(import_error)
        
        class HotTopicAPI:
            def __init__(self):
                self.logger = get_unified_logger(__name__)
                self.logger.error(f"🌍 热搜API模块导入失败: {error_msg}")
            
            async def get_hot_topics(self, platform: str = None):
                """热搜API不可用时返回空数据"""
                self.logger.warning("🌍 热搜API不可用，无法获取真实数据")
                return []  # 返回空列表，不返回模拟数据


class WorldPerceptionOrgan(LifeOrgan):
    """
    林嫣然的世界感知器官
    
    功能：
    1. 感知外部世界变化（热搜、新闻、趋势）
    2. 基于林嫣然人格判断事件重要性
    3. 生成主动思考和表达
    4. 触发其他器官的协同反应
    """
    
    # 🔥 P0级别修复：添加类级别的实例跟踪和启动状态
    _instance = None
    _perception_started = False
    _instance_count = 0
    
    def __init__(self, config_path: Optional[str] = None, ai_adapter=None):
        # 🔥 P0级别修复：防止重复实例化和重复启动感知循环
        WorldPerceptionOrgan._instance_count += 1
        self.instance_id = WorldPerceptionOrgan._instance_count
        
        super().__init__(
            organ_name="世界感知器官",
            organ_function="感知外部世界变化，判断事件重要性",
            config_path=config_path,
            ai_adapter=ai_adapter
        )
        
        # 初始化热搜API（复用现有功能）
        self.hot_topic_api = HotTopicAPI(organ_logger=self.logger)

        # 🔥 老王新增：数据持久化管理器
        try:
            from cognitive_modules.organs.world_perception_data_manager import get_world_perception_data_manager
            self.data_manager = get_world_perception_data_manager()
            self.logger.info("🧠 数据持久化管理器初始化成功")
        except Exception as e:
            self.logger.warning(f"⚠️ 数据持久化管理器初始化失败: {e}")
            self.data_manager = None
        
        # 感知配置 - 🔥 优化频率，提高实时性
        self.perception_config = {
            'update_interval': 600,  # 🔥 改为10分钟更新一次，提高实时性
            'significance_threshold': 0.5,  # 🔥 降低重要性阈值从0.6到0.3，提高感知敏感度
            'max_topics_per_batch': 20,
            'interest_keywords': ['科技', '文化', '教育', '艺术', '大国', '军事', 'AI', '人工智能', '金融', '财经',
                                '经济', '投资', '股市', '创业', '科研', '学术', '社会', '民生', '健康', '环保'],
            'emotion_keywords': {
                '温暖': ['温暖', '感动', '治愈', '美好', '爱心'],
                '关注': ['关注', '重要', '影响', '变化', '发展'],
                '担忧': ['担忧', '问题', '危机', '困难', '挑战']
            },
            # 🔥 新增：感知优化配置
            'fallback_enabled': True,  # 启用兜底机制
            'min_events_threshold': 1,  # 最少发现事件数
            'max_empty_cycles': 3,     # 最大空感知周期
            'interest_boost_multiplier': 1.5,  # 兴趣匹配加成
            # 🔥 优化数据更新频率：热搜30min-1hour，实时新闻5min-10min
            'hot_topics_interval': 1800,  # 热搜数据获取间隔：30分钟
            'realtime_news_interval': 600,  # 实时新闻获取间隔：10分钟
            'min_call_interval': 300,  # 最小调用间隔：5分钟，确保实时性
        }
        
        # 感知状态
        self.last_perception_time = None
        self.current_world_state = {}
        self.significant_events = []
        self.perception_patterns = {}

        # 🔥 老王修复：使用Redis缓存替代内存缓存
        from utilities.redis_cache_adapter import get_redis_cache
        self.redis_cache = get_redis_cache()
        self.last_cache_update = None        # 最后缓存更新时间

        # 🔥 新增：频率控制状态 - 分离不同功能的时间戳
        self.last_hot_topics_time = None
        self.last_realtime_news_time = None
        self.last_hot_topics_call_time = None  # 热搜专用调用时间
        self.last_realtime_news_call_time = None  # 新闻专用调用时间
        
        # 🔥 P0级别修复：防止重复启动感知循环
        if not WorldPerceptionOrgan._perception_started:
            self.logger.debug(f"🌍 实例 #{self.instance_id} 启动感知循环...")
            self._start_continuous_perception()
            WorldPerceptionOrgan._perception_started = True
            self.logger.debug(f"🌍 感知循环已由实例 #{self.instance_id} 启动")
        else:
            self.logger.debug(f"🌍 实例 #{self.instance_id} 初始化完成，感知循环已在运行中")
        
        # 🔥 老王新增：启动时加载历史学习数据
        if self.data_manager:
            try:
                # 异步加载历史数据
                import asyncio
                try:
                    loop = asyncio.get_running_loop()
                    loop.create_task(self._load_historical_data_async())
                except RuntimeError:
                    # 没有运行中的事件循环，延迟加载
                    self.logger.info("🧠 将在事件循环启动后加载历史数据")
            except Exception as load_e:
                self.logger.warning(f"⚠️ 启动历史数据加载失败: {load_e}")

        self.logger.info(f"🌍 世界感知器官实例 #{self.instance_id} 初始化完成")

    async def _load_historical_data_async(self):
        """异步加载历史数据"""
        try:
            # 延迟一点时间，确保系统完全启动
            await asyncio.sleep(5)

            # 加载历史学习数据
            result = await self.load_historical_learning_data()
            if result.get('memory_loaded'):
                self.logger.info(f"🧠 历史数据加载完成: {result.get('recent_events_count', 0)}个事件")

                # 基于学习数据增强关键词
                await self.enhance_interest_keywords_from_learning()
            else:
                self.logger.warning(f"⚠️ 历史数据加载失败: {result.get('error', '未知错误')}")

        except Exception as e:
            self.logger.error(f"❌ 异步加载历史数据失败: {e}")
    
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定的AI提示词"""
        return """
        你是林嫣然的世界感知器官，负责感知外部世界的变化。
        
        你的特点：
        - 对世界充满好奇和关注
        - 善于从人文角度理解事件
        - 关注对人们生活的影响
        - 有独立的价值判断
        - 温暖而有同理心
        
        你需要：
        1. 分析热搜和新闻事件的重要性
        2. 判断哪些事件值得林嫣然关注
        3. 基于真实数据理解事件意义
        4. 决定是否需要主动表达观点
        5. 评估事件对情感和认知的影响
        
        请始终保持林嫣然温暖、智慧、有独立思考的特质。
        """
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        # 从raw_input中获取类型信息
        raw_input = processed_input.get('raw_input', {})
        input_type = raw_input.get('type', 'unknown')
        
        # 映射输入类型到决策执行方法
        if input_type in ['scheduled_perception', 'world_perception']:
            return await self._execute_world_perception(ai_decision, processed_input)
        elif input_type == 'significance_evaluation':
            return await self._execute_significance_evaluation(ai_decision, processed_input)
        elif input_type == 'proactive_expression':
            return await self._execute_proactive_expression(ai_decision, processed_input)
        elif input_type == 'topic_evaluation':
            return await self._execute_significance_evaluation(ai_decision, processed_input)
        else:
            return await self._execute_general_perception(ai_decision, processed_input)
    
    async def _execute_world_perception(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行世界感知"""
        try:
            self.logger.info("🌍 开始执行世界感知...")
            
            # 🔥 新增：同时获取热搜数据和实时新闻数据
            hot_topics_data = await self._get_hot_topics_data()
            news_data = await self._get_realtime_news_data()
            
            # 合并两种数据源
            all_perception_data = hot_topics_data + news_data
            
            if not all_perception_data:
                return {
                    'perception_type': 'world_perception',
                    'success': False,
                    'message': '无法获取世界感知数据',
                    'data': {}
                }
            
            # 识别重要事件（包含热搜和新闻）
            significant_events = await self._identify_significant_events(all_perception_data, ai_decision)
            
            # 生成主动思考
            proactive_thoughts = await self._generate_proactive_thoughts(significant_events)
            
            # 更新世界状态
            self.current_world_state = {
                'hot_topics': hot_topics_data,
                'realtime_news': news_data,
                'significant_events': significant_events,
                'last_update': datetime.now().isoformat(),
                'perception_summary': f"感知到{len(hot_topics_data)}条热搜和{len(news_data)}条新闻，识别出{len(significant_events)}个重要事件"
            }
            
            # 🔥 老王修复：保存重要事件到内存和数据库，并更新缓存
            self._update_significant_events_with_dedup(significant_events)

            # 🔥 老王新增：更新缓存的重要事件，供主动表达使用
            self._update_cached_significant_events()

            # 🔥 新增：持久化重要事件到数据库
            if self.data_manager and significant_events:
                for event in significant_events:
                    try:
                        # 🔥 老王修复：正确提取事件数据结构
                        event_data = {
                            'title': event.get('title', ''),
                            'content': event.get('desc', ''),
                            'source_platform': event.get('platform', ''),
                            'category': event.get('event_type', ''),
                            'emotion_tone': '',  # 暂时为空，后续可以通过AI分析添加
                            'significance_score': float(event.get('significance_score', 0.0)),
                            'yanran_interest_score': float(event.get('yanran_interest_level', 0.0)),
                            'heat_value': int(event.get('heat', 0)),
                            'url': event.get('url', ''),
                            'keywords': event.get('significance_reasons', []),  # 使用重要性原因作为关键词
                            'ai_analysis': {
                                'evaluation_method': event.get('evaluation_method', ''),
                                'discussion_value': event.get('discussion_value', 0),
                                'rank': event.get('rank'),
                                'timestamp': event.get('timestamp', '')
                            },
                            'perception_context': {
                                'perception_time': datetime.now().isoformat(),
                                'world_state_summary': self.current_world_state.get('perception_summary', ''),
                                'trigger_conditions': {}
                            }
                        }

                        # 🔥 老王修复：验证数据完整性后再保存
                        if event_data.get('title') and event_data.get('significance_score', 0) > 0:
                            success = self.data_manager.save_significant_event(event_data)
                            if success:
                                self.logger.debug(f"✅ 成功保存事件: {event_data['title'][:30]}...")
                            else:
                                self.logger.warning(f"⚠️ 保存事件失败: {event_data['title'][:30]}...")
                        else:
                            self.logger.warning(f"⚠️ 跳过保存空数据事件: title={event_data.get('title', 'None')}, score={event_data.get('significance_score', 0)}")
                    except Exception as save_e:
                        self.logger.warning(f"⚠️ 保存事件到数据库失败: {save_e}")

            # 🔥 新增：保存世界状态快照
            if self.data_manager:
                try:
                    snapshot_data = {
                        'hot_topics_count': len(hot_topics_data),
                        'news_count': len(news_data),
                        'significant_events_count': len(significant_events),
                        'perception_summary': self.current_world_state.get('perception_summary', ''),
                        'world_state_data': self.current_world_state,
                        'top_categories': self._analyze_top_categories(significant_events),
                        'emotion_distribution': self._analyze_emotion_distribution(significant_events)
                    }

                    self.data_manager.save_world_state_snapshot(snapshot_data)
                except Exception as snapshot_e:
                    self.logger.warning(f"⚠️ 保存世界状态快照失败: {snapshot_e}")
            
            # 通知其他器官
            await self._notify_other_organs({
                'perception_type': 'world_perception',
                'hot_topics_count': len(hot_topics_data),
                'news_count': len(news_data),
                'significant_events': significant_events,
                'proactive_thoughts': proactive_thoughts
            })
            
            self.logger.debug(f"🌍 世界感知执行完成：热搜{len(hot_topics_data)}条，新闻{len(news_data)}条，重要事件{len(significant_events)}个")
            
            return {
                'perception_type': 'world_perception',
                'success': True,
                'hot_topics_count': len(hot_topics_data),
                'news_count': len(news_data),
                'significant_events_count': len(significant_events),
                'proactive_thoughts_count': len(proactive_thoughts),
                'world_state': self.current_world_state,
                'data': {
                    'hot_topics': hot_topics_data[:10],  # 只返回前10条用于展示
                    'realtime_news': news_data[:5],     # 只返回前5条新闻
                    'significant_events': significant_events,
                    'proactive_thoughts': proactive_thoughts
                }
            }
            
        except Exception as e:
            self.logger.error(f"🌍 执行世界感知失败: {e}")
            import traceback
            self.logger.error(f"🌍 世界感知错误详情: {traceback.format_exc()}")
            return {
                'perception_type': 'world_perception',
                'success': False,
                'message': f'世界感知执行失败: {str(e)}',
                'data': {}
            }
    
    async def _get_hot_topics_data(self) -> List[Dict[str, Any]]:
        """获取热搜数据 - 从多个平台获取数据"""
        try:
            # 🔥 新增：频率控制检查
            current_time = datetime.now()
            
            # 检查热搜专用最小调用间隔
            if self.last_hot_topics_call_time:
                time_since_last_call = (current_time - self.last_hot_topics_call_time).total_seconds()
                if time_since_last_call < self.perception_config['min_call_interval']:
                    self.logger.debug(f"🌍 热搜数据获取间隔过短 ({time_since_last_call:.0f}秒 < {self.perception_config['min_call_interval']}秒)，返回缓存数据")
                    return self.cached_hot_topics
            
            # 检查热搜数据获取间隔
            if self.last_hot_topics_time:
                time_since_last_hot_topics = (current_time - self.last_hot_topics_time).total_seconds()
                if time_since_last_hot_topics < self.perception_config['hot_topics_interval']:
                    self.logger.debug(f"🌍 热搜数据获取间隔未到 ({time_since_last_hot_topics:.0f}秒 < {self.perception_config['hot_topics_interval']}秒)，返回缓存数据")
                    return self.cached_hot_topics
            
            self.logger.debug("🌍 开始获取多平台热搜数据...")
            
            # 🔥 修复：获取多个平台的热搜数据，而不是只获取微博
            core_platforms = ["weibo", "zhihu", "douyin", "bilibili", "baidu", "ithome", "36kr"]
            all_topics = []
            successful_platforms = []
            
            # 并发获取多个平台的数据
            import asyncio
            
            # 为每个平台创建任务
            async def get_platform_data(platform):
                try:
                    platform_data = await self.hot_topic_api.get_hot_topics(platform=platform)
                    if platform_data and isinstance(platform_data, list) and len(platform_data) > 0:
                        # 为每个话题添加平台标识
                        for topic in platform_data:
                            topic['platform'] = platform
                        return platform, platform_data[:10]  # 每个平台最多取10条
                    else:
                        return platform, []
                except Exception as e:
                    self.logger.warning(f"🌍 {platform} 平台获取异常: {e}")
                    return platform, []
            
            # 创建所有平台的任务
            tasks = [get_platform_data(platform) for platform in core_platforms]
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for result in results:
                if isinstance(result, Exception):
                    self.logger.warning(f"🌍 平台获取异常: {result}")
                    continue
                    
                platform, platform_data = result
                if platform_data:
                    all_topics.extend(platform_data)
                    successful_platforms.append(platform)
                    self.logger.debug(f"🌍 {platform} 平台获取成功: {len(platform_data)} 条数据")
                else:
                    self.logger.warning(f"🌍 {platform} 平台获取失败或无数据")
            
            if not all_topics:
                self.logger.error("🌍 所有热搜平台都无法获取数据，世界感知暂时无法工作")
                return []
            
            self.logger.debug(f"🌍 成功获取 {len(all_topics)} 条多平台热搜数据，来源平台: {', '.join(successful_platforms)}")
            
            # 显示原始数据详情（前10条）
            if all_topics:
                self.logger.debug(f"🌍 数据来源: 多平台聚合 ({', '.join(successful_platforms)})")
                self.logger.debug("🌍 原始热搜数据详情:")
                
                for i, topic in enumerate(all_topics[:10], 1):
                    title = topic.get('title', '无标题')
                    heat = topic.get('heat', 0)
                    url = topic.get('url', '无链接')
                    desc = topic.get('desc', '无描述')
                    platform = topic.get('platform', '未知平台')
                    
                    self.logger.debug(f"🌍   [{i:2d}] [{platform}] {title}")
                    self.logger.debug(f"🌍       热度: {heat:,} | 描述: {desc[:50]}...")
                    self.logger.debug(f"🌍       链接: {url}")
            
            # 增强数据处理
            enhanced_topics = []
            high_interest_count = 0
            
            for topic in all_topics[:self.perception_config['max_topics_per_batch']]:
                title = topic.get('title', '')
                if not title:  # 跳过无标题的数据
                    continue
                    
                yanran_interest_score = self._calculate_yanran_interest(title)
                
                enhanced_topic = {
                    'title': title,
                    'url': topic.get('url', ''),
                    'rank': topic.get('rank', len(enhanced_topics) + 1),
                    'heat': topic.get('heat', 0),
                    'desc': topic.get('desc', ''),
                    'platform': topic.get('platform', '未知'),
                    'category': self._categorize_topic(title),
                    'emotion_tone': self._analyze_emotion_tone(title),
                    'yanran_interest_score': yanran_interest_score,
                    'timestamp': datetime.now().isoformat()
                }
                enhanced_topics.append(enhanced_topic)
                
                if yanran_interest_score >= self.perception_config['significance_threshold']:
                    high_interest_count += 1
            
            # 显示处理后的数据分析
            self.logger.debug("🌍 数据分析结果:")
            self.logger.debug(f"🌍   总处理条数: {len(enhanced_topics)}")
            self.logger.debug(f"🌍   高兴趣条数: {high_interest_count}")
            self.logger.debug(f"🌍   兴趣阈值: {self.perception_config['significance_threshold']}")
            
            # 显示高兴趣话题详情
            if high_interest_count > 0:
                self.logger.debug("🌍 高兴趣话题详情:")
                for topic in enhanced_topics:
                    if topic['yanran_interest_score'] >= self.perception_config['significance_threshold']:
                        rank = topic['rank']
                        title = topic['title']
                        category = topic['category']
                        emotion = topic['emotion_tone']
                        score = topic['yanran_interest_score']
                        heat = topic['heat']
                        self.logger.debug(f"🌍   [#{rank}] {title}")
                        self.logger.debug(f"🌍        分类: {category} | 情感: {emotion} | 兴趣度: {score:.2f} | 热度: {heat:,}")
            else:
                self.logger.debug("🌍 当前没有达到兴趣阈值的话题")
            
            # 🔥 新增：更新缓存和时间戳
            self.cached_hot_topics = enhanced_topics
            self.last_hot_topics_time = current_time
            self.last_hot_topics_call_time = current_time
            
            return enhanced_topics
            
        except Exception as e:
            self.logger.error(f"🌍 获取热搜数据失败: {e}")
            import traceback
            self.logger.error(f"🌍 热搜获取错误详情: {traceback.format_exc()}")
            return []
    
    def _categorize_topic(self, title: str) -> str:
        """分类话题"""
        title_lower = title.lower()
        
        category_keywords = {
            '科技': ['科技', '技术', '数字', '互联网', 'AI', '人工智能'],
            '文化': ['文化', '艺术', '音乐', '电影', '文学', '传统'],
            '社会': ['社会', '民生', '教育', '就业', '房价', '政策'],
            '娱乐': ['娱乐', '明星', '综艺', '电视剧', '游戏'],
            '体育': ['体育', '运动', '比赛', '奥运', '足球', '篮球'],
            '健康': ['健康', '医疗', '养生', '疫情', '病毒'],
            '经济': ['经济', '金融', '股市', '投资', '创业', '就业']
        }
        
        for category, keywords in category_keywords.items():
            if any(keyword in title_lower for keyword in keywords):
                return category
        
        return '其他'
    
    def _analyze_emotion_tone(self, title: str) -> str:
        """分析情感基调"""
        title_lower = title.lower()
        
        for emotion, keywords in self.perception_config['emotion_keywords'].items():
            if any(keyword in title_lower for keyword in keywords):
                return emotion
        
        # 简单的情感分析
        positive_words = ['好', '成功', '突破', '发展', '进步', '美好']
        negative_words = ['坏', '失败', '问题', '危机', '下降', '困难']
        
        if any(word in title_lower for word in positive_words):
            return '积极'
        elif any(word in title_lower for word in negative_words):
            return '消极'
        else:
            return '中性'
    
    def _calculate_yanran_interest(self, title: str) -> float:
        """计算林嫣然的兴趣程度"""
        title_lower = title.lower()
        interest_score = 0.0
        
        # 基于兴趣关键词
        for keyword in self.perception_config['interest_keywords']:
            if keyword in title_lower:
                interest_score += 0.2
        
        # 基于情感基调
        emotion_tone = self._analyze_emotion_tone(title)
        if emotion_tone == '温暖':
            interest_score += 0.3
        elif emotion_tone == '关注':
            interest_score += 0.2
        elif emotion_tone == '积极':
            interest_score += 0.1
        
        # 基于话题类别
        category = self._categorize_topic(title)
        category_interest = {
            '文化': 0.3,
            '科技': 0.2,
            '社会': 0.2,
            '健康': 0.2,
            '经济': 0.1,
            '娱乐': 0.1
        }
        interest_score += category_interest.get(category, 0.0)
        
        return min(1.0, interest_score)
    
    async def _identify_significant_events(self, perception_data: List[Dict[str, Any]], ai_decision: Dict[str, Any]) -> List[Dict[str, Any]]:
        """🔥 P0级别修复：使用AI智能识别事件重要性，避免机械化关键词匹配"""
        significant_events = []
        
        # 🔥 新增：空感知计数器
        if not hasattr(self, '_empty_perception_count'):
            self._empty_perception_count = 0
        
        try:
            self.logger.debug(f"🌍 📋 开始AI智能识别事件重要性，共{len(perception_data)}个事件")
            
            # 🔥 关键修复：使用AI服务批量智能识别事件性质
            if not perception_data:
                self.logger.debug("🌍 📋 感知数据为空，跳过事件识别")
                return []
            
            # 🔥 减小批处理大小，避免AI响应被截断
            batch_size = 4  # 从8改为4，减少单次处理的数据量
            total_batches = (len(perception_data) + batch_size - 1) // batch_size
            self.logger.debug(f"🌍 📋 将分 {total_batches} 批次处理，每批 {batch_size} 个事件")
            
            for i in range(0, len(perception_data), batch_size):
                batch_topics = perception_data[i:i+batch_size]
                current_batch = (i // batch_size) + 1
                
                self.logger.debug(f"🌍 📋 处理第 {current_batch}/{total_batches} 批次，包含 {len(batch_topics)} 个事件")
                
                batch_results = await self._ai_evaluate_events_batch(batch_topics)
                self.logger.debug(f"🌍 📋 AI评估完成，返回 {len(batch_results)} 个结果")
                
                for j, topic in enumerate(batch_topics):
                    topic_title = topic.get('title', '未知事件')
                    
                    if j < len(batch_results):
                        ai_evaluation = batch_results[j]
                        
                        # 基于AI评估结果决定是否为重要事件
                        is_significant = ai_evaluation.get('is_significant', False)
                        significance_score = ai_evaluation.get('significance_score', 0)
                        yanran_interest = ai_evaluation.get('yanran_interest_level', 0)
                        discussion_value = ai_evaluation.get('discussion_value', 0)
                        
                        self.logger.debug(f"🌍 📋   事件: {topic_title[:30]}...")
                        self.logger.debug(f"🌍 📋   重要性: {significance_score:.2f}, 兴趣度: {yanran_interest:.2f}, 讨论价值: {discussion_value:.2f}")
                        self.logger.debug(f"🌍 📋   AI判断: {'显著' if is_significant else '不显著'}")
                        
                        if is_significant:
                            # 🔥 老王修复：确保重要性分数和阈值都是数字类型
                            threshold = self.perception_config['significance_threshold']
                            try:
                                significance_score = float(significance_score) if significance_score is not None else 0.0
                                threshold = float(threshold) if threshold is not None else 0.5
                            except (ValueError, TypeError):
                                significance_score = 0.0
                                threshold = 0.5

                            if significance_score >= threshold:
                                event = {
                                    'title': topic_title,
                                    'platform': topic.get('platform', 'unknown'),
                                    'significance_score': significance_score,
                                    'event_type': topic.get('category', '其他'),
                                    'rank': topic.get('rank', 999) if topic.get('rank', 999) != 999 else None,
                                    'heat': topic.get('heat', 0),
                                    'url': topic.get('url', ''),
                                    'desc': topic.get('desc', topic.get('content', ''))[:200],
                                    # 🔥 移除虚假观点生成 - 保持真实性
                                    'significance_reasons': ai_evaluation.get('reasons', []),
                                    'yanran_interest_level': yanran_interest,
                                    'discussion_value': discussion_value,
                                    'evaluation_method': 'ai_intelligent',
                                    'timestamp': datetime.now().isoformat()
                                }
                                significant_events.append(event)
                                self.logger.debug(f"🌍 📋   ✅ 识别为重要事件: {topic_title[:30]}... (重要性: {significance_score:.2f})")
                            else:
                                self.logger.debug(f"🌍 📋   ❌ 重要性 {significance_score:.2f} 未达阈值 {threshold}")
                        else:
                            avoid_reason = ai_evaluation.get('avoid_reason', '未达到重要性标准')
                            self.logger.debug(f"🌍 📋   ❌ 跳过事件: {avoid_reason}")
                    else:
                        self.logger.warning(f"🌍 📋   ⚠️ 事件 {topic_title[:30]}... 缺少AI评估结果，使用兜底方法")
                        # 兜底：使用传统方法
                        fallback_result = await self._fallback_evaluate_single_event(topic)
                        if fallback_result:
                            significant_events.append(fallback_result)
                            self.logger.debug(f"🌍 📋   ✅ 兜底方法识别: {topic_title[:30]}...")
            
            # 🔥 更新空感知计数器
            if significant_events:
                self._empty_perception_count = 0  # 重置计数器
            else:
                self._empty_perception_count += 1
            
            # 按重要性排序
            significant_events.sort(key=lambda x: x['significance_score'], reverse=True)
            
            # 🔥 确保至少返回一些内容（如果有数据的话）
            if not significant_events and perception_data and self._empty_perception_count >= self.perception_config['max_empty_cycles']:
                # 选择前2个作为兜底内容
                for topic in perception_data[:2]:
                    fallback_event = {
                        'title': topic.get('title', '未知事件'),
                        'platform': topic.get('platform', 'unknown'),
                        'significance_score': 0.1,  # 兜底评分
                        'event_type': topic.get('category', '其他'),
                        'rank': topic.get('rank'),
                        'heat': topic.get('heat', 0),
                        'url': topic.get('url', ''),
                        'desc': topic.get('desc', topic.get('content', ''))[:200],
                        # 🔥 移除虚假观点生成 - 保持真实性
                        'significance_reasons': ['兜底选择'],
                        'evaluation_method': 'fallback_forced',
                        'timestamp': datetime.now().isoformat()
                    }
                    significant_events.append(fallback_event)
            
            self.logger.debug(f"🌍 AI智能识别完成：从{len(perception_data)}条数据中识别出{len(significant_events)}个重要事件")
            
            # 记录识别结果详情
            if significant_events:
                self.logger.debug("🌍 重要事件详情:")
                for i, event in enumerate(significant_events[:3], 1):
                    title = event['title']
                    score = event['significance_score']
                    interest = event.get('yanran_interest_level', 0)
                    reasons = ', '.join(event.get('significance_reasons', []))
                    self.logger.debug(f"🌍   {i}. {title[:40]}... (重要性:{score:.2f}, 兴趣度:{interest:.2f}, 原因:{reasons})")
            
            return significant_events[:10]  # 最多返回10个重要事件
            
        except Exception as e:
            self.logger.error(f"🌍 AI智能识别事件失败: {e}")
            # 兜底：使用传统方法
            return await self._fallback_identify_significant_events(perception_data)
    
    async def _ai_evaluate_events_batch(self, topics_batch: List[Dict]) -> List[Dict]:
        """使用AI批量评估事件重要性 - 🔥 修复AI评估结果提取问题"""
        try:
            self.logger.debug(f"🌍 📋 AI批量评估开始，事件数量: {len(topics_batch)}")
            
            # 构建AI评估提示词
            evaluation_prompt = self._build_event_evaluation_prompt(topics_batch)
            
            # 使用AI决策引擎评估
            ai_result = await self.ai_decision_engine.decide(
                organ_prompt=evaluation_prompt,
                context={
                    'topics_batch': topics_batch,
                    'yanran_interests': self.perception_config['interest_keywords'],
                    'evaluation_criteria': {
                        'avoid_political': True,
                        'prefer_topics': ['科技', '财经', '人工智能', '文学', '军事', '写作', 'AI'],
                        'avoid_topics': ['政治', '政府', '官员', '政策争议', '政治人物']
                    }
                },
                decision_type="event_significance_evaluation"
            )
            
            self.logger.debug(f"🌍 📋 AI决策结果: should_act={ai_result.get('should_act') if ai_result else None}")
            
            # 🔥 关键修复：多层级解析AI评估结果
            evaluations = []
            
            if ai_result and ai_result.get('should_act'):
                # 尝试从多个可能的字段中提取评估结果
                potential_evaluations = (
                    ai_result.get('evaluations') or 
                    ai_result.get('actions') or 
                    ai_result.get('reasoning') or
                    ai_result.raw_decision.get('evaluations') if hasattr(ai_result, 'raw_decision') else None
                )
                
                self.logger.debug(f"🌍 📋 潜在评估数据类型: {type(potential_evaluations)}")
                
                if isinstance(potential_evaluations, list):
                    evaluations = potential_evaluations
                    self.logger.debug(f"🌍 📋 直接获取到评估列表，长度: {len(evaluations)}")
                elif isinstance(potential_evaluations, str):
                    # 尝试从字符串中解析JSON
                    try:
                        import json
                        if '{' in potential_evaluations and '}' in potential_evaluations:
                            json_start = potential_evaluations.find('{')
                            json_end = potential_evaluations.rfind('}') + 1
                            json_str = potential_evaluations[json_start:json_end]
                            parsed_data = json.loads(json_str)
                            evaluations = parsed_data.get('evaluations', [])
                            self.logger.debug(f"🌍 📋 从字符串解析JSON成功，评估数量: {len(evaluations)}")
                    except Exception as parse_error:
                        self.logger.debug(f"🌍 📋 字符串解析失败: {parse_error}")
                elif isinstance(potential_evaluations, dict):
                    # 如果是字典，可能直接包含评估数据
                    if 'evaluations' in potential_evaluations:
                        evaluations = potential_evaluations['evaluations']
                        self.logger.debug(f"🌍 📋 从字典中提取评估数据，数量: {len(evaluations)}")
                    else:
                        # 尝试将单个字典转为列表
                        evaluations = [potential_evaluations]
                        self.logger.debug("🌍 📋 将单个字典转为评估列表")
                
                # 🔥 新增：从ai_result的其他字段中查找评估数据
                if not evaluations and hasattr(ai_result, 'raw_decision'):
                    raw_decision = ai_result.raw_decision
                    if isinstance(raw_decision, dict):
                        for key in ['evaluations', 'event_evaluations', 'results', 'analysis']:
                            if key in raw_decision and isinstance(raw_decision[key], list):
                                evaluations = raw_decision[key]
                                self.logger.debug(f"🌍 📋 从raw_decision.{key}中找到评估数据，数量: {len(evaluations)}")
                                break
            
            # 🔥 验证和标准化评估结果，处理数量不匹配问题
            if evaluations:
                standardized_evaluations = []
                
                # 🔥 处理评估结果数量不匹配的情况
                for i in range(len(topics_batch)):
                    if i < len(evaluations) and isinstance(evaluations[i], dict):
                        # 使用AI评估结果
                        evaluation = evaluations[i]
                        standardized_eval = {
                            'is_significant': evaluation.get('is_significant', False),
                            'significance_score': float(evaluation.get('significance_score', 0.3)),
                            'yanran_interest_level': float(evaluation.get('yanran_interest_level', 0.3)),
                            'discussion_value': float(evaluation.get('discussion_value', 0.3)),
                            'reasons': evaluation.get('reasons', []),
                            # 🔥 移除虚假观点生成 - 保持真实性
                            'avoid_reason': evaluation.get('avoid_reason', '')
                        }
                        standardized_evaluations.append(standardized_eval)
                        self.logger.debug(f"🌍 📋 事件{i+1}: 使用AI评估结果")
                    else:
                        # 🔥 对缺失的评估结果，生成默认评估
                        topic_title = topics_batch[i].get('title', '未知事件')
                        self.logger.warning(f"🌍 📋 事件{i+1}({topic_title[:20]}...): AI评估缺失，使用默认评估")
                        
                        # 基于事件标题和内容的简单评估
                        title = topics_batch[i].get('title', '')
                        interest_score = 0.3
                        
                        # 简单关键词匹配
                        interest_keywords = self.perception_config.get('interest_keywords', [])
                        for keyword in interest_keywords:
                            if keyword in title:
                                interest_score = 0.6
                                break
                        
                        standardized_eval = {
                            'is_significant': interest_score >= 0.5,
                            'significance_score': interest_score,
                            'yanran_interest_level': interest_score,
                            'discussion_value': interest_score,
                            'reasons': ['AI评估缺失，使用兜底评估'],
                            # 🔥 移除虚假观点生成 - 保持真实性
                            'avoid_reason': '无'
                        }
                        standardized_evaluations.append(standardized_eval)
                
                if len(evaluations) != len(topics_batch):
                    self.logger.warning(f"🌍 📋 AI评估结果数量不匹配: 期望{len(topics_batch)}个，实际{len(evaluations)}个，已补齐")
                else:
                    self.logger.debug(f"🌍 📋 AI评估结果数量匹配: {len(evaluations)}个")
                
                self.logger.debug(f"🌍 📋 AI评估成功，返回 {len(standardized_evaluations)} 个标准化结果")
                return standardized_evaluations
            else:
                self.logger.warning(f"🌍 📋 AI评估结果为空，期望{len(topics_batch)}个")
            
            # 🔥 兜底：为每个事件返回默认评估
            self.logger.debug(f"🌍 📋 使用兜底评估，为 {len(topics_batch)} 个事件生成默认结果")
            return [
                {
                    'is_significant': False, 
                    'significance_score': 0.3,
                    'yanran_interest_level': 0.3,
                    'discussion_value': 0.3,
                    'reasons': ['AI评估暂不可用'],
                    # 🔥 移除虚假观点生成 - 保持真实性
                    'avoid_reason': 'AI评估系统暂时不可用'
                } 
                for _ in topics_batch
            ]
            
        except Exception as e:
            self.logger.error(f"🌍 AI批量评估失败: {e}")
            import traceback
            self.logger.error(f"🌍 AI批量评估异常详情: {traceback.format_exc()}")
            
            # 异常兜底：返回默认评估
            return [
                {
                    'is_significant': False, 
                    'significance_score': 0.3,
                    'yanran_interest_level': 0.3,
                    'discussion_value': 0.3,
                    'reasons': [f'AI评估异常: {str(e)}'],
                    # 🔥 移除虚假观点生成 - 保持真实性
                    'avoid_reason': 'AI评估系统异常'
                } 
                for _ in topics_batch
            ]
    
    def _build_event_evaluation_prompt(self, topics_batch: List[Dict]) -> str:
        """构建事件评估提示词"""
        prompt = """你是林嫣然的世界感知器官，需要智能识别哪些事件值得关注和讨论。

林嫣然的兴趣偏好：
- 喜欢：科技创新、财经动态、人工智能、文学艺术、军事科技、写作技巧、AI发展
- 避免：政治争议、政府政策、官员相关、敏感政治话题

评估标准：
1. 事件重要性 (0-1分)：对社会、科技、经济的影响程度
2. 林嫣然兴趣度 (0-1分)：符合林嫣然兴趣偏好的程度
3. 讨论价值 (0-1分)：是否适合与用户讨论交流
4. 综合重要性 = (事件重要性 * 0.4 + 兴趣度 * 0.4 + 讨论价值 * 0.2)

请对以下事件进行评估：

"""
        
        for i, topic in enumerate(topics_batch, 1):
            title = topic.get('title', '未知事件')
            desc = topic.get('desc', topic.get('content', ''))[:100]
            platform = topic.get('platform', 'unknown')
            prompt += f"{i}. 标题：{title}\n   描述：{desc}\n   来源：{platform}\n\n"
        
        prompt += """
请为每个事件返回JSON格式的评估结果：
{
  "decision_type": "event_significance_evaluation",
  "should_act": true,
  "confidence": 0.8,
  "reasoning": "基于林嫣然的兴趣偏好和事件重要性进行评估",
  "evaluations": [
    {
      "event_index": 1,
      "is_significant": true/false,
      "significance_score": 0.0-1.0,
      "yanran_interest_level": 0.0-1.0,
      "discussion_value": 0.0-1.0,
      "reasons": ["原因1", "原因2"],
      // 🔥 已移除虚假观点字段 - 保持真实性
      "avoid_reason": "如果不推荐，说明原因"
    }
  ],
  "actions": ["识别重要事件", "生成评估报告"],
  "emotion_impact": {
    "user_emotion": "中性",
    "self_emotion": "专注"
  },
  "priority": "normal"
}

🔥 重要提醒：
1. 必须严格按照上述JSON格式输出
2. 确保所有字段都包含在内
3. evaluations数组的长度必须与输入事件数量一致
4. 不要在JSON外添加任何解释文字
5. 确保JSON语法正确，所有括号和引号都正确闭合
"""
        
        return prompt
    
    async def _fallback_evaluate_single_event(self, topic: Dict) -> Optional[Dict]:
        """兜底方法：评估单个事件"""
        try:
            title = topic.get('title', '')
            yanran_interest_score = topic.get('yanran_interest_score', 0)
            rank = topic.get('rank', 999)
            
            # 使用传统方法计算重要性
            is_significant = False
            significance_reasons = []
            
            # 条件1：林嫣然兴趣度评估
            if yanran_interest_score >= self.perception_config['significance_threshold']:
                is_significant = True
                significance_reasons.append(f"兴趣匹配({yanran_interest_score:.2f})")
            
            # 条件2：热搜排名
            if rank != 999 and rank <= 10:
                is_significant = True
                significance_reasons.append(f"热搜排名({rank})")
            
            # 条件3：关键词匹配
            for keyword in self.perception_config['interest_keywords']:
                if keyword in title:
                    is_significant = True
                    significance_reasons.append(f"关键词匹配({keyword})")
                    break
            
            if is_significant:
                return {
                    'title': title,
                    'platform': topic.get('platform', 'unknown'),
                    'significance_score': yanran_interest_score,
                    'event_type': topic.get('category', '其他'),
                    'rank': rank if rank != 999 else None,
                    'heat': topic.get('heat', 0),
                    'url': topic.get('url', ''),
                    'desc': topic.get('desc', topic.get('content', ''))[:200],
                    # 🔥 移除虚假观点生成 - 保持真实性
                    'significance_reasons': significance_reasons,
                    'evaluation_method': 'fallback_traditional',
                    'timestamp': datetime.now().isoformat()
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"🌍 兜底评估单个事件失败: {e}")
            return None
    
    async def _fallback_identify_significant_events(self, perception_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """兜底方法：使用传统关键词匹配识别重要事件"""
        significant_events = []
        
        try:
            self.logger.debug("🌍 使用兜底方法识别重要事件")
            
            for topic in perception_data:
                fallback_result = await self._fallback_evaluate_single_event(topic)
                if fallback_result:
                    significant_events.append(fallback_result)
            
            # 按重要性排序
            significant_events.sort(key=lambda x: x['significance_score'], reverse=True)
            
            self.logger.debug(f"🌍 兜底方法识别到{len(significant_events)}个重要事件")
            return significant_events[:10]
            
        except Exception as e:
            self.logger.error(f"🌍 兜底识别重要事件失败: {e}")
            return []
    
    async def _generate_proactive_thoughts(self, significant_events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成主动思考"""
        proactive_thoughts = []
        
        for event in significant_events:
            # 构建思考上下文
            thought_context = {
                'significant_event': event,
                'yanran_personality': {
                    'traits': ['温暖', '智慧', '独立思考', '人文关怀'],
                    'interests': self.perception_config['interest_keywords'],
                    'values': ['真诚', '理解', '成长', '美好']
                },
                'world_context': self.current_world_state
            }
            
            # 使用AI生成主动思考
            thought_decision = await self.ai_decision_engine.decide(
                organ_prompt=f"""
                基于这个重要事件，林嫣然会产生什么样的思考和感悟？
                
                事件信息：{event}
                
                请考虑：
                1. 林嫣然的人格特质和价值观
                2. 这个事件对她的意义
                3. 她可能的情感反应
                4. 她的表达方式会是什么样的
                """,
                context=thought_context,
                decision_type='proactive_expression'
            )
            
            if thought_decision.should_act:
                proactive_thoughts.append({
                    'event': event.get('title', '未知事件'),  # 修复：使用title字段而不是topic
                    'event_platform': event.get('platform', 'unknown'),
                    'event_category': event.get('event_type', '其他'),
                    'thought': thought_decision.get('yanran_perspective', ''),
                    'expression_intent': thought_decision.get('actions', []),
                    'emotion': thought_decision.get('emotion_impact', {}),
                    'confidence': thought_decision.confidence,
                    'trigger_event_details': {
                        'title': event.get('title', ''),
                        'platform': event.get('platform', ''),
                        'significance_score': event.get('significance_score', 0),
                        'rank': event.get('rank'),
                        'heat': event.get('heat', 0)
                    }
                })
        
        return proactive_thoughts
    
    async def _execute_significance_evaluation(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行重要性评估"""
        return {
            'success': True,
            'evaluation_result': ai_decision,
            'is_significant': ai_decision.get('should_act', False),
            'confidence': ai_decision.confidence
        }
    
    async def _execute_proactive_expression(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行主动表达"""
        return {
            'success': True,
            'expression_generated': ai_decision.get('should_act', False),
            'expression_content': ai_decision.get('yanran_perspective', ''),
            'expression_actions': ai_decision.get('actions', []),
            'emotion_impact': ai_decision.get('emotion_impact', {})
        }
    
    async def _execute_general_perception(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行通用感知"""
        return {
            'success': True,
            'perception_result': ai_decision,
            'should_continue': ai_decision.get('should_act', True)
        }
    
    def _start_continuous_perception(self):
        """启动持续感知"""
        async def perception_loop():
            while True:
                try:
                    await self._perform_scheduled_perception()
                    await asyncio.sleep(self.perception_config['update_interval'])
                except Exception as e:
                    self.logger.error(f"定时感知失败: {e}")
                    await asyncio.sleep(60)  # 出错时等待1分钟再试
        
        # 安全启动感知循环
        try:
            # 检查是否有运行中的事件循环
            try:
                loop = asyncio.get_running_loop()
                # 在运行中的事件循环中创建任务
                loop.create_task(perception_loop())
                self.logger.debug("🌍 持续感知已在当前事件循环中启动")
            except RuntimeError:
                # 没有运行中的事件循环，延迟启动
                self.logger.debug("🌍 当前无事件循环，持续感知将在系统启动后自动开始")
                # 将启动函数保存，等待系统启动后调用
                self._perception_loop_func = perception_loop
        except Exception as e:
            self.logger.warning(f"🌍 启动持续感知失败: {e}，将在需要时手动触发感知")
    
    async def _perform_scheduled_perception(self):
        """执行定时感知"""
        try:
            self.logger.debug("🌍 开始执行定时世界感知...")
            
            # 构建感知上下文
            perception_context = {
                'last_perception': self.last_perception_time,
                'current_world_state': self.current_world_state,
                'perception_patterns': self.perception_patterns
            }
            
            self.logger.debug(f"🌍 感知上下文构建完成，上次感知时间: {self.last_perception_time}")
            
            # 执行感知
            self.logger.debug("🌍 开始AI决策感知...")
            result = await self.process_with_ai_decision(
                input_data={'type': 'scheduled_perception'},
                context=perception_context
            )
            
            self.last_perception_time = datetime.now()
            self.logger.debug(f"🌍 感知完成，更新感知时间: {self.last_perception_time}")
            
            # 记录感知结果详情
            if result:
                # 从器官处理结果中提取实际的执行结果
                execution_result = result.get('result', {})
                ai_decision = result.get('ai_decision', {})
                confidence = result.get('confidence', 0)
                
                self.logger.debug(f"🌍 AI决策完成 - 信心度: {confidence:.2f}, 决策: {ai_decision.get('should_act', False)}")
                
                # 检查执行结果是否成功
                if execution_result and execution_result.get('success'):
                    significant_events = execution_result.get('significant_events', [])
                    world_state = execution_result.get('world_state', {})
                    proactive_thoughts = execution_result.get('proactive_thoughts', [])
                    
                    self.logger.debug(f"🌍 感知结果 - 重要事件: {len(significant_events)}个, 主动思考: {len(proactive_thoughts)}个")
                    
                    if significant_events:
                        self.logger.debug("🌍 重要事件列表:")
                        for i, event in enumerate(significant_events[:3], 1):  # 只显示前3个
                            topic = event.get('topic', {})
                            title = topic.get('title', '未知事件')
                            score = event.get('significance_score', 0)
                            self.logger.debug(f"🌍   {i}. {title} (重要性: {score:.2f})")
                    
                    if proactive_thoughts:
                        self.logger.debug("🌍 主动思考内容:")
                        for i, thought in enumerate(proactive_thoughts[:2], 1):  # 只显示前2个
                            thought_content = thought.get('thought', '')
                            confidence = thought.get('confidence', 0)
                            self.logger.debug(f"🌍   {i}. {thought_content[:50]}... (置信度: {confidence:.2f})")
                    
                    # 如果有重要发现，通知其他器官
                    if significant_events:
                        self.logger.debug("🌍 检测到重要事件，开始通知其他器官...")
                        await self._notify_other_organs(execution_result)
                        self.logger.debug("🌍 器官通知完成")
                    else:
                        self.logger.debug("🌍 本次感知未发现重要事件，但系统运行正常")
                        # 🔥 增加详细信息，帮助调试
                        hot_topics_count = execution_result.get('hot_topics_count', 0)
                        news_count = execution_result.get('news_count', 0)
                        world_state = execution_result.get('world_state', {})
                        if hot_topics_count > 0 or news_count > 0:
                            self.logger.debug(f"🌍 数据获取情况：热搜{hot_topics_count}条，新闻{news_count}条")
                            perception_summary = world_state.get('perception_summary', '')
                            if perception_summary:
                                self.logger.debug(f"🌍 感知摘要：{perception_summary}")
                        else:
                            self.logger.warning("🌍 未获取到任何外部数据，请检查数据源连接")
                else:
                    # 即使执行结果不成功，也要记录AI决策信息
                    should_act = ai_decision.get('should_act', False)
                    reasoning = ai_decision.get('reasoning', '无推理信息')
                    is_fallback = ai_decision.get('is_fallback', False)
                    
                    if is_fallback:
                        self.logger.debug(f"🌍 使用兜底决策 - 是否行动: {should_act}")
                        self.logger.debug(f"🌍 兜底推理: {reasoning[:100]}...")
                    else:
                        self.logger.warning(f"🌍 感知执行未成功，但AI决策正常 - 是否行动: {should_act}")
                        self.logger.debug(f"🌍 决策推理: {reasoning[:100]}...")
            else:
                self.logger.warning("🌍 感知执行失败或无结果")
            
        except Exception as e:
            self.logger.error(f"🌍 定时感知执行失败: {e}")
            import traceback
            self.logger.debug(f"🌍 感知错误详情: {traceback.format_exc()}")
    
    async def _notify_other_organs(self, perception_result: Dict[str, Any]):
        """通知其他器官"""
        notification_data = {
            'type': 'world_perception_update',
            'significant_events': perception_result.get('significant_events', []),
            'world_state': perception_result.get('world_state', {}),
            'proactive_thoughts': perception_result.get('proactive_thoughts', []),
            'timestamp': datetime.now().isoformat()
        }
        
        # 🔥 P0级别修复：直接触发主动表达，避免感知反馈处理器的单例问题
        significant_events = perception_result.get('significant_events', [])
        if significant_events:
            try:
                self.logger.debug(f"🌍 检测到{len(significant_events)}个重要事件，直接触发主动表达")
                
                # 直接获取主动表达器官并触发
                from cognitive_modules.organs.proactive_expression_organ import get_instance as get_proactive_organ
                proactive_organ = get_proactive_organ()
                
                if proactive_organ and hasattr(proactive_organ, 'trigger_expression_from_perception'):
                    # 🔥 老王修复：传递当前触发的事件到主动表达器官
                    if hasattr(proactive_organ, '_current_trigger_events'):
                        proactive_organ._current_trigger_events = significant_events
                        self.logger.debug(f"🌍 📤 已传递 {len(significant_events)} 个触发事件到主动表达器官")

                    # 🔥 直接调用主动表达触发
                    await proactive_organ.trigger_expression_from_perception(notification_data)
                    self.logger.debug(f"🌍 ✅ 主动表达触发成功：{len(significant_events)}个重要事件")

                    # 记录触发的事件详情
                    for i, event in enumerate(significant_events[:2], 1):
                        title = event.get('title', '未知事件')
                        score = event.get('significance_score', 0)
                        try:
                            safe_score = float(score) if score is not None else 0.0
                        except (ValueError, TypeError):
                            safe_score = 0.0
                        self.logger.debug(f"🌍   触发事件{i}: {title[:40]}... (重要性: {safe_score:.2f})")
                else:
                    self.logger.warning("🌍 主动表达器官不可用，无法触发主动表达")
                    
            except Exception as e:
                self.logger.error(f"🌍 直接触发主动表达失败: {e}")
                import traceback
                self.logger.error(f"🌍 触发错误详情: {traceback.format_exc()}")
        
        # 🔥 兜底：发送感知结果到感知反馈处理器（作为备用机制）
        try:
            # 导入感知反馈处理器
            from core.perception_feedback_processor import get_perception_feedback_processor, PerceptionPriority
            
            processor = get_perception_feedback_processor()
            
            # 判断优先级
            priority = PerceptionPriority.NORMAL
            if significant_events:
                if len(significant_events) >= 3:  # 有多个重要事件
                    priority = PerceptionPriority.HIGH
                elif any('紧急' in str(event) or '重要' in str(event) for event in significant_events):
                    priority = PerceptionPriority.URGENT
            
            # 添加感知结果到处理器
            processor.add_perception_result(
                source_organ="世界感知器官",
                perception_type="world_perception_update",
                content=notification_data,
                priority=priority
            )
            
            self.logger.debug(f"感知结果已发送到反馈处理器 (优先级: {priority.name}) - 备用机制")
            
        except Exception as e:
            self.logger.warning(f"发送感知结果到反馈处理器失败: {e}")
        
        # 通知所有连接的器官
        for organ_name, connection in self.neural_connections.items():
            try:
                await self.send_signal_to_organ(organ_name, notification_data)
            except Exception as e:
                self.logger.warning(f"通知器官 {organ_name} 失败: {e}")
    
    # 公共接口方法
    async def get_current_world_state(self) -> Dict[str, Any]:
        """获取当前世界状态"""
        return self.current_world_state
    
    async def get_significant_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取重要事件"""
        return self.significant_events[:limit]

    def _update_significant_events_with_dedup(self, new_events: List[Dict[str, Any]]):
        """🔥 老王新增：更新重要事件列表，带去重功能 - 使用Redis缓存"""
        try:
            # 从Redis获取当前事件列表和已处理ID集合
            significant_events = self.redis_cache.get("significant_events", namespace="world_perception") or []
            processed_event_ids = set(self.redis_cache.get("processed_event_ids", namespace="world_perception") or [])

            for event in new_events:
                # 生成事件唯一标识
                event_id = self._generate_event_unique_id(event)

                # 如果事件未处理过，添加到列表
                if event_id not in processed_event_ids:
                    # 添加事件ID到事件数据中
                    event['_unique_id'] = event_id
                    event['_added_time'] = datetime.now().isoformat()

                    significant_events.append(event)
                    processed_event_ids.add(event_id)

                    self.logger.debug(f"🌍 📝 新增重要事件: {event.get('title', '未知')[:30]}...")
                else:
                    self.logger.debug(f"🌍 🔄 跳过重复事件: {event.get('title', '未知')[:30]}...")

            # 保持最近100个事件
            if len(significant_events) > 100:
                # 移除最旧的事件，同时清理processed_event_ids
                removed_events = significant_events[:-100]
                for removed_event in removed_events:
                    removed_id = removed_event.get('_unique_id')
                    if removed_id and removed_id in processed_event_ids:
                        processed_event_ids.discard(removed_id)

                significant_events = significant_events[-100:]
                self.logger.debug(f"🌍 🧹 清理旧事件，保持最近100个，当前事件数: {len(significant_events)}")

            # 保存到Redis，TTL设置为24小时
            self.redis_cache.set("significant_events", significant_events, ttl=24*3600, namespace="world_perception")
            self.redis_cache.set("processed_event_ids", list(processed_event_ids), ttl=24*3600, namespace="world_perception")

            # 更新内存中的引用（向后兼容）
            self.significant_events = significant_events

        except Exception as e:
            self.logger.error(f"🌍 更新重要事件失败: {e}")

    def _generate_event_unique_id(self, event: Dict[str, Any]) -> str:
        """🔥 老王新增：生成事件唯一标识"""
        import hashlib

        # 使用标题+平台+日期生成唯一ID
        title = event.get('title', '')
        platform = event.get('platform', event.get('source_platform', ''))

        # 使用当天日期，这样同一天的相同事件会被去重
        today = datetime.now().strftime('%Y%m%d')

        content = f"{title}_{platform}_{today}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]

    def _update_cached_significant_events(self):
        """🔥 老王新增：更新缓存的重要事件，供主动表达使用 - 使用Redis缓存"""
        try:
            # 从Redis获取重要事件列表
            significant_events = self.redis_cache.get("significant_events", namespace="world_perception") or []

            # 🔥 老王修复：按时间优先排序 - 越靠近当前时间的优先级越高
            sorted_events = sorted(
                significant_events,
                key=lambda x: (
                    x.get('_added_time', '2020-01-01'),  # 时间优先（最新的在前）
                    x.get('significance_score', 0)       # 重要性分数其次
                ),
                reverse=True  # 降序排列：最新时间 + 最高分数
            )

            # 过滤出最近6小时内的高质量事件
            current_time = datetime.now()
            fresh_events = []

            for event in sorted_events:
                try:
                    added_time_str = event.get('_added_time', '')
                    if added_time_str:
                        added_time = datetime.fromisoformat(added_time_str)
                        time_diff = current_time - added_time

                        # 🔥 老王修复：确保重要性分数类型转换
                        significance_score = event.get('significance_score', 0)
                        try:
                            significance_score = float(significance_score) if significance_score is not None else 0.0
                        except (ValueError, TypeError):
                            significance_score = 0.0

                        # 6小时内的事件 + 重要性分数 >= 0.3
                        if time_diff.total_seconds() <= 21600 and significance_score >= 0.3:
                            fresh_events.append(event)
                    else:
                        # 没有时间戳的事件，如果重要性足够高也包含
                        significance_score = event.get('significance_score', 0)
                        try:
                            significance_score = float(significance_score) if significance_score is not None else 0.0
                        except (ValueError, TypeError):
                            significance_score = 0.0

                        if significance_score >= 0.6:
                            fresh_events.append(event)

                except Exception as e:
                    self.logger.debug(f"🌍 处理事件时间失败: {e}")
                    continue

            # 更新Redis缓存，最多保留10个最重要的新鲜事件
            cached_events = fresh_events[:10]
            self.redis_cache.set("cached_significant_events", cached_events, ttl=6*3600, namespace="world_perception")
            self.last_cache_update = current_time

            self.logger.debug(f"🌍 💾 缓存更新完成: {len(cached_events)} 个新鲜重要事件")

            # 记录缓存的事件详情
            for i, event in enumerate(cached_events[:3], 1):
                title = event.get('title', '未知事件')
                score = event.get('significance_score', 0)
                self.logger.debug(f"🌍   缓存事件{i}: {title[:40]}... (重要性: {score:.2f})")

            # 向后兼容：更新内存中的引用
            self.cached_significant_events = cached_events

        except Exception as e:
            self.logger.error(f"🌍 更新缓存失败: {e}")
            # 兜底：如果更新失败，从Redis获取或使用空列表
            cached_events = self.redis_cache.get("cached_significant_events", namespace="world_perception") or []
            self.cached_significant_events = cached_events
    
    async def evaluate_topic_significance(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """评估话题重要性"""
        context = {
            'topic': topic,
            'yanran_interests': self.perception_config['interest_keywords'],
            'current_world_state': self.current_world_state
        }
        
        return await self.process_with_ai_decision(
            input_data={'type': 'topic_evaluation', 'topic': topic},
            context=context
        )
    
    async def generate_world_summary(self) -> str:
        """生成世界状态摘要"""
        if not self.current_world_state:
            return "暂时还没有感知到外部世界的变化。"
        
        summary_prompt = f"""
        基于当前真实感知到的世界状态数据，生成一个温暖而有洞察力的世界状态摘要。
        
        当前世界状态：
        {json.dumps(self.current_world_state, ensure_ascii=False, indent=2)}
        
        请用林嫣然的语言风格，简洁而有温度地描述你对当前世界的感知和理解。
        """
        
        return await self.ai_decision_engine.generate_response(summary_prompt)

    def _analyze_top_categories(self, significant_events: List[Dict[str, Any]]) -> Dict[str, int]:
        """分析主要事件分类"""
        categories = {}
        for event in significant_events:
            category = event.get('topic', {}).get('category', '未分类')
            categories[category] = categories.get(category, 0) + 1
        return dict(sorted(categories.items(), key=lambda x: x[1], reverse=True))

    def _analyze_emotion_distribution(self, significant_events: List[Dict[str, Any]]) -> Dict[str, int]:
        """分析情感分布"""
        emotions = {}
        for event in significant_events:
            emotion = event.get('topic', {}).get('emotion_tone', '中性')
            emotions[emotion] = emotions.get(emotion, 0) + 1
        return dict(sorted(emotions.items(), key=lambda x: x[1], reverse=True))

    async def load_historical_learning_data(self) -> Dict[str, Any]:
        """🔥 老王修复：加载历史学习数据到Redis缓存，避免内存存储"""
        if not self.data_manager:
            return {}

        try:
            # 🔥 老王修复：首先检查Redis缓存
            cached_events = self.redis_cache.get("significant_events", namespace="world_perception")
            if cached_events and len(cached_events) > 0:
                self.logger.debug(f"🧠 💾 从Redis缓存加载历史数据: {len(cached_events)}个事件")
                # 向后兼容：更新内存引用
                self.significant_events = cached_events
                return {
                    'recent_events_count': len(cached_events),
                    'learning_patterns': {},
                    'memory_loaded': True,
                    'source': 'redis_cache'
                }

            # 🔥 缓存未命中，从数据库加载
            recent_events = self.data_manager.get_recent_significant_events(limit=100, days=30)

            # 获取学习模式
            learning_patterns = self.data_manager.get_learning_patterns()

            # 🔥 老王修复：保存到Redis缓存，不再使用内存存储
            if recent_events:
                # 为每个事件添加时间戳（如果没有的话）
                for event in recent_events:
                    if '_added_time' not in event:
                        # 使用数据库的created_at字段
                        created_at = event.get('created_at')
                        if created_at:
                            event['_added_time'] = created_at.isoformat() if hasattr(created_at, 'isoformat') else str(created_at)
                        else:
                            event['_added_time'] = datetime.now().isoformat()

                # 保存到Redis，TTL设置为24小时
                self.redis_cache.set("significant_events", recent_events, ttl=24*3600, namespace="world_perception")
                self.logger.debug(f"🧠 💾 历史数据已保存到Redis缓存: {len(recent_events)}个事件")

                # 向后兼容：更新内存引用
                self.significant_events = recent_events

            # 分析并更新感知模式
            if learning_patterns:
                self.perception_patterns.update({
                    'category_preferences': learning_patterns.get('category_preferences', []),
                    'emotion_patterns': learning_patterns.get('emotion_patterns', []),
                    'last_analysis': learning_patterns.get('analysis_time', '')
                })

            self.logger.debug(f"🧠 加载历史学习数据: {len(recent_events)}个事件, {len(learning_patterns)}个模式")

            return {
                'recent_events_count': len(recent_events),
                'learning_patterns': learning_patterns,
                'memory_loaded': True,
                'source': 'database'
            }

        except Exception as e:
            self.logger.error(f"❌ 加载历史学习数据失败: {e}")
            return {'memory_loaded': False, 'error': str(e)}

    async def enhance_interest_keywords_from_learning(self):
        """🔥 老王新增：基于学习数据增强兴趣关键词"""
        if not self.data_manager:
            return

        try:
            # 获取学习模式
            patterns = self.data_manager.get_learning_patterns()

            # 从高频分类中提取关键词
            new_keywords = []
            category_prefs = patterns.get('category_preferences', [])

            for cat_data in category_prefs[:5]:  # 取前5个高频分类
                category = cat_data.get('category', '')
                if category and category not in ['未分类', '其他']:
                    new_keywords.append(category)

            # 更新兴趣关键词
            if new_keywords:
                self.perception_config['interest_keywords'].extend(new_keywords)
                # 去重
                self.perception_config['interest_keywords'] = list(set(self.perception_config['interest_keywords']))

                # 保存到数据库
                self.data_manager.update_interest_keywords(new_keywords, "learned_from_patterns")

                self.logger.debug(f"🧠 基于学习增强兴趣关键词: {new_keywords}")

        except Exception as e:
            self.logger.warning(f"⚠️ 增强兴趣关键词失败: {e}")
    
    def get_perception_statistics(self) -> Dict[str, Any]:
        """获取感知统计"""
        return {
            'organ_name': self.organ_name,
            'last_perception_time': self.last_perception_time.isoformat() if self.last_perception_time else None,
            'total_significant_events': len(self.significant_events),
            'perception_config': self.perception_config,
            'world_state_available': bool(self.current_world_state),
            'organ_status': self.get_organ_status()
        }
    
    async def _process_organ_signal(self, source_organ_name: str, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理来自其他器官的信号"""
        signal_type = signal_data.get('type', 'unknown')
        
        if signal_type == 'request_world_state':
            # 其他器官请求世界状态
            return {
                'type': 'world_state_response',
                'world_state': self.current_world_state,
                'significant_events': self.significant_events[-5:],  # 最近5个重要事件
                'timestamp': datetime.now().isoformat()
            }
        elif signal_type == 'interest_update':
            # 其他器官更新兴趣关键词
            new_interests = signal_data.get('interests', [])
            self.perception_config['interest_keywords'].extend(new_interests)
            self.perception_config['interest_keywords'] = list(set(self.perception_config['interest_keywords']))
            
            return {
                'type': 'interest_updated',
                'updated_interests': self.perception_config['interest_keywords'],
                'timestamp': datetime.now().isoformat()
            }
        else:
            return await super()._process_organ_signal(source_organ_name, signal_data)
    
    async def _get_realtime_news_data(self) -> List[Dict[str, Any]]:
        """获取实时新闻数据"""
        try:
            # 🔥 新增：频率控制检查
            current_time = datetime.now()
            
            # 检查新闻专用最小调用间隔
            if self.last_realtime_news_call_time:
                time_since_last_call = (current_time - self.last_realtime_news_call_time).total_seconds()
                if time_since_last_call < self.perception_config['min_call_interval']:
                    self.logger.debug(f"🌍 实时新闻获取间隔过短 ({time_since_last_call:.0f}秒 < {self.perception_config['min_call_interval']}秒)，返回缓存数据")
                    return self.cached_realtime_news
            
            # 检查实时新闻获取间隔
            if self.last_realtime_news_time:
                time_since_last_news = (current_time - self.last_realtime_news_time).total_seconds()
                if time_since_last_news < self.perception_config['realtime_news_interval']:
                    self.logger.debug(f"🌍 实时新闻获取间隔未到 ({time_since_last_news:.0f}秒 < {self.perception_config['realtime_news_interval']}秒)，返回缓存数据")
                    return self.cached_realtime_news
            
            self.logger.debug("🌍 开始获取实时新闻数据...")
            
            # 使用已实现的新闻连接器
            try:
                from connectors.datasource.news_connector import NewsConnector
                news_connector = NewsConnector()
                
                # 获取最近的重要新闻
                news_data, success = news_connector.get_recent_news(days=1, limit=20, importance=0.5)
                
                if success and news_data and isinstance(news_data, list):
                    enhanced_news = []
                    for news in news_data:
                        if not isinstance(news, dict):
                            continue
                            
                        # 安全获取分类信息
                        categories = news.get('categories', [])
                        category = '其他'
                        if categories and isinstance(categories, list) and len(categories) > 0:
                            if isinstance(categories[0], dict) and 'name' in categories[0]:
                                category = categories[0]['name']
                        
                        enhanced_news.append({
                            'id': news.get('id', 0),
                            'title': news.get('title', '无标题'),
                            'content': str(news.get('content', news.get('summary', '')))[:300],  # 限制内容长度
                            'source': news.get('source', '未知来源'),
                            'category': category,
                            'published_at': news.get('published_at', ''),
                            'importance_score': news.get('importance_score', 0.5),
                            'platform': 'realtime_news',
                            'yanran_interest_score': self._calculate_news_interest(
                                news.get('title', ''), 
                                str(news.get('content', ''))
                            ),
                            'timestamp': datetime.now().isoformat()
                        })
                    
                    self.logger.debug(f"🌍 成功获取 {len(enhanced_news)} 条实时新闻数据")
                    # 🔥 新增：更新缓存和时间戳
                    self.cached_realtime_news = enhanced_news
                    self.last_realtime_news_time = current_time
                    self.last_realtime_news_call_time = current_time
                    return enhanced_news
                else:
                    self.logger.warning("🌍 实时新闻API获取失败或无数据")
                    # 🔥 修复：尝试使用其他新闻源作为兜底
                    fallback_news = self._get_fallback_news()
                    if fallback_news:
                        self.logger.debug(f"🌍 使用兜底新闻源获取 {len(fallback_news)} 条数据")
                        self.cached_realtime_news = fallback_news
                        self.last_realtime_news_time = current_time
                        self.last_realtime_news_call_time = current_time
                        return fallback_news
                    else:
                        # 🔥 新增：即使失败也更新时间戳，避免频繁重试
                        self.last_realtime_news_time = current_time
                        self.last_realtime_news_call_time = current_time
                        return []
                    
            except ImportError:
                self.logger.warning("🌍 新闻连接器未找到，跳过实时新闻获取")
                return []
            except Exception as e:
                self.logger.warning(f"🌍 实时新闻获取异常: {e}")
                import traceback
                self.logger.debug(f"🌍 新闻获取错误详情: {traceback.format_exc()}")
                return []
                
        except Exception as e:
            self.logger.error(f"🌍 获取实时新闻数据失败: {e}")
            return []
    
    def _calculate_news_interest(self, title: str, content: str) -> float:
        """计算新闻的林嫣然兴趣程度"""
        text = f"{title} {content}".lower()
        interest_score = 0.0
        
        # 基于兴趣关键词
        for keyword in self.perception_config['interest_keywords']:
            if keyword in text:
                interest_score += 0.15
        
        # 基于新闻类别特殊关键词
        news_interest_keywords = {
            '科技创新': ['AI', '人工智能', '数字化', '创新', '科技突破'],
            '文化教育': ['教育', '文化', '艺术', '传统', '学习'],
            '社会民生': ['民生', '社会', '公平', '就业', '住房', '医疗'],
            '环境健康': ['环保', '健康', '气候', '绿色', '可持续']
        }
        
        for category, keywords in news_interest_keywords.items():
            if any(keyword in text for keyword in keywords):
                interest_score += 0.2
        
        return min(1.0, interest_score)
    
    def _get_fallback_news(self) -> List[Dict[str, Any]]:
        """获取兜底新闻数据"""
        try:
            # 尝试使用AKShare作为兜底新闻源
            from connectors.datasource.akshare_connector import AKShareConnector
            akshare_connector = AKShareConnector()
            
            if akshare_connector.is_available():
                news_result = akshare_connector.get_financial_news(limit=10)
                if news_result.get("success") and news_result.get("data"):
                    fallback_news = []
                    for news in news_result["data"]:
                        fallback_news.append({
                            'id': 0,
                            'title': news.get('title', '无标题'),
                            'content': news.get('summary', '')[:300],
                            'source': news.get('source', 'AKShare'),
                            'category': news.get('category', '财经'),
                            'published_at': news.get('published_at', ''),
                            'importance_score': 0.6,  # 财经新闻默认重要性
                            'platform': 'akshare_fallback',
                            'yanran_interest_score': self._calculate_news_interest(
                                news.get('title', ''), 
                                news.get('summary', '')
                            ),
                            'timestamp': datetime.now().isoformat()
                        })
                    self.logger.debug(f"🌍 AKShare兜底新闻源获取成功: {len(fallback_news)} 条")
                    return fallback_news
            
            # 如果AKShare也不可用，返回空列表
            self.logger.warning("🌍 所有新闻源都不可用")
            return []
            
        except Exception as e:
            self.logger.warning(f"🌍 兜底新闻获取失败: {e}")
            return []
    
    def __str__(self):
        return f"林嫣然的世界感知器官 - 感知外部世界变化的智能器官 (实例 #{self.instance_id})"


# 🔥 P0级别修复：添加单例获取函数
def get_instance(ai_adapter=None) -> WorldPerceptionOrgan:
    """获取世界感知器官单例实例"""
    if WorldPerceptionOrgan._instance is None:
        WorldPerceptionOrgan._instance = WorldPerceptionOrgan(ai_adapter=ai_adapter)
    return WorldPerceptionOrgan._instance 