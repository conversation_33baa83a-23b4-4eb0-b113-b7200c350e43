#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习适应器官

负责学习能力、知识管理和适应性调整。

作者: Claude
创建日期: 2025-06-30
版本: 1.0
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from utilities.unified_logger import get_unified_logger
from utilities.singleton_manager import get_singleton_manager
from cognitive_modules.life_organ_base import LifeOrganBase
from cognitive_modules.ai.yanran_decision_engine import YanranAIDecisionEngine

logger = get_unified_logger("learning_adaptation_organ")
singleton_manager = get_singleton_manager()

class LearningAdaptationOrgan(LifeOrganBase):
    """学习适应器官"""
    
    def __init__(self):
        """初始化学习适应器官"""
        super().__init__(
            organ_name="learning_adaptation_organ",
            description="学习能力和适应性调整",
            dependencies=["emotional_processing_organ", "decision_making_organ"]
        )
        
        self.logger = logger
        self.knowledge_base = {}
        self.learning_history = []
        self.adaptation_patterns = {}
        self.learning_metrics = {
            "total_sessions": 0,
            "successful_adaptations": 0,
            "knowledge_items": 0,
            "learning_efficiency": 0.7
        }
        
        # 初始化学习系统
        self._init_learning_system()

        # 🔥 香草修复：启动智能学习机制
        self._start_intelligent_learning()

        logger.info("🧠 林嫣然学习适应器官初始化完成")

    def _start_intelligent_learning(self):
        """启动智能学习机制 - 🔥 香草修复：持续学习和优化"""
        try:
            import threading
            import time

            def intelligent_learning_worker():
                """智能学习工作线程"""
                while True:
                    try:
                        time.sleep(2700)  # 每45分钟执行一次

                        # 分析学习效果
                        self._analyze_learning_effectiveness()

                        # 优化知识库
                        self._optimize_knowledge_base()

                        # 更新适应策略
                        self._update_adaptation_strategies()

                        # 学习模式识别
                        self._learn_pattern_recognition()

                        logger.debug("🧠 智能学习任务执行完成")

                    except Exception as e:
                        logger.error(f"智能学习任务异常: {e}")

            # 启动后台学习线程
            learning_thread = threading.Thread(target=intelligent_learning_worker, daemon=True)
            learning_thread.start()

            logger.debug("🧠 智能学习机制已启动")

        except Exception as e:
            logger.error(f"启动智能学习机制失败: {e}")

    def _init_learning_system(self):
        """初始化学习系统"""
        try:
            # 学习类型
            self.learning_types = {
                "pattern_recognition": {"weight": 0.3, "retention": 0.8},
                "behavioral_adaptation": {"weight": 0.25, "retention": 0.7},
                "knowledge_acquisition": {"weight": 0.2, "retention": 0.9},
                "skill_improvement": {"weight": 0.15, "retention": 0.6},
                "preference_learning": {"weight": 0.1, "retention": 0.5}
            }
            
            # 适应策略
            self.adaptation_strategies = {
                "gradual": {"speed": 0.3, "stability": 0.9},
                "moderate": {"speed": 0.6, "stability": 0.7},
                "rapid": {"speed": 0.9, "stability": 0.4}
            }
            
            logger.success("🧠 学习系统初始化完成")
            
        except Exception as e:
            logger.error(f"🧠 学习系统初始化失败: {e}")
    
    def process_learning_event(self, event_type: str, data: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理学习事件
        
        Args:
            event_type: 学习事件类型
            data: 学习数据
            context: 学习上下文
            
        Returns:
            学习处理结果
        """
        try:
            context = context or {}
            
            # 分析学习内容
            learning_analysis = self._analyze_learning_content(event_type, data, context)
            
            # 更新知识库
            knowledge_update = self._update_knowledge_base(learning_analysis)
            
            # 识别适应模式
            adaptation_pattern = self._identify_adaptation_pattern(learning_analysis)
            
            # 调整行为策略
            strategy_adjustment = self._adjust_behavior_strategy(adaptation_pattern)
            
            # 记录学习历史
            learning_record = {
                "timestamp": datetime.now(),
                "event_type": event_type,
                "data": data,
                "analysis": learning_analysis,
                "knowledge_update": knowledge_update,
                "adaptation": adaptation_pattern,
                "strategy_adjustment": strategy_adjustment
            }
            
            self.learning_history.append(learning_record)
            self._update_learning_metrics()
            
            logger.success(f"🧠 处理学习事件: {event_type}")
            
            return {
                "success": True,
                "learning_type": learning_analysis["type"],
                "knowledge_gained": knowledge_update,
                "adaptation_applied": adaptation_pattern,
                "strategy_changes": strategy_adjustment,
                "learning_id": len(self.learning_history)
            }
            
        except Exception as e:
            logger.error(f"🧠 处理学习事件失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "learning_type": "unknown"
            }
    
    def _analyze_learning_content(self, event_type: str, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """分析学习内容"""
        try:
            # 确定学习类型
            learning_type = self._classify_learning_type(event_type, data)
            
            # 计算学习重要性
            importance = self._calculate_importance(data, context)
            
            # 分析学习复杂度
            complexity = self._analyze_complexity(data)
            
            # 评估学习价值
            value = self._evaluate_learning_value(data, context)
            
            return {
                "type": learning_type,
                "importance": importance,
                "complexity": complexity,
                "value": value,
                "timestamp": datetime.now(),
                "source": context.get("source", "unknown"),
                "confidence": self._calculate_confidence(data, context)
            }
            
        except Exception as e:
            logger.error(f"🧠 分析学习内容失败: {e}")
            return {
                "type": "general",
                "importance": 0.5,
                "complexity": 0.5,
                "value": 0.5,
                "timestamp": datetime.now(),
                "source": "unknown",
                "confidence": 0.3
            }
    
    def _classify_learning_type(self, event_type: str, data: Dict[str, Any]) -> str:
        """分类学习类型"""
        try:
            if "pattern" in event_type.lower() or "trend" in event_type.lower():
                return "pattern_recognition"
            elif "behavior" in event_type.lower() or "action" in event_type.lower():
                return "behavioral_adaptation"
            elif "knowledge" in event_type.lower() or "fact" in event_type.lower():
                return "knowledge_acquisition"
            elif "skill" in event_type.lower() or "ability" in event_type.lower():
                return "skill_improvement"
            elif "preference" in event_type.lower() or "like" in event_type.lower():
                return "preference_learning"
            else:
                return "general"
                
        except Exception as e:
            logger.error(f"🧠 分类学习类型失败: {e}")
            return "general"
    
    def _calculate_importance(self, data: Dict[str, Any], context: Dict[str, Any]) -> float:
        """计算学习重要性"""
        try:
            importance = 0.5  # 基础重要性
            
            # 根据数据频率调整
            frequency = data.get("frequency", 1)
            if frequency > 5:
                importance += 0.2
            elif frequency > 2:
                importance += 0.1
            
            # 根据上下文调整
            if context.get("urgent", False):
                importance += 0.3
            
            if context.get("user_feedback", False):
                importance += 0.2
            
            # 根据成功率调整
            success_rate = data.get("success_rate", 0.5)
            importance += (success_rate - 0.5) * 0.4
            
            return max(0.0, min(1.0, importance))
            
        except Exception as e:
            logger.error(f"🧠 计算重要性失败: {e}")
            return 0.5
    
    def _analyze_complexity(self, data: Dict[str, Any]) -> float:
        """分析学习复杂度"""
        try:
            complexity = 0.3  # 基础复杂度
            
            # 根据数据结构复杂度
            if isinstance(data, dict):
                complexity += len(data) * 0.05
            
            # 根据数据类型
            if "relationships" in data:
                complexity += 0.2
            
            if "dependencies" in data:
                complexity += 0.3
            
            if "conditions" in data:
                complexity += 0.2
            
            return max(0.1, min(1.0, complexity))
            
        except Exception as e:
            logger.error(f"🧠 分析复杂度失败: {e}")
            return 0.5
    
    def _evaluate_learning_value(self, data: Dict[str, Any], context: Dict[str, Any]) -> float:
        """评估学习价值"""
        try:
            value = 0.4  # 基础价值
            
            # 根据应用性调整
            if context.get("applicable", True):
                value += 0.2
            
            # 根据新颖性调整
            if data.get("novel", False):
                value += 0.3
            
            # 根据相关性调整
            relevance = context.get("relevance", 0.5)
            value += relevance * 0.3
            
            return max(0.0, min(1.0, value))
            
        except Exception as e:
            logger.error(f"🧠 评估学习价值失败: {e}")
            return 0.5
    
    def _calculate_confidence(self, data: Dict[str, Any], context: Dict[str, Any]) -> float:
        """计算学习置信度"""
        try:
            confidence = 0.5  # 基础置信度
            
            # 根据数据来源调整
            source_reliability = context.get("source_reliability", 0.5)
            confidence += (source_reliability - 0.5) * 0.4
            
            # 根据验证次数调整
            verification_count = data.get("verification_count", 1)
            if verification_count > 3:
                confidence += 0.2
            elif verification_count > 1:
                confidence += 0.1
            
            return max(0.1, min(1.0, confidence))
            
        except Exception as e:
            logger.error(f"🧠 计算置信度失败: {e}")
            return 0.5
    
    def _update_knowledge_base(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """更新知识库"""
        try:
            learning_type = analysis["type"]
            
            if learning_type not in self.knowledge_base:
                self.knowledge_base[learning_type] = {
                    "items": [],
                    "patterns": {},
                    "last_updated": datetime.now(),
                    "confidence_sum": 0.0,
                    "item_count": 0
                }
            
            kb_section = self.knowledge_base[learning_type]
            
            # 添加新知识项
            knowledge_item = {
                "id": len(kb_section["items"]) + 1,
                "content": analysis,
                "timestamp": datetime.now(),
                "access_count": 0,
                "last_accessed": None
            }
            
            kb_section["items"].append(knowledge_item)
            kb_section["confidence_sum"] += analysis["confidence"]
            kb_section["item_count"] += 1
            kb_section["last_updated"] = datetime.now()
            
            self.learning_metrics["knowledge_items"] = sum(
                section["item_count"] for section in self.knowledge_base.values()
            )
            
            logger.success(f"🧠 知识库已更新: {learning_type}")
            
            return {
                "type": learning_type,
                "item_id": knowledge_item["id"],
                "total_items": kb_section["item_count"],
                "avg_confidence": kb_section["confidence_sum"] / kb_section["item_count"]
            }
            
        except Exception as e:
            logger.error(f"🧠 更新知识库失败: {e}")
            return {"type": "unknown", "item_id": 0, "total_items": 0, "avg_confidence": 0.0}
    
    def _identify_adaptation_pattern(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """识别适应模式 - 🔥 香草修复：智能适应模式识别"""
        try:
            learning_type = analysis["type"]
            importance = analysis["importance"]
            complexity = analysis["complexity"]

            # 🔥 基于历史成功率选择策略
            historical_success = self._analyze_historical_adaptation_success(learning_type)

            # 🔥 多因素决策适应策略
            strategy_score = self._calculate_strategy_score(importance, complexity, historical_success)

            # 🔥 智能策略选择
            if strategy_score > 0.8:
                strategy = "immediate"
            elif strategy_score > 0.6:
                strategy = "accelerated"
            elif strategy_score > 0.4:
                strategy = "gradual"
            else:
                strategy = "conservative"

            # 🔥 动态强度计算
            intensity = self._calculate_adaptive_intensity(importance, complexity, historical_success)

            # 🔥 模式学习和优化
            pattern_key = f"{learning_type}_{strategy}"
            pattern_effectiveness = self._update_pattern_effectiveness(pattern_key, analysis)

            # 🔥 基于效果反馈调整策略
            if pattern_effectiveness.get("recent_success_rate", 0.5) < 0.3:
                # 如果最近成功率低，调整策略
                strategy = self._adjust_strategy_based_on_feedback(strategy, pattern_effectiveness)
                pattern_key = f"{learning_type}_{strategy}"

            return {
                "strategy": strategy,
                "intensity": intensity,
                "pattern_key": pattern_key,
                "usage_count": pattern_effectiveness.get("count", 0),
                "expected_success": pattern_effectiveness.get("success_rate", 0.5),
                "recent_success_rate": pattern_effectiveness.get("recent_success_rate", 0.5),
                "confidence": pattern_effectiveness.get("confidence", 0.5)
            }

        except Exception as e:
            logger.error(f"🧠 识别适应模式失败: {e}")
            return {
                "strategy": "gradual",
                "intensity": 0.5,
                "pattern_key": "general_gradual",
                "usage_count": 0,
                "expected_success": 0.5,
                "recent_success_rate": 0.5,
                "confidence": 0.3
            }
    
    def _adjust_behavior_strategy(self, adaptation_pattern: Dict[str, Any]) -> Dict[str, Any]:
        """调整行为策略"""
        try:
            strategy = adaptation_pattern["strategy"]
            intensity = adaptation_pattern["intensity"]
            
            adjustments = {}
            
            # 根据策略调整参数
            if strategy == "rapid":
                adjustments["response_speed"] = min(1.0, intensity + 0.3)
                adjustments["risk_tolerance"] = min(1.0, intensity + 0.2)
                adjustments["exploration_rate"] = min(1.0, intensity + 0.1)
            elif strategy == "moderate":
                adjustments["response_speed"] = intensity
                adjustments["risk_tolerance"] = intensity * 0.8
                adjustments["exploration_rate"] = intensity * 0.6
            else:  # gradual
                adjustments["response_speed"] = intensity * 0.6
                adjustments["risk_tolerance"] = intensity * 0.4
                adjustments["exploration_rate"] = intensity * 0.3
            
            # 调整学习效率
            self.learning_metrics["learning_efficiency"] = min(1.0, 
                self.learning_metrics["learning_efficiency"] * 0.9 + intensity * 0.1
            )
            
            logger.success(f"🧠 行为策略已调整: {strategy}")
            
            return adjustments
            
        except Exception as e:
            logger.error(f"🧠 调整行为策略失败: {e}")
            return {"response_speed": 0.5, "risk_tolerance": 0.5, "exploration_rate": 0.5}
    
    def _update_learning_metrics(self):
        """更新学习指标"""
        try:
            self.learning_metrics["total_sessions"] += 1
            
            # 计算成功适应率
            recent_sessions = self.learning_history[-10:] if len(self.learning_history) >= 10 else self.learning_history
            successful = sum(1 for session in recent_sessions if session["analysis"]["confidence"] > 0.6)
            
            if recent_sessions:
                success_rate = successful / len(recent_sessions)
                self.learning_metrics["successful_adaptations"] = success_rate
            
        except Exception as e:
            logger.error(f"🧠 更新学习指标失败: {e}")
    
    def get_knowledge(self, learning_type: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """获取知识"""
        try:
            if learning_type and learning_type in self.knowledge_base:
                items = self.knowledge_base[learning_type]["items"]
                return sorted(items, key=lambda x: x["timestamp"], reverse=True)[:limit]
            else:
                all_items = []
                for section in self.knowledge_base.values():
                    all_items.extend(section["items"])
                return sorted(all_items, key=lambda x: x["timestamp"], reverse=True)[:limit]
                
        except Exception as e:
            logger.error(f"🧠 获取知识失败: {e}")
            return []
    
    def get_learning_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取学习历史"""
        return self.learning_history[-limit:]
    
    def get_adaptation_patterns(self) -> Dict[str, Any]:
        """获取适应模式"""
        return self.adaptation_patterns.copy()
    
    def get_learning_metrics(self) -> Dict[str, Any]:
        """获取学习指标"""
        return self.learning_metrics.copy()
    
    def reset_learning_data(self, confirm: bool = False) -> bool:
        """重置学习数据"""
        if not confirm:
            logger.warning("🧠 重置学习数据需要确认")
            return False
        
        try:
            self.knowledge_base.clear()
            self.learning_history.clear()
            self.adaptation_patterns.clear()
            self.learning_metrics = {
                "total_sessions": 0,
                "successful_adaptations": 0,
                "knowledge_items": 0,
                "learning_efficiency": 0.7
            }
            
            logger.success("🧠 学习数据已重置")
            return True
            
        except Exception as e:
            logger.error(f"🧠 重置学习数据失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取器官状态"""
        return {
            "organ_name": self.organ_name,
            "is_active": True,
            "learning_metrics": self.learning_metrics.copy(),
            "knowledge_types": list(self.knowledge_base.keys()),
            "adaptation_patterns_count": len(self.adaptation_patterns),
            "last_learning": self.learning_history[-1]["timestamp"].isoformat() if self.learning_history else None,
            "health_status": "healthy"
        }

    def _analyze_historical_adaptation_success(self, learning_type: str) -> float:
        """分析历史适应成功率 - 🔥 香草修复：基于历史数据分析"""
        try:
            if not self.learning_history:
                return 0.5  # 默认成功率

            # 筛选相同学习类型的历史记录
            type_history = [h for h in self.learning_history if h.get("learning_type") == learning_type]

            if not type_history:
                return 0.5

            # 计算最近的成功率
            recent_history = type_history[-20:]  # 最近20次
            success_count = sum(1 for h in recent_history if h.get("success", False))

            return success_count / len(recent_history)

        except Exception as e:
            logger.error(f"分析历史适应成功率异常: {e}")
            return 0.5

    def _calculate_strategy_score(self, importance: float, complexity: float, historical_success: float) -> float:
        """计算策略评分"""
        try:
            # 综合考虑重要性、复杂度和历史成功率
            score = (importance * 0.4 + (1 - complexity) * 0.3 + historical_success * 0.3)
            return max(0.0, min(1.0, score))
        except Exception as e:
            logger.error(f"计算策略评分异常: {e}")
            return 0.5

    def _calculate_adaptive_intensity(self, importance: float, complexity: float, historical_success: float) -> float:
        """计算自适应强度"""
        try:
            # 基于多因素计算强度
            base_intensity = (importance + (1 - complexity)) / 2

            # 基于历史成功率调整
            if historical_success > 0.7:
                adjustment = 0.1  # 成功率高，可以增强强度
            elif historical_success < 0.3:
                adjustment = -0.1  # 成功率低，降低强度
            else:
                adjustment = 0.0

            return max(0.1, min(1.0, base_intensity + adjustment))

        except Exception as e:
            logger.error(f"计算自适应强度异常: {e}")
            return 0.5

    def _update_pattern_effectiveness(self, pattern_key: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """更新模式效果"""
        try:
            if pattern_key not in self.adaptation_patterns:
                self.adaptation_patterns[pattern_key] = {
                    "count": 0,
                    "success_rate": 0.5,
                    "recent_successes": [],
                    "confidence": 0.3,
                    "last_used": None
                }

            pattern = self.adaptation_patterns[pattern_key]
            pattern["count"] += 1
            pattern["last_used"] = datetime.now()

            # 更新最近成功记录（假设当前是成功的，实际应该基于反馈）
            pattern["recent_successes"].append(True)  # 这里应该基于实际反馈
            if len(pattern["recent_successes"]) > 10:
                pattern["recent_successes"] = pattern["recent_successes"][-10:]

            # 计算最近成功率
            recent_success_rate = sum(pattern["recent_successes"]) / len(pattern["recent_successes"])
            pattern["recent_success_rate"] = recent_success_rate

            # 更新置信度
            pattern["confidence"] = min(1.0, pattern["count"] / 20.0)  # 20次后达到最高置信度

            return pattern

        except Exception as e:
            logger.error(f"更新模式效果异常: {e}")
            return {"count": 0, "success_rate": 0.5, "confidence": 0.3}

    def _adjust_strategy_based_on_feedback(self, current_strategy: str, pattern_effectiveness: Dict[str, Any]) -> str:
        """基于反馈调整策略"""
        try:
            recent_success_rate = pattern_effectiveness.get("recent_success_rate", 0.5)

            # 策略调整逻辑
            if current_strategy == "immediate" and recent_success_rate < 0.3:
                return "accelerated"  # 降级
            elif current_strategy == "accelerated" and recent_success_rate < 0.3:
                return "gradual"  # 降级
            elif current_strategy == "gradual" and recent_success_rate > 0.8:
                return "accelerated"  # 升级
            elif current_strategy == "conservative" and recent_success_rate > 0.6:
                return "gradual"  # 升级

            return current_strategy  # 保持不变

        except Exception as e:
            logger.error(f"基于反馈调整策略异常: {e}")
            return current_strategy

# 工厂函数
def create_learning_adaptation_organ() -> LearningAdaptationOrgan:
    """创建学习适应器官实例"""
    return LearningAdaptationOrgan()

# 向后兼容
def get_instance() -> LearningAdaptationOrgan:
    """获取学习适应器官实例"""
    return singleton_manager.get_or_create(
        'learning_adaptation_organ',
        create_learning_adaptation_organ
    ) 