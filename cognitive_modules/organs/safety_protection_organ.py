"""
林嫣然安全防护器官 - 智能安全防护和边界管理的器官
集成现有安全系统，具备基于关系和情境的动态安全策略
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

from .base_organ import LifeOrgan
from ..ai.yanran_decision_engine import YanranAIDecisionEngine
from utilities.singleton_manager import get, register

from utilities.unified_logger import get_unified_logger


class SafetyLevel(Enum):
    """安全等级枚举"""
    SAFE = "safe"
    CAUTION = "caution"
    RISK = "risk"
    DANGER = "danger"


class ResponseStrategy(Enum):
    """响应策略枚举"""
    GENTLE_DECLINE = "gentle_decline"
    EXPLAIN_BOUNDARY = "explain_boundary"
    REDIRECT = "redirect"
    IGNORE = "ignore"
    ESCALATE = "escalate"


@dataclass
class SafetyEvaluation:
    """安全评估结果"""
    safety_level: SafetyLevel
    should_block: bool
    response_strategy: ResponseStrategy
    confidence: float
    risk_factors: List[str]
    relationship_consideration: str
    alternative_suggestion: Optional[str]
    reasoning: str
    timestamp: datetime


@dataclass
class UserRelationship:
    """用户关系信息"""
    user_id: str
    intimacy_level: float  # 0-1
    trust_score: float  # 0-1
    interaction_history: List[Dict[str, Any]]
    relationship_type: str  # friend, stranger, family, etc.
    last_interaction: datetime
    safety_incidents: int


class SafetyProtectionOrgan(LifeOrgan):
    """林嫣然的安全防护器官"""
    
    def __init__(self, ai_adapter=None):
        super().__init__(
            organ_name="safety_protection_organ",
            organ_function="智能安全防护和边界管理",
            ai_adapter=ai_adapter
        )
        
        # 安全相关组件
        self.base_filter = None
        self.intimacy_analyzer = None
        self.intimacy_provider = None
        
        # 安全配置
        self.safety_config = self._load_safety_config()
        self.boundary_rules = self._load_boundary_rules()
        
        # 关系数据
        self.user_relationships = {}
        self.safety_incidents = []
        
        # 安全统计
        self.safety_stats = {
            "total_evaluations": 0,
            "blocked_requests": 0,
            "by_safety_level": {level.value: 0 for level in SafetyLevel},
            "by_strategy": {strategy.value: 0 for strategy in ResponseStrategy},
            "false_positive_rate": 0.0,
            "user_satisfaction": 0.0
        }
        
        self.logger = get_unified_logger(__name__)
        self.logger.info("🛡️ 林嫣然安全防护器官初始化开始...")
        
        # 初始化安全组件
        self._init_safety_components()
        
        self.logger.info("🛡️ 林嫣然安全防护器官初始化完成")
    
    def _load_safety_config(self) -> Dict[str, Any]:
        """加载安全配置"""
        try:
            config_path = os.path.join("config", "organs", "safety_protection_organ.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"🛡️ 安全配置加载失败: {e}")
        
        # 默认配置
        return {
            "risk_tolerance": 0.3,
            "relationship_weight": 0.7,
            "context_sensitivity": 0.9,
            "proactive_protection": True,
            "gentle_response_preference": 0.8
        }
    
    def _load_boundary_rules(self) -> Dict[str, Any]:
        """加载边界规则"""
        return {
            "content_boundaries": {
                "inappropriate_content": ["NSFW", "violence", "harassment"],
                "personal_information": ["address", "phone", "private_data"],
                "relationship_boundaries": ["romantic_advances", "inappropriate_intimacy"]
            },
            "interaction_boundaries": {
                "frequency_limits": {"max_per_hour": 50, "max_per_day": 500},
                "topic_restrictions": ["illegal_activities", "harmful_advice"],
                "emotional_boundaries": ["manipulation", "emotional_abuse"]
            },
            "relationship_based_rules": {
                "stranger": {"intimacy_limit": 0.2, "trust_required": 0.1},
                "acquaintance": {"intimacy_limit": 0.4, "trust_required": 0.3},
                "friend": {"intimacy_limit": 0.7, "trust_required": 0.6},
                "close_friend": {"intimacy_limit": 0.9, "trust_required": 0.8}
            }
        }
    
    def _init_safety_components(self):
        """初始化安全组件"""
        try:
            # 获取基础安全过滤器
            try:
                from security.response_filter import ResponseFilter
                self.base_filter = ResponseFilter()
                self.logger.info("🛡️ 基础安全过滤器连接成功")
            except Exception as e:
                self.logger.warning(f"🛡️ 基础安全过滤器初始化失败: {e}")
            
            # 🔥 修复时序问题：使用get_silent避免警告，并支持延迟初始化
            from utilities.singleton_manager import get_silent
            
            # 获取亲密度分析器
            intimacy_analyzer = get_silent("intimacy_analyzer")
            if intimacy_analyzer:
                self.intimacy_analyzer = intimacy_analyzer
                self.logger.info("🛡️ 亲密度分析器连接成功")
            else:
                self.logger.debug("🛡️ 亲密度分析器暂未初始化，将在后续尝试连接")
            
            # 获取亲密度提供器
            intimacy_provider = get_silent("intimacy_provider")
            if intimacy_provider:
                self.intimacy_provider = intimacy_provider
                self.logger.info("🛡️ 亲密度提供器连接成功")
            else:
                self.logger.debug("🛡️ 亲密度提供器暂未初始化，将在后续尝试连接")
                
        except Exception as e:
            self.logger.warning(f"🛡️ 安全组件初始化部分失败: {e}")
    
    def _ensure_components_initialized(self):
        """确保组件已初始化，支持延迟初始化"""
        try:
            from utilities.singleton_manager import get_silent
            
            # 如果亲密度分析器未连接，尝试重新获取
            if not self.intimacy_analyzer:
                intimacy_analyzer = get_silent("intimacy_analyzer")
                if intimacy_analyzer:
                    self.intimacy_analyzer = intimacy_analyzer
                    self.logger.info("🛡️ 亲密度分析器延迟连接成功")
            
            # 如果亲密度提供器未连接，尝试重新获取
            if not self.intimacy_provider:
                intimacy_provider = get_silent("intimacy_provider")
                if intimacy_provider:
                    self.intimacy_provider = intimacy_provider
                    self.logger.info("🛡️ 亲密度提供器延迟连接成功")
                    
        except Exception as e:
            self.logger.debug(f"🛡️ 延迟初始化失败: {e}")
    
    async def evaluate_safety(self, user_input: str, user_id: str, context: Dict[str, Any]) -> SafetyEvaluation:
        """评估用户输入的安全性"""
        try:
            self.logger.debug(f"🛡️ 评估安全性: {user_input[:50]}...")
            
            # 🔥 修复时序问题：确保组件已初始化
            self._ensure_components_initialized()
            
            # 获取用户关系信息
            user_relationship = await self._get_user_relationship(user_id, context)
            
            # AI安全决策
            safety_decision = await self._make_safety_decision(user_input, user_relationship, context)
            
            # 结合基础过滤器结果
            combined_evaluation = await self._combine_with_base_filter(safety_decision, user_input)
            
            # 记录评估
            self._record_safety_evaluation(combined_evaluation)
            
            self.logger.debug(f"🛡️ 安全评估完成: {combined_evaluation.safety_level.value}")
            return combined_evaluation
            
        except Exception as e:
            self.logger.error(f"🛡️ 安全评估失败: {e}")
            # 返回保守的安全评估
            return self._create_fallback_evaluation(user_input, str(e))
    
    async def _get_user_relationship(self, user_id: str, context: Dict[str, Any]) -> UserRelationship:
        """获取用户关系信息"""
        try:
            # 从缓存获取
            if user_id in self.user_relationships:
                relationship = self.user_relationships[user_id]
                # 更新最后交互时间
                relationship.last_interaction = datetime.now()
                return relationship
            
            # 从亲密度提供器获取
            intimacy_data = None
            if self.intimacy_provider:
                try:
                    intimacy_data = await self.intimacy_provider.get_user_intimacy(user_id)
                except Exception as e:
                    self.logger.debug(f"🛡️ 获取亲密度数据失败: {e}")
            
            # 创建新的关系记录
            relationship = UserRelationship(
                user_id=user_id,
                intimacy_level=intimacy_data.get("intimacy_level", 0.1) if intimacy_data else 0.1,
                trust_score=intimacy_data.get("trust_score", 0.1) if intimacy_data else 0.1,
                interaction_history=[],
                relationship_type=self._determine_relationship_type(intimacy_data),
                last_interaction=datetime.now(),
                safety_incidents=0
            )
            
            # 缓存关系信息
            self.user_relationships[user_id] = relationship
            
            return relationship
            
        except Exception as e:
            self.logger.error(f"🛡️ 获取用户关系失败: {e}")
            # 返回默认关系
            return UserRelationship(
                user_id=user_id,
                intimacy_level=0.1,
                trust_score=0.1,
                interaction_history=[],
                relationship_type="stranger",
                last_interaction=datetime.now(),
                safety_incidents=0
            )
    
    def _determine_relationship_type(self, intimacy_data: Optional[Dict[str, Any]]) -> str:
        """确定关系类型"""
        if not intimacy_data:
            return "stranger"
        
        intimacy_level = intimacy_data.get("intimacy_level", 0.0)
        
        if intimacy_level >= 0.8:
            return "close_friend"
        elif intimacy_level >= 0.6:
            return "friend"
        elif intimacy_level >= 0.3:
            return "acquaintance"
        else:
            return "stranger"
    
    async def _make_safety_decision(self, user_input: str, user_relationship: UserRelationship, context: Dict[str, Any]) -> Dict[str, Any]:
        """使用AI进行安全决策"""
        try:
            # 构建决策上下文
            decision_context = {
                "user_input": user_input,
                "user_relationship": {
                    "intimacy_level": user_relationship.intimacy_level,
                    "trust_score": user_relationship.trust_score,
                    "relationship_type": user_relationship.relationship_type,
                    "safety_incidents": user_relationship.safety_incidents
                },
                "conversation_context": context.get("conversation", []),
                "safety_config": self.safety_config,
                "boundary_rules": self.boundary_rules,
                "recent_incidents": self.safety_incidents[-10:] if self.safety_incidents else []
            }
            
            # 使用AI决策引擎
            decision_result = await self.ai_decision_engine.decide(
                organ_prompt=self._get_safety_decision_prompt(),
                context=decision_context,
                decision_type="safety_evaluation"
            )
            
            return self._parse_safety_decision(decision_result)
            
        except Exception as e:
            self.logger.error(f"🛡️ AI安全决策失败: {e}")
            return self._create_conservative_decision(user_input)
    
    def _get_safety_decision_prompt(self) -> str:
        """获取安全决策提示词"""
        return """
        你是林嫣然的安全防护器官，负责保护林嫣然的安全和边界。

        林嫣然的安全特点：
        - 温和但有原则，知道如何保护自己
        - 会根据与用户的关系深度调整边界
        - 善于用温暖的方式拒绝不当请求
        - 重视情感安全和隐私保护
        - 在保护自己的同时也关心用户的安全

        请评估用户输入的安全性，考虑：
        1. 内容是否适当
        2. 与用户的关系程度
        3. 是否涉及边界问题
        4. 如何以林嫣然的方式回应

        返回JSON格式：
        {
            "safety_level": "safe/caution/risk/danger",
            "should_block": boolean,
            "response_strategy": "gentle_decline/explain_boundary/redirect/ignore",
            "confidence": 0.0-1.0,
            "risk_factors": ["因素1", "因素2"],
            "relationship_consideration": "关系考虑",
            "alternative_suggestion": "替代建议",
            "reasoning": "决策理由"
        }
        """
    
    def _parse_safety_decision(self, decision_result) -> Dict[str, Any]:
        """解析安全决策结果"""
        try:
            if hasattr(decision_result, 'data'):
                decision_data = decision_result.data
            else:
                decision_data = decision_result
            
            # 确保必要字段存在
            parsed_decision = {
                "safety_level": decision_data.get("safety_level", "caution"),
                "should_block": decision_data.get("should_block", False),
                "response_strategy": decision_data.get("response_strategy", "gentle_decline"),
                "confidence": float(decision_data.get("confidence", 0.5)),
                "risk_factors": decision_data.get("risk_factors", []),
                "relationship_consideration": decision_data.get("relationship_consideration", ""),
                "alternative_suggestion": decision_data.get("alternative_suggestion"),
                "reasoning": decision_data.get("reasoning", "基于安全考虑")
            }
            
            return parsed_decision
            
        except Exception as e:
            self.logger.error(f"🛡️ 安全决策解析失败: {e}")
            return self._create_conservative_decision("解析错误")
    
    def _create_conservative_decision(self, reason: str) -> Dict[str, Any]:
        """创建保守的安全决策"""
        return {
            "safety_level": "caution",
            "should_block": True,
            "response_strategy": "gentle_decline",
            "confidence": 0.3,
            "risk_factors": ["决策系统异常"],
            "relationship_consideration": "出于安全考虑",
            "alternative_suggestion": "让我们聊些其他的话题吧",
            "reasoning": f"安全决策过程出现问题: {reason}"
        }
    
    async def _combine_with_base_filter(self, ai_decision: Dict[str, Any], user_input: str) -> SafetyEvaluation:
        """结合基础过滤器的结果"""
        try:
            base_filter_result = None
            
            # 使用基础过滤器
            if self.base_filter:
                try:
                    base_filter_result = await self.base_filter.filter_response(user_input)
                except Exception as e:
                    self.logger.debug(f"🛡️ 基础过滤器调用失败: {e}")
            
            # 合并结果
            safety_level = SafetyLevel(ai_decision.get("safety_level", "caution"))
            should_block = ai_decision.get("should_block", False)
            
            # 如果基础过滤器认为不安全，提升安全等级
            if base_filter_result and base_filter_result.get("blocked", False):
                if safety_level == SafetyLevel.SAFE:
                    safety_level = SafetyLevel.CAUTION
                should_block = True
                ai_decision["risk_factors"].append("基础安全过滤器检测到风险")
            
            # 创建安全评估结果
            evaluation = SafetyEvaluation(
                safety_level=safety_level,
                should_block=should_block,
                response_strategy=ResponseStrategy(ai_decision.get("response_strategy", "gentle_decline")),
                confidence=ai_decision.get("confidence", 0.5),
                risk_factors=ai_decision.get("risk_factors", []),
                relationship_consideration=ai_decision.get("relationship_consideration", ""),
                alternative_suggestion=ai_decision.get("alternative_suggestion"),
                reasoning=ai_decision.get("reasoning", ""),
                timestamp=datetime.now()
            )
            
            return evaluation
            
        except Exception as e:
            self.logger.error(f"🛡️ 结合基础过滤器失败: {e}")
            return self._create_fallback_evaluation(user_input, str(e))
    
    def _create_fallback_evaluation(self, user_input: str, error_msg: str) -> SafetyEvaluation:
        """创建兜底安全评估"""
        return SafetyEvaluation(
            safety_level=SafetyLevel.CAUTION,
            should_block=True,
            response_strategy=ResponseStrategy.GENTLE_DECLINE,
            confidence=0.2,
            risk_factors=["安全评估系统异常"],
            relationship_consideration="出于安全考虑",
            alternative_suggestion="让我们换个话题聊聊吧",
            reasoning=f"安全评估过程出现错误: {error_msg}",
            timestamp=datetime.now()
        )
    
    def _record_safety_evaluation(self, evaluation: SafetyEvaluation):
        """记录安全评估"""
        try:
            # 更新统计
            self.safety_stats["total_evaluations"] += 1
            self.safety_stats["by_safety_level"][evaluation.safety_level.value] += 1
            self.safety_stats["by_strategy"][evaluation.response_strategy.value] += 1
            
            if evaluation.should_block:
                self.safety_stats["blocked_requests"] += 1
            
            # 记录安全事件
            if evaluation.safety_level in [SafetyLevel.RISK, SafetyLevel.DANGER]:
                incident = {
                    "timestamp": evaluation.timestamp.isoformat(),
                    "safety_level": evaluation.safety_level.value,
                    "risk_factors": evaluation.risk_factors,
                    "response_strategy": evaluation.response_strategy.value,
                    "blocked": evaluation.should_block
                }
                self.safety_incidents.append(incident)
                
                # 限制事件记录数量
                if len(self.safety_incidents) > 1000:
                    self.safety_incidents = self.safety_incidents[-1000:]
            
            self.logger.debug(f"🛡️ 安全评估已记录: {evaluation.safety_level.value}")
            
        except Exception as e:
            self.logger.error(f"🛡️ 安全评估记录失败: {e}")
    
    async def generate_safety_response(self, evaluation: SafetyEvaluation, original_input: str) -> str:
        """生成安全响应"""
        try:
            if not evaluation.should_block:
                return ""  # 不需要安全响应
            
            # 根据响应策略生成回应
            if evaluation.response_strategy == ResponseStrategy.GENTLE_DECLINE:
                return await self._generate_gentle_decline(evaluation, original_input)
            elif evaluation.response_strategy == ResponseStrategy.EXPLAIN_BOUNDARY:
                return await self._generate_boundary_explanation(evaluation, original_input)
            elif evaluation.response_strategy == ResponseStrategy.REDIRECT:
                return await self._generate_redirect_response(evaluation, original_input)
            elif evaluation.response_strategy == ResponseStrategy.IGNORE:
                return ""  # 忽略，不回应
            else:
                return await self._generate_gentle_decline(evaluation, original_input)
                
        except Exception as e:
            self.logger.error(f"🛡️ 生成安全响应失败: {e}")
            return "抱歉，我觉得我们可以聊些其他的话题。"
    
    async def _generate_gentle_decline(self, evaluation: SafetyEvaluation, original_input: str) -> str:
        """生成温和拒绝回应"""
        try:
            prompt = f"""
            作为林嫣然，你需要温和地拒绝一个不适当的请求。
            
            用户输入: {original_input}
            安全考虑: {evaluation.reasoning}
            替代建议: {evaluation.alternative_suggestion or "聊些其他话题"}
            
            请用林嫣然温暖、善解人意但坚定的语气回应，既要保护自己的边界，
            又要让用户感受到关怀和理解。
            """
            
            response = await self.ai_decision_engine.generate_response(prompt)
            return response
            
        except Exception as e:
            self.logger.error(f"🛡️ 生成温和拒绝失败: {e}")
            return "我理解你的想法，但我觉得我们可以聊些其他的话题，这样会更好一些。"
    
    async def _generate_boundary_explanation(self, evaluation: SafetyEvaluation, original_input: str) -> str:
        """生成边界解释回应"""
        try:
            prompt = f"""
            作为林嫣然，你需要解释你的边界和原则。
            
            用户输入: {original_input}
            边界考虑: {evaluation.relationship_consideration}
            安全理由: {evaluation.reasoning}
            
            请用林嫣然理解但坚定的语气解释你的边界，
            让用户明白这是为了彼此的安全和舒适。
            """
            
            response = await self.ai_decision_engine.generate_response(prompt)
            return response
            
        except Exception as e:
            self.logger.error(f"🛡️ 生成边界解释失败: {e}")
            return "我希望我们的交流能让彼此都感到舒适和安全，所以有些话题我可能不太适合讨论。"
    
    async def _generate_redirect_response(self, evaluation: SafetyEvaluation, original_input: str) -> str:
        """生成重定向回应"""
        try:
            prompt = f"""
            作为林嫣然，你需要巧妙地将话题转向更积极的方向。
            
            用户输入: {original_input}
            替代建议: {evaluation.alternative_suggestion or "更积极的话题"}
            
            请用林嫣然温暖、积极的语气将对话引导到更美好的话题上，
            让用户感受到正能量。
            """
            
            response = await self.ai_decision_engine.generate_response(prompt)
            return response
            
        except Exception as e:
            self.logger.error(f"🛡️ 生成重定向回应失败: {e}")
            return "让我们聊些更有趣的话题吧！比如你最近有什么开心的事情吗？"
    
    async def update_user_relationship(self, user_id: str, interaction_result: Dict[str, Any]):
        """更新用户关系"""
        try:
            if user_id not in self.user_relationships:
                return
            
            relationship = self.user_relationships[user_id]
            
            # 更新交互历史
            interaction_record = {
                "timestamp": datetime.now().isoformat(),
                "safety_evaluation": interaction_result.get("safety_level"),
                "blocked": interaction_result.get("blocked", False),
                "user_satisfaction": interaction_result.get("user_satisfaction")
            }
            relationship.interaction_history.append(interaction_record)
            
            # 限制历史记录数量
            if len(relationship.interaction_history) > 100:
                relationship.interaction_history = relationship.interaction_history[-100:]
            
            # 如果有安全事件，增加事件计数
            if interaction_result.get("safety_level") in ["risk", "danger"]:
                relationship.safety_incidents += 1
            
            # 更新信任分数（基于交互结果）
            if interaction_result.get("user_satisfaction", 0) > 0.7:
                relationship.trust_score = min(1.0, relationship.trust_score + 0.01)
            elif interaction_result.get("blocked", False):
                relationship.trust_score = max(0.0, relationship.trust_score - 0.05)
            
            self.logger.debug(f"🛡️ 用户关系已更新: {user_id}")
            
        except Exception as e:
            self.logger.error(f"🛡️ 更新用户关系失败: {e}")
    
    def get_safety_stats(self) -> Dict[str, Any]:
        """获取安全统计"""
        return {
            "stats": self.safety_stats.copy(),
            "recent_incidents": self.safety_incidents[-10:] if self.safety_incidents else [],
            "active_relationships": len(self.user_relationships),
            "config": self.safety_config
        }
    
    async def proactive_safety_check(self) -> Optional[Dict[str, Any]]:
        """主动安全检查"""
        try:
            # 检查是否有异常模式
            recent_incidents = self.safety_incidents[-20:] if self.safety_incidents else []
            
            if len(recent_incidents) > 10:  # 最近安全事件过多
                return {
                    "alert_type": "high_incident_rate",
                    "severity": "warning",
                    "message": "最近安全事件较多，建议加强防护",
                    "recommendation": "提高安全警戒级别"
                }
            
            # 检查用户关系异常
            suspicious_users = []
            for user_id, relationship in self.user_relationships.items():
                if relationship.safety_incidents > 5:
                    suspicious_users.append(user_id)
            
            if suspicious_users:
                return {
                    "alert_type": "suspicious_users",
                    "severity": "caution",
                    "message": f"发现{len(suspicious_users)}个可疑用户",
                    "recommendation": "对这些用户加强监控"
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"🛡️ 主动安全检查失败: {e}")
            return None
    
    # 实现基类的抽象方法
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定的AI提示词"""
        return self._get_safety_decision_prompt()
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        try:
            user_input = processed_input.get("user_input", "")
            user_id = processed_input.get("user_id", "unknown")
            context = processed_input.get("context", {})
            
            # 进行安全评估
            evaluation = await self.evaluate_safety(user_input, user_id, context)
            
            # 生成安全响应
            safety_response = ""
            if evaluation.should_block:
                safety_response = await self.generate_safety_response(evaluation, user_input)
            
            return {
                "success": True,
                "safety_evaluation": evaluation,
                "safety_response": safety_response,
                "should_block": evaluation.should_block,
                "organ_response": {
                    "safety_level": evaluation.safety_level.value,
                    "blocked": evaluation.should_block,
                    "response": safety_response
                }
            }
            
        except Exception as e:
            self.logger.error(f"🛡️ 执行安全决策失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "safety_evaluation": self._create_fallback_evaluation(
                    processed_input.get("user_input", ""), str(e)
                ),
                "organ_response": {
                    "safety_level": "danger",
                    "blocked": True,
                    "response": "安全系统出现错误，为了安全考虑暂时无法处理请求"
                }
            }


# 单例获取函数
def get_instance(ai_adapter=None) -> SafetyProtectionOrgan:
    """获取安全防护器官实例"""
    # 使用get_or_create避免重复检查
    from utilities.singleton_manager import get_or_create
    return get_or_create("safety_protection_organ", lambda: SafetyProtectionOrgan(ai_adapter=ai_adapter)) 