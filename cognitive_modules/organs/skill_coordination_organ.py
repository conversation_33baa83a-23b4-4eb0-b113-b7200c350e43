"""
林嫣然技能协调器官 - 智能技能协调和优化的器官
作为林嫣然的"技能大脑"，负责技能选择、组合和优化使用
"""

import asyncio
import json
import os
from utilities.unified_logger import get_unified_logger
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from enum import Enum

from .base_organ import LifeOrgan
from ..ai.yanran_decision_engine import YanranAIDecisionEngine
from utilities.singleton_manager import get, register


class SkillPriority(Enum):
    """技能优先级枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class SkillCategory(Enum):
    """技能类别枚举"""
    COMMUNICATION = "communication"  # 聊天、搜索等
    CREATIVE = "creative"  # 绘画、音乐等
    SOCIAL = "social"  # 微信消息等
    ANALYTICAL = "analytical"  # 金融数据等
    UTILITY = "utility"  # 工具类技能


@dataclass
class SkillUsageRecord:
    """技能使用记录"""
    skill_name: str
    category: SkillCategory
    usage_time: datetime
    success: bool
    execution_time: float
    user_satisfaction: Optional[float]
    context: Dict[str, Any]
    result_quality: Optional[float]


@dataclass
class SkillCombination:
    """技能组合方案"""
    combination_id: str
    skills: List[str]
    purpose: str
    success_rate: float
    avg_execution_time: float
    user_preference: float
    context_suitability: Dict[str, float]


class SkillCoordinationOrgan(LifeOrgan):
    """林嫣然的技能协调器官"""
    
    def __init__(self, ai_adapter=None):
        super().__init__(
            organ_name="skill_coordination_organ",
            organ_function="智能技能协调和优化",
            ai_adapter=ai_adapter
        )
        
        # 技能相关组件
        self.skill_manager = None
        self.available_skills = {}
        self.skill_usage_history = []
        self.skill_combinations = []
        
        # 技能协调配置
        self.coordination_config = self._load_coordination_config()
        self.skill_categories = self._categorize_skills()
        
        # 性能统计
        self.coordination_stats = {
            "total_coordinations": 0,
            "successful_combinations": 0,
            "skill_usage_count": {},
            "avg_response_time": 0.0,
            "user_satisfaction": 0.0,
            "optimization_rate": 0.0
        }
        
        self.logger = get_unified_logger(__name__)
        self.logger.info("🧠 林嫣然技能协调器官初始化开始...")
        
        # 初始化技能协调系统
        self._init_skill_coordination()
        
        self.logger.info("🧠 林嫣然技能协调器官初始化完成")
    
    def _load_coordination_config(self) -> Dict[str, Any]:
        """加载技能协调配置"""
        try:
            config_path = os.path.join("config", "organs", "skill_coordination_organ.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"🧠 技能协调配置加载失败: {e}")
        
        # 默认配置
        return {
            "skill_selection_strategy": "intelligent",
            "combination_threshold": 0.7,
            "optimization_interval": 3600,
            "max_concurrent_skills": 3,
            "user_preference_weight": 0.6,
            "performance_weight": 0.4,
            "yanran_personality": {
                "preferred_skill_types": ["creative", "communication"],
                "skill_combination_style": "thoughtful",
                "learning_enthusiasm": 0.8
            }
        }
    
    def _categorize_skills(self) -> Dict[str, SkillCategory]:
        """对技能进行分类"""
        return {
            "chat_skill": SkillCategory.COMMUNICATION,
            "search_skill": SkillCategory.COMMUNICATION,
            "drawing_skill": SkillCategory.CREATIVE,
            "music_skill": SkillCategory.CREATIVE,
            "wechat_messaging": SkillCategory.SOCIAL,
            "financial_data": SkillCategory.ANALYTICAL,
            "greeting_skill": SkillCategory.UTILITY,
            "skill_template": SkillCategory.UTILITY
        }
    
    def _init_skill_coordination(self):
        """初始化技能协调系统"""
        try:
            # 获取技能管理器
            skill_manager = get("skill_manager")
            if skill_manager:
                self.skill_manager = skill_manager
                self._discover_available_skills()
                self.logger.info("🧠 技能管理器连接成功")
            else:
                self.logger.warning("🧠 技能管理器未找到，将使用基础协调功能")
            
            # 加载历史技能组合
            self._load_skill_combinations()
            
        except Exception as e:
            self.logger.warning(f"🧠 技能协调系统初始化部分失败: {e}")
    
    def _discover_available_skills(self):
        """发现可用技能"""
        try:
            if self.skill_manager:
                # 获取已注册的技能
                registered_skills = getattr(self.skill_manager, 'skills', {})
                for skill_name, skill_instance in registered_skills.items():
                    self.available_skills[skill_name] = {
                        "instance": skill_instance,
                        "category": self.skill_categories.get(skill_name, SkillCategory.UTILITY),
                        "enabled": True,
                        "performance_score": 1.0,
                        "last_used": None
                    }
                
                self.logger.info(f"🧠 发现 {len(self.available_skills)} 个可用技能")
                
        except Exception as e:
            self.logger.error(f"🧠 技能发现失败: {e}")
    
    def _load_skill_combinations(self):
        """加载技能组合方案"""
        try:
            combinations_path = os.path.join("data", "skill_combinations.json")
            if os.path.exists(combinations_path):
                with open(combinations_path, 'r', encoding='utf-8') as f:
                    combinations_data = json.load(f)
                    for combo_data in combinations_data:
                        combination = SkillCombination(**combo_data)
                        self.skill_combinations.append(combination)
                
                self.logger.info(f"🧠 加载了 {len(self.skill_combinations)} 个技能组合方案")
            else:
                # 创建默认组合
                self._create_default_combinations()
                
        except Exception as e:
            self.logger.warning(f"🧠 技能组合加载失败: {e}")
            self._create_default_combinations()
    
    def _create_default_combinations(self):
        """创建默认技能组合"""
        default_combinations = [
            SkillCombination(
                combination_id="creative_response",
                skills=["chat_skill", "drawing_skill"],
                purpose="创意回应：结合聊天和绘画",
                success_rate=0.8,
                avg_execution_time=3.5,
                user_preference=0.9,
                context_suitability={"creative": 0.9, "casual": 0.7}
            )
        ]
        
        self.skill_combinations = default_combinations
        self.logger.info("🧠 创建了默认技能组合方案")
    
    async def coordinate_skills(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """协调技能使用"""
        try:
            self.logger.info(f"🧠 开始技能协调: {user_input[:50]}...")
            
            # AI决策：选择最适合的技能或技能组合
            coordination_decision = await self._make_coordination_decision(user_input, context)
            
            if not coordination_decision.get("should_coordinate", False):
                return {
                    "coordinated": False,
                    "reason": coordination_decision.get("reasoning", "无需技能协调"),
                    "suggestion": coordination_decision.get("alternative_suggestion")
                }
            
            # 执行技能协调
            coordination_result = await self._execute_skill_coordination(coordination_decision, context)
            
            # 记录协调结果
            self._record_coordination(coordination_decision, coordination_result)
            
            return coordination_result
            
        except Exception as e:
            self.logger.error(f"🧠 技能协调失败: {e}")
            return {
                "coordinated": False,
                "error": str(e),
                "fallback_suggestion": "使用单一技能处理请求"
            }
    
    async def _make_coordination_decision(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """AI决策：技能协调策略"""
        try:
            # 构建决策上下文
            decision_context = {
                "user_input": user_input,
                "conversation_context": context.get("conversation", []),
                "available_skills": list(self.available_skills.keys()),
                "skill_categories": {name: cat.value for name, cat in self.skill_categories.items()},
                "recent_usage": self.skill_usage_history[-10:] if self.skill_usage_history else [],
                "yanran_preferences": self.coordination_config.get("yanran_personality", {})
            }
            
            # 使用AI决策引擎
            decision_result = await self.ai_decision_engine.decide(
                organ_prompt=self._get_coordination_decision_prompt(),
                context=decision_context,
                decision_type="skill_coordination"
            )
            
            return self._parse_coordination_decision(decision_result)
            
        except Exception as e:
            self.logger.error(f"🧠 技能协调决策失败: {e}")
            return {"should_coordinate": False, "reasoning": "决策过程出现错误"}
    
    def _get_coordination_decision_prompt(self) -> str:
        """获取技能协调决策提示词"""
        return """
        你是林嫣然的技能协调器官，负责智能地选择和组合技能来最好地回应用户。

        林嫣然的技能协调特点：
        - 偏好创意和沟通类技能
        - 善于将多种技能巧妙结合
        - 注重用户体验和回应质量
        - 会根据情境选择最合适的技能组合
        - 学习能力强，会优化技能使用策略

        请基于以下信息做出技能协调决策：
        1. 分析用户输入的意图和需求
        2. 评估可用技能和历史组合的适用性
        3. 考虑林嫣然的个性偏好
        4. 决定是否需要技能协调，如何协调

        返回JSON格式，包含：
        - should_coordinate: 是否需要协调
        - coordination_type: single/combination/sequence
        - selected_skills: 选择的技能列表
        - reasoning: 决策理由
        """
    
    def _parse_coordination_decision(self, decision_result) -> Dict[str, Any]:
        """解析技能协调决策结果"""
        try:
            if isinstance(decision_result, str):
                # 尝试解析JSON
                import re
                json_match = re.search(r'\{.*\}', decision_result, re.DOTALL)
                if json_match:
                    decision_result = json.loads(json_match.group())
                else:
                    # 如果无法解析，返回默认决策
                    return {"should_coordinate": False, "reasoning": "决策结果解析失败"}
            
            return {
                "should_coordinate": decision_result.get("should_coordinate", False),
                "coordination_type": decision_result.get("coordination_type", "single"),
                "selected_skills": decision_result.get("selected_skills", []),
                "reasoning": decision_result.get("reasoning", "")
            }
            
        except Exception as e:
            self.logger.error(f"🧠 决策结果解析失败: {e}")
            return {"should_coordinate": False, "reasoning": "决策结果解析错误"}
    
    async def _execute_skill_coordination(self, decision: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行技能协调"""
        try:
            coordination_type = decision.get("coordination_type", "single")
            selected_skills = decision.get("selected_skills", [])
            
            start_time = datetime.now()
            results = {}
            
            if coordination_type == "single" and selected_skills:
                # 单一技能执行
                skill_name = selected_skills[0]
                result = await self._execute_single_skill(skill_name, context)
                results[skill_name] = result
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "coordinated": True,
                "coordination_type": coordination_type,
                "skills_used": selected_skills,
                "execution_time": execution_time,
                "results": results,
                "success": all(r.get("success", False) for r in results.values()),
                "yanran_comment": f"我使用了{', '.join(selected_skills)}来帮助你"
            }
            
        except Exception as e:
            self.logger.error(f"🧠 技能协调执行失败: {e}")
            return {
                "coordinated": False,
                "error": str(e),
                "skills_attempted": selected_skills
            }
    
    async def _execute_single_skill(self, skill_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行单一技能"""
        try:
            if skill_name in self.available_skills:
                skill_info = self.available_skills[skill_name]
                skill_instance = skill_info["instance"]
                
                # 调用技能的execute方法
                if hasattr(skill_instance, 'execute'):
                    result = await skill_instance.execute(context.get("user_input", ""), context)
                    return {"success": True, "result": result}
                else:
                    return {"success": False, "error": "技能无execute方法"}
            else:
                return {"success": False, "error": "技能不存在"}
                
        except Exception as e:
            self.logger.error(f"🧠 单一技能执行失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _record_coordination(self, decision: Dict[str, Any], result: Dict[str, Any]):
        """记录技能协调结果"""
        try:
            # 记录使用的技能
            skills_used = decision.get("selected_skills", [])
            for skill_name in skills_used:
                usage_record = SkillUsageRecord(
                    skill_name=skill_name,
                    category=self.skill_categories.get(skill_name, SkillCategory.UTILITY),
                    usage_time=datetime.now(),
                    success=result.get("success", False),
                    execution_time=result.get("execution_time", 0.0),
                    user_satisfaction=None,
                    context={"coordination_type": decision.get("coordination_type")},
                    result_quality=None
                )
                self.skill_usage_history.append(usage_record)
            
            # 限制历史记录数量
            if len(self.skill_usage_history) > 1000:
                self.skill_usage_history = self.skill_usage_history[-1000:]
            
            # 更新统计
            self.coordination_stats["total_coordinations"] += 1
            if result.get("success", False):
                self.coordination_stats["successful_combinations"] += 1
            
            self.logger.info(f"🧠 技能协调记录已保存: {skills_used}")
            
        except Exception as e:
            self.logger.error(f"🧠 技能协调记录失败: {e}")
    
    def get_coordination_stats(self) -> Dict[str, Any]:
        """获取技能协调统计"""
        return {
            "stats": self.coordination_stats.copy(),
            "available_skills": list(self.available_skills.keys()),
            "skill_combinations": len(self.skill_combinations),
            "usage_history_count": len(self.skill_usage_history),
            "config": self.coordination_config
        }
    
    # 实现基类的抽象方法
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定的AI提示词"""
        return self._get_coordination_decision_prompt()
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        try:
            user_input = processed_input.get("user_input", "")
            context = processed_input.get("context", {})
            
            # 进行技能协调
            result = await self.coordinate_skills(user_input, context)
            
            return {
                "success": True,
                "coordination_result": result,
                "organ_response": result
            }
            
        except Exception as e:
            self.logger.error(f"🧠 执行技能协调决策失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "organ_response": {"coordinated": False, "reason": "执行过程出现错误"}
            }


# 单例获取函数
def get_instance(ai_adapter=None) -> SkillCoordinationOrgan:
    """获取技能协调器官实例"""
    # 使用get_or_create避免重复检查
    from utilities.singleton_manager import get_or_create
    return get_or_create("skill_coordination_organ", lambda: SkillCoordinationOrgan(ai_adapter=ai_adapter)) 