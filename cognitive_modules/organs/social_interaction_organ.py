#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
社交互动器官

负责社交关系管理、互动策略和沟通协调。

作者: Claude
创建日期: 2025-06-30
版本: 1.0
"""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from utilities.unified_logger import get_unified_logger
from utilities.singleton_manager import get_singleton_manager
from cognitive_modules.life_organ_base import LifeOrganBase
from cognitive_modules.ai.yanran_decision_engine import YanranAIDecisionEngine

logger = get_unified_logger("social_interaction_organ")
singleton_manager = get_singleton_manager()

class SocialInteractionOrgan(LifeOrganBase):
    """社交互动器官"""
    
    def __init__(self):
        """初始化社交互动器官"""
        super().__init__(
            organ_name="social_interaction_organ",
            description="社交关系管理和互动协调",
            dependencies=["emotional_processing_organ"]
        )
        
        self.logger = logger
        self.user_profiles = {}
        self.interaction_history = []
        self.social_context = {
            "active_conversations": 0,
            "last_interaction": None,
            "interaction_mode": "friendly",
            "social_energy": 0.8
        }
        
        # 初始化社交系统
        self._init_social_system()
        
        logger.info("👥 林嫣然社交互动器官初始化完成")
    
    def _init_social_system(self):
        """初始化社交系统"""
        try:
            # 互动模式
            self.interaction_modes = {
                "professional": {"formality": 0.8, "warmth": 0.4},
                "friendly": {"formality": 0.4, "warmth": 0.8},
                "casual": {"formality": 0.2, "warmth": 0.6},
                "supportive": {"formality": 0.3, "warmth": 0.9}
            }
            
            # 沟通风格
            self.communication_styles = {
                "direct": {"clarity": 0.9, "diplomacy": 0.3},
                "diplomatic": {"clarity": 0.6, "diplomacy": 0.9},
                "balanced": {"clarity": 0.7, "diplomacy": 0.7},
                "empathetic": {"clarity": 0.5, "diplomacy": 0.8}
            }
            
            logger.success("👥 社交系统初始化完成")
            
        except Exception as e:
            logger.error(f"👥 社交系统初始化失败: {e}")
    
    def process_interaction(self, user_id: str, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理社交互动
        
        Args:
            user_id: 用户ID
            message: 用户消息
            context: 互动上下文
            
        Returns:
            互动处理结果
        """
        try:
            context = context or {}
            
            # 更新用户档案
            self._update_user_profile(user_id, message, context)
            
            # 分析互动意图
            interaction_analysis = self._analyze_interaction(message, context)
            
            # 选择响应策略
            response_strategy = self._select_response_strategy(user_id, interaction_analysis)
            
            # 生成响应
            response = self._generate_response(message, interaction_analysis, response_strategy)
            
            # 记录互动历史
            interaction_record = {
                "timestamp": datetime.now(),
                "user_id": user_id,
                "message": message,
                "response": response,
                "analysis": interaction_analysis,
                "strategy": response_strategy
            }
            
            self.interaction_history.append(interaction_record)
            self._update_social_context()
            
            logger.success(f"👥 处理了与用户 {user_id} 的互动")
            
            return {
                "success": True,
                "response": response,
                "strategy": response_strategy,
                "analysis": interaction_analysis,
                "user_profile": self.user_profiles.get(user_id, {})
            }
            
        except Exception as e:
            logger.error(f"👥 处理互动失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，我在处理您的消息时遇到了问题。"
            }
    
    def _update_user_profile(self, user_id: str, message: str, context: Dict[str, Any]):
        """更新用户档案"""
        try:
            if user_id not in self.user_profiles:
                self.user_profiles[user_id] = {
                    "first_interaction": datetime.now(),
                    "interaction_count": 0,
                    "preferences": {},
                    "communication_style": "unknown",
                    "relationship_level": "new",
                    "last_interaction": None
                }
            
            profile = self.user_profiles[user_id]
            profile["interaction_count"] += 1
            profile["last_interaction"] = datetime.now()
            
            # 分析用户偏好
            self._analyze_user_preferences(profile, message, context)
            
            # 更新关系等级
            self._update_relationship_level(profile)
            
            logger.success(f"👥 用户 {user_id} 档案已更新")
            
        except Exception as e:
            logger.error(f"👥 更新用户档案失败: {e}")
    
    def _analyze_user_preferences(self, profile: Dict[str, Any], message: str, context: Dict[str, Any]):
        """分析用户偏好"""
        try:
            # 简单的偏好分析
            preferences = profile.get("preferences", {})
            
            # 分析沟通风格
            if "请" in message or "谢谢" in message:
                preferences["politeness"] = preferences.get("politeness", 0) + 0.1
            
            if "?" in message or "？" in message:
                preferences["questioning"] = preferences.get("questioning", 0) + 0.1
            
            if len(message) > 100:
                preferences["detailed"] = preferences.get("detailed", 0) + 0.1
            else:
                preferences["concise"] = preferences.get("concise", 0) + 0.1
            
            # 分析话题偏好
            topics = ["财经", "技术", "生活", "娱乐", "学习"]
            for topic in topics:
                if topic in message:
                    preferences[f"topic_{topic}"] = preferences.get(f"topic_{topic}", 0) + 0.1
            
            profile["preferences"] = preferences
            
        except Exception as e:
            logger.error(f"👥 分析用户偏好失败: {e}")
    
    def _update_relationship_level(self, profile: Dict[str, Any]):
        """更新关系等级"""
        try:
            interaction_count = profile["interaction_count"]
            
            if interaction_count >= 20:
                profile["relationship_level"] = "close"
            elif interaction_count >= 10:
                profile["relationship_level"] = "familiar"
            elif interaction_count >= 3:
                profile["relationship_level"] = "acquainted"
            else:
                profile["relationship_level"] = "new"
                
        except Exception as e:
            logger.error(f"👥 更新关系等级失败: {e}")
    
    def _analyze_interaction(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """分析互动意图"""
        try:
            # 情感分析
            emotion = self._detect_emotion(message)
            
            # 意图分析
            intent = self._detect_intent(message)
            
            # 紧急程度分析
            urgency = self._detect_urgency(message)
            
            return {
                "emotion": emotion,
                "intent": intent,
                "urgency": urgency,
                "message_length": len(message),
                "has_question": "?" in message or "？" in message,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"👥 分析互动失败: {e}")
            return {
                "emotion": "neutral",
                "intent": "general",
                "urgency": 0.5,
                "message_length": len(message),
                "has_question": False,
                "timestamp": datetime.now()
            }
    
    def _detect_emotion(self, message: str) -> str:
        """检测情感"""
        positive_words = ["开心", "高兴", "喜欢", "棒", "好", "谢谢"]
        negative_words = ["难过", "生气", "讨厌", "坏", "糟糕", "失望"]
        
        for word in positive_words:
            if word in message:
                return "positive"
        
        for word in negative_words:
            if word in message:
                return "negative"
        
        return "neutral"
    
    def _detect_intent(self, message: str) -> str:
        """检测意图"""
        if "?" in message or "？" in message:
            return "question"
        elif "请" in message or "帮助" in message:
            return "request"
        elif "谢谢" in message or "感谢" in message:
            return "gratitude"
        elif "再见" in message or "拜拜" in message:
            return "farewell"
        else:
            return "general"
    
    def _detect_urgency(self, message: str) -> float:
        """检测紧急程度"""
        urgent_words = ["紧急", "急", "马上", "立即", "快"]
        urgency = 0.3  # 基础紧急度
        
        for word in urgent_words:
            if word in message:
                urgency += 0.2
        
        return min(1.0, urgency)
    
    def _select_response_strategy(self, user_id: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """选择响应策略"""
        try:
            profile = self.user_profiles.get(user_id, {})
            relationship_level = profile.get("relationship_level", "new")
            
            # 根据关系等级选择策略
            if relationship_level == "close":
                formality = 0.2
                warmth = 0.9
            elif relationship_level == "familiar":
                formality = 0.3
                warmth = 0.8
            elif relationship_level == "acquainted":
                formality = 0.5
                warmth = 0.6
            else:
                formality = 0.7
                warmth = 0.5
            
            # 根据情感调整策略
            if analysis["emotion"] == "negative":
                warmth += 0.2
                formality -= 0.1
            elif analysis["emotion"] == "positive":
                warmth += 0.1
            
            # 根据意图调整策略
            if analysis["intent"] == "request":
                formality += 0.1
            elif analysis["intent"] == "gratitude":
                warmth += 0.1
            
            return {
                "formality": max(0.1, min(1.0, formality)),
                "warmth": max(0.1, min(1.0, warmth)),
                "response_length": "medium" if analysis["message_length"] > 50 else "short",
                "include_emotion": analysis["emotion"] != "neutral"
            }
            
        except Exception as e:
            logger.error(f"👥 选择响应策略失败: {e}")
            return {
                "formality": 0.5,
                "warmth": 0.6,
                "response_length": "medium",
                "include_emotion": False
            }
    
    def _generate_response(self, message: str, analysis: Dict[str, Any], strategy: Dict[str, Any]) -> str:
        """生成响应"""
        try:
            # 基础响应模板
            responses = {
                "question": "这是一个很好的问题。",
                "request": "我会尽力帮助您。",
                "gratitude": "不客气，很高兴能帮到您！",
                "farewell": "再见，期待下次与您交流！",
                "general": "我理解您的意思。"
            }
            
            base_response = responses.get(analysis["intent"], responses["general"])
            
            # 根据策略调整响应
            if strategy["warmth"] > 0.7:
                base_response = "😊 " + base_response
            
            if strategy["formality"] > 0.7:
                base_response = "您好，" + base_response
            
            return base_response
            
        except Exception as e:
            logger.error(f"👥 生成响应失败: {e}")
            return "我理解您的意思，让我来帮助您。"
    
    def _update_social_context(self):
        """更新社交上下文"""
        try:
            self.social_context["last_interaction"] = datetime.now()
            
            # 更新活跃对话数
            recent_interactions = [
                record for record in self.interaction_history[-10:]
                if (datetime.now() - record["timestamp"]).seconds < 300  # 5分钟内
            ]
            self.social_context["active_conversations"] = len(set(
                record["user_id"] for record in recent_interactions
            ))
            
            # 调整社交能量
            if len(recent_interactions) > 5:
                self.social_context["social_energy"] = max(0.1, self.social_context["social_energy"] - 0.1)
            else:
                self.social_context["social_energy"] = min(1.0, self.social_context["social_energy"] + 0.05)
                
        except Exception as e:
            logger.error(f"👥 更新社交上下文失败: {e}")
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """获取用户档案"""
        return self.user_profiles.get(user_id, {})
    
    def get_interaction_history(self, user_id: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """获取互动历史"""
        if user_id:
            user_history = [
                record for record in self.interaction_history
                if record["user_id"] == user_id
            ]
            return user_history[-limit:]
        else:
            return self.interaction_history[-limit:]
    
    def get_social_stats(self) -> Dict[str, Any]:
        """获取社交统计"""
        return {
            "total_users": len(self.user_profiles),
            "total_interactions": len(self.interaction_history),
            "active_conversations": self.social_context["active_conversations"],
            "social_energy": self.social_context["social_energy"],
            "last_interaction": self.social_context["last_interaction"]
        }
    
    def get_status(self) -> Dict[str, Any]:
        """获取器官状态"""
        return {
            "organ_name": self.organ_name,
            "is_active": True,
            "social_context": self.social_context.copy(),
            "user_count": len(self.user_profiles),
            "interaction_count": len(self.interaction_history),
            "health_status": "healthy"
        }

# 工厂函数
def create_social_interaction_organ() -> SocialInteractionOrgan:
    """创建社交互动器官实例"""
    return SocialInteractionOrgan()

# 向后兼容
def get_instance() -> SocialInteractionOrgan:
    """获取社交互动器官实例"""
    return singleton_manager.get_or_create(
        'social_interaction_organ',
        create_social_interaction_organ
    ) 