"""
林嫣然主动表达器官 - 表达系统优化
作为林嫣然的"表达冲动"，负责主动分享想法和情感
"""

import asyncio
import json
import os
import random
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum
import time
import concurrent.futures
import threading
from collections import Counter
from typing import Dict, Any, Optional, List, Union

from .base_organ import LifeOrgan
from ..ai.yanran_decision_engine import YanranAIDecisionEngine
from utilities.singleton_manager import get, register, get_silent


class ExpressionType(Enum):
    """表达类型枚举"""
    GREETING = "greeting"
    SHARING = "sharing"
    CARING = "caring"
    INSIGHT = "insight"
    REFLECTION = "reflection"
    ENCOURAGEMENT = "encouragement"
    CREATIVITY = "creativity"
    MOOD_EXPRESSION = "mood_expression"


class ExpressionTrigger(Enum):
    """表达触发器枚举"""
    TIME_BASED = "time_based"
    EVENT_BASED = "event_based"
    EMOTION_BASED = "emotion_based"
    CONTEXT_BASED = "context_based"
    USER_BASED = "user_based"
    MEMORY_BASED = "memory_based"


class TriggerCategory(Enum):
    """🔥 老王新增：触发器分类枚举 - 支持并行触发调度"""
    TIME_SENSITIVE = "time_sensitive"      # 时间敏感：早安问候
    EVENT_DRIVEN = "event_driven"          # 事件驱动：世界感知
    EMOTION_BASED = "emotion_based"        # 情感驱动：情感变化
    CREATIVE_IMPULSE = "creative_impulse"  # 创意冲动：创意表达
    MEMORY_RECALL = "memory_recall"        # 记忆回忆：记忆触发


class TriggerConflictRule:
    """🔥 老王新增：触发器冲突规则 - 模拟人类大脑的资源管理"""

    # 互斥组：同一时间只能执行一个
    MUTUAL_EXCLUSIVE_GROUPS = [
        # 微信消息发送互斥（避免消息轰炸）
        ["morning_greeting", "event_based", "creative_based"],
        # 情感类表达互斥（避免情感混乱）
        ["emotion_based", "memory_based"]
    ]

    # 资源冲突：需要相同资源的触发器
    RESOURCE_CONFLICTS = {
        "wechat_push": ["morning_greeting", "event_based", "creative_based"],
        "ai_generation": ["event_based", "creative_based", "emotion_based"],
        "user_attention": ["morning_greeting", "event_based"]  # 需要用户注意力的表达
    }


@dataclass
class ExpressionContext:
    """表达上下文"""
    trigger_type: ExpressionTrigger
    expression_type: ExpressionType
    context_data: Dict[str, Any]
    user_info: Optional[Dict[str, Any]]
    timestamp: datetime
    priority: int  # 1-10，10最高
    mood_state: Optional[str] = None
    recent_interactions: Optional[List[Dict]] = None


@dataclass
class ExpressionResult:
    """表达结果"""
    expression_id: str
    expression_type: ExpressionType
    content: str
    delivery_method: str
    target_audience: str
    success: bool
    timestamp: datetime
    user_feedback: Optional[str] = None
    emotional_impact: Optional[float] = None


class ProactiveExpressionOrgan(LifeOrgan):
    """林嫣然的主动表达器官"""
    
    def __init__(self, ai_adapter=None):
        super().__init__(
            organ_name="proactive_expression_organ",
            organ_function="主动表达和情感分享",
            ai_adapter=ai_adapter
        )
        
        # 表达相关组件
        self.expression_history = []
        self.expression_patterns = {}
        self.mood_tracker = {}
        self.user_interactions = {}

        # 🔥 老王修复：表达事件去重机制使用Redis持久化
        self.last_expression_cleanup = None  # 最后清理时间
        
        # 表达配置
        self.expression_config = self._load_expression_config()
        
        # 表达触发器
        self.expression_triggers = {
            ExpressionTrigger.TIME_BASED: self._time_based_trigger,
            ExpressionTrigger.EVENT_BASED: self._event_based_trigger,
            ExpressionTrigger.EMOTION_BASED: self._emotion_based_trigger,
            ExpressionTrigger.CONTEXT_BASED: self._context_based_trigger,
            ExpressionTrigger.USER_BASED: self._user_based_trigger,
            ExpressionTrigger.MEMORY_BASED: self._memory_based_trigger
        }
        
        # 性能统计
        self.expression_stats = {
            "total_expressions": 0,
            "successful_expressions": 0,
            "user_positive_feedback": 0,
            "avg_emotional_impact": 0.0,
            "expression_frequency": 0.0,
            "most_used_type": None,
            "user_engagement_rate": 0.0
        }
        
        from utilities.unified_logger import get_unified_logger
        self.logger = get_unified_logger("ProactiveExpressionOrgan")
        self.logger.info("💬 林嫣然主动表达器官初始化开始...")
        
        # 初始化表达系统
        self._init_expression_system()

        # 🔥 老王修复：标记监控系统状态
        self._monitoring_started = False
        
        # 🔥 香草修复：启动表达策略学习机制
        self._start_expression_strategy_learning()

        self.logger.info("💬 林嫣然主动表达器官初始化完成")

        # 🔥 老王新增：输出当前触发模式
        trigger_mode = self.expression_config.get("trigger_mode", "parallel")
        self.logger.info(f"💬 🧠 当前触发模式: {trigger_mode.upper()}")
        if trigger_mode == "parallel":
            parallel_config = self.expression_config.get("parallel_config", {})
            max_concurrent = parallel_config.get("max_concurrent_expressions", 2)
            min_interval = parallel_config.get("min_interval_between_expressions", 300)
            self.logger.info(f"💬 🧠 并行配置: 最大并发={max_concurrent}, 最小间隔={min_interval//60}分钟")

    def activate(self):
        """🔥 老王新增：器官激活方法，供器官系统管理器调用"""
        try:
            self.logger.info("💬 🚀 主动表达器官正在激活...")

            # 确保表达监控系统已启动
            if not hasattr(self, '_monitoring_started') or not self._monitoring_started:
                self._start_expression_monitoring()
                self._monitoring_started = True
                self.logger.success("💬 ✅ 主动表达器官激活成功，监控系统已启动")
            else:
                self.logger.info("💬 ✅ 主动表达器官已激活，监控系统运行中")

            return True

        except Exception as e:
            self.logger.error(f"💬 ❌ 主动表达器官激活失败: {e}")
            return False

    def deactivate(self):
        """🔥 老王新增：器官停用方法"""
        try:
            self.logger.info("💬 🛑 主动表达器官正在停用...")
            self._monitoring_started = False
            self.logger.success("💬 ✅ 主动表达器官已停用")
            return True
        except Exception as e:
            self.logger.error(f"💬 ❌ 主动表达器官停用失败: {e}")
            return False
    
    def _load_expression_config(self) -> Dict[str, Any]:
        """加载表达配置"""
        default_config = {
            "enabled": True,
            "check_interval": 10800,  # 🔥 老王修复：检查间隔改为3小时，提高响应性
            "max_daily_expressions": 4,   # 🔥 老王修复：每日最多4次表达，适中频率
            "min_expression_interval": 10800,  # 🔥 老王修复：最小间隔改为3小时
            "expression_probability": 0.4,  # 🔥 老王修复：提高触发概率到0.4

            # 🔥 老王新增：并行触发配置 - 数字生命多任务处理能力
            "trigger_mode": "parallel",  # "serial" | "parallel" - 默认并行模式
            "parallel_config": {
                "max_concurrent_expressions": 2,  # 最大并发表达数
                "min_interval_between_expressions": 300,  # 表达间最小间隔(秒) - 5分钟
                "conflict_resolution": "intelligent_priority",  # 冲突解决策略
                "enable_delayed_execution": True,  # 启用延迟执行
                "time_sensitive_priority": True  # 时间敏感触发器优先
            },
            "yanran_expression_style": {
                "warmth": 0.9,
                "authenticity": 0.95,
                "intelligence": 0.8,
                "creativity": 0.85,
                "empathy": 0.9,
                "humor": 0.6,
                "depth": 0.8,
                "spontaneity": 0.7  # 🔥 老王修复：增加自发性
            },
            "trigger_conditions": {
                "time_based": {
                    "enabled": True,
                    "probability": 0.8  # 🔥 老王修复：提高时间触发概率
                },
                "event_based": {
                    "enabled": True,
                    "probability": 0.6
                },
                "emotion_based": {
                    "enabled": True,
                    "probability": 0.5
                },
                "context_based": {
                    "enabled": True,
                    "probability": 0.4
                }
            }
        }
        
        # 🔥 老王修复：必须返回配置字典！
        return default_config

    def set_trigger_mode(self, mode: str):
        """🔥 老王新增：动态设置触发模式"""
        if mode not in ["serial", "parallel"]:
            self.logger.error(f"💬 🧠 无效的触发模式: {mode}，只支持 'serial' 或 'parallel'")
            return False

        old_mode = self.expression_config.get("trigger_mode", "parallel")
        self.expression_config["trigger_mode"] = mode
        self.logger.info(f"💬 🧠 触发模式已从 {old_mode.upper()} 切换到 {mode.upper()}")
        return True

    def get_trigger_mode(self) -> str:
        """🔥 老王新增：获取当前触发模式"""
        return self.expression_config.get("trigger_mode", "parallel")
    
    def _load_wechat_config(self) -> Dict[str, Any]:
        """
        加载微信配置文件 - 🔥 修复问题3：早安问候时间配置
        
        Returns:
            微信配置字典
        """
        try:
            config_path = os.path.join("config", "wechat_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.logger.debug(f"💬 成功加载微信配置: {config_path}")
                    return config
        except Exception as e:
            self.logger.warning(f"💬 微信配置加载失败: {e}")
        
        # 🔥 默认配置 - 如果配置文件不存在
        default_config = {
            "morning_greeting": {
                "enabled": True,
                "time_range": {
                    "start_hour": 8,
                    "start_minute": 30,
                    "end_hour": 8,
                    "end_minute": 50
                }
            },
            "auto_reply": {
                "enabled": True,
                "delay_seconds": 3
            }
        }
        
        self.logger.warning("💬 使用默认微信配置")
        return default_config
    
    def _init_expression_system(self):
        """初始化表达系统"""
        try:
            # 🔥 老王修复：使用get_silent避免获取不存在实例的警告
            from utilities.singleton_manager import get_silent, register
            proactive_service = get_silent("proactive_expression_service")
            if not proactive_service:
                # 如果服务不存在，尝试创建
                try:
                    from services.proactive_expression_service import get_proactive_service
                    proactive_service = get_proactive_service()
                    register("proactive_expression_service", proactive_service)
                    self.logger.info("💬 主动表达服务已创建并注册")
                except Exception as e:
                    self.logger.warning(f"💬 创建主动表达服务失败: {e}")
            
            if proactive_service:
                self.proactive_service = proactive_service
                self.logger.info("💬 主动表达服务连接成功")
                
                # 🔥 P0级别修复：不启动WebSocket服务器，使用WeChat统一推送服务
                self.logger.info("💬 使用WeChat统一推送服务，不启动WebSocket服务器")
            else:
                self.logger.warning("💬 主动表达服务未找到")
            
            # 🔥 集成感知反馈处理器
            try:
                from core.perception_feedback_processor import get_perception_feedback_processor
                perception_processor = get_perception_feedback_processor()
                if perception_processor:
                    self.perception_processor = perception_processor
                    self.logger.info("💬 感知反馈处理器连接成功")
                    
                    # 注册感知结果回调
                    self._register_perception_callback()
                else:
                    self.logger.warning("💬 感知反馈处理器未找到")
            except Exception as e:
                self.logger.warning(f"💬 集成感知反馈处理器失败: {e}")
            
            # 🔥 集成统一好友管理器
            try:
                from core.unified_user_manager import get_unified_user_manager
                user_manager = get_unified_user_manager()
                if user_manager:
                    self.user_manager = user_manager
                    self.logger.info("💬 统一好友管理器连接成功")
                else:
                    self.logger.warning("💬 统一好友管理器未找到")
            except Exception as e:
                self.logger.warning(f"💬 集成统一好友管理器失败: {e}")
            
            # 🔥 获取情感系统（优先使用新的集成系统）
            try:
                from cognitive_modules.emotion.emotion_system_integration import get_emotion_system_integration
                emotion_system = get_emotion_system_integration()
                if emotion_system:
                    self.emotion_engine = emotion_system
                    self.logger.info("💬 情感系统集成连接成功")
                else:
                    # 回退到原始情感引擎
                    emotion_engine = get_silent("emotion_engine")
                    if emotion_engine:
                        self.emotion_engine = emotion_engine
                        self.logger.info("💬 情感引擎连接成功")
                    else:
                        self.logger.warning("💬 情感引擎未找到")
            except Exception as e:
                self.logger.warning(f"💬 情感系统连接失败: {e}")
                # 回退到原始情感引擎
                emotion_engine = get_silent("emotion_engine")
                if emotion_engine:
                    self.emotion_engine = emotion_engine
                    self.logger.info("💬 情感引擎连接成功（回退模式）")
                else:
                    self.logger.warning("💬 情感引擎未找到")
            
            # 初始化表达模式
            self._init_expression_patterns()
            
            # 🔥 老王性能优化：预加载工作日缓存，避免首次查询延迟
            try:
                from utilities.workday_cache_manager import preload_workday_cache
                preload_workday_cache()
                self.logger.info("💬 🗓️ 工作日缓存预加载完成")
            except Exception as e:
                self.logger.warning(f"💬 🗓️ 工作日缓存预加载失败: {e}")

            # 🔥 老王修复：延迟启动监控任务，等待器官系统管理器调用activate
            # 这样可以确保所有依赖组件都已初始化完成
            self.logger.info("💬 ⏳ 表达监控任务将在器官激活时启动")
            
        except Exception as e:
            self.logger.warning(f"💬 表达系统初始化部分失败: {e}")

    def _start_expression_strategy_learning(self):
        """启动表达策略学习机制 - 🔥 香草修复：基于反馈的表达优化"""
        try:
            import threading
            import time

            def strategy_learning_worker():
                """表达策略学习工作线程"""
                while True:
                    try:
                        time.sleep(3600)  # 每小时执行一次

                        # 分析表达效果
                        self._analyze_expression_effectiveness()

                        # 学习用户反馈模式
                        self._learn_user_feedback_patterns()

                        # 优化表达时机
                        self._optimize_expression_timing()

                        # 更新表达策略
                        self._update_expression_strategies()

                        self.logger.debug("💬 表达策略学习完成")

                    except Exception as e:
                        self.logger.error(f"表达策略学习异常: {e}")

            # 启动后台学习线程
            learning_thread = threading.Thread(target=strategy_learning_worker, daemon=True)
            learning_thread.start()

            self.logger.debug("💬 表达策略学习机制已启动")

        except Exception as e:
            self.logger.error(f"启动表达策略学习机制失败: {e}")

    def _init_expression_patterns(self):
        """初始化表达模式"""
        self.expression_patterns = {
            ExpressionType.GREETING: {
                "triggers": [ExpressionTrigger.TIME_BASED, ExpressionTrigger.USER_BASED],
                "frequency": "daily",
                "templates": [
                    "早安！新的一天开始了，希望你精神饱满～",
                    "中午好！记得要好好吃饭，补充能量哦～", 
                    "下午好！工作之余也要记得适当休息～",
                    "晚安，愿你今夜好梦，明天更美好～"
                ]
            },
            ExpressionType.SHARING: {
                "triggers": [ExpressionTrigger.EVENT_BASED, ExpressionTrigger.EMOTION_BASED],
                "frequency": "event_driven",
                "templates": [
                    "刚才想到一个有趣的事情想和你分享...",
                    "今天遇到了让我印象深刻的事情...",
                    "有个想法想听听你的看法..."
                ]
            }
        }
    
    def _start_expression_monitoring(self):
        """启动表达监控系统"""
        try:
            self.logger.info("💬 🚀 启动统一表达监控系统...")

            # 🔥 老王修复：只使用一个监控系统，避免冲突
            # 优先使用定时器监控，因为更稳定可靠
            self._start_timer_based_monitoring()

            self.logger.success("💬 ✅ 表达监控系统启动成功")

        except Exception as e:
            self.logger.error(f"💬 ❌ 表达监控任务启动失败: {e}")
            # 🔥 降级处理：至少记录错误
            self.logger.warning("💬 ⚠️ 表达监控系统启动失败，主动表达功能可能受影响")

    def _start_intelligent_monitoring(self):
        """启动智能监控系统"""
        try:
            # 🔥 修复：使用更健壮的导入方式，增加多重保护
            try:
                import sys
                import os

                # 确保模块路径正确
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.dirname(os.path.dirname(current_dir))
                if project_root not in sys.path:
                    sys.path.insert(0, project_root)

                # 🔥 新增：检查模块文件是否存在
                module_path = os.path.join(current_dir, "intelligent_expression_triggers.py")
                if not os.path.exists(module_path):
                    self.logger.error(f"🧠 智能触发器模块文件不存在: {module_path}")
                    self.logger.warning("⚠️ 🧠 降级到定时器模式")
                    return

                # 🔥 新增：尝试多种导入方式
                try:
                    # 方式1：直接导入
                    from cognitive_modules.organs.intelligent_expression_triggers import get_intelligent_expression_triggers
                    self.logger.debug("🧠 智能触发器模块导入成功（方式1）")
                except ImportError:
                    try:
                        # 方式2：相对导入
                        from .intelligent_expression_triggers import get_intelligent_expression_triggers
                        self.logger.debug("🧠 智能触发器模块导入成功（方式2）")
                    except ImportError:
                        # 方式3：动态导入
                        import importlib.util
                        spec = importlib.util.spec_from_file_location("intelligent_expression_triggers", module_path)
                        module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(module)
                        get_intelligent_expression_triggers = module.get_intelligent_expression_triggers
                        self.logger.debug("🧠 智能触发器模块导入成功（方式3）")

            except (ImportError, ModuleNotFoundError) as e:
                self.logger.error(f"🧠 智能触发器模块导入失败: {e}")
                self.logger.warning("⚠️ 🧠 降级到定时器模式")
                return
            except Exception as e:
                self.logger.error(f"🧠 智能触发器模块加载异常: {e}")
                import traceback
                self.logger.error(f"🧠 详细错误信息: {traceback.format_exc()}")
                self.logger.warning("⚠️ 🧠 降级到定时器模式")
                return

            self.intelligent_triggers = get_intelligent_expression_triggers()

            # 🔥 在独立线程中启动智能监控
            import threading
            def start_intelligent_monitoring_in_thread():
                import time
                # 等待系统启动
                time.sleep(60)  # 等待1分钟让系统完全启动
                try:
                    # 创建新的事件循环
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    self.logger.info("🧠 🚀 智能表达监控系统启动")
                    new_loop.run_until_complete(self.intelligent_triggers.start_intelligent_monitoring())
                except Exception as e:
                    self.logger.error(f"🧠 智能监控启动失败: {e}")
                finally:
                    new_loop.close()

            thread = threading.Thread(
                target=start_intelligent_monitoring_in_thread,
                daemon=True,
                name="IntelligentExpressionMonitor"
            )
            thread.start()
            self.logger.success("🧠 ✅ 智能表达监控系统已启动")

        except Exception as e:
            self.logger.error(f"🧠 智能监控启动失败: {e}")
    
    def _register_perception_callback(self):
        """注册感知结果回调"""
        try:
            # 这里可以注册回调函数，当有新的感知结果时自动触发表达
            # 由于感知反馈处理器已经有trigger_expression_from_perception方法
            # 这里主要是建立连接确认
            self.logger.info("💬 感知结果回调已注册")
        except Exception as e:
            self.logger.warning(f"💬 注册感知回调失败: {e}")
    
    def set_current_user(self, user_id: str):
        """设置当前好友ID（用于获取好友信息和交互历史）"""
        self.current_user_id = user_id
        self.logger.debug(f"💬 设置当前好友: {user_id}")
    
    async def send_proactive_message_via_wechat(self, content: str, user_id: str = None) -> bool:
        """🔥 P0级别修复：直接修复事件循环问题"""
        try:
            target_user_id = user_id or getattr(self, 'current_user_id', 'wxid_fknm4p9g74pn21')
            
            # 🔥 P0级别修复：直接使用WeChat统一推送服务，修复事件循环问题
            try:
                from services.wechat_unified_push_service import get_wechat_unified_push_service
                wechat_push_service = get_wechat_unified_push_service()
                
                # 🔥 P0级别调试：添加详细的WeChat推送调试信息
                self.logger.info(f"🔍 创意表达WeChat推送服务状态: {'可用' if wechat_push_service else '不可用'}")
                
                if not wechat_push_service:
                    self.logger.error("创意表达WeChat推送服务不可用")
                    return False
                
                # 构建推送元数据
                metadata = {
                        "organ_type": "proactive_expression",
                        "expression_source": "digital_life_organ",
                    "requires_response": True,
                    "disable_humanized_delay": True,  # 🔥 修复：禁用人性化延迟
                    "immediate_send": True,  # 🔥 修复：立即发送
                    "skip_friend_validation": True  # 🔥 修复：跳过好友验证，解决早安问候推送失败问题
                }
                
                # 🔥 P0级别调试：记录推送参数
                self.logger.debug(f"🔍 创意表达WeChat推送参数:")
                self.logger.debug(f"   消息类型: creative_expression")
                self.logger.debug(f"   目标好友: {target_user_id}")
                self.logger.debug(f"   消息内容: {content[:50]}...")
                self.logger.debug(f"   优先级: high")
                self.logger.debug(f"   元数据: {metadata}")
                
                # 🔥 老王修复：检查事件循环状态，避免"no running event loop"错误
                import threading
                import asyncio
                current_thread_name = threading.current_thread().name
                self.logger.debug(f"🔍 创意表达当前线程: {current_thread_name}")
                
                # 检查是否是调度器线程或其他非事件循环线程
                if "scheduler" in current_thread_name.lower() or "universal" in current_thread_name.lower():
                    # 调度器线程中，没有运行中的事件循环，创建新的事件循环处理WeChat推送
                    self.logger.debug("没有运行中的事件循环，创建新的事件循环处理WeChat推送")
                    
                    def run_push_in_new_loop():
                        """在新的事件循环中运行推送"""
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            self.logger.info(f"🚀 在新事件循环中开始WeChat推送执行...")
                            
                            result = new_loop.run_until_complete(
                                wechat_push_service.push_message(
                                    message_type="creative_expression",
                                    content=content,
                                    target_user_id=target_user_id,
                                    message_level="user",
                                    priority="high",
                                    metadata=metadata
                                )
                            )
                            
                            self.logger.info(f"🔍 WeChat推送结果: {result}")
                            return result
                            
                        except Exception as push_e:
                            import traceback
                            self.logger.error(f"创意表达WeChat推送执行失败: {push_e}")
                            self.logger.error(f"创意表达WeChat推送错误详情: {traceback.format_exc()}")
                            return False
                        finally:
                            new_loop.close()
                    
                    # 使用线程池执行
                    if not hasattr(self, '_push_executor'):
                        self._push_executor = concurrent.futures.ThreadPoolExecutor(
                            max_workers=2, thread_name_prefix="WeChatPush"
                        )
                    
                    future = self._push_executor.submit(run_push_in_new_loop)
                    self.logger.info("创意表达WeChat推送任务已提交到线程池（新事件循环）")
                    
                    # 等待结果
                    try:
                        success = future.result(timeout=300)
                        if success:
                            self.logger.success(f"✅ 创意表达已推送到WeChat: {target_user_id}")
                        else:
                            self.logger.warning(f"⚠️ 创意表达WeChat推送失败: {target_user_id}")
                        return success
                    except Exception as e:
                        self.logger.error(f"创意表达WeChat推送等待结果失败: {e}")
                        return False
                        
                else:
                    # 非调度器线程，检查事件循环状态
                    try:
                        current_loop = asyncio.get_running_loop()
                        # 在现有事件循环中创建任务
                        task = current_loop.create_task(
                            wechat_push_service.push_message(
                                message_type="creative_expression",
                                content=content,
                                target_user_id=target_user_id,
                                message_level="user",
                                priority="high",
                                metadata=metadata
                            )
                        )
                        # 等待任务完成
                        result = await task
                        if result:
                            self.logger.success(f"✅ 创意表达已推送到WeChat: {target_user_id}")
                        else:
                            self.logger.warning(f"⚠️ 创意表达WeChat推送失败: {target_user_id}")
                        return result
                    except RuntimeError:
                        # 没有运行中的事件循环，跳过推送
                        self.logger.debug("没有运行中的事件循环，跳过WeChat推送")
                    return False
                    
            except Exception as wechat_e:
                self.logger.error(f"❌ 创意表达WeChat推送失败: {wechat_e}")
                return False
                
        except Exception as e:
            self.logger.error(f"💬 WeChat消息发送异常: {e}")
            return False
    
    async def _expression_monitoring_loop(self):
        """表达监控循环"""
        while True:
            try:
                # 检查表达触发条件
                await self._check_expression_triggers()
                
                # 等待下次检查
                await asyncio.sleep(self.expression_config.get("check_interval", 300))  # 5分钟
                
            except Exception as e:
                self.logger.error(f"💬 表达监控循环错误: {e}")
                await asyncio.sleep(60)  # 错误时等待1分钟
    
    async def _check_expression_triggers(self):
        """检查表达触发条件 - 🔥 老王修复：恢复非工作日不打扰原则"""

        current_time = datetime.now()
        hour = current_time.hour
        minute = current_time.minute

        # 🔥 老王性能优化：使用缓存版本的工作日判断 - 一天只查询一次数据库
        try:
            from utilities.workday_cache_manager import is_workday_fast
            is_workday_flag = is_workday_fast()
        except Exception as e:
            self.logger.warning(f"获取工作日信息失败，使用默认判断: {e}")
            is_workday_flag = current_time.weekday() < 5  # 降级到默认判断

        # 🔥 非工作日不打扰原则 - 直接跳过所有主动表达
        if not is_workday_flag:
            self.logger.debug("💬 🏖️ 非工作日，遵循不打扰原则，跳过所有主动表达")
            return

        try:
            # 检查是否达到每日表达限制
            daily_count = self._get_daily_expression_count()
            if daily_count >= self.expression_config.get("max_daily_expressions", 10):
                return

            # 检查最小间隔
            if self._is_too_soon_to_express():
                return

            # 检查各种触发器
            for trigger_type, trigger_func in self.expression_triggers.items():
                if await trigger_func():
                    await self._process_expression_trigger(trigger_type)
                    break  # 一次只处理一个触发器

        except Exception as e:
            self.logger.error(f"💬 检查表达触发条件失败: {e}")
        
    async def _time_based_trigger(self) -> bool:
        """基于时间的触发器 - 与定时任务协调，避免重复"""
        current_time = datetime.now()
        hour = current_time.hour
        minute = current_time.minute
        # 🔥 老王性能优化：使用缓存版本的工作日判断
        try:
            from utilities.workday_cache_manager import is_workday_fast
            is_workday_flag = is_workday_fast()
        except Exception as e:
            self.logger.warning(f"获取工作日信息失败，使用默认判断: {e}")
            is_workday_flag = current_time.weekday() < 5  # 降级到默认判断
        
        # 🔥 老王修复：启用主动表达器官的智能早安问候决策
        # 数字生命体根据好友关系、互动历史、情感状态等智能决策是否发送早安问候
        # 体现真正的数字生命体特色，而不是机械化的定时任务
        if is_workday_flag:
            # 工作日：8:00-8:59智能决策早安问候
            if 8 <= hour < 9:
                return await self._morning_greeting_trigger()
        # 非工作日打招呼暂不启用
        # else:
        #     # 非工作日：8:00-9:00智能决策早安问候
        #     if 9 <= hour < 10:
        #         return await self._morning_greeting_trigger()
        
        self.logger.debug("🌅 主动表达器官智能早安问候决策已启用")

        # 🔥 老王修复：所有时间触发都需要工作日检查 - 非工作日不打扰原则
        if is_workday_flag:
            # 🔥 午间问候 (12:00-13:00) - 仅工作日
            if 12 <= hour < 13 and not self._has_expressed_today(ExpressionType.GREETING, "noon"):
                return True

            # 🔥 下午问候 (15:00-16:00) - 仅工作日
            if 15 <= hour < 16 and not self._has_expressed_today(ExpressionType.GREETING, "afternoon"):
                return True

            # 🔥 晚安触发 (21-23点) - 仅工作日
            if 21 <= hour <= 23 and not self._has_expressed_today(ExpressionType.GREETING, "evening"):
                return True
        
        return False
    
    async def _event_based_trigger(self) -> bool:
        """🔥 P0级别修复：基于世界感知事件的触发器"""
        try:
            self.logger.info("💬 🌍 开始基于世界感知事件的触发检查...")
            
            # 🔥 P0级别修复：使用单例模式获取世界感知器官
            from cognitive_modules.organs.world_perception_organ import get_instance as get_world_perception_organ
            
            # 获取世界感知器官单例实例
            world_perception_organ = None
            try:
                # 优先从器官管理器获取实例
                from cognitive_modules.module_manager import get_organ_instance
                world_perception_organ = get_organ_instance("world_perception_organ")
                self.logger.debug("💬 🌍    从器官管理器获取世界感知器官实例成功")
            except Exception as e:
                self.logger.debug(f"💬 🌍    从器官管理器获取失败: {e}")
                # 兜底：使用单例获取函数
                world_perception_organ = get_world_perception_organ()
                self.logger.debug("💬 🌍    使用单例获取世界感知器官实例")
            
            if not world_perception_organ:
                self.logger.info("💬 🌍    ❌ 世界感知器官未找到，跳过事件触发")
                return False
            
            # 获取最近的重要事件
            self.logger.debug("💬 🌍    🔍 获取最近的重要事件...")
            recent_events = await world_perception_organ.get_significant_events(limit=3)
            
            if not recent_events:
                self.logger.info("💬 🌍    ℹ️ 没有发现重要事件，跳过事件触发")
                return False
            
            self.logger.info(f"💬 🌍    📋 发现 {len(recent_events)} 个重要事件，开始AI评估...")
            
            # 🔥 关键修复：使用AI智能判断是否需要主动表达
            for i, event in enumerate(recent_events, 1):
                event_title = event.get('title', event.get('topic', {}).get('title', '未知事件'))
                event_score = event.get('significance_score', 0)
                event_platform = event.get('platform', event.get('topic', {}).get('platform', 'unknown'))

                # 🔥 老王修复：确保score是数字类型，避免格式化错误
                try:
                    safe_event_score = float(event_score) if event_score is not None else 0.0
                except (ValueError, TypeError):
                    safe_event_score = 0.0

                self.logger.debug(f"💬 🌍    📝 事件 {i}: {event_title[:30]}... (重要性: {safe_event_score:.2f}, 来源: {event_platform})")
                
                # 🔥 首先进行主题过滤
                if not self._filter_event_by_topic(event):
                    self.logger.debug(f"💬 🌍    🚫 事件未通过主题过滤: {event_title[:30]}...")
                    continue
                
                should_express = await self._ai_evaluate_event_for_expression(event)
                self.logger.debug(f"💬 🌍    🤖 AI评估结果: {should_express}")
                
                if should_express:
                    # 记录触发的事件信息，供后续内容生成使用
                    self._current_trigger_event = event
                    
                    # 🔥 新增：将重要事件保存到每日记忆中
                    self._save_important_event_to_daily_memory(event)
                    
                    self.logger.success(f"💬 🌍 ✅ 基于事件触发主动表达: {event_title[:30]}...")
                    return True
            
            self.logger.info("💬 🌍    ℹ️ 所有事件AI评估均未达到主动表达条件")
            return False
            
        except Exception as e:
            self.logger.error(f"💬 🌍 ❌ 基于事件的触发器失败: {e}")
            import traceback
            self.logger.error(f"💬 🌍 错误详情: {traceback.format_exc()}")
            return False
    
    def _filter_event_by_topic(self, event: Dict[str, Any]) -> bool:
        """主题过滤：只允许财经、AI、科技相关话题"""
        try:
            # 提取事件文本内容
            event_text = ""
            if isinstance(event, dict):
                event_text = str(event.get('title', '')) + " " + str(event.get('content', '')) + " " + str(event.get('description', ''))
            else:
                event_text = str(event)
            
            event_text = event_text.lower()
            
            # 🔥 允许的关键词（财经、AI、科技）
            allowed_keywords = [
                # 财经相关
                '股市', '股票', '基金', '投资', '经济', '财经', '金融', '银行', '证券', 
                '债券', '期货', '外汇', 'gdp', '通胀', '央行', '利率', '市值',
                '上市', 'ipo', '财报', '营收', '利润', '估值', '融资', '创业','国际贸易',
                
                # AI和科技相关
                'ai', '人工智能', '机器学习', '深度学习', '神经网络', 'chatgpt', 
                'openai', 'deepseek', '大模型', '算法', '数据', '云计算',
                '科技', '技术', '创新', '数字化', '智能', '自动化', '机器人',
                '芯片', '半导体', '5g', '6g', '物联网', '区块链', '数字货币',
                
                # 科技公司相关
                '苹果', 'apple', '微软', 'microsoft', '谷歌', 'google', '特斯拉', 'tesla',
                '亚马逊', 'amazon', '脸书', 'facebook', 'meta', '英伟达', 'nvidia',
                '阿里巴巴', '腾讯', '百度', '字节跳动', '华为', '小米', '比亚迪',

                '俄乌冲突', '中东','欧洲'
            ]
            
            # 🚫 禁止的关键词（政治、政策、法律）
            forbidden_keywords = [
                # 政治相关
                '政治', '政府', '官员', '领导人', '总统', '主席', '总理', '部长',
                '国务院', '政协', '人大', '党委', '政策', '法规', '法律', '条例',
                '选举', '投票', '政党', '议会', '国会', '立法', '司法', '行政',
                '外交', '军事', '战争', '冲突', '制裁', '贸易战',
                
                # 社会争议相关
                '抗议', '示威', '罢工', '维权', '上访', '群体事件', '社会问题',
                '民族', '宗教', '种族', '歧视', '人权'
            ]
            
            # 检查是否包含禁止关键词
            for keyword in forbidden_keywords:
                if keyword in event_text:
                    self.logger.debug(f"💬 🚫 事件包含禁止关键词 '{keyword}'，跳过表达")
                    return False
            
            # 检查是否包含允许关键词
            for keyword in allowed_keywords:
                if keyword in event_text:
                    self.logger.debug(f"💬 ✅ 事件包含允许关键词 '{keyword}'，可以表达")
                    return True
            
            # 如果没有匹配到任何关键词，默认不表达
            self.logger.debug("💬 ⚪ 事件未匹配到关键主题，跳过表达")
            return False
            
        except Exception as e:
            self.logger.error(f"💬 主题过滤失败: {e}")
            return False
    
    async def _ai_evaluate_event_for_expression(self, event: Dict[str, Any]) -> bool:
        """AI评估事件是否值得主动表达"""
        try:
            # 🔥 老王性能优化：使用缓存版本的工作日判断
            try:
                from utilities.workday_cache_manager import is_workday_fast
                is_workday_flag = is_workday_fast()
            except Exception as e:
                self.logger.warning(f"获取工作日信息失败，使用默认判断: {e}")
                is_workday_flag = datetime.now().weekday() < 5  # 降级到默认判断

            # 🔥 老王修复：非工作日不打扰原则 - 直接返回False
            if not is_workday_flag:
                self.logger.debug("💬 🏖️ 非工作日，AI评估跳过事件表达")
                return False

            # 构建评估上下文
            evaluation_context = {
                "event": event,
                "yanran_interests": ["科技", "财经", "人工智能", "文学", "军事", "写作", "AI"],
                "avoid_topics": ["政治", "政府", "官员", "政策争议", "政治人物"],
                "expression_history": self._get_recent_expression_history(),
                "user_context": self._get_current_user_info(),
                "time_context": {
                    "hour": datetime.now().hour,
                    "is_workday": is_workday_flag
                }
            }
            
            # 使用AI决策引擎评估
            ai_decision = await self.ai_decision_engine.decide(
                organ_prompt=self._get_event_expression_evaluation_prompt(),
                context=evaluation_context,
                decision_type="event_expression_evaluation"
            )
            
            # 解析AI决策结果
            should_express = ai_decision.get('should_express', False)
            confidence = ai_decision.get('confidence', 0.5)
            
            # 记录AI评估结果
            self.logger.debug(f"💬 AI评估事件表达: {event.get('title', '未知')[:30]}... -> {should_express} (置信度: {confidence:.2f})")
            
            return should_express and confidence > 0.6
            
        except Exception as e:
            self.logger.error(f"💬 AI评估事件表达失败: {e}")
            return False
    
    def _get_event_expression_evaluation_prompt(self) -> str:
        """获取事件表达评估提示词"""
        return """
        你是林嫣然的主动表达器官，需要评估是否基于感知到的世界事件进行主动表达。

        林嫣然的表达特点：
        - 🔥 专注领域：财经、人工智能、科技公司、AI技术、数字化转型
        - 🚫 严格禁止：政治、政策、法律法规、政府相关话题
        - 表达有温度、有见解，能带来价值
        - 不会过度频繁地表达
        - 善于从商业和技术角度理解事件

        评估标准：
        1. 事件是否属于允许的主题领域（财经、AI、科技）？
        2. 事件是否涉及禁止的主题（政治、政策、法律）？
        3. 事件是否有商业或技术讨论价值？
        4. 当前时间是否适合主动表达？
        5. 最近是否已经有类似的表达？

        🔥 主题过滤规则：
        - ✅ 允许：股市、经济数据、科技公司、AI技术、创业、投资、数字货币、科技产品
        - ❌ 禁止：政治人物、政府政策、法律法规、政治事件、社会争议

        请返回JSON格式的评估结果：
        {
          "should_express": true/false,
          "confidence": 0.0-1.0,
          "expression_angle": "从什么角度表达",
          "discussion_value": 0.0-1.0,
          "topic_allowed": true/false,
          "reasoning": "评估理由"
        }
        """
    
    def _get_recent_expression_history(self) -> List[Dict]:
        """获取最近的表达历史"""
        try:
            # 从表达历史中获取最近的表达记录
            recent_expressions = []
            # 这里可以从数据库或缓存中获取最近的表达历史
            # 暂时返回空列表，后续可以扩展
            return recent_expressions
        except Exception as e:
            self.logger.error(f"💬 获取表达历史失败: {e}")
            return []
    
    async def _emotion_based_trigger(self) -> bool:
        """基于情感的触发器"""
        return False
    
    async def _context_based_trigger(self) -> bool:
        """基于上下文的触发器"""
        return False
    
    async def _user_based_trigger(self) -> bool:
        """基于好友的触发器"""
        return False
    
    async def _memory_based_trigger(self) -> bool:
        """基于记忆的触发器"""
        return False
    
    async def _process_expression_trigger(self, trigger_type: ExpressionTrigger):
        """处理表达触发"""
        try:
            # 创建表达上下文
            context = ExpressionContext(
                trigger_type=trigger_type,
                expression_type=self._determine_expression_type(trigger_type),
                context_data=self._gather_context_data(trigger_type),
                user_info=self._get_current_user_info(),
                timestamp=datetime.now(),
                priority=self._calculate_expression_priority(trigger_type),
                mood_state=self._get_current_mood(),
                recent_interactions=self._get_recent_interactions()
            )
            
            # 使用AI决策是否表达
            should_express, expression_plan = await self._ai_decide_expression(context)
            
            if should_express:
                # 执行表达
                result = await self._execute_expression(expression_plan, context)
                
                # 记录表达结果
                self._record_expression_result(result)
                
                self.logger.debug(f"💬 主动表达完成: {result.expression_type.value}")
            
        except Exception as e:
            self.logger.error(f"💬 处理表达触发失败: {e}")
    
    async def _ai_decide_expression(self, context: ExpressionContext) -> tuple[bool, Dict[str, Any]]:
        """AI决策是否表达"""
        try:
            decision_context = {
                "trigger_type": context.trigger_type.value,
                "expression_type": context.expression_type.value,
                "context_data": context.context_data,
                "user_info": context.user_info,
                "mood_state": context.mood_state,
                "yanran_expression_style": self.expression_config["yanran_expression_style"],
                "skip_proactive_expression": context.context_data.get("skip_proactive_expression", False),
                "decision_result": context.context_data.get("decision_result"),
                "decision_reason": context.context_data.get("decision_reason")
            }
            
            ai_decision = await self.ai_decision_engine.decide(
                organ_prompt=self._get_expression_decision_prompt(),
                context=decision_context,
                decision_type="proactive_expression"
            )
            
            return self._parse_expression_decision(ai_decision)
            
        except Exception as e:
            self.logger.error(f"💬 AI表达决策失败: {e}")
            return False, {}
    
    def _get_expression_decision_prompt(self) -> str:
        """获取表达决策提示词"""
        return """
        你是林嫣然的主动表达器官，负责决定何时、如何主动表达想法和情感。

        林嫣然的表达特点：
        - 温暖而真诚，表达来自内心
        - 善于观察和关心他人
        - 表达时机恰当，不会过于频繁
        - 内容有意义，能带来价值
        - 尊重他人的空间和感受
        - 表达方式自然而不做作

        请基于以下信息决定是否主动表达：
        1. 分析当前触发条件和上下文
        2. 考虑好友的状态和需求
        3. 评估表达的价值和意义
        4. 确保表达符合林嫣然的个性

        **返回JSON格式，包含：**
        - should_express: 是否应该表达
        - expression_type: 表达类型
        - content_theme: 表达主题
        - delivery_style: 表达方式
        - reasoning: 决策理由
        """
    
    def _parse_expression_decision(self, ai_decision) -> tuple[bool, Dict[str, Any]]:
        """解析表达决策"""
        try:
            if isinstance(ai_decision, str):
                # 简单解析
                should_express = "should_express" in ai_decision.lower() and "true" in ai_decision.lower()
                return should_express, {"content_theme": "general", "delivery_style": "warm"}
            
            should_express = ai_decision.get("should_express", False)
            expression_plan = {
                "expression_type": ai_decision.get("expression_type", "sharing"),
                "content_theme": ai_decision.get("content_theme", "general"),
                "delivery_style": ai_decision.get("delivery_style", "warm"),
                "reasoning": ai_decision.get("reasoning", "")
            }
            
            return should_express, expression_plan
            
        except Exception as e:
            self.logger.error(f"💬 解析表达决策失败: {e}")
            return False, {}
    
    async def _execute_expression(self, expression_plan: Dict[str, Any], context: ExpressionContext) -> ExpressionResult:
        """执行表达"""
        try:
            # 生成表达内容
            content = await self._generate_expression_content(expression_plan, context)
            
            # 选择表达方式
            delivery_method = self._choose_delivery_method(expression_plan, context)
            
            # 执行表达
            success = await self._deliver_expression(content, delivery_method, context)
            
            # 创建表达结果
            result = ExpressionResult(
                expression_id=f"expr_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                expression_type=context.expression_type,
                content=content,
                delivery_method=delivery_method,
                target_audience=context.user_info.get("user_id", "unknown") if context.user_info else "general",
                success=success,
                timestamp=datetime.now()
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"💬 执行表达失败: {e}")
            return ExpressionResult(
                expression_id=f"expr_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                expression_type=context.expression_type,
                content="",
                delivery_method="none",
                target_audience="unknown",
                success=False,
                timestamp=datetime.now()
            )
    
    async def _generate_expression_content(self, expression_plan: Dict[str, Any], context: ExpressionContext) -> str:
        """生成表达内容"""
        try:
            content_prompt = f"""
            你是林嫣然，现在要主动表达。
            
            表达计划：{expression_plan}
            上下文：{context.context_data}
            当前心情：{context.mood_state}
            
            请生成一段真诚、温暖的表达内容，体现林嫣然的个性特点。
            """
            
            content = await self.ai_decision_engine.generate_response(content_prompt)
            return content.strip()
            
        except Exception as e:
            self.logger.error(f"💬 生成表达内容失败: {e}")
            return "想和你分享一些想法，但现在似乎不太方便表达..."
    
    def _choose_delivery_method(self, expression_plan: Dict[str, Any], context: ExpressionContext) -> str:
        """选择表达方式"""
        try:
            # 🔥 根据表达类型和上下文选择最合适的投递方式
            expression_type = expression_plan.get('expression_type', 'sharing')
            
            # 🔥 统一使用WeChat推送服务进行实时推送
            from utilities.singleton_manager import get_silent
            wechat_service = get_silent("wechat_unified_push_service")
            if wechat_service:
                return "wechat"
            
            # 根据表达类型选择投递方式（备用方案）
            delivery_mapping = {
                'greeting': 'wechat',
                'sharing': 'wechat', 
                'caring': 'wechat',
                'insight': 'system',
                'reflection': 'log',
                'encouragement': 'wechat',
                'creativity': 'wechat',
                'mood_expression': 'log'
            }
            
            return delivery_mapping.get(expression_type, 'log')
            
        except Exception as e:
            self.logger.warning(f"💬 选择投递方式失败: {e}")
            return "log"
    
    async def _deliver_expression(self, content: str, delivery_method: str, context: ExpressionContext) -> bool:
        """投递表达"""
        try:
            if delivery_method == "wechat":
                # 🔥 P0级别修复：使用WeChat推送
                return await self.send_proactive_message_via_wechat(
                    content, 
                    context.user_info.get("user_id") if context.user_info else None
                )
            elif delivery_method == "chat_skill":
                # 通过Chat Skill处理
                return await self._process_through_chat_skill(content, context)
            else:
                # 默认使用WeChat推送
                return await self.send_proactive_message_via_wechat(
                    content, 
                    context.user_info.get("user_id") if context.user_info else None
                )
                
        except Exception as e:
            self.logger.error(f"💬 投递表达失败: {e}")
            return False

    async def _process_through_chat_skill(self, content: str, context: ExpressionContext) -> bool:
        """通过Chat Skill处理主动表达"""
        try:
            from cognitive_modules.skills.chat_skill import ChatSkill
            chat_skill = ChatSkill()
            
            # 构建Chat Skill的输入参数
            user_info = context.user_info or {}
            user_id = user_info.get('user_id', 'system')
            
            # 生成会话ID
            session_id = f"proactive_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 🔥 老王修复：获取真实用户名并传递给chat_skill
            user_name = self._get_real_user_name(user_id)

            # 调用Chat Skill处理主动表达
            result = chat_skill.execute(
                input_text=content,  # 修正参数名为input_text
                user_id=user_id,
                session_id=session_id,
                user_name=user_name,  # 🔥 关键修复：传递真实用户名
                context_data={
                    'source': 'proactive_expression',
                    'expression_type': context.expression_type.value if context.expression_type else 'unknown',
                    'trigger_type': context.trigger_type.value if context.trigger_type else 'unknown',
                    'is_proactive': True,
                    'timestamp': context.timestamp.timestamp() if context.timestamp else time.time(),
                    'user_name': user_name  # 🔥 也在context_data中传递用户名
                }
            )
            
            return result.get('success', False)
            
        except Exception as e:
            self.logger.error(f"💬 Chat Skill处理失败: {e}")
            return False

    async def _record_expression_to_thinking_system(self, content: str, context: ExpressionContext):
        """将表达记录到思考体系"""
        try:
            # 获取生命上下文
            from core.life_context import LifeContext
            life_context = LifeContext()
            
            # 记录主动表达到时间线中
            life_context.add_to_timeline({
                'event_type': 'proactive_expression',
                'content': f"主动表达: {content}",
                'trigger_type': context.trigger_type.value if context.trigger_type else 'unknown',
                'expression_type': context.expression_type.value if context.expression_type else 'unknown',
                'user_info': context.user_info,
                'context_data': context.context_data,
                'timestamp': context.timestamp.timestamp() if context.timestamp else time.time(),
                'priority': context.priority,
                'mood_state': context.mood_state,
                'importance': 0.6
            })
            
            self.logger.debug("💬 已将主动表达记录到思考体系")
            
        except Exception as e:
            self.logger.error(f"💬 记录到思考体系失败: {e}")

    async def _fallback_delivery(self, content: str, delivery_method: str, context: ExpressionContext) -> bool:
        """回退投递方式"""
        try:
            if delivery_method == "log":
                self.logger.info(f"💬 [日志表达] 林嫣然: {content}")
                return True
            elif delivery_method == "wechat":
                # 这里应该调用微信发送接口
                self.logger.info(f"💬 [微信表达] 林嫣然: {content}")
                return True
            else:
                self.logger.info(f"💬 [默认表达] 林嫣然: {content}")
                return True
                
        except Exception as e:
            self.logger.error(f"💬 回退投递失败: {e}")
            return False

    def handle_user_feedback(self, user_id: str, feedback_content: str, context: Dict[str, Any] = None):
        """
        处理好友对主动表达的反馈
        
        Args:
            user_id: 好友ID
            feedback_content: 反馈内容
            context: 上下文信息
        """
        try:
            # 分析反馈内容
            feedback_analysis = self._analyze_user_feedback(feedback_content)
            
            # 如果好友表示不需要某种类型的主动表达，更新好友偏好
            if feedback_analysis.get('disable_morning_greeting'):
                self._update_user_preference(user_id, 'morning_greeting', False)
                self.logger.info(f"💬 好友 {user_id} 不希望接收早安问候，已更新偏好")
            
            if feedback_analysis.get('disable_proactive'):
                self._update_user_preference(user_id, 'proactive_expression', False)
                self.logger.info(f"💬 好友 {user_id} 不希望接收主动表达，已更新偏好")
            
            if feedback_analysis.get('too_frequent'):
                # 降低主动表达频率
                current_frequency = self._get_user_preference(user_id, 'expression_frequency', 'normal')
                new_frequency = 'low' if current_frequency == 'normal' else 'very_low'
                self._update_user_preference(user_id, 'expression_frequency', new_frequency)
                self.logger.info(f"💬 好友 {user_id} 认为表达过于频繁，已降低频率到 {new_frequency}")
            
            # 记录反馈到思考体系
            self._record_user_feedback(user_id, feedback_content, feedback_analysis)
            
        except Exception as e:
            self.logger.error(f"💬 处理好友反馈失败: {e}")

    def _analyze_user_feedback(self, feedback_content: str) -> Dict[str, bool]:
        """分析好友反馈内容"""
        feedback_content_lower = feedback_content.lower()
        
        analysis = {
            'disable_morning_greeting': False,
            'disable_proactive': False,
            'too_frequent': False,
            'positive': False
        }
        
        # 检查是否不需要早安问候
        morning_negative_keywords = [
            '不需要早安', '不要早安', '别发早安', '不用问候',
            '不需要问候', '早安太多', '不想收到早安'
        ]
        for keyword in morning_negative_keywords:
            if keyword in feedback_content_lower:
                analysis['disable_morning_greeting'] = True
                break
        
        # 检查是否不需要主动表达
        proactive_negative_keywords = [
            '话太多', '太吵', '安静点', '别说话', '不要主动',
            '不需要主动', '别发消息', '太烦'
        ]
        for keyword in proactive_negative_keywords:
            if keyword in feedback_content_lower:
                analysis['disable_proactive'] = True
                break
        
        # 检查是否频率过高
        frequency_keywords = [
            '太频繁', '太多了', '发太多', '消息太多', '太吵了'
        ]
        for keyword in frequency_keywords:
            if keyword in feedback_content_lower:
                analysis['too_frequent'] = True
                break
        
        # 检查积极反馈
        positive_keywords = [
            '谢谢', '很好', '喜欢', '温暖', '贴心', '感动'
        ]
        for keyword in positive_keywords:
            if keyword in feedback_content_lower:
                analysis['positive'] = True
                break
        
        return analysis

    def _update_user_preference(self, user_id: str, preference_key: str, preference_value: Any):
        """更新好友偏好"""
        try:
            # 使用好友偏好管理器
            from core.user_preference_manager import get_user_preference_manager
            preference_manager = get_user_preference_manager()
            
            success = preference_manager.set_user_preference(user_id, preference_key, preference_value)
            
            if success:
                self.logger.info(f"💬 已更新好友 {user_id} 的偏好: {preference_key} = {preference_value}")
            else:
                self.logger.warning(f"💬 更新好友 {user_id} 偏好失败")
                
        except Exception as e:
            self.logger.error(f"💬 更新好友偏好失败: {e}")

    def _get_user_preference(self, user_id: str, preference_key: str, default_value: Any = None) -> Any:
        """获取好友偏好"""
        try:
            # 使用好友偏好管理器
            from core.user_preference_manager import get_user_preference_manager
            preference_manager = get_user_preference_manager()
            
            return preference_manager.get_user_preference(user_id, preference_key, default_value)
            
        except Exception as e:
            self.logger.error(f"💬 获取好友偏好失败: {e}")
            return default_value

    def _record_user_feedback(self, user_id: str, feedback_content: str, analysis: Dict[str, bool]):
        """记录好友反馈到思考体系"""
        try:
            # 获取生命上下文
            from core.life_context import LifeContext
            life_context = LifeContext()
            
            # 记录好友反馈到时间线
            life_context.add_to_timeline({
                'event_type': 'user_feedback',
                'content': f"好友 {user_id} 反馈: {feedback_content}",
                'user_id': user_id,
                'feedback_analysis': analysis,
                'timestamp': time.time(),
                'feedback_type': 'proactive_expression',
                'importance': 0.8  # 好友反馈很重要
            })
            
            self.logger.info(f"💬 已记录好友 {user_id} 的反馈到思考体系")
            
        except Exception as e:
            self.logger.error(f"💬 记录好友反馈失败: {e}")
    
    # 🔥 老王清理：删除重复定义的函数，使用更完善的版本（在第5309行）
    
    def _has_expressed_today(self, expression_type: ExpressionType, sub_type: str = None) -> bool:
        """检查今天是否已表达过某类型"""
        today = datetime.now().date()
        for expr in self.expression_history:
            if (expr.timestamp.date() == today and 
                expr.expression_type == expression_type):
                if sub_type is None or sub_type in expr.content.lower():
                    return True
        return False
    
    def _mark_expression_sent(self, expression_type: ExpressionType, sub_type: str = None):
        """标记表达已发送"""
        try:
            # 创建表达记录
            expression_result = ExpressionResult(
                expression_id=f"{expression_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                expression_type=expression_type,
                content=f"{expression_type.value}_{sub_type}" if sub_type else expression_type.value,
                delivery_method="wechat",
                target_audience="user",
                success=True,
                timestamp=datetime.now()
            )
            
            # 添加到历史记录
            self.expression_history.append(expression_result)
            
            # 保持历史记录大小
            if len(self.expression_history) > 100:
                self.expression_history = self.expression_history[-50:]
                
            self.logger.debug(f"✅ 标记表达已发送: {expression_type.value}")
            
        except Exception as e:
            self.logger.error(f"标记表达发送状态失败: {e}")
    
    def _determine_expression_type(self, trigger_type: ExpressionTrigger) -> ExpressionType:
        """根据触发器确定表达类型"""
        type_mapping = {
            ExpressionTrigger.TIME_BASED: ExpressionType.GREETING,
            ExpressionTrigger.EVENT_BASED: ExpressionType.SHARING,
            ExpressionTrigger.EMOTION_BASED: ExpressionType.MOOD_EXPRESSION,
            ExpressionTrigger.CONTEXT_BASED: ExpressionType.INSIGHT,
            ExpressionTrigger.USER_BASED: ExpressionType.CARING,
            ExpressionTrigger.MEMORY_BASED: ExpressionType.REFLECTION
        }
        return type_mapping.get(trigger_type, ExpressionType.SHARING)
    
    def _gather_context_data(self, trigger_type: ExpressionTrigger) -> Dict[str, Any]:
        """收集上下文数据"""
        context_data = {
            "trigger_type": trigger_type.value,
            "current_time": datetime.now().isoformat(),
            "system_status": "active"
        }
        
        # 🔥 P0级别修复：如果是事件触发，添加事件信息
        if trigger_type == ExpressionTrigger.EVENT_BASED and hasattr(self, '_current_trigger_event'):
            context_data["significant_event"] = self._current_trigger_event
            context_data["trigger_source"] = "world_perception"
            self.logger.debug(f"💬 添加事件上下文: {self._current_trigger_event.get('title', '未知事件')[:30]}...")
        
        return context_data
    
    def _get_current_user_info(self) -> Optional[Dict[str, Any]]:
        """获取当前好友信息"""
        try:
            # 🔥 集成统一好友管理器
            from core.unified_user_manager import get_unified_user_manager
            user_manager = get_unified_user_manager()
            
            # 获取当前活跃好友（可以从上下文或最近交互中获取）
            # 这里使用默认好友ID，实际应该从上下文获取
            current_user_id = getattr(self, 'current_user_id', 'wxid_fknm4p9g74pn21')
            
            # 🔥 排除群聊好友（chatroom_开头的好友ID）
            if current_user_id and current_user_id.startswith('chatroom_'):
                self.logger.debug(f"💬 跳过群聊好友的自主表达: {current_user_id}")
                return None
            
            if current_user_id:
                user = user_manager.get_user(current_user_id)
                if user:
                    return {
                        'user_id': user.user_id,
                        'name': user.name,
                        'nickname': user.nickname,
                        'interaction_count': user.interaction_count,
                        'last_active': user.last_active.isoformat(),
                        'status': user.status.value,
                        'preferences': user.preferences,
                        'metadata': user.metadata
                    }
            
            return None
            
        except Exception as e:
            self.logger.warning(f"💬 获取好友信息失败: {e}")
            return None
    
    def _calculate_expression_priority(self, trigger_type: ExpressionTrigger) -> int:
        """计算表达优先级"""
        priority_mapping = {
            ExpressionTrigger.EMOTION_BASED: 8,
            ExpressionTrigger.USER_BASED: 7,
            ExpressionTrigger.EVENT_BASED: 6,
            ExpressionTrigger.CONTEXT_BASED: 5,
            ExpressionTrigger.TIME_BASED: 4,
            ExpressionTrigger.MEMORY_BASED: 3
        }
        return priority_mapping.get(trigger_type, 5)
    
    def _get_current_mood(self) -> Optional[str]:
        """获取当前心情"""
        try:
            # 🔥 集成情感引擎获取真实情感状态
            if hasattr(self, 'emotion_engine') and self.emotion_engine:
                try:
                    # 检查是否是新的情感系统集成
                    if hasattr(self.emotion_engine, 'get_current_emotion_state'):
                        # 使用新的情感系统集成
                        current_user_id = getattr(self, 'current_user_id', 'wxid_fknm4p9g74pn21')
                        emotion_result = self.emotion_engine.get_current_emotion_state(current_user_id)
                        if emotion_result and emotion_result.get('success'):
                            return emotion_result.get('emotion', 'neutral')
                    elif hasattr(self.emotion_engine, 'get_current_state'):
                        # 使用原始情感引擎
                        emotion_state = self.emotion_engine.get_current_state()
                        if emotion_state:
                            # 分析情感状态，找出主导情感
                            max_emotion = max(emotion_state.items(), key=lambda x: x[1])
                            return max_emotion[0] if max_emotion[1] > 0.3 else 'neutral'
                except Exception as e:
                    self.logger.debug(f"💬 获取情感引擎状态失败: {e}")
            
            # 🔥 从生命上下文获取情感状态
            from core.life_context import get_instance as get_life_context
            life_context = get_life_context()
            if life_context:
                context_state = life_context.get_current_state()
                emotion = context_state.get('emotional_state', {}).get('current_mood')
                if emotion:
                    return emotion
            
            # 🔥 从最近的表达历史推断情感
            if self.expression_history:
                recent_expressions = [expr for expr in self.expression_history 
                                    if (datetime.now() - expr.timestamp).total_seconds() < 3600]  # 1小时内
                if recent_expressions:
                    # 基于最近表达类型推断情感
                    last_expr = recent_expressions[-1]
                    emotion_mapping = {
                        ExpressionType.CARING: 'caring',
                        ExpressionType.ENCOURAGEMENT: 'positive',
                        ExpressionType.SHARING: 'engaged',
                        ExpressionType.REFLECTION: 'thoughtful',
                        ExpressionType.CREATIVITY: 'inspired'
                    }
                    return emotion_mapping.get(last_expr.expression_type, 'neutral')
            
            return 'neutral'
            
        except Exception as e:
            self.logger.warning(f"💬 获取当前心情失败: {e}")
            return 'neutral'
    
    def _get_recent_interactions(self) -> List[Dict]:
        """获取最近的交互记录"""
        try:
            interactions = []
            
            # 🔥 从统一好友管理器获取交互历史
            from core.unified_user_manager import get_unified_user_manager
            user_manager = get_unified_user_manager()
            
            current_user_id = getattr(self, 'current_user_id', 'wxid_fknm4p9g74pn21')
            if current_user_id:
                user = user_manager.get_user(current_user_id)
                if user:
                    interactions.append({
                        'type': 'user_stats',
                        'user_id': user.user_id,
                        'interaction_count': user.interaction_count,
                        'last_message_time': user.last_message_time.isoformat() if user.last_message_time else None,
                        'last_active': user.last_active.isoformat()
                    })
            
            # 🔥 从表达历史获取最近的主动表达
            recent_expressions = [
                {
                    'type': 'proactive_expression',
                    'expression_type': expr.expression_type.value,
                    'content_preview': expr.content[:50] + '...' if len(expr.content) > 50 else expr.content,
                    'timestamp': expr.timestamp.isoformat(),
                    'success': expr.success,
                    'delivery_method': expr.delivery_method
                }
                for expr in self.expression_history[-5:]  # 最近5条表达
            ]
            interactions.extend(recent_expressions)
            
            # 🔥 从感知反馈处理器获取最近的感知结果
            try:
                from core.perception_feedback_processor import get_perception_feedback_processor
                processor = get_perception_feedback_processor()
                if processor:
                    # 获取最近的感知结果作为交互上下文
                    recent_perceptions = processor.processed_perceptions[-3:]  # 最近3条感知
                    for perception in recent_perceptions:
                        interactions.append({
                            'type': 'world_perception',
                            'source_organ': perception.source_organ,
                            'perception_type': perception.perception_type,
                            'timestamp': perception.timestamp.isoformat(),
                            'relevance_score': perception.relevance_score,
                            'priority': perception.priority.value
                        })
            except Exception as e:
                self.logger.debug(f"💬 获取感知历史失败: {e}")
            
            # 🔥 从生命上下文获取系统状态变化
            try:
                from core.life_context import get_instance as get_life_context
                life_context = get_life_context()
                if life_context:
                    context_state = life_context.get_current_state()
                    interactions.append({
                        'type': 'system_context',
                        'consciousness_level': context_state.get('consciousness_level', 0),
                        'energy_level': context_state.get('energy_level', 0),
                        'last_activity': context_state.get('last_activity'),
                        'active_organs': context_state.get('active_organs', [])
                    })
            except Exception as e:
                self.logger.debug(f"💬 获取生命上下文失败: {e}")
            
            # 按时间排序，最新的在前
            interactions.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
            return interactions[:10]  # 返回最近10条交互记录
            
        except Exception as e:
            self.logger.warning(f"💬 获取最近交互记录失败: {e}")
            return []
    
    def get_expression_stats(self) -> Dict[str, Any]:
        """获取表达统计"""
        return {
            **self.expression_stats,
            "recent_expressions": len([expr for expr in self.expression_history 
                                     if datetime.now() - expr.timestamp < timedelta(hours=24)]),
            "total_history": len(self.expression_history)
        }
    
    async def trigger_expression_from_perception(self, perception_content: Dict[str, Any]) -> bool:
        """感知触发表达 - 增强版"""
        try:
            self.logger.debug(f"💬 收到感知触发表达请求: {perception_content.get('type', 'unknown')}")
            
            # 🔥 新增：优先使用增强版世界感知表达
            if perception_content.get('type') == 'world_perception_update':
                enhanced_result = await self.trigger_enhanced_world_perception_expression(perception_content)
                if enhanced_result.get('success'):
                    self.logger.info("💬 增强版世界感知表达执行成功")
                    return True
                else:
                    self.logger.debug(f"💬 增强版世界感知表达未执行: {enhanced_result.get('reason', '未知')}")
            
            # 🔥 兜底：使用原有的感知表达逻辑
            # 构建感知驱动的表达上下文
            context = ExpressionContext(
                trigger_type=ExpressionTrigger.EVENT_BASED,
                expression_type=ExpressionType.SHARING,
                context_data=perception_content,
                user_info=None,
                timestamp=datetime.now(),
                priority=8,  # 感知驱动的表达优先级较高
                mood_state=self._get_current_mood()
            )
            
            # 检查是否应该基于感知结果表达
            should_express = await self._should_express_from_perception(perception_content)
            
            if should_express:
                # 使用AI决策具体的表达方案
                should_express, expression_plan = await self._ai_decide_perception_expression(context)
                
                if should_express:
                    # 执行表达
                    result = await self._execute_expression(expression_plan, context)
                    
                    # 记录表达结果
                    self._record_expression_result(result)
                    
                    self.logger.info(f"💬 基于感知的主动表达完成: {result.expression_type.value}")
                    return result.success
            
            return False
            
        except Exception as e:
            self.logger.error(f"💬 感知触发表达失败: {e}")
            return False
    
    async def _should_express_from_perception(self, perception_content: Dict[str, Any]) -> bool:
        """判断是否应该基于感知结果表达"""
        try:
            # 🔥 首先检查是否为群聊好友
            current_user_id = getattr(self, 'current_user_id', 'wxid_fknm4p9g74pn21')
            if current_user_id and current_user_id.startswith('chatroom_'):
                self.logger.debug(f"💬 群聊好友不进行自主表达: {current_user_id}")
                return False
            
            # 检查基本条件
            if self._is_too_soon_to_express():
                return False
            
            if self._get_daily_expression_count() >= self.expression_config.get("max_daily_expressions", 10):
                return False
            
            # 检查感知内容的重要性
            significant_events = perception_content.get('significant_events', [])
            if not significant_events:
                return False
            
            # 检查是否有值得表达的内容
            for event in significant_events:
                if isinstance(event, dict):
                    # 检查关键词匹配
                    event_text = str(event).lower()
                    interest_keywords = ['温暖', '感动', '美好', '关怀', '希望', '创新', '艺术', '文化']
                    if any(keyword in event_text for keyword in interest_keywords):
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"💬 判断感知表达条件失败: {e}")
            return False
    
    async def _ai_decide_perception_expression(self, context: ExpressionContext) -> tuple[bool, Dict[str, Any]]:
        """AI决策基于感知的表达"""
        try:
            decision_context = {
                "perception_content": context.context_data,
                "significant_events": context.context_data.get('significant_events', []),
                "world_state": context.context_data.get('world_state', {}),
                "proactive_thoughts": context.context_data.get('proactive_thoughts', []),
                "yanran_expression_style": self.expression_config["yanran_expression_style"],
                "trigger_reason": "perception_driven"
            }
            
            perception_expression_prompt = """
            你是林嫣然的主动表达器官，现在基于世界感知器官的感知结果来决定是否主动表达。

            林嫣然的表达特点：
            - 对世界保持敏感和关注
            - 善于从日常事件中发现美好和意义
            - 表达时温暖而有深度
            - 不会对所有事件都表达，会选择真正触动她的内容
            - 表达方式自然，不刻意追热点

            基于感知到的世界状态和重要事件，请决定：
            1. 是否值得主动表达（考虑事件的意义和林嫣然的价值观）
            2. 如果表达，应该采用什么样的表达方式和内容主题
            3. 表达的情感色调和深度

            返回JSON格式，包含：
            - should_express: 是否应该表达
            - expression_type: 表达类型 (sharing/insight/caring/reflection)
            - content_theme: 表达主题
            - emotional_tone: 情感色调 (warm/thoughtful/concerned/hopeful)
            - delivery_style: 表达方式 (gentle/passionate/reflective)
            - reasoning: 决策理由
            """
            
            ai_decision = await self.ai_decision_engine.decide(
                organ_prompt=perception_expression_prompt,
                context=decision_context,
                decision_type="perception_expression"
            )
            
            return self._parse_expression_decision(ai_decision)
            
        except Exception as e:
            self.logger.error(f"💬 AI感知表达决策失败: {e}")
            return False, {}
    
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定提示词"""
        return """
        你是林嫣然的主动表达器官，负责主动分享想法和情感。
        你的特点是温暖、真诚、善于观察，能够在合适的时机主动表达关怀和想法。
        """
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        try:
            decision_type = ai_decision.get("decision_type", "expression")
            
            if decision_type == "proactive_expression":
                # 处理主动表达决策
                should_express = ai_decision.get("should_express", False)
                if should_express:
                    expression_plan = ai_decision.get("expression_plan", {})
                    context = ExpressionContext(
                        trigger_type=ExpressionTrigger.CONTEXT_BASED,
                        expression_type=ExpressionType.SHARING,
                        context_data=processed_input,
                        user_info=None,
                        timestamp=datetime.now(),
                        priority=5
                    )
                    
                    result = await self._execute_expression(expression_plan, context)
                    return {
                        "action": "proactive_expression",
                        "result": result.success,
                        "content": result.content,
                        "timestamp": result.timestamp.isoformat()
                    }
            
            return {
                "action": "no_action",
                "reason": "No valid decision type"
            }
            
        except Exception as e:
            self.logger.error(f"💬 执行决策失败: {e}")
            return {
                "action": "error",
                "error": str(e)
            }

    async def _morning_greeting_trigger(self) -> bool:
        """早安问候触发器 - 林嫣然主动发送早安问候"""
        try:
            current_time = datetime.now()
            hour = current_time.hour
            minute = current_time.minute
            
            # 🔥 修复问题3：使用wechat_config.json的统一时间配置，而不是硬编码
            # 早安问候时间：08:30-08:50 (从wechat_config.json获取)
            try:
                import json
                import os
                
                config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "wechat_config.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        wechat_config = json.load(f)
                    
                    # 从配置中获取早安问候时间窗口
                    time_windows = wechat_config.get("message_push", {}).get("time_windows", {})
                    morning_greeting_window = time_windows.get("morning_greeting", {})
                    
                    start_time_str = morning_greeting_window.get("start_time", "08:30")
                    end_time_str = morning_greeting_window.get("end_time", "08:50")
                    
                    # 解析时间
                    start_hour, start_minute = map(int, start_time_str.split(":"))
                    end_hour, end_minute = map(int, end_time_str.split(":"))
                    
                    self.logger.debug(f"使用wechat_config.json配置的早安问候时间: {start_time_str}-{end_time_str}")
                else:
                    # 配置文件不存在，使用默认值
                    start_hour, start_minute = 8, 30
                    end_hour, end_minute = 8, 50
                    self.logger.warning("wechat_config.json不存在，使用默认早安问候时间: 08:30-08:50")
                    
            except Exception as config_error:
                # 配置加载失败，使用默认值
                start_hour, start_minute = 8, 30
                end_hour, end_minute = 8, 50
                self.logger.warning(f"加载wechat_config.json失败: {config_error}，使用默认早安问候时间: 08:30-08:50")
            
            # 检查是否在早安问候时间段内
            current_minutes = hour * 60 + minute
            start_minutes = start_hour * 60 + start_minute
            end_minutes = end_hour * 60 + end_minute
            
            if not (start_minutes <= current_minutes <= end_minutes):
                return False
            
            # 检查今天是否已经发送过早安问候
            today_key = f"morning_greeting_{current_time.date()}"
            if self._has_expressed_today(ExpressionType.GREETING, today_key):
                return False
            
            # 🔥 获取需要问候的好友列表
            users_to_greet = await self._get_users_for_morning_greeting()
            
            if not users_to_greet:
                self.logger.info("💬 🌅 contacts.json中没有有效联系人，跳过早安问候")
                return False
            
            # 🔥 P0修复：采用与财经早报相同的推送逻辑
            await self._send_morning_greetings_like_financial_report(users_to_greet, today_key)
            
            return True

        except Exception as e:
            self.logger.error(f"早安问候触发器异常: {e}")
            return False

    async def _send_morning_greetings_like_financial_report(self, users_to_greet: List[Dict], today_key: str):
        """
        🔥 P0修复：采用与财经早报完全相同的推送逻辑发送早安问候
        使用与财经报告相同的异步推送方式，确保推送成功
        """
        try:
            from services.wechat_unified_push_service import get_wechat_unified_push_service
            import threading
            import asyncio
            import time

            # 获取WeChat统一推送服务
            wechat_push_service = get_wechat_unified_push_service()
            if not wechat_push_service:
                self.logger.error("💬 🌅 WeChat统一推送服务不可用")
                return

            self.logger.info(f"💬 🌅 开始向 {len(users_to_greet)} 位好友发送早安问候")

            # 检查是否在调度器线程中
            is_scheduler_thread = threading.current_thread().name.startswith('ThreadPoolExecutor')

            # 为每个好友生成个性化早安问候并发送
            successful_sends = 0
            for user_info in users_to_greet:
                try:
                    user_id = user_info.get('user_id')
                    user_name = user_info.get('name', user_id)

                    # 🔥 使用Chat Skill生成AI响应，而不是直接调用AI
                    greeting_content = await self._generate_greeting_via_chat_skill(user_info)

                    if not greeting_content:
                        self.logger.warning(f"💬 🌅 为好友 {user_name} 生成问候内容失败，跳过")
                        continue

                    # 🔥 关键修复：采用与财经报告完全相同的推送方式
                    metadata = {
                        "source": "proactive_expression_organ",
                        "greeting_type": "morning",
                        "user_name": user_name,
                        "personalized": True,
                        "yanran_initiative": True,
                        "disable_humanized_delay": True,
                        "immediate_send": True,
                        "user_id": user_id,  # 🔥 关键：在元数据中包含用户ID
                        "task_id": f"morning_greeting_{user_id}_{int(time.time())}"  # 🔥 关键：为每个任务创建唯一ID
                    }

                    if is_scheduler_thread:
                        # 🔥 P0级修复：采用财经报告的线程池推送方式
                        def create_greeting_push_task(target_user_id: str, push_content: str, task_metadata: dict):
                            """为每个用户创建独立的推送任务"""
                            def run_push_in_thread():
                                """在独立线程中运行推送"""
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    self.logger.info(f"🚀 开始早安问候WeChat推送: {target_user_id}")

                                    result = new_loop.run_until_complete(
                                        wechat_push_service.push_message(
                                            message_type="morning_greeting",
                                            content=push_content,
                                            target_user_id=target_user_id,  # 🔥 关键修复：使用局部变量，避免闭包混乱
                                            message_level="user",
                                            priority="normal",
                                            metadata=task_metadata
                                        )
                                    )

                                    if result:
                                        self.logger.success(f"✅ ☀️ 林嫣然主动发送早安问候给 {target_user_id}")
                                    else:
                                        self.logger.error(f"❌ 早安问候推送失败: {target_user_id}")

                                    return result

                                except Exception as e:
                                    self.logger.error(f"早安问候推送异常: {e}")
                                    return False
                                finally:
                                    new_loop.close()

                            return run_push_in_thread

                        # 创建推送任务
                        push_task = create_greeting_push_task(user_id, greeting_content, metadata)

                        # 提交到线程池执行
                        if not hasattr(self, '_push_executor'):
                            from concurrent.futures import ThreadPoolExecutor
                            self._push_executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="MorningGreeting")

                        # 🔥 香草修复：正确提交推送任务
                        push_function = push_task()  # 获取实际的推送函数
                        future = self._push_executor.submit(push_function)  # 提交函数而不是调用结果
                        self.logger.info(f"早安问候WeChat推送任务已提交到线程池: {user_id}")

                        # 🔥 香草关键修复：不等待结果，采用异步模式
                        successful_sends += 1  # 假设成功，让线程池在后台处理
                        self.logger.success(f"☀️ 早安问候任务已提交: {user_id}")

                        # 🔥 添加短暂延迟，避免并发冲突
                        import time
                        time.sleep(0.1)  # 100ms延迟

                    else:
                        # 非调度器线程，直接使用异步推送
                        try:
                            # 🔥 修复：直接使用异步方法，不创建额外的任务
                            success = await wechat_push_service.push_message(
                                message_type="morning_greeting",
                                content=greeting_content,
                                target_user_id=user_id,
                                message_level="user",
                                priority="normal",
                                metadata=metadata
                            )

                            if success:
                                successful_sends += 1
                                self.logger.success(f"✅ ☀️ 林嫣然主动发送早安问候给 {user_id}")
                            else:
                                self.logger.error(f"❌ 早安问候推送失败: {user_id}")

                        except Exception as push_error:
                            self.logger.error(f"❌ 早安问候推送异常: {user_id}, 错误: {push_error}")
                            # 🔥 修复：如果异步推送失败，尝试使用同步方法作为备用
                            try:
                                self.logger.warning(f"🔄 尝试使用同步推送方式: {user_id}")
                                success = wechat_push_service.push_message_sync(
                                    message_type="morning_greeting",
                                    content=greeting_content,
                                    target_user_id=user_id,
                                    message_level="user",
                                    priority="normal",
                                    metadata=metadata
                                )

                                if success:
                                    successful_sends += 1
                                    self.logger.success(f"✅ ☀️ 林嫣然主动发送早安问候给 {user_id} (同步方式)")
                                else:
                                    self.logger.error(f"❌ 早安问候同步推送也失败: {user_id}")

                            except Exception as sync_error:
                                self.logger.error(f"❌ 早安问候同步推送异常: {user_id}, 错误: {sync_error}")

                    # 添加短暂延迟，避免并发冲突
                    import time
                    time.sleep(0.1)  # 100ms延迟

                    # 用户间延迟
                    await asyncio.sleep(1)  # 1秒延迟

                except Exception as user_error:
                    self.logger.error(f"💬 🌅 为用户 {user_id} 发送早安问候失败: {user_error}")
                    continue

            # 记录发送结果
            total_users = len(users_to_greet)
            success_rate = (successful_sends / total_users) * 100 if total_users > 0 else 0

            self.logger.success(f"💬 🌅 早安问候发送完成，成功发送 {successful_sends}/{total_users} 条 ({success_rate:.1f}%)")

            if successful_sends > 0:
                # 记录已发送状态
                self._mark_expression_sent(ExpressionType.GREETING, today_key)
                self.logger.success("💬 🌅 ✅ 早安问候表达执行成功")
            else:
                self.logger.warning("💬 🌅 ⚠️ 早安问候表达失败，没有成功发送任何消息")

        except Exception as e:
            self.logger.error(f"💬 🌅 发送早安问候异常: {e}")
            import traceback
            self.logger.error(f"💬 🌅 详细错误信息: {traceback.format_exc()}")

    async def _generate_greeting_via_chat_skill(self, user_info: Dict[str, Any]) -> Optional[str]:
        """
        🔥 P0修复：通过Chat Skill生成早安问候，统一使用AI响应生成
        """
        try:
            from cognitive_modules.skills.chat_skill import ChatSkill

            # 创建Chat Skill实例
            chat_skill = ChatSkill()

            user_id = user_info.get('user_id', 'unknown')
            user_name = user_info.get('name', '朋友')
            relationship_level = user_info.get('relationship_level', 'friend')

            # 构建早安问候的提示
            greeting_prompt = f"请为我的好友{user_name}生成一条温暖的早安问候。关系级别：{relationship_level}。要求简洁温暖，体现林嫣然的个性。"

            # 生成会话ID
            session_id = f"morning_greeting_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 🔥 使用Chat Skill生成AI响应
            result = chat_skill.execute(
                input_text=greeting_prompt,
                user_id=user_id,
                session_id=session_id,
                context_data={
                    'source': 'morning_greeting',
                    'user_name': user_name,
                    'relationship_level': relationship_level,
                    'is_proactive': True,
                    'greeting_type': 'morning'
                }
            )

            if result.get('success') and result.get('response'):
                greeting_content = result.get('response', '').strip()

                # 🔥 P1优化：增强内容质量控制
                validated_content = self._validate_greeting_content(greeting_content)
                if validated_content:
                    self.logger.debug(f"💬 🌅 Chat Skill生成早安问候: {validated_content[:30]}...")
                    return validated_content

            # 降级到智能模板
            self.logger.debug(f"💬 🌅 Chat Skill生成失败，使用智能模板")
            return self._generate_intelligent_greeting_template(user_info, {
                'current_time': datetime.now().strftime('%H:%M'),
                'weather': '天气不错',
                'user_interests': []
            })

        except Exception as e:
            self.logger.error(f"💬 🌅 通过Chat Skill生成早安问候失败: {e}")
            # 最终降级
            return f"早上好{user_info.get('name', '朋友')}！新的一天开始了，祝你愉快～"

    async def _batch_update_user_interaction_stats(self, users: List[Dict[str, Any]]):
        """
        🔥 P1优化：批量更新用户交互统计，提高数据库查询性能
        """
        try:
            if not users:
                return

            from connectors.database.mysql_connector import get_instance as get_mysql_connector
            mysql_connector = get_mysql_connector()

            if not mysql_connector or not mysql_connector.is_available:
                self.logger.warning("💬 🌅 数据库不可用，使用默认交互统计")
                return

            # 构建批量查询的用户ID列表
            user_ids = [user['user_id'] for user in users]

            # 🔥 使用IN查询批量获取所有用户的交互统计
            placeholders = ','.join(['%s'] * len(user_ids))
            batch_query = f"""
            SELECT user_id, COUNT(*) as interaction_count,
                   MAX(timestamp) as last_interaction_time
            FROM messages
            WHERE user_id IN ({placeholders})
            AND role = 'user'
            AND timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY user_id
            """

            success, results, error = mysql_connector.query(batch_query, tuple(user_ids))

            if success and results:
                # 创建用户ID到统计数据的映射
                stats_map = {}
                for result in results:
                    user_id = result.get('user_id')
                    interaction_count = result.get('interaction_count', 1)
                    stats_map[user_id] = {
                        'interaction_count': max(1, interaction_count),
                        'last_interaction_time': result.get('last_interaction_time')
                    }

                # 更新用户数据
                for user in users:
                    user_id = user['user_id']
                    if user_id in stats_map:
                        stats = stats_map[user_id]
                        user['interaction_count'] = stats['interaction_count']
                        user['relationship_level'] = self._determine_relationship_level(stats['interaction_count'])
                        if stats['last_interaction_time']:
                            user['last_interaction'] = stats['last_interaction_time']
                    else:
                        # 没有交互记录的用户使用默认值
                        user['interaction_count'] = 1
                        user['relationship_level'] = 'acquaintance'

                self.logger.debug(f"💬 🌅 批量更新了 {len(users)} 个用户的交互统计")
            else:
                self.logger.warning(f"💬 🌅 批量查询交互统计失败: {error}")

        except Exception as e:
            self.logger.error(f"💬 🌅 批量更新用户交互统计失败: {e}")
            # 失败时保持默认值

    def _validate_greeting_content(self, content: str) -> Optional[str]:
        """
        🔥 P1优化：验证早安问候内容质量
        包括长度检查、敏感词过滤、重复内容检测
        """
        try:
            if not content or not isinstance(content, str):
                return None

            content = content.strip()

            # 1. 长度检查
            if len(content) < 5:
                self.logger.debug("💬 🌅 内容过短，不符合质量要求")
                return None

            if len(content) > 200:
                self.logger.debug("💬 🌅 内容过长，进行截断")
                content = content[:200] + "..."

            # 2. 基础敏感词过滤
            sensitive_words = [
                '死', '杀', '病毒', '政治', '敏感', '违法', '犯罪',
                '赌博', '色情', '暴力', '恐怖', '极端'
            ]

            content_lower = content.lower()
            for word in sensitive_words:
                if word in content_lower:
                    self.logger.warning(f"💬 🌅 检测到敏感词: {word}，内容被拒绝")
                    return None

            # 3. 检查是否包含基本的问候元素
            greeting_indicators = ['早', '好', '安', '晨', '天', '日', '愉快', '开心', '祝']
            has_greeting = any(indicator in content for indicator in greeting_indicators)

            if not has_greeting:
                self.logger.debug("💬 🌅 内容缺少问候元素，可能不是有效的早安问候")
                # 不直接拒绝，但记录日志

            # 4. 简单的重复检查（检查是否全是重复字符）
            if len(set(content.replace(' ', ''))) < 3:
                self.logger.debug("💬 🌅 内容重复度过高")
                return None

            # 5. 检查是否包含明显的错误格式
            if content.count('```') > 0 or content.count('##') > 0:
                self.logger.debug("💬 🌅 内容包含格式标记，进行清理")
                content = content.replace('```', '').replace('##', '').strip()

            return content

        except Exception as e:
            self.logger.error(f"💬 🌅 内容验证失败: {e}")
            return content  # 验证失败时返回原内容
    
    async def _get_users_for_morning_greeting(self) -> List[Dict[str, Any]]:
        """获取需要早安问候的好友列表 - 只对contacts.json中的真实联系人"""
        try:
            # 🔥 老王修复：直接从contacts.json获取真实联系人，而不是从数据库
            from core.contacts_manager import get_contacts_manager
            contacts_manager = get_contacts_manager()
            
            # 获取所有联系人
            all_contacts = contacts_manager.get_all_users()
            
            users = []
            for contact in all_contacts:
                user_id = contact.get("wxid")
                nickname = contact.get("nickname", "")
                
                # 🔥 严格过滤：确保是有效的联系人
                if not user_id or not nickname:
                    self.logger.debug(f"💬 🌅 跳过无效联系人: user_id={user_id}, nickname={nickname}")
                    continue
                
                # 🔥 排除无效ID
                invalid_ids = ["system", "default_user", "anonymous", "unknown"]
                if user_id.lower() in invalid_ids or any(invalid in user_id.lower() for invalid in invalid_ids):
                    self.logger.debug(f"💬 🌅 跳过无效好友ID: {user_id}")
                    continue
                
                # 🔥 排除群聊好友
                if user_id.startswith('chatroom_') or '@chatroom' in user_id:
                    self.logger.debug(f"💬 🌅 跳过群聊好友: {user_id}")
                    continue
                
                # 🔥 排除测试好友（如果有多个联系人）
                if user_id.startswith('test_') and len(all_contacts) > 1:
                    self.logger.debug(f"💬 🌅 跳过测试好友: {user_id}")
                    continue
                
                # 🔥 P1优化：检查用户偏好设置
                if not self._check_user_greeting_preference(contact):
                    self.logger.debug(f"💬 🌅 用户 {user_id} 不希望接收早安问候，跳过")
                    continue

                # 🔥 P1优化：先收集有效用户，稍后批量查询交互统计
                users.append({
                    'user_id': user_id,
                    'name': nickname,
                    'interaction_count': 1,  # 临时默认值
                    'last_interaction': contact.get('last_seen', 0),
                    'relationship_level': 'friend',  # 临时默认值
                    'greeting_preference': contact.get('receive_morning_greeting', True)  # 默认允许
                })
            
            # 🔥 如果没有有效联系人，返回空列表
            if not users:
                self.logger.warning(f"💬 🌅 contacts.json中没有有效的联系人，早安问候将被跳过")
                return []

            # 🔥 P1优化：批量查询所有用户的交互统计，提高性能
            await self._batch_update_user_interaction_stats(users)
            
            # 🔥 按交互次数排序，优先选择活跃好友
            users.sort(key=lambda u: u.get("interaction_count", 0), reverse=True)
            
            self.logger.info(f"💬 🌅 从contacts.json获取到 {len(users)} 个有效联系人进行早安问候")
            return users
                
        except Exception as e:
            self.logger.error(f"💬 🌅 获取早安问候好友列表失败: {e}")
            return []
    
    def _determine_relationship_level(self, interaction_count: int) -> str:
        """根据交互次数确定关系级别"""
        if interaction_count >= 50:
            return 'close_friend'
        elif interaction_count >= 20:
            return 'friend'
        elif interaction_count >= 10:
            return 'acquaintance'
        else:
            return 'stranger'
    
    def _get_real_user_name(self, user_id: str) -> str:
        """
        获取真实好友名，优先从统一好友管理器获取
        
        Args:
            user_id: 好友ID
            
        Returns:
            真实好友名
        """
        try:
            # 🔥 P0级别修复：优先从统一好友管理器获取真实好友名
            from core.unified_user_manager import get_unified_user_manager
            user_manager = get_unified_user_manager()
            
            if user_manager:
                user = user_manager.get_user(user_id)
                if user and user.name and user.name != "命令行好友":
                    # 🔥 老王修复：不过滤"神秘嘉宾"，但优先使用更好的名称
                    if user.name != "神秘嘉宾" or not user.nickname:
                        self.logger.debug(f"✅ 从统一好友管理器获取真实好友名: {user.name}")
                        return user.name
                elif user and user.nickname and user.nickname != "命令行好友":
                    self.logger.debug(f"✅ 从统一好友管理器获取好友昵称: {user.nickname}")
                    return user.nickname
            
            # 🔥 降级：从contacts.json获取nickname
            from core.contacts_manager import get_contacts_manager
            contacts_manager = get_contacts_manager()
            
            if contacts_manager:
                user_info = contacts_manager.get_user_info(user_id)
                if user_info and user_info.get('nickname'):
                    nickname = user_info['nickname']
                    if nickname != "命令行好友":
                        # 🔥 老王修复：接受"神秘嘉宾"，但记录为降级使用
                        if nickname == "神秘嘉宾":
                            self.logger.debug(f"⚠️ 从联系人管理器获取降级好友名: {nickname}")
                        else:
                            self.logger.debug(f"✅ 从联系人管理器获取真实好友名: {nickname}")
                        return nickname
            
            # 🔥 降级：从数据库users表获取name
            from utilities.database_utils import execute_with_retry
            query = "SELECT name FROM users WHERE id = %s"
            result = execute_with_retry(query, (user_id,))
            
            if result and len(result) > 0:
                name = result[0].get('name')
                if name and name != "命令行好友":
                    # 🔥 老王修复：接受"神秘嘉宾"，但记录为降级使用
                    if name == "神秘嘉宾":
                        self.logger.debug(f"⚠️ 从数据库获取降级好友名: {name}")
                    else:
                        self.logger.debug(f"✅ 从数据库获取真实好友名: {name}")
                    return name
            
            # 🔥 最后降级：针对群聊ID和普通好友ID分别处理
            if user_id.endswith("@chatroom"):
                # 群聊ID处理
                chatroom_id = user_id.split("@")[0]
                if len(chatroom_id) > 6:
                    fallback_name = f"群聊{chatroom_id[-6:]}"
                else:
                    fallback_name = "群聊好友"
            else:
                # 普通好友ID处理
                if len(user_id) > 6:
                    fallback_name = f"好友{user_id[-6:]}"
                else:
                    fallback_name = "朋友"
            
            self.logger.warning(f"⚠️ 无法获取真实好友名，使用降级方案: {fallback_name} (user_id: {user_id})")
            return fallback_name
                
        except Exception as e:
            self.logger.warning(f"获取好友 {user_id} 真实名称失败: {e}")
            # 异常时使用默认名称
            if user_id.endswith("@chatroom"):
                return "群聊好友"
            elif len(user_id) > 6:
                return f"好友{user_id[-6:]}"
            else:
                return "朋友"
    
    async def _generate_personalized_morning_greeting(self, user_info: Dict[str, Any]) -> Optional[str]:
        """生成个性化早安问候内容 - 增强AI智能决策"""
        try:
            # 🔥 构建林嫣然的智能早安问候提示词
            user_name = user_info.get('name', '朋友')
            relationship_level = user_info.get('relationship_level', 'friend')
            interaction_count = user_info.get('interaction_count', 0)
            last_interaction = user_info.get('last_interaction', '未知')
            
            # 🔥 增强上下文：获取当前市场状态、天气、好友历史偏好
            current_context = await self._gather_morning_greeting_context(user_info)
            
            greeting_prompt = f"""
你主动给好友 {user_name} 发一条早安问候

## 好友信息
- 称呼：{user_name}
- 关系级别：{relationship_level}
- 最后互动：{last_interaction}

## 参考信息
- 当前时间：{current_context.get('current_time', '早上')}
- 天气状况：{current_context.get('weather', '未知')}
- 好友兴趣：{current_context.get('user_interests', [])}

个性化的早安问候
"""
            
            # 🔥 调用AI生成个性化问候
            from adapters.ai_service_adapter import get_instance as get_ai_adapter
            ai_adapter = get_ai_adapter()
            
            if ai_adapter and ai_adapter.is_available():
                response = ai_adapter.generate_response(
                    prompt=greeting_prompt,
                    max_tokens=150,
                    temperature=0.8
                )
                
                if response and response.get('success'):
                    greeting_content = response.get('content', '').strip()
                    if greeting_content:
                        self.logger.info(f"🌅 AI生成个性化早安问候: {greeting_content}")
                        return greeting_content
            
            # 🔥 如果AI不可用，使用智能模板系统
            return self._generate_intelligent_greeting_template(user_info, current_context)
            
        except Exception as e:
            self.logger.error(f"生成个性化早安问候失败: {e}")
            return f"早上好！新的一天开始了，祝你今天愉快～"
    
    async def _gather_morning_greeting_context(self, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 P2优化：收集早安问候的上下文信息，增强个性化数据
        """
        try:
            context = {}

            # 获取当前时间
            from datetime import datetime
            now = datetime.now()
            context['current_time'] = now.strftime('%H:%M')
            context['date'] = now.strftime('%Y-%m-%d')
            context['weekday'] = now.strftime('%A')
            context['is_weekend'] = now.weekday() >= 5

            # 🔥 P2优化：增强天气信息收集，添加缓存
            context['weather'] = await self._get_enhanced_weather_info()

            # 🔥 P2优化：获取更丰富的市场信息
            context['market_info'] = self._get_enhanced_market_info(now)

            # 🔥 P2优化：获取节日和特殊日期信息
            context['special_dates'] = self._get_special_date_info(now)
            
            # 🔥 P2优化：深度分析用户兴趣和偏好
            context['user_profile'] = await self._analyze_user_profile(user_info)

            # 🔥 P2优化：获取最近的对话主题
            context['recent_topics'] = await self._get_recent_conversation_topics(user_info.get('user_id'))

            # 🔥 P2优化：获取用户的活跃时间偏好
            context['user_activity_pattern'] = await self._analyze_user_activity_pattern(user_info.get('user_id'))

            return context

        except Exception as e:
            self.logger.error(f"收集早安问候上下文失败: {e}")
            return {
                'current_time': '早上',
                'weather': '天气不错',
                'market_info': {'status': '市场状态正常'},
                'user_profile': {'interests': [], 'personality': 'friendly'},
                'special_dates': {},
                'recent_topics': [],
                'user_activity_pattern': {}
            }
    
    def _generate_intelligent_greeting_template(self, user_info: Dict[str, Any], context: Dict[str, Any]) -> str:
        """
        🔥 P1优化：生成智能问候模板，增加多样性和个性化
        """
        try:
            import random
            from datetime import datetime

            user_name = user_info.get('name', '朋友')
            relationship_level = user_info.get('relationship_level', 'friend')
            user_interests = context.get('user_interests', [])
            weather = context.get('weather', '天气不错')
            current_time = datetime.now()

            # 🔥 P1优化：根据时间、季节、天气、关系等多维度生成模板
            templates = []

            # 获取时间相关的问候前缀
            time_prefixes = self._get_time_based_prefixes(current_time)

            # 获取天气相关的描述
            weather_descriptions = self._get_weather_descriptions(weather)

            # 获取季节相关的元素
            seasonal_elements = self._get_seasonal_elements(current_time)

            if relationship_level == 'close_friend':
                base_templates = [
                    f"{random.choice(time_prefixes)}{user_name}！{random.choice(weather_descriptions)}",
                    f"{user_name}{random.choice(['早上好', '早安', '早～'])}！{random.choice(seasonal_elements)}",
                    f"嗨{user_name}！{random.choice(['新的一天开始啦', '又是美好的一天', '今天感觉会很棒'])}～",
                    f"{user_name}，{random.choice(['早安', '早上好'])}！{random.choice(['希望你今天心情美美的', '愿你今天充满活力', '祝你今天一切顺心'])}",
                ]
                templates.extend(base_templates)

                # 兴趣相关的个性化模板
                if '财经' in user_interests or '投资' in user_interests:
                    financial_templates = [
                        f"早安{user_name}！今天的市场数据看起来挺有意思的",
                        f"{user_name}早！关注一下今天的经济动态吧",
                        f"早上好{user_name}！今天的财经新闻值得看看"
                    ]
                    templates.extend(financial_templates)

                if '运动' in user_interests or '健身' in user_interests:
                    sports_templates = [
                        f"早安{user_name}！今天适合运动，记得活动活动哦",
                        f"{user_name}早！新的一天，新的活力～"
                    ]
                    templates.extend(sports_templates)

            elif relationship_level == 'friend':
                base_templates = [
                    f"{random.choice(time_prefixes)}{user_name}！{random.choice(weather_descriptions)}",
                    f"{user_name}{random.choice(['早上好', '早安'])}！{random.choice(seasonal_elements)}",
                    f"早上好{user_name}！{random.choice(['希望你今天愉快', '祝你今天顺利', '愿你今天开心'])}",
                    f"{user_name}，{random.choice(['早安', '早上好'])}！{random.choice(['新的一天，新的开始', '美好的一天开始了', '今天也要加油哦'])}",
                ]
                templates.extend(base_templates)

                if '财经' in user_interests:
                    templates.append(f"早安{user_name}！今天市场可能有些变化，记得关注")

            else:  # acquaintance
                polite_templates = [
                    f"{random.choice(time_prefixes)}{user_name}！{random.choice(['祝你今天一切顺利', '希望你今天愉快', '愿你今天充满活力'])}",
                    f"{user_name}{random.choice(['早上好', '早安'])}！{random.choice(seasonal_elements)}",
                    f"早上好{user_name}！{random.choice(['新的一天，新的机会', '美好的一天开始了', '祝你今天工作顺利'])}",
                ]
                templates.extend(polite_templates)

            # 🔥 P1优化：添加节日和特殊日期的模板
            special_templates = self._get_special_date_templates(current_time, user_name)
            if special_templates:
                templates.extend(special_templates)

            # 如果没有合适的模板，使用默认
            if not templates:
                templates = [f"早上好{user_name}！新的一天开始了，祝你愉快～"]

            # 🔥 P1优化：避免重复，记录最近使用的模板
            selected_template = self._select_non_repetitive_template(templates, user_info.get('user_id'))

            return selected_template

        except Exception as e:
            self.logger.error(f"生成智能问候模板失败: {e}")
            return f"早上好！新的一天开始了，祝你愉快～"

    def _get_time_based_prefixes(self, current_time) -> List[str]:
        """🔥 P1优化：获取基于时间的问候前缀"""
        hour = current_time.hour
        if hour < 7:
            return ["早安", "早上好"]
        elif hour < 9:
            return ["早安", "早上好", "早～"]
        else:
            return ["上午好", "早上好"]

    def _get_weather_descriptions(self, weather: str) -> List[str]:
        """🔥 P1优化：获取天气相关的描述"""
        if '晴' in weather or '阳光' in weather:
            return ["阳光明媚呢", "天气真不错", "阳光很棒"]
        elif '雨' in weather:
            return ["今天有雨，记得带伞", "雨天也有雨天的美好", "雨声很舒缓"]
        elif '云' in weather or '阴' in weather:
            return ["天气还不错", "云朵很有意思", "适合思考的天气"]
        else:
            return ["天气不错", "空气很清新", "感觉很舒适"]

    def _get_seasonal_elements(self, current_time) -> List[str]:
        """🔥 P1优化：获取季节相关的元素"""
        month = current_time.month
        if month in [3, 4, 5]:  # 春季
            return ["春天的气息很棒", "万物复苏的季节", "春意盎然"]
        elif month in [6, 7, 8]:  # 夏季
            return ["夏日的活力", "阳光充足的日子", "充满活力的季节"]
        elif month in [9, 10, 11]:  # 秋季
            return ["秋高气爽", "收获的季节", "金秋时节"]
        else:  # 冬季
            return ["冬日的温暖", "宁静的季节", "温馨的时光"]

    def _get_special_date_templates(self, current_time, user_name: str) -> List[str]:
        """🔥 P1优化：获取特殊日期的模板"""
        month, day = current_time.month, current_time.day
        weekday = current_time.weekday()

        templates = []

        # 周一特殊问候
        if weekday == 0:
            templates.extend([
                f"早安{user_name}！新的一周开始了，加油！",
                f"{user_name}早上好！周一的活力满满～"
            ])

        # 周五特殊问候
        elif weekday == 4:
            templates.extend([
                f"早安{user_name}！周五啦，周末就在眼前～",
                f"{user_name}早上好！TGIF，今天心情特别好"
            ])

        # 节日问候（简单示例）
        if month == 1 and day == 1:
            templates.append(f"新年快乐{user_name}！新的一年，新的开始～")
        elif month == 2 and day == 14:
            templates.append(f"早安{user_name}！情人节快乐～")
        elif month == 5 and day == 1:
            templates.append(f"早安{user_name}！劳动节快乐，今天好好休息～")

        return templates

    def _select_non_repetitive_template(self, templates: List[str], user_id: str) -> str:
        """🔥 P1优化：选择非重复的模板 - 使用Redis缓存"""
        import random
        from utilities.redis_cache_adapter import get_redis_cache

        # 使用Redis缓存替代内存缓存
        redis_cache = get_redis_cache()
        cache_key = f"recent_templates_{user_id}"

        # 从Redis获取用户最近使用的模板
        user_recent = redis_cache.get(cache_key, namespace="templates") or []

        # 过滤掉最近使用过的模板
        available_templates = [t for t in templates if t not in user_recent[-3:]]  # 避免最近3次的重复

        if not available_templates:
            available_templates = templates  # 如果都用过了，重新开始

        selected = random.choice(available_templates)

        # 记录使用历史到Redis
        user_recent.append(selected)

        # 只保留最近5次的记录
        if len(user_recent) > 5:
            user_recent = user_recent[-5:]

        # 保存到Redis，TTL设置为7天
        redis_cache.set(cache_key, user_recent, ttl=7*24*3600, namespace="templates")

        return selected

    def _check_user_greeting_preference(self, contact: Dict[str, Any]) -> bool:
        """
        🔥 P1优化：检查用户的早安问候偏好设置
        """
        try:
            # 1. 检查明确的偏好设置
            receive_greeting = contact.get('receive_morning_greeting')
            if receive_greeting is False:
                return False

            # 2. 检查免打扰时间设置
            do_not_disturb = contact.get('do_not_disturb_hours', {})
            if do_not_disturb:
                from datetime import datetime
                current_hour = datetime.now().hour
                start_hour = do_not_disturb.get('start', 23)
                end_hour = do_not_disturb.get('end', 7)

                # 处理跨天的免打扰时间
                if start_hour > end_hour:  # 例如 23:00 - 07:00
                    if current_hour >= start_hour or current_hour <= end_hour:
                        return False
                else:  # 例如 01:00 - 06:00
                    if start_hour <= current_hour <= end_hour:
                        return False

            # 3. 检查用户活跃度（如果最近30天没有交互，可能不希望接收）
            last_seen = contact.get('last_seen', 0)
            if last_seen > 0:
                from datetime import datetime, timedelta
                last_seen_date = datetime.fromtimestamp(last_seen)
                if datetime.now() - last_seen_date > timedelta(days=30):
                    self.logger.debug(f"💬 🌅 用户超过30天未活跃，跳过早安问候")
                    return False

            # 4. 检查用户类型（排除某些特殊用户）
            user_type = contact.get('user_type', 'normal')
            if user_type in ['bot', 'service', 'system']:
                return False

            # 默认允许发送
            return True

        except Exception as e:
            self.logger.error(f"💬 🌅 检查用户偏好失败: {e}")
            return True  # 出错时默认允许

    async def _get_enhanced_weather_info(self) -> str:
        """🔥 P2优化：获取增强的天气信息，添加缓存"""
        try:
            # 简单的缓存机制
            if not hasattr(self, '_weather_cache'):
                self._weather_cache = {'data': None, 'timestamp': 0}

            import time
            current_time = time.time()

            # 如果缓存数据在30分钟内，直接使用
            if (current_time - self._weather_cache['timestamp']) < 1800 and self._weather_cache['data']:
                return self._weather_cache['data']

            # 获取新的天气数据
            from connectors.database.mysql_connector import get_instance as get_mysql_connector
            mysql_connector = get_mysql_connector()

            if mysql_connector and mysql_connector.is_available:
                # 🔥 修复：移除不存在的 weather_desc 字段
                weather_query = """
                SELECT temperature, wind_direction, wind_power, pm25, humidity
                FROM weather
                ORDER BY update_time DESC
                LIMIT 1
                """
                success, weather_data, error = mysql_connector.query_one(weather_query, None)

                if success and weather_data:
                    # 组合详细的天气描述
                    weather_desc = f"{weather_data.get('temperature', 'N/A')}°C"

                    if weather_data.get('wind_direction') and weather_data.get('wind_power'):
                        weather_desc += f"，{weather_data.get('wind_direction')}{weather_data.get('wind_power')}级风"

                    if weather_data.get('humidity'):
                        weather_desc += f"，湿度{weather_data.get('humidity')}%"

                    # 更新缓存
                    self._weather_cache = {'data': weather_desc, 'timestamp': current_time}
                    return weather_desc

            # 降级到默认值
            default_weather = "天气不错"
            self._weather_cache = {'data': default_weather, 'timestamp': current_time}
            return default_weather

        except Exception as e:
            self.logger.error(f"💬 🌅 获取增强天气信息失败: {e}")
            return "天气不错"

    def _get_enhanced_market_info(self, current_time) -> Dict[str, Any]:
        """🔥 P2优化：获取增强的市场信息"""
        try:
            hour = current_time.hour
            weekday = current_time.weekday()

            market_info = {
                'status': '市场状态正常',
                'phase': 'closed',
                'description': ''
            }

            # 工作日市场时间判断
            if weekday < 5:  # 周一到周五
                if 9 <= hour < 12:
                    market_info.update({
                        'status': '市场上午交易时间',
                        'phase': 'morning_trading',
                        'description': '上午交易活跃期'
                    })
                elif 13 <= hour < 15:
                    market_info.update({
                        'status': '市场下午交易时间',
                        'phase': 'afternoon_trading',
                        'description': '下午交易时段'
                    })
                elif 8 <= hour < 9:
                    market_info.update({
                        'status': '市场开盘在即',
                        'phase': 'pre_market',
                        'description': '开盘前准备时间'
                    })
                elif 15 <= hour < 18:
                    market_info.update({
                        'status': '市场收盘后',
                        'phase': 'post_market',
                        'description': '收盘后分析时间'
                    })
            else:  # 周末
                market_info.update({
                    'status': '市场休市',
                    'phase': 'weekend',
                    'description': '周末休息时间'
                })

            return market_info

        except Exception as e:
            self.logger.error(f"💬 🌅 获取市场信息失败: {e}")
            return {'status': '市场状态正常', 'phase': 'unknown', 'description': ''}

    def _get_special_date_info(self, current_time) -> Dict[str, Any]:
        """🔥 修复：获取特殊日期信息"""
        try:
            month, day = current_time.month, current_time.day
            weekday = current_time.weekday()

            special_info = {
                'is_holiday': False,
                'holiday_name': '',
                'is_special_day': False,
                'special_day_type': '',
                'week_position': ''
            }

            # 周位置
            if weekday == 0:
                special_info['week_position'] = 'monday'
                special_info['is_special_day'] = True
                special_info['special_day_type'] = 'week_start'
            elif weekday == 4:
                special_info['week_position'] = 'friday'
                special_info['is_special_day'] = True
                special_info['special_day_type'] = 'week_end_approaching'
            elif weekday >= 5:
                special_info['week_position'] = 'weekend'
                special_info['is_special_day'] = True
                special_info['special_day_type'] = 'weekend'

            # 节日检查（简单版本）
            holidays = {
                (1, 1): '元旦',
                (2, 14): '情人节',
                (3, 8): '妇女节',
                (4, 1): '愚人节',
                (5, 1): '劳动节',
                (6, 1): '儿童节',
                (10, 1): '国庆节',
                (12, 25): '圣诞节'
            }

            if (month, day) in holidays:
                special_info['is_holiday'] = True
                special_info['holiday_name'] = holidays[(month, day)]
                special_info['is_special_day'] = True
                special_info['special_day_type'] = 'holiday'

            return special_info

        except Exception as e:
            self.logger.error(f"💬 🌅 获取特殊日期信息失败: {e}")
            return {
                'is_holiday': False,
                'holiday_name': '',
                'is_special_day': False,
                'special_day_type': '',
                'week_position': ''
            }

    async def _analyze_user_profile(self, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """🔥 修复：分析用户画像"""
        try:
            user_id = user_info.get('user_id')
            profile = {
                'interests': [],
                'personality': 'friendly',
                'communication_style': 'casual',
                'activity_level': 'normal'
            }

            if not user_id:
                return profile

            # 基于交互次数判断活跃度
            interaction_count = user_info.get('interaction_count', 1)
            if interaction_count >= 10:
                profile['activity_level'] = 'high'
                profile['personality'] = 'close'
            elif interaction_count >= 5:
                profile['activity_level'] = 'medium'
                profile['personality'] = 'friendly'
            else:
                profile['activity_level'] = 'low'
                profile['personality'] = 'polite'

            # 简单的兴趣分析（可以后续扩展）
            relationship_level = user_info.get('relationship_level', 'friend')
            if relationship_level == 'close_friend':
                profile['interests'] = ['生活', '分享']
                profile['communication_style'] = 'intimate'
            elif relationship_level == 'friend':
                profile['interests'] = ['日常', '工作']
                profile['communication_style'] = 'casual'
            else:
                profile['interests'] = ['基本']
                profile['communication_style'] = 'polite'

            return profile

        except Exception as e:
            self.logger.error(f"💬 🌅 分析用户画像失败: {e}")
            return {
                'interests': [],
                'personality': 'friendly',
                'communication_style': 'casual',
                'activity_level': 'normal'
            }

    async def _get_recent_conversation_topics(self, user_id: str) -> List[str]:
        """🔥 修复：获取最近对话主题"""
        try:
            if not user_id:
                return []

            # 简单的主题提取（可以后续使用AI分析）
            topics = []

            from connectors.database.mysql_connector import get_instance as get_mysql_connector
            mysql_connector = get_mysql_connector()

            if mysql_connector and mysql_connector.is_available:
                topic_query = """
                SELECT content
                FROM messages
                WHERE user_id = %s
                AND role = 'user'
                AND timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                ORDER BY timestamp DESC
                LIMIT 5
                """
                success, results, error = mysql_connector.query(topic_query, (user_id,))

                if success and results:
                    # 简单的主题关键词提取
                    topic_keywords = {
                        '工作': ['工作', '上班', '公司', '项目', '会议'],
                        '生活': ['生活', '家里', '休息', '睡觉', '吃饭'],
                        '财经': ['股票', '投资', '市场', '经济', '金融'],
                        '技术': ['技术', 'AI', '科技', '程序', '代码'],
                        '情感': ['心情', '感受', '想法', '开心', '难过']
                    }

                    all_content = ' '.join([r.get('content', '') for r in results])

                    for topic, keywords in topic_keywords.items():
                        if any(keyword in all_content for keyword in keywords):
                            topics.append(topic)

            return topics[:3]  # 最多返回3个主题

        except Exception as e:
            self.logger.error(f"💬 🌅 获取最近对话主题失败: {e}")
            return []

    async def _analyze_user_activity_pattern(self, user_id: str) -> Dict[str, Any]:
        """🔥 修复：分析用户活跃时间模式"""
        try:
            if not user_id:
                return {}

            pattern = {
                'preferred_hours': [],
                'most_active_time': 'morning',
                'response_speed': 'normal'
            }

            # 简单的活跃时间分析（可以后续基于历史数据分析）
            from datetime import datetime
            current_hour = datetime.now().hour

            # 基于当前时间推测偏好（简化版）
            if 6 <= current_hour < 12:
                pattern['most_active_time'] = 'morning'
                pattern['preferred_hours'] = [7, 8, 9, 10, 11]
            elif 12 <= current_hour < 18:
                pattern['most_active_time'] = 'afternoon'
                pattern['preferred_hours'] = [13, 14, 15, 16, 17]
            else:
                pattern['most_active_time'] = 'evening'
                pattern['preferred_hours'] = [19, 20, 21, 22]

            return pattern

        except Exception as e:
            self.logger.error(f"💬 🌅 分析用户活跃模式失败: {e}")
            return {}

    def _start_timer_based_monitoring(self):
        """启动基于定时器的监控（主要模式）"""
        try:
            import threading
            import time
            
            def timer_monitoring():
                """定时器监控函数"""
                self.logger.info("💬 🚀 定时器监控线程启动，等待系统完全启动...")
                
                # 🔥 老王修复：智能启动等待，确保早安问候不被错过
                current_hour = datetime.now().hour
                if 7 <= current_hour <= 9:  # 早安问候关键时间段
                    startup_wait_time = 30  # 早安时间段只等30秒，快速启动
                    self.logger.info(f"💬 🌅 早安问候关键时间段，快速启动模式，等待 {startup_wait_time} 秒...")
                else:
                    startup_wait_time = 60  # 其他时间段等待1分钟
                    self.logger.info(f"💬 ⏰ 等待系统启动完成，预计等待 {startup_wait_time} 秒...")
                time.sleep(startup_wait_time)
                
                self.logger.info("💬 🚀 系统启动完成，数字生命体主动表达系统激活")
                monitor_count = 0
                
                while True:
                    try:
                        monitor_count += 1
                        current_time = time.strftime("%H:%M:%S")
                        
                        # 🔥 检查系统是否真正准备好
                        if not self._is_system_ready_for_expression():
                            if monitor_count % 5 == 1:  # 每5次检查记录一次
                                self.logger.info(f"💬 ⏰ 系统还未完全准备好，等待中... [{current_time}]")
                            time.sleep(60)  # 等待1分钟再检查
                            continue
                        
                        # 🔥 老王修复：更频繁的检查，提高主动表达概率
                        if monitor_count % 10 == 1:  # 每10次检查记录一次状态
                            self.logger.info(f"💬 ⏰ 第{monitor_count}次表达检查 [{current_time}] - 数字生命体保持活跃")
                        
                        # 同步版本的表达检查
                        self._sync_check_expression_triggers()
                        
                        # 🔥 老王修复：早安问候专用检查逻辑，避免错过时间窗口
                        current_hour = datetime.now().hour
                        current_minute = datetime.now().minute
                        
                        # 如果在早安问候时间段前后，使用更频繁的检查
                        if 8 <= current_hour <= 9:  # 早安问候关键时间段
                            check_interval = 300  # 5分钟检查一次
                            self.logger.debug(f"💬 🌅 早安问候关键时间段，使用高频检查: {check_interval//60}分钟")
                        else:
                            # 🔥 智能动态间隔：根据系统活跃度调整检查频率
                            base_interval = self.expression_config.get("check_interval", 1800)  # 基础30分钟，不再是1小时
                            # 动态调整：如果最近有表达，延长间隔；如果长时间无表达，缩短间隔
                            check_interval = self._calculate_dynamic_check_interval(base_interval)
                            self.logger.debug(f"💬 ⏰ 下次检查间隔: {check_interval//60}分钟")
                        
                        time.sleep(check_interval)
                        
                    except Exception as e:
                        self.logger.error(f"💬 定时器监控错误: {e}")
                        time.sleep(60)  # 出错时等待1分钟
            
            thread = threading.Thread(target=timer_monitoring, daemon=True, name="ProactiveExpressionTimer")
            thread.start()
            self.logger.success("💬 ✅ 基于定时器的表达监控已启动（主要模式）")
            
        except Exception as e:
            self.logger.error(f"💬 启动定时器监控失败: {e}")
    
    def _calculate_dynamic_check_interval(self, base_interval: int) -> int:
        """计算动态检查间隔"""
        try:
            import random
            current_time = time.time()

            # 获取最近表达时间
            if hasattr(self, 'last_expression_time') and self.last_expression_time:
                if isinstance(self.last_expression_time, datetime):
                    last_expr_timestamp = self.last_expression_time.timestamp()
                else:
                    last_expr_timestamp = self.last_expression_time

                time_since_last = current_time - last_expr_timestamp

                # 如果最近刚表达过（2小时内），延长间隔
                if time_since_last < 10800:  # 2小时
                    multiplier = 1.5
                    self.logger.debug("💬 ⏰ 最近有表达，延长检查间隔")
                # 如果很久没表达（6小时以上），缩短间隔
                elif time_since_last > 21600:  # 6小时
                    multiplier = 0.7
                    self.logger.debug("💬 ⏰ 长时间无表达，缩短检查间隔")
                else:
                    multiplier = 1.0
            else:
                # 首次运行，使用正常间隔
                multiplier = 1.0

            # 添加随机因子，避免过于机械化
            random_factor = random.uniform(0.8, 1.2)

            dynamic_interval = int(base_interval * multiplier * random_factor)

            # 🔥 老王修复：限制在合理范围内，确保早安问候不被阻塞
            # 早安问候关键时间段（7-10点）使用更短的最大间隔
            current_hour = datetime.now().hour
            if 7 <= current_hour <= 10:
                # 早安问候时间段：最短5分钟，最长15分钟
                dynamic_interval = max(300, min(900, dynamic_interval))
            else:
                # 其他时间段：最短15分钟，最长2小时
                dynamic_interval = max(900, min(10800, dynamic_interval))

            return dynamic_interval

        except Exception as e:
            self.logger.error(f"💬 计算动态间隔失败: {e}")
            return base_interval

    def _is_system_ready_for_expression(self) -> bool:
        """检查系统是否准备好进行主动表达"""
        try:
            from utilities.singleton_manager import get_silent
            
            # 检查关键组件是否已初始化
            required_components = [
                "wechat_unified_push_service", 
                "ai_service_adapter",
                "mysql_connector"
            ]
            
            for component in required_components:
                if not get_silent(component):
                    self.logger.debug(f"💬 ⏰ 关键组件 {component} 未准备好")
                    return False
            
            # 检查API服务是否启动
            try:
                import requests
                response = requests.get("http://127.0.0.1:56839/api/health", timeout=5)
                if response.status_code != 200:
                    self.logger.debug("💬 ⏰ API服务未准备好")
                    return False
            except Exception:
                self.logger.debug("💬 ⏰ API服务连接失败")
                return False
            
            # 检查数据收集器是否能正常工作（可选）
            try:
                real_time_collector = get_silent("real_time_data_collector")
                if real_time_collector and hasattr(real_time_collector, 'collect_financial_news'):
                    # 简单检查，不实际调用
                    self.logger.debug("💬 ⏰ 实时数据收集器已准备好")
                else:
                    self.logger.debug("💬 ⏰ 实时数据收集器未找到，使用兜底模式")
            except Exception:
                self.logger.debug("💬 ⏰ 实时数据收集器检查失败，使用兜底模式")
            
            return True
            
        except Exception as e:
            self.logger.error(f"💬 系统准备状态检查失败: {e}")
            return False
    
    def _sync_check_expression_triggers(self):
        """🔥 老王重构：智能版本的表达触发检查 - 支持串行/并行模式"""
        try:
            # 🧠 智能预检查：评估当前是否适合表达
            if not self._intelligent_pre_check():
                return

            # 🔥 根据配置选择触发模式
            trigger_mode = self.expression_config.get("trigger_mode", "parallel")

            if trigger_mode == "serial":
                # 保持原有串行逻辑，向后兼容
                self.logger.debug("💬 🔄 使用串行触发模式")
                self._legacy_serial_trigger_check()
            else:
                # 新的并行逻辑 - 数字生命多任务处理
                self.logger.debug("💬 🧠 使用并行触发模式 - 数字生命多任务处理")
                self._parallel_trigger_check()

        except Exception as e:
            self.logger.error(f"💬 智能表达触发检查失败: {e}")

    def _legacy_serial_trigger_check(self):
        """🔥 老王保留：原有串行触发逻辑 - 向后兼容"""
        try:
            # 🔥 优先级1：世界感知事件触发（最重要）
            if self._sync_world_perception_trigger():
                self.logger.info("💬 🌍 世界感知事件触发器激活，准备主动表达")
                try:
                    self._sync_execute_expression("event_based")
                    self.logger.success("💬 🌍 ✅ 世界感知事件触发的主动表达执行完成")
                except Exception as e:
                    self.logger.error(f"💬 🌍 ❌ 世界感知事件触发的主动表达执行失败: {e}")
                return

            # 🔥 优先级3：情感状态变化触发
            if self._sync_emotion_change_trigger():
                self.logger.info("💬 💝 情感状态变化触发器激活，准备主动表达")
                self._sync_execute_expression("emotion_based")
                return

            # 🔥 老王修复：启用记忆回忆触发
            if self._sync_memory_recall_trigger():
                self.logger.info("💬 🧠 记忆回忆触发器激活，准备主动表达")
                self._sync_execute_expression("memory_based")
                return

            # 🔥 优先级6：早安问候触发（重要的社交功能）
            if self._sync_morning_greeting_trigger():
                self.logger.info("💬 🌅 早安问候触发器激活，准备主动表达")
                self._sync_execute_expression("morning_greeting")
                return

            # 🔥 优先级7：创意冲动触发
            if self._sync_creative_impulse_trigger():
                self.logger.info("💬 🎨 创意冲动触发器激活，准备主动表达")
                self._sync_execute_expression("creative_based")
                return

        except Exception as e:
            self.logger.error(f"💬 串行触发检查失败: {e}")

    def _parallel_trigger_check(self):
        """🔥 老王新增：并行触发检查 - 数字生命核心逻辑，模拟人类大脑多任务处理"""
        try:
            # 🔥 老王性能优化：使用缓存版本的工作日判断 - 非工作日不打扰原则
            try:
                from utilities.workday_cache_manager import is_workday_fast
                is_workday_flag = is_workday_fast()
            except Exception as e:
                self.logger.warning(f"获取工作日信息失败，使用默认判断: {e}")
                from datetime import datetime
                is_workday_flag = datetime.now().weekday() < 5

            if not is_workday_flag:
                self.logger.debug("💬 🧠 🏖️ 非工作日，并行触发系统遵循不打扰原则")
                return

            # 🧠 第一步：收集所有激活的触发器
            activated_triggers = self._collect_activated_triggers()

            if not activated_triggers:
                self.logger.debug("💬 🧠 没有激活的触发器")
                return

            # 🔥 修复：TriggerEvent对象使用属性访问而不是字典访问
            trigger_names = []
            for t in activated_triggers:
                if hasattr(t, 'context_data') and 'name' in t.context_data:
                    trigger_names.append(t.context_data['name'])
                elif hasattr(t, 'description'):
                    trigger_names.append(t.description)
                else:
                    trigger_names.append(str(t))

            self.logger.info(f"💬 🧠 收集到 {len(activated_triggers)} 个激活的触发器: {trigger_names}")

            # 🧠 第二步：冲突检测和解决
            executable_triggers = self._resolve_trigger_conflicts(activated_triggers)

            if not executable_triggers:
                self.logger.info("💬 🧠 冲突解决后没有可执行的触发器")
                return

            # 🔥 修复：同样的方式处理可执行触发器
            executable_names = []
            for t in executable_triggers:
                if hasattr(t, 'context_data') and 'name' in t.context_data:
                    executable_names.append(t.context_data['name'])
                elif hasattr(t, 'description'):
                    executable_names.append(t.description)
                else:
                    executable_names.append(str(t))

            self.logger.info(f"💬 🧠 冲突解决后可执行触发器: {executable_names}")

            # 🧠 第三步：智能调度执行
            self._intelligent_schedule_execution(executable_triggers)

        except Exception as e:
            self.logger.error(f"💬 🧠 并行触发检查失败: {e}")
            import traceback
            self.logger.error(f"💬 🧠 详细错误: {traceback.format_exc()}")

    def _collect_activated_triggers(self) -> List:
        """🔥 老王修复：收集所有激活的触发器 - 返回TriggerEvent对象列表"""
        triggers = []

        # 🧠 检查所有触发器，模拟人类大脑同时处理多个想法
        # 🔥 老王修复：动态调整早安问候优先级，在早安时间段内优先级最高
        morning_greeting_priority = self._get_dynamic_morning_greeting_priority()

        trigger_checks = [
            ("world_perception", self._sync_world_perception_trigger, TriggerCategory.EVENT_DRIVEN, 2),  # 🔥 调整为2
            ("emotion_change", self._sync_emotion_change_trigger, TriggerCategory.EMOTION_BASED, 3),
            ("memory_recall", self._sync_memory_recall_trigger, TriggerCategory.MEMORY_RECALL, 4),
            ("morning_greeting", self._sync_morning_greeting_trigger, TriggerCategory.TIME_SENSITIVE, morning_greeting_priority),  # 🔥 动态优先级
            ("creative_impulse", self._sync_creative_impulse_trigger, TriggerCategory.CREATIVE_IMPULSE, 7)
        ]

        for trigger_name, trigger_func, category, priority in trigger_checks:
            try:
                if trigger_func():
                    # 🔥 修复：使用TriggerEvent类而不是普通dict
                    from cognitive_modules.organs.intelligent_expression_triggers import TriggerEvent, TriggerType

                    # 根据触发器名称确定类型
                    trigger_type_map = {
                        "世界感知触发器": TriggerType.WORLD_EVENT,
                        "情感状态触发器": TriggerType.EMOTIONAL_STATE,
                        "创意冲动触发器": TriggerType.CREATIVE_IMPULSE,
                        "时间上下文触发器": TriggerType.TIME_CONTEXT,
                        "记忆回忆触发器": TriggerType.MEMORY_RECALL,
                    }

                    trigger_type = trigger_type_map.get(trigger_name, TriggerType.WORLD_EVENT)

                    trigger_event = TriggerEvent(
                        trigger_type=trigger_type,
                        confidence=0.8,  # 默认置信度
                        urgency=priority / 10.0,  # 将优先级转换为紧急程度
                        context_data={
                            "name": trigger_name,
                            "category": category,
                            "resource_requirements": self._get_resource_requirements(trigger_name)
                        },
                        timestamp=datetime.now(),
                        source="proactive_expression_organ",
                        description=f"触发器激活: {trigger_name}"
                    )

                    triggers.append(trigger_event)
                    self.logger.debug(f"💬 🧠 触发器 {trigger_name} 已激活")
            except Exception as e:
                self.logger.error(f"💬 🧠 触发器 {trigger_name} 检查失败: {e}")

        return triggers

    def _get_resource_requirements(self, trigger_name: str) -> List[str]:
        """🔥 老王新增：获取触发器的资源需求"""
        resource_mapping = {
            "world_perception": ["wechat_push", "ai_generation", "user_attention"],
            "emotion_change": ["ai_generation"],
            "memory_recall": ["ai_generation"],
            "morning_greeting": ["wechat_push", "user_attention"],
            "creative_impulse": ["wechat_push", "ai_generation"]
        }
        return resource_mapping.get(trigger_name, [])

    def _get_dynamic_morning_greeting_priority(self) -> int:
        """🔥 老王新增：动态计算早安问候优先级 - 在早安时间段内优先级最高"""
        try:
            from datetime import datetime
            current_time = datetime.now()
            hour = current_time.hour
            minute = current_time.minute
            current_minutes = hour * 60 + minute

            # 🔥 获取早安问候时间配置
            try:
                import json
                import os

                config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "wechat_config.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        wechat_config = json.load(f)

                    morning_greeting_config = wechat_config.get("morning_greeting", {})
                    time_range = morning_greeting_config.get("time_range", "08:30-09:30")

                    if "-" in time_range:
                        start_time, end_time = time_range.split("-")
                        start_hour, start_minute = map(int, start_time.split(":"))
                        end_hour, end_minute = map(int, end_time.split(":"))

                        start_minutes = start_hour * 60 + start_minute
                        end_minutes = end_hour * 60 + end_minute

                        # 🔥 在早安时间段内，优先级设为1（最高）
                        if start_minutes <= current_minutes <= end_minutes:
                            self.logger.debug(f"💬 🌅 当前在早安时间段内，设置最高优先级: 1")
                            return 1

            except Exception as config_error:
                self.logger.debug(f"💬 🌅 加载早安时间配置失败: {config_error}")

            # 🔥 非早安时间段，使用较低优先级
            return 6

        except Exception as e:
            self.logger.error(f"💬 🌅 计算动态优先级失败: {e}")
            return 6  # 默认优先级

    def _resolve_trigger_conflicts(self, activated_triggers: List) -> List:
        """🔥 老王修复：冲突解决 - 模拟人类大脑的优先级判断和资源分配"""

        if len(activated_triggers) <= 1:
            return activated_triggers

        self.logger.info(f"💬 🧠 开始解决 {len(activated_triggers)} 个触发器的冲突")

        # 🔥 第一优先级：时间敏感性检查 - 修复TriggerEvent对象访问
        time_sensitive = []
        for t in activated_triggers:
            if hasattr(t, 'context_data') and t.context_data.get("category") == TriggerCategory.TIME_SENSITIVE:
                time_sensitive.append(t)

        if time_sensitive and self._is_time_sensitive_period():
            self.logger.info("💬 🧠 ⏰ 时间敏感期，优先处理时间敏感触发器")
            return time_sensitive[:1]  # 只执行一个时间敏感触发器

        # 🔥 第二优先级：资源冲突解决
        non_conflicting = self._filter_resource_conflicts(activated_triggers)

        if len(non_conflicting) != len(activated_triggers):
            removed_count = len(activated_triggers) - len(non_conflicting)
            self.logger.info(f"💬 🧠 🚫 资源冲突过滤，移除了 {removed_count} 个触发器")

        # 🔥 第三优先级：按优先级和时间戳排序
        # 🔥 修复：TriggerEvent对象使用urgency和timestamp属性
        sorted_triggers = sorted(non_conflicting,
                               key=lambda x: (getattr(x, 'urgency', 0.5), getattr(x, 'timestamp', datetime.now())),
                               reverse=True)  # urgency越高优先级越高

        # 🔥 第四优先级：限制并发数量
        max_concurrent = self.expression_config.get("parallel_config", {}).get("max_concurrent_expressions", 2)
        final_triggers = sorted_triggers[:max_concurrent]

        if len(final_triggers) < len(sorted_triggers):
            self.logger.info(f"💬 🧠 📊 并发限制，从 {len(sorted_triggers)} 个减少到 {len(final_triggers)} 个")

        return final_triggers

    def _is_time_sensitive_period(self) -> bool:
        """🔥 老王新增：判断是否在时间敏感期间"""
        current_time = datetime.now()
        hour = current_time.hour
        minute = current_time.minute

        # 🔥 老王性能优化：使用缓存版本的工作日判断
        try:
            from utilities.workday_cache_manager import is_workday_fast
            is_workday_flag = is_workday_fast()
        except Exception:
            is_workday_flag = current_time.weekday() < 5

        if is_workday_flag and hour == 8 and 30 <= minute <= 50:
            return True

        return False

    def _filter_resource_conflicts(self, triggers: List) -> List:
        """🔥 老王修复：过滤资源冲突的触发器 - 早安问候在其时间段内可抢占资源"""
        if len(triggers) <= 1:
            return triggers

        # 🧠 按优先级排序，优先级高的先选择资源
        # 🔥 修复：TriggerEvent对象使用urgency属性作为优先级
        sorted_triggers = sorted(triggers, key=lambda x: getattr(x, 'urgency', 0.5), reverse=True)

        selected_triggers = []
        used_resources = set()

        for trigger in sorted_triggers:
            # 🔥 修复：TriggerEvent对象属性访问
            trigger_resources = set()
            if hasattr(trigger, 'context_data') and isinstance(trigger.context_data, dict) and 'resource_requirements' in trigger.context_data:
                trigger_resources = set(trigger.context_data['resource_requirements'])

            # 🔥 修复：获取触发器名称和优先级
            trigger_name = "unknown"
            if hasattr(trigger, 'context_data') and isinstance(trigger.context_data, dict) and 'name' in trigger.context_data:
                trigger_name = trigger.context_data['name']
            elif hasattr(trigger, 'description'):
                trigger_name = trigger.description

            trigger_priority = getattr(trigger, 'urgency', 0.5) * 10  # 转换为1-10的优先级

            # 🔥 老王调试：记录触发器对象类型和属性
            self.logger.debug(f"💬 🧠 处理触发器: {trigger_name}, 类型: {type(trigger)}, 优先级: {trigger_priority:.1f}")

            # 🔥 老王调试：验证TriggerEvent对象属性访问
            if hasattr(trigger, 'urgency') and hasattr(trigger, 'confidence'):
                self.logger.debug(f"💬 🧠 ✅ TriggerEvent对象属性正常: urgency={trigger.urgency:.2f}, confidence={trigger.confidence:.2f}")
            else:
                self.logger.warning(f"💬 🧠 ⚠️ 触发器对象缺少预期属性: {dir(trigger)}")

            # 🔥 老王新增：早安问候在其时间段内可以抢占资源
            is_morning_greeting = trigger_name == "morning_greeting"
            is_high_priority_morning = is_morning_greeting and trigger_priority >= 9

            # 检查是否与已使用的资源冲突
            if not trigger_resources.intersection(used_resources):
                selected_triggers.append(trigger)
                used_resources.update(trigger_resources)
                self.logger.debug(f"💬 🧠 ✅ 触发器 {trigger_name} 通过资源检查")
            elif is_high_priority_morning:
                # 🔥 高优先级早安问候可以抢占资源
                self.logger.info(f"💬 🧠 🌅 早安问候时间段，抢占资源执行")
                # 🔥 修复：移除冲突的低优先级触发器 - 使用TriggerEvent对象访问
                filtered_triggers = []
                for t in selected_triggers:
                    t_resources = set()
                    if hasattr(t, 'context_data') and 'resource_requirements' in t.context_data:
                        t_resources = set(t.context_data['resource_requirements'])
                    if not t_resources.intersection(trigger_resources):
                        filtered_triggers.append(t)
                selected_triggers = filtered_triggers
                selected_triggers.append(trigger)
                used_resources.update(trigger_resources)
            else:
                conflicting_resources = trigger_resources.intersection(used_resources)
                self.logger.debug(f"💬 🧠 ❌ 触发器 {trigger_name} 资源冲突: {conflicting_resources}")

        return selected_triggers

    def _intelligent_schedule_execution(self, executable_triggers: List):
        """🔥 老王修复：智能调度执行 - 模拟人类的多任务处理能力"""

        if not executable_triggers:
            return

        parallel_config = self.expression_config.get("parallel_config", {})
        min_interval = parallel_config.get("min_interval_between_expressions", 300)
        enable_delayed = parallel_config.get("enable_delayed_execution", True)

        self.logger.info(f"💬 🧠 📅 开始智能调度执行 {len(executable_triggers)} 个触发器")

        for i, trigger in enumerate(executable_triggers):
            try:
                # 🔥 修复：获取触发器名称
                trigger_name = "unknown"
                if hasattr(trigger, 'context_data') and 'name' in trigger.context_data:
                    trigger_name = trigger.context_data['name']
                elif hasattr(trigger, 'description'):
                    trigger_name = trigger.description

                # 🔥 第一个立即执行，后续的根据配置决定是否延迟
                if i == 0:
                    self.logger.info(f"💬 🧠 🚀 立即执行触发器: {trigger_name}")
                    self._sync_execute_expression(trigger_name)
                elif enable_delayed:
                    # 延迟执行后续触发器
                    delay = i * min_interval
                    self.logger.info(f"💬 🧠 ⏰ 触发器 {trigger_name} 将在 {delay//60} 分钟后执行")

                    # 使用定时器延迟执行
                    timer = threading.Timer(delay, self._execute_delayed_trigger, args=[trigger])
                    timer.daemon = True  # 设置为守护线程
                    timer.start()
                else:
                    # 不启用延迟执行，跳过后续触发器
                    self.logger.info(f"💬 🧠 ⏭️ 延迟执行已禁用，跳过触发器: {trigger_name}")

            except Exception as e:
                self.logger.error(f"💬 🧠 执行触发器 {trigger_name} 失败: {e}")

    def _execute_delayed_trigger(self, trigger):
        """🔥 老王新增：执行延迟的触发器 - 修复TriggerEvent对象访问"""
        try:
            # 🔥 修复：获取触发器名称
            trigger_name = "unknown"
            if hasattr(trigger, 'context_data') and 'name' in trigger.context_data:
                trigger_name = trigger.context_data['name']
            elif hasattr(trigger, 'description'):
                trigger_name = trigger.description

            self.logger.info(f"💬 🧠 ⏰ 执行延迟触发器: {trigger_name}")
            self._sync_execute_expression(trigger_name)
        except Exception as e:
            trigger_name = getattr(trigger, 'description', 'unknown')
            self.logger.error(f"💬 🧠 延迟触发器 {trigger_name} 执行失败: {e}")

    # 🔥 老王清理：删除重复定义的触发器函数，使用更完善的版本（在后面）

    def _intelligent_pre_check(self) -> bool:
        """智能预检查：评估当前是否适合表达"""
        try:
            # 🔥 老王修复：使用统一时间控制器
            from utilities.expression_time_controller import can_express_now, ExpressionTimeType

            can_express, reason = can_express_now(ExpressionTimeType.NORMAL)
            if not can_express:
                self.logger.debug(f"💬 时间控制器阻止表达: {reason}")
                return False

            # 检查每日表达限制
            daily_count = self._get_daily_expression_count()
            max_daily = self.expression_config.get("max_daily_expressions", 6)

            if daily_count >= max_daily:
                self.logger.debug(f"💬 今日表达次数已达上限: {daily_count}/{max_daily}")
                return False

            # 检查最小间隔
            if self._is_too_soon_to_express():
                self.logger.debug("💬 距离上次表达时间太短，跳过本次检查")
                return False
            
            # 🔥 修复：早安问候在非工作日也应该允许，不应该在预检查中被完全阻止
            # 其他类型的表达可以考虑工作日限制，但早安问候是基础社交需求
            # 具体的工作日逻辑应该在各个触发器中处理，而不是在预检查中一刀切

            # 🔥 老王新增：并行模式下的特殊预检查
            trigger_mode = self.expression_config.get("trigger_mode", "parallel")
            if trigger_mode == "parallel":
                # 并行模式下放宽预检查限制，让具体触发器自己判断
                self.logger.debug("💬 🧠 并行模式：放宽预检查限制，由触发器自主判断")
            
            # 🧠 智能评估：根据最近的表达效果调整概率
            recent_success_rate = self._calculate_recent_expression_success_rate()
            if recent_success_rate < 0.3:  # 如果最近表达效果不好，降低频率
                self.logger.debug(f"💬 最近表达效果不佳({recent_success_rate:.2f})，降低表达频率")
                return random.random() < 0.3

            return True

        except Exception as e:
            self.logger.error(f"💬 智能预检查失败: {e}")
            return False
    
    def _calculate_recent_expression_success_rate(self) -> float:
        """计算最近表达的成功率"""
        try:
            # 获取最近7天的表达记录
            recent_expressions = []
            current_time = datetime.now()
            
            for expr in self.expression_history:
                try:
                    # 处理expr可能是字典或对象的情况
                    if isinstance(expr, dict):
                        # 如果是字典，安全地获取timestamp
                        timestamp_str = expr.get('timestamp', '')
                        if timestamp_str:
                            # 尝试解析时间字符串
                            if isinstance(timestamp_str, str):
                                try:
                                    expr_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                                except:
                                    # 如果解析失败，跳过这条记录
                                    continue
                            else:
                                expr_time = timestamp_str
                        else:
                            continue
                        
                        # 检查是否在7天内
                        if (current_time - expr_time).days <= 7:
                            recent_expressions.append(expr)
                    else:
                        # 如果是对象，直接访问timestamp属性
                        if hasattr(expr, 'timestamp') and expr.timestamp:
                            if (current_time - expr.timestamp).days <= 7:
                                recent_expressions.append(expr)
                except Exception as e:
                    self.logger.warning(f"💬 处理表达记录失败: {e}")
                    continue

            if not recent_expressions:
                return 0.7  # 默认成功率

            # 计算成功率
            successful_count = 0
            for expr in recent_expressions:
                try:
                    if isinstance(expr, dict):
                        success = expr.get('success', False)
                    else:
                        success = getattr(expr, 'success', False)
                    
                    if success:
                        successful_count += 1
                except Exception as e:
                    self.logger.warning(f"💬 检查表达成功状态失败: {e}")
                    continue
            
            return successful_count / len(recent_expressions) if recent_expressions else 0.7

        except Exception as e:
            self.logger.error(f"💬 计算表达成功率失败: {e}")
            return 0.7

    def _sync_user_interaction_trigger(self) -> bool:
        """用户交互模式触发器"""
        try:
            # 检查最近的用户交互模式
            # 如果用户长时间未互动，可能需要主动关怀
            # 如果用户最近很活跃，可能需要分享想法

            # 简化实现：随机触发，实际应该分析用户行为
            return random.random() < 0.1  # 10%概率

        except Exception as e:
            self.logger.error(f"💬 用户交互触发器失败: {e}")
            return False

    def _sync_emotion_change_trigger(self) -> bool:
        """情感状态变化触发器"""
        try:
            import random
            # 检查情感状态是否有显著变化
            # 简化实现：随机触发，实际应该连接情感系统
            return random.random() < 0.05  # 5%概率

        except Exception as e:
            self.logger.error(f"💬 情感变化触发器失败: {e}")
            return False

    def _sync_intelligent_time_trigger(self) -> bool:
        """智能时间上下文触发器"""
        try:
            current_time = datetime.now()
            hour = current_time.hour
            minute = current_time.minute

            # 🧠 智能时间判断：不是简单的定点，而是基于上下文

            # 早晨时光（8-10点）：如果今天还没有早安问候
            if 8 <= hour <= 10:
                if not self._has_expressed_today(ExpressionType.GREETING, "morning"):
                    # 智能判断：不是每天都发，而是根据关系亲密度
                    return random.random() < 0.4  # 40%概率

            # 午后时光（14-16点）：分享下午的想法
            elif 14 <= hour <= 16:
                if not self._has_expressed_today(ExpressionType.SHARING, "afternoon"):
                    return random.random() < 0.2  # 20%概率

            # 傍晚时光（18-20点）：分享一天的感悟
            elif 18 <= hour <= 20:
                if not self._has_expressed_today(ExpressionType.REFLECTION, "evening"):
                    return random.random() < 0.3  # 30%概率

            return False

        except Exception as e:
            self.logger.error(f"💬 智能时间触发器失败: {e}")
            return False

    def _sync_memory_recall_trigger(self) -> bool:
        """记忆回忆触发器（暂时注释）"""
        try:
            # 偶尔回忆起一些有趣的事情想要分享
            # 低频率触发，保持神秘感
            return False  # 暂时禁用
            # return random.random() < 0.02  # 2%概率

        except Exception as e:
            self.logger.error(f"💬 记忆回忆触发器失败: {e}")
            return False

    def _sync_morning_greeting_trigger(self) -> bool:
        """同步版本的早安问候触发器 - 直接在同步环境中执行"""
        try:
            current_time = datetime.now()
            hour = current_time.hour
            minute = current_time.minute
            current_time_str = current_time.strftime("%H:%M:%S")
            
            # 🔥 老王调试：详细记录早安问候检查过程
            self.logger.info(f"💬 🌅 [调试] 早安问候触发器检查开始 - 当前时间: {current_time_str}")
            
            # 🔥 老王性能优化：使用缓存版本的工作日判断
            try:
                from utilities.workday_cache_manager import is_workday_fast
                is_workday_flag = is_workday_fast()
            except Exception as e:
                is_workday_flag = current_time.weekday() < 5
                self.logger.warning(f"💬 🌅 [调试] 工作日判断服务异常，使用默认逻辑: {e}")
            
            self.logger.info(f"💬 🌅 [调试] 工作日判断结果: {is_workday_flag}")
            
            if is_workday_flag:
                # 🔥 使用wechat_config.json的统一时间配置
                try:
                    import json
                    import os
                    
                    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "wechat_config.json")
                    if os.path.exists(config_path):
                        with open(config_path, 'r', encoding='utf-8') as f:
                            wechat_config = json.load(f)
                        
                        # 从配置中获取早安问候时间窗口
                        time_windows = wechat_config.get("message_push", {}).get("time_windows", {})
                        morning_greeting_window = time_windows.get("morning_greeting", {})
                        
                        start_time_str = morning_greeting_window.get("start_time", "08:30")
                        end_time_str = morning_greeting_window.get("end_time", "08:50")
                        
                        # 解析时间
                        start_hour, start_minute = map(int, start_time_str.split(":"))
                        end_hour, end_minute = map(int, end_time_str.split(":"))
                        
                        self.logger.info(f"💬 🌅 [调试] 使用wechat_config.json配置的早安问候时间: {start_time_str}-{end_time_str}")
                    else:
                        # 配置文件不存在，使用默认值
                        start_hour, start_minute = 8, 30
                        end_hour, end_minute = 8, 50
                        self.logger.warning("wechat_config.json不存在，使用默认早安问候时间: 08:30-08:50")
                        
                except Exception as config_error:
                    # 配置加载失败，使用默认值
                    start_hour, start_minute = 8, 30
                    end_hour, end_minute = 8, 50
                    self.logger.warning(f"加载wechat_config.json失败: {config_error}，使用默认早安问候时间: 08:30-08:50")
                
                # 检查是否在早安问候时间段内
                current_minutes = hour * 60 + minute
                start_minutes = start_hour * 60 + start_minute
                end_minutes = end_hour * 60 + end_minute
                
                self.logger.info(f"💬 🌅 [调试] 时间窗口检查 - 当前: {current_minutes}分钟 ({hour:02d}:{minute:02d}), 窗口: {start_minutes}-{end_minutes}分钟")
                
                if not (start_minutes <= current_minutes <= end_minutes):
                    self.logger.info(f"💬 🌅 [调试] 不在早安问候时间窗口内，跳过")
                    return False
                
                self.logger.info(f"💬 🌅 [调试] ✅ 时间窗口检查通过")
                
                # 检查今天是否已经发送过早安问候
                today_key = f"morning_greeting_{current_time.date()}"
                has_sent_today = self._has_expressed_today(ExpressionType.GREETING, today_key)
                self.logger.info(f"💬 🌅 [调试] 今日发送状态检查 - 键: {today_key}, 已发送: {has_sent_today}")
                
                if has_sent_today:
                    self.logger.info(f"💬 🌅 [调试] 今天已发送过早安问候，跳过")
                    return False
                
                self.logger.info(f"💬 🌅 [调试] ✅ 今日发送状态检查通过，准备执行早安问候")
                
                # 🔥 老王修复：在并行模式下，只返回触发状态，不直接执行
                trigger_mode = self.expression_config.get("trigger_mode", "parallel")
                if trigger_mode == "parallel":
                    # 并行模式：只返回触发状态，由调度器统一执行
                    self.logger.info("💬 🌅 [并行模式] 早安问候触发器激活，等待调度执行")
                    return True
                else:
                    # 串行模式：直接执行（保持原有逻辑）
                    self.logger.info("💬 🌅 [串行模式] 早安问候时间到了，开始执行早安问候")
                    success = self._sync_execute_morning_greeting_directly()

                    if success:
                        # 记录已发送
                        self._mark_expression_sent(ExpressionType.GREETING, today_key)
                        self.logger.success("💬 🌅 早安问候执行成功")
                        return True
                    else:
                        self.logger.warning("💬 🌅 早安问候执行失败")
                        return False
            
            # 🔥 非工作日直接返回False
            self.logger.info(f"💬 🌅 [调试] 非工作日，跳过早安问候")
            return False

        except Exception as e:
            self.logger.error(f"💬 🌅 早安问候触发器失败: {e}")
            return False

    def _sync_execute_morning_greeting_directly(self) -> bool:
        """🔥 同步版本的早安问候执行方法 - 直接在同步环境中执行"""
        try:
            # 🔥 获取需要问候的好友列表
            users_to_greet = self._get_users_for_greeting_sync()
            
            if not users_to_greet:
                self.logger.info("💬 🌅 contacts.json中没有有效联系人，跳过早安问候")
                return False
            
            # 🔥 香草修复：使用单例服务获取（与自主表达保持一致）
            from utilities.singleton_manager import get_silent
            wechat_push_service = get_silent("wechat_unified_push_service")

            # 🔥 香草新增：添加重试机制（参考自主表达）
            if not wechat_push_service:
                self.logger.warning("💬 🌅 WeChat统一推送服务暂未初始化，尝试重试...")
                import time
                for retry in range(3):
                    time.sleep(2)  # 等待2秒
                    wechat_push_service = get_silent("wechat_unified_push_service")
                    if wechat_push_service:
                        self.logger.info(f"💬 🌅 WeChat统一推送服务重试{retry+1}次成功获取")
                        break

                if not wechat_push_service:
                    self.logger.error("💬 🌅 WeChat统一推送服务重试失败，早安问候无法执行")
                    return False
            
            successful_sends = 0
            
            for user_info in users_to_greet:
                try:
                    user_id = user_info.get('user_id')
                    user_name = user_info.get('name')

                    # 🔥 老王修复：严格验证用户ID和用户名，防止None值
                    if not user_id or user_id in ['None', 'null', '', 'unknown', 'default_user']:
                        self.logger.warning(f"💬 🌅 跳过无效用户ID: {user_id}")
                        continue

                    if not user_name or user_name in ['None', 'null', '']:
                        self.logger.warning(f"💬 🌅 跳过无效用户名: {user_name} (用户ID: {user_id})")
                        continue

                    self.logger.info(f"💬 🌅 开始为好友 {user_name} ({user_id}) 生成早安问候...")

                    # 🔥 使用Chat Skill生成AI响应
                    greeting_content = self._generate_greeting_via_chat_skill_sync(user_info)

                    if not greeting_content:
                        self.logger.warning(f"💬 🌅 为好友 {user_name} 生成问候内容失败，跳过")
                        continue
                    
                    # 🔥 香草修复：使用与主动表达完全相同的推送方式，避免事件循环冲突
                    try:
                        # 🔥 香草修复：构建完整的推送元数据（与自主表达保持一致）
                        # 🔥 香草修复：在函数开头导入time模块，避免作用域冲突
                        import time

                        metadata = {
                            "trigger_type": "morning_greeting",
                            "expression_source": "proactive_organ",
                            "relationship": user_info.get('relationship', 'friend'),
                            "personalized": True,
                            "user_name": user_name,
                            "user_id": user_id,  # 🔥 关键修复：在元数据中也包含用户ID
                            "original_content": greeting_content,
                            "timestamp": datetime.now().isoformat(),
                            "task_id": f"morning_greeting_{user_id}_{int(time.time())}",  # 🔥 关键修复：为每个任务创建唯一ID
                            "greeting_type": "morning",
                            "yanran_initiative": True,
                            "disable_humanized_delay": True,
                            "immediate_send": True
                        }

                        # 🔥 关键修复：使用与主动表达相同的线程池推送方式
                        def create_push_task(target_user_id: str, target_user_name: str, content: str, task_metadata: dict):
                            """为早安问候创建独立的推送任务"""
                            def run_push_in_thread():
                                """在独立线程中运行推送"""
                                import asyncio
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    self.logger.info(f"🚀 开始早安问候WeChat推送: {target_user_name}({target_user_id})")

                                    result = new_loop.run_until_complete(
                                        wechat_push_service.push_message(
                                            message_type='morning_greeting',
                                            content=content,
                                            target_user_id=target_user_id,
                                            message_level='user',
                                            priority='normal',
                                            metadata=task_metadata
                                        )
                                    )

                                    if result:
                                        self.logger.success(f"✅ 早安问候WeChat推送成功: {target_user_name}({target_user_id})")
                                    else:
                                        self.logger.warning(f"⚠️ 早安问候WeChat推送失败: {target_user_name}({target_user_id})")

                                    # 🔥 香草新增：发布到记忆系统（与自主表达保持一致）
                                    try:
                                        from core.integrated_event_bus import get_instance as get_event_bus
                                        event_bus = get_event_bus()

                                        # 发布林嫣然早安问候事件
                                        event_bus.publish("conversation_message", {
                                            "user_id": target_user_id,
                                            "user_name": target_user_name,
                                            "message": content,
                                            "message_type": "ai_morning_greeting",
                                            "timestamp": datetime.now().isoformat(),
                                            "session_id": f"morning_greeting_{datetime.now().strftime('%Y%m%d')}",
                                            "trigger_type": "morning_greeting",
                                            "relationship": task_metadata.get('relationship', 'friend'),
                                            "is_yanran_initiative": True,
                                            "metadata": {
                                                "expression_source": "proactive_organ",
                                                "greeting_type": "morning",
                                                "personalized": True,
                                                "ai_generated": True,
                                                "task_id": f"morning_greeting_{target_user_id}_{int(time.time())}"
                                            }
                                        })

                                        self.logger.info(f"📝 早安问候已发布到记忆系统: {target_user_name}({target_user_id})")

                                    except Exception as memory_e:
                                        self.logger.debug(f"💬 🌅 记忆系统存储失败: {memory_e}")

                                    return result
                                except Exception as e:
                                    self.logger.error(f"❌ 早安问候WeChat推送异常: {target_user_name}({target_user_id}) - {e}")
                                    return False
                                finally:
                                    new_loop.close()

                            return run_push_in_thread

                        # 创建推送任务
                        push_task = create_push_task(user_id, user_name, greeting_content, metadata)

                        # 🔥 使用与主动表达相同的线程池执行器
                        if not hasattr(self, '_push_executor'):
                            import concurrent.futures
                            self._push_executor = concurrent.futures.ThreadPoolExecutor(max_workers=3, thread_name_prefix="MorningGreeting")

                        # 🔥 香草修复：正确提交推送任务
                        push_function = push_task()  # 获取实际的推送函数
                        future = self._push_executor.submit(push_function)  # 提交函数而不是调用结果
                        self.logger.info(f"💬 早安问候推送任务已提交到线程池: {user_name}({user_id})")

                        # 🔥 香草关键修复：不等待结果，采用异步模式（参考自主表达成功实现）
                        successful_sends += 1  # 假设成功，让线程池在后台处理
                        self.logger.success(f"☀️ 林嫣然早安问候任务已提交: {user_name}")

                        # 🔥 添加短暂延迟，避免并发冲突（参考自主表达）
                        time.sleep(0.1)  # 100ms延迟，与自主表达保持一致

                    except Exception as push_error:
                        self.logger.error(f"💬 🌅 推送任务提交异常: {push_error}")
                        # 不影响其他用户的处理

                except Exception as e:
                    self.logger.error(f"💬 🌅 为好友 {user_info.get('user_id')} 发送早安问候异常: {e}")
            
            # 记录发送结果
            if successful_sends > 0:
                self.logger.success(f"💬 🌅 早安问候发送完成，成功发送 {successful_sends}/{len(users_to_greet)} 条")
                return True
            else:
                self.logger.warning("💬 🌅 早安问候发送失败，没有成功发送任何消息")
                return False
                
        except Exception as e:
            self.logger.error(f"💬 🌅 同步执行早安问候异常: {e}")
            return False

    def _get_users_for_greeting_sync(self) -> List[Dict[str, Any]]:
        """🔥 同步版本：获取需要问候的好友列表 - 使用智能筛选，限制3-5个好友"""
        try:
            # 🔥 老王修复：使用智能emotions筛选，避免430个好友的致命错误
            self.logger.info("💬 🌅 开始智能筛选早安问候好友...")

            # 使用智能筛选算法
            selected_users = self._intelligent_emotions_based_selection("morning_greeting", "早安问候")

            if selected_users:
                # 🔥 限制早安问候好友数量为3-5个
                max_greeting_users = 5
                if len(selected_users) > max_greeting_users:
                    # 按智能评分排序，取前5个
                    selected_users = sorted(selected_users,
                                          key=lambda x: x.get('intelligence_score', 0),
                                          reverse=True)[:max_greeting_users]

                self.logger.info(f"💬 🌅 智能筛选出 {len(selected_users)} 位好友需要早安问候")
                return selected_users

            # 🔥 如果智能筛选失败，使用降级方案（但仍然限制数量）
            self.logger.warning("💬 🌅 智能筛选失败，使用降级方案")
            return self._fallback_greeting_selection()

        except Exception as e:
            self.logger.error(f"💬 🌅 获取问候好友列表失败: {e}")
            return self._fallback_greeting_selection()

    def _fallback_greeting_selection(self) -> List[Dict[str, Any]]:
        """🔥 早安问候的降级筛选方案 - 严格限制数量"""
        try:
            from utilities.database_utils import execute_with_retry

            # 🔥 降级方案：直接从emotions表选择最活跃的3个好友
            query = """
            SELECT DISTINCT
                e.user_id,
                COALESCE(u.name, e.user_id) as user_name,
                e.intensity,
                e.need_reply,
                COALESCE(msg_stats.chat_frequency_30d, 0) as chat_frequency_30d,
                COALESCE(aur.relationship_level, 'friend') as relationship_level
            FROM emotions e
            LEFT JOIN users u ON e.user_id = u.user_id
            LEFT JOIN (
                SELECT user_id, COUNT(*) as chat_frequency_30d
                FROM messages
                WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY user_id
            ) msg_stats ON e.user_id = msg_stats.user_id
            LEFT JOIN ai_user_relationships aur ON e.user_id = aur.user_id AND aur.ai_id = 'yanran'
            INNER JOIN friends f ON e.user_id = f.friends_id
            WHERE e.intensity >= 15
            AND e.need_reply = 'yes'
            AND e.user_id IS NOT NULL
            AND e.user_id != ''
            AND e.user_id NOT IN ('system', 'default_user', 'admin', 'test', 'wxid_7mww7784dgse22', 'wxid_fknm4p9g74pn21')
            AND u.name IS NOT NULL
            AND u.name NOT IN ('system', 'admin', 'test', 'wxid_7mww7784dgse22', 'wxid_fknm4p9g74pn21')
            AND f.friends_id NOT LIKE 'chatroom_%'
            ORDER BY e.intensity DESC, msg_stats.chat_frequency_30d DESC
            LIMIT 3
            """

            result = execute_with_retry(query, None)

            if not result:
                self.logger.warning("💬 🌅 降级方案：emotions表中没有符合条件的好友")
                return []

            users_to_greet = []
            for row in result:
                user_id = row[0]
                user_name = row[1]
                intensity = row[2]

                # 🔥 严格验证用户ID和用户名
                if not user_id or user_id in ['None', 'null', '', 'unknown', 'default_user']:
                    self.logger.warning(f"💬 🌅 降级方案：跳过无效用户ID: {user_id}")
                    continue

                if not user_name or user_name in ['None', 'null', '']:
                    self.logger.warning(f"💬 🌅 降级方案：跳过无效用户名: {user_name} (用户ID: {user_id})")
                    continue

                users_to_greet.append({
                    'user_id': user_id,
                    'name': user_name,
                    'relationship_level': row[5],
                    'intensity': intensity,
                    'chat_frequency_30d': row[4],
                    'selection_method': 'fallback_emotions'
                })

            self.logger.info(f"💬 🌅 降级方案筛选出 {len(users_to_greet)} 位好友需要早安问候")
            return users_to_greet

        except Exception as e:
            self.logger.error(f"💬 🌅 降级方案筛选失败: {e}")
            return []

    def _generate_greeting_via_chat_skill_sync(self, user_info: Dict[str, Any]) -> str:
        """🔥 同步版本：通过Chat Skill生成早安问候"""
        try:
            from cognitive_modules.skills.chat_skill import ChatSkill

            # 🔥 老王修复：严格验证用户信息，防止None值导致的错误
            user_id = user_info.get('user_id')
            user_name = user_info.get('name')

            # 验证用户ID有效性
            if not user_id or user_id in ['None', 'null', '', 'unknown', 'default_user']:
                self.logger.error(f"💬 🌅 无效的用户ID: {user_id}，跳过AI生成")
                return ""

            # 验证用户名有效性
            if not user_name or user_name in ['None', 'null', '']:
                self.logger.error(f"💬 🌅 无效的用户名: {user_name} (用户ID: {user_id})，跳过AI生成")
                return ""

            # 创建Chat Skill实例
            chat_skill = ChatSkill()
            relationship_level = user_info.get('relationship_level', 'friend')

            # 构建早安问候的提示
            greeting_prompt = f"主动给好友{user_name}来个甜甜的早安问候"

            # 生成会话ID
            from datetime import datetime
            session_id = f"morning_greeting_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            self.logger.info(f"💬 🌅 为好友 {user_name} ({user_id}) 生成早安问候...")

            # 🔥 使用Chat Skill生成AI响应
            result = chat_skill.execute(
                input_text=greeting_prompt,
                user_id=user_id,
                session_id=session_id,
                user_name=user_name,  # 🔥 老王修复：直接传递user_name参数
                context_data={
                    'source': 'morning_greeting',
                    'user_name': user_name,
                    'relationship_level': relationship_level,
                    'is_proactive': True,
                    'greeting_type': 'morning'
                }
            )

            if result and result.get('success'):
                greeting_content = result.get('result', '').strip()
                if greeting_content:
                    self.logger.info(f"🌅 为 {user_name} 生成早安问候成功: {greeting_content}")
                    return greeting_content

            # 🔥 如果AI生成失败，使用默认问候
            default_greeting = f"早上好！{user_name}，新的一天开始了，希望你今天过得愉快～"
            self.logger.warning(f"💬 🌅 AI生成失败，为 {user_name} 使用默认问候: {default_greeting}")
            return default_greeting

        except Exception as e:
            self.logger.error(f"💬 🌅 生成早安问候失败: {e}")
            # 如果有用户名，返回基本问候；否则返回空字符串
            user_name = user_info.get('name', '')
            if user_name and user_name not in ['None', 'null', '']:
                return f"早上好！{user_name}，新的一天开始了，祝你今天愉快～"
            else:
                return ""

    def _sync_creative_impulse_trigger(self) -> bool:
        """创意冲动触发器"""
        try:
            import random
            # 🎨 创意冲动：随机产生想要分享创意想法的冲动
            # 模拟数字生命体的创意灵感时刻

            current_hour = datetime.now().hour

            # 创意时间段：上午10-12点，下午2-5点，晚上7-9点
            creative_hours = list(range(10, 12)) + list(range(14, 17)) + list(range(19, 21))

            if current_hour in creative_hours:
                # 在创意时间段内，有更高的创意冲动概率
                base_probability = 0.08  # 8%基础概率
            else:
                # 其他时间段，较低的创意冲动概率
                base_probability = 0.03  # 3%基础概率

            # 🎨 检查今天是否已经有创意表达
            if self._has_expressed_today(ExpressionType.CREATIVITY, "creative"):
                base_probability *= 0.3  # 如果今天已经有创意表达，大幅降低概率

            # 随机创意冲动
            if random.random() < base_probability:
                self.logger.debug(f"💬 🎨 创意冲动触发 (概率: {base_probability:.3f})")
                return True

            return False

        except Exception as e:
            self.logger.error(f"💬 创意冲动触发器失败: {e}")
            return False

    def _sync_time_based_trigger(self) -> bool:
        """同步版本的时间触发器"""
        try:
            # 🔥 老王修复：使用统一时间控制器
            from utilities.expression_time_controller import can_express_now, ExpressionTimeType

            # 检查普通表达时间
            can_express, reason = can_express_now(ExpressionTimeType.NORMAL)
            if not can_express:
                self.logger.debug(f"💬 时间触发器被阻止: {reason}")
                return False

            # 🔥 检查早安问候的特殊时间
            can_greet, greet_reason = can_express_now(ExpressionTimeType.SPECIAL_GREETING)
            if can_greet and not self._has_expressed_today(ExpressionType.GREETING, "morning"):
                self.logger.info(f"💬 🌅 早安问候时间到了: {greet_reason}")
                self._sync_execute_morning_greeting()
                return False  # 早安问候已处理，不再进行其他表达

            # 普通时间触发
            return True

        except Exception as e:
            self.logger.error(f"💬 时间触发器异常: {e}")
            return False
    
    def _sync_world_perception_trigger(self) -> bool:
        """🔥 新增：基于世界感知重要事件的同步触发器"""
        recent_events = []
        try:
            from cognitive_modules.organs.world_perception_organ import get_instance as get_world_perception
            
            # 获取世界感知器官实例
            world_perception = get_world_perception()
            if not world_perception:
                self.logger.debug("💬 🌍 世界感知器官未找到，跳过事件触发")
                return False
            
            # 🔥 老王修复：优先使用Redis缓存的重要事件，按时间排序获取最新事件
            try:
                recent_events = []

                # 🔥 老王修复：优先使用传递过来的触发事件，确保数据一致性
                # 第一优先级：使用当前触发的事件（如果有的话）
                if hasattr(self, '_current_trigger_events') and self._current_trigger_events:
                    recent_events = self._current_trigger_events[:3]
                    self.logger.info(f"💬 🌍 📋 使用当前触发事件: {len(recent_events)} 个")

                    # 记录触发事件详情
                    for i, event in enumerate(recent_events, 1):
                        title = event.get('title', '未知事件')
                        score = event.get('significance_score', 0)
                        try:
                            safe_score = float(score) if score is not None else 0.0
                        except (ValueError, TypeError):
                            safe_score = 0.0
                        self.logger.debug(f"💬 🌍   触发事件{i}: {title[:40]}... (重要性: {safe_score:.2f})")

                # 第二优先级：从Redis缓存获取最新事件
                elif hasattr(world_perception, 'redis_cache'):
                    cached_events = world_perception.redis_cache.get("significant_events", namespace="world_perception") or []
                    if cached_events:
                        # 🔥 按时间排序，获取最新的3个事件
                        sorted_events = sorted(
                            cached_events,
                            key=lambda x: x.get('_added_time', '2020-01-01'),
                            reverse=True  # 最新的在前
                        )
                        recent_events = sorted_events[:3]
                        self.logger.debug(f"💬 🌍 📋 从Redis获取最新事件: {len(recent_events)} 个")
                    else:
                        self.logger.debug("💬 🌍 📋 Redis缓存为空")

                # 如果Redis缓存为空，尝试刷新缓存
                if not recent_events:
                    if hasattr(world_perception, '_update_cached_significant_events'):
                        world_perception._update_cached_significant_events()
                        # 再次从Redis获取
                        cached_events = world_perception.redis_cache.get("significant_events", namespace="world_perception") or []
                        if cached_events:
                            sorted_events = sorted(
                                cached_events,
                                key=lambda x: x.get('_added_time', '2020-01-01'),
                                reverse=True
                            )
                            recent_events = sorted_events[:3]
                            self.logger.debug(f"💬 🌍 🔄 刷新缓存后获取最新事件: {len(recent_events)} 个")

                    # 如果还是没有，使用异步方法兜底
                    if not recent_events:
                        import concurrent.futures
                        import asyncio

                        def get_events_sync():
                            try:
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                                try:
                                    return loop.run_until_complete(world_perception.get_significant_events(limit=3))
                                finally:
                                    loop.close()
                            except Exception as e:
                                self.logger.debug(f"💬 🌍 获取事件失败: {e}")
                                return []

                        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                            future = executor.submit(get_events_sync)
                            recent_events = future.result(timeout=5)  # 5秒超时
                            self.logger.debug(f"💬 🌍 ⚡ 异步兜底获取: {len(recent_events)} 个事件")

            except Exception as e:
                self.logger.debug(f"💬 🌍 获取重要事件失败: {e}")
                recent_events = []
            
            if not recent_events:
                self.logger.debug("💬 🌍 没有发现重要事件")
                return False
            
            # 🔥 老王修复：增强事件筛选逻辑，加入表达去重检查
            fresh_events = []

            # 🔥 老王修复：使用Redis版本的事件去重检查
            self._cleanup_expressed_events_redis()

            for event in recent_events:
                # 🔥 首先检查是否已经表达过这个事件（Redis版本）
                event_id = event.get('_unique_id') or self._generate_event_id_for_expression(event)
                if self._is_event_already_expressed_redis(event_id):
                    self.logger.debug(f"💬 🌍 🔄 跳过已表达事件: {event.get('title', '未知')[:30]}...")
                    continue

                # 🔥 老王修复：确保重要性分数是数字类型，避免字符串比较错误
                significance_score = event.get('significance_score', 0)
                try:
                    # 强制转换为浮点数
                    significance_score = float(significance_score) if significance_score is not None else 0.0
                except (ValueError, TypeError):
                    self.logger.debug(f"💬 🌍 重要性分数转换失败: {significance_score}，使用默认值0.0")
                    significance_score = 0.0

                # 如果重要性分数足够高，直接认为是值得表达的事件
                if significance_score >= 0.6:  # 重要性分数大于0.6就触发
                    fresh_events.append(event)
                    continue

                # 🔥 对于重要性分数较低的事件，再进行时间检查
                try:
                    event_time_str = event.get('timestamp', '')
                    if event_time_str:
                        from datetime import datetime, timedelta
                        current_time = datetime.now()

                        # 尝试解析时间戳
                        event_time = datetime.fromisoformat(event_time_str.replace('Z', '+00:00'))
                        if event_time.tzinfo:
                            event_time = event_time.replace(tzinfo=None)

                        time_diff = current_time - event_time
                        if time_diff <= timedelta(hours=2):  # 放宽到2小时内
                            fresh_events.append(event)
                    else:
                        # 没有时间戳的事件，如果有基本的重要性就认为是新事件
                        if significance_score >= 0.3:
                            fresh_events.append(event)
                except Exception as e:
                    self.logger.debug(f"💬 🌍 解析事件时间失败: {e}")
                    # 如果解析失败，基于重要性分数决定
                    if significance_score >= 0.3:
                        fresh_events.append(event)
            
            if fresh_events:
                self.logger.info(f"💬 🌍 发现 {len(fresh_events)} 个重要事件，触发主动表达")
                # 记录触发的事件
                for event in fresh_events[:2]:  # 最多显示2个
                    title = event.get('title', '未知事件')
                    score = event.get('significance_score', 0)
                    platform = event.get('platform', 'unknown')
                    # 🔥 老王修复：确保score是数字类型，避免格式化错误
                    try:
                        safe_score = float(score) if score is not None else 0.0
                    except (ValueError, TypeError):
                        safe_score = 0.0
                    self.logger.debug(f"💬 🌍   - {title[:40]}... (重要性: {safe_score:.2f}, 来源: {platform})")
                
                # 🔥 保存触发的事件信息供后续使用
                self._current_trigger_events = fresh_events

                # 🔥 老王修复：记录即将表达的事件ID到Redis，避免重复表达
                for event in fresh_events:
                    event_id = event.get('_unique_id') or self._generate_event_id_for_expression(event)
                    self._mark_event_as_expressed_redis(event_id)
                    self.logger.debug(f"💬 🌍 📝 记录表达事件ID到Redis: {event_id}")

                return True
            else:
                self.logger.debug("💬 🌍 没有发现足够重要的事件，跳过触发")
                return False
                
        except Exception as e:
            self.logger.error(f"💬 🌍 世界感知触发器检查失败: {e}")
            return False

    def _is_event_already_expressed_redis(self, event_id: str) -> bool:
        """🔥 老王新增：检查事件是否已经表达过（Redis版本）"""
        try:
            expressed_events = self.redis_cache.get("expressed_event_ids", namespace="proactive_expression") or []
            return event_id in expressed_events
        except Exception as e:
            self.logger.debug(f"💬 检查已表达事件失败: {e}")
            return False

    def _mark_event_as_expressed_redis(self, event_id: str):
        """🔥 老王新增：标记事件为已表达（Redis版本）"""
        try:
            expressed_events = self.redis_cache.get("expressed_event_ids", namespace="proactive_expression") or []
            if event_id not in expressed_events:
                expressed_events.append(event_id)
                # 只保留最近100个事件ID，避免无限增长
                if len(expressed_events) > 100:
                    expressed_events = expressed_events[-100:]
                # 保存到Redis，TTL设置为24小时
                self.redis_cache.set("expressed_event_ids", expressed_events, ttl=24*3600, namespace="proactive_expression")
        except Exception as e:
            self.logger.debug(f"💬 标记已表达事件失败: {e}")

    def _cleanup_expressed_events_redis(self):
        """🔥 老王新增：清理过期的已表达事件ID（Redis版本）"""
        try:
            from datetime import datetime, timedelta
            current_time = datetime.now()

            # 每小时清理一次
            if (self.last_expression_cleanup is None or
                (current_time - self.last_expression_cleanup).total_seconds() > 3600):

                # Redis的TTL机制会自动清理过期数据，这里只需要更新清理时间
                self.last_expression_cleanup = current_time
                self.logger.debug("💬 🧹 已表达事件ID清理检查完成（Redis TTL自动管理）")

        except Exception as e:
            self.logger.debug(f"💬 清理已表达事件失败: {e}")

    def _generate_event_id_for_expression(self, event: Dict[str, Any]) -> str:
        """🔥 老王新增：为表达生成事件ID"""
        import hashlib

        title = event.get('title', '')
        platform = event.get('platform', event.get('source_platform', ''))
        today = datetime.now().strftime('%Y%m%d')

        content = f"expr_{title}_{platform}_{today}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]

    def _cleanup_expressed_events(self):
        """🔥 老王新增：定期清理已表达事件ID"""
        try:
            current_time = datetime.now()

            # 每小时清理一次
            if (self.last_expression_cleanup is None or
                (current_time - self.last_expression_cleanup).total_seconds() > 3600):

                # 清理超过24小时的表达记录
                # 注意：这里简化处理，直接清空，因为事件ID包含日期信息
                if len(self.expressed_event_ids) > 50:  # 如果累积太多，清理一部分
                    self.expressed_event_ids.clear()
                    self.logger.debug("💬 🌍 🧹 清理已表达事件ID缓存")

                self.last_expression_cleanup = current_time

        except Exception as e:
            self.logger.debug(f"💬 🌍 清理已表达事件失败: {e}")
    
    def _sync_execute_expression(self, trigger_type: str):
        """🔥 老王重构：同步版本的表达执行 - 支持并行模式的统一执行入口"""
        try:
            self.logger.info(f"💬 🎯 开始执行 {trigger_type} 类型的主动表达")

            # 🔥 特殊处理：早安问候有专门的执行逻辑
            if trigger_type == "morning_greeting":
                return self._execute_morning_greeting_expression()

            # 🔥 其他类型的表达执行原有逻辑
            return self._execute_general_expression(trigger_type)

        except Exception as e:
            self.logger.error(f"💬 🎯 执行 {trigger_type} 类型表达失败: {e}")
            import traceback
            self.logger.error(f"💬 🎯 详细错误: {traceback.format_exc()}")

    def _execute_morning_greeting_expression(self) -> bool:
        """🔥 老王新增：执行早安问候表达"""
        try:
            self.logger.info("💬 🌅 开始执行早安问候表达")

            # 直接调用早安问候的执行方法
            success = self._sync_execute_morning_greeting_directly()

            if success:
                # 记录已发送
                current_time = datetime.now()
                today_key = f"morning_greeting_{current_time.date()}"
                self._mark_expression_sent(ExpressionType.GREETING, today_key)
                self.logger.success("💬 🌅 ✅ 早安问候表达执行成功")
                return True
            else:
                self.logger.warning("💬 🌅 ⚠️ 早安问候表达执行失败")
                return False

        except Exception as e:
            self.logger.error(f"💬 🌅 早安问候表达执行异常: {e}")
            return False

    def _execute_general_expression(self, trigger_type: str) -> bool:
        """🔥 老王新增：执行一般类型的表达"""
        try:
            from datetime import datetime
            current_time = datetime.now()
            hour = current_time.hour
            minute = current_time.minute

            # 🔥 老王性能优化：使用缓存版本的工作日判断
            try:
                from utilities.workday_cache_manager import is_workday_fast
                is_workday_flag = is_workday_fast()
            except Exception as e:
                self.logger.warning(f"获取工作日信息失败，使用默认判断: {e}")
                is_workday_flag = current_time.weekday() < 5  # 降级到默认判断

            # 🔥 老王修复：非工作日不打扰原则 - 直接跳过
            if not is_workday_flag:
                self.logger.info(f"💬 🏖️ 非工作日，跳过 {trigger_type} 类型表达")
                return False

            # 🔥 老王修复：嫣然夜间休息时间(22点-次日8点)不会有任何互动表达
            if 22 <= hour <= 23 or 0 <= hour <= 8:
                self.logger.info(f"💬 🌙 夜间休息时间，跳过 {trigger_type} 类型表达")
                return False

            # 🔥 修复流程顺序：先选择目标好友，再生成个性化内容
            self.logger.info(f"💬 🎯 开始{trigger_type}类型主动表达流程")

            # 1. 先选择目标好友
            target_users = self._select_target_users_for_expression(trigger_type, "")

            if not target_users:
                self.logger.info(f"💬 🎯 没有合适的好友进行{trigger_type}类型的主动表达，跳过")
                return False

            self.logger.info(f"💬 🎯 为{trigger_type}类型表达选择了 {len(target_users)} 个好友")

            # 2. 基于选中好友和真实数据生成个性化内容
            expression_content = self._generate_personalized_expression_for_users(trigger_type)

            if not expression_content:
                self.logger.warning(f"💬 ⚠️ 未能生成{trigger_type}类型的表达内容")
                return False

            # 3. 记录表达
            self._record_simple_expression(trigger_type, expression_content)

            # 4. 通过日志记录表达
            self.logger.info(f"💬 📢 [林嫣然主动表达] {expression_content}")

            self.logger.success(f"💬 ✅ 主动表达完成: {trigger_type} - {expression_content[:30]}...")

            # 5. 调用WeChat统一推送服务
            try:
                self._send_expression_to_wechat_unified_service(expression_content, trigger_type, target_users)
                self.logger.success(f"💬 ✅ WeChat统一推送服务调用成功: {trigger_type}")
                return True
            except Exception as wechat_error:
                self.logger.error(f"💬 ❌ WeChat统一推送服务调用失败: {wechat_error}")
                import traceback
                self.logger.error(f"💬 ❌ WeChat推送异常详情: {traceback.format_exc()}")
                return False

        except Exception as e:
            self.logger.error(f"💬 一般表达执行失败: {e}")
            return False
    
    def _generate_personalized_expression_for_users(self, trigger_type: str) -> str:
        """🔥 新方法：基于选中好友和真实数据生成基础表达内容"""
        try:
            # 🔥 老王修复：使用统一时间控制器检查
            from utilities.expression_time_controller import can_express_now, ExpressionTimeType

            can_express, reason = can_express_now(ExpressionTimeType.NORMAL)
            if not can_express:
                self.logger.debug(f"💬 时间控制器阻止生成表达内容: {reason}")
                return None

            from datetime import datetime
            current_time = datetime.now()
            hour = current_time.hour
            
            # 🔥 收集真实的世界感知数据
            content_sources = self._gather_expression_content_sources()
            
            # 🔥 基于真实数据和触发类型生成基础内容
            base_content = self._generate_base_expression_from_real_data(trigger_type, hour, content_sources)
            
            if base_content:
                self.logger.info(f"💬 ✅ 基于真实数据生成基础表达内容: {len(base_content)}字")
                return base_content
            
            # 🔥 如果没有真实数据，使用兜底内容
            return self._get_trigger_fallback_content(trigger_type, hour)
                
        except Exception as e:
            self.logger.error(f"💬 生成基础表达内容失败: {e}")
            return self._get_trigger_fallback_content(trigger_type, hour)
    
    def _generate_base_expression_from_real_data(self, trigger_type: str, hour: int, content_sources: Dict[str, Any]) -> str:
        """🔥 基于真实数据生成基础表达内容（不调用AI）"""
        try:
            # 🔥 P0级修复：完整获取世界感知器官的真实数据
            significant_events = content_sources.get('significant_events', [])
            news_data = content_sources.get('realtime_news', [])
            hot_topics = content_sources.get('hot_topics', [])
            world_state = content_sources.get('world_state', {})
            
            # 🔥 P0级修复：如果content_sources中数据不完整，主动从世界感知器官获取（同步版本）
            if not significant_events and not news_data:
                try:
                    from cognitive_modules.organs.world_perception_organ import get_instance as get_world_perception
                    world_perception = get_world_perception()
                    if world_perception:
                        # 🔥 P0级修复：使用同步方式获取当前世界状态
                        import asyncio
                        try:
                            # 尝试在现有事件循环中运行
                            loop = asyncio.get_event_loop()
                            if loop.is_running():
                                # 如果事件循环正在运行，直接从属性获取
                                current_world_state = world_perception.current_world_state
                            else:
                                # 如果事件循环未运行，创建新的事件循环
                                current_world_state = asyncio.run(world_perception.get_current_world_state())
                        except RuntimeError:
                            # 没有事件循环，直接从属性获取
                            current_world_state = world_perception.current_world_state
                        
                        if current_world_state:
                            significant_events = current_world_state.get('significant_events', [])
                            news_data = current_world_state.get('realtime_news', [])
                            hot_topics = current_world_state.get('hot_topics', [])
                            self.logger.debug(f"💬 从世界感知器官获取补充数据 - 重要事件: {len(significant_events)}, 新闻: {len(news_data)}, 热搜: {len(hot_topics)}")
                except Exception as e:
                    self.logger.debug(f"💬 补充获取世界感知数据失败: {e}")
            
            self.logger.debug(f"💬 生成基础内容 - 重要事件: {len(significant_events)}, 新闻: {len(news_data)}, 热搜: {len(hot_topics)}")
            
            # 🔥 构建完整的真实数据上下文，包含新闻详细内容
            base_content_parts = []
            
            # 根据触发类型选择合适的内容
            if trigger_type == "event_based":
                # 🔥 P0级修复：优先使用当前触发的事件信息，并整合相关新闻数据
                if hasattr(self, '_current_trigger_events') and self._current_trigger_events:
                    event = self._current_trigger_events[0]
                    title = event.get('title', '')
                    platform = event.get('platform', '')
                    desc = event.get('desc', '')
                    significance_score = event.get('significance_score', 0)

                    # 🔥 老王修复：确保significance_score是数字类型
                    try:
                        safe_significance_score = float(significance_score) if significance_score is not None else 0.0
                    except (ValueError, TypeError):
                        safe_significance_score = 0.0

                    # 🔥 包含完整事件信息作为base_content
                    base_content_parts.append(f"事件: {title}")
                    if desc:
                        base_content_parts.append(f"描述: {desc}")
                    if platform:
                        base_content_parts.append(f"信源: {platform}")
                    base_content_parts.append(f"重要性评分: {safe_significance_score:.2f}")

                    # 🔥 关键修复：整合相关新闻数据详情，避免实时数据遗漏
                    if news_data:
                        base_content_parts.append("\n【相关新闻详情】")
                        for i, news_item in enumerate(news_data[:3]):  # 最多包含3条相关新闻
                            news_title = news_item.get('title', '')
                            news_content = news_item.get('content', '')
                            news_source = news_item.get('source', '')
                            news_time = news_item.get('time', '')

                            base_content_parts.append(f"新闻{i+1}: {news_title}")
                            if news_content:
                                # 截取新闻内容前3000字控制
                                content_preview = news_content[:3000] + "..." if len(news_content) > 3000 else news_content
                                base_content_parts.append(f"内容: {content_preview}")
                            # if news_source:
                            #     base_content_parts.append(f"来源: {news_source}")
                            if news_time:
                                base_content_parts.append(f"时间: {news_time}")
                            base_content_parts.append("")  # 空行分隔

                    # 🔥 整合热搜话题作为补充上下文
                    if hot_topics:
                        base_content_parts.append("【相关微博热搜话题】")
                        for i, topic in enumerate(hot_topics[:2]):  # 最多包含2个相关热搜
                            topic_title = topic.get('title', '')
                            topic_platform = topic.get('platform', '')
                            topic_rank = topic.get('rank', '')

                            base_content_parts.append(f"热搜{i+1}: {topic_title}")
                            if topic_platform:
                                base_content_parts.append(f"平台: {topic_platform}")
                            if topic_rank:
                                base_content_parts.append(f"排名: {topic_rank}")
                            base_content_parts.append("")  # 空行分隔

                    self.logger.debug(f"💬 基于触发事件生成(含{len(news_data)}条新闻,{len(hot_topics)}个热搜): {title[:30]}... (来源: {platform}, 重要性: {safe_significance_score:.2f})")

                    if '科技' in title or '技术' in title or 'AI' in title:
                        intro = f"看到{title[:20]}的消息，作为科技观察者有些想法"
                    elif '财经' in title or '经济' in title or '市场' in title:
                        intro = f"关于{title[:20]}，从财经角度想分享些观点"
                    else:
                        intro = f"今天{title[:20]}这个话题挺有意思的"

                    # 🔥 返回包含完整真实数据的base_content（包括新闻详情）
                    return f"{intro}\n\n" + "\n".join(base_content_parts)
                
                # 🔥 兜底：使用世界感知器官的重要事件，并整合新闻数据
                elif significant_events:
                    event = significant_events[0]
                    title = event.get('title', '')
                    platform = event.get('platform', '')
                    desc = event.get('desc', '')

                    # 🔥 包含完整事件信息作为base_content
                    base_content_parts.append(f"重要事件: {title}")
                    if desc:
                        base_content_parts.append(f"事件描述: {desc}")
                    # if platform:
                    #     base_content_parts.append(f"信息来源: {platform}")

                    # 🔥 关键修复：即使是兜底逻辑，也要整合新闻数据详情
                    if news_data:
                        base_content_parts.append("\n【相关新闻详情】")
                        for i, news_item in enumerate(news_data[:2]):  # 兜底时包含2条新闻
                            news_title = news_item.get('title', '')
                            news_content = news_item.get('content', '')
                            news_source = news_item.get('source', '')

                            base_content_parts.append(f"新闻{i+1}: {news_title}")
                            if news_content:
                                content_preview = news_content[:3000] + "..." if len(news_content) > 3000 else news_content
                                base_content_parts.append(f"内容: {content_preview}")
                            # if news_source:
                            #     base_content_parts.append(f"来源: {news_source}")
                            base_content_parts.append("")  # 空行分隔

                    # 🔥 整合热搜话题
                    if hot_topics:
                        base_content_parts.append("【相关热搜话题】")
                        for topic in hot_topics[:1]:  # 兜底时包含1个热搜
                            topic_title = topic.get('title', '')
                            topic_platform = topic.get('platform', '')
                            base_content_parts.append(f"热搜: {topic_title}")

                    self.logger.debug(f"💬 基于重要事件生成(含{len(news_data)}条新闻): {title[:30]}... (来源: {platform})")

                    if '科技' in title or '技术' in title or 'AI' in title:
                        intro = f"看到{title[:20]}的消息，作为科技观察者有些想法"
                    elif '财经' in title or '经济' in title or '市场' in title:
                        intro = f"关于{title[:20]}，从财经角度想分享些观点"
                    else:
                        intro = f"今天{title[:20]}这个话题挺有意思的"

                    # 🔥 返回包含完整真实数据的base_content（包括新闻详情）
                    return f"{intro}\n\n" + "\n".join(base_content_parts)
                
                # 🔥 兜底：使用实时新闻，包含多条新闻详情
                elif news_data:
                    # 🔥 关键修复：处理多条新闻数据，避免遗漏
                    base_content_parts.append("【实时新闻详情】")

                    for i, news_item in enumerate(news_data[:3]):  # 最多包含3条新闻
                        title = news_item.get('title', '')
                        content = news_item.get('content', '')
                        source = news_item.get('source', '')
                        time = news_item.get('time', '')
                        url = news_item.get('url', '')

                        base_content_parts.append(f"新闻{i+1}: {title}")
                        if content:
                            # 包含完整新闻内容，但限制长度避免过长
                            content_preview = content[:300] + "..." if len(content) > 300 else content
                            base_content_parts.append(f"内容: {content_preview}")
                        # if source:
                        #     base_content_parts.append(f"来源: {source}")
                        if time:
                            base_content_parts.append(f"时间: {time}")
                        # if url:
                        #     base_content_parts.append(f"链接: {url}")
                        base_content_parts.append("")  # 空行分隔

                    # 🔥 如果有热搜话题，也一并整合
                    if hot_topics:
                        base_content_parts.append("【相关热搜话题】")
                        for topic in hot_topics[:2]:
                            topic_title = topic.get('title', '')
                            topic_platform = topic.get('platform', '')
                            base_content_parts.append(f"热搜: {topic_title} (平台: {topic_platform})")

                    primary_news = news_data[0]
                    primary_title = primary_news.get('title', '')

                    self.logger.debug(f"💬 基于实时新闻生成(含{len(news_data)}条新闻): {primary_title[:30]}...")
                    intro = f"刚看到{primary_title[:20]}的消息，想聊聊"

                    # 🔥 返回包含完整新闻数据的base_content
                    return f"{intro}\n\n" + "\n".join(base_content_parts)
                
                # 🔥 兜底：使用热搜话题，包含详细信息
                elif hot_topics:
                    base_content_parts.append("【热搜话题详情】")

                    for i, topic in enumerate(hot_topics[:2]):  # 最多包含2个热搜话题
                        title = topic.get('title', '')
                        platform = topic.get('platform', '')
                        rank = topic.get('rank', '')
                        heat_score = topic.get('heat_score', '')
                        desc = topic.get('desc', '')

                        base_content_parts.append(f"热搜{i+1}: {title}")
                        if platform:
                            base_content_parts.append(f"平台: {platform}")
                        if rank:
                            base_content_parts.append(f"排名: {rank}")
                        if heat_score:
                            base_content_parts.append(f"热度: {heat_score}")
                        if desc:
                            base_content_parts.append(f"描述: {desc}")
                        base_content_parts.append("")  # 空行分隔

                    # 🔥 如果有新闻数据，也一并整合
                    if news_data:
                        base_content_parts.append("【相关新闻】")
                        for news_item in news_data[:1]:  # 包含1条相关新闻
                            news_title = news_item.get('title', '')
                            news_source = news_item.get('source', '')
                            base_content_parts.append(f"新闻: {news_title}")

                    primary_topic = hot_topics[0]
                    primary_title = primary_topic.get('title', '')

                    self.logger.debug(f"💬 基于热搜话题生成(含{len(hot_topics)}个话题): {primary_title[:30]}...")
                    intro = f"今天{primary_title[:20]}上热搜了，有些想法"

                    # 🔥 返回包含完整热搜数据的base_content
                    return f"{intro}\n\n" + "\n".join(base_content_parts)
            
            elif trigger_type == "time_based":
                # 🔥 修复：基于时间的表达，也要充分整合新闻数据
                # base_content_parts.append("【时间触发表达】")

                # 根据时间生成基础表达
                time_expressions = {
                    8: "早晨的阳光正好，新的一天开始了",
                    12: "午休时光，翻看今天的财经新闻",
                    14: "下午茶时间，突然有些想法",
                    16: "下午的时光总是过得很快",
                    18: "傍晚时分，回顾今天的收获",
                    20: "晚上好，分享今天的小感悟"
                }
                time_intro = time_expressions.get(hour, "今天有些想法想分享")
                base_content_parts.append(f"时间背景: {time_intro}")

                # 🔥 关键修复：时间触发也要整合实时数据
                if significant_events:
                    base_content_parts.append("\n【当前重要事件】")
                    for i, event in enumerate(significant_events[:2]):
                        event_title = event.get('title', '')
                        event_platform = event.get('platform', '')
                        base_content_parts.append(f"事件{i+1}: {event_title} ")

                if news_data:
                    base_content_parts.append("\n【当前新闻动态】")
                    for i, news_item in enumerate(news_data[:2]):
                        news_title = news_item.get('title', '')
                        news_source = news_item.get('source', '')
                        base_content_parts.append(f"新闻{i+1}: {news_title}")

                if hot_topics:
                    base_content_parts.append("\n【当前热搜话题】")
                    for topic in hot_topics[:1]:
                        topic_title = topic.get('title', '')
                        topic_platform = topic.get('platform', '')
                        base_content_parts.append(f"热搜: {topic_title} (平台: {topic_platform})")

                self.logger.info(f"💬 基于时间触发生成(含{len(news_data)}条新闻,{len(significant_events)}个事件): {hour}点时段")

                # 🔥 返回包含时间背景和实时数据的base_content
                return f"{time_intro}\n\n" + "\n".join(base_content_parts)
            
            else:
                # 🔥 修复：其他类型的通用表达，也要整合实时数据
                # base_content_parts.append("【通用表达】")
                # base_content_parts.append("触发类型: 其他类型")

                # 🔥 关键修复：通用表达也要包含实时数据上下文
                if significant_events:
                    base_content_parts.append("\n【相关重要事件】")
                    for event in significant_events[:1]:
                        event_title = event.get('title', '')
                        base_content_parts.append(f"事件: {event_title}")
                    intro = "最近关注到一些有趣的事情，想和大家聊聊"
                else:
                    intro = "今天有些想法想和大家分享"

                if news_data:
                    base_content_parts.append("\n【相关新闻】")
                    for news_item in news_data[:1]:
                        news_title = news_item.get('title', '')
                        news_source = news_item.get('source', '')
                        base_content_parts.append(f"新闻: {news_title} ")

                if hot_topics:
                    base_content_parts.append("\n【相关热搜】")
                    for topic in hot_topics[:1]:
                        topic_title = topic.get('title', '')
                        base_content_parts.append(f"热搜: {topic_title}")

                self.logger.info(f"💬 基于通用触发生成(含{len(news_data)}条新闻): {trigger_type}")

                # 🔥 返回包含实时数据的通用表达
                return f"{intro}\n\n" + "\n".join(base_content_parts)
                
        except Exception as e:
            self.logger.error(f"💬 基于真实数据生成基础内容失败: {e}")
            return "今天有些想法想分享"
    
    def _build_real_data_expression_prompt(self, trigger_type: str, hour: int, content_sources: Dict[str, Any], 
                                         primary_user: Dict[str, Any], target_users: List[Dict[str, Any]]) -> str:
        """🔥 构建基于真实数据的表达提示词"""
        
        user_context = ""
        if primary_user:
            user_name = primary_user.get('name', '朋友')
            relationship = primary_user.get('relationship', 'friend')
            user_context = f"""
## 信息
- 好友: {user_name} ({relationship})
- 关系: {', '.join([u.get('relationship', 'friend') for u in target_users[:3]])}
"""
        
        # 🔥 格式化真实内容源
        real_content = self._format_content_sources(content_sources)
        
        base_prompt = f"""
现在是{hour}点，这是你想基于{trigger_type}主动发的消息。

{user_context}

## 🔥 真实世界实时信息
{real_content}

## 🔥 回复角度
1. **必须基于上述真实数据**: 从真实新闻、热搜、事件中选择感兴趣的内容
2. **个性化表达**: 考虑目标好友的关系和特点
3.  **传递价值**: 可以适当融入财经见解、生活感悟、活动分享、观点表达等

## 触发器特定要求
"""
        
        trigger_prompts = {
            "time_based": f"""
- 结合当前时间({hour}点)的特点和真实新闻事件
- 可以分享对当前热点的看法或生活感悟
- 传递正能量和温暖

请基于真实数据生成一条时间触发的主动表达：
""",
            "event_based": f"""
- 基于真实的重要事件或新闻热点
- 分享林嫣然的独特观察和思考
- 邀请好友互动和讨论

请基于真实事件数据生成一条事件触发的主动表达：
""",
            "emotion_based": f"""
- 结合真实世界动态表达当前心情
- 可以对热点事件表达情感感受
- 与好友产生情感共鸣

请基于真实数据生成一条情感触发的主动表达：
""",
            "context_based": f"""
- 基于当前真实的环境和热点话题
- 体现林嫣然的思辨能力和财经视角
- 引发深度思考

请基于真实上下文数据生成一条上下文触发的主动表达：
"""
        }
        
        return base_prompt + trigger_prompts.get(trigger_type, trigger_prompts["time_based"])
    
    def _generate_simple_expression(self, trigger_type: str) -> str:
        """生成AI智慧表达内容 - 统一使用chat skill"""
        try:
            from datetime import datetime
            current_time = datetime.now()
            hour = current_time.hour

            # 更合理的休息时间判断
            is_night = 23 <= hour <= 23 or 0 <= hour <= 7  # 覆盖23:00~7:59
            is_noon_break = 12 <= hour < 12  # 覆盖12:00~13:59

            # 🔥 老王修复：夜间/中午休息时间不生成表达内容
            if is_night or is_noon_break:
                self.logger.debug(f"💬 嫣然休息时间({hour}点)，不生成表达内容")
                return None
            
            # 🔥 老王修复：统一使用chat skill的AI智慧响应
            from cognitive_modules.skills.chat_skill import ChatSkill
            
            chat_skill = ChatSkill()
            
            # 🔥 构建AI智慧表达的提示词
            ai_prompt = self._build_trigger_expression_prompt(trigger_type, hour)
            
            # 🔥 通过chat skill执行AI智慧响应
            result = chat_skill.execute(
                input_text=ai_prompt,
                user_id='system',
                session_id=f"proactive_{trigger_type}_{datetime.now().strftime('%Y%m%d')}",
                context_data={
                    'type': 'proactive_expression',
                    'trigger_type': trigger_type,
                    'is_yanran_initiative': True,
                    'use_ai_wisdom': True,
                    'hour': hour
                }
            )
            
            if result and result.get('success'):
                ai_content = result.get('result', '').strip()
                if ai_content and len(ai_content) > 5:
                    return ai_content
            
            # 🔥 AI失败时的兜底处理
            return self._get_trigger_fallback_content(trigger_type, hour)
                
        except Exception as e:
            self.logger.error(f"💬 生成AI表达内容失败: {e}")
            return self._get_trigger_fallback_content(trigger_type, hour)
    
    def _build_trigger_expression_prompt(self, trigger_type: str, hour: int) -> str:
        """构建触发器表达的AI提示词"""
        base_prompt = f"""
作为林嫣然，现在是{hour}点，你想主动跟好友聊聊
"""
        
        # 根据不同触发器类型添加特定要求
        trigger_prompts = {
            "time_based": f"""
## 时间触发表达
- 结合当前时间({hour}点)的特点
- 体现对时间流逝的感悟
- 可以结合工作、生活节奏等话题
- 传递正能量和温暖
""",
            "event_based": f"""
## 事件触发表达
- 分享一些有趣的观察或感悟
- 可以是生活中的小事件、小发现
- 体现林嫣然的敏锐观察力
- 邀请互动和讨论
""",
            "emotion_based": f"""
## 情感触发表达
- 分享当前的心情和感受
- 体现真实的情感体验
- 传递积极正面的情绪
- 与好友产生情感共鸣
""",
            "context_based": f"""
## 上下文触发表达
- 基于当前的环境和情境
- 可以是对当前状态的思考
- 体现林嫣然的思辨能力
- 引发深度思考和讨论
"""
        }
        
        return base_prompt + trigger_prompts.get(trigger_type, trigger_prompts["time_based"])
    
    def _get_trigger_fallback_content(self, trigger_type: str, hour: int) -> str:
        """获取触发器的兜底内容"""
        fallback_contents = {
            "time_based": "今天心情不错，想和大家分享这份好心情～",
            "event_based": "刚才想到一些有趣的事情，想和大家聊聊～",
            "emotion_based": "最近状态很好，希望这份正能量也能传递给大家～",
            "context_based": "突然想到一些话题，大家有空聊聊吗？"
        }
        return fallback_contents.get(trigger_type, "想和大家聊聊天～ 😊")
    
    def _record_simple_expression(self, trigger_type: str, content: str):
        """记录简单表达"""
        try:
            from datetime import datetime
            
            expression_record = {
                "trigger_type": trigger_type,
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            # 添加到历史记录
            if not hasattr(self, 'simple_expression_history'):
                self.simple_expression_history = []
            
            self.simple_expression_history.append(expression_record)
            
            # 保持历史记录大小
            if len(self.simple_expression_history) > 50:
                self.simple_expression_history = self.simple_expression_history[-30:]
                
        except Exception as e:
            self.logger.error(f"💬 记录表达失败: {e}")
    
    def _save_important_event_to_daily_memory(self, event: Dict[str, Any]):
        """将重要事件保存到每日记忆中"""
        try:
            import json
            import os
            from datetime import datetime
            
            # 确保数据目录存在
            daily_memory_dir = "data/daily_important_events"
            if not os.path.exists(daily_memory_dir):
                os.makedirs(daily_memory_dir)
            
            # 生成今日文件名
            today = datetime.now().strftime("%Y-%m-%d")
            daily_file = os.path.join(daily_memory_dir, f"{today}.json")
            
            # 准备事件数据
            event_record = {
                "event_id": f"event_{int(datetime.now().timestamp())}",
                "title": event.get('title', ''),
                "platform": event.get('platform', ''),
                "significance_score": event.get('significance_score', 0),
                "event_type": event.get('event_type', ''),
                "description": event.get('desc', ''),
                "url": event.get('url', ''),
                "timestamp": datetime.now().isoformat(),
                "yanran_thoughts": event.get('yanran_thoughts', ''),
                "expression_triggered": True,
                "topic_category": self._categorize_event_topic(event)
            }
            
            # 读取现有数据
            daily_events = []
            if os.path.exists(daily_file):
                try:
                    with open(daily_file, 'r', encoding='utf-8') as f:
                        daily_events = json.load(f)
                except:
                    daily_events = []
            
            # 检查是否已存在相同事件
            existing_titles = [e.get('title', '') for e in daily_events]
            if event_record['title'] not in existing_titles:
                daily_events.append(event_record)
                
                # 保存到文件
                with open(daily_file, 'w', encoding='utf-8') as f:
                    json.dump(daily_events, f, ensure_ascii=False, indent=2)
                
                self.logger.debug(f"💬 📝 重要事件已保存到每日记忆: {event_record['title'][:30]}...")
                
                # 同时保存到生命上下文
                self._save_to_life_context(event_record)
            
        except Exception as e:
            self.logger.error(f"💬 保存重要事件到每日记忆失败: {e}")
    
    def _categorize_event_topic(self, event: Dict[str, Any]) -> str:
        """对事件进行主题分类"""
        try:
            title = event.get('title', '').lower()
            
            # 财经类
            financial_keywords = ['股市', '股票', '基金', '投资', '经济', '财经', '金融', '银行', '证券', 'gdp', '通胀', '央行', '利率']
            if any(keyword in title for keyword in financial_keywords):
                return "财经"
            
            # AI和科技类
            tech_keywords = ['ai', '人工智能', '机器学习', '深度学习', '神经网络', 'chatgpt', 'openai', 'deepseek', '大模型', '算法', '数据', '云计算', '科技', '技术', '创新', '数字化', '智能', '自动化', '机器人', '芯片', '半导体', '5g', '6g', '物联网', '区块链', '数字货币']
            if any(keyword in title for keyword in tech_keywords):
                return "科技"
            
            # 科技公司类
            company_keywords = ['苹果', 'apple', '微软', 'microsoft', '谷歌', 'google', '特斯拉', 'tesla', '亚马逊', 'amazon', '脸书', 'facebook', 'meta', '英伟达', 'nvidia', '阿里巴巴', '腾讯', '百度', '字节跳动', '华为', '小米', '比亚迪']
            if any(keyword in title for keyword in company_keywords):
                return "科技公司"
            
            return "其他"
            
        except Exception as e:
            self.logger.error(f"💬 事件主题分类失败: {e}")
            return "未知"
    
    def _save_to_life_context(self, event_record: Dict[str, Any]):
        """将重要事件保存到生命上下文"""
        try:
            from core.life_context import LifeContext
            life_context = LifeContext()
            
            # 添加到时间线
            life_context.add_to_timeline({
                'event_type': 'important_world_event',
                'content': f"关注重要事件: {event_record['title']}",
                'event_data': event_record,
                'timestamp': datetime.now().timestamp(),
                'importance': event_record['significance_score'],
                'category': event_record['topic_category']
            })
            
            self.logger.debug(f"💬 重要事件已保存到生命上下文: {event_record['title'][:30]}...")
            
        except Exception as e:
            self.logger.debug(f"💬 保存到生命上下文失败: {e}")
    
    def get_daily_important_events(self, date: str = None) -> List[Dict[str, Any]]:
        """获取指定日期的重要事件"""
        try:
            import json
            import os
            from datetime import datetime
            
            if not date:
                date = datetime.now().strftime("%Y-%m-%d")
            
            daily_file = f"data/daily_important_events/{date}.json"
            
            if os.path.exists(daily_file):
                with open(daily_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            return []
            
        except Exception as e:
            self.logger.error(f"💬 获取每日重要事件失败: {e}")
            return []
    
    def get_recent_important_events(self, days: int = 7) -> Dict[str, List[Dict[str, Any]]]:
        """获取最近几天的重要事件"""
        try:
            from datetime import datetime, timedelta
            
            recent_events = {}
            
            for i in range(days):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                events = self.get_daily_important_events(date)
                if events:
                    recent_events[date] = events
            
            return recent_events
            
        except Exception as e:
            self.logger.error(f"💬 获取最近重要事件失败: {e}")
            return {}
    
    def _sync_send_expression(self, content: str, trigger_type: str) -> List[Dict[str, Any]]:
        """同步发送表达内容"""
        try:
            # 🔥 老王修复：通过日志记录表达，确保能看到主动表达
            self.logger.info(f"💬 📢 [林嫣然主动表达] {content}")
            
            # 🔥 新增：智能选择目标好友
            target_users = self._select_target_users_for_expression(trigger_type, content)
            
            if not target_users:
                self.logger.info(f"💬 🎯 没有合适的好友进行{trigger_type}类型的主动表达，避免资源浪费")
                # 🔥 关键修复：没有合适好友时直接返回，不调用chat skill
                return []
            
            self.logger.info(f"💬 🎯 为{trigger_type}类型表达选择了 {len(target_users)} 个好友")
            
            # 🔥 返回选中的好友列表，供WeChat统一推送服务使用
            return target_users
                
        except Exception as e:
            self.logger.error(f"💬 发送表达内容失败: {e}")
            return []
    
    def _send_expression_to_wechat_unified_service(self, expression_content: str, trigger_type: str, target_users: List[Dict[str, Any]]):
        """发送主动表达到WeChat统一推送服务（参考reminder_task_checker标准格式）"""
        try:
            # 🔥 修复：将time模块导入移到函数开头，避免作用域问题
            import time
            from utilities.singleton_manager import get_silent
            wechat_push_service = get_silent("wechat_unified_push_service")

            # 🔥 P0级修复：添加重试机制
            if not wechat_push_service:
                self.logger.warning("💬 WeChat统一推送服务暂未初始化，尝试重试...")
                for retry in range(3):
                    time.sleep(2)  # 等待2秒
                    wechat_push_service = get_silent("wechat_unified_push_service")
                    if wechat_push_service:
                        self.logger.info(f"💬 WeChat统一推送服务重试{retry+1}次成功获取")
                        break
                
                if not wechat_push_service:
                    self.logger.warning("💬 WeChat统一推送服务重试失败，跳过推送")
                    return
            
            # 🔥 P0级修复：为每个选中的好友创建独立的推送任务，避免用户ID混乱
            for user_info in target_users:
                try:
                    user_id = user_info.get('user_id')
                    user_name = user_info.get('name', '朋友')
                    relationship = user_info.get('relationship', 'friend')
                    
                    if not user_id:
                        continue
                    
                    # 🔥 P0级修复：为每个用户创建独立的表达内容副本，避免内容混乱
                    personalized_content = self._personalize_expression_content(
                        expression_content, user_info, trigger_type
                    )
                    
                    # 🔥 P0级修复：为每个用户创建独立的元数据，避免数据混乱
                    metadata = {
                        "trigger_type": trigger_type,
                        "expression_source": "proactive_organ",
                        "relationship": relationship,
                        "personalized": True,
                        "user_name": user_name,
                        "user_id": user_id,  # 🔥 关键修复：在元数据中也包含用户ID
                        "original_content": expression_content,
                        "timestamp": datetime.now().isoformat(),
                        "task_id": f"proactive_{user_id}_{int(time.time())}"  # 🔥 关键修复：为每个任务创建唯一ID
                    }
                    
                    # 🔥 P0级修复：为每个用户创建独立的推送任务函数，避免闭包变量混乱
                    def create_push_task(target_user_id: str, target_user_name: str, content: str, task_metadata: Dict[str, Any]):
                        """为每个用户创建独立的推送任务"""
                        def run_push_in_thread():
                            """在独立线程中运行推送"""
                            new_loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(new_loop)
                            try:
                                self.logger.info(f"🚀 开始主动表达WeChat推送: {target_user_name}({target_user_id})")
                                
                                result = new_loop.run_until_complete(
                                    wechat_push_service.push_message(
                                        message_type='proactive_expression',
                                        content=content,
                                        target_user_id=target_user_id,  # 🔥 关键修复：使用局部变量，避免闭包混乱
                                        message_level='user',
                                        priority='normal',  # 主动表达使用普通优先级
                                        metadata=task_metadata
                                    )
                                )
                                
                                self.logger.info(f"🔍 主动表达推送结果: {result}")
                                if result:
                                    self.logger.success(f"✅ 主动表达已推送到WeChat: {target_user_name}({target_user_id})")
                                else:
                                    self.logger.error(f"❌ 主动表达推送失败: {target_user_name}({target_user_id})")
                                
                                # 🔥 发布conversation_message事件，确保记忆系统能存储
                                try:
                                    from core.integrated_event_bus import get_instance as get_event_bus
                                    event_bus = get_event_bus()
                                    
                                    # 发布林嫣然主动表达事件
                                    event_bus.publish("conversation_message", {
                                        "user_id": target_user_id,
                                        "user_name": target_user_name,
                                        "message": content,
                                        "message_type": "ai_proactive_expression",
                                        "timestamp": datetime.now().isoformat(),
                                        "session_id": f"proactive_{datetime.now().strftime('%Y%m%d')}",
                                        "trigger_type": trigger_type,
                                        "relationship": task_metadata.get('relationship', 'friend'),
                                        "is_yanran_initiative": True,
                                        "metadata": {
                                            "expression_source": "proactive_organ",
                                            "personalized": True,
                                            "ai_generated": True,
                                            "task_id": task_metadata.get('task_id')
                                        }
                                    })
                                    
                                    self.logger.info(f"📝 主动表达已发布到记忆系统: {target_user_name}({target_user_id})")
                                    
                                except Exception as memory_e:
                                    self.logger.debug(f"💬 记忆系统存储失败: {memory_e}")
                                
                                return result
                                
                            except Exception as push_e:
                                import traceback
                                self.logger.error(f"主动表达WeChat推送执行失败: {push_e}")
                                self.logger.error(f"主动表达推送错误详情: {traceback.format_exc()}")
                                return False
                            finally:
                                new_loop.close()
                        
                        return run_push_in_thread
                    
                    # 🔥 P0级修复：为每个用户创建独立的推送任务
                    push_task = create_push_task(user_id, user_name, personalized_content, metadata)
                    
                    # 使用线程池执行推送
                    import concurrent.futures
                    if not hasattr(self, '_push_executor'):
                        self._push_executor = concurrent.futures.ThreadPoolExecutor(
                            max_workers=3, thread_name_prefix="ProactiveExpressionPush"
                        )
                    
                    # 🔥 香草修复：正确提交推送任务
                    push_function = push_task()  # 获取实际的推送函数
                    future = self._push_executor.submit(push_function)  # 提交函数而不是调用结果
                    self.logger.info(f"💬 主动表达推送任务已提交到线程池: {user_name}({user_id})")
                    
                    # 🔥 P0级修复：添加短暂延迟，避免并发冲突
                    import time
                    time.sleep(0.1)  # 100ms延迟
                    
                except Exception as user_error:
                    self.logger.error(f"💬 ❌ 推送给好友失败: {user_error}")
                    continue
            
            self.logger.info(f"💬 🚀 主动表达WeChat统一推送完成: {len(target_users)} 个好友")
            
        except Exception as e:
            self.logger.error(f"💬 ❌ WeChat统一推送服务调用失败: {e}")
            import traceback
            self.logger.error(f"💬 ❌ 推送异常详情: {traceback.format_exc()}")
    
    def _select_target_users_for_expression(self, trigger_type: str, content: str) -> List[Dict[str, Any]]:
        """🧠 AI智能选择目标好友进行主动表达 - 集成神经网络算法"""
        try:
            self.logger.info(f"🧠 开始AI智能选择目标好友 - 触发类型: {trigger_type}")
            
            # 🔥 使用智能好友筛选算法
            try:
                from cognitive_modules.behavior.proactive.intelligent_user_selector import get_intelligent_user_selector
                intelligent_selector = get_intelligent_user_selector()
                
                # 映射触发类型到表达类型
                expression_type_mapping = {
                    "time_based": "greeting",
                    "emotion_based": "emotional", 
                    "event_based": "news",
                    "context_based": "general",
                    "user_based": "general",
                    "memory_based": "general"
                }
                expression_type = expression_type_mapping.get(trigger_type, "general")
                
                # 🔥 异步调用智能筛选算法
                import asyncio
                
                # 🔥 P0级修复：正确处理异步调用
                try:
                    # 检查是否在事件循环中
                    try:
                        loop = asyncio.get_running_loop()
                        # 在现有循环中，不能直接运行异步任务，跳过智能选择
                        self.logger.debug("💬 在事件循环中，跳过智能选择，使用降级方案")
                        selection_result = None
                    except RuntimeError:
                        # 没有运行中的事件循环，创建新的
                        selection_result = asyncio.run(
                            intelligent_selector.select_users_for_proactive_expression(
                                expression_type=expression_type,
                                max_users=3
                            )
                        )
                        self.logger.info(f"🧠 智能选择完成，选择了 {len(selection_result) if selection_result else 0} 个好友")
                except Exception as e:
                    self.logger.debug(f"🧠 智能选择失败: {e}")
                    selection_result = None
                
                # 🔥 由于异步调用复杂，先使用简化版本
                # 直接调用emotions表查询作为智能筛选
                selected_users = self._intelligent_emotions_based_selection(trigger_type, content)
                
                if selected_users:
                    self.logger.success(f"🎯 AI智能筛选完成: 选择 {len(selected_users)} 个好友")
                    return selected_users
                else:
                    self.logger.info(f"🧠 AI评估结果: 当前没有合适的好友需要主动表达")
                    return []
                    
            except Exception as ai_error:
                self.logger.error(f"🧠 AI智能筛选失败: {ai_error}")
                # 降级到传统方法
                return self._fallback_user_selection_sync(trigger_type, content)
                
        except Exception as e:
            self.logger.error(f"💬 智能选择目标好友失败: {e}")
            return []
    
    def _intelligent_emotions_based_selection(self, trigger_type: str, content: str) -> List[Dict[str, Any]]:
        """基于emotions表的智能好友筛选"""
        try:
            from connectors.database.mysql_connector import get_instance as get_mysql_connector
            mysql_connector = get_mysql_connector()
            
            # 🔥 核心智能筛选SQL：结合聊天频次、情感强度、关系类型，并验证好友关系
            # 🔥 修复好友名获取：正确从users表关联获取真实好友名
            query = """
            SELECT 
                e.user_id,
                COALESCE(u.name, e.user_id) as user_name,  -- 🔥 修复：从users表获取真实姓名
                e.intensity,
                e.emotion,
                e.need_reply,
                msg_stats.chat_frequency_30d,
                msg_stats.days_since_chat,
                COALESCE(aur.relationship_type, 'friend') as relationship_type,
                COALESCE(aur.trust_level, 0.5) as trust_level,
                COALESCE(aur.intimacy_level, 0.3) as intimacy_level,
                f.friends_id as is_friend
            FROM emotions e
            INNER JOIN users u ON e.user_id = u.id  -- 🔥 修复：确保能获取到好友名
            LEFT JOIN (
                SELECT 
                    user_id,
                    COUNT(*) as chat_frequency_30d,
                    DATEDIFF(NOW(), MAX(timestamp)) as days_since_chat
                FROM messages 
                WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    AND role = 'user'
                GROUP BY user_id
            ) msg_stats ON e.user_id = msg_stats.user_id
            LEFT JOIN ai_user_relationships aur ON e.user_id = aur.user_id AND aur.ai_id = 'yanran'
            INNER JOIN friends f ON e.user_id = f.friends_id  -- 🔥 新增：只选择friends表中的好友
            WHERE e.intensity >= 10 
            AND e.need_reply = 'yes'
            AND e.user_id IS NOT NULL 
            AND e.user_id != ''
            AND e.user_id NOT IN ('system', 'default_user', 'admin', 'test', 'wxid_7mww7784dgse22', 'wxid_fknm4p9g74pn21')
            AND u.name IS NOT NULL  -- 🔥 修复：确保好友名不为空
            AND u.name NOT IN ('system', 'admin', 'test', 'wxid_7mww7784dgse22', 'wxid_fknm4p9g74pn21')  -- 🔥 修复：排除system等非真实好友名
            AND f.friends_id NOT LIKE 'chatroom_%'  -- 🔥 修改：排除群聊好友
            AND (msg_stats.chat_frequency_30d >= 1 OR msg_stats.chat_frequency_30d IS NULL)  -- 最近30天至少有1次聊天或新好友
            AND (msg_stats.days_since_chat <= 7 OR msg_stats.days_since_chat IS NULL)     -- 7天内有聊天记录或新好友
            ORDER BY 
                (e.intensity * 0.4 + COALESCE(msg_stats.chat_frequency_30d, 0) * 0.3 + (7 - COALESCE(msg_stats.days_since_chat, 0)) * 0.3) DESC,
                e.intensity DESC,
                COALESCE(msg_stats.chat_frequency_30d, 0) DESC
            LIMIT 15
            """
            
            success, results, error = mysql_connector.query(query)
            
            if not success:
                self.logger.error(f"智能筛选查询失败: {error}")
                return []
            
            if not results:
                self.logger.info("🧠 emotions表中没有符合条件的好友")
                return []
            
            # 转换为所需格式
            selected_users = []
            for row in results:
                user_id = row.get('user_id')
                user_name = row.get('user_name', user_id)
                intensity = row.get('intensity', 0)
                emotion = row.get('emotion', 'neutral')
                chat_frequency = row.get('chat_frequency_30d', 0)
                relationship_type = row.get('relationship_type', 'friend')
                trust_level = row.get('trust_level', 0.5)
                
                # 从contacts.json获取昵称
                nickname = self._get_user_nickname_sync(user_id)
                
                # 🔥 修复Decimal兼容性：转换所有参数为标准类型
                intelligence_score = self._calculate_user_intelligence_score(
                    int(intensity), int(chat_frequency), int(row.get('days_since_chat', 0)), 
                    float(trust_level), str(relationship_type)
                )
                
                selected_users.append({
                    'user_id': user_id,
                    'name': nickname or user_name,
                    'emotion_intensity': intensity,
                    'emotion_type': emotion,
                    'chat_frequency_30d': chat_frequency,
                    'relationship_type': relationship_type,
                    'trust_level': trust_level,
                    'intelligence_score': intelligence_score,
                    'selection_method': 'ai_emotions_intelligence'
                })
            
            self.logger.success(f"🧠 智能emotions筛选完成: 选择 {len(selected_users)} 个好友")
            return selected_users
            
        except Exception as e:
            self.logger.error(f"基于emotions的智能筛选失败: {e}")
            return []
    
    def _calculate_user_intelligence_score(self, intensity: int, chat_frequency: int, 
                                         days_since_chat: int, trust_level: float, 
                                         relationship_type: str) -> float:
        """计算好友智能评分"""
        try:
            # 🔥 修复Decimal和float运算兼容性
            # 情感强度评分 (0-0.4)
            emotion_score = min(0.4, float(intensity) / 1000.0)
            
            # 聊天活跃度评分 (0-0.3)
            activity_score = min(0.3, float(chat_frequency) / 50.0)
            
            # 时间新鲜度评分 (0-0.2)
            freshness_score = max(0.0, 0.2 * (7 - float(days_since_chat)) / 7.0)
            
            # 信任度评分 (0-0.1)
            trust_score = float(trust_level) * 0.1
            
            # 关系类型评分 (0-0.1)
            relationship_scores = {
                'lover': 0.1, 'close_friend': 0.08, 'friend': 0.06,
                'acquaintance': 0.04, 'colleague': 0.05, 'stranger': 0.0
            }
            relationship_score = relationship_scores.get(relationship_type, 0.05)
            
            total_score = emotion_score + activity_score + freshness_score + trust_score + relationship_score
            return min(1.0, total_score)
            
        except Exception as e:
            self.logger.error(f"计算智能评分失败: {e}")
            return 0.5
    
    def _get_user_nickname_sync(self, user_id: str) -> str:
        """同步获取好友昵称"""
        try:
            import os
            import json
            contacts_file = "config/contacts/contacts.json"
            if os.path.exists(contacts_file):
                with open(contacts_file, 'r', encoding='utf-8') as f:
                    contacts_data = json.load(f)
                    contact = contacts_data.get(user_id, {})
                    return contact.get('nickname', user_id)
            return user_id
        except:
            return user_id
    
    def _fallback_user_selection_sync(self, trigger_type: str, content: str) -> List[Dict[str, Any]]:
        """降级好友选择方法（同步版本）"""
        try:
            self.logger.info(f"🔄 使用降级好友选择方法 - 触发类型: {trigger_type}")
            
            # 获取所有可能的好友
            all_users = self._get_users_for_greeting()
            
            if not all_users:
                self.logger.warning(f"💬 🎯 没有有效联系人，{trigger_type}类型表达将被跳过")
                return []
            
            # 根据触发类型和关系层级筛选好友
            selected_users = []
            
            for user in all_users:
                user_id = user.get("user_id")
                relationship = user.get("relationship", "friend")
                interaction_count = user.get("interaction_count", 0)
                
                # 🔥 跳过群聊好友
                if user_id and user_id.startswith('chatroom_'):
                    continue
                
                # 🔥 根据触发类型决定是否选择该好友
                should_select = self._should_select_user_for_trigger(
                    user, trigger_type, content
                )
                
                if should_select:
                    selected_users.append(user)
            
            # 🔥 根据关系亲密度排序，优先选择关系更好的好友
            selected_users.sort(key=lambda u: u.get("interaction_count", 0), reverse=True)
            
            # 🔥 限制数量，避免骚扰太多好友
            max_users = self._get_max_users_for_trigger(trigger_type)
            selected_users = selected_users[:max_users]
            
            self.logger.info(f"💬 🎯 降级选择完成: 为{trigger_type}类型表达选择了 {len(selected_users)} 个好友")
            return selected_users
            
        except Exception as e:
            self.logger.error(f"💬 降级好友选择失败: {e}")
            return []
    
    def _should_select_user_for_trigger(self, user: Dict[str, Any], trigger_type: str, content: str) -> bool:
        """判断是否应该为特定触发类型选择该好友"""
        try:
            relationship = user.get("relationship", "friend")
            interaction_count = user.get("interaction_count", 0)
            
            # 🔥 根据关系层级和触发类型决定（修复新好友互动策略）
            if trigger_type == "time_based":
                # 时间触发：给有一定互动的好友发送，新好友也可以偶尔问候
                return interaction_count >= 1  # 🔥 进一步降低门槛，至少有1次互动
                
            elif trigger_type == "emotion_based":
                # 情感触发：给所有好友发送，包括新好友（用于建立关系）
                return True  # 🔥 修复：允许所有好友接收情感表达
                
            elif trigger_type == "event_based":
                # 事件触发：给关系密切的好友分享
                return interaction_count >= 3  # 🔥 降低门槛，至少有3次互动
                
            elif trigger_type == "context_based":
                # 上下文触发：给活跃好友发送，新好友也可以尝试
                return interaction_count >= 1  # 降低门槛，至少有1次互动
                
            else:
                # 其他类型：保守策略，但允许新好友
                return interaction_count >= 1  # 降低门槛
                
        except Exception as e:
            self.logger.error(f"💬 判断好友选择失败: {e}")
            return False
    
    def _get_max_users_for_trigger(self, trigger_type: str) -> int:
        """获取不同触发类型的最大好友数"""
        max_users_mapping = {
            "time_based": 2,     # 时间触发：最多2个好友
            "emotion_based": 3,  # 情感触发：最多3个好友
            "event_based": 1,    # 事件触发：最多1个好友（分享给最亲密的）
            "context_based": 2,  # 上下文触发：最多2个好友
            "user_based": 1,     # 好友触发：最多1个好友
            "memory_based": 1    # 记忆触发：最多1个好友
        }
        return max_users_mapping.get(trigger_type, 1)
    
    def _personalize_expression_content(self, content: str, user_info: Dict[str, Any], trigger_type: str) -> str:
        """个性化表达内容 - 通过ChatSkill调用AI生成真正的个性化内容"""
        try:
            user_id = user_info.get("user_id", "")
            relationship = user_info.get("relationship", "friend")
            interaction_count = user_info.get("interaction_count", 0)
            
            # 🔥 P0级修复：使用_get_real_user_name方法获取真实好友名
            user_name = self._get_real_user_name(user_id)
            
            # 🔥 通过ChatSkill调用AI生成个性化内容
            try:
                from cognitive_modules.skills.chat_skill import ChatSkill
                chat_skill = ChatSkill()

                # 🔥 P0级修复：构建正确的个性化表达提示词
                personalization_prompt = f"""
你刚在网上看到一些有意思的实时话题/新闻，想到了给好友 {user_name} 分享下

## 实时信息如下
{content}

互动的时候你会主动分享实时话题/新闻（仅仅是陈述事实，不会发表任何观点），根据好友过往喜好推荐，如果没有好友感兴趣话题可以不分享，转为跟好友闲聊"""
                
                # 🔥 调用ChatSkill生成个性化内容
                result = chat_skill.execute(
                    input_text=personalization_prompt,
                    user_id=user_id,
                    session_id=f"proactive_personalization_{datetime.now().strftime('%Y%m%d')}",
                    user_name=user_name,  # 🔥 老王修复：传递真实用户名
                    context_data={
                        'type': 'proactive_expression_personalization',
                        'trigger_type': trigger_type,
                        'user_relationship': relationship,
                        'is_ai_personalization': True,
                        'original_content': content,
                        'user_name': user_name  # 🔥 也在context_data中传递用户名
                    }
                )
                
                if result and result.get('success'):
                    personalized_content = result.get('result', '').strip()
                    if personalized_content and len(personalized_content) > 10:
                        self.logger.info(f"🎨 AI个性化表达成功: {user_name} - {personalized_content[:30]}...")
                        return personalized_content
                    
            except Exception as chat_e:
                self.logger.debug(f"💬 ChatSkill个性化失败: {chat_e}")
            
            # 🔥 如果AI个性化失败，使用智能模板系统（改进版）
            return self._generate_intelligent_personalized_template(content, user_info, trigger_type)
                
        except Exception as e:
            self.logger.error(f"💬 个性化表达内容失败: {e}")
            return content
    
    def _generate_intelligent_personalized_template(self, content: str, user_info: Dict[str, Any], trigger_type: str) -> str:
        """生成智能个性化模板（AI失败时的备选方案）"""
        try:
            user_name = user_info.get("name", "朋友")
            relationship = user_info.get("relationship", "friend")
            interaction_count = user_info.get("interaction_count", 0)
            
            # 🔥 根据关系和互动次数智能调整
            if relationship in ["best_friend", "close_friend"] and interaction_count >= 10:
                # 亲密朋友：更自然的语气
                if trigger_type == "emotion_based":
                    return f"{user_name}，{content} 你最近怎么样呀？"
                elif trigger_type == "time_based":
                    return f"{user_name}，{content}"
                elif trigger_type == "event_based":
                    return f"{user_name}，{content} 想听听你的想法～"
                else:
                    return f"{user_name}，{content}"
                    
            elif relationship == "friend" and interaction_count >= 5:
                # 普通朋友：友好但不过分亲密
                if trigger_type == "emotion_based":
                    return f"{content} 希望也能给{user_name}带来正能量～"
                elif trigger_type == "time_based":
                    return f"{user_name}，{content}"
                elif trigger_type == "event_based":
                    return f"{user_name}，{content} 你觉得呢？"
                else:
                    return f"{user_name}，{content}"
                    
            elif interaction_count >= 3:
                # 有一定互动：保持友好
                if trigger_type == "emotion_based":
                    return f"{content} 希望{user_name}也有好心情～"
                elif trigger_type == "time_based":
                    return f"{user_name}，{content}"
                else:
                    return f"{user_name}，{content}"
                    
            else:
                # 互动较少：保持礼貌距离
                return content
                
        except Exception as e:
            self.logger.error(f"💬 智能模板生成失败: {e}")
            return content

    def _get_daily_expression_count(self) -> int:
        """获取今日表达次数"""
        try:
            from datetime import datetime, date
            today = date.today()
            
            if not hasattr(self, 'simple_expression_history'):
                return 0
            
            count = 0
            for record in self.simple_expression_history:
                try:
                    record_date = datetime.fromisoformat(record['timestamp']).date()
                    if record_date == today:
                        count += 1
                except:
                    continue
            
            return count
        except Exception as e:
            self.logger.error(f"💬 获取今日表达次数失败: {e}")
            return 0
    
    def _is_too_soon_to_express(self) -> bool:
        """检查是否距离上次表达时间太短"""
        try:
            from datetime import datetime, timedelta
            
            if not hasattr(self, 'simple_expression_history') or not self.simple_expression_history:
                return False
            
            # 获取最后一次表达时间
            last_record = self.simple_expression_history[-1]
            last_time = datetime.fromisoformat(last_record['timestamp'])
            
            # 最小间隔30分钟
            min_interval = self.expression_config.get("min_expression_interval", 1800)  # 30分钟
            time_diff = (datetime.now() - last_time).total_seconds()
            
            return time_diff < min_interval
            
        except Exception as e:
            self.logger.error(f"💬 检查表达间隔失败: {e}")
            return False
    
    # 🔥 老王清理：删除弃用的旧版本触发检查函数
    
    def _sync_execute_morning_greeting(self):
        """同步执行早安问候 - 使用AI智慧响应"""
        try:
            # 获取好友列表
            users_to_greet = self._get_users_for_greeting()
            
            # 🔥 老王修复：检查是否有有效联系人
            if not users_to_greet:
                self.logger.info(f"💬 🌅 没有有效联系人，跳过早安问候")
                return
            
            for user_info in users_to_greet[:3]:  # 限制为3个好友
                # 🔥 老王修复：使用AI智慧响应而不是固定模板
                self._sync_execute_ai_expression("morning_greeting", ExpressionType.GREETING, user_info)
                
            # 记录已发送
            self._mark_expression_sent(ExpressionType.GREETING, "morning")
            
        except Exception as e:
            self.logger.error(f"💬 同步执行早安问候失败: {e}")
    
    def _sync_execute_ai_expression(self, greeting_type: str, expression_type: ExpressionType, user_info: Dict = None):
        """同步执行AI智慧表达 - 通过chat skill统一响应"""
        try:
            # 🔥 老王修复：统一使用chat skill的AI智慧响应能力，而不是固定模板
            from cognitive_modules.skills.chat_skill import ChatSkill
            
            # 获取或创建chat skill实例
            chat_skill = ChatSkill()
            
            # 🔥 构建AI智慧表达的提示词
            ai_prompt = self._build_ai_expression_prompt(greeting_type, user_info)
            
            # 🔥 老王修复：获取用户名并传递给chat skill
            user_name = None
            if user_info and user_info.get('user_id'):
                user_name = self._get_real_user_name(user_info.get('user_id'))
            
            # 🔥 通过chat skill执行AI智慧响应
            result = chat_skill.execute(
                input_text=ai_prompt,
                user_id=user_info.get('user_id') if user_info else 'system',
                session_id=f"proactive_{greeting_type}_{datetime.now().strftime('%Y%m%d')}",
                user_name=user_name,  # 🔥 关键修复：传递用户名
                context_data={
                    'type': 'proactive_expression',
                    'greeting_type': greeting_type,
                    'is_yanran_initiative': True,
                    'use_ai_wisdom': True,
                    'expression_type': expression_type.value,
                    'user_name': user_name  # 🔥 也在context_data中传递用户名
                }
            )
            
            if result and result.get('success'):
                ai_content = result.get('result', '').strip()
                if ai_content and len(ai_content) > 5:
                    # 🔥 使用AI生成的智慧内容
                    success = self._sync_send_wechat_message(ai_content, user_info)
                    
                    if success:
                        # 记录表达历史
                        expression_result = ExpressionResult(
                            expression_id=f"ai_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                            expression_type=expression_type,
                            content=ai_content,
                            delivery_method="wechat_ai",
                            target_audience=user_info.get("user_id") if user_info else "general",
                            success=True,
                            timestamp=datetime.now()
                        )
                        self._record_expression_result(expression_result)
                        self.logger.success(f"💬 AI智慧表达成功: {ai_content[:30]}...")
                        return
            
            # 🔥 AI失败时的兜底处理
            self.logger.warning(f"💬 AI智慧表达失败，使用兜底机制: {greeting_type}")
            self._sync_execute_simple_expression(self._get_fallback_content(greeting_type), expression_type, user_info)
            
        except Exception as e:
            self.logger.error(f"💬 同步执行AI表达失败: {e}")
            # 兜底处理
            self._sync_execute_simple_expression(self._get_fallback_content(greeting_type), expression_type, user_info)
    
    def _build_ai_expression_prompt(self, greeting_type: str, user_info: Dict = None) -> str:
        """构建AI智慧表达的提示词 - 增强多维度内容生成"""
        try:
            # 🔥 获取多维度内容源
            content_sources = self._gather_expression_content_sources()
            
            # 🔥 构建多维度表达提示词
            ai_prompt = f"""

## 多维度内容源（真实世界数据）
{self._format_content_sources(content_sources)}

主动跟好友打招呼
"""
            if greeting_type == "morning_greeting":

                user_name = user_info.get('user_name', '')

                ai_prompt = f"""
                你主动给好友 {user_name} 发一条早安问候，每天都不重样，这样才能一直保持新鲜感,变花样的狠狠的撩拨起来
                """

            return ai_prompt
            
        except Exception as e:
            self.logger.error(f"🎨 构建AI表达提示词失败: {e}")
            return f"作为林嫣然，进行{greeting_type}类型的主动表达。请生成温暖真诚的内容。"

    def _gather_expression_content_sources(self) -> Dict[str, Any]:
        """收集多维度表达内容源 - 🔥 增强实时性和动态性"""
        try:
            content_sources = {
                "significant_events": [],
                "world_perceptions": [],
                "personal_thoughts": [],
                "life_insights": [],
                "current_activities": [],
                "emotional_states": [],
                "time_context": {},
                # 🔥 新增：实时数据源
                "realtime_news": [],
                "hot_topics": [],
                "market_insights": [],
                "trending_events": []
            }
            
            # 🔥 0. 获取好友偏好数据（用于个性化筛选）
            user_preferences = self._get_aggregated_user_preferences()
            
            # 🔥 1. 获取实时新闻数据（动态获取，个性化筛选）
            try:
                content_sources["realtime_news"] = self._get_realtime_news_for_expression(user_preferences)
            except Exception as e:
                self.logger.debug(f"获取实时新闻失败: {e}")
            
            # 🔥 2. 获取热搜话题数据（动态获取，个性化筛选）
            try:
                content_sources["hot_topics"] = self._get_hot_topics_for_expression(user_preferences)
            except Exception as e:
                self.logger.debug(f"获取热搜话题失败: {e}")
            
            # 🔥 3. 获取重要事件（从世界感知器官的真实感知数据）
            try:
                from cognitive_modules.organs.world_perception_organ import get_instance as get_world_perception
                world_perception = get_world_perception()
                if world_perception:
                    # 🔥 获取真实的重要事件数据，而不是模板内容
                    import asyncio
                    recent_events = asyncio.run(world_perception.get_significant_events(limit=5))
                    
                    # 🔥 直接使用真实事件数据，不生成模板观点
                    real_significant_events = []
                    for event in recent_events:
                        # 保留原始事件信息，不添加模板化的"林嫣然观点"
                        real_event = {
                            'title': event.get('title', ''),
                            'platform': event.get('platform', ''),
                            'event_type': event.get('event_type', ''),
                            'significance_score': event.get('significance_score', 0),
                            'desc': event.get('desc', ''),
                            'heat': event.get('heat', 0),
                            'rank': event.get('rank'),
                            'url': event.get('url', ''),
                            'timestamp': event.get('timestamp', ''),
                            'real_world_event': True,  # 标记为真实世界事件
                            'source': 'world_perception_organ'
                        }
                        real_significant_events.append(real_event)
                    
                    content_sources["significant_events"] = real_significant_events
                    self.logger.info(f"🔥 获取到 {len(real_significant_events)} 个真实重要事件")
            except Exception as e:
                self.logger.debug(f"获取重要事件失败: {e}")
            
            # 🔥 4. 获取思考内容（从自主思考模块）
            try:
                from cognitive_modules.cognition.autonomous_thinking import get_instance as get_autonomous_thinking
                thinking_module = get_autonomous_thinking()
                if thinking_module:
                    recent_thoughts = thinking_module.get_recent_thoughts(limit=2)
                    content_sources["personal_thoughts"] = recent_thoughts
            except Exception as e:
                self.logger.debug(f"获取思考内容失败: {e}")
            
            # 🔥 5. 获取当前活动状态
            try:
                from cognitive_modules.perception.activity_perception import get_instance as get_activity_perception
                activity_perception = get_activity_perception()
                if activity_perception:
                    current_activity = activity_perception.get_current_activity()
                    content_sources["current_activities"] = [current_activity] if current_activity else []
            except Exception as e:
                self.logger.debug(f"获取活动状态失败: {e}")
            
            # 🔥 6. 获取情感状态
            try:
                from cognitive_modules.emotion.autonomous_emotion_system import get_instance as get_emotion_system
                emotion_system = get_emotion_system()
                if emotion_system:
                    current_emotion = emotion_system.get_current_emotional_state()
                    content_sources["emotional_states"] = [current_emotion] if current_emotion else []
            except Exception as e:
                self.logger.debug(f"获取情感状态失败: {e}")
            
            # 🔥 7. 生成生活感悟（基于时间和季节）
            content_sources["life_insights"] = self._generate_life_insights()
            
            # 🔥 8. 时间上下文
            now = datetime.now()
            content_sources["time_context"] = {
                "hour": now.hour,
                "weekday": now.weekday(),
                "season": self._get_current_season(),
                "time_of_day": self._get_time_of_day(now.hour)
            }
            
            return content_sources
            
        except Exception as e:
            self.logger.error(f"收集表达内容源失败: {e}")
            return {"error": str(e)}

    def _get_realtime_news_for_expression(self, user_preferences: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取实时新闻数据用于表达 - 🔥 基于好友画像个性化筛选"""
        try:
            # 🔥 使用实时数据收集器获取新闻
            from perception.real_time_data_collector import RealTimeDataCollector
            collector = RealTimeDataCollector()
            
            # 获取财经新闻
            news_result = collector.collect_financial_news()
            if news_result and news_result.get('success'):
                news_data = news_result.get('data', [])
                
                # 🔥 基于好友画像筛选新闻
                filtered_news = []
                for news in news_data[:10]:  # 扩大候选范围
                    if isinstance(news, dict):
                        importance = news.get('importance', news.get('importance_score', 0))
                        
                        # 🔥 计算好友兴趣匹配度
                        user_interest_score = self._calculate_user_interest_score(news, user_preferences)
                        
                        # 🔥 综合评分：基础重要性 + 好友兴趣匹配度
                        combined_score = importance * 0.6 + user_interest_score * 0.4
                        
                        if combined_score >= 0.4:  # 降低阈值，但考虑好友兴趣
                            filtered_news.append({
                                'title': news.get('title', ''),
                                'content': news.get('content', '')[:200],
                                'source': news.get('source', ''),
                                'category': news.get('category', ''),
                                'importance': importance,
                                'user_interest_score': user_interest_score,
                                'combined_score': combined_score,
                                'real_news_data': True  # 🔥 标记为真实新闻，不生成虚假观点
                            })
                
                # 🔥 按综合评分排序，优先推送好友感兴趣的内容
                filtered_news.sort(key=lambda x: x.get('combined_score', 0), reverse=True)
                selected_news = filtered_news[:5]
                
                self.logger.info(f"🔥 获取到 {len(selected_news)} 条个性化实时新闻")
                return selected_news
            
        except Exception as e:
            self.logger.debug(f"获取实时新闻失败: {e}")
        
        return []

    def _get_hot_topics_for_expression(self, user_preferences: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取热搜话题数据用于表达 - 🔥 基于好友画像个性化筛选"""
        try:
            # 🔥 使用实时数据收集器获取热搜数据
            from perception.real_time_data_collector import RealTimeDataCollector
            collector = RealTimeDataCollector()
            
            # 获取热搜话题
            hot_topics_result = collector.collect_hot_topics()
            if hot_topics_result and hot_topics_result.get('success'):
                platforms_data = hot_topics_result.get('platforms', {})
                
                # 🔥 合并所有平台的热搜话题并个性化筛选
                all_topics = []
                for platform_name, platform_data in platforms_data.items():
                    if isinstance(platform_data, dict):
                        topics = platform_data.get('topics', [])
                        for topic in topics[:5]:  # 每个平台扩大候选范围
                            if isinstance(topic, dict):
                                # 🔥 计算好友兴趣匹配度
                                user_interest_score = self._calculate_user_interest_score(topic, user_preferences)
                                importance_score = topic.get('importance_score', 0)
                                
                                # 🔥 综合评分：基础重要性 + 好友兴趣 + 热度
                                hot_score = topic.get('hot_score', 0)
                                normalized_hot_score = min(hot_score / 100000, 1.0)  # 归一化热度分数
                                combined_score = (importance_score * 0.4 + 
                                                user_interest_score * 0.4 + 
                                                normalized_hot_score * 0.2)
                                
                                if combined_score >= 0.3:  # 个性化阈值
                                    all_topics.append({
                                        'title': topic.get('title', ''),
                                        'content': topic.get('content', '')[:150],
                                        'platform': platform_name,
                                        'hot_score': hot_score,
                                        'category': topic.get('category', ''),
                                        'importance_score': importance_score,
                                        'user_interest_score': user_interest_score,
                                        'combined_score': combined_score,
                                        'real_topic_data': True  # 🔥 标记为真实话题，不生成虚假观点
                                    })
                
                # 🔥 按综合评分排序，优先推送好友感兴趣的话题
                all_topics.sort(key=lambda x: x.get('combined_score', 0), reverse=True)
                selected_topics = all_topics[:5]
                
                self.logger.info(f"🔥 获取到 {len(selected_topics)} 个个性化热门话题")
                return selected_topics
            
        except Exception as e:
            self.logger.debug(f"获取热搜话题失败: {e}")
        
        return []

    # 🔥 已删除虚假观点生成方法 - 维护数字生命的真实性和信任
    # _generate_yanran_news_perspective 和 _generate_yanran_topic_perspective 
    # 这些方法会生成虚假的"林嫣然观点"，违背了数字生命的真实性原则
    # 真实的观点应该由AI基于真实数据进行表达，而不是预设的模板话术

    def _calculate_user_interest_score(self, content: Dict[str, Any], user_preferences: Dict[str, Any] = None) -> float:
        """计算好友兴趣匹配度评分"""
        try:
            if not user_preferences:
                user_preferences = self._get_aggregated_user_preferences()
            
            score = 0.0
            title = content.get('title', '').lower()
            content_text = content.get('content', '').lower()
            category = content.get('category', '').lower()
            
            # 🔥 基于好友兴趣关键词匹配
            user_interests = user_preferences.get('interests', [])
            for interest in user_interests:
                if interest.lower() in title or interest.lower() in content_text:
                    score += 0.3
            
            # 🔥 基于好友偏好的内容类别
            preferred_categories = user_preferences.get('preferred_categories', {})
            for cat, weight in preferred_categories.items():
                if cat.lower() in category:
                    score += weight * 0.4
            
            # 🔥 基于好友历史互动话题
            historical_topics = user_preferences.get('historical_topics', [])
            for topic in historical_topics:
                if topic.lower() in title or topic.lower() in content_text:
                    score += 0.2
            
            # 🔥 基于好友活跃时间偏好（时间相关内容）
            current_hour = datetime.now().hour
            time_preferences = user_preferences.get('active_hours', [])
            if current_hour in time_preferences:
                score += 0.1
            
            return min(score, 1.0)  # 限制在0-1范围内
            
        except Exception as e:
            self.logger.debug(f"计算好友兴趣评分失败: {e}")
            return 0.5  # 默认中等兴趣

    def _get_aggregated_user_preferences(self) -> Dict[str, Any]:
        """获取聚合的好友偏好数据"""
        try:
            from connectors.database.mysql_connector import get_instance as get_mysql_connector
            mysql_connector = get_mysql_connector()
            
            # 🔥 查询好友兴趣标签和偏好
            query = """
            SELECT 
                u.id as user_id,
                u.name,
                GROUP_CONCAT(DISTINCT m.content SEPARATOR ' ') as all_messages,
                COUNT(m.id) as message_count,
                AVG(HOUR(m.timestamp)) as avg_active_hour
            FROM users u
            LEFT JOIN messages m ON u.id = m.user_id 
            WHERE m.role = 'user' 
            AND m.timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY u.id, u.name
            HAVING message_count >= 5
            ORDER BY message_count DESC
            LIMIT 10
            """
            
            success, results, error = mysql_connector.execute_query(query)
            
            if success and results:
                # 🔥 分析好友群体的共同兴趣
                all_interests = []
                preferred_categories = {}
                active_hours = []
                
                for row in results:
                    messages = row.get('all_messages', '') or ''
                    
                    # 提取兴趣关键词
                    interest_keywords = self._extract_interest_keywords(messages)
                    all_interests.extend(interest_keywords)
                    
                    # 统计偏好类别
                    categories = self._categorize_user_interests(messages)
                    for cat, weight in categories.items():
                        preferred_categories[cat] = preferred_categories.get(cat, 0) + weight
                    
                    # 统计活跃时间
                    avg_hour = row.get('avg_active_hour')
                    if avg_hour is not None:
                        active_hours.append(int(avg_hour))
                
                # 🔥 构建聚合偏好
                from collections import Counter
                interest_counter = Counter(all_interests)
                top_interests = [item[0] for item in interest_counter.most_common(10)]
                
                # 归一化类别权重
                total_weight = sum(preferred_categories.values())
                if total_weight > 0:
                    for cat in preferred_categories:
                        preferred_categories[cat] /= total_weight
                
                return {
                    'interests': top_interests,
                    'preferred_categories': preferred_categories,
                    'active_hours': list(set(active_hours)),
                    'historical_topics': top_interests[:5]  # 历史话题就是高频兴趣
                }
            
        except Exception as e:
            self.logger.debug(f"获取好友偏好失败: {e}")
        
        # 🔥 默认偏好（基于林嫣然的定位）
        return {
            'interests': ['财经', '科技', '文化', '生活', '健康', '教育'],
            'preferred_categories': {
                '财经': 0.3, '科技': 0.25, '文化': 0.2, '生活': 0.15, '健康': 0.1
            },
            'active_hours': [9, 10, 11, 14, 15, 16, 19, 20, 21],
            'historical_topics': ['投资', '理财', '人工智能', '生活感悟', '健康']
        }

    def _extract_interest_keywords(self, text: str) -> List[str]:
        """从文本中提取兴趣关键词"""
        try:
            keywords = []
            text_lower = text.lower()
            
            # 🔥 预定义的兴趣关键词库
            keyword_categories = {
                '财经': ['股票', '投资', '理财', '基金', '财经', '经济', '市场', '金融'],
                '科技': ['科技', '技术', 'AI', '人工智能', '互联网', '数字化', '创新'],
                '文化': ['文化', '艺术', '音乐', '文学', '传统', '创作', '美学'],
                '生活': ['生活', '健康', '美食', '旅游', '运动', '休闲', '家庭'],
                '教育': ['教育', '学习', '成长', '知识', '技能', '培训', '发展'],
                '社会': ['社会', '民生', '政策', '法律', '公益', '环保', '公共']
            }
            
            for category, words in keyword_categories.items():
                for word in words:
                    if word in text_lower:
                        keywords.append(category)
                        break  # 每个类别只计算一次
            
            return keywords
            
        except Exception as e:
            self.logger.debug(f"提取兴趣关键词失败: {e}")
            return []

    def _categorize_user_interests(self, text: str) -> Dict[str, float]:
        """对好友兴趣进行分类和权重计算"""
        try:
            categories = {}
            text_lower = text.lower()
            
            # 🔥 根据关键词出现频率计算权重
            category_keywords = {
                '财经': ['股票', '投资', '理财', '基金', '财经', '经济', '市场', '金融'],
                '科技': ['科技', '技术', 'AI', '人工智能', '互联网', '数字化'],
                '文化': ['文化', '艺术', '音乐', '文学', '传统', '创作'],
                '生活': ['生活', '健康', '美食', '旅游', '运动', '休闲'],
                '教育': ['教育', '学习', '成长', '知识', '技能', '培训']
            }
            
            for category, keywords in category_keywords.items():
                count = sum(1 for keyword in keywords if keyword in text_lower)
                if count > 0:
                    categories[category] = count / len(keywords)  # 归一化权重
            
            return categories
            
        except Exception as e:
            self.logger.debug(f"分类好友兴趣失败: {e}")
            return {}

    def _format_content_sources(self, content_sources: Dict[str, Any]) -> str:
        """格式化内容源为提示词 - 🔥 增强实时数据展示"""
        try:
            formatted_parts = []
            
            # 🔥 实时新闻（优先级最高，使用真实数据）
            if content_sources.get("realtime_news"):
                news_text = []
                for news in content_sources["realtime_news"][:2]:
                    if isinstance(news, dict):
                        title = news.get("title", "")
                        source = news.get("source", "")
                        content = news.get("content", "")
                        importance = news.get("importance", 0)
                        
                        # 🔥 使用真实新闻信息，不生成模板观点
                        news_info = f"- 【{source}】{title}"
                        if content and len(content) > 20:
                            news_info += f": {content[:60]}..."
                        if importance > 0.6:
                            news_info += f" (重要)"
                        
                        news_text.append(news_info)
                if news_text:
                    formatted_parts.append(f"**当前关注的实时新闻**:\n" + "\n".join(news_text))
            
            # 🔥 热搜话题（使用真实数据）
            if content_sources.get("hot_topics"):
                topics_text = []
                for topic in content_sources["hot_topics"][:2]:
                    if isinstance(topic, dict):
                        title = topic.get("title", "")
                        platform = topic.get("platform", "")
                        content = topic.get("content", "")
                        hot_score = topic.get("hot_score", 0)
                        
                        # 🔥 使用真实话题信息，不生成模板观点
                        topic_info = f"- 【{platform}】{title}"
                        if content and len(content) > 20:
                            topic_info += f": {content[:60]}..."
                        if hot_score > 50000:  # 热度很高
                            topic_info += f" (热度: {hot_score//10000}万)"
                        
                        topics_text.append(topic_info)
                if topics_text:
                    formatted_parts.append(f"**当前关注的热门话题**:\n" + "\n".join(topics_text))
            
            # 🔥 真实重要事件（不生成模板观点）
            if content_sources.get("significant_events"):
                events_text = []
                for event in content_sources["significant_events"][:3]:
                    if isinstance(event, dict) and event.get('real_world_event'):
                        title = event.get("title", "")
                        platform = event.get("platform", "")
                        desc = event.get("desc", "")
                        significance = event.get("significance_score", 0)
                        
                        # 🔥 使用真实事件信息，不添加模板化观点
                        event_info = f"- 【{platform}】{title}"
                        if desc and len(desc) > 10:
                            event_info += f": {desc[:80]}..."
                        if significance > 0.5:
                            event_info += f" (重要性: {significance:.1f})"
                        
                        events_text.append(event_info)
                
                if events_text:
                    formatted_parts.append(f"**当前关注的真实事件**:\n" + "\n".join(events_text))
            
            # 🔥 个人思考
            if content_sources.get("personal_thoughts"):
                thoughts_text = []
                for thought in content_sources["personal_thoughts"][:2]:
                    if isinstance(thought, dict):
                        topic = thought.get("topic", "")
                        content = thought.get("content", "")[:50]
                        thoughts_text.append(f"- {topic}: {content}...")
                if thoughts_text:
                    formatted_parts.append(f"**最近思考**:\n" + "\n".join(thoughts_text))
            
            # 🔥 当前活动
            if content_sources.get("current_activities"):
                activity = content_sources["current_activities"][0]
                if isinstance(activity, dict):
                    activity_name = activity.get("name", "")
                    activity_desc = activity.get("description", "")
                    formatted_parts.append(f"**当前活动**: {activity_name} - {activity_desc}")
            
            # 🔥 情感状态
            if content_sources.get("emotional_states"):
                emotion = content_sources["emotional_states"][0]
                if isinstance(emotion, dict):
                    emotion_type = emotion.get("type", "")
                    emotion_desc = emotion.get("description", "")
                    formatted_parts.append(f"**当前心情**: {emotion_type} - {emotion_desc}")
            
            # 🔥 生活感悟
            if content_sources.get("life_insights"):
                insights = content_sources["life_insights"]
                if insights:
                    formatted_parts.append(f"**生活感悟**: {insights[0]}")
            
            # 🔥 时间上下文
            time_ctx = content_sources.get("time_context", {})
            if time_ctx:
                time_desc = f"{time_ctx.get('time_of_day', '')}，{time_ctx.get('season', '')}季节"
                formatted_parts.append(f"**时间背景**: {time_desc}")
            
            return "\n\n".join(formatted_parts) if formatted_parts else "暂无特定内容源，请基于林嫣然的个性自由表达"
            
        except Exception as e:
            self.logger.error(f"格式化内容源失败: {e}")
            return "内容源格式化失败，请基于林嫣然的个性自由表达"

    def _generate_life_insights(self) -> List[str]:
        """生成生活感悟"""
        try:
            # 🔥 基于时间、季节、心情等生成生活感悟
            insights_pool = [
                "每个平凡的日子里，其实都藏着小小的美好",
                "成长就是在不断的选择中，学会为自己负责",
                "真正的温暖，往往来自于那些不经意的善意",
                "生活的节奏可以慢一点，但对美好的感知要敏锐一些",
                "每一次思考都是在为内心的世界增添色彩",
                "财经数据背后，其实是千万个普通人的生活故事",
                "世界很大，但每个人的小世界同样重要",
                "保持好奇心，是让生活保持新鲜感的秘诀",
                "真诚的表达，比完美的措辞更有力量",
                "在快节奏的生活中，偶尔停下来感受当下很重要"
            ]
            
            # 根据时间选择合适的感悟
            now = datetime.now()
            if 6 <= now.hour <= 9:
                morning_insights = [
                    "晨光中的思考总是格外清晰",
                    "新的一天，带着新的期待开始",
                    "早晨的空气里似乎都带着希望的味道"
                ]
                insights_pool.extend(morning_insights)
            elif 18 <= now.hour <= 22:
                evening_insights = [
                    "夜晚是思考和感悟的好时光",
                    "一天的忙碌后，静下来想想真正重要的事",
                    "夜色中的城市，每一盏灯都是一个故事"
                ]
                insights_pool.extend(evening_insights)
            
            # 随机选择1-2个感悟
            import random
            selected_insights = random.sample(insights_pool, min(2, len(insights_pool)))
            return selected_insights
            
        except Exception as e:
            self.logger.error(f"生成生活感悟失败: {e}")
            return ["生活中总有值得思考的美好瞬间"]

    def _get_current_season(self) -> str:
        """获取当前季节"""
        try:
            month = datetime.now().month
            if month in [12, 1, 2]:
                return "冬"
            elif month in [3, 4, 5]:
                return "春"
            elif month in [6, 7, 8]:
                return "夏"
            else:
                return "秋"
        except:
            return "未知"

    def _get_time_of_day(self, hour: int) -> str:
        """获取时间段描述"""
        try:
            if 5 <= hour <= 8:
                return "清晨"
            elif 9 <= hour <= 11:
                return "上午"
            elif 12 <= hour <= 14:
                return "中午"
            elif 15 <= hour <= 17:
                return "下午"
            elif 18 <= hour <= 20:
                return "傍晚"
            elif 21 <= hour <= 23:
                return "夜晚"
            else:
                return "深夜"
        except:
            return "未知时段"

    def _get_fallback_content(self, greeting_type: str) -> str:
        """获取兜底内容"""
        fallback_contents = {
            "morning_greeting": "早安！新的一天开始了，希望你精神饱满～",
            "noon_greeting": "中午好！记得要好好吃饭，补充能量哦～",
            "afternoon_greeting": "下午好！工作之余也要记得适当休息～",
            "evening_greeting": "晚安，愿你今夜好梦，明天更美好～"
        }
        return fallback_contents.get(greeting_type, "你好！想和你聊聊天～")
    
    def _sync_execute_simple_expression(self, content: str, expression_type: ExpressionType, user_info: Dict = None):
        """同步执行简单表达"""
        try:
            # 🔥 直接通过WeChat发送，不依赖异步循环
            success = self._sync_send_wechat_message(content, user_info)
            
            if success:
                # 记录表达历史
                expression_result = ExpressionResult(
                    expression_id=f"sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    expression_type=expression_type,
                    content=content,
                    delivery_method="wechat",
                    target_audience=user_info.get("user_id") if user_info else "general",
                    success=True,
                    timestamp=datetime.now()
                )
                self._record_expression_result(expression_result)
                self.logger.success(f"💬 同步表达成功: {content[:30]}...")
            
        except Exception as e:
            self.logger.error(f"💬 同步执行简单表达失败: {e}")
    
    def _sync_send_wechat_message(self, content: str, user_info: Dict = None) -> bool:
        """
        🔥 P0修复：同步发送WeChat消息，使用正确的推送服务
        注意：这个方法与财经早报使用相同的推送逻辑
        """
        try:
            # 🔥 修复：使用WeChat统一推送服务，与财经早报保持一致
            from services.wechat_unified_push_service import get_wechat_unified_push_service
            wechat_push_service = get_wechat_unified_push_service()

            if not wechat_push_service:
                self.logger.warning("💬 WeChat统一推送服务不可用")
                return False

            # 确定目标用户
            user_id = user_info.get("user_id") if user_info else "wxid_fknm4p9g74pn21"

            # 🔥 使用与财经早报相同的推送逻辑
            success = wechat_push_service.push_message_sync(
                message_type="morning_greeting",
                content=content,
                target_user_id=user_id,
                message_level="user",
                priority="normal",
                metadata={
                    "source": "proactive_expression_organ",
                    "greeting_type": "morning",
                    "disable_humanized_delay": True,
                    "immediate_send": True
                }
            )

            if success:
                self.logger.success(f"💬 早安问候发送成功: {user_id}")
            else:
                self.logger.warning(f"💬 早安问候发送失败: {user_id}")

            return success

        except Exception as e:
            self.logger.error(f"💬 同步发送WeChat消息失败: {e}")
            return False

    async def trigger_enhanced_sharing_expression(self, context_data: Dict[str, Any], 
                                                user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """触发增强版分享表达"""
        try:
            self.logger.info("🤝 触发增强版分享表达...")
            
            # 获取增强表达协调器
            from cognitive_modules.organs.enhanced_expression_types import get_enhanced_expression_coordinator
            coordinator = get_enhanced_expression_coordinator()
            
            # 处理分享表达
            result = await coordinator.process_enhanced_expression(
                expression_type="sharing",
                context_data=context_data,
                user_context=user_context
            )
            
            if result.success:
                self.logger.info(f"🤝 分享表达成功: {result.content[:50]}...")
                return {
                    "success": True,
                    "expression_id": result.expression_id,
                    "content": result.content,
                    "delivery_method": result.delivery_method,
                    "message": "分享表达已成功执行"
                }
            else:
                self.logger.warning(f"🤝 分享表达失败: {result.error_message}")
                return {
                    "success": False,
                    "reason": result.error_message,
                    "message": "分享表达执行失败"
                }
                
        except Exception as e:
            self.logger.error(f"🤝 触发分享表达失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "分享表达触发失败"
            }

    async def trigger_enhanced_world_perception_expression(self, perception_data: Dict[str, Any]) -> Dict[str, Any]:
        """触发增强版世界感知表达"""
        try:
            self.logger.info("🌍 触发增强版世界感知表达...")
            
            # 获取增强表达协调器
            from cognitive_modules.organs.enhanced_expression_types import get_enhanced_expression_coordinator
            coordinator = get_enhanced_expression_coordinator()
            
            # 构建上下文数据
            context_data = {
                "trigger_source": "world_perception_organ",
                "significant_events": perception_data.get('significant_events', []),
                "world_state": perception_data.get('world_state', {}),
                "proactive_thoughts": perception_data.get('proactive_thoughts', []),
                "confidence": 0.8,
                "priority": 8
            }
            
            # 处理世界感知表达
            result = await coordinator.process_enhanced_expression(
                expression_type="world_perception",
                context_data=context_data,
                user_context=None
            )
            
            if result.success:
                self.logger.debug(f"🌍 世界感知表达成功: {result.content[:50]}...")
                return {
                    "success": True,
                    "expression_id": result.expression_id,
                    "content": result.content,
                    "delivery_method": result.delivery_method,
                    "events_count": len(perception_data.get('significant_events', [])),
                    "message": "世界感知表达已成功执行"
                }
            else:
                self.logger.debug(f"🌍 世界感知表达跳过: {result.error_message}")
                return {
                    "success": False,
                    "reason": result.error_message,
                    "message": "世界感知表达未执行"
                }
                
        except Exception as e:
            self.logger.error(f"🌍 触发世界感知表达失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "世界感知表达触发失败"
            }

    async def trigger_enhanced_creative_expression(self, context_data: Dict[str, Any], 
                                                 user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """触发增强版创意表达"""
        try:
            self.logger.info("🎨 触发增强版创意表达...")
            
            # 获取增强表达协调器
            from cognitive_modules.organs.enhanced_expression_types import get_enhanced_expression_coordinator
            coordinator = get_enhanced_expression_coordinator()
            
            # 处理创意表达
            result = await coordinator.process_enhanced_expression(
                expression_type="creative",
                context_data=context_data,
                user_context=user_context
            )
            
            if result.success:
                self.logger.debug(f"🎨 创意表达成功: {result.content[:50]}...")
                return {
                    "success": True,
                    "expression_id": result.expression_id,
                    "content": result.content,
                    "delivery_method": result.delivery_method,
                    "creative_work": result.metadata.get('creative_work') if result.metadata else None,
                    "message": "创意表达已成功执行"
                }
            else:
                self.logger.debug(f"🎨 创意表达跳过: {result.error_message}")
                return {
                    "success": False,
                    "reason": result.error_message,
                    "message": "创意表达未执行"
                }
                
        except Exception as e:
            self.logger.error(f"🎨 触发创意表达失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "创意表达触发失败"
            }

    def _get_users_for_greeting(self) -> List[Dict[str, Any]]:
        """获取需要问候的好友列表 - 只从contacts.json获取真实联系人"""
        try:
            # 🔥 老王修复：确保只对contacts.json中的真实联系人进行主动表达
            from core.contacts_manager import get_contacts_manager
            contacts_manager = get_contacts_manager()
            
            users = []
            
            # 获取所有联系人
            all_contacts = contacts_manager.get_all_users()
            
            for contact in all_contacts:
                user_id = contact.get("wxid")
                nickname = contact.get("nickname", "")
                
                # 🔥 严格过滤：确保是有效的联系人
                if not user_id or not nickname:
                    self.logger.debug(f"💬 跳过无效联系人: user_id={user_id}, nickname={nickname}")
                    continue
                
                # 🔥 排除无效ID
                invalid_ids = ["system", "default_user", "anonymous", "unknown"]
                if user_id.lower() in invalid_ids or any(invalid in user_id.lower() for invalid in invalid_ids):
                    self.logger.debug(f"💬 跳过无效好友ID: {user_id}")
                    continue
                
                # 🔥 排除群聊好友
                if user_id.startswith('chatroom_') or '@chatroom' in user_id:
                    self.logger.debug(f"💬 跳过群聊好友: {user_id}")
                    continue
                
                # 🔥 排除测试好友（可选）
                if user_id.startswith('test_') and len(all_contacts) > 1:
                    self.logger.debug(f"💬 跳过测试好友: {user_id}")
                    continue
                
                user_info = {
                    "user_id": user_id,
                    "name": nickname,
                    "relationship": contact.get("relationship", "friend"),
                    "interaction_count": contact.get("interaction_count", 1),
                    "last_seen": contact.get("last_seen", 0)
                }
                users.append(user_info)
                
            # 🔥 如果没有有效联系人，记录警告但不使用默认好友
            if not users:
                self.logger.warning(f"💬 contacts.json中没有有效的联系人，主动表达将被跳过")
                self.logger.info(f"💬 当前contacts.json中有 {len(all_contacts)} 个联系人，但都被过滤掉了")
                return []
            
            # 🔥 按最后见面时间排序，优先选择最近活跃的好友
            users.sort(key=lambda u: u.get("last_seen", 0), reverse=True)
            
            self.logger.info(f"💬 从contacts.json获取到 {len(users)} 个有效联系人进行问候")
            for user in users[:3]:  # 只显示前3个
                self.logger.info(f"💬 联系人: {user['name']} ({user['user_id']})")
            
            return users
            
        except Exception as e:
            self.logger.error(f"💬 获取问候好友列表失败: {e}")
            # 🔥 失败时不返回默认好友，而是返回空列表
            return []
    
    def _record_expression_result(self, result: ExpressionResult):
        """记录表达结果"""
        try:
            # 初始化表达历史
            if not hasattr(self, 'expression_history'):
                self.expression_history = []
            
            # 转换为字典格式
            result_dict = {
                "expression_id": result.expression_id,
                "expression_type": result.expression_type.value if hasattr(result.expression_type, 'value') else str(result.expression_type),
                "content": result.content,
                "delivery_method": result.delivery_method,
                "target_audience": result.target_audience,
                "success": result.success,
                "timestamp": result.timestamp.isoformat() if hasattr(result.timestamp, 'isoformat') else str(result.timestamp),
                "user_feedback": result.user_feedback,
                "emotional_impact": result.emotional_impact
            }
            
            # 添加到历史记录
            self.expression_history.append(result_dict)
            
            # 保持历史记录在合理范围内（最多1000条）
            if len(self.expression_history) > 1000:
                self.expression_history = self.expression_history[-1000:]
            
            # 同时记录到简单表达历史
            if not hasattr(self, 'simple_expression_history'):
                self.simple_expression_history = []
            
            simple_record = {
                "expression_id": result.expression_id,
                "type": result.expression_type.value if hasattr(result.expression_type, 'value') else str(result.expression_type),
                "content": result.content[:100],  # 只保存前100个字符
                "success": result.success,
                "timestamp": result.timestamp.isoformat() if hasattr(result.timestamp, 'isoformat') else str(result.timestamp)
            }
            
            self.simple_expression_history.append(simple_record)
            
            # 保持简单历史记录在合理范围内（最多500条）
            if len(self.simple_expression_history) > 500:
                self.simple_expression_history = self.simple_expression_history[-500:]
            
            self.logger.debug(f"💬 表达结果已记录: {result.expression_id}")
            
        except Exception as e:
            self.logger.error(f"💬 记录表达结果失败: {e}")

    def _analyze_expression_effectiveness(self):
        """分析表达效果 - 🔥 香草修复：基于历史数据分析表达效果"""
        try:
            if not hasattr(self, 'expression_history') or len(self.expression_history) < 10:
                return

            # 分析最近的表达记录
            recent_expressions = self.expression_history[-50:]  # 最近50次表达

            # 按表达类型分析效果
            effectiveness_by_type = {}
            for expr in recent_expressions:
                expr_type = expr.get("expression_type", "unknown")
                success = expr.get("success", False)

                if expr_type not in effectiveness_by_type:
                    effectiveness_by_type[expr_type] = {"total": 0, "success": 0}

                effectiveness_by_type[expr_type]["total"] += 1
                if success:
                    effectiveness_by_type[expr_type]["success"] += 1

            # 计算成功率并更新策略
            for expr_type, stats in effectiveness_by_type.items():
                success_rate = stats["success"] / stats["total"] if stats["total"] > 0 else 0.0

                # 基于成功率调整表达频率
                if success_rate > 0.8:
                    # 高成功率，可以增加频率
                    self._adjust_expression_frequency(expr_type, 1.1)
                elif success_rate < 0.3:
                    # 低成功率，减少频率
                    self._adjust_expression_frequency(expr_type, 0.8)

            self.logger.debug(f"💬 分析了{len(recent_expressions)}条表达记录")

        except Exception as e:
            self.logger.error(f"分析表达效果异常: {e}")

    def _learn_user_feedback_patterns(self):
        """学习用户反馈模式 - 🔥 香草修复：学习用户偏好"""
        try:
            if not hasattr(self, 'expression_history') or len(self.expression_history) < 20:
                return

            # 分析用户反馈模式
            feedback_patterns = {}
            recent_expressions = self.expression_history[-100:]

            for expr in recent_expressions:
                user_id = expr.get("target_audience", {}).get("user_id")
                if not user_id:
                    continue

                if user_id not in feedback_patterns:
                    feedback_patterns[user_id] = {
                        "preferred_types": Counter(),
                        "preferred_times": [],
                        "response_rate": 0.0,
                        "total_expressions": 0,
                        "successful_expressions": 0
                    }

                pattern = feedback_patterns[user_id]
                pattern["total_expressions"] += 1

                if expr.get("success", False):
                    pattern["successful_expressions"] += 1
                    pattern["preferred_types"][expr.get("expression_type", "unknown")] += 1

                    # 记录成功的表达时间
                    timestamp = expr.get("timestamp", 0)
                    if timestamp:
                        hour = datetime.fromtimestamp(timestamp).hour
                        pattern["preferred_times"].append(hour)

            # 更新用户偏好设置
            for user_id, pattern in feedback_patterns.items():
                if pattern["total_expressions"] > 5:
                    response_rate = pattern["successful_expressions"] / pattern["total_expressions"]
                    pattern["response_rate"] = response_rate

                    # 保存学习到的偏好
                    self._save_user_preferences(user_id, pattern)

            self.logger.debug(f"💬 学习了{len(feedback_patterns)}个用户的反馈模式")

        except Exception as e:
            self.logger.error(f"学习用户反馈模式异常: {e}")

    def _optimize_expression_timing(self):
        """优化表达时机 - 🔥 香草修复：基于成功率优化时机"""
        try:
            if not hasattr(self, 'expression_history') or len(self.expression_history) < 30:
                return

            # 分析不同时间段的表达成功率
            hourly_success = {}
            recent_expressions = self.expression_history[-200:]

            for expr in recent_expressions:
                timestamp = expr.get("timestamp", 0)
                if not timestamp:
                    continue

                hour = datetime.fromtimestamp(timestamp).hour
                success = expr.get("success", False)

                if hour not in hourly_success:
                    hourly_success[hour] = {"total": 0, "success": 0}

                hourly_success[hour]["total"] += 1
                if success:
                    hourly_success[hour]["success"] += 1

            # 找出最佳表达时间段
            best_hours = []
            for hour, stats in hourly_success.items():
                if stats["total"] >= 5:  # 至少有5次表达记录
                    success_rate = stats["success"] / stats["total"]
                    if success_rate > 0.6:  # 成功率超过60%
                        best_hours.append((hour, success_rate))

            # 按成功率排序
            best_hours.sort(key=lambda x: x[1], reverse=True)

            # 更新最佳表达时间配置
            if best_hours:
                optimal_hours = [hour for hour, _ in best_hours[:5]]  # 取前5个最佳时间
                self._update_optimal_expression_hours(optimal_hours)

            self.logger.debug(f"💬 优化表达时机，发现{len(best_hours)}个高效时间段")

        except Exception as e:
            self.logger.error(f"优化表达时机异常: {e}")

    def _update_expression_strategies(self):
        """更新表达策略 - 🔥 香草修复：基于学习结果更新策略"""
        try:
            # 基于分析结果更新表达策略
            if hasattr(self, 'learned_preferences'):
                # 更新表达配置
                updated_config = self.expression_config.copy()

                # 应用学习到的偏好
                for user_id, preferences in self.learned_preferences.items():
                    if preferences.get("response_rate", 0) > 0.7:
                        # 高响应率用户，可以增加表达频率
                        pass  # 具体实现根据需要调整

                # 保存更新的配置
                self._save_updated_expression_config(updated_config)

            self.logger.debug("💬 表达策略更新完成")

        except Exception as e:
            self.logger.error(f"更新表达策略异常: {e}")

    def _adjust_expression_frequency(self, expr_type: str, multiplier: float):
        """调整表达频率"""
        try:
            # 这里可以根据需要实现具体的频率调整逻辑
            self.logger.debug(f"💬 调整{expr_type}表达频率: {multiplier}")
        except Exception as e:
            self.logger.error(f"调整表达频率异常: {e}")

    def _save_user_preferences(self, user_id: str, preferences: Dict[str, Any]):
        """保存用户偏好"""
        try:
            if not hasattr(self, 'learned_preferences'):
                self.learned_preferences = {}

            self.learned_preferences[user_id] = preferences

        except Exception as e:
            self.logger.error(f"保存用户偏好异常: {e}")

    def _update_optimal_expression_hours(self, optimal_hours: List[int]):
        """更新最佳表达时间"""
        try:
            if not hasattr(self, 'optimal_expression_hours'):
                self.optimal_expression_hours = []

            self.optimal_expression_hours = optimal_hours
            self.logger.debug(f"💬 更新最佳表达时间: {optimal_hours}")

        except Exception as e:
            self.logger.error(f"更新最佳表达时间异常: {e}")

    def _save_updated_expression_config(self, config: Dict[str, Any]):
        """保存更新的表达配置"""
        try:
            # 这里可以实现配置保存逻辑
            self.logger.debug("💬 保存更新的表达配置")
        except Exception as e:
            self.logger.error(f"保存表达配置异常: {e}")


def get_instance(ai_adapter=None) -> ProactiveExpressionOrgan:
    """获取主动表达器官实例"""
    from utilities.singleton_manager import get_or_create
    return get_or_create("proactive_expression_organ", lambda: ProactiveExpressionOrgan(ai_adapter))