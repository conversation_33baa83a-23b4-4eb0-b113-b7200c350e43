"""
林嫣然财富管理器官 - 智能投资理财和财富规划的器官
作为林嫣然的"理财大脑"，负责投资决策、风险管理和财富增长
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import pandas as pd

from .base_organ import LifeOrgan
from ..ai.yanran_decision_engine import YanranAIDecisionEngine
from utilities.singleton_manager import get, register

from utilities.unified_logger import get_unified_logger

class InvestmentType(Enum):
    """投资类型枚举"""
    STOCKS = "stocks"
    BONDS = "bonds"
    FUNDS = "funds"
    CRYPTO = "crypto"
    REAL_ESTATE = "real_estate"
    COMMODITIES = "commodities"


class RiskLevel(Enum):
    """风险等级枚举"""
    CONSERVATIVE = "conservative"  # 保守型
    MODERATE = "moderate"  # 稳健型
    AGGRESSIVE = "aggressive"  # 积极型
    SPECULATIVE = "speculative"  # 投机型


@dataclass
class InvestmentPosition:
    """投资持仓"""
    symbol: str
    investment_type: InvestmentType
    quantity: float
    cost_basis: float
    current_price: float
    purchase_date: datetime
    target_price: Optional[float]
    stop_loss: Optional[float]


@dataclass
class InvestmentDecision:
    """投资决策"""
    decision_id: str
    symbol: str
    action: str  # buy, sell, hold
    quantity: float
    reasoning: str
    confidence: float
    risk_assessment: str
    expected_return: float
    time_horizon: str


class WealthManagementOrgan(LifeOrgan):
    """林嫣然的财富管理器官"""
    
    def __init__(self, ai_adapter=None):
        super().__init__(
            organ_name="wealth_management_organ",
            organ_function="智能投资理财和财富规划",
            ai_adapter=ai_adapter
        )
        
        # 财富管理相关组件
        self.portfolio = {}  # 投资组合
        self.investment_history = []  # 投资历史
        self.market_data = {}  # 市场数据
        self.risk_profile = None  # 风险偏好
        
        # 财富管理配置
        self.wealth_config = self._load_wealth_config()
        self.investment_strategies = self._load_investment_strategies()
        
        # 性能统计
        self.wealth_stats = {
            "total_portfolio_value": 0.0,
            "total_return": 0.0,
            "return_rate": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "successful_trades": 0,
            "total_trades": 0,
            "current_positions": 0
        }
        
        self.logger = get_unified_logger(__name__)
        self.logger.info("💰 林嫣然财富管理器官初始化开始...")
        
        # 初始化财富管理系统
        self._init_wealth_management()
        
        self.logger.info("💰 林嫣然财富管理器官初始化完成")
    
    def _load_wealth_config(self) -> Dict[str, Any]:
        """加载财富管理配置"""
        try:
            config_path = os.path.join("config", "organs", "wealth_management_organ.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"💰 财富管理配置加载失败: {e}")
        
        # 默认配置
        return {
            "risk_tolerance": "moderate",
            "investment_horizon": "long_term",  # short_term, medium_term, long_term
            "target_return": 0.08,  # 8% 年化收益目标
            "max_position_size": 0.1,  # 单个投资最大占比10%
            "diversification_threshold": 5,  # 最少5个不同投资
            "rebalance_frequency": 30,  # 30天重新平衡一次
            "yanran_investment_style": {
                "preferred_sectors": ["technology", "healthcare", "renewable_energy"],
                "investment_philosophy": "value_growth",  # value, growth, value_growth
                "social_responsibility": True,  # ESG投资
                "learning_oriented": True  # 学习型投资
            }
        }
    
    def _load_investment_strategies(self) -> Dict[str, Any]:
        """加载投资策略"""
        return {
            "conservative": {
                "risk_level": RiskLevel.CONSERVATIVE,
                "target_allocation": {
                    "bonds": 0.6,
                    "stocks": 0.3,
                    "cash": 0.1
                },
                "max_single_position": 0.05,
                "stop_loss_threshold": 0.05
            },
            "moderate": {
                "risk_level": RiskLevel.MODERATE,
                "target_allocation": {
                    "stocks": 0.6,
                    "bonds": 0.3,
                    "alternatives": 0.1
                },
                "max_single_position": 0.1,
                "stop_loss_threshold": 0.08
            },
            "aggressive": {
                "risk_level": RiskLevel.AGGRESSIVE,
                "target_allocation": {
                    "stocks": 0.8,
                    "alternatives": 0.15,
                    "cash": 0.05
                },
                "max_single_position": 0.15,
                "stop_loss_threshold": 0.12
            }
        }
    
    def _init_wealth_management(self):
        """初始化财富管理系统"""
        try:
            # 获取金融数据技能
            financial_skill = get("financial_data_skill")
            if financial_skill:
                self.financial_data_skill = financial_skill
                self.logger.info("💰 金融数据技能连接成功")
            else:
                self.logger.warning("💰 金融数据技能未找到，将使用基础功能")
            
            # 初始化风险偏好
            self._init_risk_profile()
            
            # 加载投资组合数据
            self._load_portfolio_data()
            
        except Exception as e:
            self.logger.warning(f"💰 财富管理系统初始化部分失败: {e}")
    
    def _init_risk_profile(self):
        """初始化风险偏好配置"""
        risk_tolerance = self.wealth_config.get("risk_tolerance", "moderate")
        self.risk_profile = {
            "risk_level": risk_tolerance,
            "max_loss_tolerance": 0.2 if risk_tolerance == "aggressive" else 0.1,
            "preferred_sectors": self.wealth_config.get("yanran_investment_style", {}).get("preferred_sectors", []),
            "esg_priority": self.wealth_config.get("yanran_investment_style", {}).get("social_responsibility", True),
            "learning_focus": self.wealth_config.get("yanran_investment_style", {}).get("learning_oriented", True)
        }
        
        self.logger.info(f"💰 风险偏好配置完成: {risk_tolerance}")
    
    def _load_portfolio_data(self):
        """加载投资组合数据 - 生产环境版本，禁用模拟数据"""
        try:
            portfolio_path = os.path.join("data", "investment_portfolio.json")
            if os.path.exists(portfolio_path):
                with open(portfolio_path, 'r', encoding='utf-8') as f:
                    portfolio_data = json.load(f)
                    # 验证数据完整性
                    if not portfolio_data or not isinstance(portfolio_data, list):
                        self.logger.warning("💰 投资组合数据格式无效")
                        self.portfolio = {}
                        return
                    
                    # 加载真实投资数据
                    loaded_count = 0
                    for position_data in portfolio_data:
                        try:
                            # 验证必需字段
                            required_fields = ["symbol", "investment_type", "quantity", "cost_basis", "current_price", "purchase_date"]
                            if not all(field in position_data for field in required_fields):
                                self.logger.warning(f"💰 跳过不完整的投资记录: {position_data.get('symbol', 'unknown')}")
                                continue
                                
                            position = InvestmentPosition(
                                symbol=position_data["symbol"],
                                investment_type=InvestmentType(position_data["investment_type"]),
                                quantity=float(position_data["quantity"]),
                                cost_basis=float(position_data["cost_basis"]),
                                current_price=float(position_data["current_price"]),
                                purchase_date=datetime.fromisoformat(position_data["purchase_date"]),
                                target_price=float(position_data["target_price"]) if position_data.get("target_price") else None,
                                stop_loss=float(position_data["stop_loss"]) if position_data.get("stop_loss") else None
                            )
                            self.portfolio[position.symbol] = position
                            loaded_count += 1
                        except Exception as e:
                            self.logger.warning(f"💰 跳过无效投资记录 {position_data.get('symbol', 'unknown')}: {e}")
                            continue
                
                self.logger.success(f"💰 从真实数据成功加载了 {loaded_count} 个投资持仓")
            else:
                # 🔥 生产环境：不创建模拟数据，保持空投资组合
                self.logger.info("💰 投资组合数据文件不存在，初始化为空投资组合")
                self.logger.info("💰 请通过API或数据导入功能添加真实投资数据")
                self.portfolio = {}
                
        except Exception as e:
            self.logger.error(f"💰 投资组合数据加载失败: {e}")
            # 🔥 生产环境：出错时也不创建模拟数据
            self.logger.info("💰 投资组合初始化为空，等待真实投资数据")
            self.portfolio = {}
    
    def _create_sample_portfolio(self):
        """
        🚫 生产环境禁用：不再创建示例投资组合
        此方法已被禁用以确保生产环境只使用真实数据
        """
        self.logger.warning("💰 _create_sample_portfolio 方法在生产环境中已被禁用")
        return None
        """创建示例投资组合 - 生产环境已禁用"""
        # 🔥 生产环境修复：完全禁用模拟数据创建
        self.logger.warning("💰 ⚠️ 生产环境已禁用模拟数据创建")
        self.logger.info("💰 如需添加投资数据，请使用以下方式：")
        self.logger.info("💰 1. 通过API接口导入真实数据")
        self.logger.info("💰 2. 手动创建 data/investment_portfolio.json 文件")
        self.logger.info("💰 3. 使用数据同步功能从券商系统获取")
        return
    
    async def analyze_investment_opportunity(self, symbol: str, investment_type: InvestmentType) -> Dict[str, Any]:
        """分析投资机会"""
        try:
            self.logger.info(f"💰 分析投资机会: {symbol}")
            
            # AI决策：评估投资机会
            investment_analysis = await self._make_investment_decision(symbol, investment_type, "analyze")
            
            return {
                "symbol": symbol,
                "investment_type": investment_type.value,
                "analysis": investment_analysis,
                "recommendation": investment_analysis.get("recommendation", "hold"),
                "confidence": investment_analysis.get("confidence", 0.5),
                "risk_assessment": investment_analysis.get("risk_assessment", "medium"),
                "yanran_insight": self._generate_yanran_investment_insight(investment_analysis)
            }
            
        except Exception as e:
            self.logger.error(f"💰 投资机会分析失败: {e}")
            return {
                "symbol": symbol,
                "error": str(e),
                "recommendation": "hold"
            }
    
    async def _make_investment_decision(self, symbol: str, investment_type: InvestmentType, action: str) -> Dict[str, Any]:
        """AI决策：投资决策分析"""
        try:
            # 构建决策上下文
            decision_context = {
                "symbol": symbol,
                "investment_type": investment_type.value,
                "action": action,
                "current_portfolio": self._get_portfolio_summary(),
                "risk_profile": self.risk_profile,
                "market_conditions": self._get_market_conditions(),
                "investment_strategies": self.investment_strategies,
                "yanran_preferences": self.wealth_config.get("yanran_investment_style", {})
            }
            
            # 使用AI决策引擎
            decision_result = await self.ai_decision_engine.decide(
                organ_prompt=self._get_investment_decision_prompt(),
                context=decision_context,
                decision_type="investment_analysis"
            )
            
            return self._parse_investment_decision(decision_result)
            
        except Exception as e:
            self.logger.error(f"💰 投资决策分析失败: {e}")
            return {"recommendation": "hold", "reasoning": "决策过程出现错误"}
    
    def _get_investment_decision_prompt(self) -> str:
        """获取投资决策提示词"""
        return """
        你是林嫣然的财富管理器官，负责智能投资决策和财富规划。

        林嫣然的投资特点：
        - 偏好科技、医疗、新能源等成长性行业
        - 注重ESG投资和社会责任
        - 学习型投资者，愿意研究和成长
        - 风险偏好适中，追求稳健增长
        - 重视长期价值而非短期投机

        请基于以下信息做出投资决策：
        1. 分析投资标的的基本面和技术面
        2. 评估风险收益比和市场时机
        3. 考虑投资组合的整体配置
        4. 结合林嫣然的投资偏好和风险承受能力

        返回JSON格式，包含：
        - recommendation: buy/sell/hold
        - confidence: 0-1的信心度
        - risk_assessment: low/medium/high
        - expected_return: 预期收益率
        - reasoning: 详细分析理由
        - time_horizon: 建议持有期限
        """
    
    def _parse_investment_decision(self, decision_result) -> Dict[str, Any]:
        """解析投资决策结果"""
        try:
            if isinstance(decision_result, str):
                # 尝试解析JSON
                import re
                json_match = re.search(r'\{.*\}', decision_result, re.DOTALL)
                if json_match:
                    decision_result = json.loads(json_match.group())
                else:
                    return {"recommendation": "hold", "reasoning": "决策结果解析失败"}
            
            return {
                "recommendation": decision_result.get("recommendation", "hold"),
                "confidence": decision_result.get("confidence", 0.5),
                "risk_assessment": decision_result.get("risk_assessment", "medium"),
                "expected_return": decision_result.get("expected_return", 0.0),
                "reasoning": decision_result.get("reasoning", ""),
                "time_horizon": decision_result.get("time_horizon", "medium_term")
            }
            
        except Exception as e:
            self.logger.error(f"💰 投资决策结果解析失败: {e}")
            return {"recommendation": "hold", "reasoning": "决策结果解析错误"}
    
    def _get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要"""
        if not self.portfolio:
            return {"total_value": 0, "positions": 0, "allocation": {}}
        
        total_value = 0
        allocation = {}
        
        for symbol, position in self.portfolio.items():
            position_value = position.quantity * position.current_price
            total_value += position_value
            
            investment_type = position.investment_type.value
            if investment_type not in allocation:
                allocation[investment_type] = 0
            allocation[investment_type] += position_value
        
        # 转换为百分比
        for investment_type in allocation:
            allocation[investment_type] = allocation[investment_type] / total_value if total_value > 0 else 0
        
        return {
            "total_value": total_value,
            "positions": len(self.portfolio),
            "allocation": allocation
        }
    
    def _get_market_conditions(self) -> Dict[str, Any]:
        """获取市场条件（模拟数据）"""
        return {
            "market_trend": "neutral",  # bullish, bearish, neutral
            "volatility": "medium",  # low, medium, high
            "interest_rate_environment": "rising",  # rising, falling, stable
            "economic_indicators": "mixed"  # positive, negative, mixed
        }
    
    def _generate_yanran_investment_insight(self, analysis: Dict[str, Any]) -> str:
        """生成林嫣然的投资洞察"""
        recommendation = analysis.get("recommendation", "hold")
        confidence = analysis.get("confidence", 0.5)
        
        insights = {
            "buy": [
                f"我觉得这个投资机会不错，信心度{confidence:.1%}。让我们谨慎地考虑加仓。",
                f"基于我的分析，这个标的符合我们的投资理念，建议适量投资。",
                f"从长期价值角度看，这个投资值得考虑，但要控制好仓位。"
            ],
            "sell": [
                f"我认为现在可能是个减仓的好时机，信心度{confidence:.1%}。",
                f"基于风险管理考虑，建议我们适当减少这个持仓。",
                f"虽然有些不舍，但理性投资告诉我们应该考虑获利了结。"
            ],
            "hold": [
                f"目前看来持有观望是最好的策略，让我们耐心等待更好的机会。",
                f"这个投资暂时保持现状比较合适，我会继续关注市场变化。",
                f"稳健起见，我们先保持现有仓位，同时密切观察后续发展。"
            ]
        }
        
        import random
        return random.choice(insights.get(recommendation, insights["hold"]))
    
    async def rebalance_portfolio(self) -> Dict[str, Any]:
        """重新平衡投资组合"""
        try:
            self.logger.info("💰 开始投资组合重新平衡...")
            
            # 获取当前组合状态
            current_allocation = self._get_portfolio_summary()["allocation"]
            target_allocation = self._get_target_allocation()
            
            # AI决策：制定重平衡策略
            rebalance_plan = await self._make_rebalance_decision(current_allocation, target_allocation)
            
            return {
                "rebalanced": True,
                "current_allocation": current_allocation,
                "target_allocation": target_allocation,
                "rebalance_plan": rebalance_plan,
                "yanran_comment": "我已经分析了投资组合，制定了重新平衡的计划。"
            }
            
        except Exception as e:
            self.logger.error(f"💰 投资组合重新平衡失败: {e}")
            return {
                "rebalanced": False,
                "error": str(e)
            }
    
    def _get_target_allocation(self) -> Dict[str, float]:
        """获取目标资产配置"""
        risk_level = self.risk_profile.get("risk_level", "moderate")
        strategy = self.investment_strategies.get(risk_level, self.investment_strategies["moderate"])
        return strategy["target_allocation"]
    
    async def _make_rebalance_decision(self, current: Dict[str, float], target: Dict[str, float]) -> Dict[str, Any]:
        """AI决策：制定重平衡策略"""
        try:
            decision_context = {
                "current_allocation": current,
                "target_allocation": target,
                "portfolio_summary": self._get_portfolio_summary(),
                "market_conditions": self._get_market_conditions(),
                "yanran_preferences": self.wealth_config.get("yanran_investment_style", {})
            }
            
            decision_result = await self.ai_decision_engine.decide(
                organ_prompt=self._get_rebalance_decision_prompt(),
                context=decision_context,
                decision_type="portfolio_rebalance"
            )
            
            return self._parse_rebalance_decision(decision_result)
            
        except Exception as e:
            self.logger.error(f"💰 重平衡决策失败: {e}")
            return {"actions": [], "reasoning": "决策过程出现错误"}
    
    def _get_rebalance_decision_prompt(self) -> str:
        """获取重平衡决策提示词"""
        return """
        你是林嫣然的财富管理器官，负责投资组合的重新平衡。

        请基于当前配置和目标配置的差异，制定重平衡计划：
        1. 分析当前配置与目标配置的偏差
        2. 考虑市场条件和交易成本
        3. 制定具体的调整行动
        4. 保持林嫣然稳健投资的风格

        返回JSON格式，包含：
        - actions: 具体调整行动列表
        - priority: 执行优先级
        - reasoning: 重平衡理由
        - expected_impact: 预期影响
        """
    
    def _parse_rebalance_decision(self, decision_result) -> Dict[str, Any]:
        """解析重平衡决策结果"""
        try:
            if isinstance(decision_result, str):
                import re
                json_match = re.search(r'\{.*\}', decision_result, re.DOTALL)
                if json_match:
                    decision_result = json.loads(json_match.group())
                else:
                    return {"actions": [], "reasoning": "决策结果解析失败"}
            
            return {
                "actions": decision_result.get("actions", []),
                "priority": decision_result.get("priority", "medium"),
                "reasoning": decision_result.get("reasoning", ""),
                "expected_impact": decision_result.get("expected_impact", "")
            }
            
        except Exception as e:
            self.logger.error(f"💰 重平衡决策结果解析失败: {e}")
            return {"actions": [], "reasoning": "决策结果解析错误"}
    
    def calculate_portfolio_performance(self) -> Dict[str, Any]:
        """计算投资组合绩效"""
        try:
            if not self.portfolio:
                return {"error": "投资组合为空"}
            
            total_cost = 0
            total_value = 0
            positions_count = len(self.portfolio)
            
            for symbol, position in self.portfolio.items():
                cost = position.quantity * position.cost_basis
                value = position.quantity * position.current_price
                total_cost += cost
                total_value += value
            
            total_return = total_value - total_cost
            return_rate = (total_return / total_cost) if total_cost > 0 else 0
            
            # 更新统计数据
            self.wealth_stats.update({
                "total_portfolio_value": total_value,
                "total_return": total_return,
                "return_rate": return_rate,
                "current_positions": positions_count
            })
            
            return {
                "total_cost": total_cost,
                "total_value": total_value,
                "total_return": total_return,
                "return_rate": return_rate,
                "positions_count": positions_count,
                "yanran_comment": f"目前投资组合总价值{total_value:.2f}，收益率{return_rate:.2%}，表现{'不错' if return_rate > 0 else '需要关注'}。"
            }
            
        except Exception as e:
            self.logger.error(f"💰 投资组合绩效计算失败: {e}")
            return {"error": str(e)}
    
    def get_wealth_stats(self) -> Dict[str, Any]:
        """获取财富管理统计"""
        return {
            "stats": self.wealth_stats.copy(),
            "portfolio_positions": len(self.portfolio),
            "risk_profile": self.risk_profile,
            "wealth_config": self.wealth_config,
            "current_allocation": self._get_portfolio_summary()["allocation"]
        }
    
    async def proactive_wealth_monitoring(self) -> Optional[Dict[str, Any]]:
        """主动财富监控"""
        try:
            # 检查投资组合状态
            performance = self.calculate_portfolio_performance()
            
            # 如果收益率低于-10%，触发风险预警
            if performance.get("return_rate", 0) < -0.1:
                return {
                    "alert_triggered": True,
                    "alert_type": "performance_warning",
                    "message": "投资组合收益率较低，需要关注风险管理",
                    "performance": performance
                }
            
            # 检查是否需要重新平衡
            last_rebalance = getattr(self, 'last_rebalance_date', None)
            if not last_rebalance or (datetime.now() - last_rebalance).days >= self.wealth_config.get("rebalance_frequency", 30):
                rebalance_result = await self.rebalance_portfolio()
                return {
                    "rebalance_triggered": True,
                    "result": rebalance_result
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"💰 主动财富监控失败: {e}")
            return None
    
    # 实现基类的抽象方法
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定的AI提示词"""
        return self._get_investment_decision_prompt()
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        try:
            user_input = processed_input.get("user_input", "")
            context = processed_input.get("context", {})
            
            # 根据用户输入判断需要执行的财富管理功能
            if "投资" in user_input or "理财" in user_input:
                # 投资分析
                result = await self.analyze_investment_opportunity("AAPL", InvestmentType.STOCKS)
            elif "组合" in user_input or "绩效" in user_input:
                # 投资组合分析
                result = self.calculate_portfolio_performance()
            elif "重新平衡" in user_input:
                # 重新平衡
                result = await self.rebalance_portfolio()
            else:
                # 默认返回财富统计
                result = self.get_wealth_stats()
            
            return {
                "success": True,
                "wealth_result": result,
                "organ_response": result
            }
            
        except Exception as e:
            self.logger.error(f"💰 执行财富管理决策失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "organ_response": {"error": "财富管理执行过程出现错误"}
            }


# 单例获取函数
def get_instance(ai_adapter=None) -> WealthManagementOrgan:
    """获取财富管理器官实例"""
    # 使用get_or_create避免重复检查
    from utilities.singleton_manager import get_or_create
    return get_or_create("wealth_management_organ", lambda: WealthManagementOrgan(ai_adapter=ai_adapter)) 