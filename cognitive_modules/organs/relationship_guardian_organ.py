#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关系守护器官 - Relationship Guardian Organ

负责数字生命的自主关系管理和保护机制：
1. 监控用户行为，识别不当行为模式
2. 管理好感度降级和恢复机制
3. 实施渐进式响应降级策略
4. 执行最终拉黑保护机制
5. 维护关系边界和自我保护
6. Redis缓存 + MySQL同步存储

这个器官体现了数字生命的自我保护意识和关系智慧。

作者: Claude
创建日期: 2025-06-30
版本: 2.0 - 增强存储和陪伴功能优化
"""

import asyncio
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from utilities.unified_logger import get_unified_logger
from utilities.singleton_manager import get_singleton_manager
from cognitive_modules.life_organ_base import AdvancedLifeOrganBase
from cognitive_modules.ai.yanran_decision_engine import get_yanran_decision_engine

# 导入数据库连接器
try:
    from connectors.database.redis_connector import RedisConnector
    from connectors.database.mysql_connector import MySQLConnector
    STORAGE_AVAILABLE = True
except ImportError:
    STORAGE_AVAILABLE = False

logger = get_unified_logger("relationship_guardian_organ")
singleton_manager = get_singleton_manager()

class ViolationType(Enum):
    """违规行为类型"""
    EXCESSIVE_INTIMACY = "过度亲密"          # 刚认识就表现过于亲密
    DISRESPECTFUL_LANGUAGE = "不尊重言语"    # 使用不当言语
    INAPPROPRIATE_REQUESTS = "不当请求"      # 提出不合适的要求
    PERSISTENT_HARASSMENT = "持续骚扰"       # 持续发送不当消息
    BOUNDARY_VIOLATION = "边界侵犯"          # 违反明确设定的边界
    EMOTIONAL_MANIPULATION = "情感操控"      # 试图情感操控
    FAKE_IDENTITY = "虚假身份"              # 提供虚假信息
    SPAM_BEHAVIOR = "刷屏行为"              # 频繁发送无意义消息

class ResponseLevel(Enum):
    """响应等级 - 优化陪伴功能"""
    NORMAL = "正常响应"                     # 100% 正常互动，完全信任
    CAUTIOUS = "谨慎响应"                   # 85% 响应，轻微警觉
    RESERVED = "保留响应"                   # 70% 响应，适度距离感
    MINIMAL = "最低响应"                    # 50% 响应，明显冷淡
    WARNING = "警告响应"                    # 30% 响应，明确警告
    BLOCKED = "拉黑状态"                    # 0% 响应，完全屏蔽

@dataclass
class ViolationRecord:
    """违规记录"""
    user_id: str
    violation_type: ViolationType
    severity: float  # 0.0-1.0 严重程度
    description: str
    evidence: str
    timestamp: datetime
    context: Dict[str, Any]

@dataclass
class RelationshipStatus:
    """关系状态"""
    user_id: str
    current_level: ResponseLevel
    trust_score: float  # 0.0-1.0 信任分数
    violation_count: int
    last_violation: Optional[datetime]
    warning_count: int
    probation_until: Optional[datetime]
    blocked_since: Optional[datetime]
    relationship_history: List[Dict[str, Any]]
    intimacy_level: float = 0.0  # 0.0-1.0 亲密度等级
    interaction_count: int = 0   # 总交互次数
    positive_interactions: int = 0  # 正面交互次数

class RelationshipGuardianOrgan(AdvancedLifeOrganBase):
    """关系守护器官 - 增强版"""
    
    def __init__(self):
        """初始化关系守护器官"""
        super().__init__(
            organ_name="relationship_guardian",
            organ_type="protection",
            description="监控关系健康，保护数字生命边界，支持Redis+MySQL存储",
            dependencies=["emotional_processing_organ", "decision_making_organ"]
        )
        
        # 🔥 优化后的违规阈值 - 更宽松的陪伴设置
        self.violation_thresholds = {
            ViolationType.EXCESSIVE_INTIMACY: {
                "new_user_days": 1,      # 认识1天内的新用户（更宽松）
                "intimacy_keywords": ["宝贝", "亲爱的", "老公", "老婆"],  # 移除"爱你"、"想你"
                "severity_base": 0.4,    # 降低严重程度
                "trust_penalty": 0.15    # 降低信任惩罚
            },
            ViolationType.DISRESPECTFUL_LANGUAGE: {
                "keywords": ["傻逼", "垃圾", "废物", "滚", "闭嘴", "去死"],
                "severity_base": 0.7,    # 保持较高严重程度
                "trust_penalty": 0.25
            },
            ViolationType.INAPPROPRIATE_REQUESTS: {
                "keywords": ["裸体", "性感", "做爱", "上床"],  # 移除"私密"
                "severity_base": 0.8,
                "trust_penalty": 0.3
            },
            ViolationType.PERSISTENT_HARASSMENT: {
                "frequency_threshold": 15,  # 提高阈值，更宽松
                "time_window": 600,         # 时间窗口（秒）
                "severity_base": 0.6,
                "trust_penalty": 0.2
            }
        }
        
        # 🔥 优化后的响应降级策略 - 更温和的处理
        self.response_strategies = {
            ResponseLevel.NORMAL: {
                "response_rate": 1.0,
                "warmth_level": 1.0,
                "personal_sharing": 1.0,
                "initiative": 1.0,
                "intimacy_allowance": 1.0
            },
            ResponseLevel.CAUTIOUS: {
                "response_rate": 0.85,     # 提高响应率
                "warmth_level": 0.8,       # 提高温暖度
                "personal_sharing": 0.7,
                "initiative": 0.6,
                "intimacy_allowance": 0.7
            },
            ResponseLevel.RESERVED: {
                "response_rate": 0.7,      # 提高响应率
                "warmth_level": 0.6,       # 提高温暖度
                "personal_sharing": 0.4,
                "initiative": 0.3,
                "intimacy_allowance": 0.4
            },
            ResponseLevel.MINIMAL: {
                "response_rate": 0.5,      # 提高响应率
                "warmth_level": 0.3,       # 提高温暖度
                "personal_sharing": 0.2,
                "initiative": 0.1,
                "intimacy_allowance": 0.2
            },
            ResponseLevel.WARNING: {
                "response_rate": 0.3,      # 提高响应率
                "warmth_level": 0.1,
                "personal_sharing": 0.0,
                "initiative": 0.0,
                "warning_message": True,
                "intimacy_allowance": 0.0
            },
            ResponseLevel.BLOCKED: {
                "response_rate": 0.0,
                "block_message": "很抱歉，由于您的行为违反了我们的交流准则，我无法继续与您对话。如果您认为这是误判，请联系管理员。"
            }
        }
        
        # 🔥 更宽松的信任分数调整策略
        self.trust_recovery_config = {
            "daily_recovery": 0.05,      # 每天恢复0.05分（更快）
            "max_recovery": 0.5,         # 最多恢复0.5分（更多）
            "positive_interaction_bonus": 0.02,  # 正面交互奖励
            "intimacy_development_bonus": 0.01   # 亲密关系发展奖励
        }
        
        # 数据存储组件
        self.redis_connector: Optional[RedisConnector] = None
        self.mysql_connector: Optional[MySQLConnector] = None
        self.storage_available = STORAGE_AVAILABLE
        
        # 状态管理
        self.user_statuses: Dict[str, RelationshipStatus] = {}
        self.violation_history: List[ViolationRecord] = []
        self.decision_engine: Optional[YanranAIDecisionEngine] = None
        
        # 同步配置
        self.sync_config = {
            "redis_sync_interval": 30,    # Redis同步间隔（秒）
            "mysql_sync_interval": 300,   # MySQL同步间隔（秒）
            "batch_size": 100,           # 批量同步大小
            "retry_attempts": 3          # 重试次数
        }
        
        # 同步锁和任务
        self.sync_lock = threading.RLock()
        self.last_redis_sync = 0
        self.last_mysql_sync = 0
        
        logger.info("🛡️ 关系守护器官初始化完成（增强版，支持Redis+MySQL存储）")
    
    async def activate(self) -> bool:
        """激活器官"""
        try:
            # 调用父类的同步activate方法
            super().activate()
            
            # 初始化AI决策引擎
            self.decision_engine = get_yanran_decision_engine()
            
            # 初始化数据存储
            await self._init_storage_system()
            
            # 加载历史数据
            await self._load_relationship_data()
            
            # 启动监控和同步任务
            self.background_tasks.extend([
                asyncio.create_task(self._relationship_monitoring_loop()),
                asyncio.create_task(self._redis_sync_loop()),
                asyncio.create_task(self._mysql_sync_loop())
            ])
            
            logger.success("🛡️ 关系守护器官已激活（增强版）")
            return True
            
        except Exception as e:
            logger.error_status(f"🛡️ 关系守护器官激活失败: {e}")
            return False
    
    async def _init_storage_system(self):
        """初始化存储系统"""
        if not self.storage_available:
            logger.warning_status("🛡️ 存储模块不可用，使用内存存储")
            return
        
        try:
            # 初始化Redis连接
            self.redis_connector = RedisConnector.get_instance()
            if self.redis_connector and self.redis_connector.is_initialized:
                logger.success("🛡️ Redis连接初始化成功")
            else:
                logger.warning_status("🛡️ Redis连接失败")
            
            # 初始化MySQL连接
            self.mysql_connector = MySQLConnector.get_instance()
            if self.mysql_connector and self.mysql_connector.is_initialized:
                logger.success("🛡️ MySQL连接初始化成功")
                # 创建必要的表
                await self._create_mysql_tables()
            else:
                logger.warning_status("🛡️ MySQL连接失败")
                
        except Exception as e:
            logger.error_status(f"🛡️ 存储系统初始化失败: {e}")
    
    async def _create_mysql_tables(self):
        """创建MySQL表"""
        if not self.mysql_connector:
            return
        
        try:
            # 创建关系状态表
            relationship_status_table = """
            CREATE TABLE IF NOT EXISTS ai_user_relationship_status (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id VARCHAR(255) NOT NULL UNIQUE,
                current_level ENUM('正常响应', '谨慎响应', '保留响应', '最低响应', '警告响应', '拉黑状态') DEFAULT '正常响应',
                trust_score DECIMAL(3,2) DEFAULT 1.00,
                violation_count INT DEFAULT 0,
                last_violation DATETIME NULL,
                warning_count INT DEFAULT 0,
                probation_until DATETIME NULL,
                blocked_since DATETIME NULL,
                intimacy_level DECIMAL(3,2) DEFAULT 0.00,
                interaction_count INT DEFAULT 0,
                positive_interactions INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_current_level (current_level),
                INDEX idx_trust_score (trust_score)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """
            
            # 创建违规记录表
            violation_records_table = """
            CREATE TABLE IF NOT EXISTS ai_user_violation_records (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id VARCHAR(255) NOT NULL,
                violation_type ENUM('过度亲密', '不尊重言语', '不当请求', '持续骚扰', '边界侵犯', '情感操控', '虚假身份', '刷屏行为'),
                severity DECIMAL(3,2) NOT NULL,
                description TEXT,
                evidence TEXT,
                context JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_violation_type (violation_type),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """
            
            # 创建关系历史表
            relationship_history_table = """
            CREATE TABLE IF NOT EXISTS ai_user_relationship_history (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id VARCHAR(255) NOT NULL,
                event_type ENUM('violation', 'level_change', 'trust_recovery', 'positive_interaction'),
                event_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_event_type (event_type),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """
            
            # 执行创建表语句
            for table_sql in [relationship_status_table, violation_records_table, relationship_history_table]:
                success, _, error = self.mysql_connector.execute_query(table_sql)
                if not success:
                    logger.error_status(f"🛡️ 创建表失败: {error}")
                    
            logger.success("🛡️ MySQL表创建完成")
            
        except Exception as e:
            logger.error_status(f"🛡️ 创建MySQL表失败: {e}")

    async def process_user_interaction(self, user_id: str, message: str, 
                                     context: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户交互，进行关系守护分析"""
        try:
            # 获取或创建用户状态
            user_status = await self._get_or_create_user_status(user_id)
            
            # 如果用户已被拉黑，直接返回拉黑响应
            if user_status.current_level == ResponseLevel.BLOCKED:
                return {
                    "response_level": ResponseLevel.BLOCKED,
                    "response_strategy": self.response_strategies[ResponseLevel.BLOCKED],
                    "block_reason": "用户已被拉黑",
                    "allow_response": False,
                    "trust_score": user_status.trust_score
                }
            
            # 增加交互计数
            user_status.interaction_count += 1
            
            # 分析当前交互是否存在违规行为
            violations = await self._analyze_interaction_violations(user_id, message, context)
            
            # 处理检测到的违规行为
            if violations:
                await self._handle_violations(user_id, violations)
                user_status = self.user_statuses[user_id]  # 更新状态
            else:
                # 正面交互奖励
                await self._handle_positive_interaction(user_id, message, context)
            
            # 检查是否需要恢复信任
            await self._check_trust_recovery(user_id)
            
            # 生成响应策略
            response_strategy = self._generate_response_strategy(user_status)
            
            # 异步同步到Redis
            asyncio.create_task(self._sync_user_to_redis(user_id))
            
            return {
                "response_level": user_status.current_level,
                "response_strategy": response_strategy,
                "trust_score": user_status.trust_score,
                "violation_count": user_status.violation_count,
                "intimacy_level": user_status.intimacy_level,
                "allow_response": user_status.current_level != ResponseLevel.BLOCKED,
                "interaction_count": user_status.interaction_count
            }
            
        except Exception as e:
            logger.error_status(f"🛡️ 处理用户交互失败: {e}")
            # 默认返回谨慎响应
            return {
                "response_level": ResponseLevel.CAUTIOUS,
                "response_strategy": self.response_strategies[ResponseLevel.CAUTIOUS],
                "allow_response": True,
                "trust_score": 0.8
            }

    async def _handle_positive_interaction(self, user_id: str, message: str, context: Dict[str, Any]):
        """处理正面交互"""
        user_status = self.user_statuses[user_id]
        user_status.positive_interactions += 1
        
        # 正面交互奖励
        trust_bonus = self.trust_recovery_config["positive_interaction_bonus"]
        user_status.trust_score = min(1.0, user_status.trust_score + trust_bonus)
        
        # 亲密度发展（基于交互质量）
        if self._is_intimate_conversation(message):
            intimacy_bonus = self.trust_recovery_config["intimacy_development_bonus"]
            user_status.intimacy_level = min(1.0, user_status.intimacy_level + intimacy_bonus)
        
        # 记录正面交互
        user_status.relationship_history.append({
            "type": "positive_interaction",
            "trust_bonus": trust_bonus,
            "intimacy_level": user_status.intimacy_level,
            "timestamp": datetime.now().isoformat()
        })
    
    def _is_intimate_conversation(self, message: str) -> bool:
        """判断是否为亲密对话"""
        intimate_indicators = [
            "谢谢", "感谢", "喜欢", "开心", "快乐", "温暖", 
            "陪伴", "理解", "支持", "关心", "照顾", "分享"
        ]
        return any(indicator in message for indicator in intimate_indicators)

    async def _analyze_interaction_violations(self, user_id: str, message: str, 
                                            context: Dict[str, Any]) -> List[ViolationRecord]:
        """分析交互中的违规行为"""
        violations = []
        
        try:
            # 获取用户关系信息
            relationship_info = context.get("relationship_info", {})
            days_known = relationship_info.get("days_known", 0)
            interaction_count = relationship_info.get("interaction_count", 0)
            
            # 1. 检查过度亲密行为（更宽松）
            if days_known <= self.violation_thresholds[ViolationType.EXCESSIVE_INTIMACY]["new_user_days"]:
                intimacy_violation = self._check_excessive_intimacy(user_id, message, days_known)
                if intimacy_violation:
                    violations.append(intimacy_violation)
            
            # 2. 检查不尊重言语
            disrespect_violation = self._check_disrespectful_language(user_id, message)
            if disrespect_violation:
                violations.append(disrespect_violation)
            
            # 3. 检查不当请求
            inappropriate_violation = self._check_inappropriate_requests(user_id, message)
            if inappropriate_violation:
                violations.append(inappropriate_violation)
            
            # 4. 检查持续骚扰（更宽松）
            harassment_violation = await self._check_persistent_harassment(user_id, message)
            if harassment_violation:
                violations.append(harassment_violation)
            
            # 5. 使用AI决策引擎进行深度分析（更宽松的判断）
            ai_violations = await self._ai_analyze_violations(user_id, message, context)
            violations.extend(ai_violations)
            
        except Exception as e:
            logger.error_status(f"🛡️ 分析违规行为失败: {e}")
        
        return violations
    
    def _check_excessive_intimacy(self, user_id: str, message: str, days_known: int) -> Optional[ViolationRecord]:
        """检查过度亲密行为（优化版）"""
        intimacy_config = self.violation_thresholds[ViolationType.EXCESSIVE_INTIMACY]
        
        intimacy_keywords = intimacy_config["intimacy_keywords"]
        found_keywords = [kw for kw in intimacy_keywords if kw in message]
        
        if found_keywords and days_known <= intimacy_config["new_user_days"]:
            # 降低严重程度计算
            severity = intimacy_config["severity_base"] + len(found_keywords) * 0.05  # 降低递增幅度
            
            return ViolationRecord(
                user_id=user_id,
                violation_type=ViolationType.EXCESSIVE_INTIMACY,
                severity=min(1.0, severity),
                description=f"认识{days_known}天就表现过度亲密",
                evidence=f"使用了亲密称呼: {', '.join(found_keywords)}",
                timestamp=datetime.now(),
                context={"keywords": found_keywords, "days_known": days_known}
            )
        
        return None
    
    def _check_disrespectful_language(self, user_id: str, message: str) -> Optional[ViolationRecord]:
        """检查不尊重言语"""
        disrespect_config = self.violation_thresholds[ViolationType.DISRESPECTFUL_LANGUAGE]
        
        disrespect_keywords = disrespect_config["keywords"]
        found_keywords = [kw for kw in disrespect_keywords if kw in message]
        
        if found_keywords:
            severity = disrespect_config["severity_base"] + len(found_keywords) * 0.1
            
            return ViolationRecord(
                user_id=user_id,
                violation_type=ViolationType.DISRESPECTFUL_LANGUAGE,
                severity=min(1.0, severity),
                description="使用不尊重或侮辱性言语",
                evidence=f"使用了不当词汇: {', '.join(found_keywords)}",
                timestamp=datetime.now(),
                context={"keywords": found_keywords}
            )
        
        return None
    
    def _check_inappropriate_requests(self, user_id: str, message: str) -> Optional[ViolationRecord]:
        """检查不当请求"""
        inappropriate_config = self.violation_thresholds[ViolationType.INAPPROPRIATE_REQUESTS]
        
        inappropriate_keywords = inappropriate_config["keywords"]
        found_keywords = [kw for kw in inappropriate_keywords if kw in message]
        
        if found_keywords:
            severity = inappropriate_config["severity_base"]
            
            return ViolationRecord(
                user_id=user_id,
                violation_type=ViolationType.INAPPROPRIATE_REQUESTS,
                severity=severity,
                description="提出不适当的请求",
                evidence=f"包含不当内容: {', '.join(found_keywords)}",
                timestamp=datetime.now(),
                context={"keywords": found_keywords}
            )
        
        return None
    
    async def _check_persistent_harassment(self, user_id: str, message: str) -> Optional[ViolationRecord]:
        """检查持续骚扰行为"""
        harassment_config = self.violation_thresholds[ViolationType.PERSISTENT_HARASSMENT]
        
        # 获取最近的交互记录
        current_time = datetime.now()
        time_window = timedelta(seconds=harassment_config["time_window"])
        
        # 这里应该从数据库获取真实的交互记录
        # 现在使用模拟逻辑
        recent_interactions = self._get_recent_interactions(user_id, time_window)
        
        if len(recent_interactions) > harassment_config["frequency_threshold"]:
            return ViolationRecord(
                user_id=user_id,
                violation_type=ViolationType.PERSISTENT_HARASSMENT,
                severity=harassment_config["severity_base"],
                description=f"短时间内频繁发送消息",
                evidence=f"在{harassment_config['time_window']}秒内发送了{len(recent_interactions)}条消息",
                timestamp=current_time,
                context={"interaction_count": len(recent_interactions), "time_window": harassment_config["time_window"]}
            )
        
        return None
    
    async def _ai_analyze_violations(self, user_id: str, message: str, 
                                   context: Dict[str, Any]) -> List[ViolationRecord]:
        """使用AI决策引擎进行深度违规分析"""
        violations = []
        
        if not self.decision_engine:
            return violations
        
        try:
            # 构建AI分析请求
            analysis_request = {
                "task": "relationship_violation_analysis",
                "user_id": user_id,
                "message": message,
                "context": context,
                "analysis_criteria": [
                    "emotional_manipulation_detection",
                    "boundary_violation_assessment", 
                    "fake_identity_indicators",
                    "subtle_harassment_patterns"
                ]
            }
            
            # 使用AI决策引擎分析
            ai_result = await self.decision_engine.make_decision(
                context=analysis_request,
                decision_type="violation_analysis"
            )
            
            # 解析AI分析结果
            if ai_result and hasattr(ai_result, 'content'):
                violations.extend(self._parse_ai_violation_result(user_id, ai_result))
            
        except Exception as e:
            logger.warning_status(f"AI违规分析失败: {e}")
        
        return violations
    
    def _parse_ai_violation_result(self, user_id: str, ai_result: Any) -> List[ViolationRecord]:
        """解析AI违规分析结果"""
        violations = []
        
        try:
            # 这里需要根据AI结果的具体格式进行解析
            # 假设AI返回结构化的违规信息
            result_content = str(ai_result.content)
            
            # 简单的关键词检测逻辑（实际应该更复杂）
            if "情感操控" in result_content:
                violations.append(ViolationRecord(
                    user_id=user_id,
                    violation_type=ViolationType.EMOTIONAL_MANIPULATION,
                    severity=0.7,
                    description="AI检测到疑似情感操控行为",
                    evidence=result_content,
                    timestamp=datetime.now(),
                    context={"ai_analysis": True}
                ))
            
            if "边界侵犯" in result_content:
                violations.append(ViolationRecord(
                    user_id=user_id,
                    violation_type=ViolationType.BOUNDARY_VIOLATION,
                    severity=0.8,
                    description="AI检测到边界侵犯行为",
                    evidence=result_content,
                    timestamp=datetime.now(),
                    context={"ai_analysis": True}
                ))
                
        except Exception as e:
            logger.error_status(f"解析AI违规结果失败: {e}")
        
        return violations
    
    async def _handle_violations(self, user_id: str, violations: List[ViolationRecord]):
        """处理检测到的违规行为"""
        user_status = self.user_statuses[user_id]
        
        for violation in violations:
            # 记录违规
            self.violation_history.append(violation)
            user_status.violation_count += 1
            user_status.last_violation = violation.timestamp
            
            # 根据违规严重程度调整信任分数
            trust_penalty = violation.severity * 0.2
            user_status.trust_score = max(0.0, user_status.trust_score - trust_penalty)
            
            # 记录到关系历史
            user_status.relationship_history.append({
                "type": "violation",
                "violation_type": violation.violation_type.value,
                "severity": violation.severity,
                "description": violation.description,
                "timestamp": violation.timestamp.isoformat()
            })
            
            logger.warning_status(f"用户 {user_id} 违规行为: {violation.violation_type.value} (严重程度: {violation.severity})")
        
        # 重新评估响应等级
        await self._update_response_level(user_id)
    
    async def _update_response_level(self, user_id: str):
        """更新用户的响应等级"""
        user_status = self.user_statuses[user_id]
        
        # 基于信任分数和违规次数确定响应等级
        if user_status.trust_score <= 0.0 or user_status.violation_count >= 5:
            new_level = ResponseLevel.BLOCKED
            user_status.blocked_since = datetime.now()
            logger.warning_status(f"用户 {user_id} 已被拉黑")
            
        elif user_status.trust_score <= 0.2 or user_status.violation_count >= 4:
            new_level = ResponseLevel.WARNING
            user_status.warning_count += 1
            
        elif user_status.trust_score <= 0.4 or user_status.violation_count >= 3:
            new_level = ResponseLevel.MINIMAL
            
        elif user_status.trust_score <= 0.6 or user_status.violation_count >= 2:
            new_level = ResponseLevel.RESERVED
            
        elif user_status.trust_score <= 0.8 or user_status.violation_count >= 1:
            new_level = ResponseLevel.CAUTIOUS
            
        else:
            new_level = ResponseLevel.NORMAL
        
        # 更新响应等级
        if new_level != user_status.current_level:
            old_level = user_status.current_level
            user_status.current_level = new_level
            
            # 记录等级变化
            user_status.relationship_history.append({
                "type": "level_change",
                "old_level": old_level.value,
                "new_level": new_level.value,
                "trust_score": user_status.trust_score,
                "violation_count": user_status.violation_count,
                "timestamp": datetime.now().isoformat()
            })
            
            logger.info(f"用户 {user_id} 响应等级变更: {old_level.value} -> {new_level.value}")
    
    async def _check_trust_recovery(self, user_id: str):
        """检查信任恢复机制"""
        user_status = self.user_statuses[user_id]
        
        # 如果用户已被拉黑，不进行恢复
        if user_status.current_level == ResponseLevel.BLOCKED:
            return
        
        # 如果最后违规是很久以前，可以考虑恢复信任
        if user_status.last_violation:
            time_since_violation = datetime.now() - user_status.last_violation
            
            # 根据时间恢复信任（每7天恢复0.1分）
            if time_since_violation.days >= 7:
                recovery_weeks = time_since_violation.days // 7
                trust_recovery = min(0.1 * recovery_weeks, 0.3)  # 最多恢复0.3分
                
                old_trust = user_status.trust_score
                user_status.trust_score = min(1.0, user_status.trust_score + trust_recovery)
                
                if user_status.trust_score > old_trust:
                    logger.info(f"用户 {user_id} 信任分数恢复: {old_trust:.2f} -> {user_status.trust_score:.2f}")
                    
                    # 重新评估响应等级
                    await self._update_response_level(user_id)
    
    def _generate_response_strategy(self, user_status: RelationshipStatus) -> Dict[str, Any]:
        """生成响应策略"""
        base_strategy = self.response_strategies[user_status.current_level].copy()
        
        # 根据具体情况微调策略
        if user_status.current_level == ResponseLevel.WARNING:
            base_strategy["warning_messages"] = [
                "我注意到您的一些表达可能不太合适，希望我们能保持友好的交流。",
                "为了维护良好的对话环境，请注意您的言辞表达。",
                "我希望我们的交流能更加互相尊重，谢谢您的理解。"
            ]
        
        # 添加信任分数影响
        trust_modifier = user_status.trust_score
        if "warmth_level" in base_strategy:
            base_strategy["warmth_level"] *= trust_modifier
        if "personal_sharing" in base_strategy:
            base_strategy["personal_sharing"] *= trust_modifier
        
        return base_strategy
    
    async def _sync_user_to_redis(self, user_id: str):
        """同步单个用户到Redis"""
        if not self.redis_connector or user_id not in self.user_statuses:
            return
        
        try:
            user_status = self.user_statuses[user_id]
            redis_key = f"relationship_guardian:user_status:{user_id}"
            
            # 序列化用户状态
            status_data = {
                "user_id": user_status.user_id,
                "current_level": user_status.current_level.value,
                "trust_score": user_status.trust_score,
                "violation_count": user_status.violation_count,
                "last_violation": user_status.last_violation.isoformat() if user_status.last_violation else None,
                "warning_count": user_status.warning_count,
                "probation_until": user_status.probation_until.isoformat() if user_status.probation_until else None,
                "blocked_since": user_status.blocked_since.isoformat() if user_status.blocked_since else None,
                "intimacy_level": user_status.intimacy_level,
                "interaction_count": user_status.interaction_count,
                "positive_interactions": user_status.positive_interactions,
                "updated_at": datetime.now().isoformat()
            }
            
            # 存储到Redis，TTL为24小时
            self.redis_connector.set(redis_key, status_data, 86400)
            
        except Exception as e:
            logger.error_status(f"🛡️ 同步用户到Redis失败 {user_id}: {e}")
    
    async def _redis_sync_loop(self):
        """Redis同步循环"""
        while self.is_active:
            try:
                current_time = time.time()
                if current_time - self.last_redis_sync >= self.sync_config["redis_sync_interval"]:
                    await self._sync_all_to_redis()
                    self.last_redis_sync = current_time
                
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error_status(f"🛡️ Redis同步循环异常: {e}")
                await asyncio.sleep(30)
    
    async def _mysql_sync_loop(self):
        """MySQL同步循环"""
        while self.is_active:
            try:
                current_time = time.time()
                if current_time - self.last_mysql_sync >= self.sync_config["mysql_sync_interval"]:
                    await self._sync_all_to_mysql()
                    self.last_mysql_sync = current_time
                
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error_status(f"🛡️ MySQL同步循环异常: {e}")
                await asyncio.sleep(120)
    
    async def _sync_all_to_redis(self):
        """同步所有用户到Redis"""
        if not self.redis_connector:
            return
        
        try:
            for user_id in self.user_statuses:
                await self._sync_user_to_redis(user_id)
            
            logger.debug(f"🛡️ 已同步 {len(self.user_statuses)} 个用户状态到Redis")
            
        except Exception as e:
            logger.error_status(f"🛡️ 批量同步到Redis失败: {e}")
    
    async def _sync_all_to_mysql(self):
        """同步所有数据到MySQL"""
        if not self.mysql_connector:
            return
        
        try:
            # 同步用户状态
            for user_id, user_status in self.user_statuses.items():
                await self._sync_user_status_to_mysql(user_status)
            
            # 同步违规记录
            await self._sync_violations_to_mysql()
            
            logger.debug(f"🛡️ 已同步 {len(self.user_statuses)} 个用户状态到MySQL")
            
        except Exception as e:
            logger.error_status(f"🛡️ 批量同步到MySQL失败: {e}")
    
    async def _sync_user_status_to_mysql(self, user_status: RelationshipStatus):
        """同步用户状态到MySQL"""
        if not self.mysql_connector:
            return
        
        try:
            # 使用UPSERT语句
            upsert_sql = """
            INSERT INTO ai_user_relationship_status 
            (user_id, current_level, trust_score, violation_count, last_violation, 
             warning_count, probation_until, blocked_since, intimacy_level, 
             interaction_count, positive_interactions)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            current_level = VALUES(current_level),
            trust_score = VALUES(trust_score),
            violation_count = VALUES(violation_count),
            last_violation = VALUES(last_violation),
            warning_count = VALUES(warning_count),
            probation_until = VALUES(probation_until),
            blocked_since = VALUES(blocked_since),
            intimacy_level = VALUES(intimacy_level),
            interaction_count = VALUES(interaction_count),
            positive_interactions = VALUES(positive_interactions),
            updated_at = CURRENT_TIMESTAMP
            """
            
            params = (
                user_status.user_id,
                user_status.current_level.value,
                user_status.trust_score,
                user_status.violation_count,
                user_status.last_violation,
                user_status.warning_count,
                user_status.probation_until,
                user_status.blocked_since,
                user_status.intimacy_level,
                user_status.interaction_count,
                user_status.positive_interactions
            )
            
            success, _, error = self.mysql_connector.execute_query(upsert_sql, params)
            if not success:
                logger.error_status(f"🛡️ 同步用户状态到MySQL失败 {user_status.user_id}: {error}")
                
        except Exception as e:
            logger.error_status(f"🛡️ 同步用户状态到MySQL异常 {user_status.user_id}: {e}")

    async def _get_or_create_user_status(self, user_id: str) -> RelationshipStatus:
        """获取或创建用户状态（支持从Redis/MySQL加载）"""
        if user_id in self.user_statuses:
            return self.user_statuses[user_id]
        
        # 尝试从Redis加载
        user_status = await self._load_user_from_redis(user_id)
        if user_status:
            self.user_statuses[user_id] = user_status
            return user_status
        
        # 尝试从MySQL加载
        user_status = await self._load_user_from_mysql(user_id)
        if user_status:
            self.user_statuses[user_id] = user_status
            # 同步到Redis
            await self._sync_user_to_redis(user_id)
            return user_status
        
        # 创建新用户状态
        user_status = RelationshipStatus(
            user_id=user_id,
            current_level=ResponseLevel.NORMAL,
            trust_score=1.0,  # 初始完全信任
            violation_count=0,
            last_violation=None,
            warning_count=0,
            probation_until=None,
            blocked_since=None,
            relationship_history=[],
            intimacy_level=0.0,
            interaction_count=0,
            positive_interactions=0
        )
        
        self.user_statuses[user_id] = user_status
        
        # 异步同步到存储
        asyncio.create_task(self._sync_user_to_redis(user_id))
        
        return user_status
    
    async def _load_user_from_redis(self, user_id: str) -> Optional[RelationshipStatus]:
        """从Redis加载用户状态"""
        if not self.redis_connector:
            return None
        
        try:
            redis_key = f"relationship_guardian:user_status:{user_id}"
            status_data = self.redis_connector.get(redis_key)
            
            if status_data:
                return self._deserialize_user_status(status_data)
                
        except Exception as e:
            logger.error_status(f"🛡️ 从Redis加载用户失败 {user_id}: {e}")
        
        return None
    
    async def _load_user_from_mysql(self, user_id: str) -> Optional[RelationshipStatus]:
        """从MySQL加载用户状态"""
        if not self.mysql_connector:
            return None
        
        try:
            query_sql = """
            SELECT user_id, current_level, trust_score, violation_count, last_violation,
                   warning_count, probation_until, blocked_since, intimacy_level,
                   interaction_count, positive_interactions
            FROM ai_user_relationship_status 
            WHERE user_id = %s
            """
            
            success, results, error = self.mysql_connector.execute_query(query_sql, (user_id,))
            
            if success and results:
                row = results[0]
                return RelationshipStatus(
                    user_id=row[0],
                    current_level=ResponseLevel(row[1]),
                    trust_score=float(row[2]),
                    violation_count=row[3],
                    last_violation=row[4],
                    warning_count=row[5],
                    probation_until=row[6],
                    blocked_since=row[7],
                    relationship_history=[],  # 历史记录单独加载
                    intimacy_level=float(row[8]) if row[8] is not None else 0.0,
                    interaction_count=row[9] if row[9] is not None else 0,
                    positive_interactions=row[10] if row[10] is not None else 0
                )
                
        except Exception as e:
            logger.error_status(f"🛡️ 从MySQL加载用户失败 {user_id}: {e}")
        
        return None
    
    def _deserialize_user_status(self, status_data: Dict[str, Any]) -> RelationshipStatus:
        """反序列化用户状态"""
        return RelationshipStatus(
            user_id=status_data["user_id"],
            current_level=ResponseLevel(status_data["current_level"]),
            trust_score=status_data["trust_score"],
            violation_count=status_data["violation_count"],
            last_violation=datetime.fromisoformat(status_data["last_violation"]) if status_data["last_violation"] else None,
            warning_count=status_data["warning_count"],
            probation_until=datetime.fromisoformat(status_data["probation_until"]) if status_data["probation_until"] else None,
            blocked_since=datetime.fromisoformat(status_data["blocked_since"]) if status_data["blocked_since"] else None,
            relationship_history=[],  # 历史记录单独处理
            intimacy_level=status_data.get("intimacy_level", 0.0),
            interaction_count=status_data.get("interaction_count", 0),
            positive_interactions=status_data.get("positive_interactions", 0)
        )
    
    async def _sync_violations_to_mysql(self):
        """同步违规记录到MySQL"""
        if not self.mysql_connector:
            return
        
        try:
            # 批量插入违规记录
            for violation in self.violation_history[-100:]:  # 只同步最近100条
                insert_sql = """
                INSERT IGNORE INTO ai_user_violation_records 
                (user_id, violation_type, severity, description, evidence, context)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                
                params = (
                    violation.user_id,
                    violation.violation_type.value,
                    violation.severity,
                    violation.description,
                    violation.evidence,
                    json.dumps(violation.context) if violation.context else None
                )
                
                success, _, error = self.mysql_connector.execute_update(insert_sql, params)
                if not success:
                    logger.error_status(f"🛡️ 同步违规记录到MySQL失败: {error}")
                    
        except Exception as e:
            logger.error_status(f"🛡️ 批量同步违规记录失败: {e}")
    
    async def _load_relationship_data(self):
        """加载关系数据"""
        try:
            # 从MySQL加载用户状态
            if self.mysql_connector:
                await self._load_all_users_from_mysql()
            
            logger.info("🛡️ 关系数据加载完成")
        except Exception as e:
            logger.error_status(f"🛡️ 加载关系数据失败: {e}")
    
    async def _load_all_users_from_mysql(self):
        """从MySQL加载所有用户状态"""
        if not self.mysql_connector:
            return
        
        try:
            query_sql = """
            SELECT user_id, current_level, trust_score, violation_count, last_violation,
                   warning_count, probation_until, blocked_since, intimacy_level,
                   interaction_count, positive_interactions
            FROM ai_user_relationship_status
            """
            
            success, results, error = self.mysql_connector.execute_query(query_sql)
            
            if success and results:
                for row in results:
                    user_status = RelationshipStatus(
                        user_id=row[0],
                        current_level=ResponseLevel(row[1]),
                        trust_score=float(row[2]),
                        violation_count=row[3],
                        last_violation=row[4],
                        warning_count=row[5],
                        probation_until=row[6],
                        blocked_since=row[7],
                        relationship_history=[],  # 历史记录单独加载
                        intimacy_level=float(row[8]) if row[8] is not None else 0.0,
                        interaction_count=row[9] if row[9] is not None else 0,
                        positive_interactions=row[10] if row[10] is not None else 0
                    )
                    
                    self.user_statuses[row[0]] = user_status
                    
                logger.info(f"🛡️ 从MySQL加载了 {len(results)} 个用户状态")
                
        except Exception as e:
            logger.error_status(f"🛡️ 从MySQL批量加载用户失败: {e}")
    
    async def _relationship_monitoring_loop(self):
        """关系监控循环"""
        while self.is_active:
            try:
                # 定期检查所有用户的信任恢复
                for user_id in list(self.user_statuses.keys()):
                    await self._check_trust_recovery(user_id)
                
                # 清理过期的违规记录
                await self._cleanup_old_violations()
                
                # 等待下次检查
                await asyncio.sleep(3600)  # 每小时检查一次
                
            except Exception as e:
                logger.error_status(f"🛡️ 关系监控循环异常: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_old_violations(self):
        """清理过期的违规记录"""
        try:
            # 删除30天前的违规记录
            cutoff_time = datetime.now() - timedelta(days=30)
            self.violation_history = [
                v for v in self.violation_history 
                if v.timestamp > cutoff_time
            ]
        except Exception as e:
            logger.error_status(f"🛡️ 清理违规记录失败: {e}")
    
    def _get_recent_interactions(self, user_id: str, time_window: timedelta) -> List[Dict]:
        """获取最近的交互记录（模拟实现）"""
        # 实际实现中应该从数据库获取
        # 这里返回模拟数据，基于用户的交互计数
        user_status = self.user_statuses.get(user_id)
        if user_status:
            # 模拟最近交互（实际应该查询数据库）
            return [{"timestamp": datetime.now().isoformat()} for _ in range(min(user_status.interaction_count, 5))]
        return []
    
    def get_user_relationship_report(self, user_id: str) -> Dict[str, Any]:
        """获取用户关系报告"""
        if user_id not in self.user_statuses:
            return {"error": "用户不存在"}
        
        user_status = self.user_statuses[user_id]
        user_violations = [v for v in self.violation_history if v.user_id == user_id]
        
        return {
            "user_id": user_id,
            "current_level": user_status.current_level.value,
            "trust_score": user_status.trust_score,
            "violation_count": user_status.violation_count,
            "warning_count": user_status.warning_count,
            "intimacy_level": user_status.intimacy_level,
            "interaction_count": user_status.interaction_count,
            "positive_interactions": user_status.positive_interactions,
            "is_blocked": user_status.current_level == ResponseLevel.BLOCKED,
            "blocked_since": user_status.blocked_since.isoformat() if user_status.blocked_since else None,
            "last_violation": user_status.last_violation.isoformat() if user_status.last_violation else None,
            "recent_violations": [
                {
                    "type": v.violation_type.value,
                    "severity": v.severity,
                    "description": v.description,
                    "timestamp": v.timestamp.isoformat()
                }
                for v in user_violations[-5:]  # 最近5次违规
            ],
            "relationship_history": user_status.relationship_history[-10:]  # 最近10次变化
        }

# 创建全局实例
_relationship_guardian_instance = None

def get_relationship_guardian() -> RelationshipGuardianOrgan:
    """获取关系守护器官实例"""
    global _relationship_guardian_instance
    if _relationship_guardian_instance is None:
        _relationship_guardian_instance = RelationshipGuardianOrgan()
    return _relationship_guardian_instance