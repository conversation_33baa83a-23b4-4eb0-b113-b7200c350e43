"""
林嫣然数据感知器官 - 智能数据连接和感知的器官
作为林嫣然的"数据感知神经"，负责数据收集、处理和智能感知
"""

import asyncio
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from enum import Enum

from .base_organ import LifeOrgan
from ..ai.yanran_decision_engine import YanranAIDecisionEngine
from utilities.singleton_manager import get, register

from utilities.unified_logger import get_unified_logger



class DataSourceType(Enum):
    """数据源类型枚举"""
    DATABASE = "database"
    API = "api"
    FILE = "file"
    STREAM = "stream"
    SENSOR = "sensor"
    WEB = "web"


class DataQuality(Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"


@dataclass
class DataSource:
    """数据源"""
    source_id: str
    source_type: DataSourceType
    name: str
    url: Optional[str]
    credentials: Optional[Dict[str, str]]
    refresh_interval: int  # 秒
    priority: int  # 1-10，10最高
    is_active: bool
    last_update: Optional[datetime]
    data_quality: DataQuality


@dataclass
class DataPerceptionResult:
    """数据感知结果"""
    perception_id: str
    source_id: str
    data_type: str
    content: Any
    quality_score: float
    confidence: float
    insights: List[str]
    anomalies: List[str]
    timestamp: datetime


class DataPerceptionOrgan(LifeOrgan):
    """林嫣然的数据感知器官"""
    
    def __init__(self, ai_adapter=None):
        super().__init__(
            organ_name="data_perception_organ",
            organ_function="智能数据连接和感知优化",
            ai_adapter=ai_adapter
        )
        
        # 数据感知相关组件
        self.data_sources = {}  # 数据源
        self.perception_history = []  # 感知历史
        self.data_cache = {}  # 数据缓存
        self.anomaly_patterns = []  # 异常模式
        
        # 数据感知配置
        self.perception_config = self._load_perception_config()
        
        # 性能统计
        self.perception_stats = {
            "total_data_sources": 0,
            "active_sources": 0,
            "data_quality_avg": 0.0,
            "perception_count": 0,
            "anomaly_detection_count": 0,
            "successful_connections": 0,
            "failed_connections": 0,
            "avg_response_time": 0.0
        }
        
        self.logger = get_unified_logger(__name__)
        self.logger.info("📊 林嫣然数据感知器官初始化开始...")
        
        # 初始化数据感知系统
        self._init_data_perception()
        
        self.logger.info("📊 林嫣然数据感知器官初始化完成")
    
    def _load_perception_config(self) -> Dict[str, Any]:
        """加载数据感知配置"""
        try:
            config_path = os.path.join("config", "organs", "data_perception_organ.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"📊 数据感知配置加载失败: {e}")
        
        # 默认配置
        return {
            "perception_mode": "intelligent",  # intelligent, passive, active
            "data_quality_threshold": 0.7,
            "anomaly_detection_enabled": True,
            "auto_source_discovery": True,
            "cache_duration": 3600,  # 1小时
            "max_concurrent_connections": 10,
            "yanran_data_preferences": {
                "preferred_data_types": ["financial", "social", "creative", "educational"],
                "quality_focus": True,
                "real_time_priority": True,
                "insight_generation": True,
                "pattern_recognition": True
            }
        }
    
    def _init_data_perception(self):
        """初始化数据感知系统"""
        try:
            # 获取数据连接器
            mysql_connector = get("mysql_connector")
            if mysql_connector:
                self.mysql_connector = mysql_connector
                self.logger.info("📊 MySQL连接器连接成功")
            else:
                self.logger.warning("📊 MySQL连接器未找到")
            
            # 获取实时数据收集器
            try:
                # 🔥 老王修复：使用正确的导入方式获取实时数据收集器
                from perception.real_time_data_collector import get_instance as get_data_collector_instance
                self.data_collector = get_data_collector_instance()
                self.logger.info("📊 实时数据收集器连接成功")
            except Exception as e:
                self.logger.warning(f"📊 实时数据收集器初始化失败: {e}")
                self.data_collector = None
            
            # 初始化数据源
            self._init_data_sources()
            
            # 启动数据感知任务
            self._start_perception_tasks()
            
        except Exception as e:
            self.logger.warning(f"📊 数据感知系统初始化部分失败: {e}")
    
    def _init_data_sources(self):
        """初始化数据源"""
        try:
            # 预定义数据源
            default_sources = [
                DataSource(
                    source_id="mysql_primary",
                    source_type=DataSourceType.DATABASE,
                    name="主数据库",
                    url="mysql://localhost",
                    credentials=None,
                    refresh_interval=60,
                    priority=9,
                    is_active=True,
                    last_update=None,
                    data_quality=DataQuality.EXCELLENT
                ),
                DataSource(
                    source_id="trend_api",
                    source_type=DataSourceType.API,
                    name="趋势数据API",
                    url="https://api.trend.com",
                    credentials=None,
                    refresh_interval=300,
                    priority=8,
                    is_active=True,
                    last_update=None,
                    data_quality=DataQuality.GOOD
                ),
                DataSource(
                    source_id="financial_stream",
                    source_type=DataSourceType.STREAM,
                    name="金融数据流",
                    url="wss://finance.stream.com",
                    credentials=None,
                    refresh_interval=5,
                    priority=7,
                    is_active=True,
                    last_update=None,
                    data_quality=DataQuality.GOOD
                )
            ]
            
            for source in default_sources:
                self.data_sources[source.source_id] = source
            
            self.perception_stats["total_data_sources"] = len(self.data_sources)
            self.perception_stats["active_sources"] = sum(1 for s in self.data_sources.values() if s.is_active)
            
            self.logger.info(f"📊 初始化了 {len(self.data_sources)} 个数据源")
            
        except Exception as e:
            self.logger.warning(f"📊 数据源初始化失败: {e}")
    
    def _start_perception_tasks(self):
        """🔥 香草修复：启动数据感知任务，处理事件循环问题"""
        try:
            # 检查是否有运行中的事件循环
            try:
                loop = asyncio.get_running_loop()
                # 如果有运行中的事件循环，创建任务
                loop.create_task(self._perception_loop())
                self.logger.info("📊 数据感知任务已启动（使用现有事件循环）")
            except RuntimeError:
                # 没有运行中的事件循环，使用线程启动
                import threading
                def run_perception_loop():
                    try:
                        asyncio.run(self._perception_loop())
                    except Exception as e:
                        self.logger.warning(f"📊 感知循环运行失败: {e}")

                perception_thread = threading.Thread(target=run_perception_loop, daemon=True)
                perception_thread.start()
                self.logger.info("📊 数据感知任务已启动（使用新线程）")

        except Exception as e:
            self.logger.warning(f"📊 数据感知任务启动失败: {e}")
    
    async def _perception_loop(self):
        """数据感知循环 - 🔥 优化频率"""
        while True:
            try:
                await self._perform_data_perception()
                await asyncio.sleep(300)  # 🔥 改为5分钟一次感知，降低频率
            except Exception as e:
                self.logger.error(f"📊 数据感知循环错误: {e}")
                await asyncio.sleep(600)  # 🔥 错误时等待10分钟
    
    async def _perform_data_perception(self):
        """执行数据感知"""
        try:
            # 检查活跃数据源
            active_sources = [s for s in self.data_sources.values() if s.is_active]
            
            for source in active_sources:
                # 检查是否需要更新
                if self._should_refresh_source(source):
                    perception_result = await self._perceive_data_source(source)
                    if perception_result:
                        await self._process_perception_result(perception_result)
            
        except Exception as e:
            self.logger.error(f"📊 数据感知执行失败: {e}")
    
    def _should_refresh_source(self, source: DataSource) -> bool:
        """判断是否需要刷新数据源"""
        if not source.last_update:
            return True
        
        time_since_update = (datetime.now() - source.last_update).total_seconds()
        return time_since_update >= source.refresh_interval
    
    async def _perceive_data_source(self, source: DataSource) -> Optional[DataPerceptionResult]:
        """感知数据源"""
        try:
            self.logger.debug(f"📊 感知数据源: {source.name}")
            
            # 根据数据源类型获取数据
            data_content = await self._fetch_data_from_source(source)
            
            if data_content is None:
                self.perception_stats["failed_connections"] += 1
                return None
            
            # AI决策：分析数据内容
            analysis_result = await self._analyze_data_with_ai(source, data_content)
            
            # 创建感知结果
            perception_result = DataPerceptionResult(
                perception_id=f"perception_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{source.source_id}",
                source_id=source.source_id,
                data_type=source.source_type.value,
                content=data_content,
                quality_score=analysis_result.get("quality_score", 0.5),
                confidence=analysis_result.get("confidence", 0.5),
                insights=analysis_result.get("insights", []),
                anomalies=analysis_result.get("anomalies", []),
                timestamp=datetime.now()
            )
            
            # 更新数据源状态
            source.last_update = datetime.now()
            self.perception_stats["successful_connections"] += 1
            self.perception_stats["perception_count"] += 1
            
            return perception_result
            
        except Exception as e:
            self.logger.error(f"📊 数据源感知失败 {source.source_id}: {e}")
            self.perception_stats["failed_connections"] += 1
            return None
    
    async def _fetch_data_from_source(self, source: DataSource) -> Optional[Any]:
        """从数据源获取数据"""
        try:
            if source.source_type == DataSourceType.DATABASE:
                return await self._fetch_database_data(source)
            elif source.source_type == DataSourceType.API:
                return await self._fetch_api_data(source)
            elif source.source_type == DataSourceType.STREAM:
                return await self._fetch_stream_data(source)
            else:
                # 模拟数据
                return {"timestamp": datetime.now().isoformat(), "value": "sample_data"}
                
        except Exception as e:
            self.logger.error(f"📊 数据获取失败 {source.source_id}: {e}")
            return None
    
    async def _fetch_database_data(self, source: DataSource) -> Optional[Dict[str, Any]]:
        """从数据库获取数据"""
        try:
            if hasattr(self, 'mysql_connector'):
                # 获取最新的一些数据作为示例
                return {
                    "type": "database_sample",
                    "timestamp": datetime.now().isoformat(),
                    "record_count": 100,
                    "status": "active"
                }
            return None
        except Exception as e:
            self.logger.error(f"📊 数据库数据获取失败: {e}")
            return None
    
    async def _fetch_api_data(self, source: DataSource) -> Optional[Dict[str, Any]]:
        """从API获取数据"""
        try:
            # 模拟API数据
            return {
                "type": "api_data",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "data_points": 50
            }
        except Exception as e:
            self.logger.error(f"📊 API数据获取失败: {e}")
            return None
    
    async def _fetch_stream_data(self, source: DataSource) -> Optional[Dict[str, Any]]:
        """从数据流获取数据"""
        try:
            # 模拟流数据
            return {
                "type": "stream_data",
                "timestamp": datetime.now().isoformat(),
                "stream_rate": "10/sec",
                "quality": "high"
            }
        except Exception as e:
            self.logger.error(f"📊 流数据获取失败: {e}")
            return None
    
    async def _analyze_data_with_ai(self, source: DataSource, data_content: Any) -> Dict[str, Any]:
        """AI分析数据内容"""
        try:
            # 构建分析上下文
            analysis_context = {
                "source_info": {
                    "source_id": source.source_id,
                    "source_type": source.source_type.value,
                    "name": source.name,
                    "priority": source.priority
                },
                "data_content": data_content,
                "perception_config": self.perception_config,
                "yanran_preferences": self.perception_config.get("yanran_data_preferences", {})
            }
            
            # 使用AI决策引擎
            decision_result = await self.ai_decision_engine.decide(
                organ_prompt=self._get_data_analysis_prompt(),
                context=analysis_context,
                decision_type="data_perception_analysis"
            )
            
            return self._parse_analysis_result(decision_result)
            
        except Exception as e:
            self.logger.error(f"📊 AI数据分析失败: {e}")
            return {"quality_score": 0.5, "confidence": 0.3, "insights": [], "anomalies": []}
    
    def _get_data_analysis_prompt(self) -> str:
        """获取数据分析提示词"""
        return """
        你是林嫣然的数据感知器官，负责智能分析和感知数据。

        林嫣然的数据感知特点：
        - 注重数据质量和准确性
        - 善于发现数据中的模式和洞察
        - 对金融、社交、创意、教育类数据特别敏感
        - 重视实时性和相关性
        - 具备异常检测能力

        请分析提供的数据，评估：
        1. 数据质量分数 (0-1)
        2. 分析信心度 (0-1)
        3. 关键洞察和发现
        4. 潜在异常或问题

        返回JSON格式，包含：
        - quality_score: 数据质量分数
        - confidence: 分析信心度
        - insights: 洞察列表
        - anomalies: 异常列表
        - yanran_comment: 林嫣然的个人感受
        """
    
    def _parse_analysis_result(self, decision_result) -> Dict[str, Any]:
        """解析分析结果"""
        try:
            if isinstance(decision_result, str):
                # 尝试解析JSON
                import re
                json_match = re.search(r'\{.*\}', decision_result, re.DOTALL)
                if json_match:
                    decision_result = json.loads(json_match.group())
                else:
                    return {"quality_score": 0.5, "confidence": 0.3, "insights": [], "anomalies": []}
            
            return {
                "quality_score": decision_result.get("quality_score", 0.5),
                "confidence": decision_result.get("confidence", 0.5),
                "insights": decision_result.get("insights", []),
                "anomalies": decision_result.get("anomalies", []),
                "yanran_comment": decision_result.get("yanran_comment", "")
            }
            
        except Exception as e:
            self.logger.error(f"📊 分析结果解析失败: {e}")
            return {"quality_score": 0.5, "confidence": 0.3, "insights": [], "anomalies": []}
    
    async def _process_perception_result(self, result: DataPerceptionResult):
        """处理感知结果"""
        try:
            # 添加到历史记录
            self.perception_history.append(result)
            
            # 限制历史记录长度
            if len(self.perception_history) > 1000:
                self.perception_history = self.perception_history[-1000:]
            
            # 检测异常
            if result.anomalies:
                self.perception_stats["anomaly_detection_count"] += 1
                await self._handle_anomalies(result)
            
            # 缓存高质量数据
            if result.quality_score > 0.8:
                self.data_cache[result.source_id] = {
                    "content": result.content,
                    "timestamp": result.timestamp,
                    "quality_score": result.quality_score
                }
            
            # 更新统计
            self._update_perception_stats(result)
            
        except Exception as e:
            self.logger.error(f"📊 感知结果处理失败: {e}")
    
    async def _handle_anomalies(self, result: DataPerceptionResult):
        """处理异常 - 🔥 老王修复：优化异常处理逻辑，避免重复通用异常"""
        try:
            # 🔥 过滤通用异常描述，只记录具体异常
            filtered_anomalies = []
            generic_patterns = [
                "部分数据点的准确性有待验证",
                "金融数据的异常波动可能预示着市场的不确定性",
                "需要进一步监控和分析"
            ]

            for anomaly in result.anomalies:
                # 检查是否为通用异常描述
                is_generic = any(pattern in anomaly for pattern in generic_patterns)
                if not is_generic:
                    filtered_anomalies.append(anomaly)

            # 只有具体异常才记录和警告
            if filtered_anomalies:
                self.logger.warning(f"📊 检测到具体数据异常 {result.source_id}: {filtered_anomalies}")

                # 记录异常模式
                for anomaly in filtered_anomalies:
                    anomaly_pattern = {
                        "source_id": result.source_id,
                        "anomaly": anomaly,
                        "timestamp": result.timestamp,
                        "quality_score": result.quality_score
                    }
                    self.anomaly_patterns.append(anomaly_pattern)
            else:
                # 通用异常降级为debug日志
                self.logger.debug(f"📊 检测到通用数据异常 {result.source_id}: {result.anomalies}")

            # 限制异常模式记录长度
            if len(self.anomaly_patterns) > 500:
                self.anomaly_patterns = self.anomaly_patterns[-500:]

        except Exception as e:
            self.logger.error(f"📊 异常处理失败: {e}")
    
    def _update_perception_stats(self, result: DataPerceptionResult):
        """更新感知统计"""
        try:
            # 更新平均数据质量
            total_quality = sum(r.quality_score for r in self.perception_history[-100:])
            self.perception_stats["data_quality_avg"] = total_quality / min(len(self.perception_history), 100)
            
        except Exception as e:
            self.logger.error(f"📊 统计更新失败: {e}")
    
    def add_data_source(self, source: DataSource) -> bool:
        """添加数据源"""
        try:
            self.data_sources[source.source_id] = source
            self.perception_stats["total_data_sources"] = len(self.data_sources)
            if source.is_active:
                self.perception_stats["active_sources"] += 1
            
            self.logger.info(f"📊 添加数据源: {source.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"📊 添加数据源失败: {e}")
            return False
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        try:
            recent_perceptions = self.perception_history[-50:] if self.perception_history else []
            
            quality_by_source = {}
            for perception in recent_perceptions:
                if perception.source_id not in quality_by_source:
                    quality_by_source[perception.source_id] = []
                quality_by_source[perception.source_id].append(perception.quality_score)
            
            # 计算平均质量
            avg_quality_by_source = {}
            for source_id, scores in quality_by_source.items():
                avg_quality_by_source[source_id] = sum(scores) / len(scores)
            
            return {
                "overall_quality": self.perception_stats["data_quality_avg"],
                "quality_by_source": avg_quality_by_source,
                "total_perceptions": len(self.perception_history),
                "recent_anomalies": len([p for p in recent_perceptions if p.anomalies]),
                "active_sources": self.perception_stats["active_sources"],
                "yanran_assessment": self._generate_yanran_quality_assessment()
            }
            
        except Exception as e:
            self.logger.error(f"📊 数据质量报告生成失败: {e}")
            return {"error": str(e)}
    
    def _generate_yanran_quality_assessment(self) -> str:
        """生成林嫣然的数据质量评估"""
        quality_avg = self.perception_stats["data_quality_avg"]
        
        if quality_avg >= 0.8:
            return "数据质量很不错！我能从中获得很多有价值的洞察。"
        elif quality_avg >= 0.6:
            return "数据质量还可以，不过有些地方还可以改进。"
        elif quality_avg >= 0.4:
            return "数据质量一般般，我需要更仔细地筛选和分析。"
        else:
            return "数据质量有些担忧，需要重点关注数据源的可靠性。"
    
    def get_perception_stats(self) -> Dict[str, Any]:
        """获取数据感知统计"""
        return {
            "stats": self.perception_stats.copy(),
            "data_sources_count": len(self.data_sources),
            "perception_history_count": len(self.perception_history),
            "anomaly_patterns_count": len(self.anomaly_patterns),
            "config": self.perception_config
        }
    
    # 实现基类的抽象方法
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定的AI提示词"""
        return self._get_data_analysis_prompt()
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        try:
            user_input = processed_input.get("user_input", "")
            
            # 根据用户输入判断需要执行的数据感知功能
            if "数据质量" in user_input or "质量报告" in user_input:
                # 数据质量报告
                result = self.get_data_quality_report()
            elif "数据源" in user_input:
                # 数据源信息
                result = {
                    "data_sources": {source_id: {
                        "name": source.name,
                        "type": source.source_type.value,
                        "is_active": source.is_active,
                        "priority": source.priority
                    } for source_id, source in self.data_sources.items()}
                }
            else:
                # 默认返回感知统计
                result = self.get_perception_stats()
            
            return {
                "success": True,
                "perception_result": result,
                "organ_response": result
            }
            
        except Exception as e:
            self.logger.error(f"📊 执行数据感知决策失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "organ_response": {"error": "数据感知执行过程出现错误"}
            }


# 单例获取函数
def get_instance(ai_adapter=None) -> DataPerceptionOrgan:
    """获取数据感知器官实例"""
    # 使用get_or_create避免重复检查
    from utilities.singleton_manager import get_or_create
    return get_or_create("data_perception_organ", lambda: DataPerceptionOrgan(ai_adapter=ai_adapter)) 