#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
决策制定器官

负责智能决策、策略选择和行为规划。

作者: Claude
创建日期: 2025-06-30
版本: 1.0
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from utilities.singleton_manager import get_singleton_manager
from cognitive_modules.life_organ_base import LifeOrganBase
from cognitive_modules.ai.yanran_decision_engine import YanranAIDecisionEngine

from utilities.unified_logger import get_unified_logger
logger = get_unified_logger("decision_making_organ")
singleton_manager = get_singleton_manager()

class DecisionMakingOrgan(LifeOrganBase):
    """决策制定器官"""
    
    def __init__(self):
        """初始化决策制定器官"""
        super().__init__(
            organ_name="decision_making_organ",
            description="智能决策和策略选择",
            dependencies=["emotional_processing_organ"]
        )
        
        self.logger = logger
        self.decision_history = []
        self.decision_criteria = {
            "importance": 0.3,
            "urgency": 0.2,
            "emotional_impact": 0.2,
            "resource_cost": 0.15,
            "success_probability": 0.15
        }
        
        # 初始化决策系统
        self._init_decision_system()
        
        logger.info("🧠 林嫣然决策制定器官初始化完成")
    
    def _init_decision_system(self):
        """初始化决策系统"""
        try:
            # 决策类型
            self.decision_types = {
                "routine": {"weight": 0.1, "threshold": 0.3},
                "important": {"weight": 0.5, "threshold": 0.6},
                "critical": {"weight": 1.0, "threshold": 0.8},
                "emergency": {"weight": 1.5, "threshold": 0.9}
            }
            
            # 决策策略
            self.strategies = {
                "conservative": {"risk_tolerance": 0.2, "speed": 0.3},
                "balanced": {"risk_tolerance": 0.5, "speed": 0.5},
                "aggressive": {"risk_tolerance": 0.8, "speed": 0.8}
            }
            
            logger.success("🧠 决策系统初始化完成")
            
        except Exception as e:
            logger.error(f"🧠 决策系统初始化失败: {e}")
    
    def make_decision(self, context: Dict[str, Any], options: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        制定决策
        
        Args:
            context: 决策上下文
            options: 可选方案列表
            
        Returns:
            决策结果
        """
        try:
            if not options:
                return {
                    "success": False,
                    "error": "没有可选方案",
                    "decision": None
                }
            
            # 分析决策上下文
            decision_analysis = self._analyze_context(context)
            
            # 评估所有选项
            evaluated_options = []
            for option in options:
                score = self._evaluate_option(option, decision_analysis)
                evaluated_options.append({
                    "option": option,
                    "score": score,
                    "reasoning": self._generate_reasoning(option, score, decision_analysis)
                })
            
            # 选择最佳方案
            best_option = max(evaluated_options, key=lambda x: x["score"])
            
            # 记录决策
            decision_record = {
                "id": len(self.decision_history) + 1,
                "timestamp": datetime.now(),
                "context": context,
                "options": options,
                "chosen_option": best_option["option"],
                "score": best_option["score"],
                "reasoning": best_option["reasoning"],
                "decision_type": decision_analysis["type"]
            }
            
            self.decision_history.append(decision_record)
            
            logger.success(f"🧠 决策完成: {best_option['option'].get('name', '未命名方案')} (得分: {best_option['score']:.2f})")
            
            return {
                "success": True,
                "decision": best_option["option"],
                "score": best_option["score"],
                "reasoning": best_option["reasoning"],
                "alternatives": [opt for opt in evaluated_options if opt != best_option],
                "decision_id": decision_record["id"]
            }
            
        except Exception as e:
            logger.error(f"🧠 决策制定失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "decision": None
            }
    
    def _analyze_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """分析决策上下文"""
        try:
            # 获取情感状态
            emotional_state = self._get_emotional_context()
            
            # 分析紧急程度
            urgency = context.get("urgency", 0.5)
            
            # 分析重要程度
            importance = context.get("importance", 0.5)
            
            # 确定决策类型
            decision_type = "routine"
            if urgency > 0.8 or importance > 0.8:
                decision_type = "critical"
            elif urgency > 0.6 or importance > 0.6:
                decision_type = "important"
            elif urgency > 0.9:
                decision_type = "emergency"
            
            return {
                "urgency": urgency,
                "importance": importance,
                "type": decision_type,
                "emotional_context": emotional_state,
                "timestamp": datetime.now(),
                "resources_available": context.get("resources", {}),
                "constraints": context.get("constraints", [])
            }
            
        except Exception as e:
            logger.error(f"🧠 上下文分析失败: {e}")
            return {
                "urgency": 0.5,
                "importance": 0.5,
                "type": "routine",
                "emotional_context": {},
                "timestamp": datetime.now(),
                "resources_available": {},
                "constraints": []
            }
    
    def _get_emotional_context(self) -> Dict[str, Any]:
        """获取情感上下文"""
        try:
            # 尝试获取情感处理器官
            emotional_organ = singleton_manager.get_instance('emotional_processing_organ')
            if emotional_organ:
                return emotional_organ.get_emotional_state()
            else:
                return {"primary_emotion": "neutral", "intensity": 0.5}
        except Exception as e:
            logger.warning(f"🧠 获取情感上下文失败: {e}")
            return {"primary_emotion": "neutral", "intensity": 0.5}
    
    def _evaluate_option(self, option: Dict[str, Any], analysis: Dict[str, Any]) -> float:
        """评估选项"""
        try:
            score = 0.0
            
            # 基础评分
            base_score = option.get("base_score", 0.5)
            score += base_score * 0.3
            
            # 重要性评分
            importance_score = option.get("importance", 0.5)
            score += importance_score * self.decision_criteria["importance"]
            
            # 紧急性评分
            urgency_score = option.get("urgency", 0.5)
            score += urgency_score * self.decision_criteria["urgency"]
            
            # 情感影响评分
            emotional_impact = self._calculate_emotional_impact(option, analysis["emotional_context"])
            score += emotional_impact * self.decision_criteria["emotional_impact"]
            
            # 资源成本评分（成本越低分数越高）
            cost = option.get("cost", 0.5)
            cost_score = 1.0 - cost
            score += cost_score * self.decision_criteria["resource_cost"]
            
            # 成功概率评分
            success_prob = option.get("success_probability", 0.5)
            score += success_prob * self.decision_criteria["success_probability"]
            
            # 确保分数在0-1范围内
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            logger.error(f"🧠 选项评估失败: {e}")
            return 0.1
    
    def _calculate_emotional_impact(self, option: Dict[str, Any], emotional_context: Dict[str, Any]) -> float:
        """计算情感影响"""
        try:
            current_emotion = emotional_context.get("primary_emotion", "neutral")
            current_intensity = emotional_context.get("intensity", 0.5)
            
            option_emotion = option.get("emotional_effect", "neutral")
            option_intensity = option.get("emotional_intensity", 0.5)
            
            # 简单的情感匹配逻辑
            if current_emotion == option_emotion:
                return 0.8  # 情感一致性高
            elif current_emotion == "sad" and option_emotion == "happy":
                return 0.9  # 正面情感提升
            elif current_emotion == "happy" and option_emotion == "sad":
                return 0.2  # 避免负面情感
            else:
                return 0.5  # 中性影响
                
        except Exception as e:
            logger.error(f"🧠 计算情感影响失败: {e}")
            return 0.5
    
    def _generate_reasoning(self, option: Dict[str, Any], score: float, analysis: Dict[str, Any]) -> str:
        """生成决策推理"""
        try:
            reasoning_parts = []
            
            # 基础评价
            if score > 0.8:
                reasoning_parts.append("这是一个优秀的选择")
            elif score > 0.6:
                reasoning_parts.append("这是一个不错的选择")
            elif score > 0.4:
                reasoning_parts.append("这是一个可行的选择")
            else:
                reasoning_parts.append("这个选择存在一些问题")
            
            # 具体分析
            if option.get("importance", 0) > 0.7:
                reasoning_parts.append("重要性很高")
            
            if option.get("urgency", 0) > 0.7:
                reasoning_parts.append("紧急性很高")
            
            if option.get("cost", 1) < 0.3:
                reasoning_parts.append("成本较低")
            
            if option.get("success_probability", 0) > 0.7:
                reasoning_parts.append("成功概率较高")
            
            return "，".join(reasoning_parts) + "。"
            
        except Exception as e:
            logger.error(f"🧠 生成推理失败: {e}")
            return "决策分析完成。"
    
    def get_decision_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取决策历史"""
        return self.decision_history[-limit:]
    
    def update_criteria(self, new_criteria: Dict[str, float]) -> bool:
        """更新决策标准"""
        try:
            # 验证权重总和
            total_weight = sum(new_criteria.values())
            if abs(total_weight - 1.0) > 0.01:
                logger.warning(f"🧠 决策标准权重总和不为1: {total_weight}")
                return False
            
            self.decision_criteria.update(new_criteria)
            logger.success("🧠 决策标准已更新")
            return True
            
        except Exception as e:
            logger.error(f"🧠 更新决策标准失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取器官状态"""
        return {
            "organ_name": self.organ_name,
            "is_active": True,
            "decision_count": len(self.decision_history),
            "last_decision": self.decision_history[-1]["timestamp"].isoformat() if self.decision_history else None,
            "criteria": self.decision_criteria.copy(),
            "health_status": "healthy"
        }

# 工厂函数
def create_decision_making_organ() -> DecisionMakingOrgan:
    """创建决策制定器官实例"""
    return DecisionMakingOrgan()

# 向后兼容
def get_instance() -> DecisionMakingOrgan:
    """获取决策制定器官实例"""
    return singleton_manager.get_or_create(
        'decision_making_organ',
        create_decision_making_organ
    ) 