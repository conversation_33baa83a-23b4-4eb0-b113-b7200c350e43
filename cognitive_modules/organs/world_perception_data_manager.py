"""
世界感知器官数据持久化管理器
老王设计：让这个SB器官能真正学习和记忆，而不是重启就忘光
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from utilities.unified_logger import get_unified_logger
# 🔥 老王修复：使用统一数据库管理服务，遵循代码最大化复用原则
from utilities.database_manager import get_database as get_database_manager
from utilities.database_utils import execute_with_retry


@dataclass
class SignificantEvent:
    """重要事件数据类"""
    event_id: str
    title: str
    content: str = ""
    source_platform: str = ""
    category: str = ""
    emotion_tone: str = ""
    significance_score: float = 0.0
    yanran_interest_score: float = 0.0
    heat_value: int = 0
    url: str = ""
    keywords: List[str] = None
    ai_analysis: Dict[str, Any] = None
    perception_context: Dict[str, Any] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        if self.ai_analysis is None:
            self.ai_analysis = {}
        if self.perception_context is None:
            self.perception_context = {}
        if self.created_at is None:
            self.created_at = datetime.now()


class WorldPerceptionDataManager:
    """世界感知器官数据持久化管理器"""
    
    def __init__(self, db_config: Dict[str, Any] = None):
        self.logger = get_unified_logger("WorldPerceptionDataManager")

        # 🔥 老王修复：使用统一数据库管理器，不重复造轮子
        try:
            self.db_manager = get_database_manager()
            self.logger.debug(f"🔍 数据库管理器获取结果: {self.db_manager}")

            if self.db_manager:
                self.logger.debug(f"🔍 MySQL管理器: {self.db_manager.mysql_manager}")
                if self.db_manager.mysql_manager:
                    self.logger.debug(f"🔍 MySQL可用性: {self.db_manager.mysql_manager.is_available}")

                # 🔥 修复：正确检查MySQL可用性
                if self.db_manager.mysql_manager and self.db_manager.mysql_manager.is_available:
                    self.logger.info("✅ 统一数据库管理器连接成功")
                    # 确保表存在
                    self._ensure_tables_exist()
                else:
                    self.logger.error("❌ 统一数据库管理器MySQL不可用")
                    self.db_manager = None
            else:
                self.logger.error("❌ 无法获取统一数据库管理器")
                self.db_manager = None
        except Exception as e:
            self.logger.error(f"❌ 初始化统一数据库管理器失败: {e}")
            import traceback
            self.logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
            self.db_manager = None

        self.logger.info("🧠 世界感知数据管理器初始化完成")
    
    def _init_database(self):
        """初始化数据库连接 - 🔥 老王修复：已使用统一数据库管理器，此方法保留兼容性"""
        # 使用统一数据库管理器，不需要单独初始化连接
        pass
    
    def _ensure_tables_exist(self):
        """确保所需表存在 - 🔥 老王修复：使用统一数据库管理器"""
        try:
            if not self.db_manager:
                self.logger.warning("⚠️ 数据库管理器不可用，跳过表检查")
                return

            # 🔥 使用统一数据库管理器执行查询
            success, result, error = self.db_manager.execute_query("SHOW TABLES LIKE 'world_significant_events'")

            if success and result:
                self.logger.info("✅ 世界感知数据表检查通过")
            else:
                self.logger.warning("⚠️ 世界感知相关表不存在，请先执行 sql/world_perception_organ_tables.sql")

        except Exception as e:
            self.logger.error(f"❌ 检查数据表失败: {e}")
    
    def _generate_event_id(self, title: str, source_platform: str = "") -> str:
        """生成事件唯一ID"""
        content = f"{title}_{source_platform}_{datetime.now().strftime('%Y%m%d')}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def save_significant_event(self, event_data: Dict[str, Any]) -> bool:
        """保存重要事件 - 🔥 老王修复：使用统一数据库管理器"""
        try:
            if not self.db_manager:
                self.logger.error("❌ 数据库管理器不可用")
                return False

            # 生成事件ID
            event_id = self._generate_event_id(
                event_data.get('title', ''),
                event_data.get('source_platform', '')
            )

            # 准备数据 - 🔥 使用元组参数而不是字典
            insert_params = (
                event_id,
                event_data.get('title', ''),
                event_data.get('content', ''),
                event_data.get('source_platform', ''),
                event_data.get('category', ''),
                event_data.get('emotion_tone', ''),
                float(event_data.get('significance_score', 0.0)),
                float(event_data.get('yanran_interest_score', 0.0)),
                int(event_data.get('heat_value', 0)),
                event_data.get('url', ''),
                json.dumps(event_data.get('keywords', []), ensure_ascii=False),
                json.dumps(event_data.get('ai_analysis', {}), ensure_ascii=False),
                json.dumps(event_data.get('perception_context', {}), ensure_ascii=False)
            )

            # 插入或更新 - 🔥 使用统一数据库管理器
            sql = """
            INSERT INTO world_significant_events
            (event_id, title, content, source_platform, category, emotion_tone,
             significance_score, yanran_interest_score, heat_value, url, keywords, ai_analysis, perception_context)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                content = VALUES(content),
                category = VALUES(category),
                emotion_tone = VALUES(emotion_tone),
                significance_score = VALUES(significance_score),
                yanran_interest_score = VALUES(yanran_interest_score),
                heat_value = VALUES(heat_value),
                url = VALUES(url),
                keywords = VALUES(keywords),
                ai_analysis = VALUES(ai_analysis),
                perception_context = VALUES(perception_context),
                updated_at = CURRENT_TIMESTAMP
            """

            # 🔥 老王修复：INSERT语句应该使用execute_update，不是execute_query
            success, affected_rows, error = self.db_manager.execute_update(sql, insert_params)

            if success:
                self.logger.debug(f"✅ 保存重要事件: {event_data.get('title', '')[:50]}...")
                return True
            else:
                self.logger.error(f"❌ 保存重要事件失败: {error}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 保存重要事件异常: {e}")
            return False
    
    def save_world_state_snapshot(self, world_state: Dict[str, Any]) -> bool:
        """保存世界状态快照 - 🔥 老王修复：使用统一数据库管理器"""
        try:
            if not self.db_manager:
                self.logger.error("❌ 数据库管理器不可用")
                return False

            # 生成快照ID
            snapshot_id = hashlib.md5(
                f"world_state_{datetime.now().isoformat()}".encode('utf-8')
            ).hexdigest()

            # 准备数据 - 🔥 使用元组参数
            insert_params = (
                snapshot_id,
                datetime.now(),
                world_state.get('hot_topics_count', 0),
                world_state.get('news_count', 0),
                world_state.get('significant_events_count', 0),
                json.dumps(world_state, ensure_ascii=False),
                world_state.get('perception_summary', ''),
                json.dumps(world_state.get('top_categories', {}), ensure_ascii=False),
                json.dumps(world_state.get('emotion_distribution', {}), ensure_ascii=False)
            )

            sql = """
            INSERT INTO world_state_snapshots
            (snapshot_id, snapshot_time, hot_topics_count, news_count, significant_events_count,
             world_state_data, perception_summary, top_categories, emotion_distribution)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            # 🔥 老王修复：INSERT语句应该使用execute_update，不是execute_query
            success, affected_rows, error = self.db_manager.execute_update(sql, insert_params)

            if success:
                self.logger.debug(f"✅ 保存世界状态快照: {world_state.get('perception_summary', '')[:50]}...")
                return True
            else:
                self.logger.error(f"❌ 保存世界状态快照失败: {error}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 保存世界状态快照异常: {e}")
            return False
    
    def get_recent_significant_events(self, limit: int = 50, days: int = 7) -> List[Dict[str, Any]]:
        """获取最近的重要事件 - 🔥 老王修复：使用统一数据库管理器"""
        try:
            if not self.db_manager:
                self.logger.error("❌ 数据库管理器不可用")
                return []

            # 查询最近的重要事件
            sql = """
            SELECT * FROM world_significant_events
            WHERE created_at >= %s
            ORDER BY significance_score DESC, created_at DESC
            LIMIT %s
            """

            since_date = datetime.now() - timedelta(days=days)
            params = (since_date, limit)

            success, result, error = self.db_manager.execute_query(sql, params)

            if not success or not result:
                if error:
                    self.logger.error(f"❌ 获取重要事件失败: {error}")
                return []

            events = result

            # 解析JSON字段
            for event in events:
                try:
                    if event.get('keywords') and isinstance(event['keywords'], str):
                        event['keywords'] = json.loads(event['keywords'])
                    if event.get('ai_analysis') and isinstance(event['ai_analysis'], str):
                        event['ai_analysis'] = json.loads(event['ai_analysis'])
                    if event.get('perception_context') and isinstance(event['perception_context'], str):
                        event['perception_context'] = json.loads(event['perception_context'])
                except json.JSONDecodeError as json_e:
                    self.logger.warning(f"⚠️ JSON解析失败: {json_e}")

            self.logger.debug(f"✅ 获取最近重要事件: {len(events)}个")
            return events

        except Exception as e:
            self.logger.error(f"❌ 获取重要事件异常: {e}")
            return []
    
    def get_learning_patterns(self) -> Dict[str, Any]:
        """获取学习模式统计 - 🔥 老王修复：使用统一数据库管理器"""
        try:
            if not self.db_manager:
                self.logger.error("❌ 数据库管理器不可用")
                return {}

            # 统计最近7天的数据
            since_date = datetime.now() - timedelta(days=7)

            # 获取事件分类统计
            category_sql = """
                SELECT category, COUNT(*) as count, AVG(significance_score) as avg_score
                FROM world_significant_events
                WHERE created_at >= %s
                GROUP BY category
                ORDER BY count DESC
            """

            success1, category_stats, error1 = self.db_manager.execute_query(category_sql, (since_date,))
            if not success1:
                self.logger.error(f"❌ 获取分类统计失败: {error1}")
                category_stats = []

            # 获取情感色调统计
            emotion_sql = """
                SELECT emotion_tone, COUNT(*) as count, AVG(yanran_interest_score) as avg_interest
                FROM world_significant_events
                WHERE created_at >= %s
                GROUP BY emotion_tone
                ORDER BY count DESC
            """

            success2, emotion_stats, error2 = self.db_manager.execute_query(emotion_sql, (since_date,))
            if not success2:
                self.logger.error(f"❌ 获取情感统计失败: {error2}")
                emotion_stats = []

            patterns = {
                'category_preferences': category_stats or [],
                'emotion_patterns': emotion_stats or [],
                'analysis_time': datetime.now().isoformat()
            }

            self.logger.debug(f"✅ 获取学习模式: {len(category_stats or [])}个分类, {len(emotion_stats or [])}个情感模式")
            return patterns

        except Exception as e:
            self.logger.error(f"❌ 获取学习模式异常: {e}")
            return {}
    
    def update_interest_keywords(self, keywords: List[str], category: str = "auto_learned") -> bool:
        """更新兴趣关键词 - 🔥 老王修复：使用统一数据库管理器"""
        try:
            if not self.db_manager:
                self.logger.error("❌ 数据库管理器不可用")
                return False

            success_count = 0

            for keyword in keywords:
                # 插入或更新关键词
                sql = """
                INSERT INTO interest_keywords_evolution
                (keyword, category, hit_count, last_hit_time, auto_generated)
                VALUES (%s, %s, 1, %s, 1)
                ON DUPLICATE KEY UPDATE
                    hit_count = hit_count + 1,
                    last_hit_time = VALUES(last_hit_time),
                    updated_at = CURRENT_TIMESTAMP
                """

                params = (keyword, category, datetime.now())
                # 🔥 老王修复：INSERT/UPDATE语句应该使用execute_update，不是execute_query
                success, affected_rows, error = self.db_manager.execute_update(sql, params)

                if success:
                    success_count += 1
                else:
                    self.logger.warning(f"⚠️ 更新关键词失败: {keyword} - {error}")

            if success_count > 0:
                self.logger.debug(f"✅ 更新兴趣关键词: {success_count}/{len(keywords)}个成功")
                return True
            else:
                self.logger.error(f"❌ 所有关键词更新失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 更新兴趣关键词异常: {e}")
            return False
    
    def close(self):
        """关闭数据库连接 - 🔥 老王修复：使用统一数据库管理器"""
        # 统一数据库管理器会自动管理连接，不需要手动关闭
        if self.db_manager:
            self.logger.info("🔌 世界感知数据管理器已关闭（连接由统一管理器管理）")
        else:
            self.logger.info("🔌 世界感知数据管理器已关闭")


# 单例模式
_world_perception_data_manager = None

def get_world_perception_data_manager() -> WorldPerceptionDataManager:
    """获取世界感知数据管理器单例"""
    global _world_perception_data_manager
    if _world_perception_data_manager is None:
        _world_perception_data_manager = WorldPerceptionDataManager()
    return _world_perception_data_manager
