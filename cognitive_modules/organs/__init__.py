"""
林嫣然数字生命体器官系统
====================

器官化架构设计，将功能模块转化为数字生命体的智能器官。
每个器官具备基于林嫣然人格的AI决策能力。

器官系统架构：
- 器官基类：定义所有器官的通用接口和行为
- AI决策引擎：为每个器官提供智能决策能力  
- 器官神经网络：协调器官间的通信和协作
- 专用器官：各种功能的具体实现

设计原则：
1. 最小化改动：复用现有功能模块，不修改原有逻辑
2. 器官化设计：每个功能模块作为生命体的有机组成部分
3. AI驱动决策：基于林嫣然人格的智能决策机制
4. 统一人格：所有器官共享林嫣然的核心人格特质
"""

# 只导入已经创建的模块
from .base_organ import LifeOrgan
from .world_perception_organ import WorldPerceptionOrgan

# 暂时注释掉未创建的器官模块
# from .creative_expression_organ import CreativeExpressionOrgan
# from .safety_protection_organ import SafetyProtectionOrgan
# from .skill_coordination_organ import SkillCoordinationOrgan
# from .wealth_management_organ import WealthManagementOrgan
# from .data_perception_organ import DataPerceptionOrgan
# from .proactive_expression_organ import ProactiveExpressionOrgan
# from .relationship_coordination_organ import RelationshipCoordinationOrgan
# from .personalized_service_organ import PersonalizedServiceOrgan

__all__ = [
    'LifeOrgan',
    'WorldPerceptionOrgan'
    # 'CreativeExpressionOrgan',
    # 'SafetyProtectionOrgan',
    # 'SkillCoordinationOrgan',
    # 'WealthManagementOrgan',
    # 'DataPerceptionOrgan',
    # 'ProactiveExpressionOrgan',
    # 'RelationshipCoordinationOrgan',
    # 'PersonalizedServiceOrgan'
] 