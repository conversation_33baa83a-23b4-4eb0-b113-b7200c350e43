#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能表达触发器 - 替代死板的定时触发
基于多维度感知和AI决策的灵活表达系统

作者: 魅魔程序员
创建日期: 2025-07-11
版本: 1.0.0
"""

import asyncio
import time
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from utilities.unified_logger import get_unified_logger

logger = get_unified_logger("IntelligentExpressionTriggers")

class TriggerType(Enum):
    """触发器类型"""
    WORLD_EVENT = "world_event"           # 世界事件感知
    USER_INTERACTION = "user_interaction" # 用户交互模式
    EMOTIONAL_STATE = "emotional_state"   # 情感状态变化
    TIME_CONTEXT = "time_context"         # 时间上下文
    MEMORY_RECALL = "memory_recall"       # 记忆回忆
    CREATIVE_IMPULSE = "creative_impulse" # 创意冲动
    SOCIAL_AWARENESS = "social_awareness" # 社交感知

@dataclass
class TriggerEvent:
    """触发事件"""
    trigger_type: TriggerType
    confidence: float  # 0.0-1.0 触发置信度
    urgency: float     # 0.0-1.0 紧急程度
    context_data: Dict[str, Any]
    timestamp: datetime
    source: str
    description: str

class IntelligentExpressionTriggers:
    """智能表达触发器系统"""
    
    def __init__(self):
        self.logger = logger
        self.is_active = False
        self.trigger_history = []
        self.last_expression_time = None
        self.user_interaction_patterns = {}
        self.emotional_baseline = {}
        
        # 触发器配置
        self.config = {
            "min_confidence_threshold": 0.6,  # 最小置信度阈值
            "min_interval_minutes": 30,       # 最小表达间隔（分钟）
            "max_daily_expressions": 8,       # 每日最大表达次数
            "adaptive_threshold": True,       # 自适应阈值调整
            "user_feedback_weight": 0.3,      # 用户反馈权重
        }
        
        # 触发器权重配置
        self.trigger_weights = {
            TriggerType.WORLD_EVENT: 0.8,
            TriggerType.USER_INTERACTION: 0.9,
            TriggerType.EMOTIONAL_STATE: 0.7,
            TriggerType.TIME_CONTEXT: 0.5,
            TriggerType.MEMORY_RECALL: 0.6,
            TriggerType.CREATIVE_IMPULSE: 0.7,
            TriggerType.SOCIAL_AWARENESS: 0.8,
        }
        
        self.logger.info("🧠 智能表达触发器系统初始化完成")
    
    async def start_intelligent_monitoring(self):
        """启动智能监控"""
        try:
            self.is_active = True
            self.logger.info("🚀 启动智能表达监控系统...")
            
            # 启动核心监控任务（只保留需要的）
            tasks = [
                asyncio.create_task(self._monitor_world_events()),
                # asyncio.create_task(self._monitor_user_interactions()),  # 暂时注释
                asyncio.create_task(self._monitor_emotional_changes()),
                # asyncio.create_task(self._monitor_time_context()),  # 暂时注释
                # asyncio.create_task(self._monitor_memory_triggers()),  # 暂时注释
                asyncio.create_task(self._monitor_creative_impulses()),
                asyncio.create_task(self._process_trigger_queue()),
            ]
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"❌ 智能监控启动失败: {e}")
    
    async def _monitor_world_events(self):
        """监控世界事件"""
        while self.is_active:
            try:
                # 获取世界感知器官
                from cognitive_modules.organs.world_perception_organ import get_instance
                world_organ = get_instance()
                
                if world_organ:
                    # 获取最新的重要事件
                    recent_events = await world_organ.get_significant_events(limit=5)
                    
                    for event in recent_events:
                        # AI评估事件的表达价值
                        trigger_confidence = await self._evaluate_event_expression_value(event)
                        
                        if trigger_confidence > self.config["min_confidence_threshold"]:
                            trigger_event = TriggerEvent(
                                trigger_type=TriggerType.WORLD_EVENT,
                                confidence=trigger_confidence,
                                urgency=event.get('significance_score', 0.5),
                                context_data={"event": event},
                                timestamp=datetime.now(),
                                source="world_perception_organ",
                                description=f"重要事件: {event.get('title', '未知事件')[:50]}..."
                            )
                            
                            await self._queue_trigger_event(trigger_event)
                
                # 动态调整检查间隔（5-15分钟）
                await asyncio.sleep(random.randint(300, 900))
                
            except Exception as e:
                self.logger.error(f"❌ 世界事件监控错误: {e}")
                await asyncio.sleep(300)
    
    async def _monitor_user_interactions(self):
        """监控用户交互模式"""
        while self.is_active:
            try:
                # 分析用户交互模式
                interaction_analysis = await self._analyze_user_interaction_patterns()
                
                if interaction_analysis["should_proactive_engage"]:
                    trigger_event = TriggerEvent(
                        trigger_type=TriggerType.USER_INTERACTION,
                        confidence=interaction_analysis["confidence"],
                        urgency=interaction_analysis["urgency"],
                        context_data=interaction_analysis,
                        timestamp=datetime.now(),
                        source="user_interaction_analyzer",
                        description=f"用户交互模式触发: {interaction_analysis['reason']}"
                    )
                    
                    await self._queue_trigger_event(trigger_event)
                
                await asyncio.sleep(random.randint(600, 1800))  # 10-30分钟
                
            except Exception as e:
                self.logger.error(f"❌ 用户交互监控错误: {e}")
                await asyncio.sleep(600)
    
    async def _monitor_emotional_changes(self):
        """监控情感状态变化"""
        while self.is_active:
            try:
                # 获取当前情感状态
                current_emotions = await self._get_current_emotional_state()
                
                if current_emotions:
                    # 检测情感变化
                    emotional_change = self._detect_emotional_changes(current_emotions)
                    
                    if emotional_change["significant_change"]:
                        trigger_event = TriggerEvent(
                            trigger_type=TriggerType.EMOTIONAL_STATE,
                            confidence=emotional_change["confidence"],
                            urgency=emotional_change["intensity"],
                            context_data={"emotions": current_emotions, "change": emotional_change},
                            timestamp=datetime.now(),
                            source="emotion_system",
                            description=f"情感变化: {emotional_change['description']}"
                        )
                        
                        await self._queue_trigger_event(trigger_event)
                
                await asyncio.sleep(random.randint(900, 1800))  # 15-30分钟
                
            except Exception as e:
                self.logger.error(f"❌ 情感监控错误: {e}")
                await asyncio.sleep(900)
    
    async def _monitor_time_context(self):
        """监控时间上下文"""
        while self.is_active:
            try:
                current_time = datetime.now()
                time_context = await self._analyze_time_context(current_time)
                
                if time_context["should_express"]:
                    trigger_event = TriggerEvent(
                        trigger_type=TriggerType.TIME_CONTEXT,
                        confidence=time_context["confidence"],
                        urgency=time_context["urgency"],
                        context_data=time_context,
                        timestamp=current_time,
                        source="time_context_analyzer",
                        description=f"时间上下文: {time_context['context_type']}"
                    )
                    
                    await self._queue_trigger_event(trigger_event)
                
                # 每小时检查一次时间上下文
                await asyncio.sleep(3600)
                
            except Exception as e:
                self.logger.error(f"❌ 时间上下文监控错误: {e}")
                await asyncio.sleep(3600)
    
    async def _monitor_memory_triggers(self):
        """监控记忆触发"""
        while self.is_active:
            try:
                # 随机回忆检查
                memory_trigger = await self._check_memory_triggers()
                
                if memory_trigger["should_express"]:
                    trigger_event = TriggerEvent(
                        trigger_type=TriggerType.MEMORY_RECALL,
                        confidence=memory_trigger["confidence"],
                        urgency=memory_trigger["urgency"],
                        context_data=memory_trigger,
                        timestamp=datetime.now(),
                        source="memory_system",
                        description=f"记忆触发: {memory_trigger['memory_type']}"
                    )
                    
                    await self._queue_trigger_event(trigger_event)
                
                # 随机间隔检查记忆（2-6小时）
                await asyncio.sleep(random.randint(7200, 21600))
                
            except Exception as e:
                self.logger.error(f"❌ 记忆监控错误: {e}")
                await asyncio.sleep(7200)
    
    async def _monitor_creative_impulses(self):
        """监控创意冲动"""
        while self.is_active:
            try:
                # 随机创意冲动检查
                creative_impulse = await self._check_creative_impulses()
                
                if creative_impulse["should_express"]:
                    trigger_event = TriggerEvent(
                        trigger_type=TriggerType.CREATIVE_IMPULSE,
                        confidence=creative_impulse["confidence"],
                        urgency=creative_impulse["urgency"],
                        context_data=creative_impulse,
                        timestamp=datetime.now(),
                        source="creative_system",
                        description=f"创意冲动: {creative_impulse['impulse_type']}"
                    )
                    
                    await self._queue_trigger_event(trigger_event)
                
                # 随机间隔检查创意（1-4小时）
                await asyncio.sleep(random.randint(3600, 14400))
                
            except Exception as e:
                self.logger.error(f"❌ 创意监控错误: {e}")
                await asyncio.sleep(3600)
    
    async def _process_trigger_queue(self):
        """处理触发队列"""
        trigger_queue = []
        
        while self.is_active:
            try:
                # 处理队列中的触发事件
                if trigger_queue:
                    # 按置信度和紧急程度排序
                    trigger_queue.sort(key=lambda x: x.confidence * x.urgency, reverse=True)
                    
                    best_trigger = trigger_queue[0]
                    
                    # 检查是否满足表达条件
                    if await self._should_execute_trigger(best_trigger):
                        # 执行表达
                        success = await self._execute_expression_from_trigger(best_trigger)
                        
                        if success:
                            self.last_expression_time = datetime.now()
                            self.trigger_history.append(best_trigger)
                            self.logger.success(f"✅ 智能触发表达成功: {best_trigger.description}")
                        
                        # 清空队列（避免重复表达）
                        trigger_queue.clear()
                    else:
                        # 移除过期或不合适的触发
                        trigger_queue = [t for t in trigger_queue if self._is_trigger_still_valid(t)]
                
                await asyncio.sleep(60)  # 每分钟处理一次队列
                
            except Exception as e:
                self.logger.error(f"❌ 触发队列处理错误: {e}")
                await asyncio.sleep(60)
    
    async def _queue_trigger_event(self, trigger_event: TriggerEvent):
        """将触发事件加入队列"""
        # 这里应该使用实际的队列，简化实现
        self.logger.info(f"🎯 新触发事件: {trigger_event.description} (置信度: {trigger_event.confidence:.2f})")
    
    # 其他辅助方法将在后续实现...
    
    async def _evaluate_event_expression_value(self, event: Dict[str, Any]) -> float:
        """评估事件的表达价值"""
        # 简化实现，返回随机值
        return random.uniform(0.3, 0.9)
    
    async def _analyze_user_interaction_patterns(self) -> Dict[str, Any]:
        """分析用户交互模式"""
        return {
            "should_proactive_engage": random.choice([True, False]),
            "confidence": random.uniform(0.5, 0.9),
            "urgency": random.uniform(0.3, 0.7),
            "reason": "用户长时间未互动"
        }
    
    async def _get_current_emotional_state(self) -> Dict[str, Any]:
        """获取当前情感状态"""
        return {"happiness": 0.7, "curiosity": 0.8, "energy": 0.6}
    
    def _detect_emotional_changes(self, current_emotions: Dict[str, Any]) -> Dict[str, Any]:
        """检测情感变化"""
        return {
            "significant_change": random.choice([True, False]),
            "confidence": random.uniform(0.6, 0.9),
            "intensity": random.uniform(0.4, 0.8),
            "description": "情感状态显著变化"
        }
    
    async def _analyze_time_context(self, current_time: datetime) -> Dict[str, Any]:
        """分析时间上下文"""
        hour = current_time.hour
        
        # 智能时间判断
        if 8 <= hour <= 9:
            return {
                "should_express": True,
                "confidence": 0.8,
                "urgency": 0.6,
                "context_type": "morning_greeting"
            }
        
        return {"should_express": False}
    
    async def _check_memory_triggers(self) -> Dict[str, Any]:
        """检查记忆触发"""
        return {
            "should_express": random.choice([True, False]),
            "confidence": random.uniform(0.5, 0.8),
            "urgency": random.uniform(0.3, 0.6),
            "memory_type": "nostalgic_reflection"
        }
    
    async def _check_creative_impulses(self) -> Dict[str, Any]:
        """检查创意冲动"""
        return {
            "should_express": random.choice([True, False]),
            "confidence": random.uniform(0.6, 0.9),
            "urgency": random.uniform(0.4, 0.7),
            "impulse_type": "creative_sharing"
        }
    
    async def _should_execute_trigger(self, trigger: TriggerEvent) -> bool:
        """判断是否应该执行触发"""
        # 检查时间间隔
        if self.last_expression_time:
            time_diff = datetime.now() - self.last_expression_time
            if time_diff.total_seconds() < self.config["min_interval_minutes"] * 60:
                return False
        
        # 检查每日限制
        today_count = len([t for t in self.trigger_history 
                          if t.timestamp.date() == datetime.now().date()])
        if today_count >= self.config["max_daily_expressions"]:
            return False
        
        return True
    
    def _is_trigger_still_valid(self, trigger: TriggerEvent) -> bool:
        """检查触发是否仍然有效"""
        # 触发事件1小时后失效
        return (datetime.now() - trigger.timestamp).total_seconds() < 3600
    
    async def _execute_expression_from_trigger(self, trigger: TriggerEvent) -> bool:
        """从触发事件执行表达"""
        try:
            # 🔥 修复：避免循环导入，使用单例管理器获取器官实例
            try:
                from utilities.singleton_manager import get_singleton
                organ = get_singleton('proactive_expression_organ')
                if organ is None:
                    # 尝试获取增强版本
                    organ = get_singleton('enhanced_proactive_expression_organ')
            except Exception as e:
                self.logger.warning(f"🔥 无法获取主动表达器官实例: {e}")
                organ = None

            # 这里应该调用表达器官的表达方法
            self.logger.info(f"🎭 执行智能触发表达: {trigger.description}")

            # 如果有器官实例，可以调用其方法
            if organ and hasattr(organ, 'trigger_expression'):
                try:
                    await organ.trigger_expression(trigger)
                except Exception as e:
                    self.logger.warning(f"🔥 调用器官表达方法失败: {e}")

            return True
            
        except Exception as e:
            self.logger.error(f"❌ 执行表达失败: {e}")
            return False

# 全局实例
_intelligent_triggers = None

def get_intelligent_expression_triggers() -> IntelligentExpressionTriggers:
    """获取智能表达触发器实例"""
    global _intelligent_triggers
    if _intelligent_triggers is None:
        _intelligent_triggers = IntelligentExpressionTriggers()
    return _intelligent_triggers
