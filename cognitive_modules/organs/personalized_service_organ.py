"""
林嫣然个性化服务器官 - 服务编排优化
作为林嫣然的"服务编排中心"，提供个性化服务
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Set
from dataclasses import dataclass
from enum import Enum

from .base_organ import <PERSON><PERSON><PERSON>
from ..ai.yanran_decision_engine import YanranAIDecisionEngine
from utilities.singleton_manager import get, register

from utilities.unified_logger import get_unified_logger

class ServiceType(Enum):
    """服务类型枚举"""
    CONVERSATION = "conversation"
    CREATIVE_ASSISTANCE = "creative_assistance"
    INFORMATION_SERVICE = "information_service"
    EMOTIONAL_SUPPORT = "emotional_support"
    TASK_ASSISTANCE = "task_assistance"
    ENTERTAINMENT = "entertainment"
    LEARNING_SUPPORT = "learning_support"
    LIFESTYLE_GUIDANCE = "lifestyle_guidance"


class ServicePriority(Enum):
    """服务优先级枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    BACKGROUND = "background"


@dataclass
class UserProfile:
    """用户画像"""
    user_id: str
    preferences: Dict[str, Any]
    interaction_history: List[Dict]
    personality_traits: Dict[str, float]
    service_usage: Dict[str, int]
    feedback_scores: Dict[str, float]
    last_interaction: datetime
    total_interactions: int
    favorite_services: List[str]
    learning_style: str
    communication_style: str


@dataclass
class ServiceRequest:
    """服务请求"""
    request_id: str
    user_id: str
    service_type: ServiceType
    request_content: str
    priority: ServicePriority
    context: Dict[str, Any]
    created_at: datetime
    deadline: Optional[datetime]
    status: str  # pending, processing, completed, failed
    assigned_organs: List[str]
    result: Optional[Dict[str, Any]] = None


@dataclass
class ServicePlan:
    """服务计划"""
    plan_id: str
    service_request: ServiceRequest
    orchestration_steps: List[Dict[str, Any]]
    involved_organs: List[str]
    estimated_duration: int
    personalization_level: float
    quality_target: float
    created_at: datetime


class PersonalizedServiceOrgan(LifeOrgan):
    """林嫣然的个性化服务器官"""
    
    def __init__(self, ai_adapter=None):
        super().__init__(
            organ_name="personalized_service_organ",
            organ_function="个性化服务编排和用户体验优化",
            ai_adapter=ai_adapter
        )
        
        # 服务相关组件
        self.user_profiles = {}  # 用户画像
        self.service_requests = []  # 服务请求队列
        self.service_plans = []  # 服务计划
        self.service_history = []  # 服务历史
        self.personalization_models = {}  # 个性化模型
        
        # 服务配置
        self.service_config = self._load_service_config()
        
        # 服务编排器
        self.service_orchestrators = {
            ServiceType.CONVERSATION: self._orchestrate_conversation_service,
            ServiceType.CREATIVE_ASSISTANCE: self._orchestrate_creative_service,
            ServiceType.INFORMATION_SERVICE: self._orchestrate_information_service,
            ServiceType.EMOTIONAL_SUPPORT: self._orchestrate_emotional_service,
            ServiceType.TASK_ASSISTANCE: self._orchestrate_task_service,
            ServiceType.ENTERTAINMENT: self._orchestrate_entertainment_service,
            ServiceType.LEARNING_SUPPORT: self._orchestrate_learning_service,
            ServiceType.LIFESTYLE_GUIDANCE: self._orchestrate_lifestyle_service
        }
        
        # 性能统计
        self.service_stats = {
            "total_requests": 0,
            "completed_requests": 0,
            "failed_requests": 0,
            "avg_response_time": 0.0,
            "user_satisfaction": 0.0,
            "personalization_accuracy": 0.0,
            "service_quality": 0.0,
            "active_users": 0
        }
        
        self.logger = get_unified_logger(__name__)
        self.logger.info("🎯 林嫣然个性化服务器官初始化开始...")
        
        # 初始化服务系统
        self._init_service_system()
        
        self.logger.info("🎯 林嫣然个性化服务器官初始化完成")
    
    def _load_service_config(self) -> Dict[str, Any]:
        """加载服务配置"""
        try:
            config_path = os.path.join("config", "organs", "personalized_service_organ.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"🎯 服务配置加载失败: {e}")
        
        # 默认配置
        return {
            "service_mode": "adaptive",
            "max_concurrent_requests": 10,
            "service_timeout": 120,
            "personalization_level": 0.8,
            "quality_threshold": 0.75,
            "auto_learning": True,
            "yanran_service_style": {
                "warmth": 0.95,
                "professionalism": 0.85,
                "creativity": 0.8,
                "empathy": 0.9,
                "efficiency": 0.8,
                "adaptability": 0.85,
                "attention_to_detail": 0.9,
                "user_centricity": 0.95
            }
        }
    
    def _init_service_system(self):
        """初始化服务系统"""
        try:
            # 获取相关器官服务
            self._connect_to_organs()
            
            # 初始化个性化模型
            self._init_personalization_models()
            
            # 启动服务监控任务
            self._start_service_monitoring()
            
        except Exception as e:
            self.logger.warning(f"🎯 服务系统初始化部分失败: {e}")
    
    def _connect_to_organs(self):
        """连接到其他器官"""
        try:
            # 连接到各个器官
            organ_connections = {
                "world_perception": "world_perception_organ",
                "creative_expression": "creative_expression_organ",
                "safety_protection": "safety_protection_organ",
                "skill_coordination": "skill_coordination_organ",
                "wealth_management": "wealth_management_organ",
                "data_perception": "data_perception_organ",
                "proactive_expression": "proactive_expression_organ",
                "relationship_coordination": "relationship_coordination_organ"
            }
            
            self.connected_organs = {}
            for name, organ_id in organ_connections.items():
                organ = get(organ_id)
                if organ:
                    self.connected_organs[name] = organ
                    self.logger.info(f"🎯 已连接到 {name}")
                else:
                    # 🔥 对于主动表达器官，尝试连接增强版本
                    if name == "proactive_expression":
                        enhanced_organ = get("enhanced_proactive_expression_organ")
                        if enhanced_organ:
                            self.connected_organs[name] = enhanced_organ
                            self.logger.info(f"🎯 已连接到 {name} (增强版本)")
                        else:
                            self.logger.warning(f"🎯 未找到 {name}")
                    else:
                        self.logger.warning(f"🎯 未找到 {name}")
            
        except Exception as e:
            self.logger.error(f"🎯 连接器官失败: {e}")
    
    def _init_personalization_models(self):
        """初始化个性化模型"""
        try:
            # 初始化不同类型的个性化模型
            self.personalization_models = {
                "preference_model": self._create_preference_model(),
                "behavior_model": self._create_behavior_model(),
                "interaction_model": self._create_interaction_model(),
                "satisfaction_model": self._create_satisfaction_model()
            }
            
            self.logger.info("🎯 个性化模型初始化完成")
            
        except Exception as e:
            self.logger.error(f"🎯 个性化模型初始化失败: {e}")
    
    def _create_preference_model(self) -> Dict[str, Any]:
        """创建偏好模型"""
        return {
            "model_type": "preference_learning",
            "features": ["service_type", "interaction_style", "content_preference", "timing_preference"],
            "weights": {"service_type": 0.3, "interaction_style": 0.25, "content_preference": 0.25, "timing_preference": 0.2},
            "learning_rate": 0.1,
            "decay_factor": 0.95
        }
    
    def _create_behavior_model(self) -> Dict[str, Any]:
        """创建行为模型"""
        return {
            "model_type": "behavior_prediction",
            "features": ["usage_pattern", "response_time", "engagement_level", "feedback_pattern"],
            "weights": {"usage_pattern": 0.4, "response_time": 0.2, "engagement_level": 0.25, "feedback_pattern": 0.15},
            "prediction_window": 7,
            "confidence_threshold": 0.7
        }
    
    def _create_interaction_model(self) -> Dict[str, Any]:
        """创建交互模型"""
        return {
            "model_type": "interaction_optimization",
            "features": ["communication_style", "emotional_state", "context_awareness", "personalization_level"],
            "weights": {"communication_style": 0.3, "emotional_state": 0.3, "context_awareness": 0.25, "personalization_level": 0.15},
            "adaptation_rate": 0.2,
            "memory_length": 100
        }
    
    def _create_satisfaction_model(self) -> Dict[str, Any]:
        """创建满意度模型"""
        return {
            "model_type": "satisfaction_prediction",
            "features": ["service_quality", "response_time", "personalization_fit", "expectation_match"],
            "weights": {"service_quality": 0.35, "response_time": 0.2, "personalization_fit": 0.25, "expectation_match": 0.2},
            "satisfaction_threshold": 0.8,
            "improvement_suggestions": True
        }
    
    def _start_service_monitoring(self):
        """启动服务监控"""
        try:
            # 创建服务监控循环
            asyncio.create_task(self._service_monitoring_loop())
            self.logger.info("🎯 服务监控任务已启动")
        except Exception as e:
            self.logger.warning(f"🎯 服务监控任务启动失败: {e}")
    
    async def _service_monitoring_loop(self):
        """服务监控循环"""
        while True:
            try:
                # 处理服务请求
                await self._process_service_requests()
                
                # 更新用户画像
                await self._update_user_profiles()
                
                # 优化服务质量
                await self._optimize_service_quality()
                
                # 等待下次检查
                await asyncio.sleep(self.service_config.get("monitoring_interval", 30))
                
            except Exception as e:
                self.logger.error(f"🎯 服务监控循环错误: {e}")
                await asyncio.sleep(15)
    
    async def _process_service_requests(self):
        """处理服务请求"""
        try:
            # 获取待处理的请求
            pending_requests = [req for req in self.service_requests if req.status == "pending"]
            
            # 按优先级排序
            pending_requests.sort(key=lambda x: self._get_priority_score(x.priority), reverse=True)
            
            # 处理请求
            for request in pending_requests[:self.service_config.get("max_concurrent_requests", 10)]:
                await self._handle_service_request(request)
            
        except Exception as e:
            self.logger.error(f"🎯 处理服务请求失败: {e}")
    
    def _get_priority_score(self, priority: ServicePriority) -> int:
        """获取优先级分数"""
        priority_scores = {
            ServicePriority.CRITICAL: 10,
            ServicePriority.HIGH: 8,
            ServicePriority.MEDIUM: 5,
            ServicePriority.LOW: 3,
            ServicePriority.BACKGROUND: 1
        }
        return priority_scores.get(priority, 5)
    
    async def _handle_service_request(self, request: ServiceRequest):
        """处理服务请求"""
        try:
            request.status = "processing"
            
            # 获取用户画像
            user_profile = self._get_or_create_user_profile(request.user_id)
            
            # 创建个性化服务计划
            service_plan = await self._create_service_plan(request, user_profile)
            
            # 执行服务计划
            result = await self._execute_service_plan(service_plan)
            
            # 更新请求状态
            request.result = result
            request.status = "completed" if result.get("success", False) else "failed"
            
            # 更新统计信息
            self._update_service_stats(request, result)
            
            # 学习和优化
            await self._learn_from_service_result(request, result, user_profile)
            
            self.logger.info(f"🎯 服务请求完成: {request.request_id} - {request.status}")
            
        except Exception as e:
            request.status = "failed"
            request.result = {"error": str(e)}
            self.logger.error(f"🎯 处理服务请求失败: {e}")
    
    def _get_or_create_user_profile(self, user_id: str) -> UserProfile:
        """获取或创建用户画像"""
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = UserProfile(
                user_id=user_id,
                preferences={},
                interaction_history=[],
                personality_traits={},
                service_usage={},
                feedback_scores={},
                last_interaction=datetime.now(),
                total_interactions=0,
                favorite_services=[],
                learning_style="adaptive",
                communication_style="friendly"
            )
        return self.user_profiles[user_id]
    
    async def _create_service_plan(self, request: ServiceRequest, user_profile: UserProfile) -> ServicePlan:
        """创建服务计划"""
        try:
            # 使用AI决策创建个性化服务计划
            plan_decision = await self._ai_create_service_plan(request, user_profile)
            
            orchestration_steps = plan_decision.get("orchestration_steps", [])
            involved_organs = plan_decision.get("involved_organs", [])
            
            service_plan = ServicePlan(
                plan_id=f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.service_plans)}",
                service_request=request,
                orchestration_steps=orchestration_steps,
                involved_organs=involved_organs,
                estimated_duration=plan_decision.get("estimated_duration", 60),
                personalization_level=plan_decision.get("personalization_level", 0.8),
                quality_target=plan_decision.get("quality_target", 0.75),
                created_at=datetime.now()
            )
            
            self.service_plans.append(service_plan)
            
            return service_plan
            
        except Exception as e:
            self.logger.error(f"🎯 创建服务计划失败: {e}")
            # 返回默认计划
            return self._create_default_service_plan(request)
    
    async def _ai_create_service_plan(self, request: ServiceRequest, user_profile: UserProfile) -> Dict[str, Any]:
        """AI创建服务计划"""
        try:
            plan_context = {
                "service_request": {
                    "type": request.service_type.value,
                    "content": request.request_content,
                    "priority": request.priority.value,
                    "context": request.context
                },
                "user_profile": {
                    "preferences": user_profile.preferences,
                    "personality_traits": user_profile.personality_traits,
                    "service_usage": user_profile.service_usage,
                    "communication_style": user_profile.communication_style,
                    "learning_style": user_profile.learning_style
                },
                "available_organs": list(self.connected_organs.keys()),
                "yanran_service_style": self.service_config["yanran_service_style"],
                "service_config": self.service_config
            }
            
            ai_decision = await self.ai_decision_engine.decide(
                prompt=self._get_service_plan_prompt(),
                context=plan_context,
                decision_type="service_orchestration"
            )
            
            return self._parse_service_plan_decision(ai_decision)
            
        except Exception as e:
            self.logger.error(f"🎯 AI创建服务计划失败: {e}")
            return self._get_default_plan_decision(request)
    
    def _get_service_plan_prompt(self) -> str:
        """获取服务计划提示词"""
        return """
        你是林嫣然的个性化服务器官，负责为用户创建最佳的服务体验计划。

        林嫣然的服务特点：
        - 温暖贴心，始终以用户为中心
        - 专业高效，提供高质量服务
        - 富有创意，能够个性化定制
        - 善解人意，理解用户真正需求
        - 注重细节，追求完美体验
        - 适应性强，灵活调整服务方式

        请基于以下信息创建个性化服务计划：
        1. 分析用户的服务请求和真实需求
        2. 结合用户画像进行个性化定制
        3. 选择最合适的器官协作组合
        4. 设计高效的服务编排流程
        5. 确保服务质量和用户满意度

        返回JSON格式，包含：
        - orchestration_steps: 服务编排步骤
        - involved_organs: 参与的器官列表
        - estimated_duration: 预估执行时间
        - personalization_level: 个性化程度
        - quality_target: 质量目标
        - reasoning: 计划理由
        """
    
    def _parse_service_plan_decision(self, ai_decision) -> Dict[str, Any]:
        """解析服务计划决策"""
        try:
            if isinstance(ai_decision, str):
                # 简单解析
                return self._get_default_plan_decision_simple()
            
            return {
                "orchestration_steps": ai_decision.get("orchestration_steps", []),
                "involved_organs": ai_decision.get("involved_organs", []),
                "estimated_duration": ai_decision.get("estimated_duration", 60),
                "personalization_level": ai_decision.get("personalization_level", 0.8),
                "quality_target": ai_decision.get("quality_target", 0.75),
                "reasoning": ai_decision.get("reasoning", "")
            }
            
        except Exception as e:
            self.logger.error(f"🎯 解析服务计划决策失败: {e}")
            return self._get_default_plan_decision_simple()
    
    def _get_default_plan_decision(self, request: ServiceRequest) -> Dict[str, Any]:
        """获取默认计划决策"""
        # 根据服务类型选择默认器官
        organ_mapping = {
            ServiceType.CONVERSATION: ["proactive_expression"],
            ServiceType.CREATIVE_ASSISTANCE: ["creative_expression", "skill_coordination"],
            ServiceType.INFORMATION_SERVICE: ["world_perception", "data_perception"],
            ServiceType.EMOTIONAL_SUPPORT: ["proactive_expression", "safety_protection"],
            ServiceType.TASK_ASSISTANCE: ["skill_coordination", "relationship_coordination"],
            ServiceType.ENTERTAINMENT: ["creative_expression", "proactive_expression"],
            ServiceType.LEARNING_SUPPORT: ["world_perception", "skill_coordination"],
            ServiceType.LIFESTYLE_GUIDANCE: ["wealth_management", "world_perception"]
        }
        
        involved_organs = organ_mapping.get(request.service_type, ["proactive_expression"])
        
        return {
            "orchestration_steps": [{"organ": organ, "action": "process"} for organ in involved_organs],
            "involved_organs": involved_organs,
            "estimated_duration": 60,
            "personalization_level": 0.7,
            "quality_target": 0.75
        }
    
    def _get_default_plan_decision_simple(self) -> Dict[str, Any]:
        """获取简单默认计划决策"""
        return {
            "orchestration_steps": [{"organ": "proactive_expression", "action": "process"}],
            "involved_organs": ["proactive_expression"],
            "estimated_duration": 60,
            "personalization_level": 0.7,
            "quality_target": 0.75
        }
    
    def _create_default_service_plan(self, request: ServiceRequest) -> ServicePlan:
        """创建默认服务计划"""
        return ServicePlan(
            plan_id=f"default_plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            service_request=request,
            orchestration_steps=[{"organ": "proactive_expression", "action": "process"}],
            involved_organs=["proactive_expression"],
            estimated_duration=60,
            personalization_level=0.5,
            quality_target=0.6,
            created_at=datetime.now()
        )
    
    async def _execute_service_plan(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """执行服务计划"""
        try:
            # 根据服务类型选择编排器
            orchestrator = self.service_orchestrators.get(
                service_plan.service_request.service_type,
                self._orchestrate_default_service
            )
            
            # 执行服务编排
            result = await orchestrator(service_plan)
            
            return {
                "success": True,
                "service_result": result,
                "execution_time": datetime.now().isoformat(),
                "quality_score": self._calculate_quality_score(result),
                "personalization_score": service_plan.personalization_level
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "execution_time": datetime.now().isoformat()
            }
    
    # 服务编排器实现
    async def _orchestrate_conversation_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排对话服务"""
        try:
            # 获取主动表达器官
            expression_organ = self.connected_organs.get("proactive_expression")
            if expression_organ:
                # 调用表达器官处理对话
                result = {"conversation_handled": True, "response": "对话服务已处理"}
            else:
                result = {"conversation_handled": False, "error": "表达器官不可用"}
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _orchestrate_creative_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排创意服务"""
        try:
            # 获取创意表达器官
            creative_organ = self.connected_organs.get("creative_expression")
            if creative_organ:
                result = {"creative_service_handled": True, "creative_output": "创意服务已处理"}
            else:
                result = {"creative_service_handled": False, "error": "创意器官不可用"}
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _orchestrate_information_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排信息服务"""
        try:
            # 获取世界感知器官
            perception_organ = self.connected_organs.get("world_perception")
            if perception_organ:
                result = {"information_service_handled": True, "information": "信息服务已处理"}
            else:
                result = {"information_service_handled": False, "error": "感知器官不可用"}
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _orchestrate_emotional_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排情感支持服务"""
        try:
            # 获取主动表达器官和安全防护器官
            expression_organ = self.connected_organs.get("proactive_expression")
            safety_organ = self.connected_organs.get("safety_protection")
            
            if expression_organ and safety_organ:
                result = {"emotional_support_handled": True, "support": "情感支持服务已处理"}
            else:
                result = {"emotional_support_handled": False, "error": "相关器官不可用"}
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _orchestrate_task_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排任务协助服务"""
        try:
            # 获取技能协调器官
            skill_organ = self.connected_organs.get("skill_coordination")
            if skill_organ:
                result = {"task_assistance_handled": True, "assistance": "任务协助服务已处理"}
            else:
                result = {"task_assistance_handled": False, "error": "技能器官不可用"}
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _orchestrate_entertainment_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排娱乐服务"""
        try:
            # 获取创意表达器官
            creative_organ = self.connected_organs.get("creative_expression")
            if creative_organ:
                result = {"entertainment_handled": True, "entertainment": "娱乐服务已处理"}
            else:
                result = {"entertainment_handled": False, "error": "创意器官不可用"}
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _orchestrate_learning_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排学习支持服务"""
        try:
            # 获取世界感知器官和技能协调器官
            perception_organ = self.connected_organs.get("world_perception")
            skill_organ = self.connected_organs.get("skill_coordination")
            
            if perception_organ and skill_organ:
                result = {"learning_support_handled": True, "learning": "学习支持服务已处理"}
            else:
                result = {"learning_support_handled": False, "error": "相关器官不可用"}
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _orchestrate_lifestyle_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排生活方式指导服务"""
        try:
            # 获取财富管理器官
            wealth_organ = self.connected_organs.get("wealth_management")
            if wealth_organ:
                result = {"lifestyle_guidance_handled": True, "guidance": "生活方式指导服务已处理"}
            else:
                result = {"lifestyle_guidance_handled": False, "error": "财富器官不可用"}
            
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    async def _orchestrate_default_service(self, service_plan: ServicePlan) -> Dict[str, Any]:
        """编排默认服务"""
        try:
            return {"default_service_handled": True, "message": "默认服务已处理"}
            
        except Exception as e:
            return {"error": str(e)}
    
    def _calculate_quality_score(self, result: Dict[str, Any]) -> float:
        """计算质量分数"""
        try:
            # 简化的质量评分
            if result.get("error"):
                return 0.0
            
            # 根据服务结果计算质量分数
            base_score = 0.7
            if any(key.endswith("_handled") and result[key] for key in result.keys()):
                base_score = 0.8
            
            return base_score
            
        except Exception as e:
            self.logger.error(f"🎯 计算质量分数失败: {e}")
            return 0.5
    
    def _update_service_stats(self, request: ServiceRequest, result: Dict[str, Any]):
        """更新服务统计"""
        try:
            self.service_stats["total_requests"] += 1
            
            if result.get("success", False):
                self.service_stats["completed_requests"] += 1
            else:
                self.service_stats["failed_requests"] += 1
            
            # 更新质量分数
            quality_score = result.get("quality_score", 0.5)
            current_quality = self.service_stats["service_quality"]
            total_requests = self.service_stats["total_requests"]
            
            # 计算移动平均
            self.service_stats["service_quality"] = (current_quality * (total_requests - 1) + quality_score) / total_requests
            
        except Exception as e:
            self.logger.error(f"🎯 更新服务统计失败: {e}")
    
    async def _learn_from_service_result(self, request: ServiceRequest, result: Dict[str, Any], user_profile: UserProfile):
        """从服务结果中学习"""
        try:
            # 更新用户画像
            user_profile.total_interactions += 1
            user_profile.last_interaction = datetime.now()
            
            # 更新服务使用统计
            service_type = request.service_type.value
            if service_type not in user_profile.service_usage:
                user_profile.service_usage[service_type] = 0
            user_profile.service_usage[service_type] += 1
            
            # 记录交互历史
            interaction_record = {
                "timestamp": datetime.now().isoformat(),
                "service_type": service_type,
                "request_content": request.request_content[:100],  # 限制长度
                "result_quality": result.get("quality_score", 0.5),
                "success": result.get("success", False)
            }
            
            user_profile.interaction_history.append(interaction_record)
            
            # 限制历史记录长度
            if len(user_profile.interaction_history) > 100:
                user_profile.interaction_history = user_profile.interaction_history[-50:]
            
        except Exception as e:
            self.logger.error(f"🎯 从服务结果学习失败: {e}")
    
    async def _update_user_profiles(self):
        """更新用户画像"""
        try:
            # 简化的用户画像更新
            for user_id, profile in self.user_profiles.items():
                # 分析用户偏好
                self._analyze_user_preferences(profile)
                
        except Exception as e:
            self.logger.error(f"🎯 更新用户画像失败: {e}")
    
    def _analyze_user_preferences(self, user_profile: UserProfile):
        """分析用户偏好"""
        try:
            # 分析服务类型偏好
            if user_profile.service_usage:
                most_used_service = max(user_profile.service_usage, key=user_profile.service_usage.get)
                if most_used_service not in user_profile.favorite_services:
                    user_profile.favorite_services.append(most_used_service)
            
            # 限制收藏服务数量
            if len(user_profile.favorite_services) > 5:
                user_profile.favorite_services = user_profile.favorite_services[-5:]
            
        except Exception as e:
            self.logger.error(f"🎯 分析用户偏好失败: {e}")
    
    async def _optimize_service_quality(self):
        """优化服务质量"""
        try:
            # 分析服务质量
            current_quality = self.service_stats["service_quality"]
            
            if current_quality < self.service_config.get("quality_threshold", 0.75):
                # 触发质量优化
                await self._trigger_quality_optimization()
            
        except Exception as e:
            self.logger.error(f"🎯 优化服务质量失败: {e}")
    
    async def _trigger_quality_optimization(self):
        """触发质量优化"""
        try:
            # 请求关系协调器官进行优化
            coordination_organ = self.connected_organs.get("relationship_coordination")
            if coordination_organ and hasattr(coordination_organ, 'request_organ_coordination'):
                await coordination_organ.request_organ_coordination(
                    requesting_organ="personalized_service_organ",
                    target_organs=list(self.connected_organs.keys()),
                    coordination_purpose="Optimize service quality"
                )
            
        except Exception as e:
            self.logger.error(f"🎯 触发质量优化失败: {e}")
    
    # 公共API方法
    async def request_service(self, user_id: str, service_type: ServiceType, 
                            request_content: str, priority: ServicePriority = ServicePriority.MEDIUM,
                            context: Optional[Dict[str, Any]] = None) -> str:
        """请求服务"""
        try:
            request_id = f"req_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.service_requests)}"
            
            service_request = ServiceRequest(
                request_id=request_id,
                user_id=user_id,
                service_type=service_type,
                request_content=request_content,
                priority=priority,
                context=context or {},
                created_at=datetime.now(),
                deadline=None,
                status="pending",
                assigned_organs=[]
            )
            
            self.service_requests.append(service_request)
            
            self.logger.info(f"🎯 收到服务请求: {request_id}")
            return request_id
            
        except Exception as e:
            self.logger.error(f"🎯 请求服务失败: {e}")
            return ""
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计"""
        return {
            **self.service_stats,
            "pending_requests": len([req for req in self.service_requests if req.status == "pending"]),
            "processing_requests": len([req for req in self.service_requests if req.status == "processing"]),
            "total_users": len(self.user_profiles),
            "connected_organs": len(self.connected_organs)
        }
    
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定提示词"""
        return """
        你是林嫣然的个性化服务器官，负责为用户提供最佳的个性化服务体验。
        你的特点是温暖贴心、专业高效、富有创意、善解人意、注重细节、适应性强。
        """
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        try:
            decision_type = ai_decision.get("decision_type", "service")
            
            if decision_type == "service_orchestration":
                # 处理服务编排决策
                service_plan = ai_decision.get("service_plan", {})
                
                return {
                    "action": "service_orchestrated",
                    "service_plan": service_plan,
                    "timestamp": datetime.now().isoformat()
                }
            
            return {
                "action": "no_action",
                "reason": "No valid decision type"
            }
            
        except Exception as e:
            self.logger.error(f"🎯 执行决策失败: {e}")
            return {
                "action": "error",
                "error": str(e)
            }


def get_instance(ai_adapter=None) -> PersonalizedServiceOrgan:
    """获取个性化服务器官实例"""
    from utilities.singleton_manager import get_or_create
    return get_or_create("personalized_service_organ", lambda: PersonalizedServiceOrgan(ai_adapter)) 