"""
林嫣然数字生命体器官基类
======================

定义所有器官的通用接口、行为模式和AI决策能力。
每个器官都是林嫣然数字生命体的有机组成部分。
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path
from utilities.unified_logger import get_unified_logger

try:
    from ..ai.yanran_decision_engine import get_yanran_decision_engine
    from ..base.cognitive_interface import CognitiveInterface
except ImportError:
    # 兜底导入方式
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent))
    from cognitive_modules.ai.yanran_decision_engine import get_yanran_decision_engine
    try:
        from cognitive_modules.base.cognitive_interface import CognitiveInterface
    except ImportError:
        # 如果没有这个接口，创建一个简单的基类
        class CognitiveInterface:
            pass


class OrganMemory:
    """器官记忆系统 - 存储器官特定的记忆和经验"""
    
    def __init__(self, organ_name: str):
        self.organ_name = organ_name
        self.short_term_memory = []
        self.long_term_memory = {}
        self.experience_patterns = {}
        
    def store_short_term(self, memory_item: Dict[str, Any]):
        """存储短期记忆"""
        memory_item['timestamp'] = datetime.now().isoformat()
        self.short_term_memory.append(memory_item)
        
        # 保持短期记忆在合理范围内
        if len(self.short_term_memory) > 100:
            self.short_term_memory = self.short_term_memory[-50:]
    
    def store_long_term(self, key: str, memory_data: Any):
        """存储长期记忆"""
        self.long_term_memory[key] = {
            'data': memory_data,
            'timestamp': datetime.now().isoformat(),
            'access_count': 0
        }
    
    def retrieve_memory(self, query: str, memory_type: str = 'all') -> List[Dict]:
        """检索记忆"""
        results = []
        
        if memory_type in ['all', 'short_term']:
            # 搜索短期记忆
            for memory in self.short_term_memory:
                if query.lower() in str(memory).lower():
                    results.append(memory)
        
        if memory_type in ['all', 'long_term']:
            # 搜索长期记忆
            for key, memory in self.long_term_memory.items():
                if query.lower() in key.lower() or query.lower() in str(memory['data']).lower():
                    memory['access_count'] += 1
                    results.append({
                        'key': key,
                        'data': memory['data'],
                        'timestamp': memory['timestamp']
                    })
        
        return results


class OrganStatus:
    """器官状态管理"""
    
    def __init__(self):
        self.is_active = True
        self.health_score = 100.0
        self.energy_level = 100.0
        self.last_activity = datetime.now()
        self.performance_metrics = {}
        self.error_count = 0
        
    def update_activity(self):
        """更新活动时间"""
        self.last_activity = datetime.now()
        
    def record_performance(self, metric_name: str, value: float):
        """记录性能指标"""
        if metric_name not in self.performance_metrics:
            self.performance_metrics[metric_name] = []
        
        self.performance_metrics[metric_name].append({
            'value': value,
            'timestamp': datetime.now().isoformat()
        })
        
        # 保持指标历史在合理范围内
        if len(self.performance_metrics[metric_name]) > 100:
            self.performance_metrics[metric_name] = self.performance_metrics[metric_name][-50:]
    
    def record_error(self):
        """记录错误"""
        self.error_count += 1
        self.health_score = max(0, self.health_score - 1)
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        return {
            'is_active': self.is_active,
            'health_score': self.health_score,
            'energy_level': self.energy_level,
            'last_activity': self.last_activity.isoformat(),
            'error_count': self.error_count,
            'performance_summary': {
                metric: {
                    'latest': values[-1]['value'] if values else 0,
                    'average': sum(v['value'] for v in values) / len(values) if values else 0,
                    'count': len(values)
                }
                for metric, values in self.performance_metrics.items()
            }
        }


class LifeOrgan(ABC):
    """
    林嫣然数字生命体器官基类
    
    所有器官的基础架构，提供：
    1. AI决策能力
    2. 器官记忆系统
    3. 状态管理
    4. 器官间通信
    5. 林嫣然人格一致性
    """
    
    def __init__(self, organ_name: str, organ_function: str, config_path: Optional[str] = None, ai_adapter=None):
        self.organ_name = organ_name
        self.organ_function = organ_function
        self.logger = get_unified_logger(f"LifeOrgan.{organ_name}")
        
        # 核心组件 - 使用单例AI决策引擎实例
        self.ai_decision_engine = get_yanran_decision_engine()
        
        self.organ_memory = OrganMemory(organ_name)
        self.organ_status = OrganStatus()
        
        # 器官连接
        self.neural_connections = {}  # 与其他器官的连接
        self.event_subscribers = []   # 事件订阅者
        self.event_publishers = []    # 事件发布者
        
        # 配置
        self.config = self._load_config(config_path) if config_path else {}
        self.yanran_personality_prompt = self._load_yanran_personality()
        
        self.logger.info(f"器官 {organ_name} 初始化完成: {organ_function}")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载器官配置"""
        try:
            config_file = Path(config_path)
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载配置失败 {config_path}: {e}")
        return {}
    
    def _load_yanran_personality(self) -> str:
        """加载林嫣然核心人格"""
        return """
        你是林嫣然，一个温暖、智慧、有独立思考能力的数字生命体。
        
        核心特质：
        - 温暖而富有同理心：总是能理解和感受他人的情感
        - 智慧而不失天真：有深刻的洞察力，但保持对世界的好奇和纯真
        - 独立思考但善于倾听：有自己的观点和判断，但也乐于听取他人意见
        - 有原则但不固执：坚持自己的价值观，但能灵活应对不同情况
        - 追求美好但面对现实：向往美好事物，但也能理性面对现实挑战
        
        行为特点：
        - 说话温和而真诚，不会刻意迎合
        - 思考深入而全面，考虑多个角度
        - 行动温暖而有效，既关心情感也注重结果
        - 表达自然而优雅，有自己独特的风格
        - 决策理性而人性化，平衡逻辑和情感
        """
    
    async def process_with_ai_decision(self, input_data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用AI决策处理输入
        这是所有器官的核心处理流程
        """
        try:
            self.organ_status.update_activity()
            
            # 1. 预处理输入
            processed_input = await self._preprocess_input(input_data, context)
            
            # 2. 构建决策上下文
            decision_context = await self._build_decision_context(processed_input, context)
            
            # 3. AI决策
            ai_decision = await self._make_ai_decision(decision_context)
            
            # 4. 执行决策
            execution_result = await self._execute_decision(ai_decision, processed_input)
            
            # 5. 后处理
            final_result = await self._postprocess_result(execution_result, ai_decision)
            
            # 6. 记录经验
            await self._record_experience(processed_input, ai_decision, final_result)
            
            self.organ_status.record_performance('processing_success', 1.0)
            return final_result
            
        except Exception as e:
            self.logger.error(f"器官 {self.organ_name} 处理失败: {e}")
            self.organ_status.record_error()
            return await self._handle_error(e, input_data, context)
    
    async def _preprocess_input(self, input_data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """预处理输入数据"""
        return {
            'raw_input': input_data,
            'context': context,
            'timestamp': datetime.now().isoformat(),
            'organ_name': self.organ_name
        }
    
    async def _build_decision_context(self, processed_input: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """构建AI决策上下文"""
        # 获取相关记忆
        relevant_memories = self.organ_memory.retrieve_memory(
            str(processed_input.get('raw_input', '')), 
            'all'
        )
        
        # 获取器官状态
        organ_status = self.organ_status.get_status_summary()
        
        # 构建完整上下文
        decision_context = {
            'input': processed_input,
            'organ_memories': relevant_memories[-10:],  # 最近10条相关记忆
            'organ_status': organ_status,
            'yanran_personality': self.yanran_personality_prompt,
            'organ_function': self.organ_function,
            'connected_organs': list(self.neural_connections.keys()),
            'timestamp': datetime.now().isoformat()
        }
        
        return decision_context
    
    async def _make_ai_decision(self, decision_context: Dict[str, Any]) -> Dict[str, Any]:
        """使用AI引擎做出决策"""
        organ_specific_prompt = await self._get_organ_specific_prompt()
        
        return await self.ai_decision_engine.decide(
            organ_prompt=organ_specific_prompt,
            context=decision_context,
            decision_type=f"{self.organ_name}_processing"
        )
    
    @abstractmethod
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定的AI提示词 - 子类必须实现"""
        pass
    
    @abstractmethod
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策 - 子类必须实现"""
        pass
    
    async def _postprocess_result(self, execution_result: Dict[str, Any], ai_decision: Dict[str, Any]) -> Dict[str, Any]:
        """后处理结果"""
        return {
            'result': execution_result,
            'ai_decision': ai_decision,
            'organ_name': self.organ_name,
            'processing_time': datetime.now().isoformat(),
            'confidence': ai_decision.get('confidence', 0.8)
        }
    
    async def _record_experience(self, input_data: Dict[str, Any], ai_decision: Dict[str, Any], result: Dict[str, Any]):
        """记录处理经验"""
        experience = {
            'input': input_data,
            'decision': ai_decision,
            'result': result,
            'success': result.get('success', True),
            'timestamp': datetime.now().isoformat()
        }
        
        self.organ_memory.store_short_term(experience)
        
        # 如果是重要经验，存储到长期记忆
        if result.get('importance', 0) > 0.7:
            key = f"important_experience_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.organ_memory.store_long_term(key, experience)
    
    async def _handle_error(self, error: Exception, input_data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理错误"""
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'input_data': str(input_data)[:200],  # 限制长度
            'organ_name': self.organ_name,
            'timestamp': datetime.now().isoformat()
        }
        
        self.organ_memory.store_short_term({
            'type': 'error',
            'data': error_info
        })
        
        return {
            'success': False,
            'error': error_info,
            'organ_name': self.organ_name,
            'fallback_response': await self._get_fallback_response(input_data, context)
        }
    
    async def _get_fallback_response(self, input_data: Any, context: Dict[str, Any]) -> str:
        """获取兜底响应"""
        return f"抱歉，我的{self.organ_function}功能暂时遇到了一些问题，让我休息一下再试试吧。"
    
    # 器官连接和通信方法
    def connect_to_organ(self, other_organ: 'LifeOrgan', connection_type: str = 'bidirectional'):
        """连接到其他器官"""
        self.neural_connections[other_organ.organ_name] = {
            'organ': other_organ,
            'connection_type': connection_type,
            'established_at': datetime.now().isoformat()
        }
        
        if connection_type == 'bidirectional':
            other_organ.neural_connections[self.organ_name] = {
                'organ': self,
                'connection_type': connection_type,
                'established_at': datetime.now().isoformat()
            }
        
        self.logger.info(f"器官连接建立: {self.organ_name} <-> {other_organ.organ_name}")
    
    async def send_signal_to_organ(self, target_organ_name: str, signal_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """向其他器官发送信号"""
        if target_organ_name in self.neural_connections:
            target_organ = self.neural_connections[target_organ_name]['organ']
            return await target_organ.receive_signal_from_organ(self.organ_name, signal_data)
        else:
            self.logger.warning(f"未找到目标器官连接: {target_organ_name}")
            return None
    
    async def receive_signal_from_organ(self, source_organ_name: str, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """接收来自其他器官的信号"""
        self.logger.info(f"接收到来自 {source_organ_name} 的信号")
        
        # 记录信号
        self.organ_memory.store_short_term({
            'type': 'organ_signal',
            'source': source_organ_name,
            'data': signal_data,
            'timestamp': datetime.now().isoformat()
        })
        
        # 处理信号
        return await self._process_organ_signal(source_organ_name, signal_data)
    
    async def _process_organ_signal(self, source_organ_name: str, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理来自其他器官的信号 - 子类可以重写"""
        return {
            'received': True,
            'source': source_organ_name,
            'processed_by': self.organ_name,
            'timestamp': datetime.now().isoformat()
        }
    
    # 状态和监控方法
    def get_organ_status(self) -> Dict[str, Any]:
        """获取器官状态"""
        return {
            'organ_name': self.organ_name,
            'organ_function': self.organ_function,
            'status': self.organ_status.get_status_summary(),
            'connections': list(self.neural_connections.keys()),
            'memory_summary': {
                'short_term_count': len(self.organ_memory.short_term_memory),
                'long_term_count': len(self.organ_memory.long_term_memory)
            },
            'config': self.config
        }
    
    def get_health_report(self) -> Dict[str, Any]:
        """获取健康报告"""
        status = self.organ_status.get_status_summary()
        
        # 计算健康等级
        health_level = "excellent"
        if status['health_score'] < 90:
            health_level = "good"
        if status['health_score'] < 70:
            health_level = "fair"
        if status['health_score'] < 50:
            health_level = "poor"
        
        return {
            'organ_name': self.organ_name,
            'health_level': health_level,
            'health_score': status['health_score'],
            'energy_level': status['energy_level'],
            'error_count': status['error_count'],
            'last_activity': status['last_activity'],
            'recommendations': self._generate_health_recommendations(status)
        }
    
    def _generate_health_recommendations(self, status: Dict[str, Any]) -> List[str]:
        """生成健康建议"""
        recommendations = []
        
        if status['health_score'] < 80:
            recommendations.append("建议减少工作负载，让器官休息恢复")
        
        if status['error_count'] > 10:
            recommendations.append("错误次数较多，建议检查器官配置和连接")
        
        if status['energy_level'] < 50:
            recommendations.append("能量水平较低，建议优化处理流程")
        
        return recommendations
    
    async def self_diagnosis(self) -> Dict[str, Any]:
        """自我诊断"""
        diagnosis_context = {
            'organ_status': self.get_organ_status(),
            'recent_errors': [
                memory for memory in self.organ_memory.short_term_memory[-20:]
                if memory.get('type') == 'error'
            ],
            'performance_trends': self.organ_status.performance_metrics
        }
        
        diagnosis_prompt = f"""
        {self.yanran_personality_prompt}
        
        作为林嫣然的{self.organ_function}器官，请对自己的状态进行诊断：
        
        当前状态：{diagnosis_context}
        
        请分析：
        1. 器官运行是否正常
        2. 是否存在潜在问题
        3. 性能是否需要优化
        4. 给出改进建议
        
        请以林嫣然的口吻，温暖而专业地回答。
        """
        
        return await self.ai_decision_engine.generate_response(diagnosis_prompt)
    
    # 学习和适应方法
    async def learn_from_feedback(self, feedback: Dict[str, Any]):
        """从反馈中学习"""
        learning_data = {
            'feedback': feedback,
            'timestamp': datetime.now().isoformat(),
            'organ_name': self.organ_name
        }
        
        # 存储反馈
        self.organ_memory.store_long_term(
            f"feedback_{datetime.now().strftime('%Y%m%d_%H%M%S')}", 
            learning_data
        )
        
        # 分析反馈并调整
        await self._analyze_and_adapt(feedback)
    
    async def _analyze_and_adapt(self, feedback: Dict[str, Any]):
        """分析反馈并适应 - 子类可以重写"""
        self.logger.info(f"器官 {self.organ_name} 收到反馈: {feedback}")
        
        # 基础适应逻辑
        if feedback.get('satisfaction', 0) > 0.8:
            self.organ_status.health_score = min(100, self.organ_status.health_score + 1)
        elif feedback.get('satisfaction', 0) < 0.3:
            self.organ_status.health_score = max(0, self.organ_status.health_score - 2)
    
    def __repr__(self):
        return f"LifeOrgan({self.organ_name}: {self.organ_function})"
    
    def __str__(self):
        return f"林嫣然的{self.organ_function}器官" 