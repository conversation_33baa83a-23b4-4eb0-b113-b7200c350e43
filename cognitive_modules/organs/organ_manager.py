#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
器官管理器

统一管理所有器官系统，提供器官协调和状态监控功能

作者: Claude
创建日期: 2025-06-30
版本: 1.0
"""

import os
import sys
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Set
from enum import Enum

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from utilities.unified_logger import get_unified_logger, setup_unified_logging

# 配置日志
setup_unified_logging()
logger = get_unified_logger("organ_manager")

class OrganStatus(Enum):
    """器官状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    INITIALIZING = "initializing"
    SHUTTING_DOWN = "shutting_down"

class OrganPriority(Enum):
    """器官优先级"""
    CRITICAL = 1    # 关键器官（如感知器官）
    HIGH = 2        # 高优先级（如创意表达器官）
    MEDIUM = 3      # 中等优先级（如个性化服务器官）
    LOW = 4         # 低优先级（如辅助器官）

class OrganManager:
    """器官管理器"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'OrganManager':
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = OrganManager()
        return cls._instance
    
    def __init__(self):
        """初始化器官管理器"""
        self.organs: Dict[str, Dict[str, Any]] = {}
        self.organ_instances: Dict[str, Any] = {}
        self.is_initialized = False
        self.startup_order: List[str] = []
        self.shutdown_order: List[str] = []
        
        # 器官配置映射
        self.organ_configs = {
            "creative_expression_organ": {
                "module_path": "cognitive_modules.organs.creative_expression_organ",
                "priority": OrganPriority.HIGH,
                "dependencies": [],
                "description": "创意表达器官"
            },
            "data_perception_organ": {
                "module_path": "cognitive_modules.organs.data_perception_organ", 
                "priority": OrganPriority.CRITICAL,
                "dependencies": [],
                "description": "数据感知器官"
            },
            "world_perception_organ": {
                "module_path": "cognitive_modules.organs.world_perception_organ",
                "priority": OrganPriority.CRITICAL,
                "dependencies": [],
                "description": "世界感知器官",
                "singleton": True  # 🔥 P0级别修复：标记为单例
            },
            "personalized_service_organ": {
                "module_path": "cognitive_modules.organs.personalized_service_organ",
                "priority": OrganPriority.MEDIUM,
                "dependencies": ["data_perception_organ"],
                "description": "个性化服务器官"
            },
            "proactive_expression_organ": {
                "module_path": "cognitive_modules.organs.proactive_expression_organ",
                "priority": OrganPriority.HIGH,
                "dependencies": ["creative_expression_organ"],
                "description": "主动表达器官"
            },
            "social_interaction_organ": {
                "module_path": "cognitive_modules.organs.social_interaction_organ",
                "priority": OrganPriority.HIGH,
                "dependencies": ["personalized_service_organ"],
                "description": "社交互动器官"
            },
            "learning_adaptation_organ": {
                "module_path": "cognitive_modules.organs.learning_adaptation_organ",
                "priority": OrganPriority.MEDIUM,
                "dependencies": ["data_perception_organ"],
                "description": "学习适应器官"
            },
            "emotional_processing_organ": {
                "module_path": "cognitive_modules.organs.emotional_processing_organ",
                "priority": OrganPriority.HIGH,
                "dependencies": [],
                "description": "情感处理器官"
            },
            "decision_making_organ": {
                "module_path": "cognitive_modules.organs.decision_making_organ",
                "priority": OrganPriority.CRITICAL,
                "dependencies": ["emotional_processing_organ", "data_perception_organ"],
                "description": "决策制定器官"
            }
        }
        
        # 自动初始化
        self.initialize()
    
    def initialize(self) -> bool:
        """初始化器官管理器"""
        try:
            logger.info("器官管理器开始初始化...")
            
            # 注册所有器官
            for organ_name, config in self.organ_configs.items():
                self.register_organ(organ_name, config)
            
            # 计算启动顺序
            self._calculate_startup_order()
            
            # 启动所有器官
            self._startup_all_organs()
            
            self.is_initialized = True
            logger.success(f"器官管理器初始化完成，管理 {len(self.organs)} 个器官")
            return True
            
        except Exception as e:
            logger.error(f"器官管理器初始化失败: {e}")
            return False
    
    def register_organ(self, organ_name: str, config: Dict[str, Any]) -> bool:
        """注册器官"""
        try:
            self.organs[organ_name] = {
                "name": organ_name,
                "module_path": config["module_path"],
                "priority": config["priority"],
                "dependencies": config.get("dependencies", []),
                "description": config.get("description", ""),
                "status": OrganStatus.INACTIVE,
                "instance": None,
                "last_activity": None,
                "error_count": 0,
                "startup_time": None
            }
            
            logger.info(f"器官已注册: {organ_name} - {config.get('description', '')}")
            return True
            
        except Exception as e:
            logger.error(f"注册器官 {organ_name} 失败: {e}")
            return False
    
    def _calculate_startup_order(self):
        """计算器官启动顺序（基于依赖关系和优先级）"""
        try:
            # 拓扑排序算法处理依赖关系
            visited = set()
            temp_visited = set()
            order = []
            
            def visit(organ_name: str):
                if organ_name in temp_visited:
                    raise ValueError(f"检测到循环依赖: {organ_name}")
                if organ_name in visited:
                    return
                
                temp_visited.add(organ_name)
                
                # 先访问依赖的器官
                dependencies = self.organs[organ_name]["dependencies"]
                for dep in dependencies:
                    if dep in self.organs:
                        visit(dep)
                
                temp_visited.remove(organ_name)
                visited.add(organ_name)
                order.append(organ_name)
            
            # 按优先级排序器官列表
            organ_list = sorted(
                self.organs.keys(),
                key=lambda x: self.organs[x]["priority"].value
            )
            
            # 计算启动顺序
            for organ_name in organ_list:
                if organ_name not in visited:
                    visit(organ_name)
            
            self.startup_order = order
            self.shutdown_order = list(reversed(order))  # 关闭顺序与启动顺序相反
            
            logger.info(f"器官启动顺序: {' -> '.join(self.startup_order)}")
            
        except Exception as e:
            logger.error(f"计算器官启动顺序失败: {e}")
            # 回退到简单的优先级排序
            self.startup_order = sorted(
                self.organs.keys(),
                key=lambda x: self.organs[x]["priority"].value
            )
            self.shutdown_order = list(reversed(self.startup_order))
    
    def _startup_all_organs(self):
        """启动所有器官"""
        for organ_name in self.startup_order:
            self.startup_organ(organ_name)
    
    def startup_organ(self, organ_name: str) -> bool:
        """启动指定器官"""
        try:
            if organ_name not in self.organs:
                logger.error(f"器官不存在: {organ_name}")
                return False
            
            organ_info = self.organs[organ_name]
            
            # 检查状态
            if organ_info["status"] == OrganStatus.ACTIVE:
                logger.info(f"器官 {organ_name} 已经处于活跃状态")
                return True
            
            # 设置初始化状态
            organ_info["status"] = OrganStatus.INITIALIZING
            
            # 检查依赖
            for dep in organ_info["dependencies"]:
                if dep in self.organs and self.organs[dep]["status"] != OrganStatus.ACTIVE:
                    logger.warning(f"器官 {organ_name} 的依赖 {dep} 未启动")
            
            # 动态导入器官模块
            module_path = organ_info["module_path"]
            try:
                # 尝试导入模块并获取实例
                module = __import__(module_path, fromlist=[''])
                
                # 尝试不同的实例获取方法
                instance = None
                if hasattr(module, 'get_instance'):
                    instance = module.get_instance()
                elif hasattr(module, 'create_instance'):
                    instance = module.create_instance()
                elif hasattr(module, 'getInstance'):
                    instance = module.getInstance()
                
                if instance:
                    organ_info["instance"] = instance
                    self.organ_instances[organ_name] = instance
                    organ_info["status"] = OrganStatus.ACTIVE
                    organ_info["startup_time"] = datetime.now()
                    organ_info["last_activity"] = datetime.now()
                    
                    logger.success(f"器官 {organ_name} 启动成功")
                    return True
                else:
                    logger.warning(f"器官 {organ_name} 无法获取实例，标记为非活跃状态")
                    organ_info["status"] = OrganStatus.INACTIVE
                    return False
                    
            except ImportError as e:
                logger.warning(f"器官 {organ_name} 模块导入失败: {e}")
                organ_info["status"] = OrganStatus.ERROR
                organ_info["error_count"] += 1
                return False
            except Exception as e:
                logger.error(f"器官 {organ_name} 启动异常: {e}")
                organ_info["status"] = OrganStatus.ERROR
                organ_info["error_count"] += 1
                return False
                
        except Exception as e:
            logger.error(f"启动器官 {organ_name} 失败: {e}")
            return False
    
    def shutdown_organ(self, organ_name: str) -> bool:
        """关闭指定器官"""
        try:
            if organ_name not in self.organs:
                logger.error(f"器官不存在: {organ_name}")
                return False
            
            organ_info = self.organs[organ_name]
            
            if organ_info["status"] != OrganStatus.ACTIVE:
                logger.info(f"器官 {organ_name} 已经非活跃")
                return True
            
            organ_info["status"] = OrganStatus.SHUTTING_DOWN
            
            # 尝试优雅关闭器官实例
            instance = organ_info["instance"]
            if instance and hasattr(instance, 'shutdown'):
                try:
                    instance.shutdown()
                except Exception as e:
                    logger.warning(f"器官 {organ_name} 优雅关闭失败: {e}")
            
            # 清理实例
            organ_info["instance"] = None
            if organ_name in self.organ_instances:
                del self.organ_instances[organ_name]
            
            organ_info["status"] = OrganStatus.INACTIVE
            logger.info(f"器官 {organ_name} 已关闭")
            return True
            
        except Exception as e:
            logger.error(f"关闭器官 {organ_name} 失败: {e}")
            return False
    
    def get_organ_instance(self, organ_name: str) -> Optional[Any]:
        """获取器官实例"""
        if organ_name in self.organ_instances:
            # 更新活动时间
            if organ_name in self.organs:
                self.organs[organ_name]["last_activity"] = datetime.now()
            return self.organ_instances[organ_name]
        return None
    
    def get_organ_status(self, organ_name: str) -> Optional[OrganStatus]:
        """获取器官状态"""
        if organ_name in self.organs:
            return self.organs[organ_name]["status"]
        return None
    
    def list_active_organs(self) -> List[str]:
        """列出活跃的器官"""
        return [
            name for name, info in self.organs.items()
            if info["status"] == OrganStatus.ACTIVE
        ]
    
    def list_inactive_organs(self) -> List[str]:
        """列出非活跃的器官"""
        return [
            name for name, info in self.organs.items()
            if info["status"] == OrganStatus.INACTIVE
        ]
    
    def get_organ_stats(self) -> Dict[str, Any]:
        """获取器官统计信息"""
        stats = {
            "total_organs": len(self.organs),
            "active_organs": len(self.list_active_organs()),
            "inactive_organs": len(self.list_inactive_organs()),
            "error_organs": len([
                name for name, info in self.organs.items()
                if info["status"] == OrganStatus.ERROR
            ]),
            "organ_details": {}
        }
        
        for name, info in self.organs.items():
            stats["organ_details"][name] = {
                "status": info["status"].value,
                "description": info["description"],
                "priority": info["priority"].value,
                "dependencies": info["dependencies"],
                "error_count": info["error_count"],
                "startup_time": info["startup_time"].isoformat() if info["startup_time"] else None,
                "last_activity": info["last_activity"].isoformat() if info["last_activity"] else None
            }
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """器官系统健康检查"""
        health_status = {
            "overall_health": "healthy",
            "timestamp": datetime.now().isoformat(),
            "issues": [],
            "recommendations": []
        }
        
        active_organs = self.list_active_organs()
        inactive_organs = self.list_inactive_organs()
        
        # 检查关键器官状态
        critical_organs = [
            name for name, info in self.organs.items()
            if info["priority"] == OrganPriority.CRITICAL
        ]
        
        critical_inactive = [org for org in critical_organs if org in inactive_organs]
        if critical_inactive:
            health_status["overall_health"] = "critical"
            health_status["issues"].append(f"关键器官未活跃: {critical_inactive}")
            health_status["recommendations"].append("立即启动关键器官")
        
        # 检查错误器官
        error_organs = [
            name for name, info in self.organs.items()
            if info["status"] == OrganStatus.ERROR
        ]
        
        if error_organs:
            if health_status["overall_health"] == "healthy":
                health_status["overall_health"] = "warning"
            health_status["issues"].append(f"器官错误: {error_organs}")
            health_status["recommendations"].append("检查并修复错误器官")
        
        # 活跃率检查
        activity_rate = len(active_organs) / len(self.organs) if self.organs else 0
        if activity_rate < 0.7:  # 70%以下活跃率
            if health_status["overall_health"] == "healthy":
                health_status["overall_health"] = "warning"
            health_status["issues"].append(f"器官活跃率过低: {activity_rate:.1%}")
            health_status["recommendations"].append("检查器官启动状态")
        
        return health_status
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        active_organs = self.list_active_organs()
        total_organs = len(self.organs)
        
        return {
            "total_organs": total_organs,
            "active_organs": len(active_organs),
            "inactive_organs": len(self.list_inactive_organs()),
            "organ_activity_rate": len(active_organs) / total_organs if total_organs > 0 else 0,
            "active_organ_list": active_organs,
            "system_health": self.health_check()["overall_health"],
            "timestamp": datetime.now().isoformat()
        }
    
    def shutdown(self):
        """关闭器官管理器"""
        try:
            logger.info("器官管理器开始关闭...")
            
            # 按关闭顺序关闭所有器官
            for organ_name in self.shutdown_order:
                self.shutdown_organ(organ_name)
            
            self.is_initialized = False
            logger.info("器官管理器已关闭")
            
        except Exception as e:
            logger.error(f"器官管理器关闭失败: {e}")

# 全局实例获取函数
def get_instance() -> OrganManager:
    """获取器官管理器实例"""
    return OrganManager.get_instance()

if __name__ == "__main__":
    # 测试器官管理器
    manager = get_instance()
    
    logger.info("器官管理器状态:")
    stats = manager.get_organ_stats()
    logger.info(f"总器官数: {stats['total_organs']}")
    logger.info(f"活跃器官数: {stats['active_organs']}")
    logger.info(f"非活跃器官数: {stats['inactive_organs']}")
    
    # 健康检查
    health = manager.health_check()
    logger.info(f"系统健康状态: {health['overall_health']}")
    if health['issues']:
        logger.warning(f"发现问题: {health['issues']}")
    if health['recommendations']:
        logger.info(f"建议: {health['recommendations']}") 