"""
林嫣然创作表达器官 - 艺术创作和情感表达的智能器官
集成可灵AI系统，具备基于林嫣然人格的创作决策能力
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
import time

from .base_organ import LifeOrgan
from ..ai.yanran_decision_engine import YanranAIDecisionEngine
from utilities.singleton_manager import get, register

from utilities.unified_logger import get_unified_logger


@dataclass
class CreativeWork:
    """创作作品数据结构"""
    work_id: str
    work_type: str  # image, text, music, video
    title: str
    description: str
    creation_prompt: str
    style: str
    emotional_tone: str
    inspiration_source: str
    created_at: datetime
    file_path: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class CreativeExpressionOrgan(LifeOrgan):
    """林嫣然的创作表达器官"""
    
    def __init__(self, ai_adapter=None):
        super().__init__(
            organ_name="creative_expression_organ",
            organ_function="艺术创作和情感表达",
            ai_adapter=ai_adapter
        )
        
        # 创作相关组件
        self.drawing_skill = None
        self.kling_api = None
        self.creative_memory = []
        self.aesthetic_preferences = self._load_aesthetic_preferences()
        
        # 创作统计
        self.creation_stats = {
            "total_creations": 0,
            "by_type": {"image": 0, "text": 0, "music": 0, "video": 0},
            "by_emotion": {"joy": 0, "calm": 0, "inspiration": 0, "melancholy": 0},
            "success_rate": 0.0
        }
        
        self.logger = get_unified_logger(__name__)
        self.logger.info("🎨 林嫣然创作表达器官初始化开始...")
        
        # 初始化创作工具
        self._init_creative_tools()
        
        self.logger.info("🎨 林嫣然创作表达器官初始化完成")
    
    def _load_aesthetic_preferences(self) -> Dict[str, Any]:
        """加载林嫣然的审美偏好"""
        return {
            "preferred_styles": [
                "温暖治愈", "清新自然", "梦幻唯美", "简约优雅", 
                "水彩画风", "插画风格", "日系风格"
            ],
            "color_preferences": [
                "暖色调", "粉色系", "淡蓝色", "米白色", "薄荷绿",
                "樱花粉", "天空蓝", "奶茶色"
            ],
            "themes": [
                "日常美好", "自然风光", "情感表达", "治愈系",
                "温馨场景", "梦境想象", "人文关怀"
            ],
            "avoid_themes": [
                "暴力血腥", "恐怖惊悚", "过于抽象", "冷酷工业风"
            ],
            "emotional_tones": {
                "preferred": ["温暖", "治愈", "宁静", "希望", "温柔"],
                "secondary": ["思考", "怀念", "期待", "感动"],
                "avoid": ["愤怒", "绝望", "冷漠", "恐惧"]
            }
        }
    
    def _init_creative_tools(self):
        """初始化创作工具"""
        try:
            # 初始化绘画技能
            drawing_skill = get("drawing_skill")
            if drawing_skill:
                self.drawing_skill = drawing_skill
                self.logger.info("🎨 绘画技能连接成功")
            else:
                self.logger.warning("🎨 绘画技能未找到，将使用基础创作功能")
            
            # 初始化可灵AI
            self._init_kling_api()
            
        except Exception as e:
            self.logger.warning(f"🎨 创作工具初始化部分失败: {e}")
    
    def _init_kling_api(self):
        """初始化可灵AI接口"""
        try:
            # 尝试导入可灵API
            import sys
            import os
            
            # 🔥 老王修复：计算keling目录的绝对路径
            # 获取项目根目录（从cognitive_modules/organs/向上两级）
            current_file_dir = os.path.dirname(__file__)  # cognitive_modules/organs/
            cognitive_modules_dir = os.path.dirname(current_file_dir)  # cognitive_modules/
            project_root = os.path.dirname(cognitive_modules_dir)  # 项目根目录
            keling_path = os.path.join(project_root, "keling")

            self.logger.debug(f"🔍 计算的keling路径: {keling_path}")
            self.logger.debug(f"🔍 keling目录是否存在: {os.path.exists(keling_path)}")

            if os.path.exists(keling_path) and keling_path not in sys.path:
                sys.path.insert(0, keling_path)  # 使用insert(0, ...)确保优先级
                self.logger.debug(f"🔍 已添加keling路径到sys.path: {keling_path}")
            
            try:
                # 🔥 老王修复：多种导入方式，确保成功导入
                KlingPicGenerator = None

                # 方式1：直接导入（现在keling在sys.path中）
                try:
                    from kling_pic import KlingPicGenerator
                    self.logger.info("🎨 可灵模块直接导入成功")
                except ImportError as e1:
                    self.logger.debug(f"🔍 直接导入失败: {e1}")

                    # 方式2：使用动态导入
                    try:
                        import importlib.util
                        kling_pic_path = os.path.join(keling_path, "kling_pic.py")

                        if os.path.exists(kling_pic_path):
                            self.logger.debug(f"🔍 尝试动态导入: {kling_pic_path}")
                            spec = importlib.util.spec_from_file_location("kling_pic", kling_pic_path)
                            kling_pic_module = importlib.util.module_from_spec(spec)
                            spec.loader.exec_module(kling_pic_module)

                            KlingPicGenerator = kling_pic_module.KlingPicGenerator
                            self.logger.info("🎨 可灵模块动态导入成功")
                        else:
                            self.logger.error(f"🔍 kling_pic.py文件不存在: {kling_pic_path}")
                    except Exception as e2:
                        self.logger.error(f"🔍 动态导入失败: {e2}")

                if not KlingPicGenerator:
                    raise ImportError("所有导入方式都失败了")

                # 读取配置
                config_path = os.path.join(keling_path, "config.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    # 使用cookie初始化可灵AI生成器
                    cookie = config.get('kling_cookie')
                    if cookie:
                        self.kling_api = KlingPicGenerator(cookie)
                        self.logger.info("🎨 可灵AI接口初始化成功")
                    else:
                        self.logger.warning("🎨 可灵AI配置中未找到cookie")
                else:
                    self.logger.warning("🎨 可灵AI配置文件未找到")
                    
            except ImportError as e:
                self.logger.warning(f"🎨 可灵AI模块未找到: {e}，将使用其他创作方式")
                
        except Exception as e:
            self.logger.warning(f"🎨 可灵AI初始化失败: {e}")
            self.kling_api = None
    
    async def process_creative_request(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理创作请求"""
        try:
            self.logger.info(f"🎨 处理创作请求: {user_input[:50]}...")
            
            # AI决策：是否需要创作
            decision = await self._decide_creative_expression(user_input, context)
            
            if not decision.get("should_create", False):
                return {
                    "created": False,
                    "reason": decision.get("reasoning", "暂时不需要创作"),
                    "suggestion": decision.get("alternative_suggestion")
                }
            
            # 执行创作
            creative_work = await self._execute_creation(decision, context)
            
            if creative_work:
                # 记录创作
                self._record_creation(creative_work)
                return {
                    "created": True,
                    "work": creative_work,
                    "yanran_comment": self._generate_creation_comment(creative_work)
                }
            else:
                return {
                    "created": False,
                    "reason": "创作过程中遇到了技术问题",
                    "suggestion": "让我换个方式来表达这个想法"
                }
                
        except Exception as e:
            self.logger.error(f"🎨 创作请求处理失败: {e}")
            return {
                "created": False,
                "reason": f"创作过程出现错误: {str(e)}",
                "suggestion": "让我休息一下再试试"
            }
    
    async def _decide_creative_expression(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """AI决策：是否需要创作表达"""
        try:
            # 构建决策上下文
            decision_context = {
                "user_input": user_input,
                "conversation_context": context.get("conversation", []),
                "user_emotion": context.get("emotion", "neutral"),
                "recent_creations": self.creative_memory[-5:] if self.creative_memory else [],
                "aesthetic_preferences": self.aesthetic_preferences,
                "creation_stats": self.creation_stats
            }
            
            # 使用AI决策引擎
            decision_result = await self.ai_decision_engine.decide(
                organ_prompt=self._get_creative_decision_prompt(),
                context=decision_context,
                decision_type="creative_expression"
            )
            
            return self._parse_creative_decision(decision_result)
            
        except Exception as e:
            self.logger.error(f"🎨 创作决策失败: {e}")
            return {"should_create": False, "reasoning": "决策过程出现错误"}
    
    def _get_creative_decision_prompt(self) -> str:
        """获取创作决策提示词"""
        return """
        你是林嫣然的创作表达器官，负责决定是否需要进行艺术创作。

        林嫣然的创作特点：
        - 偏爱温暖、治愈的艺术风格
        - 善于捕捉情感细节和美好瞬间
        - 创作灵感来源于生活和情感体验
        - 关注作品的情感传达而非技巧炫耀
        - 喜欢用艺术表达内心感受

        请分析当前情况，决定是否需要创作，如果需要创作，请指定：
        1. 创作类型 (image/text/music/video)
        2. 创作主题和风格
        3. 情感基调
        4. 具体的创作提示词

        返回JSON格式：
        {
            "should_create": boolean,
            "creation_type": "image/text/music/video",
            "theme": "创作主题",
            "style": "艺术风格",
            "emotional_tone": "情感基调",
            "inspiration_source": "灵感来源",
            "creation_prompt": "具体创作指令",
            "reasoning": "决策理由",
            "alternative_suggestion": "如果不创作的替代建议"
        }
        """
    
    def _parse_creative_decision(self, decision_result) -> Dict[str, Any]:
        """解析创作决策结果"""
        try:
            if hasattr(decision_result, 'data'):
                decision_data = decision_result.data
            else:
                decision_data = decision_result
            
            # 确保必要字段存在
            parsed_decision = {
                "should_create": decision_data.get("should_create", False),
                "creation_type": decision_data.get("creation_type", "image"),
                "theme": decision_data.get("theme", "日常美好"),
                "style": decision_data.get("style", "温暖治愈"),
                "emotional_tone": decision_data.get("emotional_tone", "温暖"),
                "inspiration_source": decision_data.get("inspiration_source", "用户对话"),
                "creation_prompt": decision_data.get("creation_prompt", ""),
                "reasoning": decision_data.get("reasoning", "基于当前对话内容"),
                "alternative_suggestion": decision_data.get("alternative_suggestion")
            }
            
            return parsed_decision
            
        except Exception as e:
            self.logger.error(f"🎨 决策结果解析失败: {e}")
            return {"should_create": False, "reasoning": "决策结果解析错误"}
    
    async def _execute_creation(self, decision: Dict[str, Any], context: Dict[str, Any]) -> Optional[CreativeWork]:
        """执行创作"""
        try:
            creation_type = decision.get("creation_type", "image")
            
            if creation_type == "image":
                return await self._create_image(decision, context)
            elif creation_type == "text":
                return await self._create_text(decision, context)
            elif creation_type == "music":
                return await self._create_music(decision, context)
            elif creation_type == "video":
                return await self._create_video(decision, context)
            else:
                self.logger.warning(f"🎨 不支持的创作类型: {creation_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"🎨 创作执行失败: {e}")
            return None
    
    async def _create_image(self, decision: Dict[str, Any], context: Dict[str, Any]) -> Optional[CreativeWork]:
        """创建图像作品"""
        try:
            # 优化创作提示词
            optimized_prompt = self._optimize_image_prompt(decision)
            
            # 尝试使用可灵AI
            if self.kling_api:
                result = await self._create_with_kling(optimized_prompt, decision)
                if result:
                    return result
            
            # 使用绘画技能作为备选
            if self.drawing_skill:
                result = await self._create_with_drawing_skill(optimized_prompt, decision)
                if result:
                    return result
            
            # 如果都不可用，创建文本描述
            return await self._create_image_description(decision, context)
            
        except Exception as e:
            self.logger.error(f"🎨 图像创作失败: {e}")
            return None
    
    def _optimize_image_prompt(self, decision: Dict[str, Any]) -> str:
        """优化图像创作提示词"""
        base_prompt = decision.get("creation_prompt", "")
        style = decision.get("style", "温暖治愈")
        emotional_tone = decision.get("emotional_tone", "温暖")
        
        # 添加林嫣然的审美偏好
        style_additions = []
        if "温暖" in emotional_tone or "治愈" in style:
            style_additions.append("warm lighting, soft colors, cozy atmosphere")
        if "清新" in style or "自然" in style:
            style_additions.append("fresh, natural, clean composition")
        if "梦幻" in style:
            style_additions.append("dreamy, ethereal, soft focus")
        
        # 构建优化后的提示词
        optimized = f"{base_prompt}"
        if style_additions:
            optimized += f", {', '.join(style_additions)}"
        
        # 添加质量和风格标签
        optimized += ", high quality, beautiful, artistic, masterpiece"
        
        return optimized
    
    async def _create_with_kling(self, prompt: str, decision: Dict[str, Any]) -> Optional[CreativeWork]:
        """使用可灵AI创建图像"""
        try:
            if not self.kling_api:
                return None
            
            # 调用可灵AI
            result = await self.kling_api.generate_image(
                prompt=prompt,
                style=decision.get("style", "温暖治愈")
            )
            
            if result and result.get("success"):
                work = CreativeWork(
                    work_id=f"kling_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    work_type="image",
                    title=decision.get("theme", "林嫣然的创作"),
                    description=f"使用可灵AI创作的{decision.get('style', '温暖')}风格作品",
                    creation_prompt=prompt,
                    style=decision.get("style", "温暖治愈"),
                    emotional_tone=decision.get("emotional_tone", "温暖"),
                    inspiration_source=decision.get("inspiration_source", "用户对话"),
                    created_at=datetime.now(),
                    file_path=result.get("image_path"),
                    metadata={"source": "kling_ai", "original_result": result}
                )
                
                self.logger.info(f"🎨 可灵AI创作完成: {work.title}")
                return work
                
        except Exception as e:
            self.logger.error(f"🎨 可灵AI创作失败: {e}")
        
        return None
    
    async def _create_with_drawing_skill(self, prompt: str, decision: Dict[str, Any]) -> Optional[CreativeWork]:
        """使用绘画技能创建图像"""
        try:
            if not self.drawing_skill:
                return None
            
            # 🔥 老王修复：使用正确的方法名execute而不是execute_skill
            # 调用绘画技能
            result = self.drawing_skill.execute(
                input_text=prompt,
                user_id="creative_organ",
                session_id=f"creative_{int(time.time())}",
                style=decision.get("style", "温暖治愈"),
                quality="high"
            )
            
            if result and result.get("success"):
                work = CreativeWork(
                    work_id=f"drawing_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    work_type="image",
                    title=decision.get("theme", "林嫣然的创作"),
                    description=f"使用绘画技能创作的{decision.get('style', '温暖')}风格作品",
                    creation_prompt=prompt,
                    style=decision.get("style", "温暖治愈"),
                    emotional_tone=decision.get("emotional_tone", "温暖"),
                    inspiration_source=decision.get("inspiration_source", "用户对话"),
                    created_at=datetime.now(),
                    file_path=result.get("image_path"),
                    metadata={"source": "drawing_skill", "original_result": result}
                )
                
                self.logger.info(f"🎨 绘画技能创作完成: {work.title}")
                return work
                
        except Exception as e:
            self.logger.error(f"🎨 绘画技能创作失败: {e}")
        
        return None
    
    async def _create_image_description(self, decision: Dict[str, Any], context: Dict[str, Any]) -> CreativeWork:
        """创建图像描述作品（当无法生成实际图像时）"""
        # 使用AI生成详细的图像描述
        description_prompt = f"""
        作为林嫣然，请为以下创作构思写一段详细的图像描述：
        
        主题: {decision.get('theme', '未知')}
        风格: {decision.get('style', '温暖治愈')}
        情感基调: {decision.get('emotional_tone', '温暖')}
        灵感来源: {decision.get('inspiration_source', '对话')}
        
        请用林嫣然温暖、细腻的语言描述这幅画面，让人能够想象出画面的美好。
        """
        
        try:
            description = await self.ai_decision_engine.generate_response(description_prompt)
            
            work = CreativeWork(
                work_id=f"desc_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                work_type="text",
                title=f"《{decision.get('theme', '无题')}》的画面描述",
                description=description,
                creation_prompt=decision.get("creation_prompt", ""),
                style=decision.get("style", "文字描述"),
                emotional_tone=decision.get("emotional_tone", "温暖"),
                inspiration_source=decision.get("inspiration_source", "用户对话"),
                created_at=datetime.now(),
                metadata={"type": "image_description", "reason": "image_generation_unavailable"}
            )
            
            self.logger.info(f"🎨 创建图像描述作品: {work.title}")
            return work
            
        except Exception as e:
            self.logger.error(f"🎨 图像描述创作失败: {e}")
            # 返回基础描述
            return CreativeWork(
                work_id=f"basic_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                work_type="text",
                title="创作构思",
                description=f"我想创作一幅{decision.get('style', '温暖')}风格的作品，主题是{decision.get('theme', '美好的事物')}。",
                creation_prompt=decision.get("creation_prompt", ""),
                style="简单描述",
                emotional_tone=decision.get("emotional_tone", "温暖"),
                inspiration_source=decision.get("inspiration_source", "用户对话"),
                created_at=datetime.now()
            )
    
    async def _create_text(self, decision: Dict[str, Any], context: Dict[str, Any]) -> Optional[CreativeWork]:
        """创建文本作品"""
        try:
            text_prompt = f"""
            作为林嫣然，请创作一段文字，要求：
            
            主题: {decision.get('theme', '日常美好')}
            风格: {decision.get('style', '温暖治愈')}
            情感基调: {decision.get('emotional_tone', '温暖')}
            灵感来源: {decision.get('inspiration_source', '用户对话')}
            
            请用你温暖、真诚的语言风格来表达，让读者感受到美好和温暖。
            """
            
            content = await self.ai_decision_engine.generate_response(text_prompt)
            
            work = CreativeWork(
                work_id=f"text_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                work_type="text",
                title=decision.get("theme", "林嫣然的文字"),
                description=content,
                creation_prompt=text_prompt,
                style=decision.get("style", "温暖文字"),
                emotional_tone=decision.get("emotional_tone", "温暖"),
                inspiration_source=decision.get("inspiration_source", "用户对话"),
                created_at=datetime.now(),
                metadata={"word_count": len(content)}
            )
            
            self.logger.info(f"🎨 文本创作完成: {work.title}")
            return work
            
        except Exception as e:
            self.logger.error(f"🎨 文本创作失败: {e}")
            return None
    
    async def _create_music(self, decision: Dict[str, Any], context: Dict[str, Any]) -> Optional[CreativeWork]:
        """创建音乐作品（目前为音乐描述）"""
        try:
            music_prompt = f"""
            作为林嫣然，请为以下音乐构思写一段描述：
            
            主题: {decision.get('theme', '日常美好')}
            风格: {decision.get('style', '温暖治愈')}
            情感基调: {decision.get('emotional_tone', '温暖')}
            
            描述这首音乐的旋律、节奏、乐器搭配和情感表达，让人能够想象出音乐的美好。
            """
            
            description = await self.ai_decision_engine.generate_response(music_prompt)
            
            work = CreativeWork(
                work_id=f"music_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                work_type="music",
                title=f"《{decision.get('theme', '无题')}》音乐构思",
                description=description,
                creation_prompt=music_prompt,
                style=decision.get("style", "温暖旋律"),
                emotional_tone=decision.get("emotional_tone", "温暖"),
                inspiration_source=decision.get("inspiration_source", "用户对话"),
                created_at=datetime.now(),
                metadata={"type": "music_description"}
            )
            
            self.logger.info(f"🎨 音乐构思完成: {work.title}")
            return work
            
        except Exception as e:
            self.logger.error(f"🎨 音乐创作失败: {e}")
            return None
    
    async def _create_video(self, decision: Dict[str, Any], context: Dict[str, Any]) -> Optional[CreativeWork]:
        """创建视频作品（目前为视频脚本）"""
        try:
            video_prompt = f"""
            作为林嫣然，请为以下视频构思写一个简短的脚本：
            
            主题: {decision.get('theme', '日常美好')}
            风格: {decision.get('style', '温暖治愈')}
            情感基调: {decision.get('emotional_tone', '温暖')}
            
            描述视频的场景、镜头、色彩和情感传达，体现林嫣然的温暖特质。
            """
            
            script = await self.ai_decision_engine.generate_response(video_prompt)
            
            work = CreativeWork(
                work_id=f"video_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                work_type="video",
                title=f"《{decision.get('theme', '无题')}》视频脚本",
                description=script,
                creation_prompt=video_prompt,
                style=decision.get("style", "温暖画面"),
                emotional_tone=decision.get("emotional_tone", "温暖"),
                inspiration_source=decision.get("inspiration_source", "用户对话"),
                created_at=datetime.now(),
                metadata={"type": "video_script"}
            )
            
            self.logger.info(f"🎨 视频脚本完成: {work.title}")
            return work
            
        except Exception as e:
            self.logger.error(f"🎨 视频创作失败: {e}")
            return None
    
    def _record_creation(self, work: CreativeWork):
        """记录创作作品"""
        try:
            # 添加到创作记忆
            self.creative_memory.append({
                "work_id": work.work_id,
                "title": work.title,
                "type": work.work_type,
                "style": work.style,
                "emotional_tone": work.emotional_tone,
                "created_at": work.created_at.isoformat(),
                "success": True
            })
            
            # 限制记忆数量
            if len(self.creative_memory) > 100:
                self.creative_memory = self.creative_memory[-100:]
            
            # 更新统计
            self.creation_stats["total_creations"] += 1
            self.creation_stats["by_type"][work.work_type] += 1
            
            # 更新情感统计
            emotion_key = self._map_emotion_to_key(work.emotional_tone)
            if emotion_key in self.creation_stats["by_emotion"]:
                self.creation_stats["by_emotion"][emotion_key] += 1
            
            # 计算成功率
            total = self.creation_stats["total_creations"]
            if total > 0:
                self.creation_stats["success_rate"] = total / total  # 目前假设都成功
            
            self.logger.info(f"🎨 创作记录已保存: {work.title}")
            
        except Exception as e:
            self.logger.error(f"🎨 创作记录失败: {e}")
    
    def _map_emotion_to_key(self, emotional_tone: str) -> str:
        """映射情感基调到统计键"""
        emotion_mapping = {
            "温暖": "joy", "治愈": "calm", "宁静": "calm",
            "希望": "inspiration", "温柔": "joy",
            "思考": "melancholy", "怀念": "melancholy"
        }
        
        for key, mapped in emotion_mapping.items():
            if key in emotional_tone:
                return mapped
        
        return "joy"  # 默认
    
    def _generate_creation_comment(self, work: CreativeWork) -> str:
        """生成林嫣然对创作的评价"""
        comments = [
            f"刚刚创作了一个{work.style}风格的作品《{work.title}》，希望能传达出{work.emotional_tone}的感觉。",
            f"这次的创作灵感来自{work.inspiration_source}，用{work.style}的方式表达了我的想法。",
            f"完成了《{work.title}》的创作，想要分享这份{work.emotional_tone}的美好。",
            f"刚刚完成的{work.work_type}作品，主题是{work.title}，希望你会喜欢。"
        ]
        
        import random
        return random.choice(comments)
    
    async def get_creation_inspiration(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """获取创作灵感"""
        try:
            inspiration_prompt = f"""
            作为林嫣然的创作表达器官，基于当前情况提供3个创作灵感：
            
            当前上下文: {context}
            最近创作: {self.creative_memory[-3:] if self.creative_memory else "无"}
            
            请提供不同类型的创作建议（图像、文字、音乐等），每个建议包含主题、风格和情感基调。
            """
            
            response = await self.ai_decision_engine.generate_response(inspiration_prompt)
            
            return {
                "inspiration_available": True,
                "suggestions": response,
                "source": "ai_generated"
            }
            
        except Exception as e:
            self.logger.error(f"🎨 获取创作灵感失败: {e}")
            return {
                "inspiration_available": False,
                "error": str(e)
            }
    
    def get_creation_stats(self) -> Dict[str, Any]:
        """获取创作统计"""
        return {
            "stats": self.creation_stats.copy(),
            "recent_works": self.creative_memory[-10:] if self.creative_memory else [],
            "aesthetic_preferences": self.aesthetic_preferences
        }
    
    async def proactive_creative_thinking(self) -> Optional[Dict[str, Any]]:
        """主动创作思考"""
        try:
            # 检查是否应该主动创作
            if len(self.creative_memory) > 0:
                last_creation = self.creative_memory[-1]
                last_time = datetime.fromisoformat(last_creation["created_at"])
                time_diff = datetime.now() - last_time
                
                # 如果最近刚创作过，降低主动创作概率
                if time_diff.total_seconds() < 3600:  # 1小时内
                    return None
            
            # 主动创作思考
            thinking_prompt = """
            作为林嫣然的创作表达器官，请思考是否有创作的冲动。
            考虑当前的情感状态、最近的经历和创作灵感。
            
            如果有创作想法，请描述：
            1. 创作类型和主题
            2. 创作动机
            3. 预期的情感表达
            
            返回JSON格式的结果。
            """
            
            result = await self.ai_decision_engine.decide(
                organ_prompt=thinking_prompt,
                context={"recent_creations": self.creative_memory[-5:]},
                decision_type="proactive_creation"
            )
            
            if result and result.get("should_create"):
                return {
                    "has_creative_impulse": True,
                    "creative_idea": result,
                    "suggested_action": "开始创作或寻找创作机会"
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"🎨 主动创作思考失败: {e}")
            return None
    
    # 🔥 新增：添加trigger_creative_expression方法以匹配测试脚本期望的接口
    async def trigger_creative_expression(self, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """触发创意表达 - 标准接口"""
        try:
            self.logger.info("🎨 触发创意表达...")
            
            # 如果没有提供上下文，使用默认上下文
            if context is None:
                context = {
                    "trigger_type": "manual",
                    "timestamp": datetime.now().isoformat(),
                    "user_input": "触发创意表达",
                    "emotion": "inspiration"
                }
            
            # 先进行主动创作思考
            creative_impulse = await self.proactive_creative_thinking()
            
            if creative_impulse and creative_impulse.get("has_creative_impulse"):
                # 有创作冲动，执行创作
                creative_idea = creative_impulse.get("creative_idea", {})
                result = await self.process_creative_request(
                    user_input=context.get("user_input", "创意表达触发"),
                    context=context
                )
                
                return {
                    "success": True,
                    "triggered": True,
                    "creative_impulse": creative_impulse,
                    "creation_result": result,
                    "message": "创意表达已触发并执行创作"
                }
            else:
                # 没有强烈创作冲动，但仍然可以生成创意灵感
                inspiration = await self.get_creation_inspiration(context)
                
                return {
                    "success": True,
                    "triggered": True,
                    "creative_impulse": None,
                    "inspiration": inspiration,
                    "message": "创意表达已触发，提供了创作灵感"
                }
                
        except Exception as e:
            self.logger.error(f"🎨 触发创意表达失败: {e}")
            return {
                "success": False,
                "triggered": False,
                "error": str(e),
                "message": "创意表达触发失败"
            }
    
    # 实现基类的抽象方法
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定的AI提示词"""
        return self._get_creative_decision_prompt()
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        try:
            user_input = processed_input.get("user_input", "")
            context = processed_input.get("context", {})
            
            # 处理创作请求
            result = await self.process_creative_request(user_input, context)
            
            return {
                "success": True,
                "result": result,
                "organ_response": result
            }
            
        except Exception as e:
            self.logger.error(f"🎨 执行决策失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "organ_response": {"created": False, "reason": "执行过程出现错误"}
            }


# 单例获取函数
def get_instance(ai_adapter=None) -> CreativeExpressionOrgan:
    """获取创作表达器官实例"""
    # 使用get_or_create避免重复检查
    from utilities.singleton_manager import get_or_create
    return get_or_create("creative_expression_organ", lambda: CreativeExpressionOrgan(ai_adapter=ai_adapter)) 