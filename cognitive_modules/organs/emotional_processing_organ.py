#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
情感处理器官

负责情感分析、情感状态管理和情感反应处理。

作者: Claude
创建日期: 2025-06-30
版本: 1.0
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from utilities.unified_logger import get_unified_logger
from utilities.singleton_manager import get_singleton_manager
from cognitive_modules.life_organ_base import LifeOrganBase
from cognitive_modules.ai.yanran_decision_engine import YanranAIDecisionEngine

logger = get_unified_logger("emotional_processing_organ")
singleton_manager = get_singleton_manager()

class EmotionalProcessingOrgan(LifeOrganBase):
    """情感处理器官"""
    
    def __init__(self):
        """初始化情感处理器官"""
        super().__init__(
            organ_name="emotional_processing_organ",
            description="情感分析和状态管理",
            dependencies=[]
        )
        
        self.logger = logger
        self.emotional_state = {
            "primary_emotion": "neutral",
            "intensity": 0.5,
            "mood": "calm",
            "energy_level": 0.7,
            "last_update": datetime.now()
        }
        
        # 初始化情感处理系统
        self._init_emotion_system()
        
        logger.info("💖 林嫣然情感处理器官初始化完成")
    
    def _init_emotion_system(self):
        """初始化情感系统"""
        try:
            # 情感分类映射
            self.emotion_categories = {
                "positive": ["happy", "excited", "content", "grateful", "optimistic"],
                "negative": ["sad", "angry", "frustrated", "worried", "disappointed"],
                "neutral": ["calm", "neutral", "focused", "thoughtful", "balanced"]
            }
            
            # 情感强度等级
            self.intensity_levels = {
                "very_low": 0.1,
                "low": 0.3,
                "medium": 0.5,
                "high": 0.7,
                "very_high": 0.9
            }
            
            logger.success("💖 情感系统初始化完成")
            
        except Exception as e:
            logger.error(f"💖 情感系统初始化失败: {e}")
    
    def process_emotion(self, trigger: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理情感触发事件
        
        Args:
            trigger: 情感触发器
            context: 上下文信息
            
        Returns:
            情感处理结果
        """
        try:
            context = context or {}
            
            # 分析情感
            emotion_analysis = self._analyze_emotion(trigger, context)
            
            # 更新情感状态
            self._update_emotional_state(emotion_analysis)
            
            # 生成情感反应
            emotional_response = self._generate_emotional_response(emotion_analysis)
            
            return {
                "success": True,
                "emotion": emotion_analysis,
                "response": emotional_response,
                "current_state": self.emotional_state.copy()
            }
            
        except Exception as e:
            logger.error(f"💖 情感处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "current_state": self.emotional_state.copy()
            }
    
    def _analyze_emotion(self, trigger: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """分析情感"""
        try:
            # 简单的情感分析逻辑
            positive_keywords = ["好", "棒", "成功", "开心", "喜欢", "赞", "优秀"]
            negative_keywords = ["坏", "失败", "难过", "生气", "讨厌", "差", "糟糕"]
            
            emotion = "neutral"
            intensity = 0.5
            
            # 检测正面情感
            for keyword in positive_keywords:
                if keyword in trigger:
                    emotion = "happy"
                    intensity = 0.7
                    break
            
            # 检测负面情感
            for keyword in negative_keywords:
                if keyword in trigger:
                    emotion = "sad"
                    intensity = 0.6
                    break
            
            return {
                "emotion": emotion,
                "intensity": intensity,
                "trigger": trigger,
                "timestamp": datetime.now(),
                "confidence": 0.8
            }
            
        except Exception as e:
            logger.error(f"💖 情感分析失败: {e}")
            return {
                "emotion": "neutral",
                "intensity": 0.5,
                "trigger": trigger,
                "timestamp": datetime.now(),
                "confidence": 0.1
            }
    
    def _update_emotional_state(self, emotion_analysis: Dict[str, Any]):
        """更新情感状态"""
        try:
            # 更新主要情感
            self.emotional_state["primary_emotion"] = emotion_analysis["emotion"]
            
            # 更新强度（加权平均）
            current_intensity = self.emotional_state["intensity"]
            new_intensity = emotion_analysis["intensity"]
            self.emotional_state["intensity"] = (current_intensity * 0.7 + new_intensity * 0.3)
            
            # 更新时间戳
            self.emotional_state["last_update"] = datetime.now()
            
            # 根据情感调整能量等级
            if emotion_analysis["emotion"] in ["happy", "excited"]:
                self.emotional_state["energy_level"] = min(1.0, self.emotional_state["energy_level"] + 0.1)
            elif emotion_analysis["emotion"] in ["sad", "tired"]:
                self.emotional_state["energy_level"] = max(0.1, self.emotional_state["energy_level"] - 0.1)
            
            logger.success(f"💖 情感状态已更新: {emotion_analysis['emotion']}")
            
        except Exception as e:
            logger.error(f"💖 更新情感状态失败: {e}")
    
    def _generate_emotional_response(self, emotion_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成情感反应"""
        try:
            emotion = emotion_analysis["emotion"]
            intensity = emotion_analysis["intensity"]
            
            # 情感反应模板
            responses = {
                "happy": {
                    "expression": "😊",
                    "tone": "cheerful",
                    "energy": "high",
                    "message": "我感到很开心！"
                },
                "sad": {
                    "expression": "😢",
                    "tone": "gentle",
                    "energy": "low",
                    "message": "我有点难过..."
                },
                "excited": {
                    "expression": "🎉",
                    "tone": "enthusiastic",
                    "energy": "very_high",
                    "message": "太棒了！我很兴奋！"
                },
                "neutral": {
                    "expression": "😐",
                    "tone": "calm",
                    "energy": "medium",
                    "message": "我保持平静的状态。"
                }
            }
            
            response = responses.get(emotion, responses["neutral"]).copy()
            response["intensity"] = intensity
            response["timestamp"] = datetime.now()
            
            return response
            
        except Exception as e:
            logger.error(f"💖 生成情感反应失败: {e}")
            return {
                "expression": "😐",
                "tone": "neutral",
                "energy": "medium",
                "message": "情感处理中...",
                "intensity": 0.5,
                "timestamp": datetime.now()
            }
    
    def get_emotional_state(self) -> Dict[str, Any]:
        """获取当前情感状态"""
        return self.emotional_state.copy()
    
    def set_mood(self, mood: str, intensity: float = 0.5) -> bool:
        """
        设置心情
        
        Args:
            mood: 心情类型
            intensity: 强度
            
        Returns:
            是否设置成功
        """
        try:
            self.emotional_state["mood"] = mood
            self.emotional_state["intensity"] = max(0.0, min(1.0, intensity))
            self.emotional_state["last_update"] = datetime.now()
            
            logger.success(f"💖 心情已设置为: {mood} (强度: {intensity})")
            return True
            
        except Exception as e:
            logger.error(f"💖 设置心情失败: {e}")
            return False
    
    def reset_emotional_state(self):
        """重置情感状态"""
        try:
            self.emotional_state = {
                "primary_emotion": "neutral",
                "intensity": 0.5,
                "mood": "calm",
                "energy_level": 0.7,
                "last_update": datetime.now()
            }
            
            logger.success("💖 情感状态已重置")
            
        except Exception as e:
            logger.error(f"💖 重置情感状态失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取器官状态"""
        return {
            "organ_name": self.organ_name,
            "is_active": True,
            "emotional_state": self.emotional_state.copy(),
            "last_activity": datetime.now().isoformat(),
            "health_status": "healthy"
        }

# 工厂函数
def create_emotional_processing_organ() -> EmotionalProcessingOrgan:
    """创建情感处理器官实例"""
    return EmotionalProcessingOrgan()

# 向后兼容
def get_instance() -> EmotionalProcessingOrgan:
    """获取情感处理器官实例"""
    return singleton_manager.get_or_create(
        'emotional_processing_organ',
        create_emotional_processing_organ
    ) 