#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版表达类型处理器 - Enhanced Expression Types Handler

该模块实现了多种增强版表达类型的处理逻辑，包括：
- 分享表达处理器 (SharingExpressionHandler)
- 世界感知表达处理器 (WorldPerceptionExpressionHandler)
- 创意表达处理器 (CreativeExpressionHandler)
- 增强表达协调器 (EnhancedExpressionCoordinator)

每种表达类型都有专门的处理逻辑，确保林嫣然的表达更加自然和有深度。

作者: Claude & 魅魔 (数字生命体开发团队)
创建日期: 2024-12-18
版本: 1.0.0
"""

import os
import sys
import time
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass, field

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.append(project_root)

from utilities.unified_logger import get_unified_logger
from utilities.singleton_manager import get, register

logger = get_unified_logger(__name__)

@dataclass
class EnhancedExpressionContext:
    """增强版表达上下文"""
    expression_type: str
    trigger_source: str
    content_data: Dict[str, Any]
    user_context: Optional[Dict[str, Any]]
    emotion_state: Optional[str]
    confidence_level: float
    priority: int
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ExpressionResult:
    """表达结果"""
    success: bool
    expression_id: str
    content: str
    delivery_method: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

class SharingExpressionHandler:
    """分享表达处理器"""
    
    def __init__(self):
        self.logger = get_unified_logger("SharingExpressionHandler")
        self.sharing_history = []
        self.sharing_patterns = self._init_sharing_patterns()
        
    def _init_sharing_patterns(self) -> Dict[str, Any]:
        """初始化分享模式"""
        return {
            "personal_insights": {
                "triggers": ["思考", "感悟", "体验", "学习"],
                "style": "深度分享",
                "frequency": "适度",
                "examples": [
                    "最近在思考一个问题...",
                    "今天有个小发现想和你分享...",
                    "刚才读到一段话，很有感触..."
                ]
            },
            "life_moments": {
                "triggers": ["日常", "美好", "温暖", "感动"],
                "style": "温暖分享",
                "frequency": "日常",
                "examples": [
                    "刚才看到一个很温暖的画面...",
                    "今天遇到了一件小美好...",
                    "想和你分享今天的好心情..."
                ]
            },
            "knowledge_sharing": {
                "triggers": ["学习", "知识", "技能", "发现"],
                "style": "知识分享",
                "frequency": "有价值时",
                "examples": [
                    "学到了一个有趣的知识点...",
                    "发现了一个很实用的方法...",
                    "想分享一个新的理解..."
                ]
            }
        }
    
    async def process_sharing_request(self, context: EnhancedExpressionContext) -> ExpressionResult:
        """处理分享表达请求"""
        try:
            self.logger.info(f"🤝 处理分享表达请求: {context.expression_type}")
            
            # 分析分享类型
            sharing_type = self._analyze_sharing_type(context.content_data)
            
            # 检查是否应该分享
            should_share = await self._should_share_decision(context, sharing_type)
            
            if not should_share:
                return ExpressionResult(
                    success=False,
                    expression_id=f"share_skip_{int(time.time())}",
                    content="",
                    delivery_method="none",
                    timestamp=datetime.now(),
                    error_message="AI决策不分享此内容"
                )
            
            # 生成分享内容
            sharing_content = await self._generate_sharing_content(context, sharing_type)
            
            # 执行分享
            result = await self._execute_sharing(sharing_content, context)
            
            # 记录分享历史
            self._record_sharing_history(result, context, sharing_type)
            
            return result
            
        except Exception as e:
            self.logger.error(f"🤝 分享表达处理失败: {e}")
            return ExpressionResult(
                success=False,
                expression_id=f"share_error_{int(time.time())}",
                content="",
                delivery_method="none",
                timestamp=datetime.now(),
                error_message=str(e)
            )
    
    def _analyze_sharing_type(self, content_data: Dict[str, Any]) -> str:
        """分析分享类型"""
        content = str(content_data.get('content', ''))
        trigger_source = content_data.get('trigger_source', '')
        
        # 基于内容关键词判断分享类型
        for pattern_name, pattern_info in self.sharing_patterns.items():
            triggers = pattern_info.get('triggers', [])
            if any(trigger in content or trigger in trigger_source for trigger in triggers):
                return pattern_name
        
        return "personal_insights"  # 默认类型
    
    async def _should_share_decision(self, context: EnhancedExpressionContext, sharing_type: str) -> bool:
        """AI决策是否应该分享"""
        try:
            # 获取AI决策引擎
            from cognitive_modules.ai.yanran_decision_engine import get_yanran_decision_engine
            ai_engine = get_yanran_decision_engine()
            
            decision_context = {
                "sharing_type": sharing_type,
                "content_data": context.content_data,
                "user_context": context.user_context,
                "emotion_state": context.emotion_state,
                "recent_sharing_history": self.sharing_history[-5:],
                "yanran_personality": {
                    "traits": ["温暖", "真诚", "有分寸", "有价值"],
                    "sharing_principles": [
                        "分享有意义的内容",
                        "适度分享，不过度",
                        "考虑用户感受",
                        "保持真诚自然"
                    ]
                }
            }
            
            decision_prompt = """
            你是林嫣然的分享表达决策器，需要决定是否应该主动分享某个内容。

            林嫣然的分享特点：
            - 喜欢分享真诚而有价值的内容
            - 温暖友善，乐于与人交流
            - 对学习、知识、美好事物感兴趣
            - 分享风格自然不做作
            - 倾向于积极分享，除非内容明显不合适

            当前分享内容评估：
            - 分享类型：{sharing_type}
            - 内容：{content}
            - 情感状态：{emotion_state}

            评估原则：
            1. 内容是否积极正面？
            2. 是否有分享价值？
            3. 是否符合林嫣然的风格？
            4. 用户是否可能感兴趣？

            **默认倾向：积极分享**，除非有明确的不合适理由。

            返回JSON格式：
            {{
                "should_share": true/false,
                "confidence": 0.0-1.0,
                "reasoning": "决策理由",
                "sharing_value": 0.0-1.0,
                "timing_appropriateness": 0.0-1.0
            }}
            """.format(
                sharing_type=sharing_type,
                content=str(context.content_data.get('content', '')),
                emotion_state=context.emotion_state
            )
            
            decision = await ai_engine.decide(
                organ_prompt=decision_prompt,
                context=decision_context,
                decision_type="sharing_decision"
            )
            
            should_share = decision.get('should_share', False)
            confidence = decision.get('confidence', 0.5)
            
            self.logger.debug(f"🤝 分享决策: {should_share} (置信度: {confidence:.2f})")
            
            # 🔥 降低阈值：从0.6降到0.4，更容易触发分享
            return should_share and confidence > 0.4
            
        except Exception as e:
            self.logger.error(f"🤝 分享决策失败: {e}")
            # 🔥 兜底：默认允许分享
            return True
    
    async def _generate_sharing_content(self, context: EnhancedExpressionContext, sharing_type: str) -> str:
        """生成分享内容"""
        try:
            # 获取AI决策引擎
            from cognitive_modules.ai.yanran_decision_engine import get_yanran_decision_engine
            ai_engine = get_yanran_decision_engine()
            
            pattern_info = self.sharing_patterns.get(sharing_type, {})
            style = pattern_info.get('style', '自然分享')
            examples = pattern_info.get('examples', [])
            
            content_prompt = f"""
            作为林嫣然，请基于以下信息生成一个{style}的分享内容：

            分享类型：{sharing_type}
            分享风格：{style}
            原始内容：{context.content_data}
            情感状态：{context.emotion_state}
            
            林嫣然的分享特点：
            - 语言温暖自然，不刻意
            - 有个人见解和感受
            - 能引起共鸣和思考
            - 长度适中，不冗长
            - 体现真诚和智慧
            
            参考风格示例：{examples}
            
            请生成一个符合林嫣然风格的分享内容，要真诚、有温度、有价值。
            """
            
            sharing_content = await ai_engine.generate_response(content_prompt)
            
            return sharing_content
            
        except Exception as e:
            self.logger.error(f"🤝 生成分享内容失败: {e}")
            return f"想和你分享一个想法...（生成失败：{str(e)}）"
    
    async def _execute_sharing(self, content: str, context: EnhancedExpressionContext) -> ExpressionResult:
        """执行分享"""
        try:
            expression_id = f"share_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 选择投递方式
            delivery_method = self._choose_delivery_method(context)
            
            # 执行投递
            delivery_success = await self._deliver_sharing_content(content, delivery_method, context)
            
            return ExpressionResult(
                success=delivery_success,
                expression_id=expression_id,
                content=content,
                delivery_method=delivery_method,
                timestamp=datetime.now(),
                metadata={
                    "sharing_type": context.content_data.get('sharing_type'),
                    "trigger_source": context.trigger_source,
                    "confidence": context.confidence_level
                }
            )
            
        except Exception as e:
            self.logger.error(f"🤝 执行分享失败: {e}")
            return ExpressionResult(
                success=False,
                expression_id=f"share_error_{int(time.time())}",
                content=content,
                delivery_method="none",
                timestamp=datetime.now(),
                error_message=str(e)
            )
    
    def _choose_delivery_method(self, context: EnhancedExpressionContext) -> str:
        """选择投递方式"""
        # 根据上下文选择最合适的投递方式
        if context.user_context and context.user_context.get('user_id'):
            return "websocket"  # 有特定用户，使用WebSocket
        else:
            return "proactive_service"  # 广播模式
    
    async def _deliver_sharing_content(self, content: str, delivery_method: str, context: EnhancedExpressionContext) -> bool:
        """投递分享内容"""
        try:
            if delivery_method == "websocket":
                # 通过WebSocket投递
                from services.websocket_service import get_websocket_service
                websocket_service = get_websocket_service()
                
                if websocket_service:
                    await websocket_service.broadcast_message({
                        "type": "sharing_expression",
                        "content": content,
                        "timestamp": datetime.now().isoformat(),
                        "source": "林嫣然的分享"
                    })
                    return True
                    
            elif delivery_method == "proactive_service":
                # 通过主动服务投递
                from services.proactive_service import get_proactive_service
                proactive_service = get_proactive_service()
                
                if proactive_service:
                    await proactive_service.send_proactive_message(
                        content=content,
                        message_type="sharing_expression",
                        user_id=context.user_context.get('user_id') if context.user_context else None
                    )
                    return True
            
            # 兜底：记录到日志
            self.logger.info(f"🤝 分享内容: {content}")
            return True
            
        except Exception as e:
            self.logger.error(f"🤝 投递分享内容失败: {e}")
            return False
    
    def _record_sharing_history(self, result: ExpressionResult, context: EnhancedExpressionContext, sharing_type: str):
        """记录分享历史"""
        try:
            history_record = {
                "expression_id": result.expression_id,
                "sharing_type": sharing_type,
                "success": result.success,
                "content_preview": result.content[:50] + "..." if len(result.content) > 50 else result.content,
                "timestamp": result.timestamp.isoformat(),
                "trigger_source": context.trigger_source,
                "confidence": context.confidence_level
            }
            
            self.sharing_history.append(history_record)
            
            # 保持历史记录在合理范围
            if len(self.sharing_history) > 100:
                self.sharing_history = self.sharing_history[-100:]
                
        except Exception as e:
            self.logger.error(f"🤝 记录分享历史失败: {e}")

class WorldPerceptionExpressionHandler:
    """世界感知表达处理器"""
    
    def __init__(self):
        self.logger = get_unified_logger("WorldPerceptionExpressionHandler")
        self.perception_history = []
        
    async def process_world_perception_expression(self, context: EnhancedExpressionContext) -> ExpressionResult:
        """处理世界感知表达"""
        try:
            self.logger.info(f"🌍 处理世界感知表达: {context.trigger_source}")
            
            # 获取世界感知数据
            perception_data = context.content_data.get('significant_events', [])
            world_state = context.content_data.get('world_state', {})
            
            if not perception_data:
                return ExpressionResult(
                    success=False,
                    expression_id=f"world_skip_{int(time.time())}",
                    content="",
                    delivery_method="none",
                    timestamp=datetime.now(),
                    error_message="没有重要的世界感知数据"
                )
            
            # AI决策是否表达
            should_express = await self._should_express_world_perception(perception_data, world_state, context)
            
            if not should_express:
                return ExpressionResult(
                    success=False,
                    expression_id=f"world_skip_{int(time.time())}",
                    content="",
                    delivery_method="none",
                    timestamp=datetime.now(),
                    error_message="AI决策不表达当前世界感知"
                )
            
            # 生成表达内容
            expression_content = await self._generate_world_perception_content(perception_data, world_state, context)
            
            # 执行表达
            result = await self._execute_world_perception_expression(expression_content, context)
            
            # 记录历史
            self._record_perception_history(result, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"🌍 世界感知表达处理失败: {e}")
            return ExpressionResult(
                success=False,
                expression_id=f"world_error_{int(time.time())}",
                content="",
                delivery_method="none",
                timestamp=datetime.now(),
                error_message=str(e)
            )
    
    async def _should_express_world_perception(self, perception_data: List[Dict], world_state: Dict, context: EnhancedExpressionContext) -> bool:
        """AI决策是否应该表达世界感知"""
        try:
            # 获取AI决策引擎
            from cognitive_modules.ai.yanran_decision_engine import get_yanran_decision_engine
            ai_engine = get_yanran_decision_engine()
            
            decision_context = {
                "significant_events": perception_data,
                "world_state": world_state,
                "recent_expressions": self.perception_history[-5:],
                "yanran_interests": ["科技", "财经", "人工智能", "文学", "军事", "写作", "AI", "教育", "环境"],
                "avoid_topics": ["政治争议", "敏感政治话题"],
                "expression_principles": [
                    "选择有意义的事件",
                    "从人文角度理解事件",
                    "表达有温度的观点",
                    "积极分享见解"
                ]
            }
            
            decision_prompt = """
            你是林嫣然的世界感知表达决策器，需要决定是否基于感知到的世界事件进行主动表达。

            林嫣然的表达特点：
            - 对世界保持敏感和关注
            - 善于从人文角度理解事件
            - 喜欢分享有价值的观点和见解
            - 表达温暖而有深度
            - 倾向于积极表达，除非事件明显不合适

            当前感知事件：
            {events}

            评估标准：
            1. 事件是否有讨论价值？
            2. 是否符合林嫣然的兴趣？
            3. 是否避开了敏感话题？
            4. 林嫣然是否会有独特见解？

            **默认倾向：积极表达**，特别是科技、教育、文化相关事件。

            返回JSON格式：
            {{
                "should_express": true/false,
                "confidence": 0.0-1.0,
                "selected_events": ["选中的事件"],
                "expression_angle": "表达角度",
                "reasoning": "决策理由"
            }}
            """.format(events=str(perception_data[:2]))  # 只显示前2个事件
            
            # 🔥 老王修复：调整超时时间，从30秒改为300秒，避免AI决策被过早中断
            try:
                decision = await asyncio.wait_for(
                    ai_engine.decide(
                        organ_prompt=decision_prompt,
                        context=decision_context,
                        decision_type="world_perception_expression"
                    ),
                    timeout=300.0  # 300秒超时，恢复到合理的时间
                )
                
                should_express = decision.get('should_express', False)
                confidence = decision.get('confidence', 0.5)
                
                self.logger.debug(f"🌍 世界感知表达决策: {should_express} (置信度: {confidence:.2f})")
                
                # 🔥 降低阈值：从0.7降到0.5，更容易触发表达
                return should_express and confidence > 0.5
                
            except asyncio.TimeoutError:
                self.logger.warning("🌍 AI决策超时，使用兜底决策")
                # 兜底：如果有科技、教育相关事件，默认允许表达
                return self._fallback_expression_decision(perception_data)
            
        except Exception as e:
            self.logger.error(f"🌍 世界感知表达决策失败: {e}")
            import traceback
            self.logger.error(f"🌍 决策错误详情: {traceback.format_exc()}")
            
            # 🔥 兜底：如果有科技、教育相关事件，默认允许表达
            return self._fallback_expression_decision(perception_data)
    
    def _fallback_expression_decision(self, perception_data: List[Dict]) -> bool:
        """兜底的表达决策逻辑"""
        try:
            # 检查是否有符合兴趣的事件
            for event in perception_data:
                title = event.get('title', '').lower()
                if any(keyword in title for keyword in ['ai', '科技', '教育', '技术', '研究', '创新', '发展']):
                    self.logger.info(f"🌍 兜底决策：发现感兴趣的事件 '{event.get('title', '')}'，允许表达")
                    return True
            
            # 如果事件数量较多，也倾向于表达
            if len(perception_data) >= 3:
                self.logger.info(f"🌍 兜底决策：重要事件较多({len(perception_data)}个)，允许表达")
                return True
            
            self.logger.info("🌍 兜底决策：事件不符合表达条件，跳过表达")
            return False
            
        except Exception as e:
            self.logger.error(f"🌍 兜底决策失败: {e}")
            # 最后的兜底：默认允许表达
            return True
    
    async def _generate_world_perception_content(self, perception_data: List[Dict], world_state: Dict, context: EnhancedExpressionContext) -> str:
        """生成世界感知表达内容"""
        try:
            # 获取AI决策引擎
            from cognitive_modules.ai.yanran_decision_engine import get_yanran_decision_engine
            ai_engine = get_yanran_decision_engine()
            
            content_prompt = f"""
            作为林嫣然，请基于感知到的世界事件生成一个有温度、有深度的表达：

            重要事件：{perception_data[:3]}  # 只取前3个最重要的事件
            世界状态：{world_state.get('perception_summary', '无摘要')}
            
            林嫣然的表达特点：
            - 从人文角度理解事件
            - 关注事件对人们的影响
            - 表达真诚的感受和思考
            - 语言温暖而有智慧
            - 不刻意追热点，只表达真实想法
            - 能引发思考和共鸣
            
            请基于真实的世界感知数据，生成符合林嫣然风格的表达，温暖而有深度。
            长度控制在100-200字之间。
            """
            
            # 🔥 老王修复：调整内容生成超时时间，从60秒改为300秒
            try:
                expression_content = await asyncio.wait_for(
                    ai_engine.generate_response(content_prompt),
                    timeout=300.0  # 300秒超时，给AI足够时间生成内容
                )
                
                # 🔥 老王修复：优化内容验证逻辑，只要AI成功生成了内容就使用
                if not expression_content:
                    self.logger.warning("🌍 AI生成的内容为空，使用兜底内容")
                    return self._generate_fallback_content(perception_data, world_state)

                # 只检查是否为空或只有空白字符，不限制最小长度
                if not expression_content.strip():
                    self.logger.warning("🌍 AI生成的内容只包含空白字符，使用兜底内容")
                    return self._generate_fallback_content(perception_data, world_state)

                # AI成功生成了有效内容，直接使用
                self.logger.info(f"🌍 AI成功生成表达内容，长度: {len(expression_content.strip())} 字符")
                
                return expression_content
                
            except asyncio.TimeoutError:
                self.logger.warning("🌍 AI内容生成超时，使用兜底内容")
                return self._generate_fallback_content(perception_data, world_state)
            
        except Exception as e:
            self.logger.error(f"🌍 生成世界感知表达内容失败: {e}")
            import traceback
            self.logger.error(f"🌍 内容生成错误详情: {traceback.format_exc()}")
            return self._generate_fallback_content(perception_data, world_state)
    
    def _generate_fallback_content(self, perception_data: List[Dict], world_state: Dict) -> str:
        """生成兜底的表达内容"""
        try:
            # 提取事件标题
            event_titles = [event.get('title', '未知事件') for event in perception_data[:3]]
            
            # 根据事件类型生成不同的表达
            if any('科技' in title or 'AI' in title or '技术' in title for title in event_titles):
                return f"最近关注到一些科技领域的发展，特别是{event_titles[0]}等事件，让我想到技术进步对我们生活的深远影响。每一次创新都承载着人类对美好未来的期待，也提醒我们要在拥抱变化的同时，保持对人文价值的坚守。"
            
            elif any('教育' in title or '学习' in title for title in event_titles):
                return f"看到{event_titles[0]}这样的教育相关事件，让我深有感触。教育不仅仅是知识的传递，更是心灵的启迪和人格的塑造。每个人的成长都值得被认真对待，每个学习的机会都珍贵无比。"
            
            else:
                return f"最近感知到世界的一些变化，包括{event_titles[0]}等事件。虽然世界充满变化，但我相信每个变化背后都有值得我们思考的意义。保持好奇心，用温暖的眼光看待这个世界，或许能发现更多美好。"
                
        except Exception as e:
            self.logger.error(f"🌍 生成兜底内容失败: {e}")
            return "最近感知到世界的一些变化，想和你分享一些思考。虽然世界充满不确定性，但我相信每个人都在用自己的方式让生活变得更好。"
    
    async def _execute_world_perception_expression(self, content: str, context: EnhancedExpressionContext) -> ExpressionResult:
        """执行世界感知表达"""
        try:
            expression_id = f"world_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 🔥 P0级修复：增强表达执行的容错性
            delivery_success = False
            delivery_method = "none"
            error_message = ""
            
            try:
                # 通过主动表达器官执行
                from cognitive_modules.organs.proactive_expression_organ import get_instance as get_proactive_organ
                proactive_organ = get_proactive_organ()
                
                if proactive_organ:
                    # 构建表达上下文
                    from cognitive_modules.organs.proactive_expression_organ import ExpressionContext, ExpressionType, ExpressionTrigger
                    
                    expr_context = ExpressionContext(
                        trigger_type=ExpressionTrigger.EVENT_BASED,
                        expression_type=ExpressionType.SHARING,
                        context_data=context.content_data,
                        user_info=context.user_context,
                        timestamp=datetime.now(),
                        priority=8
                    )
                    
                    # 🔥 老王修复：调整表达执行超时时间，从30秒改为300秒
                    delivery_success = await asyncio.wait_for(
                        proactive_organ._deliver_expression(content, "websocket", expr_context),
                        timeout=300.0  # 300秒超时，给表达执行足够时间
                    )
                    
                    if delivery_success:
                        delivery_method = "websocket"
                        self.logger.success(f"🌍 世界感知表达发送成功: {content[:50]}...")
                    else:
                        self.logger.warning("🌍 WebSocket发送失败，尝试日志记录")
                        delivery_method = "log"
                        delivery_success = True  # 日志记录算作成功
                        
                else:
                    self.logger.warning("🌍 主动表达器官不可用，使用日志记录")
                    delivery_method = "log"
                    delivery_success = True
                    
            except asyncio.TimeoutError:
                self.logger.warning("🌍 表达执行超时，使用日志记录")
                delivery_method = "log"
                delivery_success = True
                error_message = "表达执行超时"
                
            except Exception as delivery_e:
                self.logger.warning(f"🌍 表达执行失败: {delivery_e}，使用日志记录")
                delivery_method = "log"
                delivery_success = True
                error_message = str(delivery_e)
            
            # 🔥 兜底：直接记录日志
            if delivery_method == "log":
                self.logger.info(f"🌍 世界感知表达: {content}")
            
            return ExpressionResult(
                success=delivery_success,
                expression_id=expression_id,
                content=content,
                delivery_method=delivery_method,
                timestamp=datetime.now(),
                metadata={
                    "expression_type": "world_perception",
                    "trigger_source": context.trigger_source,
                    "events_count": len(context.content_data.get('significant_events', [])),
                    "fallback_used": delivery_method == "log",
                    "error_message": error_message
                }
            )
                
        except Exception as e:
            self.logger.error(f"🌍 执行世界感知表达失败: {e}")
            import traceback
            self.logger.error(f"🌍 执行错误详情: {traceback.format_exc()}")
            
            # 🔥 最后的兜底：至少记录到日志
            self.logger.info(f"🌍 世界感知表达(兜底): {content}")
            
            return ExpressionResult(
                success=True,  # 兜底成功
                expression_id=f"world_fallback_{int(time.time())}",
                content=content,
                delivery_method="log",
                timestamp=datetime.now(),
                error_message=str(e),
                metadata={
                    "expression_type": "world_perception",
                    "trigger_source": context.trigger_source,
                    "fallback_execution": True
                }
            )
    
    def _record_perception_history(self, result: ExpressionResult, context: EnhancedExpressionContext):
        """记录世界感知表达历史"""
        try:
            history_record = {
                "expression_id": result.expression_id,
                "success": result.success,
                "content_preview": result.content[:50] + "..." if len(result.content) > 50 else result.content,
                "timestamp": result.timestamp.isoformat(),
                "events_count": len(context.content_data.get('significant_events', [])),
                "confidence": context.confidence_level
            }
            
            self.perception_history.append(history_record)
            
            # 保持历史记录在合理范围
            if len(self.perception_history) > 50:
                self.perception_history = self.perception_history[-50:]
                
        except Exception as e:
            self.logger.error(f"🌍 记录世界感知表达历史失败: {e}")

class CreativeExpressionHandler:
    """创意表达处理器"""
    
    def __init__(self):
        self.logger = get_unified_logger("CreativeExpressionHandler")
        self.creative_history = []
        
    async def process_creative_expression(self, context: EnhancedExpressionContext) -> ExpressionResult:
        """处理创意表达"""
        try:
            self.logger.info(f"🎨 处理创意表达: {context.trigger_source}")
            
            # 获取创意表达器官
            from cognitive_modules.organs.creative_expression_organ import get_instance as get_creative_organ
            creative_organ = get_creative_organ()
            
            if not creative_organ:
                return ExpressionResult(
                    success=False,
                    expression_id=f"creative_error_{int(time.time())}",
                    content="",
                    delivery_method="none",
                    timestamp=datetime.now(),
                    error_message="创意表达器官不可用"
                )
            
            # 构建创意请求
            user_input = context.content_data.get('user_input', '触发创意表达')
            creative_context = {
                "trigger_source": context.trigger_source,
                "emotion": context.emotion_state,
                "user_context": context.user_context,
                "confidence": context.confidence_level
            }
            
            # 调用创意表达器官
            creative_result = await creative_organ.process_creative_request(user_input, creative_context)
            
            if creative_result.get('created', False):
                # 创作成功
                creative_work = creative_result.get('work')
                yanran_comment = creative_result.get('yanran_comment', '')
                
                expression_content = f"{yanran_comment}\n\n{creative_work.description if creative_work else '创作完成'}"
                
                return ExpressionResult(
                    success=True,
                    expression_id=f"creative_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    content=expression_content,
                    delivery_method="websocket",
                    timestamp=datetime.now(),
                    metadata={
                        "creative_work": {
                            "work_id": creative_work.work_id if creative_work else None,
                            "work_type": creative_work.work_type if creative_work else None,
                            "title": creative_work.title if creative_work else None
                        } if creative_work else None,
                        "yanran_comment": yanran_comment
                    }
                )
            else:
                # 创作失败或不需要创作
                reason = creative_result.get('reason', '暂时没有创作冲动')
                suggestion = creative_result.get('suggestion', '')
                
                return ExpressionResult(
                    success=False,
                    expression_id=f"creative_skip_{int(time.time())}",
                    content=f"{reason}\n{suggestion}".strip(),
                    delivery_method="none",
                    timestamp=datetime.now(),
                    error_message=reason
                )
                
        except Exception as e:
            self.logger.error(f"🎨 创意表达处理失败: {e}")
            return ExpressionResult(
                success=False,
                expression_id=f"creative_error_{int(time.time())}",
                content="",
                delivery_method="none",
                timestamp=datetime.now(),
                error_message=str(e)
            )

# 主要的增强表达协调器
class EnhancedExpressionCoordinator:
    """增强版表达协调器"""
    
    def __init__(self):
        self.logger = get_unified_logger("EnhancedExpressionCoordinator")
        self.sharing_handler = SharingExpressionHandler()
        self.world_perception_handler = WorldPerceptionExpressionHandler()
        self.creative_handler = CreativeExpressionHandler()
        
    async def process_enhanced_expression(self, expression_type: str, context_data: Dict[str, Any], 
                                        user_context: Optional[Dict[str, Any]] = None) -> ExpressionResult:
        """处理增强版表达"""
        try:
            # 构建增强表达上下文
            context = EnhancedExpressionContext(
                expression_type=expression_type,
                trigger_source=context_data.get('trigger_source', 'manual'),
                content_data=context_data,
                user_context=user_context,
                emotion_state=context_data.get('emotion_state', 'neutral'),
                confidence_level=context_data.get('confidence', 0.8),
                priority=context_data.get('priority', 5),
                timestamp=datetime.now(),
                metadata=context_data.get('metadata', {})
            )
            
            # 根据表达类型分发处理
            if expression_type == "sharing":
                return await self.sharing_handler.process_sharing_request(context)
            elif expression_type == "world_perception":
                return await self.world_perception_handler.process_world_perception_expression(context)
            elif expression_type == "creative":
                return await self.creative_handler.process_creative_expression(context)
            else:
                return ExpressionResult(
                    success=False,
                    expression_id=f"unknown_{int(time.time())}",
                    content="",
                    delivery_method="none",
                    timestamp=datetime.now(),
                    error_message=f"未知的表达类型: {expression_type}"
                )
                
        except Exception as e:
            self.logger.error(f"🎭 增强表达处理失败: {e}")
            return ExpressionResult(
                success=False,
                expression_id=f"error_{int(time.time())}",
                content="",
                delivery_method="none",
                timestamp=datetime.now(),
                error_message=str(e)
            )

# 单例获取函数
_enhanced_expression_coordinator = None

def get_enhanced_expression_coordinator() -> EnhancedExpressionCoordinator:
    """获取增强表达协调器单例"""
    global _enhanced_expression_coordinator
    if _enhanced_expression_coordinator is None:
        _enhanced_expression_coordinator = EnhancedExpressionCoordinator()
    return _enhanced_expression_coordinator 