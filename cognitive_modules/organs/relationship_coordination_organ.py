"""
林嫣然关系协调器官 - 跨模块协同优化
作为林嫣然的"协调中枢"，统筹各器官工作
"""

import asyncio
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Set
from dataclasses import dataclass
from enum import Enum

from .base_organ import <PERSON><PERSON><PERSON>
from ..ai.yanran_decision_engine import YanranAIDecisionEngine
from utilities.singleton_manager import get, register

from utilities.unified_logger import get_unified_logger


class CoordinationType(Enum):
    """协调类型枚举"""
    ORGAN_SYNC = "organ_sync"
    WORKFLOW_ORCHESTRATION = "workflow_orchestration"
    CONFLICT_RESOLUTION = "conflict_resolution"
    RESOURCE_ALLOCATION = "resource_allocation"
    PRIORITY_MANAGEMENT = "priority_management"
    COMMUNICATION_BRIDGE = "communication_bridge"
    PERFORMANCE_OPTIMIZATION = "performance_optimization"


class RelationshipType(Enum):
    """关系类型枚举"""
    COLLABORATIVE = "collaborative"
    DEPENDENT = "dependent"
    INDEPENDENT = "independent"
    COMPETITIVE = "competitive"
    SUPPORTIVE = "supportive"
    HIERARCHICAL = "hierarchical"


@dataclass
class OrganRelationship:
    """器官关系"""
    source_organ: str
    target_organ: str
    relationship_type: RelationshipType
    strength: float  # 0-1
    priority: int  # 1-10
    last_interaction: datetime
    interaction_count: int
    success_rate: float
    dependencies: List[str]


@dataclass
class CoordinationTask:
    """协调任务"""
    task_id: str
    coordination_type: CoordinationType
    involved_organs: List[str]
    primary_organ: str
    task_description: str
    priority: int
    deadline: Optional[datetime]
    status: str  # pending, in_progress, completed, failed
    created_at: datetime
    updated_at: datetime
    result: Optional[Dict[str, Any]] = None


class RelationshipCoordinationOrgan(LifeOrgan):
    """林嫣然的关系协调器官"""
    
    def __init__(self, ai_adapter=None):
        super().__init__(
            organ_name="relationship_coordination_organ",
            organ_function="跨模块协同和关系协调",
            ai_adapter=ai_adapter
        )
        
        # 协调相关组件
        self.organ_relationships = {}  # 器官关系映射
        self.coordination_tasks = []  # 协调任务队列
        self.coordination_history = []  # 协调历史
        self.conflict_resolution_rules = {}  # 冲突解决规则
        self.performance_metrics = {}  # 性能指标
        
        # 协调配置
        self.coordination_config = self._load_coordination_config()
        
        # 协调策略
        self.coordination_strategies = {
            CoordinationType.ORGAN_SYNC: self._handle_organ_sync,
            CoordinationType.WORKFLOW_ORCHESTRATION: self._handle_workflow_orchestration,
            CoordinationType.CONFLICT_RESOLUTION: self._handle_conflict_resolution,
            CoordinationType.RESOURCE_ALLOCATION: self._handle_resource_allocation,
            CoordinationType.PRIORITY_MANAGEMENT: self._handle_priority_management,
            CoordinationType.COMMUNICATION_BRIDGE: self._handle_communication_bridge,
            CoordinationType.PERFORMANCE_OPTIMIZATION: self._handle_performance_optimization
        }
        
        # 性能统计
        self.coordination_stats = {
            "total_coordinations": 0,
            "successful_coordinations": 0,
            "failed_coordinations": 0,
            "avg_coordination_time": 0.0,
            "organ_sync_rate": 0.0,
            "conflict_resolution_rate": 0.0,
            "performance_improvement": 0.0,
            "relationship_health": 0.0
        }
        
        self.logger = get_unified_logger(__name__)
        self.logger.info("🤝 林嫣然关系协调器官初始化开始...")
        
        # 初始化协调系统
        self._init_coordination_system()
        
        self.logger.info("🤝 林嫣然关系协调器官初始化完成")
    
    def _load_coordination_config(self) -> Dict[str, Any]:
        """加载协调配置"""
        try:
            config_path = os.path.join("config", "organs", "relationship_coordination_organ.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"🤝 协调配置加载失败: {e}")
        
        # 默认配置
        return {
            "coordination_mode": "intelligent",
            "max_concurrent_tasks": 5,
            "coordination_interval": 60,
            "conflict_resolution_timeout": 30,
            "performance_monitoring": True,
            "auto_optimization": True,
            "yanran_coordination_style": {
                "collaborative_approach": 0.9,
                "conflict_avoidance": 0.8,
                "harmony_preference": 0.95,
                "efficiency_focus": 0.8,
                "empathy_in_coordination": 0.9,
                "patience_level": 0.85
            }
        }
    
    def _init_coordination_system(self):
        """初始化协调系统"""
        try:
            # 获取器官神经网络
            neural_network = get("organ_neural_network")
            if neural_network:
                self.neural_network = neural_network
                self.logger.info("🤝 器官神经网络连接成功")
            else:
                self.logger.warning("🤝 器官神经网络未找到")
            
            # 发现和映射器官关系
            self._discover_organ_relationships()
            
            # 初始化协调规则
            self._init_coordination_rules()
            
            # 启动协调监控任务
            self._start_coordination_monitoring()

            # 🔥 老王添加：启动延迟器官发现任务
            self._start_delayed_organ_discovery()
            
        except Exception as e:
            self.logger.warning(f"🤝 协调系统初始化部分失败: {e}")
    
    def _discover_organ_relationships(self):
        """发现器官关系"""
        try:
            # 获取所有已注册的器官
            registered_organs = self._get_registered_organs()
            
            # 分析器官间的关系
            for organ_name in registered_organs:
                self._analyze_organ_relationships(organ_name, registered_organs)
            
            self.logger.info(f"🤝 发现了 {len(self.organ_relationships)} 个器官关系")
            
        except Exception as e:
            self.logger.error(f"🤝 发现器官关系失败: {e}")
    
    def _get_registered_organs(self) -> List[str]:
        """获取已注册的器官"""
        try:
            # 从单例管理器获取器官列表
            organ_names = []
            
            # 预定义的器官名称
            potential_organs = [
                "world_perception_organ",
                "creative_expression_organ", 
                "safety_protection_organ",
                "skill_coordination_organ",
                "wealth_management_organ",
                "data_perception_organ",
                "proactive_expression_organ",
                "enhanced_proactive_expression_organ"  # 🔥 支持增强版本
            ]
            
            for organ_name in potential_organs:
                # 🔥 老王修复：使用get_silent避免警告，并处理器官未初始化的情况
                from utilities.singleton_manager import get_silent
                organ = get_silent(organ_name)
                if organ:
                    organ_names.append(organ_name)
                else:
                    # 器官还未初始化，记录但不报错
                    self.logger.debug(f"🤝 器官 {organ_name} 尚未初始化，将在后续发现")
            
            return organ_names
            
        except Exception as e:
            self.logger.error(f"🤝 获取器官列表失败: {e}")
            return []

    def _start_delayed_organ_discovery(self):
        """
        🔥 老王添加：启动延迟器官发现任务

        在系统初始化过程中，某些器官可能还未初始化，
        这个任务会定期重新发现器官，确保所有器官都被正确识别
        """
        try:
            import threading
            import time

            def delayed_discovery():
                # 等待5秒，让其他器官有时间初始化
                time.sleep(5)

                try:
                    # 重新发现器官关系
                    old_count = len(self.organ_relationships)
                    self._discover_organ_relationships()
                    new_count = len(self.organ_relationships)

                    if new_count > old_count:
                        self.logger.info(f"🤝 延迟发现了 {new_count - old_count} 个新器官关系")
                    else:
                        self.logger.debug("🤝 延迟发现未找到新器官")

                except Exception as e:
                    self.logger.warning(f"🤝 延迟器官发现失败: {e}")

            # 在后台线程中执行延迟发现
            discovery_thread = threading.Thread(target=delayed_discovery, daemon=True)
            discovery_thread.start()

            self.logger.debug("🤝 延迟器官发现任务已启动")

        except Exception as e:
            self.logger.warning(f"🤝 启动延迟器官发现任务失败: {e}")
    
    def _analyze_organ_relationships(self, organ_name: str, all_organs: List[str]):
        """分析器官关系"""
        try:
            # 基于器官功能定义关系
            relationship_rules = {
                "world_perception_organ": {
                    "creative_expression_organ": RelationshipType.COLLABORATIVE,
                    "proactive_expression_organ": RelationshipType.SUPPORTIVE,
                    "data_perception_organ": RelationshipType.COLLABORATIVE
                },
                "creative_expression_organ": {
                    "world_perception_organ": RelationshipType.DEPENDENT,
                    "skill_coordination_organ": RelationshipType.COLLABORATIVE,
                    "proactive_expression_organ": RelationshipType.SUPPORTIVE
                },
                "safety_protection_organ": {
                    "*": RelationshipType.HIERARCHICAL
                },
                "skill_coordination_organ": {
                    "*": RelationshipType.SUPPORTIVE
                }
            }
            
            organ_rules = relationship_rules.get(organ_name, {})
            
            for target_organ in all_organs:
                if target_organ == organ_name:
                    continue
                
                # 检查特定关系
                if target_organ in organ_rules:
                    relationship_type = organ_rules[target_organ]
                elif "*" in organ_rules:
                    relationship_type = organ_rules["*"]
                else:
                    relationship_type = RelationshipType.INDEPENDENT
                
                # 创建关系对象
                relationship = OrganRelationship(
                    source_organ=organ_name,
                    target_organ=target_organ,
                    relationship_type=relationship_type,
                    strength=self._calculate_relationship_strength(relationship_type),
                    priority=self._calculate_relationship_priority(relationship_type),
                    last_interaction=datetime.now(),
                    interaction_count=0,
                    success_rate=1.0,
                    dependencies=[]
                )
                
                relationship_key = f"{organ_name}->{target_organ}"
                self.organ_relationships[relationship_key] = relationship
                
        except Exception as e:
            self.logger.error(f"🤝 分析器官关系失败: {e}")
    
    def _calculate_relationship_strength(self, rel_type: RelationshipType) -> float:
        """计算关系强度"""
        strength_mapping = {
            RelationshipType.COLLABORATIVE: 0.8,
            RelationshipType.DEPENDENT: 0.9,
            RelationshipType.SUPPORTIVE: 0.7,
            RelationshipType.HIERARCHICAL: 0.85,
            RelationshipType.INDEPENDENT: 0.3,
            RelationshipType.COMPETITIVE: 0.5
        }
        return strength_mapping.get(rel_type, 0.5)
    
    def _calculate_relationship_priority(self, rel_type: RelationshipType) -> int:
        """计算关系优先级"""
        priority_mapping = {
            RelationshipType.DEPENDENT: 9,
            RelationshipType.HIERARCHICAL: 8,
            RelationshipType.COLLABORATIVE: 7,
            RelationshipType.SUPPORTIVE: 6,
            RelationshipType.COMPETITIVE: 5,
            RelationshipType.INDEPENDENT: 3
        }
        return priority_mapping.get(rel_type, 5)
    
    def _init_coordination_rules(self):
        """初始化协调规则"""
        self.conflict_resolution_rules = {
            "resource_conflict": {
                "strategy": "priority_based",
                "timeout": 30,
                "fallback": "round_robin"
            },
            "priority_conflict": {
                "strategy": "user_priority",
                "timeout": 15,
                "fallback": "safety_first"
            },
            "workflow_conflict": {
                "strategy": "sequential_execution",
                "timeout": 60,
                "fallback": "parallel_execution"
            }
        }
    
    def _start_coordination_monitoring(self):
        """启动协调监控"""
        try:
            # 创建协调监控循环
            asyncio.create_task(self._coordination_monitoring_loop())
            self.logger.info("🤝 协调监控任务已启动")
        except Exception as e:
            self.logger.warning(f"🤝 协调监控任务启动失败: {e}")
    
    async def _coordination_monitoring_loop(self):
        """协调监控循环"""
        while True:
            try:
                # 检查协调任务
                await self._check_coordination_tasks()
                
                # 监控器官关系健康度
                await self._monitor_relationship_health()
                
                # 等待下次检查
                await asyncio.sleep(self.coordination_config.get("coordination_interval", 60))
                
            except Exception as e:
                self.logger.error(f"🤝 协调监控循环错误: {e}")
                await asyncio.sleep(30)
    
    async def _check_coordination_tasks(self):
        """检查协调任务"""
        try:
            # 处理待处理的协调任务
            pending_tasks = [task for task in self.coordination_tasks if task.status == "pending"]
            
            for task in pending_tasks:
                await self._execute_coordination_task(task)
            
            # 清理已完成的任务
            self._cleanup_completed_tasks()
            
        except Exception as e:
            self.logger.error(f"🤝 检查协调任务失败: {e}")
    
    async def _execute_coordination_task(self, task: CoordinationTask):
        """执行协调任务"""
        try:
            task.status = "in_progress"
            task.updated_at = datetime.now()
            
            # 根据协调类型选择处理策略
            handler = self.coordination_strategies.get(task.coordination_type)
            if handler:
                result = await handler(task)
                task.result = result
                task.status = "completed" if result.get("success", False) else "failed"
            else:
                task.status = "failed"
                task.result = {"error": "Unknown coordination type"}
            
            # 更新统计信息
            self._update_coordination_stats(task)
            
            self.logger.info(f"🤝 协调任务完成: {task.task_id} - {task.status}")
            
        except Exception as e:
            task.status = "failed"
            task.result = {"error": str(e)}
            self.logger.error(f"🤝 执行协调任务失败: {e}")
    
    async def _handle_organ_sync(self, task: CoordinationTask) -> Dict[str, Any]:
        """处理器官同步"""
        try:
            # 同步器官状态
            sync_results = {}
            
            for organ_name in task.involved_organs:
                organ = get(organ_name)
                if organ:
                    # 获取器官状态
                    if hasattr(organ, 'get_organ_status'):
                        status = organ.get_organ_status()
                        sync_results[organ_name] = status
                    else:
                        sync_results[organ_name] = {"status": "unknown"}
                else:
                    sync_results[organ_name] = {"status": "not_found"}
            
            return {
                "success": True,
                "sync_results": sync_results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_workflow_orchestration(self, task: CoordinationTask) -> Dict[str, Any]:
        """处理工作流编排"""
        try:
            # 简化的工作流编排
            orchestration_plan = {
                "plan_type": "sequential",
                "steps": [{"organ": organ, "action": "process"} for organ in task.involved_organs]
            }
            
            execution_result = {"executed_steps": len(orchestration_plan["steps"]), "success": True}
            
            return {
                "success": True,
                "orchestration_plan": orchestration_plan,
                "execution_result": execution_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_conflict_resolution(self, task: CoordinationTask) -> Dict[str, Any]:
        """处理冲突解决"""
        try:
            conflict_type = "resource_conflict"  # 简化实现
            resolution_strategy = "priority_based"
            resolution_result = {"strategy_applied": resolution_strategy, "resolution_success": True}
            
            return {
                "success": True,
                "conflict_type": conflict_type,
                "resolution_strategy": resolution_strategy,
                "resolution_result": resolution_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_resource_allocation(self, task: CoordinationTask) -> Dict[str, Any]:
        """处理资源分配"""
        try:
            resource_requirements = {"cpu": 0.5, "memory": 0.3, "network": 0.2}
            allocation_plan = {"allocation": resource_requirements, "priority": "balanced"}
            allocation_result = {"allocation_success": True, "allocated_resources": resource_requirements}
            
            return {
                "success": True,
                "resource_requirements": resource_requirements,
                "allocation_plan": allocation_plan,
                "allocation_result": allocation_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_priority_management(self, task: CoordinationTask) -> Dict[str, Any]:
        """处理优先级管理"""
        try:
            priority_conflicts = []
            new_priorities = {}
            application_result = {"priorities_applied": len(new_priorities)}
            
            return {
                "success": True,
                "priority_conflicts": priority_conflicts,
                "new_priorities": new_priorities,
                "application_result": application_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_communication_bridge(self, task: CoordinationTask) -> Dict[str, Any]:
        """处理通信桥接"""
        try:
            bridge_connections = task.involved_organs
            communication_test = {"quality_score": 0.9, "latency": 10}
            
            return {
                "success": True,
                "bridge_connections": bridge_connections,
                "communication_test": communication_test,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_performance_optimization(self, task: CoordinationTask) -> Dict[str, Any]:
        """处理性能优化"""
        try:
            performance_analysis = {"bottlenecks": [], "overall_performance": 0.8}
            optimization_suggestions = ["optimize_caching", "reduce_latency"]
            optimization_result = {"applied_optimizations": len(optimization_suggestions), "performance_improvement": 0.1}
            
            return {
                "success": True,
                "performance_analysis": performance_analysis,
                "optimization_suggestions": optimization_suggestions,
                "optimization_result": optimization_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    # 协调任务创建和管理
    async def create_coordination_task(self, coordination_type: CoordinationType, 
                                     involved_organs: List[str], 
                                     primary_organ: str,
                                     task_description: str,
                                     priority: int = 5,
                                     deadline: Optional[datetime] = None) -> str:
        """创建协调任务"""
        try:
            task_id = f"coord_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.coordination_tasks)}"
            
            task = CoordinationTask(
                task_id=task_id,
                coordination_type=coordination_type,
                involved_organs=involved_organs,
                primary_organ=primary_organ,
                task_description=task_description,
                priority=priority,
                deadline=deadline,
                status="pending",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.coordination_tasks.append(task)
            
            self.logger.info(f"🤝 创建协调任务: {task_id}")
            return task_id
            
        except Exception as e:
            self.logger.error(f"🤝 创建协调任务失败: {e}")
            return ""
    
    async def request_organ_coordination(self, requesting_organ: str, 
                                       target_organs: List[str], 
                                       coordination_purpose: str) -> Dict[str, Any]:
        """请求器官协调"""
        try:
            # 使用AI决策是否需要协调
            coordination_decision = await self._ai_decide_coordination(
                requesting_organ, target_organs, coordination_purpose
            )
            
            if coordination_decision.get("should_coordinate", False):
                # 创建协调任务
                task_id = await self.create_coordination_task(
                    coordination_type=CoordinationType.WORKFLOW_ORCHESTRATION,
                    involved_organs=[requesting_organ] + target_organs,
                    primary_organ=requesting_organ,
                    task_description=coordination_purpose,
                    priority=coordination_decision.get("priority", 5)
                )
                
                return {
                    "coordination_granted": True,
                    "task_id": task_id,
                    "coordination_plan": coordination_decision.get("coordination_plan", {}),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "coordination_granted": False,
                    "reason": coordination_decision.get("reason", "Coordination not needed"),
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"🤝 请求器官协调失败: {e}")
            return {
                "coordination_granted": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _ai_decide_coordination(self, requesting_organ: str, 
                                    target_organs: List[str], 
                                    purpose: str) -> Dict[str, Any]:
        """AI决策是否需要协调"""
        try:
            decision_context = {
                "requesting_organ": requesting_organ,
                "target_organs": target_organs,
                "coordination_purpose": purpose,
                "current_relationships": self._get_relevant_relationships(requesting_organ, target_organs),
                "system_load": self._get_system_load(),
                "yanran_coordination_style": self.coordination_config["yanran_coordination_style"]
            }
            
            ai_decision = await self.ai_decision_engine.decide(
                organ_prompt=self._get_coordination_decision_prompt(),
                context=decision_context,
                decision_type="organ_coordination"
            )
            
            return self._parse_coordination_decision(ai_decision)
            
        except Exception as e:
            self.logger.error(f"🤝 AI协调决策失败: {e}")
            return {"should_coordinate": False, "reason": "Decision error"}
    
    def _get_coordination_decision_prompt(self) -> str:
        """获取协调决策提示词"""
        return """
        你是林嫣然的关系协调器官，负责统筹各器官的协同工作。

        林嫣然的协调特点：
        - 追求和谐，避免冲突
        - 重视效率，但不牺牲质量
        - 善于平衡各方需求
        - 以用户体验为中心
        - 具备同理心，理解各器官的需求
        - 决策公正，基于整体利益

        请基于以下信息决定是否需要协调：
        1. 分析请求的合理性和必要性
        2. 评估协调的成本和收益
        3. 考虑当前系统负载和优先级
        4. 确保协调符合林嫣然的协调风格

        返回JSON格式，包含：
        - should_coordinate: 是否需要协调
        - priority: 协调优先级 (1-10)
        - coordination_plan: 协调计划
        - reasoning: 决策理由
        """
    
    def _parse_coordination_decision(self, ai_decision) -> Dict[str, Any]:
        """解析协调决策"""
        try:
            if isinstance(ai_decision, str):
                # 简单解析
                should_coordinate = "should_coordinate" in ai_decision.lower() and "true" in ai_decision.lower()
                return {
                    "should_coordinate": should_coordinate,
                    "priority": 5,
                    "coordination_plan": {},
                    "reason": "Basic decision"
                }
            
            return {
                "should_coordinate": ai_decision.get("should_coordinate", False),
                "priority": ai_decision.get("priority", 5),
                "coordination_plan": ai_decision.get("coordination_plan", {}),
                "reason": ai_decision.get("reasoning", "")
            }
            
        except Exception as e:
            self.logger.error(f"🤝 解析协调决策失败: {e}")
            return {"should_coordinate": False, "reason": "Parse error"}
    
    # 辅助方法
    def _get_relevant_relationships(self, requesting_organ: str, target_organs: List[str]) -> Dict[str, Any]:
        """获取相关关系"""
        relevant = {}
        for target in target_organs:
            key = f"{requesting_organ}->{target}"
            if key in self.organ_relationships:
                relevant[key] = self.organ_relationships[key]
        return relevant
    
    def _get_system_load(self) -> Dict[str, Any]:
        """获取系统负载"""
        return {
            "active_tasks": len([t for t in self.coordination_tasks if t.status == "in_progress"]),
            "pending_tasks": len([t for t in self.coordination_tasks if t.status == "pending"]),
            "total_tasks": len(self.coordination_tasks)
        }
    
    def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        try:
            # 保留最近100个已完成的任务
            completed_tasks = [t for t in self.coordination_tasks if t.status in ["completed", "failed"]]
            if len(completed_tasks) > 100:
                # 移动到历史记录
                self.coordination_history.extend(completed_tasks[:-50])
                # 保留最近50个
                self.coordination_tasks = [t for t in self.coordination_tasks 
                                         if t.status not in ["completed", "failed"]] + completed_tasks[-50:]
            
        except Exception as e:
            self.logger.error(f"🤝 清理任务失败: {e}")
    
    def _update_coordination_stats(self, task: CoordinationTask):
        """更新协调统计"""
        try:
            self.coordination_stats["total_coordinations"] += 1
            
            if task.status == "completed":
                self.coordination_stats["successful_coordinations"] += 1
            elif task.status == "failed":
                self.coordination_stats["failed_coordinations"] += 1
            
            # 计算成功率
            total = self.coordination_stats["total_coordinations"]
            if total > 0:
                success_rate = self.coordination_stats["successful_coordinations"] / total
                self.coordination_stats["organ_sync_rate"] = success_rate
                
        except Exception as e:
            self.logger.error(f"🤝 更新统计失败: {e}")
    
    async def _monitor_relationship_health(self):
        """监控关系健康度"""
        try:
            total_health = 0
            count = 0
            
            for relationship in self.organ_relationships.values():
                # 计算关系健康度
                health = relationship.success_rate * relationship.strength
                total_health += health
                count += 1
            
            if count > 0:
                avg_health = total_health / count
                self.coordination_stats["relationship_health"] = avg_health
                
        except Exception as e:
            self.logger.error(f"🤝 监控关系健康度失败: {e}")
    
    def get_coordination_stats(self) -> Dict[str, Any]:
        """获取协调统计"""
        return {
            **self.coordination_stats,
            "active_relationships": len(self.organ_relationships),
            "pending_tasks": len([t for t in self.coordination_tasks if t.status == "pending"]),
            "total_tasks": len(self.coordination_tasks)
        }
    
    async def _get_organ_specific_prompt(self) -> str:
        """获取器官特定提示词"""
        return """
        你是林嫣然的关系协调器官，负责统筹各器官的协同工作。
        你的特点是追求和谐、重视效率、善于平衡、具备同理心。
        """
    
    async def _execute_decision(self, ai_decision: Dict[str, Any], processed_input: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI决策"""
        try:
            decision_type = ai_decision.get("decision_type", "coordination")
            
            if decision_type == "organ_coordination":
                # 处理器官协调决策
                should_coordinate = ai_decision.get("should_coordinate", False)
                if should_coordinate:
                    coordination_plan = ai_decision.get("coordination_plan", {})
                    
                    # 创建协调任务
                    task_id = await self.create_coordination_task(
                        coordination_type=CoordinationType.WORKFLOW_ORCHESTRATION,
                        involved_organs=coordination_plan.get("involved_organs", []),
                        primary_organ=coordination_plan.get("primary_organ", "unknown"),
                        task_description=coordination_plan.get("description", "AI-initiated coordination"),
                        priority=ai_decision.get("priority", 5)
                    )
                    
                    return {
                        "action": "coordination_initiated",
                        "task_id": task_id,
                        "coordination_plan": coordination_plan,
                        "timestamp": datetime.now().isoformat()
                    }
            
            return {
                "action": "no_action",
                "reason": "No valid decision type"
            }
            
        except Exception as e:
            self.logger.error(f"🤝 执行决策失败: {e}")
            return {
                "action": "error",
                "error": str(e)
            }


def get_instance(ai_adapter=None) -> RelationshipCoordinationOrgan:
    """获取关系协调器官实例"""
    from utilities.singleton_manager import get_or_create
    return get_or_create("relationship_coordination_organ", lambda: RelationshipCoordinationOrgan(ai_adapter)) 