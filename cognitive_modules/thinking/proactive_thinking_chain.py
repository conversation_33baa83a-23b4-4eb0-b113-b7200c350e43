"""
主动表达思维链路增强模块
集成现有33个认知模块，实现思维链路与主动表达的联动
Phase 2: 记忆-思维集成的核心组件
"""

import asyncio
import time
from typing import Dict, List, Optional
from datetime import datetime
from utilities.unified_logger import get_unified_logger, setup_unified_logging

logger = get_unified_logger(__name__)

class ProactiveThinkingChain:
    """主动表达思维链路"""
    
    def __init__(self):
        self.enhanced_memory_integration = None
        self.yanran_proactive_trigger = None
        self.proactive_service = None
        self.is_initialized = False
        
        # 思维链路步骤配置
        self.thinking_steps = [
            {
                "id": "memory_retrieval",
                "name": "记忆检索",
                "description": "检索用户相关记忆和上下文",
                "timeout": 3.0,
                "required": True
            },
            {
                "id": "relationship_analysis",
                "name": "关系分析", 
                "description": "分析用户关系状态和边界感",
                "timeout": 2.0,
                "required": True
            },
            {
                "id": "opportunity_detection",
                "name": "机会检测",
                "description": "检测主动表达机会",
                "timeout": 5.0,
                "required": True
            },
            {
                "id": "content_generation",
                "name": "内容生成",
                "description": "生成主动表达内容",
                "timeout": 8.0,
                "required": True
            },
            {
                "id": "boundary_check",
                "name": "边界检查",
                "description": "最终边界感检查",
                "timeout": 2.0,
                "required": True
            }
        ]
        
    async def initialize(self):
        """初始化主动表达思维链路"""
        try:
            # 延迟导入避免循环依赖
            from cognitive_modules.memory.proactive.enhanced_memory_integration import get_enhanced_memory_integration
            from cognitive_modules.behavior.proactive.yanran_proactive_trigger import get_yanran_proactive_trigger
            from services.proactive_expression_service import get_proactive_service
            
            self.enhanced_memory_integration = get_enhanced_memory_integration()
            self.yanran_proactive_trigger = get_yanran_proactive_trigger()
            self.proactive_service = get_proactive_service()
            
            # 初始化依赖组件
            await self.enhanced_memory_integration.initialize()
            await self.yanran_proactive_trigger.initialize()
            # await self.proactive_service.initialize()  # 注释掉以避免启动WebSocket，使用统一WebSocket服务
            logger.info("💬 主动表达思维链使用统一WebSocket服务")
            
            self.is_initialized = True
            logger.info("✅ 主动表达思维链路初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 主动表达思维链路初始化失败: {e}")
            raise
    
    async def execute_proactive_thinking_process(self, user_id: str, trigger_context: Dict = None) -> Dict:
        """执行主动表达思维过程"""
        try:
            thinking_context = {
                "user_id": user_id,
                "start_time": time.time(),
                "trigger_context": trigger_context or {},
                "thinking_steps": [],
                "final_decision": {},
                "execution_time": 0,
                "success": False
            }
            
            logger.info(f"🧠 开始执行用户 {user_id} 的主动表达思维过程")
            
            # 执行思维链路步骤
            for step in self.thinking_steps:
                step_result = await self._execute_thinking_step(user_id, step, thinking_context)
                thinking_context["thinking_steps"].append(step_result)
                
                # 如果必需步骤失败，终止思维过程
                if step["required"] and not step_result.get("success", False):
                    logger.warning(f"⚠️ 必需思维步骤失败: {step['name']}")
                    thinking_context["final_decision"] = {
                        "action": "abort",
                        "reason": f"必需步骤失败: {step['name']}",
                        "step_failed": step["id"]
                    }
                    break
            
            # 生成最终决策
            if not thinking_context["final_decision"]:
                thinking_context["final_decision"] = await self._generate_final_decision(thinking_context)
            
            thinking_context["execution_time"] = time.time() - thinking_context["start_time"]
            thinking_context["success"] = thinking_context["final_decision"].get("action") == "express"
            
            logger.info(f"🧠 用户 {user_id} 思维过程完成，耗时 {thinking_context['execution_time']:.2f}s，决策: {thinking_context['final_decision'].get('action')}")
            
            return thinking_context
            
        except Exception as e:
            logger.error(f"❌ 执行主动表达思维过程失败 (用户: {user_id}): {e}")
            return {
                "user_id": user_id,
                "success": False,
                "error": str(e),
                "final_decision": {"action": "abort", "reason": "思维过程异常"}
            }
    
    async def _execute_thinking_step(self, user_id: str, step: Dict, context: Dict) -> Dict:
        """执行单个思维步骤"""
        step_start_time = time.time()
        step_result = {
            "step_id": step["id"],
            "step_name": step["name"],
            "start_time": step_start_time,
            "success": False,
            "result": {},
            "execution_time": 0,
            "error": None
        }
        
        try:
            # 根据步骤ID执行相应的逻辑
            if step["id"] == "memory_retrieval":
                step_result["result"] = await self._step_memory_retrieval(user_id, context)
            elif step["id"] == "relationship_analysis":
                step_result["result"] = await self._step_relationship_analysis(user_id, context)
            elif step["id"] == "opportunity_detection":
                step_result["result"] = await self._step_opportunity_detection(user_id, context)
            elif step["id"] == "content_generation":
                step_result["result"] = await self._step_content_generation(user_id, context)
            elif step["id"] == "boundary_check":
                step_result["result"] = await self._step_boundary_check(user_id, context)
            else:
                step_result["result"] = {"message": f"未知步骤: {step['id']}"}
            
            step_result["success"] = True
            
        except Exception as e:
            step_result["error"] = str(e)
            logger.error(f"❌ 思维步骤执行失败 ({step['name']}): {e}")
        
        step_result["execution_time"] = time.time() - step_start_time
        return step_result
    
    async def _step_memory_retrieval(self, user_id: str, context: Dict) -> Dict:
        """步骤1: 记忆检索"""
        enhanced_memory = await self.enhanced_memory_integration.get_enhanced_user_memory(user_id)
        return {
            "enhanced_memory": enhanced_memory,
            "memory_quality": self._assess_memory_quality(enhanced_memory),
            "key_insights": self._extract_key_insights(enhanced_memory)
        }
    
    async def _step_relationship_analysis(self, user_id: str, context: Dict) -> Dict:
        """步骤2: 关系分析"""
        memory_data = context.get("thinking_steps", [{}])[-1].get("result", {}).get("enhanced_memory", {})
        relationship_context = memory_data.get("relationship_context", {})
        
        return {
            "relationship_type": relationship_context.get("relationship_type", "stranger"),
            "intimacy_level": relationship_context.get("intimacy_level", 0),
            "communication_style": relationship_context.get("communication_style", "formal"),
            "boundary_assessment": self._assess_boundaries(relationship_context),
            "interaction_readiness": self._assess_interaction_readiness(relationship_context)
        }
    
    async def _step_opportunity_detection(self, user_id: str, context: Dict) -> Dict:
        """步骤3: 机会检测"""
        opportunities = await self.yanran_proactive_trigger.evaluate_proactive_opportunities(user_id)
        
        return {
            "opportunities": opportunities,
            "opportunity_count": len(opportunities),
            "best_opportunity": opportunities[0] if opportunities else None,
            "opportunity_score": opportunities[0].get("score", 0.0) if opportunities else 0.0
        }
    
    async def _step_content_generation(self, user_id: str, context: Dict) -> Dict:
        """步骤4: 内容生成"""
        opportunities = context.get("thinking_steps", [{}])[-1].get("result", {}).get("opportunities", [])
        best_opportunity = opportunities[0] if opportunities else None
        
        if not best_opportunity:
            return {"content": None, "generation_success": False, "reason": "无推荐机会"}
        
        content = await self.yanran_proactive_trigger.generate_yanran_proactive_content(
            user_id=user_id,
            trigger_type=best_opportunity.get("type", "default"),
            context=best_opportunity
        )
        
        return {
            "content": content,
            "generation_success": content is not None,
            "content_quality": self._assess_content_quality(content) if content else 0.0
        }
    
    async def _step_boundary_check(self, user_id: str, context: Dict) -> Dict:
        """步骤5: 边界检查"""
        content = context.get("thinking_steps", [{}])[-1].get("result", {}).get("content")
        relationship_analysis = context.get("thinking_steps", [{}])[-3].get("result", {})
        
        if not content:
            return {"boundary_check_passed": False, "reason": "无内容可检查"}
        
        boundary_check = await self._perform_boundary_check(user_id, content, relationship_analysis)
        
        return {
            "boundary_check_passed": boundary_check["passed"],
            "boundary_score": boundary_check["score"],
            "boundary_warnings": boundary_check.get("warnings", []),
            "adjusted_content": boundary_check.get("adjusted_content", content)
        }
    
    async def _generate_final_decision(self, thinking_context: Dict) -> Dict:
        """生成最终决策"""
        steps = thinking_context.get("thinking_steps", [])
        
        # 检查关键步骤是否成功
        content_step = next((s for s in steps if s["step_id"] == "content_generation"), None)
        boundary_step = next((s for s in steps if s["step_id"] == "boundary_check"), None)
        
        if not content_step or not content_step.get("success"):
            return {"action": "abort", "reason": "内容生成失败"}
        
        content = content_step.get("result", {}).get("content")
        if not content:
            return {"action": "abort", "reason": "无有效内容"}
        
        if boundary_step and not boundary_step.get("result", {}).get("boundary_check_passed"):
            return {"action": "abort", "reason": "边界检查未通过"}
        
        return {
            "action": "express",
            "content": content,
            "confidence": self._calculate_decision_confidence(thinking_context),
            "reasoning": "所有检查通过，可以主动表达"
        }
    
    # 辅助方法
    def _assess_memory_quality(self, memory_data: Dict) -> float:
        """评估记忆质量"""
        if not memory_data:
            return 0.0
        
        quality_factors = []
        
        # 检查记忆层完整性
        memory_layers = memory_data.get("memory_layers", {})
        completeness = len([k for k, v in memory_layers.items() if v]) / len(memory_layers)
        quality_factors.append(completeness)
        
        # 检查洞察质量
        insights = memory_data.get("consolidated_insights", {})
        insight_quality = 1.0 if insights.get("user_profile_summary") else 0.5
        quality_factors.append(insight_quality)
        
        return sum(quality_factors) / len(quality_factors) if quality_factors else 0.0
    
    def _extract_key_insights(self, memory_data: Dict) -> List[str]:
        """提取关键洞察"""
        insights = []
        
        consolidated = memory_data.get("consolidated_insights", {})
        if consolidated.get("user_profile_summary"):
            insights.append(consolidated["user_profile_summary"])
        
        if consolidated.get("interaction_patterns"):
            insights.extend(consolidated["interaction_patterns"])
        
        return insights
    
    def _assess_boundaries(self, relationship_context: Dict) -> Dict:
        """评估边界"""
        intimacy_level = relationship_context.get("intimacy_level", 0)
        
        return {
            "intimacy_level": intimacy_level,
            "boundary_level": "high" if intimacy_level < 1000 else "medium" if intimacy_level < 10000 else "low",
            "sharing_allowed": intimacy_level > 500,
            "proactive_allowed": intimacy_level > 100
        }
    
    def _assess_interaction_readiness(self, relationship_context: Dict) -> float:
        """评估交互准备度"""
        intimacy_level = relationship_context.get("intimacy_level", 0)
        
        if intimacy_level > 20000:
            return 0.9
        elif intimacy_level > 10000:
            return 0.7
        elif intimacy_level > 1000:
            return 0.5
        elif intimacy_level > 100:
            return 0.3
        else:
            return 0.1
    
    def _assess_content_quality(self, content: Dict) -> float:
        """评估内容质量"""
        if not content:
            return 0.0
        
        quality_factors = []
        
        # 检查内容完整性
        if content.get("content"):
            quality_factors.append(1.0)
        else:
            quality_factors.append(0.0)
        
        # 检查人格应用
        if content.get("yanran_personality_applied"):
            quality_factors.append(1.0)
        else:
            quality_factors.append(0.5)
        
        # 检查触发类型
        if content.get("trigger_type"):
            quality_factors.append(1.0)
        else:
            quality_factors.append(0.5)
        
        return sum(quality_factors) / len(quality_factors)
    
    async def _perform_boundary_check(self, user_id: str, content: Dict, relationship_analysis: Dict) -> Dict:
        """执行边界检查"""
        boundary_assessment = relationship_analysis.get("boundary_assessment", {})
        
        check_result = {
            "passed": True,
            "score": 1.0,
            "warnings": [],
            "adjusted_content": content
        }
        
        # 检查亲密度要求
        intimacy_level = boundary_assessment.get("intimacy_level", 0)
        if intimacy_level < 100:
            check_result["passed"] = False
            check_result["warnings"].append("用户亲密度不足，不建议主动表达")
        
        # 检查分享深度
        if not boundary_assessment.get("sharing_allowed", False):
            check_result["passed"] = False
            check_result["warnings"].append("用户边界设置不允许主动分享")
        
        return check_result
    
    def _calculate_decision_confidence(self, thinking_context: Dict) -> float:
        """计算决策置信度"""
        steps = thinking_context.get("thinking_steps", [])
        
        success_count = sum(1 for step in steps if step.get("success", False))
        total_steps = len(steps)
        
        if total_steps == 0:
            return 0.0
        
        return success_count / total_steps


# 单例管理
_proactive_thinking_chain_instance = None

def get_proactive_thinking_chain() -> ProactiveThinkingChain:
    """获取主动表达思维链路单例"""
    global _proactive_thinking_chain_instance
    if _proactive_thinking_chain_instance is None:
        _proactive_thinking_chain_instance = ProactiveThinkingChain()
    return _proactive_thinking_chain_instance 