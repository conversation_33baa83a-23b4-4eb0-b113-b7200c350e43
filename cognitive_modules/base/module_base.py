#!/usr/bin/env python3
"""
认知模块基类 - Cognitive Module Base

该模块提供了认知模块的基础实现，实现了ICognitiveModule接口，
并提供了一些通用功能，所有具体的认知模块可以继承这个基类。

作者: Claude
创建日期: 2024-07-15
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Set, Type, Tuple

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.cognitive_interface import ICognitiveModule

# 配置日志记录
setup_unified_logging()

class CognitiveModuleBase(ICognitiveModule):
    """
    认知模块基类
    
    提供ICognitiveModule接口的基本实现，包括模块初始化、
    状态管理、配置管理等通用功能。具体的认知模块可以继承这个基类。
    """
    
    def __init__(self, module_id: str, module_type: str, config: Dict[str, Any] = None):
        """
        初始化认知模块
        
        参数:
            module_id: 模块唯一标识符
            module_type: 模块类型（perception, emotion, memory, cognition, autonomy, behavior等）
            config: 配置信息
        """
        # 模块标识和类型
        self.module_id = module_id
        self.module_type = module_type
        
        # 日志记录器
        self.logger = get_unified_logger(f"cognitive.{module_type}.{module_id}")
        
        # 配置信息
        if isinstance(config, dict):
            self.config = config
        elif config is None:
            self.config = {}
        else:
            self.config = {}
            self.logger.warning_status(f"模块 {module_id} 构造函数接收到非字典类型的config: {type(config)}，已重置为空字典")
        
        # 模块状态
        self.is_initialized = False
        self.is_active = False
        self.last_update_time = 0
        self.last_process_time = 0
        self.error_count = 0
        self.process_count = 0
        
        # 模块内部状态
        self.state: Dict[str, Any] = {}
        
        # 处理锁，确保线程安全
        self.process_lock = threading.Lock()
        
        # 模块描述
        self.description = "基础认知模块"
        
        # 依赖模块
        self.dependencies: List[str] = []
        
        self.logger.info(f"模块 {module_id} 已创建")
    
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化或重新初始化模块
        
        参数:
            config: 配置信息
            
        返回:
            bool: 初始化是否成功
        """
        try:
            # 更新配置
            if config:
                self.config.update(config)
            
            # 执行初始化逻辑
            self._do_initialize()
            
            # 标记为已初始化
            self.is_initialized = True
            self.error_count = 0
            self.logger.success(f"模块 {self.module_id} 初始化成功")
            return True
        except Exception as e:
            self.error_count += 1
            self.logger.error_status(f"模块 {self.module_id} 初始化失败: {str(e)}")
            return False
    
    def _do_initialize(self) -> None:
        """
        执行具体的初始化逻辑
        
        子类应该重写这个方法，实现具体的初始化逻辑。
        基类实现为空，确保即使子类不重写也不会报错。
        """
        pass
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        参数:
            input_data: 输入数据
            
        返回:
            Dict[str, Any]: 处理结果
        """
        # 如果模块未初始化或未激活，返回错误
        if not self.is_initialized:
            self.logger.error_status(f"模块 {self.module_id} 未初始化")
            return {"error": "module_not_initialized", "module_id": self.module_id}
        
        if not self.is_active:
            self.logger.warning_status(f"模块 {self.module_id} 未激活")
            return {"error": "module_not_active", "module_id": self.module_id}
        
        # 确保线程安全
        with self.process_lock:
            start_time = time.time()
            
            try:
                # 更新处理计数和时间
                self.process_count += 1
                self.last_process_time = start_time
                
                # 调用具体的处理逻辑
                result = self._do_process(input_data)
                
                # 计算处理时间
                process_time = time.time() - start_time
                
                # 返回结果，包含处理元数据
                return {
                    **result,
                    "meta": {
                        "module_id": self.module_id,
                        "module_type": self.module_type,
                        "process_time": process_time,
                        "process_count": self.process_count
                    }
                }
            except Exception as e:
                # 记录错误并增加错误计数
                self.error_count += 1
                self.logger.error_status(f"模块 {self.module_id} 处理异常: {str(e)}")
                
                # 返回错误信息
                return {
                    "error": "module_processing_error",
                    "module_id": self.module_id,
                    "error_message": str(e),
                    "meta": {
                        "module_id": self.module_id,
                        "module_type": self.module_type,
                        "process_time": time.time() - start_time,
                        "process_count": self.process_count,
                        "error_count": self.error_count
                    }
                }
    
    def _do_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行具体的处理逻辑
        
        子类应该重写这个方法，实现具体的处理逻辑。
        基类实现返回空结果，确保即使子类不重写也不会报错。
        
        参数:
            input_data: 输入数据
            
        返回:
            Dict[str, Any]: 处理结果
        """
        return {"result": "not_implemented"}
    
    def update(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        根据上下文信息更新模块的内部状态。
        
        参数:
            context: 上下文信息
            
        返回:
            bool: 更新成功返回True，否则返回False
        """
        try:
            # 更新最后更新时间
            self.last_update_time = time.time()
            
            # 调用具体的更新逻辑
            self._do_update(context)
            
            return True
        except Exception as e:
            self.error_count += 1
            self.logger.error_status(f"模块 {self.module_id} 状态更新异常: {str(e)}")
            return False
    
    def _do_update(self, context: Dict[str, Any]) -> None:
        """
        执行具体的状态更新逻辑
        
        子类应该重写这个方法，实现具体的状态更新逻辑。
        基类实现为空，确保即使子类不重写也不会报错。
        
        参数:
            context: 上下文信息
        """
        pass
    
    def get_state(self) -> Dict[str, Any]:
        """
        获取模块当前状态
        
        返回模块的当前内部状态信息。
        
        返回:
            Dict[str, Any]: 模块状态信息
        """
        return {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "is_initialized": self.is_initialized,
            "is_active": self.is_active,
            "last_update_time": self.last_update_time,
            "last_process_time": self.last_process_time,
            "error_count": self.error_count,
            "process_count": self.process_count,
            "state": self.state
        }
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        返回:
            Dict[str, Any]: 模块状态信息
        """
        return {
            "id": self.module_id,
            "type": self.module_type,
            "description": self.description,
            "active": self.is_active,
            "initialized": self.is_initialized,
            "health": self.check_health()
        }
    
    def check_health(self) -> Dict[str, Any]:
        """
        检查模块健康状态
        
        检查模块是否处于健康状态，包括初始化状态、错误计数等。
        
        返回:
            Dict[str, Any]: 健康状态信息
        """
        healthy = (
            self.is_initialized and 
            self.error_count < 5  # 可配置的错误阈值
        )
        
        return {
            "healthy": healthy,
            "error_count": self.error_count,
            "last_update": self.last_update_time,
            "last_process": self.last_process_time
        }
    
    def activate(self) -> bool:
        """
        激活模块
        
        激活模块，使其能够处理请求。
        
        返回:
            bool: 激活成功返回True，否则返回False
        """
        if not self.is_initialized:
            self.logger.error_status(f"模块 {self.module_id} 未初始化，无法激活")
            return False
        
        try:
            # 执行激活逻辑
            self._do_activate()
            
            # 标记为已激活
            self.is_active = True
            self.logger.info(f"模块 {self.module_id} 已激活")
            return True
        except Exception as e:
            self.error_count += 1
            self.logger.error_status(f"模块 {self.module_id} 激活失败: {str(e)}")
            return False
    
    def _do_activate(self) -> None:
        """
        执行具体的激活逻辑
        
        子类应该重写这个方法，实现具体的激活逻辑。
        基类实现为空，确保即使子类不重写也不会报错。
        """
        pass
    
    def deactivate(self) -> bool:
        """
        停用模块
        
        停用模块，使其不再处理请求。
        
        返回:
            bool: 停用成功返回True，否则返回False
        """
        if not self.is_active:
            self.logger.info(f"模块 {self.module_id} 已经处于停用状态")
            return True
        
        try:
            # 执行停用逻辑
            self._do_deactivate()
            
            # 标记为已停用
            self.is_active = False
            self.logger.info(f"模块 {self.module_id} 已停用")
            return True
        except Exception as e:
            self.error_count += 1
            self.logger.error_status(f"模块 {self.module_id} 停用失败: {str(e)}")
            return False
    
    def _do_deactivate(self) -> None:
        """
        执行具体的停用逻辑
        
        子类应该重写这个方法，实现具体的停用逻辑。
        基类实现为空，确保即使子类不重写也不会报错。
        """
        pass
    
    def shutdown(self) -> bool:
        """
        关闭模块，执行必要的清理工作
        
        返回:
            bool: 关闭成功返回True，否则返回False
        """
        try:
            # 如果模块处于激活状态，先停用
            if self.is_active:
                self.deactivate()
            
            # 执行关闭逻辑
            self._do_shutdown()
            
            # 标记为未初始化
            self.is_initialized = False
            self.logger.info(f"模块 {self.module_id} 已关闭")
            return True
        except Exception as e:
            self.error_count += 1
            self.logger.error_status(f"模块 {self.module_id} 关闭失败: {str(e)}")
            return False
    
    def _do_shutdown(self) -> None:
        """
        执行具体的关闭逻辑
        
        子类应该重写这个方法，实现具体的关闭逻辑。
        基类实现为空，确保即使子类不重写也不会报错。
        """
        pass
    
    def save_state(self) -> Dict[str, Any]:
        """
        保存模块状态
        
        返回可序列化的状态数据，用于持久化存储。
        
        返回:
            Dict[str, Any]: 状态数据
        """
        # 基础状态信息
        state_data = {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "is_initialized": self.is_initialized,
            "is_active": self.is_active,
            "last_update_time": self.last_update_time,
            "last_process_time": self.last_process_time,
            "error_count": self.error_count,
            "process_count": self.process_count
        }
        
        # 调用子类实现的状态保存逻辑
        try:
            custom_state = self._do_save_state()
            if custom_state:
                state_data["custom_state"] = custom_state
        except Exception as e:
            self.logger.error_status(f"模块 {self.module_id} 保存状态异常: {str(e)}")
        
        return state_data
    
    def _do_save_state(self) -> Dict[str, Any]:
        """
        执行具体的状态保存逻辑
        
        子类应该重写这个方法，实现具体的状态保存逻辑。
        基类实现返回空字典，确保即使子类不重写也不会报错。
        
        返回:
            Dict[str, Any]: 自定义状态数据
        """
        return {}
    
    def load_state(self, state_data: Dict[str, Any]) -> bool:
        """
        加载模块状态
        
        从状态数据恢复模块状态。
        
        参数:
            state_data: 状态数据
            
        返回:
            bool: 加载成功返回True，否则返回False
        """
        try:
            # 加载基础状态信息
            if "is_initialized" in state_data:
                self.is_initialized = state_data["is_initialized"]
            if "is_active" in state_data:
                self.is_active = state_data["is_active"]
            if "last_update_time" in state_data:
                self.last_update_time = state_data["last_update_time"]
            if "last_process_time" in state_data:
                self.last_process_time = state_data["last_process_time"]
            if "error_count" in state_data:
                self.error_count = state_data["error_count"]
            if "process_count" in state_data:
                self.process_count = state_data["process_count"]
            
            # 调用子类实现的状态加载逻辑
            if "custom_state" in state_data:
                self._do_load_state(state_data["custom_state"])
            
            self.logger.success(f"模块 {self.module_id} 状态加载成功")
            return True
        except Exception as e:
            self.error_count += 1
            self.logger.error_status(f"模块 {self.module_id} 状态加载异常: {str(e)}")
            return False
    
    def _do_load_state(self, custom_state: Dict[str, Any]) -> None:
        """
        执行具体的状态加载逻辑
        
        子类应该重写这个方法，实现具体的状态加载逻辑。
        基类实现为空，确保即使子类不重写也不会报错。
        
        参数:
            custom_state: 自定义状态数据
        """
        pass

    def update_config(self, config: Dict[str, Any]) -> bool:
        """
        更新模块配置
        
        参数:
            config: 新的配置信息
            
        返回:
            bool: 更新成功返回True，否则返回False
        """
        try:
            # 🔥 老王修复：类型检查，确保config是字典类型
            if not isinstance(config, dict):
                self.logger.warning(f"模块 {self.module_id} 配置更新跳过: config不是字典类型 (类型: {type(config)}, 值: {config})")
                return True  # 跳过但不报错
            
            # 更新配置
            self.config.update(config)
            
            # 执行配置更新逻辑
            self._do_update_config(config)
            
            self.logger.success(f"模块 {self.module_id} 配置更新成功")
            return True
        except Exception as e:
            self.error_count += 1
            # 🔥 老王调试：添加更详细的错误信息
            import traceback
            stack_trace = traceback.format_stack()
            self.logger.error_status(f"模块 {self.module_id} 配置更新异常: {str(e)}")
            self.logger.error_status(f"调用堆栈: {''.join(stack_trace[-5:])}")  # 显示最后5层调用堆栈
            return False
    
    def _do_update_config(self, config: Dict[str, Any]) -> None:
        """
        执行具体的配置更新逻辑
        
        子类应该重写这个方法，实现具体的配置更新逻辑。
        基类实现为空，确保即使子类不重写也不会报错。
        
        参数:
            config: 新的配置信息
        """
        pass
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取模块配置
        
        返回:
            Dict[str, Any]: 模块配置
        """
        return self.config
    
    def set_dependencies(self, dependencies: List[str]) -> None:
        """
        设置模块依赖
        
        参数:
            dependencies: 依赖模块ID列表
        """
        self.dependencies = dependencies
        self.logger.info(f"模块 {self.module_id} 依赖设置为: {dependencies}")
    
    def get_dependencies(self) -> List[str]:
        """
        获取模块依赖
        
        返回:
            List[str]: 依赖模块ID列表
        """
        return self.dependencies

class CognitionModule(ICognitiveModule):
    """
    认知模块基类
    
    提供认知模块的基本功能和通用实现。
    """
    
    def __init__(self, module_type: str = "cognition", module_name: str = None):
        """
        初始化认知模块
        
        Args:
            module_type: 模块类型
            module_name: 模块名称
        """
        self.module_type = module_type
        self.module_name = module_name or self.__class__.__name__.lower()
        self.module_id = f"{module_type}.{self.module_name}"
        self.is_initialized = False
        self.is_active = False
        self.config = {}
        self.state = {}
        self.stats = {
            "process_count": 0,
            "error_count": 0,
            "last_process_time": 0,
            "total_process_time": 0
        }
        self.dependencies = []
        
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化模块
        
        Args:
            config: 配置信息
            
        Returns:
            初始化是否成功
        """
        if config:
            self.config = config
            
        self.is_initialized = True
        return True
        
    def activate(self) -> bool:
        """
        激活模块
        
        Returns:
            激活是否成功
        """
        if not self.is_initialized:
            return False
            
        self.is_active = True
        return True
        
    def deactivate(self) -> bool:
        """
        停用模块
        
        Returns:
            停用是否成功
        """
        self.is_active = False
        return True
        
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        if not self.is_initialized or not self.is_active:
            return {"status": "error", "message": "模块未初始化或未激活"}
            
        import time
        start_time = time.time()
        
        try:
            self.stats["process_count"] += 1
            result = self._process_implementation(input_data)
            
            process_time = time.time() - start_time
            self.stats["last_process_time"] = process_time
            self.stats["total_process_time"] += process_time
            
            return result
        except Exception as e:
            self.stats["error_count"] += 1
            
            import traceback
            error_trace = traceback.format_exc()
            
            logger.error_status(f"处理异常: {e}\n{error_trace}")
            
            return {
                "status": "error",
                "message": str(e),
                "traceback": error_trace
            }
            
    def _process_implementation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理实现
        
        此方法应由子类重写，提供具体的处理逻辑。
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        raise NotImplementedError("子类必须实现_process_implementation方法")
        
    def update(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
            
        Returns:
            更新是否成功
        """
        return True
        
    def get_state(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        return {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "module_name": self.module_name,
            "initialized": self.is_initialized,
            "active": self.is_active,
            "config": self.config,
            "state": self.state,
            "stats": self.stats
        }
        
    def shutdown(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        self.deactivate()
        self.is_initialized = False
        return True
        
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态摘要
        """
        return {
            "module_id": self.module_id,
            "status": "active" if self.is_active else "inactive",
            "initialized": self.is_initialized,
            "process_count": self.stats["process_count"],
            "error_count": self.stats["error_count"]
        }
        
    def get_dependencies(self) -> List[str]:
        """
        获取模块依赖
        
        Returns:
            依赖模块ID列表
        """
        return self.dependencies
        
    def set_dependencies(self, dependencies: List[str]) -> bool:
        """
        设置模块依赖
        
        Args:
            dependencies: 依赖模块ID列表
            
        Returns:
            设置是否成功
        """
        self.dependencies = dependencies
        return True 