#!/usr/bin/env python3
"""
认知模块管理器 - Cognitive Module Manager

该模块实现了认知模块的管理器，负责认知模块的加载、激活、停用和卸载。
管理器维护了所有已加载模块的状态，并提供统一的接口来操作模块。

作者: Claude
创建日期: 2024-07-08
版本: 1.1
"""

import os
import sys
import json
import importlib
import inspect
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import time
from typing import Dict, Any, List, Optional, Type, Set, Tuple

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.cognitive_interface import ICognitiveModule
from cognitive_modules.base.module_base import CognitiveModuleBase

# 尝试导入事件总线和生命上下文
# 首先尝试导入enhanced_event_bus，如果不存在则导入event_bus
try:
    from core.enhanced_event_bus import get_instance as get_event_bus
except ImportError:
    try:
        from core.event_bus import get_instance as get_event_bus
    except ImportError:
        raise ImportError("无法导入事件总线模块，请确保项目结构正确")

try:
    from core.life_context import get_instance as get_life_context
except ImportError:
    raise ImportError("无法导入生命上下文模块，请确保项目结构正确")

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.module_manager")

class ModuleManager:
    """
    认知模块管理器
    
    负责认知模块的加载、激活、停用和卸载。
    管理器维护了所有已加载模块的状态，并提供统一的接口来操作模块。
    支持模块热加载、依赖解析和状态监控功能。
    """
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls, config: Dict[str, Any] = None) -> 'ModuleManager':
        """
        获取单例实例
        
        Args:
            config: 配置（可选）
            
        Returns:
            ModuleManager: 模块管理器实例
        """
        with cls._lock:
            # 首先检查全局模块缓存
            try:
                # 检查main模块是否定义了全局模块缓存
                if 'GLOBAL_MODULE_CACHE' in sys.modules['__main__'].__dict__:
                    global_cache = sys.modules['__main__'].__dict__['GLOBAL_MODULE_CACHE']
                    cache_key = 'cognitive_module_manager'
                    
                    if cache_key in global_cache:
                        return global_cache[cache_key]
                    
                    if cls._instance is None:
                        cls._instance = ModuleManager(config)
                    
                    global_cache[cache_key] = cls._instance
                    return cls._instance
            except Exception:
                # 如果访问全局缓存失败，继续使用类变量单例模式
                pass
                
            # 使用类变量单例模式
            if cls._instance is None:
                cls._instance = ModuleManager(config)
            return cls._instance
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化模块管理器
        
        初始化模块存储、事件总线和生命上下文。
        """
        # 确保单例模式
        if ModuleManager._instance is not None:
            raise RuntimeError("ModuleManager已经实例化，请使用get_instance()获取实例")
            
        # 模块存储
        self.modules: Dict[str, ICognitiveModule] = {}
        self.module_types: Dict[str, Set[str]] = {}
        self.active_modules: Set[str] = set()
        
        # 加载模块类映射
        self.module_classes: Dict[str, Type[ICognitiveModule]] = {}
        
        # 事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 模块加载状态
        self.is_initialized = False
        
        # 模块依赖关系
        self.dependencies: Dict[str, List[str]] = {}
        
        # 模块配置
        self.module_configs: Dict[str, Dict[str, Any]] = {}
        
        # 模块加载顺序
        self.load_order: List[str] = []
        
        # 模块状态监控
        self.module_status: Dict[str, Dict[str, Any]] = {}
        
        # 模块目录映射
        self.module_directories = {
            "perception": "cognitive_modules.perception",
            "emotion": "cognitive_modules.emotion",
            "memory": "cognitive_modules.memory",
            "cognition": "cognitive_modules.cognition",
            "physiology": "cognitive_modules.physiology",
            "behavior": "cognitive_modules.behavior",
            "autonomy": "cognitive_modules.autonomy",
            "society": "cognitive_modules.society"
        }
        
        # 尝试从配置中加载
        if config:
            self.module_configs = config.get("modules", {})
            
            # 如果配置中包含依赖关系，加载它们
            if "dependencies" in config:
                self.dependencies = config["dependencies"]
                
        logger.info("认知模块管理器已创建")
        
    def initialize(self) -> bool:
        """
        初始化模块管理器
        
        扫描模块目录，加载模块类定义。
        
        返回:
            bool: 初始化成功返回True，否则返回False
        """
        if self.is_initialized:
            logger.warning_status("认知模块管理器已经初始化")
            return True
            
        logger.success("初始化认知模块管理器")
        
        try:
            # 注册事件处理器
            self._register_event_handlers()
            
            # 扫描并加载模块类
            success = self._scan_modules()
            
            if success:
                self.is_initialized = True
                logger.success("认知模块管理器初始化成功")
                
                # 发布初始化事件
                self._publish_event("module_manager.initialized", {
                    "module_count": len(self.module_classes),
                    "module_types": list(self.module_types.keys())
                })
            else:
                logger.error_status("认知模块管理器初始化失败")
                
            return success
            
        except Exception as e:
            logger.error_status(f"认知模块管理器初始化异常: {str(e)}")
            return False
            
    def _register_event_handlers(self) -> None:
        """
        注册事件处理器
        
        为模块管理器注册事件处理器，以响应系统事件。
        """
        # 注册系统关闭事件处理器
        self.event_bus.subscribe("system.shutdown", self._handle_system_shutdown)
        
        # 注册模块错误事件处理器
        self.event_bus.subscribe("system.error", self._handle_module_error)
        
        logger.info("认知模块管理器事件处理器已注册")
        
    def _handle_system_shutdown(self, event_data: Dict[str, Any]) -> None:
        """
        处理系统关闭事件
        
        在系统关闭时关闭所有模块。
        
        参数:
            event_data: 事件数据
        """
        logger.info("处理系统关闭事件，关闭所有模块")
        self.shutdown_all_modules()
        
    def _handle_module_error(self, event_data: Dict[str, Any]) -> None:
        """
        处理模块错误事件
        
        根据错误类型和严重程度，可能会停用出错的模块。
        
        参数:
            event_data: 事件数据
        """
        if "error_type" in event_data and "module_id" in event_data:
            error_type = event_data["error_type"]
            module_id = event_data["module_id"]
            
            if error_type == "module_processing_error" and module_id in self.modules:
                error_count = self.modules[module_id].error_count
                
                # 如果连续错误次数过多，自动停用模块
                if error_count > 5:  # 可配置的阈值
                    logger.warning_status(f"模块 {module_id} 连续错误次数过多，自动停用")
                    self.deactivate_module(module_id)
                    
                    # 发布模块故障事件
                    self._publish_event("module_manager.module_failure", {
                        "module_id": module_id,
                        "error_count": error_count,
                        "action": "deactivated"
                    })
                    
    def _scan_modules(self) -> bool:
        """
        扫描模块目录，加载模块类定义
        
        返回:
            bool: 扫描成功返回True，否则返回False
        """
        try:
            logger.info("扫描认知模块目录")
            
            # 清空现有模块类映射
            self.module_classes.clear()
            self.module_types.clear()
            
            # 认知模块根目录
            modules_dir = os.path.join(current_dir)
            
            # 扫描子目录(每个子目录代表一个模块类型)
            for module_type in os.listdir(modules_dir):
                type_dir = os.path.join(modules_dir, module_type)
                
                # 跳过非目录和特殊目录
                if not os.path.isdir(type_dir) or module_type.startswith("__") or module_type == "base":
                    continue
                    
                logger.info(f"扫描模块类型: {module_type}")
                self.module_types[module_type] = set()
                
                # 扫描模块文件
                for file_name in os.listdir(type_dir):
                    if file_name.endswith(".py") and not file_name.startswith("__"):
                        module_name = file_name[:-3]  # 去除.py扩展名
                        
                        # 动态导入模块
                        try:
                            module_path = f"cognitive_modules.{module_type}.{module_name}"
                            
                            # 检查是否需要重新加载模块
                            if module_path in sys.modules:
                                module = importlib.reload(sys.modules[module_path])
                                logger.info(f"重新加载模块: {module_path}")
                            else:
                                module = importlib.import_module(module_path)
                                logger.info(f"首次加载模块: {module_path}")
                            
                            # 查找模块中的认知模块类
                            for name, obj in inspect.getmembers(module):
                                if (inspect.isclass(obj) and 
                                    issubclass(obj, CognitiveModuleBase) and 
                                    obj != CognitiveModuleBase):
                                    
                                    # 记录找到的模块类
                                    class_id = f"{module_type}.{name}"
                                    self.module_classes[class_id] = obj
                                    self.module_types[module_type].add(class_id)
                                    
                                    logger.info(f"发现模块类: {class_id}")
                                    
                        except Exception as e:
                            logger.error_status(f"加载模块 {module_type}.{module_name} 失败: {str(e)}")
                            
            logger.success(f"模块扫描完成，发现 {len(self.module_classes)} 个模块类")
            return True
            
        except Exception as e:
            logger.error_status(f"扫描模块目录异常: {str(e)}")
            return False 

    def load_module(self, module_type: str, module_name: str) -> bool:
        """
        加载指定模块
        
        Args:
            module_type: 模块类型（perception, emotion, memory, cognition, physiology, behavior）
            module_name: 模块名称
            
        Returns:
            是否加载成功
        """
        # 检查模块类型是否有效
        if module_type not in self.module_directories:
            logger.error_status(f"无效的模块类型: {module_type}")
            return False
        
        # 构建模块ID和导入路径
        module_id = f"{module_type}.{module_name}"
        module_path = f"{self.module_directories[module_type]}.{module_name}"
        
        # 检查模块是否已加载
        if module_id in self.modules:
            # 使用debug级别而不是warning级别，减少警告日志
            logger.debug(f"模块已加载: {module_id}")
            return True
        
        try:
            # 尝试导入模块
            try:
                module = importlib.import_module(module_path)
            except ImportError as e:
                logger.error_status(f"导入模块失败: {module_path}, 错误: {str(e)}")
                return False
            
            # 方法1: 使用get_instance函数
            if hasattr(module, 'get_instance'):
                # 获取模块配置
                module_config = self.module_configs.get(module_id, {})
                
                # 初始化模块
                instance = module.get_instance(module_config)
                
                # 存储模块实例
                self.modules[module_id] = instance
                
                # 更新加载顺序
                self.load_order.append(module_id)
                
                logger.info(f"已通过get_instance加载模块: {module_id}")
                return True
            # 方法2: 使用基于类的模块
            elif hasattr(module, module_name) and inspect.isclass(getattr(module, module_name)):
                module_class = getattr(module, module_name)
                if issubclass(module_class, ICognitiveModule):
                    # 获取模块配置
                    module_config = self.module_configs.get(module_id, {})
                    
                    # 实例化并初始化模块
                    instance = module_class()
                    if instance.initialize(module_config):
                        # 存储模块实例
                        self.modules[module_id] = instance
                        
                        # 更新加载顺序
                        self.load_order.append(module_id)
                        
                        logger.info(f"已通过类加载模块: {module_id}")
                        return True
                    else:
                        logger.error_status(f"模块初始化失败: {module_id}")
                        return False
            # 方法3: 使用适配器模式加载简单模块
            elif hasattr(module, "process") and callable(getattr(module, "process")):
                # 使用适配器加载简单模块
                from cognitive_modules.base.module_adapter import SimpleModuleAdapter
                
                # 获取模块配置
                module_config = self.module_configs.get(module_id, {})
                
                # 创建适配器
                adapter = SimpleModuleAdapter(self.module_directories[module_type], module_name)
                if adapter.initialize(module_config):
                    # 存储模块实例
                    self.modules[module_id] = adapter
                    
                    # 更新加载顺序
                    self.load_order.append(module_id)
                    
                    logger.info(f"已通过适配器加载模块: {module_id}")
                    return True
                else:
                    logger.error_status(f"模块适配器初始化失败: {module_id}")
                    return False
            else:
                logger.error_status(f"模块不符合接口规范: {module_path}")
                return False
        except Exception as e:
            logger.error_status(f"加载模块失败: {module_id}, 错误: {e}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def unload_module(self, module_id: str) -> bool:
        """
        卸载指定模块
        
        Args:
            module_id: 模块ID
            
        Returns:
            是否卸载成功
        """
        if module_id not in self.modules:
            logger.warning_status(f"模块未加载: {module_id}")
            return False
        
        try:
            # 关闭模块
            instance = self.modules[module_id]
            if hasattr(instance, 'shutdown'):
                instance.shutdown()
            
            # 移除模块
            del self.modules[module_id]
            
            # 更新加载顺序
            if module_id in self.load_order:
                self.load_order.remove(module_id)
            
            logger.info(f"已卸载模块: {module_id}")
            return True
        except Exception as e:
            logger.error_status(f"卸载模块失败: {module_id}, 错误: {e}")
            return False
    
    def get_module(self, module_id: str) -> Optional[ICognitiveModule]:
        """
        获取指定模块的实例
        
        Args:
            module_id: 模块ID
            
        Returns:
            模块实例
        """
        return self.modules.get(module_id)
        
    def activate_module(self, module_id: str) -> bool:
        """
        激活指定的模块
        
        使模块进入活动状态，能够处理请求。
        
        参数:
            module_id: 模块ID
            
        返回:
            bool: 激活成功返回True，否则返回False
        """
        if module_id not in self.modules:
            logger.error_status(f"未知的模块ID: {module_id}")
            return False
            
        module = self.modules[module_id]
        
        # 如果模块已经激活，直接返回成功
        if module_id in self.active_modules:
            logger.info(f"模块已经处于激活状态: {module_id}")
            return True
            
        # 激活模块
        success = module.activate()
        
        if success:
            # 添加到激活模块集合
            self.active_modules.add(module_id)
            
            # 更新生命上下文
            self.life_context.update_context(
                f"cognitive_modules.{module.module_type}.{module_id}.active", 
                True
            )
            
            logger.info(f"模块已激活: {module_id}")
            
            # 发布模块激活事件
            self._publish_event("module_manager.module_activated", {
                "module_id": module_id,
                "module_type": module.module_type
            })
            
        return success
        
    def deactivate_module(self, module_id: str) -> bool:
        """
        停用指定的模块
        
        使模块进入非活动状态，不再处理请求。
        
        参数:
            module_id: 模块ID
            
        返回:
            bool: 停用成功返回True，否则返回False
        """
        if module_id not in self.modules:
            logger.error_status(f"未知的模块ID: {module_id}")
            return False
            
        module = self.modules[module_id]
        
        # 如果模块已经停用，直接返回成功
        if module_id not in self.active_modules:
            logger.info(f"模块已经处于停用状态: {module_id}")
            return True
            
        # 停用模块
        success = module.deactivate()
        
        if success:
            # 从激活模块集合中移除
            self.active_modules.remove(module_id)
            
            # 更新生命上下文
            self.life_context.update_context(
                f"cognitive_modules.{module.module_type}.{module_id}.active", 
                False
            )
            
            logger.info(f"模块已停用: {module_id}")
            
            # 发布模块停用事件
            self._publish_event("module_manager.module_deactivated", {
                "module_id": module_id,
                "module_type": module.module_type
            })
            
        return success
        
    def get_active_modules(self) -> List[str]:
        """
        获取所有激活状态的模块ID
        
        返回:
            List[str]: 激活状态的模块ID列表
        """
        return list(self.active_modules)
        
    def get_modules_by_type(self, module_type: str) -> List[str]:
        """
        获取指定类型的所有已加载模块ID
        
        参数:
            module_type: 模块类型
            
        返回:
            List[str]: 指定类型的模块ID列表
        """
        return [
            module_id for module_id, module in self.modules.items()
            if module.module_type == module_type
        ]
        
    def get_active_modules_by_type(self, module_type: str) -> List[str]:
        """
        获取指定类型的所有已激活模块ID
        
        参数:
            module_type: 模块类型
            
        返回:
            List[str]: 指定类型的已激活模块ID列表
        """
        return [
            module_id for module_id in self.active_modules
            if self.modules[module_id].module_type == module_type
        ]
        
    def get_module_types(self) -> List[str]:
        """
        获取所有可用的模块类型
        
        返回:
            List[str]: 模块类型列表
        """
        return list(self.module_types.keys())
        
    def get_module_classes(self, module_type: str = None) -> Dict[str, Type[ICognitiveModule]]:
        """
        获取可用的模块类
        
        参数:
            module_type: 模块类型，如果不指定则返回所有类型
            
        返回:
            Dict[str, Type[ICognitiveModule]]: 模块类映射
        """
        if module_type:
            return {
                class_id: cls for class_id, cls in self.module_classes.items()
                if class_id.startswith(f"{module_type}.")
            }
        else:
            return self.module_classes
            
    def process_with_module(self, module_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用指定模块处理输入数据
        
        参数:
            module_id: 模块ID
            input_data: 输入数据
            
        返回:
            Dict[str, Any]: 处理结果
        """
        if module_id not in self.modules:
            logger.error_status(f"未知的模块ID: {module_id}")
            return {"success": False, "error": "未知的模块ID"}
            
        if module_id not in self.active_modules:
            logger.error_status(f"模块未激活: {module_id}")
            return {"success": False, "error": "模块未激活"}
            
        # 调用模块处理方法
        return self.modules[module_id].process(input_data)
        
    def process_with_type(self, module_type: str, input_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        使用指定类型的所有激活模块处理输入数据
        
        参数:
            module_type: 模块类型
            input_data: 输入数据
            
        返回:
            List[Dict[str, Any]]: 处理结果列表
        """
        results = []
        
        # 获取指定类型的激活模块
        active_modules = self.get_active_modules_by_type(module_type)
        
        if not active_modules:
            logger.warning_status(f"没有激活的 {module_type} 类型模块")
            return results
            
        # 依次调用每个模块处理
        for module_id in active_modules:
            result = self.process_with_module(module_id, input_data)
            results.append({
                "module_id": module_id,
                "result": result
            })
            
        return results
        
    def update_all_modules(self, context: Dict[str, Any]) -> Dict[str, bool]:
        """
        更新所有已加载模块的状态
        
        参数:
            context: 上下文信息
            
        返回:
            Dict[str, bool]: 更新结果映射，模块ID -> 是否成功
        """
        results = {}
        
        for module_id, module in self.modules.items():
            results[module_id] = module.update(context)
            
        return results
        
    def shutdown_all_modules(self) -> Dict[str, bool]:
        """
        关闭所有已加载模块
        
        返回:
            Dict[str, bool]: 关闭结果映射，模块ID -> 是否成功
        """
        results = {}
        
        # 先停用所有模块
        for module_id in list(self.active_modules):
            self.deactivate_module(module_id)
            
        # 关闭所有模块
        for module_id, module in list(self.modules.items()):
            results[module_id] = module.shutdown()
            
            # 从模块字典中移除
            if results[module_id]:
                del self.modules[module_id]
                
        # 清空激活模块集合
        self.active_modules.clear()
        
        logger.info("所有模块已关闭")
        
        return results
        
    def save_all_module_states(self) -> Dict[str, bool]:
        """
        保存所有模块状态
        
        返回:
            Dict[str, bool]: 保存结果映射，模块ID -> 是否成功
        """
        results = {}
        
        for module_id, module in self.modules.items():
            results[module_id] = module.save_state()
            
        return results
        
    def load_all_module_states(self) -> Dict[str, bool]:
        """
        加载所有模块状态
        
        返回:
            Dict[str, bool]: 加载结果映射，模块ID -> 是否成功
        """
        results = {}
        
        for module_id, module in self.modules.items():
            results[module_id] = module.load_state()
            
        return results
        
    def load_config(self, config_path: str) -> bool:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            是否加载成功
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.module_configs = config.get('modules', {})
            
            logger.info(f"已加载配置文件: {config_path}")
            return True
        except Exception as e:
            logger.error_status(f"加载配置文件失败: {e}")
            return False
    
    def load_modules_from_config(self) -> Dict[str, bool]:
        """
        根据配置加载模块
        
        Returns:
            模块加载结果字典
        """
        results = {}
        
        # 获取要加载的模块列表
        modules_to_load = self.module_configs.keys()
        
        for module_id in modules_to_load:
            success = self.load_module(module_id.split('.')[0], module_id.split('.')[1])
            results[module_id] = success
        
        return results
    
    def get_all_modules(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有已加载模块的信息
        
        Returns:
            模块信息字典
        """
        modules_info = {}
        
        for module_id, instance in self.modules.items():
            # 获取模块状态
            if hasattr(instance, 'get_status'):
                status = instance.get_status()
            else:
                status = {
                    'name': module_id,
                    'is_running': hasattr(instance, 'is_running') and instance.is_running
                }
            
            modules_info[module_id] = status
        
        return modules_info
    
    def scan_available_modules(self) -> Dict[str, List[str]]:
        """
        扫描可用模块
        
        Returns:
            可用模块字典，按类型分组
        """
        available_modules = {}
        
        for module_type, package_path in self.module_directories.items():
            available_modules[module_type] = []
            
            # 转换包路径为文件系统路径
            parts = package_path.split('.')
            package_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), *parts)
            
            if not os.path.exists(package_dir):
                logger.warning_status(f"模块目录不存在: {package_dir}")
                continue
            
            # 遍历目录查找模块
            for item in os.listdir(package_dir):
                # 检查是否是Python模块或包
                if item.endswith('.py') and item != '__init__.py':
                    module_name = item[:-3]  # 去掉.py后缀
                    available_modules[module_type].append(module_name)
                elif os.path.isdir(os.path.join(package_dir, item)) and os.path.exists(os.path.join(package_dir, item, '__init__.py')):
                    available_modules[module_type].append(item)
        
        return available_modules
    
    def update_module_config(self, module_id: str, config: Dict[str, Any]) -> bool:
        """
        更新模块配置
        
        Args:
            module_id: 模块ID
            config: 新的配置信息
            
        Returns:
            是否更新成功
        """
        # 更新配置
        self.module_configs[module_id] = config
        
        # 如果模块已加载，重新初始化模块
        instance = self.modules.get(module_id)
        if instance and hasattr(instance, 'initialize'):
            try:
                success = instance.initialize(config)
                logger.success(f"模块配置更新{'成功' if success else '失败'}: {module_id}")
                return success
            except Exception as e:
                logger.error_status(f"更新模块配置异常: {module_id}, 错误: {e}")
                return False
        
        return True
    
    def save_config(self, config_path: str) -> bool:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            是否保存成功
        """
        try:
            # 构建配置
            config = {
                'modules': self.module_configs
            }
            
            # 保存到文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已保存配置到文件: {config_path}")
            return True
        except Exception as e:
            logger.error_status(f"保存配置文件失败: {e}")
            return False
    
    def set_dependency(self, module_id: str, depends_on: List[str]) -> bool:
        """
        设置模块依赖关系
        
        Args:
            module_id: 模块ID
            depends_on: 依赖的模块ID列表
            
        Returns:
            是否设置成功
        """
        # 检查循环依赖
        for dep in depends_on:
            if dep in self.dependencies:
                if module_id in self.dependencies[dep]:
                    logger.error_status(f"检测到循环依赖: {module_id} <-> {dep}")
                    return False
        
        self.dependencies[module_id] = depends_on
        logger.info(f"已设置模块依赖: {module_id} -> {depends_on}")
        return True
    
    def get_dependencies(self, module_id: str) -> List[str]:
        """
        获取模块依赖
        
        Args:
            module_id: 模块ID
            
        Returns:
            依赖的模块ID列表
        """
        return self.dependencies.get(module_id, [])
    
    def resolve_dependencies(self) -> List[str]:
        """
        解析模块加载顺序以满足依赖关系
        
        Returns:
            按依赖顺序排序的模块ID列表
        """
        # 构建依赖图
        graph = {module_id: set(deps) for module_id, deps in self.dependencies.items()}
        
        # 添加没有显式依赖的模块
        for module_id in self.modules:
            if module_id not in graph:
                graph[module_id] = set()
        
        # 拓扑排序
        result = []
        visited = set()
        temp_visited = set()
        
        def visit(node):
            if node in temp_visited:
                raise ValueError(f"检测到循环依赖，包含模块: {node}")
            if node in visited:
                return
            
            temp_visited.add(node)
            for neighbor in graph.get(node, set()):
                visit(neighbor)
            
            temp_visited.remove(node)
            visited.add(node)
            result.insert(0, node)
        
        # 对每个节点执行DFS
        for module_id in graph:
            if module_id not in visited:
                visit(module_id)
        
        return result
    
    def load_with_dependencies(self, module_id: str) -> Dict[str, bool]:
        """
        加载模块及其所有依赖
        
        先加载所有依赖模块，然后加载目标模块。

        参数:
            module_id: 模块ID
            
        返回:
            Dict[str, bool]: 加载结果，键为模块ID，值为是否加载成功
        """
        # 解析模块类型和名称
        parts = module_id.split('.')
        if len(parts) < 2:
            logger.error_status(f"无效的模块ID: {module_id}")
            return {module_id: False}
            
        module_type, module_name = parts[0], parts[1]
        
        # 获取所有依赖
        all_deps = self.resolve_dependencies()
        
        # 查找模块在依赖图中的位置
        if module_id not in all_deps:
            # 如果模块没有依赖，直接加载
            logger.info(f"模块 {module_id} 没有依赖，直接加载")
            success = self.load_module(module_type, module_name)
            return {module_id: success}
            
        # 按照拓扑顺序加载所有依赖模块
        result = {}
        for dep_id in all_deps:
            # 如果是目标模块或其依赖，加载它
            if dep_id == module_id or dep_id in self.get_all_dependencies(module_id):
                # 解析依赖模块的类型和名称
                dep_parts = dep_id.split('.')
                if len(dep_parts) < 2:
                    logger.error_status(f"无效的依赖模块ID: {dep_id}")
                    result[dep_id] = False
                    continue
                    
                dep_type, dep_name = dep_parts[0], dep_parts[1]
                
                # 如果已经加载，跳过
                if dep_id in self.modules:
                    result[dep_id] = True
                    continue
                    
                # 加载依赖模块
                success = self.load_module(dep_type, dep_name)
                result[dep_id] = success
                
                if not success:
                    logger.error_status(f"加载依赖模块失败: {dep_id}")
                    # 不中断，继续尝试加载其他依赖
        
        # 激活模块
        if module_id in self.modules and module_id not in self.active_modules:
            success = self.activate_module(module_id)
            if not success:
                logger.error_status(f"激活模块失败: {module_id}")
                
        return result
        
    def get_all_dependencies(self, module_id: str) -> Set[str]:
        """
        获取模块的所有依赖（直接和间接依赖）
        
        使用递归方式获取所有依赖模块。
        
        参数:
            module_id: 模块ID
            
        返回:
            Set[str]: 所有依赖模块的ID集合
        """
        result = set()
        
        def collect_deps(mid):
            if mid in self.dependencies:
                for dep in self.dependencies[mid]:
                    if dep not in result:
                        result.add(dep)
                        collect_deps(dep)
        
        collect_deps(module_id)
        return result

    def watch_modules(self, interval: int = 5) -> threading.Thread:
        """
        启动模块监视线程，定期检查模块状态
        
        创建一个后台线程，定期检查所有活动模块的健康状态，
        如果发现模块异常，会尝试自动恢复或发出警告。
        
        参数:
            interval: 检查间隔时间（秒）
            
        返回:
            threading.Thread: 监视线程对象
        """
        def monitor_task():
            while True:
                try:
                    # 检查所有活动模块
                    for module_id in list(self.active_modules):
                        if module_id not in self.modules:
                            logger.warning_status(f"发现活动列表中的模块已不存在: {module_id}")
                            self.active_modules.remove(module_id)
                            continue
                            
                        module = self.modules[module_id]
                        
                        # 检查模块健康状态
                        if hasattr(module, 'check_health'):
                            try:
                                health_status = module.check_health()
                                
                                # 更新模块状态
                                self.module_status[module_id] = {
                                    "health": health_status,
                                    "last_check": time.time(),
                                    "error_count": getattr(module, 'error_count', 0)
                                }
                                
                                # 如果模块不健康，尝试恢复
                                if not health_status.get('healthy', True):
                                    logger.warning_status(f"模块健康检查失败: {module_id}, 状态: {health_status}")
                                    
                                    # 发布模块健康警告事件
                                    self.event_bus.publish(
                                        "module_manager.health_warning",
                                        {
                                            "module_id": module_id,
                                            "health_status": health_status
                                        },
                                        source="cognitive.module_manager"
                                    )
                                    
                                    # 如果连续失败次数过高，尝试重新加载
                                    error_count = getattr(module, 'error_count', 0)
                                    if error_count > 3:  # 可配置的阈值
                                        logger.warning_status(f"模块 {module_id} 连续错误次数过多，尝试热重载")
                                        self.hot_reload_module(module_id)
                            except Exception as e:
                                logger.error_status(f"模块健康检查异常: {module_id}, 错误: {str(e)}")
                                
                                # 更新错误计数
                                if not hasattr(module, 'error_count'):
                                    module.error_count = 0
                                module.error_count += 1
                    
                    # 休眠指定间隔
                    time.sleep(interval)
                except Exception as e:
                    logger.error_status(f"模块监视线程异常: {str(e)}")
                    time.sleep(interval)
        
        # 创建监视线程
        monitor_thread = threading.Thread(target=monitor_task, daemon=True)
        monitor_thread.start()
        
        logger.success(f"模块监视线程已启动，检查间隔: {interval}秒")
        return monitor_thread

    def load_config_file(self, config_path: str) -> bool:
        """
        从文件加载模块配置
        
        参数:
            config_path: 配置文件路径
            
        返回:
            bool: 加载成功返回True，否则返回False
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 更新模块配置
            if "modules" in config:
                self.module_configs.update(config["modules"])
                
            # 更新依赖关系
            if "dependencies" in config:
                self.dependencies.update(config["dependencies"])
                
            logger.info(f"已从文件加载模块配置: {config_path}")
            return True
        except Exception as e:
            logger.error_status(f"加载模块配置文件失败: {config_path}, 错误: {str(e)}")
            return False

    def hot_reload_module(self, module_id: str) -> bool:
        """
        热重载模块，不停机的情况下更新模块
        
        先卸载模块，然后重新加载模块。如果模块之前是激活状态，会自动重新激活。
        
        参数:
            module_id: 模块ID
            
        返回:
            bool: 重载成功返回True，否则返回False
        """
        if module_id not in self.modules:
            logger.error_status(f"模块未加载: {module_id}")
            return False
        
        # 记录模块当前状态
        was_active = module_id in self.active_modules
        config = self.module_configs.get(module_id, {})
        
        # 保存模块状态
        if hasattr(self.modules[module_id], 'save_state'):
            try:
                state = self.modules[module_id].save_state()
            except:
                state = None
        else:
            state = None
        
        # 卸载模块
        if not self.unload_module(module_id):
            logger.error_status(f"卸载模块失败: {module_id}")
            return False
        
        # 分解模块ID以获取类型和名称
        parts = module_id.split('.')
        if len(parts) < 2:
            logger.error_status(f"无效的模块ID格式: {module_id}")
            return False
        
        module_type = parts[0]
        module_name = parts[1]
        
        # 重新加载模块
        if not self.load_module(module_type, module_name):
            logger.error_status(f"重新加载模块失败: {module_id}")
            return False
        
        # 恢复模块配置
        if config:
            self.update_module_config(module_id, config)
        
        # 恢复模块状态
        if state and hasattr(self.modules[module_id], 'load_state'):
            try:
                self.modules[module_id].load_state(state)
            except:
                logger.warning_status(f"恢复模块状态失败: {module_id}")
        
        # 如果模块之前是激活状态，重新激活
        if was_active:
            if not self.activate_module(module_id):
                logger.error_status(f"重新激活模块失败: {module_id}")
                return False
        
        logger.success(f"热重载模块成功: {module_id}")
        return True

    def shutdown(self) -> bool:
        """
        关闭模块管理器
        
        Returns:
            是否关闭成功
        """
        # 停止所有模块
        self.stop_all()
        
        # 卸载所有模块
        for module_id in list(self.modules.keys()):
            self.unload_module(module_id)
        
        logger.info("认知模块管理器已关闭")
        return True

    def start_all(self) -> Dict[str, bool]:
        """
        启动所有已加载的模块
        
        Returns:
            模块启动结果字典
        """
        results = {}
        
        # 按加载顺序启动模块
        for module_id in self.load_order:
            instance = self.modules.get(module_id)
            if instance:
                try:
                    if hasattr(instance, 'start'):
                        success = instance.start()
                    else:
                        success = True  # 没有start方法的模块默认启动成功
                    
                    results[module_id] = success
                    logger.success(f"模块启动{'成功' if success else '失败'}: {module_id}")
                except Exception as e:
                    results[module_id] = False
                    logger.error_status(f"启动模块异常: {module_id}, 错误: {e}")
            else:
                results[module_id] = False
                logger.warning_status(f"模块不存在，无法启动: {module_id}")
        
        return results
    
    def stop_all(self) -> Dict[str, bool]:
        """
        停止所有已加载的模块
        
        Returns:
            模块停止结果字典
        """
        results = {}
        
        # 获取实际加载的模块ID列表
        loaded_module_ids = list(self.modules.keys())
        
        # 按加载顺序的反序停止模块
        for module_id in reversed(self.load_order):
            # 只尝试停止实际加载的模块
            if module_id in loaded_module_ids:
                instance = self.modules.get(module_id)
                try:
                    if hasattr(instance, 'stop'):
                        success = instance.stop()
                    else:
                        success = True  # 没有stop方法的模块默认停止成功
                    
                    results[module_id] = success
                    logger.success(f"模块停止{'成功' if success else '失败'}: {module_id}")
                except Exception as e:
                    results[module_id] = False
                    logger.error_status(f"停止模块异常: {module_id}, 错误: {e}")
            else:
                # 使用debug级别而不是warning级别
                logger.debug(f"模块不存在，无法停止: {module_id}")
                results[module_id] = False
        
        return results
    
    def reload_module(self, module_id: str) -> bool:
        """
        重新加载指定模块
        
        Args:
            module_id: 模块ID
            
        Returns:
            是否重新加载成功
        """
        # 解析模块类型和名称
        parts = module_id.split('.')
        if len(parts) != 2:
            logger.error_status(f"无效的模块ID格式: {module_id}")
            return False
        
        module_type, module_name = parts
        
        # 先卸载模块
        self.unload_module(module_id)
        
        # 清除导入缓存
        module_path = f"{self.module_directories[module_type]}.{module_name}"
        if module_path in sys.modules:
            del sys.modules[module_path]
        
        # 重新加载模块
        return self.load_module(module_type, module_name)

    def _publish_event(self, event_name: str, event_data: Dict[str, Any]):
        """
        发布事件
        
        Args:
            event_name: 事件名称
            event_data: 事件数据
        """
        try:
            if self.event_bus:
                # 添加源信息
                event_data = dict(event_data)  # 创建副本
                event_data["source"] = "cognitive.module_manager"
                
                # 发布事件
                self.event_bus.publish(event_name, event_data)
            else:
                logger.warning_status(f"事件总线未初始化，无法发布事件: {event_name}")
        except Exception as e:
            logger.error_status(f"发布事件异常: {e}")
            import traceback
            logger.error_status(traceback.format_exc())

# 便捷函数，获取模块管理器实例
def get_instance(config: Dict[str, Any] = None) -> ModuleManager:
    """
    获取模块管理器的单例实例
    
    返回:
        ModuleManager: 模块管理器的单例实例
    """
    return ModuleManager.get_instance(config) 