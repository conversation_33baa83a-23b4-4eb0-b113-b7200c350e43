"""
器官神经网络系统
================

协调林嫣然数字生命体各器官间的通信和协作。
类似于人体的神经系统，连接和协调各个器官的工作。
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Set, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass

try:
    from ..ai.yanran_decision_engine import YanranAIDecisionEngine
except ImportError:
    # 兜底导入方式
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent))
    from cognitive_modules.ai.yanran_decision_engine import YanranAIDecisionEngine

from cognitive_modules.ai.yanran_decision_engine import get_yanran_decision_engine


class SignalType(Enum):
    """信号类型枚举"""
    PERCEPTION_UPDATE = "perception_update"
    EMOTION_TRIGGER = "emotion_trigger"
    MEMORY_RECALL = "memory_recall"
    SKILL_REQUEST = "skill_request"
    EXPRESSION_TRIGGER = "expression_trigger"
    SAFETY_ALERT = "safety_alert"
    COORDINATION_REQUEST = "coordination_request"
    STATUS_UPDATE = "status_update"
    LEARNING_FEEDBACK = "learning_feedback"
    EMERGENCY_SIGNAL = "emergency_signal"


class SignalPriority(Enum):
    """信号优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    EMERGENCY = 5


@dataclass
class NeuralSignal:
    """神经信号数据结构"""
    signal_id: str
    signal_type: SignalType
    priority: SignalPriority
    source_organ: str
    target_organ: Optional[str]
    data: Dict[str, Any]
    timestamp: datetime
    ttl: Optional[datetime] = None  # 信号生存时间
    processed: bool = False
    response_required: bool = False
    
    def is_expired(self) -> bool:
        """检查信号是否过期"""
        if self.ttl is None:
            return False
        return datetime.now() > self.ttl
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'signal_id': self.signal_id,
            'signal_type': self.signal_type.value,
            'priority': self.priority.value,
            'source_organ': self.source_organ,
            'target_organ': self.target_organ,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'ttl': self.ttl.isoformat() if self.ttl else None,
            'processed': self.processed,
            'response_required': self.response_required
        }


class NeuralPathway:
    """神经通路 - 器官间的连接"""
    
    def __init__(self, organ1: str, organ2: str, pathway_type: str, strength: float = 1.0):
        self.organ1 = organ1
        self.organ2 = organ2
        self.pathway_type = pathway_type
        self.strength = strength  # 连接强度
        self.signal_count = 0
        self.last_activity = datetime.now()
        self.success_rate = 1.0
        self.established_at = datetime.now()
        
    def update_activity(self, success: bool = True):
        """更新通路活动"""
        self.signal_count += 1
        self.last_activity = datetime.now()
        
        # 更新成功率
        if success:
            self.success_rate = min(1.0, self.success_rate + 0.01)
        else:
            self.success_rate = max(0.0, self.success_rate - 0.05)
        
        # 根据活动调整连接强度
        if self.signal_count > 100:
            self.strength = min(2.0, self.strength + 0.1)
    
    def get_pathway_info(self) -> Dict[str, Any]:
        """获取通路信息"""
        return {
            'organ1': self.organ1,
            'organ2': self.organ2,
            'pathway_type': self.pathway_type,
            'strength': self.strength,
            'signal_count': self.signal_count,
            'last_activity': self.last_activity.isoformat(),
            'success_rate': self.success_rate,
            'established_at': self.established_at.isoformat()
        }


class OrganNeuralNetwork:
    """
    器官神经网络
    
    负责：
    1. 管理器官间的连接和通信
    2. 协调器官间的协作
    3. 处理信号传递和路由
    4. 监控网络健康状态
    5. 优化通信效率
    """
    
    def __init__(self, config_path: Optional[str] = None, ai_adapter=None):
        self.logger = logging.getLogger("OrganNeuralNetwork")
        
        # 器官注册表
        self.organs: Dict[str, Any] = {}  # organ_name -> organ_instance
        
        # 神经通路
        self.neural_pathways: Dict[str, NeuralPathway] = {}  # pathway_id -> pathway
        
        # 信号队列
        self.signal_queue: List[NeuralSignal] = []
        self.signal_history: List[NeuralSignal] = []
        
        # AI协调引擎 - 使用单例实例
        self.coordination_ai = get_yanran_decision_engine()
        
        # 网络状态
        self.network_active = True
        self.signal_processing_active = True
        
        # 配置
        self.config = self._load_config(config_path)
        
        # 统计信息
        self.network_stats = {
            'total_signals_processed': 0,
            'successful_transmissions': 0,
            'failed_transmissions': 0,
            'average_processing_time': 0.0,
            'active_pathways': 0,
            'network_efficiency': 1.0
        }
        
        # 启动信号处理循环
        self._start_signal_processing()
        
        self.logger.info("器官神经网络初始化完成")
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            'max_signal_queue_size': 1000,
            'signal_processing_interval': 0.1,
            'signal_ttl_seconds': 300,
            'max_pathway_strength': 2.0,
            'network_optimization_interval': 3600,
            'signal_history_size': 10000,
            'coordination_ai_enabled': True
        }
        
        if config_path:
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                self.logger.warning(f"加载配置失败: {e}")
        
        return default_config
    
    def register_organ(self, organ_name: str, organ_instance: Any) -> bool:
        """注册器官"""
        try:
            if organ_name in self.organs:
                self.logger.warning(f"器官 {organ_name} 已存在，将被替换")
            
            self.organs[organ_name] = organ_instance
            self.logger.info(f"器官 {organ_name} 注册成功")
            
            # 自动建立与其他器官的基础连接
            asyncio.create_task(self._establish_default_connections(organ_name))
            
            return True
            
        except Exception as e:
            self.logger.error(f"注册器官 {organ_name} 失败: {e}")
            return False
    
    def unregister_organ(self, organ_name: str) -> bool:
        """注销器官"""
        try:
            if organ_name not in self.organs:
                self.logger.warning(f"器官 {organ_name} 不存在")
                return False
            
            # 移除相关的神经通路
            pathways_to_remove = []
            for pathway_id, pathway in self.neural_pathways.items():
                if pathway.organ1 == organ_name or pathway.organ2 == organ_name:
                    pathways_to_remove.append(pathway_id)
            
            for pathway_id in pathways_to_remove:
                del self.neural_pathways[pathway_id]
            
            # 移除器官
            del self.organs[organ_name]
            
            self.logger.info(f"器官 {organ_name} 注销成功")
            return True
            
        except Exception as e:
            self.logger.error(f"注销器官 {organ_name} 失败: {e}")
            return False
    
    def establish_neural_pathway(self, organ1: str, organ2: str, pathway_type: str, 
                               bidirectional: bool = True, strength: float = 1.0) -> bool:
        """建立神经通路"""
        try:
            # 检查器官是否存在
            if organ1 not in self.organs or organ2 not in self.organs:
                self.logger.error(f"器官不存在: {organ1} 或 {organ2}")
                return False
            
            # 创建通路ID
            pathway_id = f"{organ1}--{organ2}--{pathway_type}"
            
            # 检查通路是否已存在
            if pathway_id in self.neural_pathways:
                self.logger.warning(f"神经通路已存在: {pathway_id}")
                return True
            
            # 创建神经通路
            pathway = NeuralPathway(organ1, organ2, pathway_type, strength)
            self.neural_pathways[pathway_id] = pathway
            
            # 如果是双向连接，创建反向通路
            if bidirectional and organ1 != organ2:
                reverse_pathway_id = f"{organ2}--{organ1}--{pathway_type}"
                if reverse_pathway_id not in self.neural_pathways:
                    reverse_pathway = NeuralPathway(organ2, organ1, pathway_type, strength)
                    self.neural_pathways[reverse_pathway_id] = reverse_pathway
            
            self.logger.info(f"神经通路建立成功: {pathway_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"建立神经通路失败: {e}")
            return False
    
    async def _establish_default_connections(self, new_organ: str):
        """为新器官建立默认连接"""
        # 基于器官类型建立默认连接
        default_connections = {
            '世界感知器官': ['创作表达器官', '主动表达器官', '情感系统'],
            '创作表达器官': ['世界感知器官', '情感系统', '技能协调器官'],
            '安全防护器官': ['所有器官'],  # 安全器官需要连接所有器官
            '技能协调器官': ['所有器官'],  # 技能协调器官需要连接所有器官
            '财富管理器官': ['世界感知器官', '数据感知器官'],
            '主动表达器官': ['世界感知器官', '创作表达器官', '情感系统']
        }
        
        connections = default_connections.get(new_organ, [])
        
        for target_organ in connections:
            if target_organ == '所有器官':
                # 连接到所有其他器官
                for organ_name in self.organs.keys():
                    if organ_name != new_organ:
                        self.establish_neural_pathway(new_organ, organ_name, 'default_connection')
            elif target_organ in self.organs:
                self.establish_neural_pathway(new_organ, target_organ, 'default_connection')
    
    async def send_signal(self, signal: NeuralSignal) -> bool:
        """发送信号"""
        try:
            # 检查信号队列是否已满
            if len(self.signal_queue) >= self.config['max_signal_queue_size']:
                self.logger.warning("信号队列已满，丢弃最旧的信号")
                self.signal_queue.pop(0)
            
            # 设置信号TTL
            if signal.ttl is None:
                signal.ttl = datetime.now() + timedelta(seconds=self.config['signal_ttl_seconds'])
            
            # 添加到队列
            self.signal_queue.append(signal)
            
            # 如果是紧急信号，立即处理
            if signal.priority == SignalPriority.EMERGENCY:
                await self._process_signal_immediately(signal)
            
            return True
            
        except Exception as e:
            self.logger.error(f"发送信号失败: {e}")
            return False
    
    async def broadcast_signal(self, signal: NeuralSignal, exclude_organs: Optional[Set[str]] = None) -> int:
        """广播信号到所有器官"""
        if exclude_organs is None:
            exclude_organs = set()
        
        success_count = 0
        
        for organ_name in self.organs.keys():
            if organ_name not in exclude_organs and organ_name != signal.source_organ:
                # 创建针对特定器官的信号副本
                organ_signal = NeuralSignal(
                    signal_id=f"{signal.signal_id}_{organ_name}",
                    signal_type=signal.signal_type,
                    priority=signal.priority,
                    source_organ=signal.source_organ,
                    target_organ=organ_name,
                    data=signal.data.copy(),
                    timestamp=signal.timestamp,
                    ttl=signal.ttl,
                    response_required=signal.response_required
                )
                
                if await self.send_signal(organ_signal):
                    success_count += 1
        
        return success_count
    
    def _start_signal_processing(self):
        """启动信号处理循环"""
        async def signal_processing_loop():
            while self.signal_processing_active:
                try:
                    await self._process_signal_queue()
                    await asyncio.sleep(self.config['signal_processing_interval'])
                except Exception as e:
                    self.logger.error(f"信号处理循环错误: {e}")
                    await asyncio.sleep(1)
        
        # 在后台运行信号处理循环
        asyncio.create_task(signal_processing_loop())
    
    async def _process_signal_queue(self):
        """处理信号队列"""
        if not self.signal_queue:
            return
        
        # 按优先级排序
        self.signal_queue.sort(key=lambda s: s.priority.value, reverse=True)
        
        # 处理信号
        processed_signals = []
        
        for signal in self.signal_queue[:10]:  # 每次最多处理10个信号
            if signal.is_expired():
                self.logger.warning(f"信号过期: {signal.signal_id}")
                processed_signals.append(signal)
                continue
            
            try:
                success = await self._process_signal(signal)
                
                if success:
                    signal.processed = True
                    self.network_stats['successful_transmissions'] += 1
                else:
                    self.network_stats['failed_transmissions'] += 1
                
                processed_signals.append(signal)
                self.network_stats['total_signals_processed'] += 1
                
            except Exception as e:
                self.logger.error(f"处理信号失败 {signal.signal_id}: {e}")
                processed_signals.append(signal)
                self.network_stats['failed_transmissions'] += 1
        
        # 从队列中移除已处理的信号
        for signal in processed_signals:
            if signal in self.signal_queue:
                self.signal_queue.remove(signal)
        
        # 将处理过的信号添加到历史记录
        self.signal_history.extend(processed_signals)
        
        # 保持历史记录在合理范围内
        if len(self.signal_history) > self.config['signal_history_size']:
            self.signal_history = self.signal_history[-self.config['signal_history_size']//2:]
    
    async def _process_signal(self, signal: NeuralSignal) -> bool:
        """处理单个信号"""
        try:
            # 如果有指定目标器官
            if signal.target_organ:
                return await self._deliver_signal_to_organ(signal, signal.target_organ)
            
            # 如果没有指定目标，根据信号类型路由
            target_organs = await self._route_signal(signal)
            
            success_count = 0
            for target_organ in target_organs:
                if await self._deliver_signal_to_organ(signal, target_organ):
                    success_count += 1
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"处理信号失败: {e}")
            return False
    
    async def _process_signal_immediately(self, signal: NeuralSignal):
        """立即处理紧急信号"""
        try:
            await self._process_signal(signal)
        except Exception as e:
            self.logger.error(f"立即处理信号失败: {e}")
    
    async def _deliver_signal_to_organ(self, signal: NeuralSignal, target_organ: str) -> bool:
        """将信号传递给指定器官"""
        try:
            if target_organ not in self.organs:
                self.logger.warning(f"目标器官不存在: {target_organ}")
                return False
            
            organ_instance = self.organs[target_organ]
            
            # 检查器官是否有接收信号的方法
            if hasattr(organ_instance, 'receive_signal_from_organ'):
                # 调用器官的信号接收方法
                response = await organ_instance.receive_signal_from_organ(
                    signal.source_organ, 
                    signal.data
                )
                
                # 更新神经通路活动
                self._update_pathway_activity(signal.source_organ, target_organ, True)
                
                # 如果需要响应，处理响应
                if signal.response_required and response:
                    await self._handle_signal_response(signal, response, target_organ)
                
                return True
            else:
                self.logger.warning(f"器官 {target_organ} 不支持信号接收")
                return False
                
        except Exception as e:
            self.logger.error(f"传递信号到器官 {target_organ} 失败: {e}")
            self._update_pathway_activity(signal.source_organ, target_organ, False)
            return False
    
    async def _route_signal(self, signal: NeuralSignal) -> List[str]:
        """路由信号到合适的器官"""
        target_organs = []
        
        # 基于信号类型的路由规则
        routing_rules = {
            SignalType.PERCEPTION_UPDATE: ['创作表达器官', '主动表达器官', '情感系统'],
            SignalType.EMOTION_TRIGGER: ['所有器官'],
            SignalType.MEMORY_RECALL: ['记忆系统', '认知系统'],
            SignalType.SKILL_REQUEST: ['技能协调器官'],
            SignalType.EXPRESSION_TRIGGER: ['创作表达器官', '主动表达器官'],
            SignalType.SAFETY_ALERT: ['安全防护器官'],
            SignalType.COORDINATION_REQUEST: ['技能协调器官', '关系协调器官'],
            SignalType.STATUS_UPDATE: ['所有器官'],
            SignalType.LEARNING_FEEDBACK: ['所有器官'],
            SignalType.EMERGENCY_SIGNAL: ['所有器官']
        }
        
        rules = routing_rules.get(signal.signal_type, [])
        
        for rule in rules:
            if rule == '所有器官':
                target_organs.extend([name for name in self.organs.keys() if name != signal.source_organ])
            elif rule in self.organs:
                target_organs.append(rule)
        
        # 如果使用AI协调，让AI决定路由
        if self.config['coordination_ai_enabled'] and len(target_organs) > 3:
            target_organs = await self._ai_route_signal(signal, target_organs)
        
        return list(set(target_organs))  # 去重
    
    async def _ai_route_signal(self, signal: NeuralSignal, candidate_organs: List[str]) -> List[str]:
        """使用AI协调信号路由"""
        try:
            routing_context = {
                'signal': signal.to_dict(),
                'candidate_organs': candidate_organs,
                'network_state': self.get_network_status(),
                'organ_status': {name: self._get_organ_status(name) for name in candidate_organs}
            }
            
            routing_prompt = f"""
            作为林嫣然的神经网络协调系统，请决定如何路由这个信号：
            
            信号信息：{signal.to_dict()}
            候选器官：{candidate_organs}
            网络状态：{routing_context['network_state']}
            
            请选择最合适的器官来处理这个信号，考虑：
            1. 信号的类型和内容
            2. 器官的功能和当前状态
            3. 网络效率和负载平衡
            4. 林嫣然的整体利益
            
            请返回一个器官名称的列表。
            """
            
            ai_response = await self.coordination_ai.generate_response(routing_prompt)
            
            # 解析AI响应，提取器官名称
            selected_organs = []
            for organ in candidate_organs:
                if organ in ai_response:
                    selected_organs.append(organ)
            
            return selected_organs if selected_organs else candidate_organs[:3]  # 兜底策略
            
        except Exception as e:
            self.logger.error(f"AI路由失败: {e}")
            return candidate_organs[:3]  # 兜底策略
    
    async def _handle_signal_response(self, original_signal: NeuralSignal, response: Dict[str, Any], responder_organ: str):
        """处理信号响应"""
        try:
            # 创建响应信号
            response_signal = NeuralSignal(
                signal_id=f"response_{original_signal.signal_id}",
                signal_type=SignalType.STATUS_UPDATE,
                priority=SignalPriority.NORMAL,
                source_organ=responder_organ,
                target_organ=original_signal.source_organ,
                data={
                    'original_signal_id': original_signal.signal_id,
                    'response': response,
                    'response_type': 'signal_response'
                },
                timestamp=datetime.now(),
                response_required=False
            )
            
            await self.send_signal(response_signal)
            
        except Exception as e:
            self.logger.error(f"处理信号响应失败: {e}")
    
    def _update_pathway_activity(self, source_organ: str, target_organ: str, success: bool):
        """更新神经通路活动"""
        # 查找相关通路
        for pathway in self.neural_pathways.values():
            if ((pathway.organ1 == source_organ and pathway.organ2 == target_organ) or
                (pathway.organ1 == target_organ and pathway.organ2 == source_organ)):
                pathway.update_activity(success)
                break
    
    def _get_organ_status(self, organ_name: str) -> Dict[str, Any]:
        """获取器官状态"""
        if organ_name not in self.organs:
            return {'status': 'not_found'}
        
        organ = self.organs[organ_name]
        if hasattr(organ, 'get_organ_status'):
            return organ.get_organ_status()
        else:
            return {'status': 'active', 'details': 'status_method_not_available'}
    
    # 公共接口方法
    def get_network_status(self) -> Dict[str, Any]:
        """获取网络状态"""
        return {
            'network_active': self.network_active,
            'registered_organs': list(self.organs.keys()),
            'active_pathways': len(self.neural_pathways),
            'signal_queue_size': len(self.signal_queue),
            'signal_history_size': len(self.signal_history),
            'network_stats': self.network_stats,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_pathway_info(self, organ1: str, organ2: str) -> Optional[Dict[str, Any]]:
        """获取通路信息"""
        for pathway in self.neural_pathways.values():
            if ((pathway.organ1 == organ1 and pathway.organ2 == organ2) or
                (pathway.organ1 == organ2 and pathway.organ2 == organ1)):
                return pathway.get_pathway_info()
        return None
    
    def get_all_pathways(self) -> List[Dict[str, Any]]:
        """获取所有通路信息"""
        return [pathway.get_pathway_info() for pathway in self.neural_pathways.values()]
    
    def get_organ_connections(self, organ_name: str) -> List[str]:
        """获取器官的所有连接"""
        connections = []
        for pathway in self.neural_pathways.values():
            if pathway.organ1 == organ_name:
                connections.append(pathway.organ2)
            elif pathway.organ2 == organ_name:
                connections.append(pathway.organ1)
        return list(set(connections))
    
    async def coordinate_organ_collaboration(self, primary_organ: str, supporting_organs: List[str], 
                                           task: Dict[str, Any]) -> Dict[str, Any]:
        """协调器官协作"""
        try:
            # 创建协作信号
            collaboration_signal = NeuralSignal(
                signal_id=f"collaboration_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                signal_type=SignalType.COORDINATION_REQUEST,
                priority=SignalPriority.HIGH,
                source_organ="neural_network",
                target_organ=None,
                data={
                    'collaboration_type': 'organ_collaboration',
                    'primary_organ': primary_organ,
                    'supporting_organs': supporting_organs,
                    'task': task,
                    'coordination_id': f"coord_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                },
                timestamp=datetime.now(),
                response_required=True
            )
            
            # 发送给主要器官
            if primary_organ in self.organs:
                await self._deliver_signal_to_organ(collaboration_signal, primary_organ)
            
            # 发送给支持器官
            for organ in supporting_organs:
                if organ in self.organs:
                    await self._deliver_signal_to_organ(collaboration_signal, organ)
            
            return {
                'collaboration_initiated': True,
                'coordination_id': collaboration_signal.data['coordination_id'],
                'participating_organs': [primary_organ] + supporting_organs,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"协调器官协作失败: {e}")
            return {
                'collaboration_initiated': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def optimize_network(self):
        """优化网络性能"""
        try:
            # 清理过期信号
            current_time = datetime.now()
            self.signal_queue = [s for s in self.signal_queue if not s.is_expired()]
            
            # 优化通路强度
            for pathway in self.neural_pathways.values():
                # 如果通路长时间未使用，降低强度
                if (current_time - pathway.last_activity).total_seconds() > 3600:  # 1小时
                    pathway.strength = max(0.1, pathway.strength - 0.1)
            
            # 计算网络效率
            total_signals = self.network_stats['total_signals_processed']
            if total_signals > 0:
                success_rate = self.network_stats['successful_transmissions'] / total_signals
                self.network_stats['network_efficiency'] = success_rate
            
            self.logger.info("网络优化完成")
            
        except Exception as e:
            self.logger.error(f"网络优化失败: {e}")
    
    def shutdown(self):
        """关闭神经网络"""
        self.network_active = False
        self.signal_processing_active = False
        self.logger.info("器官神经网络已关闭")
    
    def __repr__(self):
        return f"OrganNeuralNetwork(organs={len(self.organs)}, pathways={len(self.neural_pathways)})"

# 全局实例
_neural_network_instance = None

def get_instance(config_path: Optional[str] = None, ai_adapter=None) -> OrganNeuralNetwork:
    """获取器官神经网络单例"""
    global _neural_network_instance
    if _neural_network_instance is None:
        _neural_network_instance = OrganNeuralNetwork(config_path, ai_adapter)
    return _neural_network_instance 