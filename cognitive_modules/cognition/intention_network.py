#!/usr/bin/env python3
"""
意图网络系统 - Intention Network System

该模块负责构建和维护用户意图的网络结构，包括：
1. 意图理解 - 深度解析用户意图
2. 意图关联 - 建立意图之间的联系
3. 意图转化 - 处理意图随时间的演变
4. 意图预测 - 预测可能的后续意图
5. 意图响应策略 - 基于意图网络生成响应策略

作者: Claude
创建日期: 2024-07-31
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random
import datetime
import threading
from typing import Dict, Any, List, Optional, Tuple, Set, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
cognition_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(cognition_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.base.cognitive_interface import ICognitionModule
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.cognition.intention_network")

class IntentionNetwork(CognitiveModuleBase, ICognitionModule):
    """
    意图网络系统模块
    
    构建和维护用户意图的网络结构，实现复杂意图的理解、预测和响应。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化意图网络系统模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "intention_network", "cognition", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 意图网络
        self.intention_graph = {}  # 意图关系图: {意图类型: {关联意图: 权重}}
        
        # 意图转化模式
        self.intention_transitions = {}  # 意图转化模式: {前置意图: {后续意图: 频率}}
        
        # 用户意图历史
        self.user_intention_history = {}  # 用户意图历史: {user_id: [意图记录]}
        
        # 意图响应策略
        self.response_strategies = {
            "信息查询": {
                "strategy": "提供准确、相关的信息",
                "tone": "清晰、专业、有帮助",
                "priority": ["准确性", "完整性", "相关性"]
            },
            "任务执行": {
                "strategy": "明确任务目标，执行并反馈结果",
                "tone": "高效、可靠、确认式",
                "priority": ["效率", "准确性", "完成度"]
            },
            "推荐请求": {
                "strategy": "提供个性化、多样化的推荐选项",
                "tone": "建议性、解释性、平衡",
                "priority": ["相关性", "多样性", "个性化"]
            },
            "导航或指引": {
                "strategy": "提供清晰、步骤化的指导",
                "tone": "明确、耐心、支持性",
                "priority": ["清晰性", "准确性", "易理解性"]
            },
            "交互或对话": {
                "strategy": "保持自然、有趣的对话流",
                "tone": "友好、投入、适应性",
                "priority": ["互动性", "情感共鸣", "持续性"]
            },
            "个人助理任务": {
                "strategy": "高效执行，注重个人化需求",
                "tone": "专业、可靠、贴心",
                "priority": ["准确性", "个性化", "及时性"]
            },
            "学习或教育": {
                "strategy": "提供结构化、深入的知识和指导",
                "tone": "教学性、耐心、鼓励",
                "priority": ["准确性", "理解性", "适应学习节奏"]
            },
            "创意或娱乐": {
                "strategy": "提供有创意、有趣的内容",
                "tone": "活泼、创新、愉悦",
                "priority": ["创新性", "娱乐性", "意外性"]
            },
            "商业或专业": {
                "strategy": "提供专业、深入的分析和建议",
                "tone": "专业、权威、客观",
                "priority": ["专业性", "深度", "准确性"]
            },
            "技术支持": {
                "strategy": "提供技术问题的解决方案",
                "tone": "技术性、精确、解释性",
                "priority": ["解决问题", "清晰性", "可执行性"]
            },
            "绘画创作": {
                "strategy": "理解视觉需求，创建符合期望的图像",
                "tone": "创意性、描述性、确认式",
                "priority": ["符合需求", "审美价值", "创意表达"]
            },
            "网址解读": {
                "strategy": "安全解析链接内容，提取关键信息",
                "tone": "客观、总结性、信息化",
                "priority": ["安全性", "准确性", "相关性"]
            },
            "不想互动": {
                "strategy": "尊重用户意愿，减少互动",
                "tone": "尊重、简洁、平静",
                "priority": ["尊重隐私", "简洁", "不打扰"]
            },
            "关系变更": {
                "strategy": "尊重用户决定，提供适当回应",
                "tone": "理解、尊重、中立",
                "priority": ["尊重用户", "适当回应", "情感理解"]
            },
            "默认": {
                "strategy": "根据上下文灵活回应",
                "tone": "适应性、自然、真诚",
                "priority": ["相关性", "真诚", "有帮助"]
            }
        }
        
        # 意图优先级
        self.intention_priority = {
            "关系变更": 10,   # 最高优先级
            "不想互动": 9,
            "技术支持": 8,
            "任务执行": 7,
            "个人助理任务": 6,
            "导航或指引": 5,
            "信息查询": 4,
            "学习或教育": 3,
            "推荐请求": 2,
            "创意或娱乐": 1,
            "交互或对话": 0,   # 最低优先级
            "默认": 0
        }
        
        # 意图时间敏感度
        self.intention_time_sensitivity = {
            "技术支持": "高",
            "任务执行": "高",
            "信息查询": "中",
            "导航或指引": "中",
            "个人助理任务": "中",
            "学习或教育": "低",
            "推荐请求": "低",
            "创意或娱乐": "低",
            "交互或对话": "低",
            "默认": "低"
        }
        
        # 意图关联强度阈值
        self.association_threshold = 0.3
        
        # 加载意图网络数据
        self._load_intention_network()
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"意图网络系统模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("intention_analyzed", self._on_intention_analyzed)
        self.event_bus.subscribe("user_message", self._on_user_message)
        self.event_bus.subscribe("system_response", self._on_system_response)
        
        logger.debug("已订阅相关事件")
    
    def _load_intention_network(self):
        """从存储加载意图网络数据"""
        data_dir = os.path.join(root_dir, "data", "cognition", "intention_network")
        os.makedirs(data_dir, exist_ok=True)
        
        # 加载意图关系图
        graph_path = os.path.join(data_dir, "intention_graph.json")
        if os.path.exists(graph_path):
            try:
                with open(graph_path, "r", encoding="utf-8") as f:
                    self.intention_graph = json.load(f)
                logger.info("已加载意图关系图")
            except Exception as e:
                logger.error_status(f"加载意图关系图失败: {e}")
        
        # 加载意图转化模式
        transition_path = os.path.join(data_dir, "intention_transitions.json")
        if os.path.exists(transition_path):
            try:
                with open(transition_path, "r", encoding="utf-8") as f:
                    self.intention_transitions = json.load(f)
                logger.info("已加载意图转化模式")
            except Exception as e:
                logger.error_status(f"加载意图转化模式失败: {e}")
    
    def _save_intention_network(self):
        """保存意图网络数据到存储"""
        data_dir = os.path.join(root_dir, "data", "cognition", "intention_network")
        os.makedirs(data_dir, exist_ok=True)
        
        # 保存意图关系图
        graph_path = os.path.join(data_dir, "intention_graph.json")
        try:
            with open(graph_path, "w", encoding="utf-8") as f:
                json.dump(self.intention_graph, f, ensure_ascii=False, indent=2)
            logger.debug("已保存意图关系图")
        except Exception as e:
            logger.error_status(f"保存意图关系图失败: {e}")
        
        # 保存意图转化模式
        transition_path = os.path.join(data_dir, "intention_transitions.json")
        try:
            with open(transition_path, "w", encoding="utf-8") as f:
                json.dump(self.intention_transitions, f, ensure_ascii=False, indent=2)
            logger.debug("已保存意图转化模式")
        except Exception as e:
            logger.error_status(f"保存意图转化模式失败: {e}")
    
    def _on_intention_analyzed(self, data: Dict[str, Any]):
        """处理意图分析事件"""
        try:
            intention = data.get("intention", {})
            user_id = data.get("user_id", "")
            message = data.get("message", "")
            
            if not intention or not user_id:
                return
            
            main_intent = intention.get("main_intent", "默认")
            sub_intent = intention.get("sub_intent")
            
            # 记录用户意图
            self._record_user_intention(user_id, main_intent, sub_intent, message)
            
            # 更新意图关系
            if sub_intent:
                self._update_intention_relation(main_intent, sub_intent)
            
            # 更新意图转化模式
            self._update_intention_transition(user_id, main_intent)
            
            # 定期保存意图网络数据
            if random.random() < 0.1:  # 10%概率保存
                self._save_intention_network()
        except Exception as e:
            logger.error_status(f"处理意图分析事件失败: {e}")
    
    def _on_user_message(self, data: Dict[str, Any]):
        """处理用户消息事件"""
        try:
            message = data.get("message", "")
            user_id = data.get("user_id", "")
            
            if not message or not user_id:
                return
            
            # 这里可以进行简单的意图预判，但详细分析由IntentionAnalysis模块完成
            # 我们主要关注消息与意图网络的关联
            
            # 更新用户上下文中的最近消息
            user_context = self.life_context.get_user_context(user_id)
            recent_messages = user_context.get("recent_messages", [])
            recent_messages.append({
                "content": message,
                "timestamp": time.time()
            })
            
            # 保留最近的10条消息
            recent_messages = recent_messages[-10:]
            
            # 更新上下文
            self.life_context.update_user_context(user_id, "recent_messages", recent_messages)
        except Exception as e:
            logger.error_status(f"处理用户消息事件失败: {e}")
    
    def _on_system_response(self, data: Dict[str, Any]):
        """处理系统响应事件"""
        try:
            response = data.get("response", "")
            user_id = data.get("user_id", "")
            
            if not response or not user_id:
                return
            
            # 更新用户对话历史
            self._update_user_dialogue(user_id, "system", response)
        except Exception as e:
            logger.error_status(f"处理系统响应事件失败: {e}")
    
    def _record_user_intention(self, user_id: str, main_intent: str, sub_intent: Optional[str], message: str):
        """记录用户意图"""
        if user_id not in self.user_intention_history:
            self.user_intention_history[user_id] = []
        
        # 创建意图记录
        intention_record = {
            "main_intent": main_intent,
            "sub_intent": sub_intent,
            "message": message,
            "timestamp": time.time(),
            "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 添加到历史记录
        self.user_intention_history[user_id].append(intention_record)
        
        # 限制历史记录长度
        if len(self.user_intention_history[user_id]) > 100:
            self.user_intention_history[user_id] = self.user_intention_history[user_id][-100:]
    
    def _update_intention_relation(self, intent1: str, intent2: str, weight_increment: float = 0.1):
        """更新意图之间的关系"""
        # 确保意图存在于图中
        if intent1 not in self.intention_graph:
            self.intention_graph[intent1] = {}
        if intent2 not in self.intention_graph:
            self.intention_graph[intent2] = {}
        
        # 更新意图关系权重（双向）
        if intent2 in self.intention_graph[intent1]:
            self.intention_graph[intent1][intent2] += weight_increment
        else:
            self.intention_graph[intent1][intent2] = weight_increment
            
        if intent1 in self.intention_graph[intent2]:
            self.intention_graph[intent2][intent1] += weight_increment
        else:
            self.intention_graph[intent2][intent1] = weight_increment
            
        # 规范化权重
        self._normalize_intention_weights(intent1)
        self._normalize_intention_weights(intent2)
    
    def _normalize_intention_weights(self, intent: str):
        """规范化意图关系权重"""
        if intent not in self.intention_graph:
            return
            
        # 获取所有关联意图的权重
        weights = list(self.intention_graph[intent].values())
        
        # 如果没有权重，返回
        if not weights:
            return
            
        # 计算权重总和
        total_weight = sum(weights)
        
        # 规范化权重
        if total_weight > 0:
            for related_intent in self.intention_graph[intent]:
                self.intention_graph[intent][related_intent] /= total_weight
    
    def _update_intention_transition(self, user_id: str, current_intent: str):
        """更新意图转化模式"""
        # 获取用户意图历史
        history = self.user_intention_history.get(user_id, [])
        
        # 如果历史记录少于2条，无法更新转化模式
        if len(history) < 2:
            return
            
        # 获取前一个意图
        previous_record = history[-2]
        previous_intent = previous_record["main_intent"]
        
        # 如果前一个意图与当前意图相同，不更新转化模式
        if previous_intent == current_intent:
            return
            
        # 确保前一个意图存在于转化模式中
        if previous_intent not in self.intention_transitions:
            self.intention_transitions[previous_intent] = {}
            
        # 更新转化频率
        if current_intent in self.intention_transitions[previous_intent]:
            self.intention_transitions[previous_intent][current_intent] += 1
        else:
            self.intention_transitions[previous_intent][current_intent] = 1
    
    def _update_user_dialogue(self, user_id: str, role: str, content: str):
        """更新用户对话历史"""
        # 获取用户上下文
        user_context = self.life_context.get_user_context(user_id)
        
        # 获取对话历史
        dialogue_history = user_context.get("dialogue_history", [])
        
        # 添加新消息
        dialogue_history.append({
            "role": role,
            "content": content,
            "timestamp": time.time()
        })
        
        # 保留最近的50条消息
        dialogue_history = dialogue_history[-50:]
        
        # 更新上下文
        self.life_context.update_user_context(user_id, "dialogue_history", dialogue_history)
    
    def get_related_intentions(self, intent: str, threshold: float = None) -> Dict[str, float]:
        """
        获取与指定意图相关的其他意图
        
        Args:
            intent: 意图类型
            threshold: 关联强度阈值，默认使用模块配置
            
        Returns:
            相关意图及其关联强度的字典
        """
        if intent not in self.intention_graph:
            return {}
            
        threshold = threshold or self.association_threshold
        
        # 筛选超过阈值的相关意图
        related = {k: v for k, v in self.intention_graph[intent].items() if v >= threshold}
        
        # 按关联强度降序排序
        related = dict(sorted(related.items(), key=lambda x: x[1], reverse=True))
        
        return related
    
    def predict_next_intention(self, user_id: str, current_intent: str = None) -> List[Tuple[str, float]]:
        """
        预测用户的下一个可能意图
        
        Args:
            user_id: 用户ID
            current_intent: 当前意图类型，如果为None则从历史中获取
            
        Returns:
            可能的下一个意图及其概率列表，按概率降序排序
        """
        # 如果未提供当前意图，尝试从历史记录获取
        if current_intent is None:
            history = self.user_intention_history.get(user_id, [])
            if history:
                current_intent = history[-1]["main_intent"]
            else:
                return []
        
        # 如果当前意图没有转化模式，返回空列表
        if current_intent not in self.intention_transitions:
            return []
            
        # 获取所有可能的下一个意图及其频率
        transitions = self.intention_transitions[current_intent]
        
        # 计算总频率
        total_frequency = sum(transitions.values())
        
        # 计算各意图的概率
        probabilities = [(intent, freq / total_frequency) for intent, freq in transitions.items()]
        
        # 按概率降序排序
        probabilities.sort(key=lambda x: x[1], reverse=True)
        
        return probabilities
    
    def get_response_strategy(self, intent: str) -> Dict[str, Any]:
        """
        获取意图的响应策略
        
        Args:
            intent: 意图类型
            
        Returns:
            响应策略字典
        """
        return self.response_strategies.get(intent, self.response_strategies["默认"])
    
    def get_intention_history(self, user_id: str, count: int = 10) -> List[Dict[str, Any]]:
        """
        获取用户意图历史
        
        Args:
            user_id: 用户ID
            count: 返回的历史记录数量
            
        Returns:
            意图历史记录列表
        """
        history = self.user_intention_history.get(user_id, [])
        return history[-count:] if history else []
    
    def get_intention_pattern(self, user_id: str) -> Dict[str, Any]:
        """
        分析用户意图模式
        
        Args:
            user_id: 用户ID
            
        Returns:
            意图模式分析结果
        """
        history = self.user_intention_history.get(user_id, [])
        
        if not history:
            return {"error": "无足够历史数据进行分析"}
            
        # 统计各意图出现频率
        intent_counts = {}
        for record in history:
            intent = record["main_intent"]
            intent_counts[intent] = intent_counts.get(intent, 0) + 1
            
        # 计算总记录数
        total_records = len(history)
        
        # 计算各意图占比
        intent_distribution = {intent: count / total_records for intent, count in intent_counts.items()}
        
        # 分析意图序列模式
        sequence_patterns = self._analyze_sequence_patterns(history)
        
        # 时间模式分析
        time_patterns = self._analyze_time_patterns(history)
        
        return {
            "intent_distribution": intent_distribution,
            "dominant_intent": max(intent_distribution.items(), key=lambda x: x[1])[0] if intent_distribution else None,
            "sequence_patterns": sequence_patterns,
            "time_patterns": time_patterns,
            "data_points": len(history)
        }
    
    def _analyze_sequence_patterns(self, history: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析意图序列模式"""
        if len(history) < 3:
            return []
            
        # 提取意图序列
        intent_sequence = [record["main_intent"] for record in history]
        
        # 寻找常见的三元组模式
        triplet_counts = {}
        for i in range(len(intent_sequence) - 2):
            triplet = (intent_sequence[i], intent_sequence[i+1], intent_sequence[i+2])
            triplet_counts[triplet] = triplet_counts.get(triplet, 0) + 1
            
        # 筛选出现频率较高的模式
        significant_patterns = []
        for triplet, count in sorted(triplet_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
            significant_patterns.append({
                "pattern": " → ".join(triplet),
                "frequency": count,
                "significance": count / (len(intent_sequence) - 2)
            })
            
        return significant_patterns
    
    def _analyze_time_patterns(self, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析意图的时间模式"""
        if not history:
            return {}
            
        # 统计每小时的意图分布
        hourly_distribution = {}
        for record in history:
            if "timestamp" in record:
                hour = datetime.datetime.fromtimestamp(record["timestamp"]).hour
                intent = record["main_intent"]
                
                if hour not in hourly_distribution:
                    hourly_distribution[hour] = {}
                    
                hourly_distribution[hour][intent] = hourly_distribution[hour].get(intent, 0) + 1
        
        # 找出每个小时的主导意图
        hourly_dominant = {}
        for hour, intents in hourly_distribution.items():
            if intents:
                dominant = max(intents.items(), key=lambda x: x[1])
                hourly_dominant[hour] = {
                    "intent": dominant[0],
                    "count": dominant[1]
                }
        
        return {
            "hourly_dominant_intents": hourly_dominant
        }
    
    def process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据（实现ICognitionModule接口）
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        input_type = input_data.get("type", "")
        
        if input_type == "get_related_intentions":
            intent = input_data.get("intent")
            threshold = input_data.get("threshold")
            if not intent:
                return {"error": "未提供意图类型"}
            return {"result": self.get_related_intentions(intent, threshold)}
            
        elif input_type == "predict_next_intention":
            user_id = input_data.get("user_id")
            current_intent = input_data.get("current_intent")
            if not user_id:
                return {"error": "未提供用户ID"}
            return {"result": self.predict_next_intention(user_id, current_intent)}
            
        elif input_type == "get_response_strategy":
            intent = input_data.get("intent")
            if not intent:
                return {"error": "未提供意图类型"}
            return {"result": self.get_response_strategy(intent)}
            
        elif input_type == "get_intention_history":
            user_id = input_data.get("user_id")
            count = input_data.get("count", 10)
            if not user_id:
                return {"error": "未提供用户ID"}
            return {"result": self.get_intention_history(user_id, count)}
            
        elif input_type == "get_intention_pattern":
            user_id = input_data.get("user_id")
            if not user_id:
                return {"error": "未提供用户ID"}
            return {"result": self.get_intention_pattern(user_id)}
            
        else:
            return {"error": "未知的输入类型"}
    
    def shutdown(self):
        """关闭模块"""
        # 保存意图网络数据
        self._save_intention_network()
        
        logger.info(f"意图网络系统模块 {self.module_id} 已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> IntentionNetwork:
    """
    获取意图网络系统模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        意图网络系统模块实例
    """
    return IntentionNetwork(config=config) 