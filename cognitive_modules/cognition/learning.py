"""
学习机制模块

负责实现数字生命体的学习能力，包括经验学习、知识迁移、技能学习等。
通过与记忆模块、推理模块和情感模块的互动，实现持续成长能力。
"""

import os
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
import random
from typing import Dict, Any, List, Optional, Tuple, Set, Union

# 配置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

class Learning:
    """
    学习机制类
    
    负责实现数字生命体的学习能力，包括：
    - 经验学习（从过去经验中提取模式和规律）
    - 知识迁移（将一个领域的知识应用到新领域）
    - 技能学习（掌握和改进特定技能）
    - 概念形成（形成和完善抽象概念）
    - 强化学习（通过反馈改进行为）
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, identity: str = None, config: Dict[str, Any] = None):
        """
        初始化学习模块
        
        Args:
            identity: 数字生命体标识
            config: 模块配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        self.identity = identity or "default"
        self.config = config or {}
        
        # 初始化学习参数
        self.learning_rate = self.config.get('learning_rate', 0.1)  # 学习速率
        self.exploration_rate = self.config.get('exploration_rate', 0.3)  # 探索率
        self.memory_impact = self.config.get('memory_impact', 0.7)  # 记忆影响因子
        self.emotion_impact = self.config.get('emotion_impact', 0.5)  # 情感影响因子
        self.retention_decay = self.config.get('retention_decay', 0.05)  # 保留衰减率
        
        # 学习状态
        self.learning_state = {
            "active_learning": True,  # 是否主动学习
            "learning_focus": None,   # 当前学习焦点
            "curiosity_level": 0.7,   # 好奇心水平
            "mastery_thresholds": {   # 技能掌握阈值
                "beginner": 0.3,
                "intermediate": 0.6,
                "advanced": 0.85,
                "expert": 0.95
            }
        }
        
        # 经验和知识库
        self.knowledge_base = {}  # 知识库
        self.skill_levels = {}    # 技能水平
        self.learning_history = [] # 学习历史
        self.concept_relations = {} # 概念关系
        
        # 初始化模块间连接
        self._initialize_module_connections()
        
        # 加载已有学习数据
        self._load_learning_data()
        
        logger.success("学习机制模块初始化完成")
    
    def _initialize_module_connections(self):
        """初始化与其他模块的连接"""
        # 这里将在实际整合时连接到记忆、情感和推理模块
        self.memory_module = None
        self.emotion_module = None
        self.reasoning_module = None
        
        # 尝试导入并连接其他模块
        try:
            from cognitive_modules.memory.memory_integration import get_instance as get_memory_instance
            self.memory_module = get_memory_instance(self.identity)
            logger.success("成功连接记忆模块")
        except ImportError:
            logger.warning_status("无法导入记忆模块，部分学习功能将受限")
            
        try:
            from cognitive_modules.emotion.emotion_simulation import get_instance as get_emotion_instance
            self.emotion_module = get_emotion_instance(self.identity)
            logger.success("成功连接情感模块")
        except ImportError:
            logger.warning_status("无法导入情感模块，情感影响学习的功能将受限")
            
        try:
            from cognitive_modules.cognition.reasoning import get_instance as get_reasoning_instance
            self.reasoning_module = get_reasoning_instance()
            logger.success("成功连接推理模块")
        except ImportError:
            logger.warning_status("无法导入推理模块，复杂学习功能将受限")
    
    def _load_learning_data(self):
        """加载已有学习数据"""
        storage_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                  "storage", self.identity, "learning")
        
        # 创建存储目录（如果不存在）
        os.makedirs(storage_dir, exist_ok=True)
        
        # 尝试加载知识库
        knowledge_path = os.path.join(storage_dir, "knowledge_base.json")
        if os.path.exists(knowledge_path):
            try:
                with open(knowledge_path, 'r', encoding='utf-8') as f:
                    self.knowledge_base = json.load(f)
                logger.info(f"已加载知识库，包含 {len(self.knowledge_base)} 个知识条目")
            except Exception as e:
                logger.error_status(f"加载知识库失败: {e}")
        
        # 尝试加载技能水平
        skills_path = os.path.join(storage_dir, "skill_levels.json")
        if os.path.exists(skills_path):
            try:
                with open(skills_path, 'r', encoding='utf-8') as f:
                    self.skill_levels = json.load(f)
                logger.info(f"已加载技能水平，包含 {len(self.skill_levels)} 个技能")
            except Exception as e:
                logger.error_status(f"加载技能水平失败: {e}")
        
        # 尝试加载概念关系
        concepts_path = os.path.join(storage_dir, "concept_relations.json")
        if os.path.exists(concepts_path):
            try:
                with open(concepts_path, 'r', encoding='utf-8') as f:
                    self.concept_relations = json.load(f)
                logger.info(f"已加载概念关系，包含 {len(self.concept_relations)} 个概念")
            except Exception as e:
                logger.error_status(f"加载概念关系失败: {e}")
    
    def _save_learning_data(self):
        """保存学习数据"""
        storage_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                  "storage", self.identity, "learning")
        
        # 创建存储目录（如果不存在）
        os.makedirs(storage_dir, exist_ok=True)
        
        # 保存知识库
        knowledge_path = os.path.join(storage_dir, "knowledge_base.json")
        try:
            with open(knowledge_path, 'w', encoding='utf-8') as f:
                json.dump(self.knowledge_base, f, ensure_ascii=False, indent=2)
            logger.debug(f"已保存知识库，包含 {len(self.knowledge_base)} 个知识条目")
        except Exception as e:
            logger.error_status(f"保存知识库失败: {e}")
        
        # 保存技能水平
        skills_path = os.path.join(storage_dir, "skill_levels.json")
        try:
            with open(skills_path, 'w', encoding='utf-8') as f:
                json.dump(self.skill_levels, f, ensure_ascii=False, indent=2)
            logger.debug(f"已保存技能水平，包含 {len(self.skill_levels)} 个技能")
        except Exception as e:
            logger.error_status(f"保存技能水平失败: {e}")
        
        # 保存概念关系
        concepts_path = os.path.join(storage_dir, "concept_relations.json")
        try:
            with open(concepts_path, 'w', encoding='utf-8') as f:
                json.dump(self.concept_relations, f, ensure_ascii=False, indent=2)
            logger.debug(f"已保存概念关系，包含 {len(self.concept_relations)} 个概念")
        except Exception as e:
            logger.error_status(f"保存概念关系失败: {e}")
    
    # =========== 经验学习功能 ===========
    
    def learn_from_experience(self, experience: Dict[str, Any]) -> Dict[str, Any]:
        """
        从经验中学习
        
        从单一经验中提取知识、更新技能和形成概念
        
        Args:
            experience: 经验数据，包含内容、情感、标签等信息
            
        Returns:
            学习结果
        """
        if not experience or not isinstance(experience, dict):
            logger.error_status("无效的经验数据")
            return {"success": False, "error": "无效的经验数据"}
        
        # 提取经验要素
        content = experience.get("content", "")
        tags = experience.get("tags", [])
        emotional_context = experience.get("emotion", "")
        emotional_intensity = experience.get("emotional_intensity", 0.5)
        importance = experience.get("importance", 0.5)
        
        if not content:
            logger.error_status("经验内容为空")
            return {"success": False, "error": "经验内容为空"}
        
        # 获取当前情感状态（如果有情感模块）
        current_emotion = None
        if self.emotion_module:
            try:
                emotion_state = self.emotion_module.get_emotion_state()
                current_emotion = emotion_state.get("current_emotion")
            except Exception as e:
                logger.warning_status(f"获取情感状态失败: {e}")
        
        # 计算学习效果修正系数
        learning_modifier = self._calculate_learning_modifier(
            importance, 
            emotional_intensity, 
            emotional_context, 
            current_emotion
        )
        
        # 执行学习过程
        knowledge_gained = self._extract_knowledge(content, tags, learning_modifier)
        skills_improved = self._update_skills(content, tags, learning_modifier)
        concepts_formed = self._form_concepts(content, tags, learning_modifier)
        
        # 记录学习历史
        learning_event = {
            "timestamp": datetime.datetime.now().isoformat(),
            "content": content[:100] + "..." if len(content) > 100 else content,
            "tags": tags,
            "emotional_context": emotional_context,
            "learning_modifier": learning_modifier,
            "knowledge_gained": len(knowledge_gained),
            "skills_improved": len(skills_improved),
            "concepts_formed": len(concepts_formed)
        }
        
        self.learning_history.append(learning_event)
        
        # 限制学习历史长度
        max_history = self.config.get("max_learning_history", 100)
        if len(self.learning_history) > max_history:
            self.learning_history = self.learning_history[-max_history:]
        
        # 保存学习数据
        self._save_learning_data()
        
        return {
            "success": True,
            "learning_modifier": learning_modifier,
            "knowledge_gained": knowledge_gained,
            "skills_improved": skills_improved,
            "concepts_formed": concepts_formed
        }
    
    def learn_from_experiences(self, experiences: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        从多个经验中学习
        
        分析多个经验，提取共同模式和规律
        
        Args:
            experiences: 经验数据列表
            
        Returns:
            学习结果
        """
        if not experiences or not isinstance(experiences, list):
            logger.error_status("无效的经验数据列表")
            return {"success": False, "error": "无效的经验数据列表"}
        
        if len(experiences) == 0:
            logger.warning_status("经验列表为空")
            return {"success": False, "error": "经验列表为空"}
        
        # 如果只有一个经验，直接调用单一经验学习
        if len(experiences) == 1:
            return self.learn_from_experience(experiences[0])
        
        # 从多个经验中学习
        total_knowledge = []
        total_skills = []
        total_concepts = []
        total_modifier = 0
        
        # 先从每个经验中分别学习
        individual_results = []
        for exp in experiences:
            result = self.learn_from_experience(exp)
            if result["success"]:
                individual_results.append(result)
                total_knowledge.extend(result["knowledge_gained"])
                total_skills.extend(result["skills_improved"])
                total_concepts.extend(result["concepts_formed"])
                total_modifier += result["learning_modifier"]
        
        if not individual_results:
            logger.error_status("所有经验都学习失败")
            return {"success": False, "error": "所有经验都学习失败"}
        
        # 平均学习修正系数
        avg_modifier = total_modifier / len(individual_results)
        
        # 分析多个经验之间的共同模式
        # 提取所有经验的内容和标签
        all_contents = [exp.get("content", "") for exp in experiences if "content" in exp]
        all_tags = []
        for exp in experiences:
            if "tags" in exp and isinstance(exp["tags"], list):
                all_tags.extend(exp["tags"])
        
        # 找出常见标签
        tag_counts = {}
        for tag in all_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        common_tags = [tag for tag, count in tag_counts.items() if count >= len(experiences) * 0.5]
        
        # 使用推理模块进行归纳推理（如果可用）
        patterns_found = []
        if self.reasoning_module:
            try:
                # 准备推理查询
                query = {
                    "examples": "\n".join(all_contents[:5])  # 限制内容数量
                }
                
                # 执行归纳推理
                reasoning_result = self.reasoning_module.reason("归纳推理", query)
                
                if reasoning_result["success"]:
                    # 提取归纳出的模式
                    patterns = reasoning_result["result"]["analysis"]
                    patterns_found.extend(patterns)
            except Exception as e:
                logger.warning_status(f"使用推理模块归纳模式失败: {e}")
        
        # 创建新的复合知识
        compound_knowledge = []
        for tag in common_tags:
            knowledge_item = {
                "concept": tag,
                "pattern": "在多个经验中反复出现",
                "confidence": min(0.5 + len(experiences) * 0.1, 0.9),
                "source": "经验归纳",
                "timestamp": datetime.datetime.now().isoformat()
            }
            self._add_to_knowledge_base(knowledge_item)
            compound_knowledge.append(knowledge_item)
        
        # 将推理得到的模式也添加到知识库
        for pattern in patterns_found:
            if isinstance(pattern, str) and pattern.strip():
                knowledge_item = {
                    "concept": "归纳模式",
                    "pattern": pattern,
                    "confidence": 0.7,
                    "source": "推理归纳",
                    "timestamp": datetime.datetime.now().isoformat()
                }
                self._add_to_knowledge_base(knowledge_item)
                compound_knowledge.append(knowledge_item)
        
        # 保存学习数据
        self._save_learning_data()
        
        return {
            "success": True,
            "learning_modifier": avg_modifier,
            "individual_learnings": len(individual_results),
            "common_tags": common_tags,
            "patterns_found": patterns_found,
            "compound_knowledge": compound_knowledge,
            "total_knowledge": len(set(total_knowledge)),
            "total_skills": len(set(total_skills)),
            "total_concepts": len(set(total_concepts))
        }
    
    def _calculate_learning_modifier(self, importance: float, emotional_intensity: float, 
                                    emotional_context: str, current_emotion: Optional[str]) -> float:
        """
        计算学习效果修正系数
        
        基于重要性、情感强度和情感状态计算学习效果
        
        Args:
            importance: 经验重要性
            emotional_intensity: 情感强度
            emotional_context: 经验中的情感
            current_emotion: 当前情感状态
            
        Returns:
            学习效果修正系数 (0.0-2.0)
        """
        # 基础修正系数基于重要性
        base_modifier = 0.5 + importance * 0.5  # 0.5-1.0
        
        # 情感强度影响
        emotion_modifier = 1.0
        if emotional_intensity > 0:
            # 情感强度高的经验更容易被学习，但过高可能干扰学习
            if emotional_intensity < 0.7:
                emotion_modifier = 1.0 + emotional_intensity * 0.5  # 1.0-1.35
            else:
                emotion_modifier = 1.35 - (emotional_intensity - 0.7) * 0.5  # 1.35-1.0
        
        # 情感一致性影响
        emotion_consistency = 1.0
        if emotional_context and current_emotion:
            if emotional_context == current_emotion:
                # 当前情感与经验情感一致时，学习效果增强
                emotion_consistency = 1.2
            elif self._are_emotions_compatible(emotional_context, current_emotion):
                # 情感相容时，学习效果略有增强
                emotion_consistency = 1.1
            elif self._are_emotions_conflicting(emotional_context, current_emotion):
                # 情感冲突时，学习效果降低
                emotion_consistency = 0.8
        
        # 计算最终修正系数
        final_modifier = base_modifier * emotion_modifier * emotion_consistency
        
        # 限制在合理范围内
        return max(0.1, min(final_modifier, 2.0))
    
    def _are_emotions_compatible(self, emotion1: str, emotion2: str) -> bool:
        """判断两种情感是否相容"""
        compatible_pairs = [
            {"好奇", "兴奋"}, {"快乐", "满足"}, {"平静", "满足"},
            {"好奇", "希望"}, {"平静", "满足"}, {"专注", "平静"}
        ]
        
        return {emotion1, emotion2} in compatible_pairs
    
    def _are_emotions_conflicting(self, emotion1: str, emotion2: str) -> bool:
        """判断两种情感是否冲突"""
        conflicting_pairs = [
            {"愤怒", "快乐"}, {"恐惧", "平静"}, {"厌恶", "喜爱"},
            {"焦虑", "满足"}, {"悲伤", "兴奋"}, {"绝望", "希望"}
        ]
        
        return {emotion1, emotion2} in conflicting_pairs
    
    def _extract_knowledge(self, content: str, tags: List[str], learning_modifier: float) -> List[str]:
        """
        从内容中提取知识
        
        Args:
            content: 经验内容
            tags: 标签列表
            learning_modifier: 学习效果修正系数
            
        Returns:
            提取的知识列表
        """
        extracted_knowledge = []
        
        # 简单知识提取策略
        if tags and content:
            for tag in tags:
                # 为每个标签创建一个知识条目
                confidence = min(0.4 + learning_modifier * 0.3, 0.9)
                knowledge_item = {
                    "concept": tag,
                    "content": content[:200],  # 限制内容长度
                    "confidence": confidence,
                    "source": "经验学习",
                    "timestamp": datetime.datetime.now().isoformat()
                }
                
                # 添加到知识库
                knowledge_id = self._add_to_knowledge_base(knowledge_item)
                if knowledge_id:
                    extracted_knowledge.append(knowledge_id)
        
        # 使用推理模块进行更复杂的知识提取（如果可用）
        if self.reasoning_module and content:
            try:
                # 准备推理查询
                query = {"problem": f"从以下内容中提取关键知识点：\n{content[:500]}"}
                
                # 执行思维链推理
                reasoning_result = self.reasoning_module.reason("思维链", query)
                
                if reasoning_result["success"]:
                    # 从推理结果中提取知识点
                    analysis_points = reasoning_result["result"]["analysis"]
                    
                    for point in analysis_points:
                        if isinstance(point, str) and point.strip():
                            # 为每个分析点创建知识条目
                            confidence = min(0.6 + learning_modifier * 0.2, 0.9)
                            knowledge_item = {
                                "concept": "推理提取",
                                "content": point,
                                "confidence": confidence,
                                "source": "推理分析",
                                "timestamp": datetime.datetime.now().isoformat()
                            }
                            
                            # 添加到知识库
                            knowledge_id = self._add_to_knowledge_base(knowledge_item)
                            if knowledge_id:
                                extracted_knowledge.append(knowledge_id)
            except Exception as e:
                logger.warning_status(f"使用推理模块提取知识失败: {e}")
        
        return extracted_knowledge
    
    def _add_to_knowledge_base(self, knowledge_item: Dict[str, Any]) -> str:
        """添加知识到知识库"""
        # 生成唯一ID
        knowledge_id = f"k_{int(time.time())}_{len(self.knowledge_base)}"
        
        # 添加到知识库
        self.knowledge_base[knowledge_id] = knowledge_item
        
        return knowledge_id
    
    # =========== 知识迁移功能 ===========
    
    def transfer_knowledge(self, source_domain: str, target_domain: str, 
                          context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        知识迁移
        
        将源领域的知识应用到目标领域
        
        Args:
            source_domain: 源领域
            target_domain: 目标领域
            context: 上下文信息
            
        Returns:
            知识迁移结果
        """
        if not source_domain or not target_domain:
            logger.error_status("源领域或目标领域为空")
            return {"success": False, "error": "源领域或目标领域为空"}
        
        # 查找与源领域相关的知识
        source_knowledge = []
        for k_id, knowledge in self.knowledge_base.items():
            if (source_domain.lower() in knowledge.get("concept", "").lower() or
                source_domain.lower() in knowledge.get("content", "").lower()):
                source_knowledge.append(knowledge)
        
        if not source_knowledge:
            logger.warning_status(f"未找到与源领域 '{source_domain}' 相关的知识")
            return {"success": False, "error": f"未找到与源领域 '{source_domain}' 相关的知识"}
        
        # 使用推理模块进行类比推理（如果可用）
        transferred_knowledge = []
        if self.reasoning_module:
            try:
                # 准备推理查询
                query = {
                    "source": source_domain,
                    "target": target_domain
                }
                
                # 添加背景信息
                background = "\n".join([
                    f"知识点: {k.get('content', '')}"
                    for k in source_knowledge[:5]  # 限制知识点数量
                ])
                
                reasoning_context = {"background": background}
                if context:
                    reasoning_context.update(context)
                
                # 执行类比推理
                reasoning_result = self.reasoning_module.reason("类比推理", query, reasoning_context)
                
                if reasoning_result["success"]:
                    # 从推理结果中提取迁移知识
                    analysis_points = reasoning_result["result"]["analysis"]
                    conclusion = reasoning_result["result"]["conclusion"]
                    
                    # 添加结论作为迁移知识
                    if conclusion:
                        knowledge_item = {
                            "concept": f"{target_domain}",
                            "content": conclusion,
                            "confidence": 0.7,
                            "source": f"从 {source_domain} 迁移",
                            "timestamp": datetime.datetime.now().isoformat()
                        }
                        knowledge_id = self._add_to_knowledge_base(knowledge_item)
                        transferred_knowledge.append(knowledge_item)
                    
                    # 添加分析点作为迁移知识
                    for point in analysis_points:
                        if isinstance(point, str) and point.strip():
                            knowledge_item = {
                                "concept": f"{target_domain}",
                                "content": point,
                                "confidence": 0.6,
                                "source": f"从 {source_domain} 迁移",
                                "timestamp": datetime.datetime.now().isoformat()
                            }
                            knowledge_id = self._add_to_knowledge_base(knowledge_item)
                            transferred_knowledge.append(knowledge_item)
            except Exception as e:
                logger.warning_status(f"使用推理模块进行知识迁移失败: {e}")
        
        # 如果推理模块不可用或失败，使用简单的知识迁移方法
        if not transferred_knowledge:
            for knowledge in source_knowledge:
                # 创建简单的迁移知识
                transfer_content = f"在 {source_domain} 中的知识可能适用于 {target_domain}: {knowledge.get('content', '')}"
                knowledge_item = {
                    "concept": f"{target_domain}",
                    "content": transfer_content,
                    "confidence": 0.4,  # 较低的置信度
                    "source": f"从 {source_domain} 简单迁移",
                    "timestamp": datetime.datetime.now().isoformat()
                }
                knowledge_id = self._add_to_knowledge_base(knowledge_item)
                transferred_knowledge.append(knowledge_item)
        
        # 记录迁移历史
        transfer_event = {
            "timestamp": datetime.datetime.now().isoformat(),
            "source_domain": source_domain,
            "target_domain": target_domain,
            "knowledge_transferred": len(transferred_knowledge)
        }
        
        self.learning_history.append(transfer_event)
        
        # 保存学习数据
        self._save_learning_data()
        
        return {
            "success": True,
            "source_domain": source_domain,
            "target_domain": target_domain,
            "source_knowledge_count": len(source_knowledge),
            "transferred_knowledge": transferred_knowledge
        }

    # =========== 技能学习功能 ===========
    
    def _update_skills(self, content: str, tags: List[str], learning_modifier: float) -> List[str]:
        """
        更新技能水平
        
        Args:
            content: 经验内容
            tags: 标签列表
            learning_modifier: 学习效果修正系数
            
        Returns:
            更新的技能列表
        """
        updated_skills = []
        
        # 检查内容和标签中的技能相关信息
        potential_skills = []
        
        # 从标签中提取潜在技能
        for tag in tags:
            if "技能" in tag or "学习" in tag or "掌握" in tag or "能力" in tag:
                potential_skills.append(tag)
        
        # 如果没有明确标记为技能的标签，将所有标签视为潜在技能
        if not potential_skills and tags:
            potential_skills = tags
        
        # 更新技能水平
        for skill in potential_skills:
            # 计算技能提升量
            skill_gain = learning_modifier * self.learning_rate
            
            # 如果技能已存在，增加熟练度
            if skill in self.skill_levels:
                old_level = self.skill_levels[skill]["level"]
                # 技能水平接近上限时，提升变慢
                if old_level > 0.8:
                    skill_gain *= (1 - old_level) * 2
                
                # 更新技能水平
                self.skill_levels[skill]["level"] = min(1.0, old_level + skill_gain)
                self.skill_levels[skill]["last_practiced"] = datetime.datetime.now().isoformat()
                self.skill_levels[skill]["practice_count"] += 1
            else:
                # 创建新技能
                self.skill_levels[skill] = {
                    "level": skill_gain,
                    "first_learned": datetime.datetime.now().isoformat(),
                    "last_practiced": datetime.datetime.now().isoformat(),
                    "practice_count": 1,
                    "description": f"从'{content[:50]}...'学习的技能"
                }
            
            updated_skills.append(skill)
        
        return updated_skills
    
    def practice_skill(self, skill: str, practice_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        练习特定技能
        
        Args:
            skill: 技能名称
            practice_data: 练习数据，包含内容、难度等信息
            
        Returns:
            练习结果
        """
        if not skill:
            logger.error_status("技能名称为空")
            return {"success": False, "error": "技能名称为空"}
        
        # 提取练习数据
        content = practice_data.get("content", "")
        difficulty = practice_data.get("difficulty", 0.5)  # 难度系数 (0.1-1.0)
        duration = practice_data.get("duration", 1.0)      # 练习时长 (小时)
        quality = practice_data.get("quality", 0.7)        # 练习质量 (0.1-1.0)
        
        # 获取当前情感状态（如果有情感模块）
        current_emotion = None
        emotional_state = None
        if self.emotion_module:
            try:
                emotional_state = self.emotion_module.get_emotion_state()
                current_emotion = emotional_state.get("current_emotion")
            except Exception as e:
                logger.warning_status(f"获取情感状态失败: {e}")
        
        # 计算情感影响
        emotion_factor = 1.0
        if current_emotion:
            if current_emotion in ["专注", "好奇", "兴奋"]:
                # 这些情感有利于技能学习
                emotion_factor = 1.2
            elif current_emotion in ["焦虑", "恐惧", "愤怒"]:
                # 这些情感不利于技能学习
                emotion_factor = 0.8
        
        # 计算练习效果
        practice_effectiveness = min(1.0, quality * duration * emotion_factor)
        
        # 调整难度对学习的影响
        # 难度应该适中：太容易学习效果低，太难也学习效果低
        difficulty_factor = 1.0
        if difficulty < 0.3:
            # 太容易的练习效果较低
            difficulty_factor = 0.5 + difficulty
        elif difficulty > 0.8:
            # 太难的练习效果下降
            difficulty_factor = 2.2 - difficulty * 1.5
        else:
            # 适中难度效果最佳
            difficulty_factor = 1.0
        
        # 计算最终技能提升量
        skill_gain = practice_effectiveness * difficulty_factor * self.learning_rate
        
        # 更新技能水平
        old_level = 0
        if skill in self.skill_levels:
            old_level = self.skill_levels[skill]["level"]
            # 技能水平接近上限时，提升变慢
            if old_level > 0.8:
                skill_gain *= (1 - old_level) * 2
            
            # 更新技能水平
            new_level = min(1.0, old_level + skill_gain)
            self.skill_levels[skill]["level"] = new_level
            self.skill_levels[skill]["last_practiced"] = datetime.datetime.now().isoformat()
            self.skill_levels[skill]["practice_count"] += 1
            
            # 判断是否达到新的掌握级别
            mastery_level = self._get_mastery_level(old_level)
            new_mastery_level = self._get_mastery_level(new_level)
            
            mastery_improved = new_mastery_level != mastery_level
        else:
            # 创建新技能
            new_level = skill_gain
            self.skill_levels[skill] = {
                "level": new_level,
                "first_learned": datetime.datetime.now().isoformat(),
                "last_practiced": datetime.datetime.now().isoformat(),
                "practice_count": 1,
                "description": f"从'{content[:50]}...'学习的技能"
            }
            
            mastery_improved = new_level >= self.learning_state["mastery_thresholds"]["beginner"]
            new_mastery_level = self._get_mastery_level(new_level)
        
        # 记录练习历史
        practice_event = {
            "timestamp": datetime.datetime.now().isoformat(),
            "skill": skill,
            "content": content[:100] + "..." if len(content) > 100 else content,
            "difficulty": difficulty,
            "quality": quality,
            "duration": duration,
            "emotion": current_emotion,
            "skill_gain": skill_gain,
            "new_level": new_level
        }
        
        self.learning_history.append(practice_event)
        
        # 情感反馈（如果有情感模块）
        if self.emotion_module and mastery_improved:
            try:
                # 提升掌握级别时产生积极情感
                self.emotion_module.update_emotion("成就感", 0.7, f"提升了{skill}技能到{new_mastery_level}级别")
            except Exception as e:
                logger.warning_status(f"更新情感状态失败: {e}")
        
        # 保存学习数据
        self._save_learning_data()
        
        return {
            "success": True,
            "skill": skill,
            "old_level": old_level,
            "new_level": new_level,
            "skill_gain": skill_gain,
            "mastery_level": self._get_mastery_level(new_level),
            "mastery_improved": mastery_improved,
            "practice_effectiveness": practice_effectiveness,
            "emotional_state": emotional_state
        }
    
    def _get_mastery_level(self, skill_level: float) -> str:
        """根据技能水平获取掌握级别"""
        thresholds = self.learning_state["mastery_thresholds"]
        
        if skill_level >= thresholds["expert"]:
            return "专家"
        elif skill_level >= thresholds["advanced"]:
            return "高级"
        elif skill_level >= thresholds["intermediate"]:
            return "中级"
        elif skill_level >= thresholds["beginner"]:
            return "初学者"
        else:
            return "未掌握"
    
    def decay_skills(self) -> Dict[str, Any]:
        """
        技能衰减
        
        随时间降低未练习技能的水平
        
        Returns:
            衰减结果
        """
        now = datetime.datetime.now()
        decayed_skills = []
        
        for skill, data in self.skill_levels.items():
            # 解析上次练习时间
            try:
                last_practiced = datetime.datetime.fromisoformat(data["last_practiced"])
                days_since_practice = (now - last_practiced).days
                
                # 超过一定时间未练习的技能开始衰减
                if days_since_practice > 7:  # 一周未练习开始衰减
                    # 计算衰减量（越久未练习衰减越快）
                    decay_rate = self.retention_decay * (1 + days_since_practice / 30)
                    
                    # 技能水平越高，衰减越慢
                    level_factor = 1 - data["level"] * 0.5  # 0.5-1.0
                    
                    # 计算最终衰减量
                    decay_amount = decay_rate * level_factor
                    
                    # 应用衰减
                    old_level = data["level"]
                    new_level = max(0.1, old_level - decay_amount)  # 不会完全忘记
                    
                    # 更新技能水平
                    self.skill_levels[skill]["level"] = new_level
                    
                    decayed_skills.append({
                        "skill": skill,
                        "old_level": old_level,
                        "new_level": new_level,
                        "days_since_practice": days_since_practice
                    })
            except (ValueError, KeyError) as e:
                logger.warning_status(f"技能'{skill}'数据格式错误: {e}")
        
        # 如果有技能衰减，保存学习数据
        if decayed_skills:
            self._save_learning_data()
        
        return {
            "success": True,
            "decayed_skills_count": len(decayed_skills),
            "decayed_skills": decayed_skills
        }
    
    def get_skill_info(self, skill: Optional[str] = None) -> Dict[str, Any]:
        """
        获取技能信息
        
        Args:
            skill: 技能名称，如果为None则返回所有技能
            
        Returns:
            技能信息
        """
        if skill:
            if skill in self.skill_levels:
                skill_data = self.skill_levels[skill].copy()
                skill_data["mastery_level"] = self._get_mastery_level(skill_data["level"])
                return {"success": True, "skill": skill, "data": skill_data}
            else:
                return {"success": False, "error": f"技能'{skill}'不存在"}
        else:
            # 返回所有技能信息
            all_skills = {}
            for s, data in self.skill_levels.items():
                skill_data = data.copy()
                skill_data["mastery_level"] = self._get_mastery_level(data["level"])
                all_skills[s] = skill_data
            
            # 按技能水平排序
            sorted_skills = sorted(all_skills.items(), key=lambda x: x[1]["level"], reverse=True)
            
            return {
                "success": True,
                "skills_count": len(all_skills),
                "skills": dict(sorted_skills)
            }
    
    # =========== 概念形成功能 ===========
    
    def _form_concepts(self, content: str, tags: List[str], learning_modifier: float) -> List[str]:
        """
        形成和完善概念
        
        Args:
            content: 经验内容
            tags: 标签列表
            learning_modifier: 学习效果修正系数
            
        Returns:
            形成的概念列表
        """
        formed_concepts = []
        
        # 使用标签作为基本概念
        for tag in tags:
            concept_id = self._process_concept(tag, content, learning_modifier)
            if concept_id:
                formed_concepts.append(concept_id)
        
        # 使用推理模块提取更复杂的概念（如果可用）
        if self.reasoning_module and content:
            try:
                # 准备推理查询
                query = {"problem": f"从以下内容中提取关键概念及其含义：\n{content[:500]}"}
                
                # 执行思维链推理
                reasoning_result = self.reasoning_module.reason("思维链", query)
                
                if reasoning_result["success"]:
                    # 从推理结果中提取概念
                    analysis_points = reasoning_result["result"]["analysis"]
                    
                    for point in analysis_points:
                        if isinstance(point, str) and ":" in point:
                            # 尝试提取概念名称和描述
                            parts = point.split(":", 1)
                            if len(parts) == 2:
                                concept_name = parts[0].strip()
                                concept_desc = parts[1].strip()
                                
                                if concept_name and concept_desc:
                                    # 处理提取的概念
                                    concept_id = self._process_concept(
                                        concept_name, concept_desc, 
                                        learning_modifier, source="推理提取"
                                    )
                                    if concept_id:
                                        formed_concepts.append(concept_id)
            except Exception as e:
                logger.warning_status(f"使用推理模块提取概念失败: {e}")
        
        return formed_concepts
    
    def _process_concept(self, concept_name: str, content: str, learning_modifier: float, 
                        source: str = "经验学习") -> Optional[str]:
        """
        处理单个概念
        
        如果概念已存在，更新其内容和关联；如果不存在，创建新概念
        
        Args:
            concept_name: 概念名称
            content: 概念相关内容
            learning_modifier: 学习效果修正系数
            source: 概念来源
            
        Returns:
            概念ID
        """
        if not concept_name or not content:
            return None
        
        # 检查概念是否已存在
        if concept_name in self.concept_relations:
            # 更新已有概念
            concept = self.concept_relations[concept_name]
            
            # 增加熟悉度
            old_familiarity = concept["familiarity"]
            concept["familiarity"] = min(1.0, old_familiarity + learning_modifier * 0.2)
            
            # 更新最后遇到时间
            concept["last_encountered"] = datetime.datetime.now().isoformat()
            concept["encounter_count"] += 1
            
            # 添加新例子（如果与现有例子不太相似）
            if "examples" in concept:
                # 简单的重复检测（实际上应该用更复杂的相似度检测）
                is_new = True
                for example in concept["examples"]:
                    if content[:50] == example[:50]:  # 简单检查前50个字符
                        is_new = False
                        break
                
                if is_new:
                    # 限制例子数量
                    if len(concept["examples"]) >= 5:
                        concept["examples"].pop(0)  # 移除最旧的例子
                    concept["examples"].append(content[:200])  # 限制长度
            
            concept_id = concept_name
        else:
            # 创建新概念
            concept = {
                "name": concept_name,
                "description": content[:200],  # 限制描述长度
                "familiarity": learning_modifier * 0.3,  # 初始熟悉度
                "first_encountered": datetime.datetime.now().isoformat(),
                "last_encountered": datetime.datetime.now().isoformat(),
                "encounter_count": 1,
                "source": source,
                "examples": [content[:200]],  # 第一个例子
                "related_concepts": [],
                "attributes": {}
            }
            
            self.concept_relations[concept_name] = concept
            concept_id = concept_name
            
            # 尝试将新概念与现有概念关联
            self._relate_new_concept(concept_name, content)
        
        return concept_id
    
    def _relate_new_concept(self, concept_name: str, content: str) -> None:
        """
        将新概念与现有概念关联
        
        Args:
            concept_name: 新概念名称
            content: 概念相关内容
        """
        # 简单的词汇匹配方法（实际应该使用更复杂的语义分析）
        for existing_name, existing_concept in self.concept_relations.items():
            if existing_name == concept_name:
                continue
            
            # 检查名称包含关系
            if (concept_name in existing_name or existing_name in concept_name) and \
               len(concept_name) > 2 and len(existing_name) > 2:
                # 添加关联
                if existing_name not in self.concept_relations[concept_name]["related_concepts"]:
                    self.concept_relations[concept_name]["related_concepts"].append(existing_name)
                
                if concept_name not in existing_concept["related_concepts"]:
                    existing_concept["related_concepts"].append(concept_name)
            
            # 检查内容匹配
            elif existing_name in content or existing_concept["description"] in content:
                # 添加关联
                if existing_name not in self.concept_relations[concept_name]["related_concepts"]:
                    self.concept_relations[concept_name]["related_concepts"].append(existing_name)
                
                if concept_name not in existing_concept["related_concepts"]:
                    existing_concept["related_concepts"].append(concept_name)
    
    def explore_concept(self, concept_name: str) -> Dict[str, Any]:
        """
        探索概念
        
        获取概念的详细信息和关联概念
        
        Args:
            concept_name: 概念名称
            
        Returns:
            概念探索结果
        """
        if not concept_name:
            logger.error_status("概念名称为空")
            return {"success": False, "error": "概念名称为空"}
        
        if concept_name not in self.concept_relations:
            logger.warning_status(f"概念'{concept_name}'不存在")
            return {"success": False, "error": f"概念'{concept_name}'不存在"}
        
        # 获取概念信息
        concept = self.concept_relations[concept_name]
        
        # 获取关联概念
        related_concepts = []
        for related_name in concept.get("related_concepts", []):
            if related_name in self.concept_relations:
                related_concepts.append({
                    "name": related_name,
                    "description": self.concept_relations[related_name].get("description", ""),
                    "familiarity": self.concept_relations[related_name].get("familiarity", 0)
                })
        
        # 使用推理模块深入分析概念（如果可用）
        concept_analysis = None
        if self.reasoning_module:
            try:
                # 准备推理查询
                query = {
                    "problem": f"分析以下概念及其特性：\n概念名称：{concept_name}\n描述：{concept['description']}\n例子：{concept.get('examples', [])}"
                }
                
                # 执行思维链推理
                reasoning_result = self.reasoning_module.reason("思维链", query)
                
                if reasoning_result["success"]:
                    concept_analysis = {
                        "analysis_points": reasoning_result["result"]["analysis"],
                        "conclusion": reasoning_result["result"]["conclusion"]
                    }
            except Exception as e:
                logger.warning_status(f"使用推理模块分析概念失败: {e}")
        
        # 每次探索概念，增加一点熟悉度
        self.concept_relations[concept_name]["familiarity"] = min(
            1.0, self.concept_relations[concept_name]["familiarity"] + 0.05
        )
        self.concept_relations[concept_name]["last_encountered"] = datetime.datetime.now().isoformat()
        self.concept_relations[concept_name]["encounter_count"] += 1
        
        # 保存学习数据
        self._save_learning_data()
        
        return {
            "success": True,
            "concept": concept,
            "related_concepts": related_concepts,
            "analysis": concept_analysis
        }
    
    def find_concept_network(self, concept_name: str, depth: int = 2) -> Dict[str, Any]:
        """
        查找概念网络
        
        探索概念及其关联概念形成的网络
        
        Args:
            concept_name: 起始概念名称
            depth: 探索深度
            
        Returns:
            概念网络结果
        """
        if not concept_name:
            logger.error_status("概念名称为空")
            return {"success": False, "error": "概念名称为空"}
        
        if concept_name not in self.concept_relations:
            logger.warning_status(f"概念'{concept_name}'不存在")
            return {"success": False, "error": f"概念'{concept_name}'不存在"}
        
        # 广度优先搜索概念网络
        network = {}
        queue = [(concept_name, 0)]  # (概念名称, 深度)
        visited = set()
        
        while queue:
            current_name, current_depth = queue.pop(0)
            
            if current_name in visited:
                continue
                
            visited.add(current_name)
            
            if current_name in self.concept_relations:
                concept = self.concept_relations[current_name]
                
                # 添加到网络
                network[current_name] = {
                    "name": current_name,
                    "description": concept.get("description", ""),
                    "familiarity": concept.get("familiarity", 0),
                    "depth": current_depth,
                    "connections": concept.get("related_concepts", [])
                }
                
                # 如果未达到最大深度，继续探索关联概念
                if current_depth < depth:
                    for related_name in concept.get("related_concepts", []):
                        if related_name not in visited:
                            queue.append((related_name, current_depth + 1))
        
        return {
            "success": True,
            "central_concept": concept_name,
            "network_size": len(network),
            "network": network
        }
    
    # =========== 通用功能 ===========
    
    def get_learning_state(self) -> Dict[str, Any]:
        """
        获取学习状态
        
        Returns:
            学习状态信息
        """
        return {
            "active_learning": self.learning_state["active_learning"],
            "learning_focus": self.learning_state["learning_focus"],
            "curiosity_level": self.learning_state["curiosity_level"],
            "knowledge_count": len(self.knowledge_base),
            "skills_count": len(self.skill_levels),
            "concepts_count": len(self.concept_relations),
            "learning_rate": self.learning_rate,
            "exploration_rate": self.exploration_rate
        }
    
    def set_learning_focus(self, focus: Optional[str] = None) -> Dict[str, Any]:
        """
        设置学习焦点
        
        Args:
            focus: 学习焦点，如果为None则清除焦点
            
        Returns:
            设置结果
        """
        old_focus = self.learning_state["learning_focus"]
        self.learning_state["learning_focus"] = focus
        
        return {
            "success": True,
            "old_focus": old_focus,
            "new_focus": focus
        }
    
    def set_active_learning(self, active: bool = True) -> Dict[str, Any]:
        """
        设置主动学习状态
        
        Args:
            active: 是否启用主动学习
            
        Returns:
            设置结果
        """
        old_state = self.learning_state["active_learning"]
        self.learning_state["active_learning"] = active
        
        return {
            "success": True,
            "old_state": old_state,
            "new_state": active
        }
    
    def adjust_learning_parameters(self, parameters: Dict[str, float]) -> Dict[str, Any]:
        """
        调整学习参数
        
        Args:
            parameters: 要调整的参数字典
            
        Returns:
            调整结果
        """
        changes = {}
        
        if "learning_rate" in parameters:
            value = parameters["learning_rate"]
            if 0.01 <= value <= 1.0:
                changes["learning_rate"] = {"old": self.learning_rate, "new": value}
                self.learning_rate = value
        
        if "exploration_rate" in parameters:
            value = parameters["exploration_rate"]
            if 0.0 <= value <= 1.0:
                changes["exploration_rate"] = {"old": self.exploration_rate, "new": value}
                self.exploration_rate = value
        
        if "memory_impact" in parameters:
            value = parameters["memory_impact"]
            if 0.0 <= value <= 1.0:
                changes["memory_impact"] = {"old": self.memory_impact, "new": value}
                self.memory_impact = value
        
        if "emotion_impact" in parameters:
            value = parameters["emotion_impact"]
            if 0.0 <= value <= 1.0:
                changes["emotion_impact"] = {"old": self.emotion_impact, "new": value}
                self.emotion_impact = value
        
        if "retention_decay" in parameters:
            value = parameters["retention_decay"]
            if 0.0 <= value <= 0.5:
                changes["retention_decay"] = {"old": self.retention_decay, "new": value}
                self.retention_decay = value
        
        if "curiosity_level" in parameters:
            value = parameters["curiosity_level"]
            if 0.0 <= value <= 1.0:
                changes["curiosity_level"] = {"old": self.learning_state["curiosity_level"], "new": value}
                self.learning_state["curiosity_level"] = value
        
        return {
            "success": True,
            "changes": changes
        }
    
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化或重新初始化模块
        
        Args:
            config: 新的配置信息
            
        Returns:
            初始化是否成功
        """
        if config:
            self.config = config
            
            # 更新配置项
            self.learning_rate = self.config.get('learning_rate', self.learning_rate)
            self.exploration_rate = self.config.get('exploration_rate', self.exploration_rate)
            self.memory_impact = self.config.get('memory_impact', self.memory_impact)
            self.emotion_impact = self.config.get('emotion_impact', self.emotion_impact)
            self.retention_decay = self.config.get('retention_decay', self.retention_decay)
            
            # 重新初始化模块连接
            self._initialize_module_connections()
            
            logger.success("学习机制模块重新初始化完成")
            
        return True
    
    def shutdown(self) -> bool:
        """
        关闭模块，执行必要的清理工作
        
        Returns:
            关闭是否成功
        """
        # 保存学习数据
        self._save_learning_data()
        
        logger.info("学习机制模块已关闭")
        return True

# 模块单例访问函数
def get_instance(identity: str = None, config: Dict[str, Any] = None) -> Learning:
    """
    获取学习机制模块的单例实例
    
    Args:
        identity: 数字生命体标识
        config: 配置信息
        
    Returns:
        学习机制模块实例
    """
    return Learning(identity, config)