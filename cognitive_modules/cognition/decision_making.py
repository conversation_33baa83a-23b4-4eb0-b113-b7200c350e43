#!/usr/bin/env python3
"""
决策模块 - Decision Making Module

负责数字生命体的决策过程，根据输入情境和历史记忆提供行动建议和选择方案。

作者: Claude
创建日期: 2025-06-11
版本: 1.0
"""

import os
import sys
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random
import time
from typing import Dict, List, Any, Optional, Tuple, Set

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(os.path.dirname(current_dir))
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入模块
from cognitive_modules.base.module_base import CognitionModule
from middleware.event_system import EventSystem, get_instance as get_event_bus

# 配置日志
setup_unified_logging()
logger = get_unified_logger("cognition.decision_making")

class DecisionMaking(CognitionModule):
    """
    决策模块
    
    负责数字生命体的决策过程。
    """
    
    def __init__(self):
        """
        初始化决策模块
        """
        super().__init__(module_type="cognition", module_name="decision_making")
        self.event_bus = None
        self.decision_history = []
        self.decision_strategies = {}
        self.current_context = {}
        self.config_path = None
        
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化模块
        
        Args:
            config: 配置信息
            
        Returns:
            初始化是否成功
        """
        # 调用父类初始化
        super().initialize(config)
        
        try:
            # 获取配置
            self.config_path = config.get("config_path", "config/decision_making.json")
            
            # 获取事件总线
            self.event_bus = get_event_bus()
            
            # 注册事件处理器
            if self.event_bus:
                self.event_bus.subscribe("context_updated", self._handle_context_update)
                self.event_bus.subscribe("request_decision", self._handle_decision_request)
            
            # 加载决策策略
            self._load_strategies()
            
            logger.success("决策模块初始化完成")
            return True
            
        except Exception as e:
            logger.error_status(f"初始化决策模块失败: {e}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            return False
            
    def _process_implementation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        operation = input_data.get("operation")
        
        if operation == "make_decision":
            return self._make_decision(input_data)
        elif operation == "update_context":
            return self._update_context(input_data)
        elif operation == "get_history":
            return self._get_decision_history(input_data)
        elif operation == "get_strategies":
            return self._get_strategies()
        else:
            return {
                "status": "error",
                "message": f"未知操作: {operation}"
            }
            
    def _make_decision(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成决策
        
        Args:
            input_data: 输入数据
            
        Returns:
            决策结果
        """
        scenario = input_data.get("scenario", {})
        options = input_data.get("options", [])
        strategy_id = input_data.get("strategy", "default")
        context = input_data.get("context", {})
        
        # 合并上下文
        merged_context = self.current_context.copy()
        merged_context.update(context)
        
        # 使用指定的决策策略
        strategy = self.decision_strategies.get(strategy_id)
        if not strategy:
            strategy = self.decision_strategies.get("default")
            if not strategy:
                return {
                    "status": "error",
                    "message": "找不到指定的决策策略",
                    "available_strategies": list(self.decision_strategies.keys())
                }
        
        # 生成决策
        decision_result = self._apply_strategy(strategy, scenario, options, merged_context)
        
        # 记录决策历史
        decision_record = {
            "timestamp": time.time(),
            "scenario": scenario,
            "options": options,
            "strategy": strategy_id,
            "context": merged_context,
            "decision": decision_result
        }
        self.decision_history.append(decision_record)
        
        # 如果历史记录过长，清理旧记录
        if len(self.decision_history) > 100:
            self.decision_history = self.decision_history[-100:]
        
        # 发布决策事件
        if self.event_bus:
            self.event_bus.publish("decision_made", {
                "decision": decision_result,
                "scenario": scenario,
                "options": options,
                "strategy": strategy_id
            })
        
        return {
            "status": "success",
            "decision": decision_result
        }
        
    def _apply_strategy(self, strategy: Dict[str, Any], scenario: Dict[str, Any], options: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用决策策略
        
        Args:
            strategy: 决策策略
            scenario: 决策场景
            options: 决策选项
            context: 决策上下文
            
        Returns:
            决策结果
        """
        strategy_type = strategy.get("type", "random")
        
        if strategy_type == "random":
            # 随机选择
            if options:
                chosen_option = random.choice(options)
                return {
                    "chosen_option": chosen_option,
                    "confidence": random.uniform(0.5, 0.9),
                    "reasoning": "随机选择"
                }
            else:
                return {
                    "action": "no_action",
                    "confidence": 0.5,
                    "reasoning": "没有可用选项"
                }
        
        elif strategy_type == "weighted":
            # 带权重的选择
            if not options:
                return {
                    "action": "no_action",
                    "confidence": 0.5,
                    "reasoning": "没有可用选项"
                }
                
            # 计算每个选项的权重
            weighted_options = []
            for option in options:
                weight = option.get("weight", 1.0)
                
                # 应用上下文修饰符
                for modifier in strategy.get("modifiers", []):
                    condition = modifier.get("condition", "")
                    if self._evaluate_condition(condition, context):
                        if option.get("tags", []) and set(option.get("tags", [])).intersection(set(modifier.get("apply_to_tags", []))):
                            weight *= modifier.get("weight_multiplier", 1.0)
                
                weighted_options.append((option, weight))
                
            # 按权重选择
            total_weight = sum(weight for _, weight in weighted_options)
            if total_weight <= 0:
                # 如果总权重为零，则随机选择
                chosen_option = random.choice(options)
                return {
                    "chosen_option": chosen_option,
                    "confidence": 0.5,
                    "reasoning": "权重总和为零，随机选择"
                }
                
            # 按权重概率选择
            selection = random.uniform(0, total_weight)
            current_weight = 0
            for option, weight in weighted_options:
                current_weight += weight
                if current_weight >= selection:
                    confidence = weight / total_weight
                    return {
                        "chosen_option": option,
                        "confidence": confidence,
                        "reasoning": f"根据权重 {weight:.2f}/{total_weight:.2f} 选择"
                    }
            
            # 防止浮点误差导致未选择
            chosen_option = weighted_options[-1][0]
            return {
                "chosen_option": chosen_option,
                "confidence": weighted_options[-1][1] / total_weight,
                "reasoning": "权重选择（默认值）"
            }
            
        elif strategy_type == "rule_based":
            # 基于规则的选择
            rules = strategy.get("rules", [])
            
            for rule in rules:
                condition = rule.get("condition", "")
                if self._evaluate_condition(condition, context):
                    action = rule.get("action", {})
                    
                    if action.get("type") == "select_option":
                        # 选择指定的选项
                        option_index = action.get("option_index", 0)
                        if 0 <= option_index < len(options):
                            chosen_option = options[option_index]
                            return {
                                "chosen_option": chosen_option,
                                "confidence": action.get("confidence", 0.8),
                                "reasoning": action.get("reasoning", "根据规则选择")
                            }
                    elif action.get("type") == "select_by_tag":
                        # 根据标签选择选项
                        tags = action.get("tags", [])
                        matching_options = [
                            option for option in options
                            if set(option.get("tags", [])).intersection(set(tags))
                        ]
                        
                        if matching_options:
                            chosen_option = random.choice(matching_options)
                            return {
                                "chosen_option": chosen_option,
                                "confidence": action.get("confidence", 0.8),
                                "reasoning": action.get("reasoning", "根据标签选择")
                            }
                    elif action.get("type") == "custom_action":
                        # 自定义行动
                        return {
                            "action": action.get("action_name", "custom"),
                            "parameters": action.get("parameters", {}),
                            "confidence": action.get("confidence", 0.8),
                            "reasoning": action.get("reasoning", "自定义行动")
                        }
            
            # 如果没有匹配的规则，使用默认行动
            if options:
                chosen_option = random.choice(options)
                return {
                    "chosen_option": chosen_option,
                    "confidence": 0.5,
                    "reasoning": "没有匹配的规则，随机选择"
                }
            else:
                return {
                    "action": "no_action",
                    "confidence": 0.5,
                    "reasoning": "没有可用选项且没有匹配的规则"
                }
                
        else:
            # 未知策略类型
            return {
                "action": "no_action",
                "confidence": 0.5,
                "reasoning": f"未知的策略类型: {strategy_type}"
            }
            
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """
        评估条件表达式
        
        简单实现，仅支持基本条件表达式
        
        Args:
            condition: 条件表达式
            context: 上下文
            
        Returns:
            条件是否满足
        """
        if not condition:
            return True
            
        try:
            # 支持简单的条件表达式，格式如：emotion.joy > 0.5
            if " > " in condition:
                key, value = condition.split(" > ")
                value = float(value)
                
                # 获取上下文值
                context_value = self._get_context_value(key, context)
                return context_value > value
                
            elif " < " in condition:
                key, value = condition.split(" < ")
                value = float(value)
                
                # 获取上下文值
                context_value = self._get_context_value(key, context)
                return context_value < value
                
            elif " == " in condition:
                key, value = condition.split(" == ")
                
                # 尝试转换为数字
                try:
                    value = float(value)
                except ValueError:
                    # 如果不是数字，则保持字符串
                    pass
                
                # 获取上下文值
                context_value = self._get_context_value(key, context)
                return context_value == value
                
            elif " in " in condition:
                key, values = condition.split(" in ")
                values = [v.strip() for v in values.strip('[]').split(',')]
                
                # 获取上下文值
                context_value = self._get_context_value(key, context)
                return str(context_value) in values
                
            else:
                # 不支持的条件
                logger.warning_status(f"不支持的条件表达式: {condition}")
                return False
                
        except Exception as e:
            logger.error_status(f"评估条件表达式失败: {condition}, 错误: {e}")
            return False
            
    def _get_context_value(self, key: str, context: Dict[str, Any]) -> Any:
        """
        获取上下文中的值
        
        支持嵌套键，如 emotion.joy
        
        Args:
            key: 键名
            context: 上下文
            
        Returns:
            上下文值
        """
        if "." in key:
            # 嵌套键
            parts = key.split(".")
            current = context
            
            for part in parts:
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return None
                    
            return current
        else:
            # 简单键
            return context.get(key)
            
    def _update_context(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新上下文
        
        Args:
            input_data: 输入数据
            
        Returns:
            更新结果
        """
        context = input_data.get("context", {})
        
        if not context:
            return {
                "status": "error",
                "message": "上下文不能为空"
            }
            
        # 更新上下文
        self.current_context.update(context)
        
        return {
            "status": "success",
            "context": self.current_context
        }
        
    def _get_decision_history(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取决策历史
        
        Args:
            input_data: 输入数据
            
        Returns:
            决策历史
        """
        limit = input_data.get("limit", 10)
        
        return {
            "status": "success",
            "history": self.decision_history[-limit:]
        }
        
    def _get_strategies(self) -> Dict[str, Any]:
        """
        获取可用的决策策略
        
        Returns:
            决策策略
        """
        return {
            "status": "success",
            "strategies": self.decision_strategies
        }
        
    def _load_strategies(self) -> bool:
        """
        加载决策策略
        
        Returns:
            是否加载成功
        """
        try:
            # 检查配置文件是否存在
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.decision_strategies = config.get("strategies", {})
            else:
                # 使用默认策略
                self.decision_strategies = {
                    "default": {
                        "type": "random",
                        "description": "随机选择策略"
                    },
                    "weighted": {
                        "type": "weighted",
                        "description": "带权重的选择策略",
                        "modifiers": []
                    },
                    "rule_based": {
                        "type": "rule_based",
                        "description": "基于规则的选择策略",
                        "rules": []
                    }
                }
                
                # 保存默认策略
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump({"strategies": self.decision_strategies}, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已加载 {len(self.decision_strategies)} 个决策策略")
            return True
            
        except Exception as e:
            logger.error_status(f"加载决策策略失败: {e}")
            return False
            
    def _handle_context_update(self, event_data: Dict[str, Any]):
        """
        处理上下文更新事件
        
        Args:
            event_data: 事件数据
        """
        context = event_data.get("context", {})
        
        if context:
            self.current_context.update(context)
            
    def _handle_decision_request(self, event_data: Dict[str, Any]):
        """
        处理决策请求事件
        
        Args:
            event_data: 事件数据
        """
        result = self._make_decision(event_data)
        
        # 发布决策结果事件
        if self.event_bus:
            self.event_bus.publish("decision_result", {
                "request_id": event_data.get("request_id"),
                "result": result
            })
            
    def shutdown(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        # 取消事件订阅
        if self.event_bus:
            self.event_bus.unsubscribe("context_updated", self._handle_context_update)
            self.event_bus.unsubscribe("request_decision", self._handle_decision_request)
        
        # 调用父类关闭方法
        return super().shutdown()
        
# 获取模块实例的工厂函数
def get_instance(config: Dict[str, Any] = None) -> DecisionMaking:
    """
    获取模块实例
    
    Args:
        config: 配置信息
        
    Returns:
        模块实例
    """
    module = DecisionMaking()
    module.initialize(config)
    return module

if __name__ == "__main__":
    # 模块测试
    decision_module = get_instance()
    decision_module.initialize()
    
    # 测试决策
    test_options = [
        {
            "id": "option1",
            "title": "选项1 - 保守方案",
            "description": "这是一个低风险、低回报的选项",
            "risk": 0.2,
            "utility": 0.4,
            "emotional_tone": "平静",
            "tags": ["保守", "安全", "稳定"]
        },
        {
            "id": "option2",
            "title": "选项2 - 激进方案",
            "description": "这是一个高风险、高回报的选项",
            "risk": 0.7,
            "utility": 0.8,
            "emotional_tone": "激动",
            "tags": ["激进", "创新", "冒险"]
        },
        {
            "id": "option3",
            "title": "选项3 - 平衡方案",
            "description": "这是一个中等风险、中等回报的选项",
            "risk": 0.4,
            "utility": 0.6,
            "emotional_tone": "好奇",
            "tags": ["平衡", "探索", "学习"]
        }
    ]
    
    print("理性决策:")
    result = decision_module.decide(test_options, {}, "rational")
    print(f"选择: {result['decision']['title']}")
    
    print("\n情感决策:")
    result = decision_module.decide(test_options, {}, "emotional")
    print(f"选择: {result['decision']['title']}")
    
    print("\n直觉决策:")
    result = decision_module.decide(test_options, {}, "intuitive")
    print(f"选择: {result['decision']['title']}")
    
    print("\n基于价值观决策:")
    result = decision_module.decide(test_options, {}, "value_based")
    print(f"选择: {result['decision']['title']}")
    
    decision_module.shutdown() 