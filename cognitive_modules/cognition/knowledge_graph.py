#!/usr/bin/env python3
"""
知识图谱模块 - Knowledge Graph Module

负责构建和维护数字生命体的知识图谱，支持知识的存储、检索和推理。

作者: <PERSON>
创建日期: 2025-06-11
版本: 1.0
"""

import os
import sys
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import uuid
from typing import Dict, List, Any, Optional, Set, Tuple

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(os.path.dirname(current_dir))
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入模块
from utilities.vector_store import VectorStore, create_vector_store
from cognitive_modules.base.module_base import CognitionModule

# 配置日志
setup_unified_logging()
logger = get_unified_logger("cognition.knowledge_graph")

class KnowledgeGraph(CognitionModule):
    """
    知识图谱模块
    
    负责构建、维护和查询知识图谱。
    """
    
    def __init__(self):
        """
        初始化知识图谱模块
        """
        super().__init__(module_type="cognition", module_name="knowledge_graph")
        self.vector_store = None
        self.entities = {}  # entity_id -> entity
        self.relations = {}  # relation_id -> relation
        self.triples = []  # [(subject_id, predicate_id, object_id), ...]
        self.config_path = None
        self.data_path = None
        
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化模块
        
        Args:
            config: 配置信息
            
        Returns:
            初始化是否成功
        """
        # 调用父类初始化
        super().initialize(config)
        
        try:
            # 🔥 香草修复：处理config为None的情况
            if config is None:
                config = {}

            # 获取配置
            self.config_path = config.get("config_path", "config/knowledge_graph.json")
            self.data_path = config.get("data_path", "data/knowledge_graph")
            vector_dimension = config.get("vector_dimension", 768)
            
            # 确保数据目录存在
            os.makedirs(self.data_path, exist_ok=True)
            
            # 初始化向量存储
            vector_store_path = os.path.join(self.data_path, "vectors.json")
            self.vector_store = create_vector_store(vector_store_path, vector_dimension)
            
            # 加载实体和关系
            self._load_data()
            
            logger.success(f"知识图谱模块初始化完成，实体数量: {len(self.entities)}，关系数量: {len(self.relations)}，三元组数量: {len(self.triples)}")
            return True
            
        except Exception as e:
            logger.error_status(f"初始化知识图谱模块失败: {e}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            return False
            
    def _process_implementation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        operation = input_data.get("operation")
        
        if operation == "query":
            return self._handle_query(input_data)
        elif operation == "add_entity":
            return self._handle_add_entity(input_data)
        elif operation == "add_relation":
            return self._handle_add_relation(input_data)
        elif operation == "add_triple":
            return self._handle_add_triple(input_data)
        elif operation == "get_entity":
            return self._handle_get_entity(input_data)
        elif operation == "get_relation":
            return self._handle_get_relation(input_data)
        elif operation == "find_path":
            return self._handle_find_path(input_data)
        elif operation == "semantic_search":
            return self._handle_semantic_search(input_data)
        else:
            return {
                "status": "error",
                "message": f"未知操作: {operation}"
            }
            
    def _handle_query(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理查询操作
        
        Args:
            input_data: 查询参数
            
        Returns:
            查询结果
        """
        query_type = input_data.get("query_type", "entity")
        query = input_data.get("query", "")
        
        if query_type == "entity":
            # 根据名称查找实体
            entities = self._find_entities_by_name(query)
            return {
                "status": "success",
                "entities": entities
            }
        elif query_type == "relation":
            # 根据名称查找关系
            relations = self._find_relations_by_name(query)
            return {
                "status": "success",
                "relations": relations
            }
        elif query_type == "triple":
            # 查找包含指定实体的三元组
            subject_id = input_data.get("subject_id")
            predicate_id = input_data.get("predicate_id")
            object_id = input_data.get("object_id")
            
            triples = self._find_triples(subject_id, predicate_id, object_id)
            
            return {
                "status": "success",
                "triples": triples
            }
        else:
            return {
                "status": "error",
                "message": f"未知查询类型: {query_type}"
            }
            
    def _handle_add_entity(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理添加实体操作
        
        Args:
            input_data: 实体数据
            
        Returns:
            添加结果
        """
        name = input_data.get("name")
        type = input_data.get("type", "general")
        properties = input_data.get("properties", {})
        vector = input_data.get("vector")
        
        if not name:
            return {
                "status": "error",
                "message": "实体名称不能为空"
            }
            
        # 生成ID
        entity_id = input_data.get("id") or f"entity_{uuid.uuid4().hex[:8]}"
        
        # 添加实体
        self.entities[entity_id] = {
            "id": entity_id,
            "name": name,
            "type": type,
            "properties": properties
        }
        
        # 如果提供了向量，添加到向量存储
        if vector and self.vector_store:
            self.vector_store.add(
                entity_id,
                vector,
                {
                    "name": name,
                    "type": type,
                    "entity_type": "entity"
                }
            )
            
        # 保存数据
        self._save_data()
        
        return {
            "status": "success",
            "entity_id": entity_id
        }
        
    def _handle_add_relation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理添加关系操作
        
        Args:
            input_data: 关系数据
            
        Returns:
            添加结果
        """
        name = input_data.get("name")
        properties = input_data.get("properties", {})
        
        if not name:
            return {
                "status": "error",
                "message": "关系名称不能为空"
            }
            
        # 生成ID
        relation_id = input_data.get("id") or f"relation_{uuid.uuid4().hex[:8]}"
        
        # 添加关系
        self.relations[relation_id] = {
            "id": relation_id,
            "name": name,
            "properties": properties
        }
        
        # 保存数据
        self._save_data()
        
        return {
            "status": "success",
            "relation_id": relation_id
        }
        
    def _handle_add_triple(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理添加三元组操作
        
        Args:
            input_data: 三元组数据
            
        Returns:
            添加结果
        """
        subject_id = input_data.get("subject_id")
        predicate_id = input_data.get("predicate_id")
        object_id = input_data.get("object_id")
        
        if not subject_id or not predicate_id or not object_id:
            return {
                "status": "error",
                "message": "三元组的主体、谓语和客体ID不能为空"
            }
            
        # 检查实体和关系是否存在
        if subject_id not in self.entities:
            return {
                "status": "error",
                "message": f"主体实体不存在: {subject_id}"
            }
            
        if predicate_id not in self.relations:
            return {
                "status": "error",
                "message": f"谓语关系不存在: {predicate_id}"
            }
            
        if object_id not in self.entities:
            return {
                "status": "error",
                "message": f"客体实体不存在: {object_id}"
            }
            
        # 添加三元组
        triple = (subject_id, predicate_id, object_id)
        
        # 检查是否已存在
        if triple in self.triples:
            return {
                "status": "warning",
                "message": "三元组已存在",
                "triple": triple
            }
            
        self.triples.append(triple)
        
        # 保存数据
        self._save_data()
        
        return {
            "status": "success",
            "triple": triple
        }
        
    def _handle_get_entity(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理获取实体操作
        
        Args:
            input_data: 查询参数
            
        Returns:
            实体数据
        """
        entity_id = input_data.get("entity_id")
        
        if not entity_id:
            return {
                "status": "error",
                "message": "实体ID不能为空"
            }
            
        entity = self.entities.get(entity_id)
        
        if not entity:
            return {
                "status": "error",
                "message": f"实体不存在: {entity_id}"
            }
            
        # 查找与该实体相关的三元组
        related_triples = self._find_triples(entity_id, None, None) + self._find_triples(None, None, entity_id)
        
        return {
            "status": "success",
            "entity": entity,
            "related_triples": related_triples
        }
        
    def _handle_get_relation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理获取关系操作
        
        Args:
            input_data: 查询参数
            
        Returns:
            关系数据
        """
        relation_id = input_data.get("relation_id")
        
        if not relation_id:
            return {
                "status": "error",
                "message": "关系ID不能为空"
            }
            
        relation = self.relations.get(relation_id)
        
        if not relation:
            return {
                "status": "error",
                "message": f"关系不存在: {relation_id}"
            }
            
        # 查找使用该关系的三元组
        related_triples = self._find_triples(None, relation_id, None)
        
        return {
            "status": "success",
            "relation": relation,
            "related_triples": related_triples
        }
        
    def _handle_find_path(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理查找路径操作
        
        Args:
            input_data: 查询参数
            
        Returns:
            路径数据
        """
        start_id = input_data.get("start_id")
        end_id = input_data.get("end_id")
        max_depth = input_data.get("max_depth", 3)
        
        if not start_id or not end_id:
            return {
                "status": "error",
                "message": "起点和终点ID不能为空"
            }
            
        if start_id not in self.entities:
            return {
                "status": "error",
                "message": f"起点实体不存在: {start_id}"
            }
            
        if end_id not in self.entities:
            return {
                "status": "error",
                "message": f"终点实体不存在: {end_id}"
            }
            
        # 查找路径
        paths = self._find_paths(start_id, end_id, max_depth)
        
        return {
            "status": "success",
            "paths": paths
        }
        
    def _handle_semantic_search(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理语义搜索操作
        
        Args:
            input_data: 查询参数
            
        Returns:
            搜索结果
        """
        query_vector = input_data.get("vector")
        top_k = input_data.get("top_k", 5)
        
        if not query_vector:
            return {
                "status": "error",
                "message": "查询向量不能为空"
            }
            
        if not self.vector_store:
            return {
                "status": "error",
                "message": "向量存储未初始化"
            }
            
        # 执行语义搜索
        results = self.vector_store.search(query_vector, top_k)
        
        # 添加实体信息
        for result in results:
            entity_id = result["id"]
            if entity_id in self.entities:
                result["entity"] = self.entities[entity_id]
                
        return {
            "status": "success",
            "results": results
        }
        
    def _find_entities_by_name(self, name: str) -> List[Dict[str, Any]]:
        """
        根据名称查找实体
        
        Args:
            name: 实体名称
            
        Returns:
            实体列表
        """
        if not name:
            return []
            
        return [
            entity for entity in self.entities.values()
            if name.lower() in entity["name"].lower()
        ]
        
    def _find_relations_by_name(self, name: str) -> List[Dict[str, Any]]:
        """
        根据名称查找关系
        
        Args:
            name: 关系名称
            
        Returns:
            关系列表
        """
        if not name:
            return []
            
        return [
            relation for relation in self.relations.values()
            if name.lower() in relation["name"].lower()
        ]
        
    def _find_triples(self, subject_id: str = None, predicate_id: str = None, object_id: str = None) -> List[Tuple[str, str, str]]:
        """
        查找符合条件的三元组
        
        Args:
            subject_id: 主体ID
            predicate_id: 谓语ID
            object_id: 客体ID
            
        Returns:
            三元组列表
        """
        result = []
        
        for triple in self.triples:
            s, p, o = triple
            
            if subject_id and s != subject_id:
                continue
                
            if predicate_id and p != predicate_id:
                continue
                
            if object_id and o != object_id:
                continue
                
            result.append(triple)
            
        return result
        
    def _find_paths(self, start_id: str, end_id: str, max_depth: int) -> List[List[Tuple[str, str, str]]]:
        """
        查找两个实体之间的路径
        
        Args:
            start_id: 起点实体ID
            end_id: 终点实体ID
            max_depth: 最大深度
            
        Returns:
            路径列表
        """
        if max_depth <= 0:
            return []
            
        # 构建邻接表
        adjacency = {}
        
        for s, p, o in self.triples:
            if s not in adjacency:
                adjacency[s] = []
            adjacency[s].append((p, o))
            
            # 反向边
            if o not in adjacency:
                adjacency[o] = []
            adjacency[o].append((f"inv_{p}", s))
            
        # 使用BFS查找路径
        paths = []
        queue = [(start_id, [])]
        visited = set()
        
        while queue:
            node, path = queue.pop(0)
            
            if node == end_id:
                paths.append(path)
                continue
                
            if node in visited:
                continue
                
            visited.add(node)
            
            if len(path) >= max_depth:
                continue
                
            if node not in adjacency:
                continue
                
            for p, o in adjacency[node]:
                new_path = path + [(node, p, o)]
                queue.append((o, new_path))
                
        return paths
        
    def _load_data(self) -> bool:
        """
        加载数据
        
        Returns:
            是否加载成功
        """
        try:
            # 加载实体
            entities_path = os.path.join(self.data_path, "entities.json")
            if os.path.exists(entities_path):
                with open(entities_path, 'r', encoding='utf-8') as f:
                    self.entities = json.load(f)
                    
            # 加载关系
            relations_path = os.path.join(self.data_path, "relations.json")
            if os.path.exists(relations_path):
                with open(relations_path, 'r', encoding='utf-8') as f:
                    self.relations = json.load(f)
                    
            # 加载三元组
            triples_path = os.path.join(self.data_path, "triples.json")
            if os.path.exists(triples_path):
                with open(triples_path, 'r', encoding='utf-8') as f:
                    self.triples = json.load(f)
                    
            return True
            
        except Exception as e:
            logger.error_status(f"加载数据失败: {e}")
            return False
            
    def _save_data(self) -> bool:
        """
        保存数据
        
        Returns:
            是否保存成功
        """
        try:
            # 确保数据目录存在
            os.makedirs(self.data_path, exist_ok=True)
            
            # 保存实体
            entities_path = os.path.join(self.data_path, "entities.json")
            with open(entities_path, 'w', encoding='utf-8') as f:
                json.dump(self.entities, f, ensure_ascii=False, indent=2)
                
            # 保存关系
            relations_path = os.path.join(self.data_path, "relations.json")
            with open(relations_path, 'w', encoding='utf-8') as f:
                json.dump(self.relations, f, ensure_ascii=False, indent=2)
                
            # 保存三元组
            triples_path = os.path.join(self.data_path, "triples.json")
            with open(triples_path, 'w', encoding='utf-8') as f:
                json.dump(self.triples, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            logger.error_status(f"保存数据失败: {e}")
            return False
            
    def shutdown(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        # 保存数据
        self._save_data()
        
        # 调用父类关闭方法
        return super().shutdown()
        
# 获取模块实例的工厂函数
def get_instance(config: Dict[str, Any] = None) -> KnowledgeGraph:
    """
    获取模块实例
    
    Args:
        config: 配置信息
        
    Returns:
        模块实例
    """
    module = KnowledgeGraph()
    module.initialize(config)
    return module 