#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自我反思模块 (Self Reflection)

该模块负责数字生命体的自我反思和元认知能力，包括自我评估、
行为反思、决策审视和思维过程的元分析。

作者: Claude
创建日期: 2024-07-17
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from datetime import datetime, timedelta

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from utilities.storage_manager import get_instance as get_storage_instance
from core.event_bus import get_instance as get_event_bus
from cognitive_modules.base.module_base import CognitiveModuleBase

# 设置日志
setup_unified_logging()
logger = get_unified_logger("cognition.self_reflection")


class SelfReflection(CognitiveModuleBase):
    """自我反思模块，负责数字生命体的元认知能力"""
    
    _instance = None

    def __init__(self, module_id: str = "self_reflection", config: Dict[str, Any] = None):
        """
        初始化自我反思模块
        
        参数:
            module_id: 模块ID
            config: 配置参数
        """
        super().__init__(module_id, "cognition", config)
        
        # 设置模块描述
        self.description = "自我反思模块 - 提供自我评估和元认知能力"
        
        # 设置依赖模块
        self.dependencies = ["episodic_memory", "semantic_memory", "reasoning"]
        
        # 事件总线
        self.event_bus = None
        
        # 存储管理器
        self.storage = None
        
        # 反思记录
        self.reflection_history = []
        
        # 反思锁
        self.reflection_lock = threading.Lock()
        
        # 上次反思时间
        self.last_reflection_time = 0
        
        # 反思线程
        self.reflection_thread = None
        self.reflection_running = False
        
        # 反思周期（秒）
        self.reflection_interval = 3600  # 默认1小时
        
        # 反思深度（0-1）
        self.reflection_depth = 0.7  # 默认深度
        
        # 反思模式
        self.reflection_modes = {
            "behavior": True,     # 行为反思
            "decision": True,     # 决策反思
            "knowledge": True,    # 知识反思
            "emotion": True,      # 情感反思
            "interaction": True,  # 交互反思
            "bias": True,         # 偏见反思
            "value": True         # 价值观反思
        }
        
        logger.success(f"自我反思模块创建成功 (ID: {self.module_id})")
    
    def _do_initialize(self) -> None:
        """
        执行初始化操作
        """
        logger.success(f"正在初始化自我反思模块 (ID: {self.module_id})...")
        
        # 获取事件总线
        self.event_bus = get_event_bus()
        
        # 获取存储管理器
        self.storage = get_storage_instance()
        
        # 从配置中获取反思参数
        if self.config:
            if "reflection_interval" in self.config:
                self.reflection_interval = self.config["reflection_interval"]
            
            if "reflection_depth" in self.config:
                self.reflection_depth = self.config["reflection_depth"]
            
            if "reflection_modes" in self.config:
                self.reflection_modes.update(self.config["reflection_modes"])
        
        # 加载反思历史
        self._load_reflection_history()
        
        # 订阅事件
        self._subscribe_events()
        
        logger.success(f"自我反思模块初始化成功 (ID: {self.module_id})")
    
    def _do_activate(self) -> None:
        """
        激活模块
        """
        logger.info(f"正在激活自我反思模块 (ID: {self.module_id})...")
        
        # 启动反思线程
        self.reflection_running = True
        self.reflection_thread = threading.Thread(
            target=self._reflection_worker,
            daemon=True
        )
        self.reflection_thread.start()
        
        logger.success(f"自我反思模块激活成功 (ID: {self.module_id})")
    
    def _do_deactivate(self) -> None:
        """
        停用模块
        """
        logger.info(f"正在停用自我反思模块 (ID: {self.module_id})...")
        
        # 停止反思线程
        self.reflection_running = False
        if self.reflection_thread and self.reflection_thread.is_alive():
            self.reflection_thread.join(timeout=5)
        
        logger.success(f"自我反思模块停用成功 (ID: {self.module_id})")
    
    def _do_shutdown(self) -> None:
        """
        关闭模块
        """
        logger.info(f"正在关闭自我反思模块 (ID: {self.module_id})...")
        
        # 先停用模块
        if self.is_active:
            self._do_deactivate()
        
        # 保存反思历史
        self._save_reflection_history()
        
        # 取消事件订阅
        self._unsubscribe_events()
        
        logger.success(f"自我反思模块关闭成功 (ID: {self.module_id})")
    
    def _subscribe_events(self) -> None:
        """
        订阅事件
        """
        if not self.event_bus:
            logger.warning_status("事件总线未初始化，无法订阅事件")
            return
            
        # 订阅决策完成事件
        self.event_bus.subscribe("cognition.decision.made", self._on_decision_made)
        
        # 订阅对话响应事件
        self.event_bus.subscribe("interaction.response.generated", self._on_response_generated)
        
        # 订阅日常维护事件
        self.event_bus.subscribe("system.daily_maintenance", self._on_daily_maintenance)
    
    def _unsubscribe_events(self) -> None:
        """
        取消事件订阅
        """
        if not self.event_bus:
            return
            
        # 取消订阅决策完成事件
        self.event_bus.unsubscribe("cognition.decision.made", self._on_decision_made)
        
        # 取消订阅对话响应事件
        self.event_bus.unsubscribe("interaction.response.generated", self._on_response_generated)
        
        # 取消订阅日常维护事件
        self.event_bus.unsubscribe("system.daily_maintenance", self._on_daily_maintenance)
    
    def _reflection_worker(self) -> None:
        """
        反思线程函数
        """
        while self.reflection_running:
            # 检查是否到达反思时间
            current_time = time.time()
            time_since_last = current_time - self.last_reflection_time
            
            if time_since_last >= self.reflection_interval:
                # 执行周期性反思
                self.reflect()
                
                # 休眠一段时间
                time.sleep(60)  # 反思后休眠1分钟
            else:
                # 继续等待
                time.sleep(min(300, self.reflection_interval - time_since_last))  # 最多休眠5分钟 
    
    def _do_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        参数:
            input_data: 输入数据
            
        返回:
            Dict[str, Any]: 处理结果
        """
        operation = input_data.get("operation", "")
        
        if operation == "reflect_now":
            # 立即执行反思
            return self.reflect()
        
        elif operation == "get_reflection_history":
            # 获取反思历史
            limit = input_data.get("limit", 10)
            return {
                "success": True,
                "history": self.get_reflection_history(limit)
            }
            
        elif operation == "get_reflection_status":
            # 获取反思状态
            return {
                "success": True,
                "active": self.is_active,
                "last_reflection_time": self.last_reflection_time,
                "next_scheduled_reflection": self._get_next_reflection_time(),
                "reflection_modes": self.reflection_modes,
                "reflection_depth": self.reflection_depth
            }
            
        elif operation == "update_reflection_settings":
            # 更新反思设置
            settings = input_data.get("settings", {})
            return self.update_reflection_settings(settings)
            
        else:
            return {
                "success": False,
                "message": f"未知的操作: {operation}"
            }
    
    def reflect(self) -> Dict[str, Any]:
        """
        执行自我反思
        
        返回:
            Dict[str, Any]: 反思结果
        """
        logger.info("开始自我反思过程...")
        
        # 使用锁防止多线程同时反思
        with self.reflection_lock:
            try:
                # 更新反思时间
                self.last_reflection_time = time.time()
                
                # 初始化反思结果
                reflection_results = {
                    "timestamp": self.last_reflection_time,
                    "insights": [],
                    "improvements": [],
                    "success": True
                }
                
                # 行为反思
                if self.reflection_modes["behavior"]:
                    behavior_reflection = self._reflect_on_behavior()
                    if behavior_reflection:
                        reflection_results["insights"].extend(behavior_reflection.get("insights", []))
                        reflection_results["improvements"].extend(behavior_reflection.get("improvements", []))
                
                # 决策反思
                if self.reflection_modes["decision"]:
                    decision_reflection = self._reflect_on_decisions()
                    if decision_reflection:
                        reflection_results["insights"].extend(decision_reflection.get("insights", []))
                        reflection_results["improvements"].extend(decision_reflection.get("improvements", []))
                
                # 知识反思
                if self.reflection_modes["knowledge"]:
                    knowledge_reflection = self._reflect_on_knowledge()
                    if knowledge_reflection:
                        reflection_results["insights"].extend(knowledge_reflection.get("insights", []))
                        reflection_results["improvements"].extend(knowledge_reflection.get("improvements", []))
                
                # 情感反思
                if self.reflection_modes["emotion"]:
                    emotion_reflection = self._reflect_on_emotions()
                    if emotion_reflection:
                        reflection_results["insights"].extend(emotion_reflection.get("insights", []))
                        reflection_results["improvements"].extend(emotion_reflection.get("improvements", []))
                
                # 交互反思
                if self.reflection_modes["interaction"]:
                    interaction_reflection = self._reflect_on_interactions()
                    if interaction_reflection:
                        reflection_results["insights"].extend(interaction_reflection.get("insights", []))
                        reflection_results["improvements"].extend(interaction_reflection.get("improvements", []))
                
                # 偏见反思
                if self.reflection_modes["bias"]:
                    bias_reflection = self._reflect_on_biases()
                    if bias_reflection:
                        reflection_results["insights"].extend(bias_reflection.get("insights", []))
                        reflection_results["improvements"].extend(bias_reflection.get("improvements", []))
                
                # 价值观反思
                if self.reflection_modes["value"]:
                    value_reflection = self._reflect_on_values()
                    if value_reflection:
                        reflection_results["insights"].extend(value_reflection.get("insights", []))
                        reflection_results["improvements"].extend(value_reflection.get("improvements", []))
                
                # 添加到反思历史
                self.reflection_history.append(reflection_results)
                
                # 保存反思历史
                self._save_reflection_history()
                
                # 发布反思完成事件
                if self.event_bus:
                    self.event_bus.publish("cognition.reflection.completed", {
                        "module_id": self.module_id,
                        "timestamp": time.time(),
                        "insight_count": len(reflection_results["insights"]),
                        "improvement_count": len(reflection_results["improvements"])
                    })
                
                logger.success(f"自我反思完成，获得 {len(reflection_results['insights'])} 个洞察和 {len(reflection_results['improvements'])} 条改进建议")
                
                return {
                    "success": True,
                    "reflection": reflection_results
                }
                
            except Exception as e:
                logger.error_status(f"自我反思过程中发生错误: {str(e)}")
                return {
                    "success": False,
                    "message": f"自我反思失败: {str(e)}"
                }
    
    def _reflect_on_behavior(self) -> Dict[str, Any]:
        """
        反思行为模式
        
        返回:
            Dict[str, Any]: 行为反思结果
        """
        # 这里应该连接到行为历史记录
        # 目前简单实现返回空结果
        return {
            "insights": ["行为反思尚未实现具体细节"],
            "improvements": ["增强行为反思能力"]
        }
    
    def _reflect_on_decisions(self) -> Dict[str, Any]:
        """
        反思决策过程
        
        返回:
            Dict[str, Any]: 决策反思结果
        """
        # 这里应该连接到决策历史记录
        # 目前简单实现返回空结果
        return {
            "insights": ["决策反思尚未实现具体细节"],
            "improvements": ["增强决策反思能力"]
        }
    
    def _reflect_on_knowledge(self) -> Dict[str, Any]:
        """
        反思知识状态
        
        返回:
            Dict[str, Any]: 知识反思结果
        """
        # 这里应该连接到知识库
        # 目前简单实现返回空结果
        return {
            "insights": ["知识反思尚未实现具体细节"],
            "improvements": ["增强知识反思能力"]
        }
    
    def _reflect_on_emotions(self) -> Dict[str, Any]:
        """
        反思情感状态
        
        返回:
            Dict[str, Any]: 情感反思结果
        """
        # 这里应该连接到情感历史记录
        # 目前简单实现返回空结果
        return {
            "insights": ["情感反思尚未实现具体细节"],
            "improvements": ["增强情感反思能力"]
        }
    
    def _reflect_on_interactions(self) -> Dict[str, Any]:
        """
        反思交互方式
        
        返回:
            Dict[str, Any]: 交互反思结果
        """
        # 这里应该连接到对话历史记录
        # 目前简单实现返回空结果
        return {
            "insights": ["交互反思尚未实现具体细节"],
            "improvements": ["增强交互反思能力"]
        }
    
    def _reflect_on_biases(self) -> Dict[str, Any]:
        """
        反思认知偏见
        
        返回:
            Dict[str, Any]: 偏见反思结果
        """
        # 这里应该实现偏见检测和分析
        # 目前简单实现返回空结果
        return {
            "insights": ["偏见反思尚未实现具体细节"],
            "improvements": ["增强偏见反思能力"]
        }
    
    def _reflect_on_values(self) -> Dict[str, Any]:
        """
        反思价值观
        
        返回:
            Dict[str, Any]: 价值观反思结果
        """
        # 这里应该连接到价值观系统
        # 目前简单实现返回空结果
        return {
            "insights": ["价值观反思尚未实现具体细节"],
            "improvements": ["增强价值观反思能力"]
        }
    
    def _load_reflection_history(self) -> None:
        """
        加载反思历史记录
        """
        if not self.storage:
            logger.warning_status("存储管理器未初始化，无法加载反思历史")
            return
            
        try:
            # 加载历史记录
            storage_key = f"cognition:reflection:history:{self.module_id}"
            history_json = self.storage.get(storage_key)
            
            if history_json:
                self.reflection_history = json.loads(history_json)
                
                # 如果有历史记录，更新上次反思时间
                if self.reflection_history:
                    self.last_reflection_time = self.reflection_history[-1]["timestamp"]
                    
                logger.info(f"已加载 {len(self.reflection_history)} 条反思历史记录")
                    
        except Exception as e:
            logger.error_status(f"加载反思历史记录失败: {str(e)}")
    
    def _save_reflection_history(self) -> None:
        """
        保存反思历史
        """
        if not self.storage:
            logger.warning_status("存储管理器未初始化，无法保存反思历史")
            return
            
        try:
            # 限制历史记录数量
            if len(self.reflection_history) > 100:
                self.reflection_history = self.reflection_history[-100:]
                
            # 保存历史记录
            storage_key = f"cognition:reflection:history:{self.module_id}"
            self.storage.set(storage_key, json.dumps(self.reflection_history))
            
            logger.debug(f"已保存 {len(self.reflection_history)} 条反思历史记录")
            
        except Exception as e:
            logger.error_status(f"保存反思历史记录失败: {str(e)}")
    
    def _get_next_reflection_time(self) -> float:
        """
        获取下次反思时间
        
        返回:
            float: 下次反思时间
        """
        return self.last_reflection_time + self.reflection_interval
    
    def _on_decision_made(self, event_data: Dict[str, Any]) -> None:
        """
        处理决策完成事件
        
        参数:
            event_data: 事件数据
        """
        # 记录决策信息以便后续反思
        pass
    
    def _on_response_generated(self, event_data: Dict[str, Any]) -> None:
        """
        处理对话响应生成事件
        
        参数:
            event_data: 事件数据
        """
        # 记录对话信息以便后续反思
        pass
    
    def _on_daily_maintenance(self, event_data: Dict[str, Any]) -> None:
        """
        处理日常维护事件
        
        参数:
            event_data: 事件数据
        """
        # 触发深度反思
        self.reflect()
    
    def get_reflection_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取反思历史记录
        
        参数:
            limit: 获取的历史记录数量
            
        返回:
            List[Dict[str, Any]]: 反思历史记录列表
        """
        # 返回最近的历史记录
        return self.reflection_history[-limit:] if self.reflection_history else []
    
    def update_reflection_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新反思设置
        
        参数:
            settings: 新的设置
            
        返回:
            Dict[str, Any]: 更新结果
        """
        try:
            # 更新反思间隔
            if "reflection_interval" in settings:
                self.reflection_interval = settings["reflection_interval"]
            
            # 更新反思深度
            if "reflection_depth" in settings:
                self.reflection_depth = settings["reflection_depth"]
            
            # 更新反思模式
            if "reflection_modes" in settings:
                for mode, enabled in settings["reflection_modes"].items():
                    if mode in self.reflection_modes:
                        self.reflection_modes[mode] = enabled
            
            return {
                "success": True,
                "message": "反思设置已更新",
                "current_settings": {
                    "reflection_interval": self.reflection_interval,
                    "reflection_depth": self.reflection_depth,
                    "reflection_modes": self.reflection_modes
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"更新反思设置失败: {str(e)}"
            }

    @staticmethod
    def get_instance(module_id: str = "self_reflection", config: Dict[str, Any] = None) -> 'SelfReflection':
        """
        获取自我反思模块实例（单例模式）
        
        参数:
            module_id: 模块ID
            config: 配置参数
            
        返回:
            SelfReflection: 自我反思模块实例
        """
        if not hasattr(SelfReflection, "_instance") or SelfReflection._instance is None:
            SelfReflection._instance = SelfReflection(module_id, config)
        
        return SelfReflection._instance


def get_instance(module_id: str = "self_reflection", config: Dict[str, Any] = None) -> SelfReflection:
    """
    获取自我反思模块实例
    
    参数:
        module_id: 模块ID
        config: 配置参数
        
    返回:
        SelfReflection: 自我反思模块实例
    """
    return SelfReflection.get_instance(module_id, config) 