#!/usr/bin/env python3
"""
社交网络模块 - Social Network

该模块负责构建和管理数字生命体的社交网络，包括：
1. 社交关系管理 - 构建和维护社交关系网络
2. 群体交互 - 管理群体交互和群体行为
3. 社交记忆 - 记录和检索社交互动历史
4. 社交推荐 - 推荐潜在的社交互动
5. 社交影响分析 - 分析社交关系对行为的影响

作者: Claude
创建日期: 2024-08-01
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import networkx as nx
from typing import Dict, Any, List, Optional, Tuple, Union, Set
from datetime import datetime

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
cognition_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(cognition_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from adapters.ai_service_adapter import get_instance as get_ai_service
from cognitive_modules.perception.social_perception import get_instance as get_social_perception

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.cognition.social_network")

class SocialNetwork(CognitiveModuleBase):
    """
    社交网络模块
    
    构建和管理数字生命体的社交网络。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化社交网络模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "social_network", "cognition", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取AI服务适配器
        self.ai_service = get_ai_service()
        
        # 获取社交感知模块
        self.social_perception = get_social_perception()
        
        # 社交网络图 (NetworkX图)
        self.social_graph = nx.DiGraph()
        
        # 社交关系类型
        self.relationship_types = {
            "friend": {"weight": 0.7, "reciprocal": True},
            "family": {"weight": 0.9, "reciprocal": True},
            "colleague": {"weight": 0.5, "reciprocal": True},
            "acquaintance": {"weight": 0.3, "reciprocal": True},
            "stranger": {"weight": 0.1, "reciprocal": True},
            "romantic": {"weight": 0.95, "reciprocal": True},
            "mentor": {"weight": 0.8, "reciprocal": False},
            "student": {"weight": 0.8, "reciprocal": False},
            "service": {"weight": 0.4, "reciprocal": False}
        }
        
        # 群组列表
        self.groups = {}
        
        # 社交互动历史
        self.interaction_history = []
        
        # 社交网络统计
        self.network_stats = {
            "node_count": 0,
            "edge_count": 0,
            "group_count": 0,
            "last_updated": time.time()
        }
        
        # 加载社交网络数据
        self.storage_path = os.path.join(root_dir, "storage", "social_network")
        os.makedirs(self.storage_path, exist_ok=True)
        self._load_social_network()
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"社交网络模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("user_message", self._on_user_message)
        self.event_bus.subscribe("group_message", self._on_group_message)
        self.event_bus.subscribe("user_joined", self._on_user_joined)
        self.event_bus.subscribe("user_left", self._on_user_left)
        self.event_bus.subscribe("social_perception_updated", self._on_social_perception_updated)
        self.event_bus.subscribe("relationship_updated", self._on_relationship_updated)
        
        logger.debug("已订阅相关事件")
    
    def _on_user_message(self, data: Dict[str, Any]):
        """处理用户消息事件"""
        try:
            user_id = data.get("user_id", "")
            message = data.get("message", "")
            
            if not user_id or not message:
                return
            
            # 记录社交互动
            self._record_interaction(user_id, "message", {"message": message[:50] + "..." if len(message) > 50 else message})
            
            # 确保用户节点存在
            self._ensure_user_node(user_id)
        except Exception as e:
            logger.error_status(f"处理用户消息事件失败: {e}")
    
    def _on_group_message(self, data: Dict[str, Any]):
        """处理群聊消息事件"""
        try:
            user_id = data.get("user_id", "")
            group_id = data.get("group_id", "")
            message = data.get("message", "")
            
            if not user_id or not group_id or not message:
                return
            
            # 记录群组互动
            self._record_group_interaction(user_id, group_id, "message", {"message": message[:50] + "..." if len(message) > 50 else message})
            
            # 确保群组存在
            self._ensure_group(group_id)
            
            # 确保用户节点存在
            self._ensure_user_node(user_id)
            
            # 确保用户是群组成员
            self._ensure_group_membership(group_id, user_id)
        except Exception as e:
            logger.error_status(f"处理群聊消息事件失败: {e}")
    
    def _on_user_joined(self, data: Dict[str, Any]):
        """处理用户加入事件"""
        try:
            user_id = data.get("user_id", "")
            group_id = data.get("group_id", "")
            
            if not user_id or not group_id:
                return
            
            # 确保群组存在
            self._ensure_group(group_id)
            
            # 确保用户节点存在
            self._ensure_user_node(user_id)
            
            # 添加用户到群组
            self._add_user_to_group(group_id, user_id)
            
            # 记录群组互动
            self._record_group_interaction(user_id, group_id, "joined", {})
        except Exception as e:
            logger.error_status(f"处理用户加入事件失败: {e}")
    
    def _on_user_left(self, data: Dict[str, Any]):
        """处理用户离开事件"""
        try:
            user_id = data.get("user_id", "")
            group_id = data.get("group_id", "")
            
            if not user_id or not group_id:
                return
            
            # 移除用户从群组
            self._remove_user_from_group(group_id, user_id)
            
            # 记录群组互动
            self._record_group_interaction(user_id, group_id, "left", {})
        except Exception as e:
            logger.error_status(f"处理用户离开事件失败: {e}")
    
    def _on_social_perception_updated(self, data: Dict[str, Any]):
        """处理社交感知更新事件"""
        try:
            user_id = data.get("user_id", "")
            relationship = data.get("relationship", {})
            
            if not user_id or not relationship:
                return
            
            # 更新社交关系
            relationship_type = relationship.get("type", "stranger")
            self._update_relationship(user_id, relationship_type)
        except Exception as e:
            logger.error_status(f"处理社交感知更新事件失败: {e}")
    
    def _on_relationship_updated(self, data: Dict[str, Any]):
        """处理关系更新事件"""
        try:
            user_id = data.get("user_id", "")
            relationship_type = data.get("relationship_type", "")
            
            if not user_id or not relationship_type:
                return
            
            # 更新社交关系
            self._update_relationship(user_id, relationship_type)
        except Exception as e:
            logger.error_status(f"处理关系更新事件失败: {e}")
    
    def _ensure_user_node(self, user_id: str):
        """
        确保用户节点存在
        
        Args:
            user_id: 用户ID
        """
        if user_id not in self.social_graph.nodes:
            # 获取用户信息
            user_info = self.life_context.get_user_info(user_id)
            
            # 添加用户节点
            self.social_graph.add_node(user_id, 
                                      type="user", 
                                      name=user_info.get("name", user_id),
                                      created_at=time.time(),
                                      last_active=time.time())
            
            # 更新网络统计
            self.network_stats["node_count"] = len(self.social_graph.nodes)
            self.network_stats["last_updated"] = time.time()
            
            logger.debug(f"添加用户节点: {user_id}")
    
    def _ensure_group(self, group_id: str):
        """
        确保群组存在
        
        Args:
            group_id: 群组ID
        """
        if group_id not in self.groups:
            # 获取群组信息
            group_info = self.life_context.get_group_info(group_id)
            
            # 添加群组
            self.groups[group_id] = {
                "id": group_id,
                "name": group_info.get("name", group_id),
                "members": [],
                "created_at": time.time(),
                "last_active": time.time()
            }
            
            # 更新网络统计
            self.network_stats["group_count"] = len(self.groups)
            self.network_stats["last_updated"] = time.time()
            
            logger.debug(f"添加群组: {group_id}")
    
    def _ensure_group_membership(self, group_id: str, user_id: str):
        """
        确保用户是群组成员
        
        Args:
            group_id: 群组ID
            user_id: 用户ID
        """
        if group_id in self.groups and user_id not in self.groups[group_id]["members"]:
            self._add_user_to_group(group_id, user_id)
    
    def _add_user_to_group(self, group_id: str, user_id: str):
        """
        添加用户到群组
        
        Args:
            group_id: 群组ID
            user_id: 用户ID
        """
        if group_id not in self.groups:
            self._ensure_group(group_id)
        
        if user_id not in self.groups[group_id]["members"]:
            self.groups[group_id]["members"].append(user_id)
            self.groups[group_id]["last_active"] = time.time()
            
            logger.debug(f"添加用户 {user_id} 到群组 {group_id}")
    
    def _remove_user_from_group(self, group_id: str, user_id: str):
        """
        从群组移除用户
        
        Args:
            group_id: 群组ID
            user_id: 用户ID
        """
        if group_id in self.groups and user_id in self.groups[group_id]["members"]:
            self.groups[group_id]["members"].remove(user_id)
            self.groups[group_id]["last_active"] = time.time()
            
            logger.debug(f"从群组 {group_id} 移除用户 {user_id}")
    
    def _update_relationship(self, user_id: str, relationship_type: str):
        """
        更新社交关系
        
        Args:
            user_id: 用户ID
            relationship_type: 关系类型
        """
        try:
            # 确保用户节点存在
            self._ensure_user_node(user_id)
            
            # 获取关系属性
            relationship_props = self.relationship_types.get(relationship_type, self.relationship_types["stranger"])
            
            # 添加数字生命体到用户的边
            self_node = "self"
            if self_node not in self.social_graph.nodes:
                self.social_graph.add_node(self_node, type="self", name="林嫣然", created_at=time.time())
            
            # 更新或添加关系边
            if self.social_graph.has_edge(self_node, user_id):
                # 更新边属性
                self.social_graph[self_node][user_id]["type"] = relationship_type
                self.social_graph[self_node][user_id]["weight"] = relationship_props["weight"]
                self.social_graph[self_node][user_id]["last_updated"] = time.time()
            else:
                # 添加新边
                self.social_graph.add_edge(self_node, user_id, 
                                          type=relationship_type,
                                          weight=relationship_props["weight"],
                                          created_at=time.time(),
                                          last_updated=time.time())
            
            # 如果是互惠关系，添加反向边
            if relationship_props["reciprocal"]:
                if self.social_graph.has_edge(user_id, self_node):
                    # 更新边属性
                    self.social_graph[user_id][self_node]["type"] = relationship_type
                    self.social_graph[user_id][self_node]["weight"] = relationship_props["weight"]
                    self.social_graph[user_id][self_node]["last_updated"] = time.time()
                else:
                    # 添加新边
                    self.social_graph.add_edge(user_id, self_node, 
                                              type=relationship_type,
                                              weight=relationship_props["weight"],
                                              created_at=time.time(),
                                              last_updated=time.time())
            
            # 更新网络统计
            self.network_stats["edge_count"] = len(self.social_graph.edges)
            self.network_stats["last_updated"] = time.time()
            
            logger.debug(f"更新与用户 {user_id} 的关系为 {relationship_type}")
        except Exception as e:
            logger.error_status(f"更新社交关系失败: {e}")
    
    def _record_interaction(self, user_id: str, interaction_type: str, details: Dict[str, Any] = None):
        """
        记录社交互动
        
        Args:
            user_id: 用户ID
            interaction_type: 互动类型
            details: 互动详情
        """
        try:
            # 确保用户节点存在
            self._ensure_user_node(user_id)
            
            # 记录互动
            interaction = {
                "user_id": user_id,
                "type": interaction_type,
                "details": details or {},
                "timestamp": time.time()
            }
            
            # 添加到互动历史
            self.interaction_history.append(interaction)
            
            # 限制历史记录大小
            if len(self.interaction_history) > 1000:
                self.interaction_history = self.interaction_history[-1000:]
            
            # 更新用户节点的最后活跃时间
            self.social_graph.nodes[user_id]["last_active"] = time.time()
        except Exception as e:
            logger.error_status(f"记录社交互动失败: {e}")
    
    def _record_group_interaction(self, user_id: str, group_id: str, interaction_type: str, details: Dict[str, Any] = None):
        """
        记录群组互动
        
        Args:
            user_id: 用户ID
            group_id: 群组ID
            interaction_type: 互动类型
            details: 互动详情
        """
        try:
            # 确保用户节点存在
            self._ensure_user_node(user_id)
            
            # 确保群组存在
            self._ensure_group(group_id)
            
            # 记录互动
            interaction = {
                "user_id": user_id,
                "group_id": group_id,
                "type": interaction_type,
                "details": details or {},
                "timestamp": time.time()
            }
            
            # 添加到互动历史
            self.interaction_history.append(interaction)
            
            # 限制历史记录大小
            if len(self.interaction_history) > 1000:
                self.interaction_history = self.interaction_history[-1000:]
            
            # 更新用户节点的最后活跃时间
            self.social_graph.nodes[user_id]["last_active"] = time.time()
            
            # 更新群组的最后活跃时间
            self.groups[group_id]["last_active"] = time.time()
        except Exception as e:
            logger.error_status(f"记录群组互动失败: {e}")
    
    def get_user_relationships(self, user_id: str = None) -> List[Dict[str, Any]]:
        """
        获取用户关系列表
        
        Args:
            user_id: 用户ID，如果为None则返回所有用户关系
            
        Returns:
            用户关系列表
        """
        try:
            relationships = []
            
            if user_id:
                # 获取指定用户的关系
                if user_id in self.social_graph.nodes:
                    # 获取出边
                    for _, target, data in self.social_graph.out_edges(user_id, data=True):
                        relationships.append({
                            "source": user_id,
                            "target": target,
                            "type": data.get("type", "unknown"),
                            "weight": data.get("weight", 0.0),
                            "last_updated": data.get("last_updated", 0)
                        })
                    
                    # 获取入边
                    for source, _, data in self.social_graph.in_edges(user_id, data=True):
                        relationships.append({
                            "source": source,
                            "target": user_id,
                            "type": data.get("type", "unknown"),
                            "weight": data.get("weight", 0.0),
                            "last_updated": data.get("last_updated", 0)
                        })
            else:
                # 获取所有关系
                for source, target, data in self.social_graph.edges(data=True):
                    relationships.append({
                        "source": source,
                        "target": target,
                        "type": data.get("type", "unknown"),
                        "weight": data.get("weight", 0.0),
                        "last_updated": data.get("last_updated", 0)
                    })
            
            return relationships
        except Exception as e:
            logger.error_status(f"获取用户关系列表失败: {e}")
            return []
    
    def get_group_members(self, group_id: str) -> List[str]:
        """
        获取群组成员列表
        
        Args:
            group_id: 群组ID
            
        Returns:
            成员ID列表
        """
        try:
            if group_id in self.groups:
                return self.groups[group_id]["members"]
            return []
        except Exception as e:
            logger.error_status(f"获取群组成员列表失败: {e}")
            return []
    
    def get_user_groups(self, user_id: str) -> List[str]:
        """
        获取用户所在的群组列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            群组ID列表
        """
        try:
            user_groups = []
            
            for group_id, group_data in self.groups.items():
                if user_id in group_data["members"]:
                    user_groups.append(group_id)
            
            return user_groups
        except Exception as e:
            logger.error_status(f"获取用户群组列表失败: {e}")
            return []
    
    def get_recent_interactions(self, user_id: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的互动记录
        
        Args:
            user_id: 用户ID，如果为None则返回所有互动
            limit: 返回记录数量限制
            
        Returns:
            互动记录列表
        """
        try:
            if user_id:
                # 获取指定用户的互动
                user_interactions = [interaction for interaction in self.interaction_history 
                                    if interaction["user_id"] == user_id]
                return sorted(user_interactions, key=lambda x: x["timestamp"], reverse=True)[:limit]
            else:
                # 获取所有互动
                return sorted(self.interaction_history, key=lambda x: x["timestamp"], reverse=True)[:limit]
        except Exception as e:
            logger.error_status(f"获取最近互动记录失败: {e}")
            return []
    
    def get_network_stats(self) -> Dict[str, Any]:
        """
        获取社交网络统计信息
        
        Returns:
            统计信息
        """
        try:
            # 更新统计信息
            self.network_stats["node_count"] = len(self.social_graph.nodes)
            self.network_stats["edge_count"] = len(self.social_graph.edges)
            self.network_stats["group_count"] = len(self.groups)
            self.network_stats["last_updated"] = time.time()
            
            return self.network_stats
        except Exception as e:
            logger.error_status(f"获取社交网络统计信息失败: {e}")
            return self.network_stats
    
    def _load_social_network(self):
        """加载社交网络数据"""
        try:
            nodes_file = os.path.join(self.storage_path, "nodes.json")
            edges_file = os.path.join(self.storage_path, "edges.json")
            groups_file = os.path.join(self.storage_path, "groups.json")
            
            # 加载节点
            if os.path.exists(nodes_file):
                with open(nodes_file, 'r', encoding='utf-8') as f:
                    nodes_data = json.load(f)
                    
                    for node_id, attrs in nodes_data.items():
                        self.social_graph.add_node(node_id, **attrs)
            
            # 加载边
            if os.path.exists(edges_file):
                with open(edges_file, 'r', encoding='utf-8') as f:
                    edges_data = json.load(f)
                    
                    for edge in edges_data:
                        source = edge.pop("source")
                        target = edge.pop("target")
                        self.social_graph.add_edge(source, target, **edge)
            
            # 加载群组
            if os.path.exists(groups_file):
                with open(groups_file, 'r', encoding='utf-8') as f:
                    self.groups = json.load(f)
            
            # 更新网络统计
            self.network_stats["node_count"] = len(self.social_graph.nodes)
            self.network_stats["edge_count"] = len(self.social_graph.edges)
            self.network_stats["group_count"] = len(self.groups)
            self.network_stats["last_updated"] = time.time()
            
            logger.data_status(f"已加载社交网络数据: {self.network_stats['node_count']} 个节点, {self.network_stats['edge_count']} 个边, {self.network_stats['group_count']} 个群组")
        except Exception as e:
            logger.error_status(f"加载社交网络数据失败: {e}")
    
    def _save_social_network(self):
        """保存社交网络数据"""
        try:
            nodes_file = os.path.join(self.storage_path, "nodes.json")
            edges_file = os.path.join(self.storage_path, "edges.json")
            groups_file = os.path.join(self.storage_path, "groups.json")
            
            # 保存节点
            nodes_data = {node: data for node, data in self.social_graph.nodes(data=True)}
            with open(nodes_file, 'w', encoding='utf-8') as f:
                json.dump(nodes_data, f, ensure_ascii=False, indent=2)
            
            # 保存边
            edges_data = []
            for source, target, data in self.social_graph.edges(data=True):
                edge_data = data.copy()
                edge_data["source"] = source
                edge_data["target"] = target
                edges_data.append(edge_data)
            
            with open(edges_file, 'w', encoding='utf-8') as f:
                json.dump(edges_data, f, ensure_ascii=False, indent=2)
            
            # 保存群组
            with open(groups_file, 'w', encoding='utf-8') as f:
                json.dump(self.groups, f, ensure_ascii=False, indent=2)
            
            logger.data_status(f"已保存社交网络数据: {len(self.social_graph.nodes)} 个节点, {len(self.social_graph.edges)} 个边, {len(self.groups)} 个群组")
        except Exception as e:
            logger.error_status(f"保存社交网络数据失败: {e}")
    
    def shutdown(self):
        """关闭模块"""
        # 保存社交网络数据
        self._save_social_network()
        
        logger.info(f"社交网络模块 {self.module_id} 已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> SocialNetwork:
    """
    获取社交网络模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        社交网络模块实例
    """
    return SocialNetwork(config=config) 