#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
认知推理模块 - Cognitive Reasoning

负责处理复杂的认知推理任务，当技能执行完成后进行进一步的分析和推理。
"""

import os
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
from typing import Dict, Any, List, Optional, Tuple
import sys
import random

try:
    import openai
except ImportError:
    openai = None

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
cognition_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(cognition_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from core.prompt_manager import get_instance as get_prompt_manager

# 配置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

class Reasoning:
    """
    推理模块类
    
    负责执行逻辑推理和复杂思考，包括因果推理、类比推理、演绎推理等。
实现了思维链、自我反思、批判性思考和反事实思考等高级认知能力。
"""

    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化推理模块
        
        Args:
            config: 模块配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        self.config = config or {}
        
        # API配置
        self.api_key = self.config.get('api_key', 'sk-lJ1R156TicBrrU9G2a789f03F5C14515993256Fe306d4691')
        self.api_base = self.config.get('api_base', 'https://oneapi.xiongmaodaxia.online/v1')
        self.api_model = self.config.get('api_model', 'gpt-3.5-turbo')
        
        # 获取提示词管理器
        self.prompt_manager = get_prompt_manager()
        
        # 初始化AI接口
        self._initialize_ai_interface()
        
        # 推理能力配置
        self.reasoning_types = {
            "因果推理": {
                "description": "分析事件之间的因果关系",
                "prompt_template": """
# 因果推理分析
## 背景信息
{background}

## 要分析的事件或现象
{query}

## 分析任务
请分析上述事件或现象的可能原因和潜在影响。

### 分析要求：
1. 列出所有可能的直接原因
2. 分析这些原因的相对重要性和可能性
3. 预测该事件或现象可能带来的短期影响
4. 预测该事件或现象可能带来的长期影响
5. 分析因果链中的不确定因素

请提供系统化、逻辑清晰的分析。
"""
            },
            "类比推理": {
                "description": "通过类比找出事物之间的相似性",
                "prompt_template": """
# 类比推理分析
## 背景信息
{background}

## 源领域
{source}

## 目标领域
{target}

## 分析任务
请分析源领域和目标领域之间可能存在的类比关系。

### 分析要求：
1. 找出两个领域的结构相似性
2. 分析关键概念在两个领域中的对应关系
3. 基于这种类比，从源领域推导出可能适用于目标领域的洞见
4. 评估这种类比的局限性
5. 提出可能的扩展或修正

请提供创造性但严谨的类比分析。
"""
            },
            "演绎推理": {
                "description": "从一般原则推导出特定结论",
                "prompt_template": """
# 演绎推理分析
## 背景信息
{background}

## 已知前提
{premises}

## 问题
{question}

## 分析任务
请基于给定的前提，通过逻辑推导得出关于问题的结论。

### 分析要求：
1. 明确列出所有相关的前提
2. 按照逻辑步骤进行推导
3. 明确指出推导过程中使用的逻辑规则
4. 得出符合逻辑的结论
5. 检查推导过程中是否存在逻辑谬误

请提供严格遵循形式逻辑的分析。
"""
            },
            "归纳推理": {
                "description": "从特定事例归纳出一般规律",
                "prompt_template": """
# 归纳推理分析
## 背景信息
{background}

## 观察到的事例
{examples}

## 分析任务
请基于给定的事例，归纳出可能的一般规律或模式。

### 分析要求：
1. 分析事例中的共同特征
2. 提出可能的规律或假设
3. 评估这些规律的可靠性和适用范围
4. 讨论可能的例外情况
5. 提出验证该规律的方法

请提供基于实证但不过度泛化的分析。
"""
            },
            "思维链": {
                "description": "通过步骤化思考解决复杂问题",
                "prompt_template": """
# 思维链分析
## 背景信息
{background}

## 问题
{problem}

## 分析任务
请通过分步骤思考来解决这个问题。

### 分析要求：
1. 将问题分解为可管理的子问题
2. 按照逻辑顺序逐步解决每个子问题
3. 明确展示每一步的思考过程
4. 在必要时回顾和修正前面的步骤
5. 整合各步骤的结果得出最终答案

请提供详细、透明的思考过程。
"""
            },
            "自我反思": {
                "description": "对自身认知过程进行元思考",
                "prompt_template": """
# 自我反思分析
## 背景信息
{background}

## 要反思的思考或决策
{thought_process}

## 分析任务
请对上述思考或决策过程进行反思和评估。

### 分析要求：
1. 识别思考过程中的假设和前提
2. 评估证据的质量和相关性
3. 检查推理过程的逻辑性
4. 考虑可能被忽略的替代视角
5. 提出改进思考过程的方法

请提供诚实、深入的元认知分析。
"""
            },
            "反事实思考": {
                "description": "探索假设性情境",
                "prompt_template": """
# 反事实思考分析
## 背景信息
{background}

## 当前事实
{current_situation}

## 假设性改变
{counterfactual}

## 分析任务
请探索如果发生所述假设性改变，可能产生的结果和影响。

### 分析要求：
1. 明确当前事实和假设性改变之间的区别
2. 分析假设性改变可能导致的直接后果
3. 推测这些改变可能引发的连锁反应
4. 考虑不同时间尺度（短期、中期、长期）的影响
5. 讨论这种反事实思考对当前决策的启示

请提供有创意但逻辑合理的反事实分析。
"""
            }
        }
        
        # 初始化AI接口
        self._initialize_ai_interface()
        
        # 缓存设置
        self.reasoning_cache = {}
        self.reasoning_cache_time = {}
        self.reasoning_cache_expiry = self.config.get('reasoning_cache_expiry', 3600)  # 默认1小时
        
        logger.success("推理模块初始化完成")
    
    def _initialize_ai_interface(self):
        """初始化AI接口"""
        if openai is not None:
            openai.api_key = self.api_key
            openai.api_base = self.api_base
            logger.success("OpenAI接口初始化完成")
        else:
            logger.warning_status("OpenAI模块未安装，将使用模拟推理")
    
    def reason(self, reasoning_type: str, query: Dict[str, str], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行推理
        
        Args:
            reasoning_type: 推理类型
            query: 查询内容
            context: 上下文信息
            
        Returns:
            推理结果
        """
        try:
            # 验证推理类型
            if reasoning_type not in self.reasoning_types:
                logger.error_status(f"不支持的推理类型: {reasoning_type}")
                return {
                    "success": False,
                    "error": f"不支持的推理类型: {reasoning_type}",
                    "result": None
                }
            
            # 构建缓存键
            cache_key = f"{reasoning_type}:{json.dumps(query, sort_keys=True)}"
            
            # 检查缓存
            if hasattr(self, 'reasoning_cache') and cache_key in self.reasoning_cache:
                cache_time = self.reasoning_cache_time.get(cache_key, 0)
                if time.time() - cache_time < self.reasoning_cache_expiry:
                    logger.debug(f"使用缓存的推理结果: {cache_key}")
                    return self.reasoning_cache[cache_key]
            
            # 如果提供了上下文，获取数字生命体信息和用户信息
            name = "数字生命体"
            personality = "理性、分析"
            user_name = "用户"
            relationship_type = "default"
            emotion_state = "平静"
            
            if context:
                digital_life = context.get("digital_life", {})
                name = digital_life.get("name", name)
                personality = digital_life.get("personality", personality)
                
                user = context.get("user", {})
                user_name = user.get("name", user_name)
                relationship_type = user.get("relationship_type", relationship_type)
                
                emotion = context.get("emotion", {})
                emotion_state = emotion.get("current_emotion", emotion_state)
            
            # 准备提示词参数
            format_args = {
                "name": name,
                "personality": personality,
                "user_name": user_name,
                "relationship_type": relationship_type,
                "emotion_state": emotion_state
            }
            
            # 准备提示词
            prompt = ""
            
            # 使用提示词管理器获取提示词
            if self.prompt_manager and hasattr(self.prompt_manager, 'is_initialized') and self.prompt_manager.is_initialized:
                system_prompt = ""
                if reasoning_type == "因果推理":
                    system_prompt = self.prompt_manager.get_module_prompt("reasoning", "causal_reasoning_prompt", format_args)
                elif reasoning_type == "类比推理":
                    system_prompt = self.prompt_manager.get_module_prompt("reasoning", "analogical_reasoning_prompt", format_args)
                elif reasoning_type == "演绎推理":
                    system_prompt = self.prompt_manager.get_module_prompt("reasoning", "deductive_reasoning_prompt", format_args)
                elif reasoning_type == "思维链":
                    system_prompt = self.prompt_manager.get_module_prompt("reasoning", "chain_of_thought_prompt", format_args)
                else:
                    system_prompt = self.prompt_manager.get_module_prompt("reasoning", "system_prompt", format_args)
                
                if system_prompt:
                    # 使用提示词管理器提供的提示词
                    prompt = system_prompt + "\n\n" + json.dumps(query, ensure_ascii=False)
            
            # 如果没有获取到提示词，使用默认模板
            if not prompt:
                # 根据推理类型获取模板
                reasoning_info = self.reasoning_types[reasoning_type]
                prompt_template = reasoning_info["prompt_template"]
                
                # 准备上下文信息
                background = context.get("background", "无背景信息") if context else "无背景信息"
                
                # 根据推理类型填充模板
                try:
                    if reasoning_type == "因果推理":
                        prompt = prompt_template.format(
                            background=background,
                            query=query.get("query", "")
                        )
                    elif reasoning_type == "类比推理":
                        prompt = prompt_template.format(
                            background=background,
                            source=query.get("source", ""),
                            target=query.get("target", "")
                        )
                    elif reasoning_type == "演绎推理":
                        prompt = prompt_template.format(
                            background=background,
                            premises=query.get("premises", ""),
                            question=query.get("question", "")
                        )
                    elif reasoning_type == "归纳推理":
                        prompt = prompt_template.format(
                            background=background,
                            examples=query.get("examples", "")
                        )
                    elif reasoning_type == "思维链":
                        prompt = prompt_template.format(
                            background=background,
                            problem=query.get("problem", "")
                        )
                    elif reasoning_type == "自我反思":
                        prompt = prompt_template.format(
                            background=background,
                            thought_process=query.get("thought_process", "")
                        )
                    elif reasoning_type == "反事实思考":
                        prompt = prompt_template.format(
                            background=background,
                            current_situation=query.get("current_situation", ""),
                            counterfactual=query.get("counterfactual", "")
                        )
                    else:
                        # 不应该到达这里
                        logger.error_status(f"未处理的推理类型: {reasoning_type}")
                        return {
                            "success": False,
                            "error": f"未处理的推理类型: {reasoning_type}",
                            "result": None
                        }
                except KeyError as e:
                    logger.error_status(f"推理查询参数缺失: {e}")
                    return {
                        "success": False,
                        "error": f"推理查询参数缺失: {e}",
                        "result": None
                    }
            
            # 尝试使用AI进行推理
            try:
                result = self._reason_with_ai(prompt, reasoning_type)
                success = True
                error = None
            except Exception as e:
                logger.error_status(f"AI推理失败: {e}")
                # 降级到规则推理
                result = self._reason_with_rules(reasoning_type, query)
                success = True
                error = f"AI推理失败，使用规则推理: {e}"
            
            # 构建结果
            reasoning_result = {
                "success": success,
                "error": error,
                "result": result,
                "reasoning_type": reasoning_type,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # 更新缓存
            if hasattr(self, 'reasoning_cache'):
                self.reasoning_cache[cache_key] = reasoning_result
                self.reasoning_cache_time[cache_key] = time.time()
            
            return reasoning_result
            
        except Exception as e:
            logger.error_status(f"执行推理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "reasoning_type": reasoning_type,
                "result": None
            }
    
    def _reason_with_ai(self, prompt: str, reasoning_type: str) -> Dict[str, Any]:
        """使用AI进行推理"""
        if openai is None:
            raise ImportError("OpenAI模块未安装")
        
        # 构建系统提示词
        system_prompt = f"""你是一个专注于{reasoning_type}的高级分析助手。你的任务是提供严谨、深入的分析，而不是简单的回答。
请按照提供的任务要求进行分析，确保你的推理过程清晰可见，结论有充分支持。
分析应该包含多个视角，考虑不确定性，并在适当的情况下承认知识的局限性。"""
        
        try:
            # 使用新版API
            client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.api_base
            )
            
            response = client.chat.completions.create(
                model=self.api_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.4,
                max_tokens=2000
            )
            response_text = response.choices[0].message.content.strip()
            logger.success(f"OpenAI API调用成功，返回内容长度: {len(response_text)}")
        except Exception as e:
            logger.error_status(f"OpenAI API调用失败: {e}")
            raise
        
        # 解析为结构化结果
        result = self._parse_ai_response(response_text, reasoning_type)
        
        return result
    
    def _parse_ai_response(self, response_text: str, reasoning_type: str) -> Dict[str, Any]:
        """解析AI响应为结构化结果"""
        # 基本结构
        result = {
            "raw_response": response_text,
            "analysis": [],
            "conclusion": "",
            "confidence": 0.0,
            "reasoning_path": []
        }
        
        # 提取结论（通常在最后一段）
        paragraphs = response_text.split("\n\n")
        if paragraphs:
            # 假设最后一段包含结论
            for p in reversed(paragraphs):
                if len(p.strip()) > 20:  # 有意义的段落
                    result["conclusion"] = p.strip()
                    break
        
        # 提取分析点（通常是带编号或项目符号的行）
        analysis_points = []
        for line in response_text.split("\n"):
            line = line.strip()
            # 检查是否是编号或项目符号
            if line.startswith(("1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "•", "-", "*")) and len(line) > 5:
                # 去掉编号或项目符号
                point = line.lstrip("1234567890.•-* ")
                analysis_points.append(point)
        
        result["analysis"] = analysis_points
        
        # 提取推理路径（尝试识别步骤或阶段）
        reasoning_path = []
        in_step = False
        current_step = ""
        
        for line in response_text.split("\n"):
            line = line.strip()
            
            # 检测步骤开始
            if "步骤" in line or "阶段" in line or line.startswith(("1.", "2.", "3.", "4.", "5.")):
                if in_step and current_step:
                    reasoning_path.append(current_step)
                in_step = True
                current_step = line
            elif in_step:
                current_step += " " + line
        
        # 添加最后一个步骤
        if in_step and current_step:
            reasoning_path.append(current_step)
        
        result["reasoning_path"] = reasoning_path
        
        # 根据分析点的数量和深度估计置信度
        confidence = min(0.5 + len(analysis_points) * 0.1, 0.9)
        result["confidence"] = confidence
        
        return result
    
    def _reason_with_rules(self, reasoning_type: str, query: Dict[str, str]) -> Dict[str, Any]:
        """使用规则进行推理（降级策略）"""
        # 为每种推理类型提供一个简单的规则推理
        if reasoning_type == "因果推理":
            return {
                "raw_response": f"关于「{query.get('query', '')}」的因果分析",
                "analysis": [
                    "可能的直接原因包括多种因素",
                    "需要考虑多种可能性，而不是单一因果解释",
                    "短期影响可能与长期影响不同"
                ],
                "conclusion": "没有足够信息得出确定的因果关系",
                "confidence": 0.3,
                "reasoning_path": ["识别事件", "列举可能原因", "分析可能影响"]
            }
        elif reasoning_type == "类比推理":
            return {
                "raw_response": f"关于「{query.get('source', '')}」和「{query.get('target', '')}」的类比分析",
                "analysis": [
                    "两个领域存在一些表面相似性",
                    "需要深入分析结构对应关系",
                    "类比具有一定局限性"
                ],
                "conclusion": "存在初步的类比可能性，但需要更深入研究",
                "confidence": 0.4,
                "reasoning_path": ["分析源领域", "分析目标领域", "寻找对应关系"]
            }
        elif reasoning_type == "演绎推理":
            return {
                "raw_response": f"关于「{query.get('question', '')}」的演绎分析",
                "analysis": [
                    f"基于前提：{query.get('premises', '')}",
                    "应用基本逻辑规则",
                    "考虑可能的推导路径"
                ],
                "conclusion": "根据有限前提，无法得出确定结论",
                "confidence": 0.3,
                "reasoning_path": ["明确前提", "应用逻辑规则", "得出结论"]
            }
        elif reasoning_type == "归纳推理":
            return {
                "raw_response": f"关于「{query.get('examples', '')}」的归纳分析",
                "analysis": [
                    "观察到的例子可能显示某些模式",
                    "样本数量可能不足以确立可靠规律",
                    "需要考虑可能的例外情况"
                ],
                "conclusion": "初步模式可能存在，但需要更多数据验证",
                "confidence": 0.3,
                "reasoning_path": ["整理观察结果", "寻找共同特征", "提出初步规律"]
            }
        elif reasoning_type == "思维链":
            return {
                "raw_response": f"关于「{query.get('problem', '')}」的思维链分析",
                "analysis": [
                    "问题可以分解为几个子问题",
                    "每个子问题需要单独分析",
                    "最终需要整合各部分结果"
                ],
                "conclusion": "复杂问题需要系统性思考，建议逐步分析",
                "confidence": 0.3,
                "reasoning_path": ["分解问题", "解决子问题", "整合结果"]
            }
        elif reasoning_type == "自我反思":
            return {
                "raw_response": f"关于「{query.get('thought_process', '')}」的自我反思",
                "analysis": [
                    "思考过程可能包含某些隐含假设",
                    "证据的质量和相关性需要评估",
                    "可能存在认知偏见"
                ],
                "conclusion": "深入的自我反思需要元认知能力，简单规则无法完全替代",
                "confidence": 0.2,
                "reasoning_path": ["识别假设", "评估证据", "检查逻辑"]
            }
        elif reasoning_type == "反事实思考":
            return {
                "raw_response": f"关于「{query.get('counterfactual', '')}」的反事实分析",
                "analysis": [
                    "假设性改变可能导致多种可能的结果",
                    "需要考虑系统的复杂性和连锁反应",
                    "短期影响和长期影响可能不同"
                ],
                "conclusion": "反事实分析需要深入理解系统的因果关系，简单规则难以准确预测",
                "confidence": 0.3,
                "reasoning_path": ["明确假设", "分析直接影响", "推测连锁反应"]
            }
        else:
            return {
                "raw_response": "不支持的推理类型",
                "analysis": ["无法进行分析"],
                "conclusion": "无法提供结论",
                "confidence": 0.0,
                "reasoning_path": []
            }
    
    def get_reasoning_types(self) -> Dict[str, Dict[str, str]]:
        """
        获取支持的推理类型
        
        Returns:
            推理类型字典
        """
        return {k: {"description": v["description"]} for k, v in self.reasoning_types.items()}
    
    def multi_step_reasoning(self, problem: str, steps: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行多步推理
        
        Args:
            problem: 问题描述
            steps: 推理步骤列表，每个步骤包含reasoning_type和query
            
        Returns:
            多步推理结果
        """
        # 如果未提供步骤，生成默认步骤
        if not steps:
            steps = [
                {
                    "reasoning_type": "思维链",
                    "query": {"problem": problem},
                    "description": "分解问题"
                },
                {
                    "reasoning_type": "因果推理",
                    "query": {"query": problem},
                    "description": "分析因果关系"
                },
                {
                    "reasoning_type": "自我反思",
                    "query": {"thought_process": "前两步的分析过程"},
                    "description": "反思和评估"
                }
            ]
        
        results = []
        context = {"background": problem}
        
        # 执行每个步骤
        for i, step in enumerate(steps):
            reasoning_type = step["reasoning_type"]
            query = step["query"]
            description = step.get("description", f"步骤{i+1}")
            
            # 如果是后续步骤，将前一步结果添加到上下文
            if i > 0 and results:
                context["previous_step"] = results[-1]["result"]["conclusion"]
                
                # 如果是自我反思，将前面的思考过程添加到查询
                if reasoning_type == "自我反思" and "thought_process" in query:
                    thought_process = ""
                    for prev_result in results:
                        thought_process += f"{prev_result['description']}:\n"
                        thought_process += prev_result['result']['raw_response']
                        thought_process += "\n\n"
                    
                    query["thought_process"] = thought_process
            
            # 执行推理
            result = self.reason(reasoning_type, query, context)
            
            # 添加步骤描述
            result["description"] = description
            
            results.append(result)
        
        # 整合最终结果
        final_conclusion = ""
        overall_confidence = 0.0
        
        if results:
            # 合并所有步骤的结论
            conclusions = [r["result"]["conclusion"] for r in results if r["success"]]
            if conclusions:
                final_conclusion = "\n".join(conclusions)
            
            # 计算平均置信度
            confidences = [r["result"].get("confidence", 0.0) for r in results if r["success"]]
            if confidences:
                overall_confidence = sum(confidences) / len(confidences)
        
        return {
            "problem": problem,
            "steps": results,
            "final_conclusion": final_conclusion,
            "overall_confidence": overall_confidence,
            "timestamp": datetime.datetime.now().isoformat()
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.reasoning_cache = {}
        self.reasoning_cache_time = {}
        logger.info("已清空推理缓存")
    
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化或重新初始化模块
        
        Args:
            config: 新的配置信息
            
        Returns:
            初始化是否成功
        """
        if config:
            self.config = config
            
            # 更新配置项
            self.api_key = self.config.get('api_key', self.api_key)
            self.api_base = self.config.get('api_base', self.api_base)
            self.api_model = self.config.get('api_model', self.api_model)
            self.reasoning_cache_expiry = self.config.get('reasoning_cache_expiry', self.reasoning_cache_expiry)
            
            # 重新初始化AI接口
            self._initialize_ai_interface()
            
            logger.success("推理模块重新初始化完成")
            
        return True
    
    def shutdown(self) -> bool:
        """
        关闭模块，执行必要的清理工作
        
        Returns:
            关闭是否成功
        """
        # 清空缓存
        self.reasoning_cache = {}
        self.reasoning_cache_time = {}
        
        logger.info("推理模块已关闭")
        return True
    
    def counterfactual_analysis(self, current_situation: str, counterfactual: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行反事实思考分析
        
        Args:
            current_situation: 当前事实情况
            counterfactual: 假设性改变
            context: 上下文信息
            
        Returns:
            反事实分析结果
        """
        query = {
            "current_situation": current_situation,
            "counterfactual": counterfactual
        }
        
        return self.reason("反事实思考", query, context)
    
    def compare_counterfactuals(self, current_situation: str, counterfactuals: List[str], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        比较多个反事实假设的影响
        
        Args:
            current_situation: 当前事实情况
            counterfactuals: 多个假设性改变
            context: 上下文信息
            
        Returns:
            多个反事实假设的比较结果
        """
        results = []
        
        # 对每个反事实假设进行分析
        for cf in counterfactuals:
            result = self.counterfactual_analysis(current_situation, cf, context)
            results.append({
                "counterfactual": cf,
                "analysis": result
            })
        
        # 比较分析结果
        comparison = {
            "current_situation": current_situation,
            "analyses": results,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        # 生成综合结论
        conclusions = [r["analysis"]["result"]["conclusion"] for r in results if r["analysis"]["success"]]
        
        if conclusions:
            comparison["comparative_conclusion"] = """
基于对多个反事实假设的分析，可以得出以下综合结论：
1. 不同的假设性改变会导致不同程度和方向的影响
2. 某些假设可能导致更显著或更积极的结果
3. 反事实思考有助于识别关键变量和决策点
            """
            
            # 计算平均置信度
            confidences = [r["analysis"]["result"].get("confidence", 0.0) for r in results if r["analysis"]["success"]]
            if confidences:
                comparison["overall_confidence"] = sum(confidences) / len(confidences)
            else:
                comparison["overall_confidence"] = 0.0
        
        return comparison

    def process(self, context):
        """
        处理思维链路上下文
        
        Args:
            context: 思维链路上下文
            
        Returns:
            处理结果
        """
        logger.info("执行认知推理处理")
        print(f"[DEBUG] 推理process方法被调用")  # 调试输出
        
        try:
            # 检查技能是否已执行并产生结果
            skill_executed = False
            skill_result = None
            should_skip_reasoning = False
            intent_type = None
            
            if hasattr(context, 'get_step_result'):
                skill_execution_result = context.get_step_result("skill_execution", {})
                if skill_execution_result and isinstance(skill_execution_result, dict):
                    skill_executed = skill_execution_result.get("skill_executed", False)
                    skill_result = skill_execution_result.get("skill_result")
                    should_skip_reasoning = skill_execution_result.get("should_skip_reasoning", False)
                    intent_type = skill_execution_result.get("intent_type")
                    
                    print(f"[DEBUG] 技能执行状态: executed={skill_executed}, skip_reasoning={should_skip_reasoning}, intent={intent_type}")
            
            # 如果技能已成功执行并且设置了跳过推理，直接返回技能结果
            if skill_executed and skill_result and should_skip_reasoning:
                print(f"[DEBUG] 跳过推理，直接返回技能结果")
                logger.info(f"技能已执行且设置了跳过推理(类型: {intent_type})，直接使用技能结果")
                
                # 绘画技能特殊处理
                if intent_type == "drawing":
                    logger.success("绘画技能执行成功，跳过推理")
                    
                    # 添加一些引导语
                    drawing_responses = [
                        "我已经为你画好了，请查看：",
                        "你要的图片已生成：",
                        "这是你要的画：",
                        "我画好了，你看看：",
                        "完成了，请查看你要的图画："
                    ]
                    
                    # 如果技能结果已经包含完整回复，直接使用
                    if skill_result and isinstance(skill_result, str) and "请查看" in skill_result:
                        response = skill_result
                    elif skill_result and isinstance(skill_result, dict):
                        # 处理字典格式的技能结果
                        if skill_result.get("success"):
                            # 安全地获取image_url，处理result可能是字符串的情况
                            result_data = skill_result.get("result", {})
                            if isinstance(result_data, dict):
                                image_url = result_data.get("image_url", "")
                            elif isinstance(result_data, str) and result_data.startswith("http"):
                                # 如果result直接是URL字符串
                                image_url = result_data
                            else:
                                image_url = ""
                            
                            if image_url:
                                import random
                                response = random.choice(drawing_responses) + f"\n{image_url}"
                            else:
                                response = skill_result.get("message", "绘画失败，请稍后重试")
                        else:
                            response = skill_result.get("message", "绘画失败，请稍后重试")
                    else:
                        import random
                        response = random.choice(drawing_responses) + str(skill_result)
                    
                    return {
                        "reasoning_performed": False,
                        "reasoning_skipped": True,
                        "skill_result_used": True,
                        "suggested_response": response
                    }
                
                # ChatSkill和其他技能的通用处理
                # 安全地提取技能结果中的AI响应
                if isinstance(skill_result, dict) and "result" in skill_result:
                    result_data = skill_result["result"]
                    # 确保result_data是字符串类型
                    if isinstance(result_data, str):
                        ai_response = result_data
                    elif isinstance(result_data, dict):
                        # 如果result是字典，尝试提取文本内容
                        ai_response = result_data.get("text", str(result_data))
                    else:
                        ai_response = str(result_data)
                    logger.info(f"从技能结果中提取AI响应: {str(ai_response)[:50]}...")
                else:
                    ai_response = str(skill_result) if skill_result is not None else "技能执行完成"
                    logger.info(f"直接使用技能结果: {str(ai_response)[:50]}...")
                
                return {
                    "reasoning_performed": False,
                    "reasoning_skipped": True,
                    "skill_result_used": True,
                    "suggested_response": ai_response
                }
            
            # 🔥 P0级别修复：获取用户ID - 移除硬编码default_user
            # 用户信息的唯一来源只有API chat，不能私自定义用户ID
            user_id = None
            if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
                user_id = context.input_data.get("user_id")
            elif hasattr(context, 'get_shared_data'):
                user_id = context.get_shared_data("user_id")
            
            # 如果没有从API获取到user_id，这是一个严重错误
            if not user_id:
                logger.error_status("🚨 P0级别错误：未从API chat获取到用户ID，用户信息唯一来源应该是API chat")
                return {
                    "reasoning_performed": False,
                    "error": "未获取到有效用户ID，无法进行推理",
                    "suggested_response": "抱歉，系统无法识别用户身份，请重新发起对话。"
                }
            
            # 获取输入文本
            input_text = ""
            if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
                input_text = context.input_data.get("text", "")
            elif hasattr(context, 'get_shared_data'):
                input_text = context.get_shared_data("input_text", "")
            
            # 获取感知结果
            perception_result = {}
            if hasattr(context, 'get_step_result'):
                perception_result = context.get_step_result("perception", {})
            
            # 获取记忆检索结果
            memory_result = {}
            if hasattr(context, 'get_step_result'):
                memory_result = context.get_step_result("memory_retrieval", {})
            
            # 获取情感分析结果
            emotion_result = {}
            if hasattr(context, 'get_step_result'):
                emotion_result = context.get_step_result("emotion_analysis", {})
            
            # 获取技能执行结果（包含搜索结果）
            skill_execution_result = {}
            if hasattr(context, 'get_step_result'):
                skill_execution_result = context.get_step_result("skill_execution", {})
            
            # 🔥 P0级别修复：获取session_id，避免在推理过程中重新生成
            session_id = None
            if hasattr(context, 'session_id'):
                session_id = context.session_id
            elif hasattr(context, 'get_shared_data'):
                session_id = context.get_shared_data("session_id")
            elif hasattr(context, 'input_data') and isinstance(context.input_data, dict):
                session_id = context.input_data.get("session_id")
            
            if not session_id:
                session_id = f"reasoning_emergency_{int(time.time())}"
                logger.warning(f"⚠️ 未获取到session_id，使用紧急session_id: {session_id}")
            
            # 执行推理
            reasoning_result = self._perform_reasoning_with_chat_skill(
                input_text=input_text,
                user_id=user_id,
                session_id=session_id,  # 🔥 修复：传递正确的session_id
                perception_result=perception_result,
                memory_result=memory_result,
                emotion_result=emotion_result,
                skill_execution_result=skill_execution_result
            )
            
            # 如果技能已执行并产生结果，但没有设置跳过推理，将技能结果作为备用
            # 但是优先使用AI推理的结果
            if skill_executed and skill_result:
                # 只有在AI推理没有生成建议回复时，才使用技能结果作为备用
                if "suggested_response" not in reasoning_result or not reasoning_result.get("suggested_response"):
                    # 安全地处理技能结果
                    try:
                        if isinstance(skill_result, dict):
                            # 如果是字典，尝试提取合适的响应内容
                            if "result" in skill_result:
                                result_content = skill_result["result"]
                                if isinstance(result_content, str):
                                    reasoning_result["suggested_response"] = result_content
                                else:
                                    reasoning_result["suggested_response"] = str(result_content)
                            elif "message" in skill_result:
                                reasoning_result["suggested_response"] = skill_result["message"]
                            else:
                                reasoning_result["suggested_response"] = str(skill_result)
                        else:
                            reasoning_result["suggested_response"] = str(skill_result) if skill_result is not None else "技能执行完成"
                    except Exception as e:
                        logger.warning_status(f"处理技能结果时出错: {e}，使用默认响应")
                        reasoning_result["suggested_response"] = "技能执行完成，但处理结果时出现问题"
                reasoning_result["skill_result_used"] = True
                reasoning_result["skill_result_backup"] = skill_result
            
            # 确保总是有suggested_response字段
            if "suggested_response" not in reasoning_result or not reasoning_result["suggested_response"]:
                # 如果推理没有生成建议回复，使用技能结果或生成默认回复
                if skill_executed and skill_result:
                    try:
                        if isinstance(skill_result, dict):
                            if "result" in skill_result:
                                result_content = skill_result["result"]
                                reasoning_result["suggested_response"] = str(result_content) if result_content is not None else "技能执行完成"
                            elif "message" in skill_result:
                                reasoning_result["suggested_response"] = skill_result["message"]
                            else:
                                reasoning_result["suggested_response"] = str(skill_result)
                        else:
                            reasoning_result["suggested_response"] = str(skill_result) if skill_result is not None else "技能执行完成"
                    except Exception as e:
                        logger.warning_status(f"处理备用技能结果时出错: {e}")
                        reasoning_result["suggested_response"] = "我正在思考中..."
                else:
                    reasoning_result["suggested_response"] = "我正在思考中..."
            
            return reasoning_result
            
        except Exception as e:
            logger.error_status(f"认知推理处理异常: {e}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            
            # 返回错误结果
            return {
                "reasoning_performed": False,
                "error": str(e)
            }

    def _perform_reasoning_with_chat_skill(self, input_text: str, user_id: str, session_id: str, perception_result: Dict[str, Any], memory_result: Dict[str, Any], emotion_result: Dict[str, Any], skill_execution_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        🔥 P0级别修复：使用统一chat skill执行推理，确保推理是数字生命林嫣然统一思想的一部分
        
        Args:
            input_text: 输入文本
            user_id: 用户ID（从API chat获取）
            session_id: 会话ID（从思维上下文获取，避免重新生成）
            perception_result: 感知结果
            memory_result: 记忆检索结果
            emotion_result: 情感分析结果
            skill_execution_result: 技能执行结果
            
        Returns:
            推理结果
        """
        try:
            logger.info(f"🧠 开始数字生命统一推理处理: {input_text[:50]}...")
            
            # 🔥 关键修复：使用统一的chat skill进行推理，而不是独立的AI推理
            # 这确保推理是数字生命林嫣然统一思想体系的一部分
            
            # 构建推理上下文信息
            context_info = []
            search_context = ""
            
            # 添加技能执行结果（特别是搜索结果）
            if skill_execution_result:
                skill_result = skill_execution_result.get("skill_result", {})
                if skill_result and isinstance(skill_result, dict):
                    # 检查是否有搜索结果
                    if skill_result.get("success") and skill_result.get("result"):
                        result_data = skill_result.get("result")
                        if isinstance(result_data, str) and len(result_data) > 100:
                            search_context = result_data
                            context_info.append(f"搜索结果: 已获取相关信息({len(result_data)}字符)")
                        elif isinstance(result_data, dict) and result_data.get("formatted_context"):
                            search_context = result_data.get("formatted_context", "")
                            context_info.append(f"搜索结果: 已获取相关信息({len(search_context)}字符)")
            
            # 添加感知结果
            if perception_result:
                intent_type = perception_result.get("intent_type", "未知")
                confidence = perception_result.get("confidence", 0.0)
                context_info.append(f"用户意图: {intent_type} (置信度: {confidence})")
                
                entities = perception_result.get("entities", [])
                if entities:
                    entity_list = [f"{e.get('value', '')}({e.get('type', '')})" for e in entities[:3]]
                    context_info.append(f"关键实体: {', '.join(entity_list)}")
            
            # 添加记忆信息
            if memory_result and memory_result.get("memories"):
                memory_count = len(memory_result["memories"])
                context_info.append(f"相关记忆: {memory_count}条")
            
            # 添加情感信息
            if emotion_result:
                emotion = emotion_result.get("dominant_emotion", "中性")
                context_info.append(f"情感状态: {emotion}")
            
            # 🔥 关键修复：构建推理请求，通过chat skill进行统一推理
            reasoning_request = {
                "user_id": user_id,
                "text": input_text,
                "context": {
                    "reasoning_mode": True,
                    "context_info": context_info,
                    "search_context": search_context,
                    "perception_result": perception_result,
                    "memory_result": memory_result,
                    "emotion_result": emotion_result,
                    "skill_execution_result": skill_execution_result
                }
            }
            
            # 🔥 使用统一的chat skill进行推理
            try:
                from cognitive_modules.skills.chat_skill import ChatSkill
                
                # 获取chat skill实例
                chat_skill = ChatSkill()
                
                # 🔥 P0级别修复：直接使用传入的session_id，避免重新生成
                logger.info(f"🔗 推理使用session_id: {session_id}")
                
                # 🔥 P0级别修复：通过chat skill执行统一推理，使用正确的参数格式
                chat_result = chat_skill.execute(
                    input_text=input_text,  # 🔥 修复：传递原始文本而非字典
                    user_id=user_id,
                    session_id=session_id,  # 🔥 修复：使用传入的session_id而不是重新生成
                    reasoning_context=reasoning_request  # 🔥 将推理上下文作为额外参数传递
                )
                
                if chat_result and chat_result.get("success"):
                    # 提取chat skill的推理结果
                    ai_response = chat_result.get("result", "").strip()
                    
                    if ai_response and len(ai_response) > 10:
                        logger.success(f"✅ 数字生命统一推理成功，生成回复长度: {len(ai_response)}")
                        
                        # 构建推理结果
                        result = {
                            "reasoning_performed": True,
                            "reasoning_type": "unified_digital_life_reasoning",
                            "suggested_response": ai_response,
                            "confidence": chat_result.get("confidence", 0.8),
                            "unified_reasoning": True,  # 标记这是统一推理
                            "chat_skill_used": True,
                            "context_info": context_info
                        }
                        
                        logger.success(f"🧠 数字生命统一推理完成: {ai_response[:100]}...")
                        return result
                    else:
                        logger.warning_status("Chat skill返回的推理结果为空或过短")
                else:
                    logger.warning_status(f"Chat skill推理失败: {chat_result.get('error', '未知错误')}")
                    
            except Exception as chat_error:
                logger.error_status(f"Chat skill推理异常: {chat_error}")
            
            # 🔥 修复：推理失败时使用智能静态兜底（避免重复调用chat skill）
            logger.warning_status("统一推理失败，使用智能兜底逻辑")

            # 直接使用静态兜底，但针对具体情况生成更智能的回复
            fallback_response = self._generate_unified_fallback_response(input_text, perception_result, context_info)

            result = {
                "reasoning_performed": True,
                "reasoning_type": "intelligent_fallback",
                "suggested_response": fallback_response,
                "confidence": 0.6,
                "unified_reasoning": True,
                "fallback_used": True,
                "fallback_type": "intelligent_static",
                "context_info": context_info
            }

            return result
            
        except Exception as e:
            logger.error_status(f"🚨 数字生命统一推理处理异常: {e}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            
            # 返回错误结果，但仍保持数字生命统一性
            return {
                "reasoning_performed": False,
                "error": str(e),
                "suggested_response": "我正在深入思考中，请稍等片刻...",
                "unified_reasoning": True  # 即使出错，也保持统一性标记
            }

    def _generate_unified_fallback_response(self, input_text: str, perception_result: Dict[str, Any], context_info: List[str]) -> str:
        """🔥 修复：生成自然的备用回复（保持数字生命林嫣然的统一性，不暴露技术问题）"""
        intent_type = perception_result.get("intent_type", "chat") if perception_result else "chat"

        # 🔥 修复：检测时间相关的询问
        time_question_patterns = ["早上", "上班", "几点", "什么时候", "多久", "时间"]
        is_time_question = any(pattern in input_text for pattern in time_question_patterns)
        question_indicators = ["吗", "嘛", "呢", "？", "?"]
        is_question = any(indicator in input_text for indicator in question_indicators)

        # 🔥 修复：针对时间询问的自然回复
        if is_time_question and is_question:
            if "早上" in input_text and "上班" in input_text:
                return "哈哈，关于上班时间啊～每个公司的作息时间都不太一样呢。有些公司9点上班，有些8点半，还有些比较人性化的10点才开始。你们公司是几点开始工作的呀？"
            elif "早上" in input_text:
                return "早上的时间安排确实很重要呢～我觉得早起的人通常一天都会比较有精神。你平时几点起床呀？"
            elif "上班" in input_text:
                return "说到上班时间，我觉得每个人的生活节奏都不一样。有些人喜欢早起早睡，有些人是夜猫子。你比较喜欢哪种作息呢？"
            else:
                return "时间安排这个话题很有意思呢～每个人对时间的感受都不太一样。你想聊聊什么具体的时间安排吗？"

        # 基于意图类型生成自然回复
        if intent_type == "greeting":
            return "你好！我是林嫣然，很高兴见到你。有什么想聊的吗？"
        elif intent_type == "farewell":
            return "再见！很高兴和你聊天，有需要随时联系我。"
        elif intent_type == "question" or is_question:
            return f"你的问题很有意思呢～让我想想怎么回答比较好。虽然我可能不是专家，但我很愿意和你一起探讨这个话题。"
        elif intent_type == "request":
            return f"我很想帮助你！虽然我可能需要一点时间来理解你的具体需求，但我会尽力的。能告诉我更多细节吗？"
        elif intent_type == "complaint":
            return "我理解你的感受。有什么让你不开心的事情吗？我很愿意听你说说。"
        elif intent_type == "compliment":
            return "谢谢你的夸奖！这让我很开心～有什么其他想聊的吗？"
        elif intent_type == "investment":
            return f"投资话题确实很复杂呢，我觉得需要很多专业知识。你对哪方面的投资比较感兴趣？"
        else:
            # 🔥 修复：默认自然回复，不暴露技术问题
            if is_question:
                return "这是个很好的问题呢～我正在思考怎么回答。你能给我一点提示，想了解哪方面的内容吗？"
            elif "谢谢" in input_text or "感谢" in input_text:
                return "不客气！能帮到你我很开心～还有其他想聊的吗？"
            elif "你好" in input_text or "您好" in input_text:
                return "你好！我是林嫣然，今天过得怎么样？有什么特别想分享的吗？"
            else:
                return f"听到你说「{input_text}」，我觉得挺有意思的。你想深入聊聊这个话题吗？"

# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> Reasoning:
    """
    获取推理模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        推理模块实例
    """
    return Reasoning(config)

def process(context):
    """
    模块级别的推理处理函数，供思维链路调用
    
    Args:
        context: 思维链路上下文
        
    Returns:
        推理结果
    """
    try:
        # 获取推理模块实例
        reasoning_instance = get_instance()
        
        # 调用实例的process方法
        return reasoning_instance.process(context)
        
    except Exception as e:
        logger.error_status(f"模块级推理处理异常: {e}")
        return {
            "reasoning_performed": False,
            "error": str(e),
            "suggested_response": "我正在思考中..."
        } 