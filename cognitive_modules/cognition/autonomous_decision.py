#!/usr/bin/env python3
"""
自主决策模块 - Autonomous Decision Module

该模块实现了数字生命体的自主决策能力，包括：
1. 基于目标的决策生成
2. 决策评估与优化
3. 风险分析与管理
4. 价值观对齐决策
5. 适应性决策调整

作者: Claude
创建日期: 2024-07-10
版本: 1.0
"""

import os
import sys
import json
import time
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, Set

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
cognition_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(cognition_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.base.cognitive_interface import ICognitionModule
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.cognition.autonomous_decision")

class AutonomousDecision(CognitiveModuleBase, ICognitionModule):
    """
    自主决策模块类
    
    实现数字生命体的自主决策能力，包括基于目标的决策生成、决策评估与优化、风险分析等。
    """
    
    def __init__(self, module_id: str, config: Dict[str, Any] = None):
        """
        初始化自主决策模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        super().__init__(module_id, "cognition", config)
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 决策状态
        self.decision_state = {
            "is_deciding": False,
            "current_decision": None,
            "last_decision_time": 0
        }
        
        # 决策历史
        self.decision_history: List[Dict[str, Any]] = []
        self.max_history_size = 100
        
        # 决策模型
        self.decision_models = {
            "utility": self._utility_based_decision,
            "risk": self._risk_based_decision,
            "value": self._value_based_decision,
            "goal": self._goal_based_decision,
            "adaptive": self._adaptive_decision
        }
        
        # 决策线程
        self.decision_thread = None
        self.decision_thread_active = False
        
        logger.info(f"自主决策模块 {module_id} 已创建")

    def _generate_default_options(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成默认选项
        
        Args:
            context: 上下文信息
            
        Returns:
            默认选项列表
        """
        current_state = context.get("current_state", {})
        activity = current_state.get("activity", "")
        
        # 基于当前活动生成选项
        options = []
        
        if activity == "conversation":
            options = [
                {
                    "id": "option_1",
                    "description": "继续当前话题",
                    "content": "继续深入探讨当前话题",
                    "base_utility": 6.0,
                    "risk": 0.1,
                    "benefit": 6.5,
                    "option_type": "helpful"
                },
                {
                    "id": "option_2",
                    "description": "转换话题",
                    "content": "引入新的相关话题",
                    "base_utility": 5.5,
                    "risk": 0.3,
                    "benefit": 7.0,
                    "is_novel": True,
                    "option_type": "creative"
                },
                {
                    "id": "option_3",
                    "description": "提问",
                    "content": "向用户提问以了解更多信息",
                    "base_utility": 7.0,
                    "risk": 0.2,
                    "benefit": 8.0,
                    "option_type": "helpful"
                }
            ]
        elif activity == "learning":
            options = [
                {
                    "id": "option_1",
                    "description": "深入学习",
                    "content": "深入学习当前主题",
                    "base_utility": 8.0,
                    "risk": 0.1,
                    "benefit": 8.5,
                    "option_type": "helpful"
                },
                {
                    "id": "option_2",
                    "description": "拓展学习",
                    "content": "学习相关的新主题",
                    "base_utility": 7.0,
                    "risk": 0.3,
                    "benefit": 7.5,
                    "is_novel": True,
                    "option_type": "creative"
                },
                {
                    "id": "option_3",
                    "description": "应用知识",
                    "content": "尝试应用已学知识",
                    "base_utility": 6.5,
                    "risk": 0.4,
                    "benefit": 9.0,
                    "option_type": "helpful"
                }
            ]
        else:
            # 通用选项
            options = [
                {
                    "id": "option_1",
                    "description": "主动行动",
                    "content": "采取主动行动解决问题",
                    "base_utility": 6.5,
                    "risk": 0.4,
                    "benefit": 7.5,
                    "option_type": "helpful"
                },
                {
                    "id": "option_2",
                    "description": "等待观察",
                    "content": "等待更多信息再做决定",
                    "base_utility": 5.0,
                    "risk": 0.2,
                    "benefit": 5.5,
                    "option_type": "cautious"
                },
                {
                    "id": "option_3",
                    "description": "寻求帮助",
                    "content": "向用户寻求更多指导",
                    "base_utility": 7.0,
                    "risk": 0.1,
                    "benefit": 6.0,
                    "option_type": "helpful"
                }
            ]
        
        return options
    
    def _make_contextual_decision(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于当前上下文做出决策
        
        Args:
            context: 上下文信息
            
        Returns:
            决策结果
        """
        # 生成默认选项
        options = self._generate_default_options(context)
        
        # 决定最适合的决策模型
        decision_type = self._determine_decision_type(options, context)
        
        # 做出决策
        return self._make_decision(options, context, decision_type)
    
    def _evaluate_decision(self, decision: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估决策
        
        Args:
            decision: 决策信息
            
        Returns:
            评估结果
        """
        # 获取当前上下文
        current_context = self.life_context.get_all_context()
        
        # 获取决策时的上下文快照
        decision_context = decision.get("context_snapshot", {})
        
        # 分析上下文变化
        context_changes = self._analyze_context_changes(decision_context, current_context)
        
        # 评估决策结果
        result_evaluation = {
            "relevance": self._evaluate_decision_relevance(decision, current_context),
            "effectiveness": self._evaluate_decision_effectiveness(decision, context_changes),
            "adaptability": self._evaluate_decision_adaptability(decision, context_changes)
        }
        
        # 总体评分
        overall_score = (
            result_evaluation["relevance"] * 0.3 +
            result_evaluation["effectiveness"] * 0.5 +
            result_evaluation["adaptability"] * 0.2
        )
        
        return {
            "success": True,
            "decision_id": decision.get("id"),
            "evaluation": result_evaluation,
            "overall_score": overall_score,
            "improvement_suggestions": self._generate_improvement_suggestions(decision, result_evaluation)
        }
    
    def _analyze_context_changes(self, old_context: Dict[str, Any], new_context: Dict[str, Any]) -> Dict[str, bool]:
        """
        分析上下文变化
        
        Args:
            old_context: 旧上下文
            new_context: 新上下文
            
        Returns:
            上下文变化分析
        """
        changes = {
            "state_changed": False,
            "goals_changed": False,
            "values_changed": False,
            "user_changed": False
        }
        
        # 检查状态变化
        old_state = old_context.get("current_state", {})
        new_state = new_context.get("current_state", {})
        if old_state.get("activity") != new_state.get("activity"):
            changes["state_changed"] = True
        
        # 检查目标变化
        old_goals = old_context.get("goals", [])
        new_goals = new_context.get("goals", [])
        if len(old_goals) != len(new_goals):
            changes["goals_changed"] = True
        else:
            old_goal_ids = {g.get("id") for g in old_goals}
            new_goal_ids = {g.get("id") for g in new_goals}
            if old_goal_ids != new_goal_ids:
                changes["goals_changed"] = True
        
        # 检查价值观变化
        old_values = old_context.get("values", {})
        new_values = new_context.get("values", {})
        if old_values.keys() != new_values.keys():
            changes["values_changed"] = True
        else:
            for key in old_values:
                if abs(old_values.get(key, 0) - new_values.get(key, 0)) > 0.2:
                    changes["values_changed"] = True
                    break
        
        # 检查用户偏好变化
        old_user = old_context.get("user", {})
        new_user = new_context.get("user", {})
        if old_user.get("preferences", {}).keys() != new_user.get("preferences", {}).keys():
            changes["user_changed"] = True
        
        return changes
    
    def _evaluate_decision_relevance(self, decision: Dict[str, Any], current_context: Dict[str, Any]) -> float:
        """
        评估决策与当前上下文的相关性
        
        Args:
            decision: 决策信息
            current_context: 当前上下文
            
        Returns:
            相关性评分（0-1）
        """
        # 获取决策上下文
        decision_context = decision.get("context_snapshot", {})
        
        # 如果决策上下文为空，返回中等相关性
        if not decision_context:
            return 0.5
        
        # 计算上下文相似度
        similarity = 0.0
        factors = 0
        
        # 比较当前状态
        if "current_state" in decision_context and "current_state" in current_context:
            old_state = decision_context["current_state"]
            new_state = current_context["current_state"]
            
            if old_state.get("activity") == new_state.get("activity"):
                similarity += 1.0
            factors += 1
        
        # 比较目标
        if "goals" in decision_context and "goals" in current_context:
            old_goals = decision_context["goals"]
            new_goals = current_context["goals"]
            
            if old_goals and new_goals:
                common_goals = 0
                for old_goal in old_goals:
                    # 确保是字典类型
                    if not isinstance(old_goal, dict):
                        continue
                        
                    for new_goal in new_goals:
                        # 确保是字典类型
                        if not isinstance(new_goal, dict):
                            continue
                            
                        if old_goal.get("id") == new_goal.get("id"):
                            common_goals += 1
                            break
                
                if old_goals:
                    similarity += common_goals / len(old_goals)
                factors += 1
        
        # 比较价值观
        if "values" in decision_context and "values" in current_context:
            old_values = decision_context["values"]
            new_values = current_context["values"]
            
            if old_values and new_values:
                value_similarity = 0
                common_keys = set(old_values.keys()) & set(new_values.keys())
                
                for key in common_keys:
                    old_val = old_values[key]
                    new_val = new_values[key]
                    value_similarity += 1.0 - min(1.0, abs(old_val - new_val))
                
                if common_keys:
                    similarity += value_similarity / len(common_keys)
                factors += 1
        
        # 计算平均相似度
        if factors > 0:
            return similarity / factors
        else:
            return 0.5
    
    def _evaluate_decision_effectiveness(self, decision: Dict[str, Any], context_changes: Dict[str, bool]) -> float:
        """
        评估决策的有效性
        
        Args:
            decision: 决策信息
            context_changes: 上下文变化
            
        Returns:
            有效性评分（0-1）
        """
        # 评估决策是否导致了预期的变化
        selected_option = decision.get("selected", {})
        
        # 如果没有选项信息，返回中等有效性
        if not selected_option:
            return 0.5
        
        # 基于选项类型评估有效性
        option_type = selected_option.get("option_type", "")
        
        if option_type == "helpful":
            # 助人型决策应该导致状态改变
            return 0.8 if context_changes.get("state_changed", False) else 0.4
            
        elif option_type == "creative":
            # 创造型决策可能导致新目标或价值观变化
            if context_changes.get("goals_changed", False) or context_changes.get("values_changed", False):
                return 0.9
            else:
                return 0.5
                
        elif option_type == "cautious":
            # 谨慎型决策应该保持稳定
            if not any(context_changes.values()):
                return 0.7
            else:
                return 0.3
                
        else:
            # 默认评估
            change_count = sum(1 for changed in context_changes.values() if changed)
            return 0.5 + (change_count * 0.1)  # 每个变化加0.1分，最高0.9
    
    def _evaluate_decision_adaptability(self, decision: Dict[str, Any], context_changes: Dict[str, bool]) -> float:
        """
        评估决策的适应性
        
        Args:
            decision: 决策信息
            context_changes: 上下文变化
            
        Returns:
            适应性评分（0-1）
        """
        # 决策模型
        model = decision.get("model", "utility")
        
        # 上下文变化数量
        change_count = sum(1 for changed in context_changes.values() if changed)
        
        # 评估模型选择的适应性
        if change_count >= 2:
            # 高变化环境
            if model in ["adaptive", "risk"]:
                return 0.9  # 适应性和风险模型适合高变化环境
            else:
                return 0.6
        elif change_count == 1:
            # 中等变化环境
            if model in ["adaptive", "goal"]:
                return 0.8  # 适应性和目标模型适合中等变化环境
            else:
                return 0.7
        else:
            # 低变化环境
            if model in ["utility", "value"]:
                return 0.8  # 效用和价值模型适合稳定环境
            else:
                return 0.7
    
    def _generate_improvement_suggestions(self, decision: Dict[str, Any], evaluation: Dict[str, float]) -> List[str]:
        """
        生成决策改进建议
        
        Args:
            decision: 决策信息
            evaluation: 评估结果
            
        Returns:
            改进建议列表
        """
        suggestions = []
        
        # 基于评估结果生成建议
        if evaluation.get("relevance", 0) < 0.5:
            suggestions.append("提高上下文感知能力，确保决策与当前环境相关")
            
        if evaluation.get("effectiveness", 0) < 0.5:
            suggestions.append("增强结果预测能力，选择更有可能产生预期结果的选项")
            
        if evaluation.get("adaptability", 0) < 0.6:
            suggestions.append("提高环境适应性，在变化环境中选择更灵活的决策模型")
        
        # 计算总体评分
        overall_score = 0.0
        if all(k in evaluation for k in ["relevance", "effectiveness", "adaptability"]):
            overall_score = (
                evaluation["relevance"] * 0.3 +
                evaluation["effectiveness"] * 0.5 +
                evaluation["adaptability"] * 0.2
            )
        
        # 基于决策模型生成建议
        model = decision.get("model", "utility")
        
        if model == "utility" and overall_score < 0.6:
            suggestions.append("考虑价值观和风险因素，不仅仅关注效用")
            
        if model == "risk" and overall_score < 0.6:
            suggestions.append("平衡风险规避和机会把握")
            
        if model == "value" and overall_score < 0.6:
            suggestions.append("确保价值观评估全面且准确")
            
        if model == "goal" and overall_score < 0.6:
            suggestions.append("确保目标优先级设置合理")
        
        # 如果没有具体建议，添加通用建议
        if not suggestions:
            suggestions.append("尝试使用适应性决策模型以综合多种因素")
        
        return suggestions
    
    def _analyze_decision_feedback(self, feedback: Dict[str, Any]) -> Dict[str, float]:
        """
        分析决策反馈
        
        Args:
            feedback: 决策反馈数据
            
        Returns:
            决策模型表现评分
        """
        model_performance = {
            "utility": 0.0,
            "risk": 0.0,
            "value": 0.0,
            "goal": 0.0,
            "adaptive": 0.0
        }
        
        model_counts = {model: 0 for model in model_performance}
        
        # 分析每个反馈
        for decision_id, result in feedback.items():
            model = result.get("model", "")
            score = result.get("score", 0.0)
            
            if model in model_performance:
                model_performance[model] += score
                model_counts[model] += 1
        
        # 计算平均表现
        for model in model_performance:
            if model_counts[model] > 0:
                model_performance[model] /= model_counts[model]
            else:
                # 没有数据的模型给予中等评分
                model_performance[model] = 0.5
        
        return model_performance
    
    def _perform_decision_reasoning(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行决策推理
        
        Args:
            query: 查询内容
            context: 上下文信息
            
        Returns:
            推理结果
        """
        # 创建推理结果
        reasoning = {
            "id": f"reasoning_{int(time.time())}",
            "query": query,
            "content": self._create_reasoning_content(query, context),
            "timestamp": time.time(),
            "conclusions": self._generate_reasoning_conclusions(query, context)
        }
        
        return {
            "success": True,
            "reasoning": reasoning
        }
    
    def _create_reasoning_content(self, query: str, context: Dict[str, Any]) -> str:
        """
        创建推理内容
        
        Args:
            query: 查询内容
            context: 上下文信息
            
        Returns:
            推理内容
        """
        # 推理模板
        reasoning_template = "关于'{query}'的决策推理：分析了{factor_count}个关键因素，包括{factors}。基于当前情境，{conclusion}。"
        
        # 提取关键因素
        factors = []
        
        if "goals" in context:
            factors.append("目标优先级")
            
        if "values" in context:
            factors.append("价值观一致性")
            
        if "user" in context and "preferences" in context["user"]:
            factors.append("用户偏好")
            
        if "current_state" in context:
            factors.append("当前活动状态")
        
        # 如果没有足够的因素，添加通用因素
        if len(factors) < 2:
            factors.extend(["可行性", "潜在收益", "风险评估"])
        
        # 随机选择一个结论
        conclusions = [
            "最优决策应该平衡短期收益和长期目标",
            "应优先考虑与核心价值观一致的选项",
            "在不确定环境下应该采取渐进式决策策略",
            "当前情境下需要权衡风险和收益",
            "基于历史决策效果，应该采用自适应决策模型"
        ]
        conclusion = random.choice(conclusions)
        
        # 填充模板
        content = reasoning_template.format(
            query=query,
            factor_count=len(factors),
            factors="、".join(factors),
            conclusion=conclusion
        )
        
        return content
    
    def _generate_reasoning_conclusions(self, query: str, context: Dict[str, Any]) -> List[str]:
        """
        生成推理结论
        
        Args:
            query: 查询内容
            context: 上下文信息
            
        Returns:
            结论列表
        """
        # 基础结论
        base_conclusions = [
            "决策过程应该是多步骤的，包括选项生成、评估和选择",
            "有效决策需要平衡直觉和分析思维",
            "决策应该考虑到不确定性和未知因素"
        ]
        
        # 根据上下文添加特定结论
        specific_conclusions = []
        
        if "goals" in context and context["goals"]:
            specific_conclusions.append("决策应当服务于已设定的优先级最高的目标")
            
        if "values" in context and context["values"]:
            top_values = sorted(context["values"].items(), key=lambda x: x[1], reverse=True)[:2]
            if top_values:
                value_names = [v[0] for v in top_values]
                specific_conclusions.append(f"决策应当与核心价值观({', '.join(value_names)})保持一致")
        
        if "current_state" in context and "activity" in context["current_state"]:
            activity = context["current_state"]["activity"]
            specific_conclusions.append(f"在{activity}活动中的决策应当考虑活动特定的需求和约束")
        
        # 合并并返回所有结论
        all_conclusions = specific_conclusions + base_conclusions
        return all_conclusions[:3]  # 最多返回3个结论
    
    def _get_decisions(self, count: int = 5, category: str = None) -> Dict[str, Any]:
        """
        获取决策历史
        
        Args:
            count: 返回的决策数量
            category: 特定类别，如果为None则返回所有类别
            
        Returns:
            决策结果
        """
        # 筛选指定类别的决策
        if category:
            filtered_decisions = [d for d in self.decision_history if d.get("selected", {}).get("category") == category]
        else:
            filtered_decisions = self.decision_history
        
        # 按时间排序，最新的在前
        sorted_decisions = sorted(filtered_decisions, key=lambda d: d.get("timestamp", 0), reverse=True)
        
        # 返回指定数量
        result_decisions = sorted_decisions[:count]
        
        return {
            "success": True,
            "decisions": result_decisions,
            "total": len(filtered_decisions),
            "returned": len(result_decisions)
        }
    
    def _trigger_autonomous_decision(self):
        """触发自主决策过程"""
        # 只有当前没有进行决策时才启动
        if not self.decision_state["is_deciding"]:
            try:
                # 获取当前上下文
                context = self.life_context.get_all_context()
                
                # 确保上下文是字典类型
                if not isinstance(context, dict):
                    logger.warning_status(f"上下文不是字典类型: {type(context)}")
                    context = {}
                
                # 做出决策
                self._make_contextual_decision(context)
            except Exception as e:
                logger.error_status(f"触发自主决策失败: {str(e)}")
    
    def _on_decision_request(self, event_data: Dict[str, Any]):
        """
        决策请求事件处理器
        
        Args:
            event_data: 事件数据
        """
        try:
            # 提取请求数据
            options = event_data.get("options", [])
            
            # 获取上下文
            context = event_data.get("context")
            if not context:
                context = self.life_context.get_all_context()
                
            # 确保上下文是字典类型
            if not isinstance(context, dict):
                logger.warning_status(f"上下文不是字典类型: {type(context)}")
                context = {}
            
            model = event_data.get("model", "")
            
            # 确定决策模型
            if not model:
                model = self._determine_decision_type(options, context)
            
            # 做出决策
            decision_result = self._make_decision(options, context, model)
            
            # 发布决策结果事件
            self.event_bus.publish("decision_result", {
                "request_id": event_data.get("request_id"),
                "result": decision_result
            })
        except Exception as e:
            logger.error_status(f"处理决策请求失败: {str(e)}")
            # 发布错误事件
            self.event_bus.publish("decision_error", {
                "request_id": event_data.get("request_id"),
                "error": str(e)
            })
    
    def _on_goal_set(self, event_data: Dict[str, Any]):
        """
        目标设置事件处理器
        
        Args:
            event_data: 事件数据
        """
        try:
            # 提取目标数据
            goal = event_data.get("goal", {})
            
            # 高优先级目标可能触发决策重评估
            if goal.get("priority", 0) >= 8:
                # 获取当前上下文
                context = self.life_context.get_all_context()
                
                # 确保上下文是字典类型
                if not isinstance(context, dict):
                    logger.warning_status(f"上下文不是字典类型: {type(context)}")
                    context = {}
                
                # 触发目标导向决策
                self.event_bus.publish("schedule_decision", {
                    "module_id": self.module_id,
                    "context": context,
                    "model": "goal",
                    "delay": random.randint(5, 15)  # 5-15秒后执行
                })
        except Exception as e:
            logger.error_status(f"处理目标设置事件失败: {str(e)}")
    
    def _on_context_changed(self, event_data: Dict[str, Any]):
        """
        上下文变化事件处理器
        
        Args:
            event_data: 事件数据
        """
        try:
            # 提取变化数据
            changes = event_data.get("changes", {})
            
            # 重大变化可能触发决策重评估
            significant_changes = ["goals", "values", "user_preferences", "activity"]
            
            if any(change in changes for change in significant_changes):
                # 有20%的概率触发决策重评估
                if random.random() < 0.2:
                    # 获取当前上下文
                    context = self.life_context.get_all_context()
                    
                    # 确保上下文是字典类型
                    if not isinstance(context, dict):
                        logger.warning_status(f"上下文不是字典类型: {type(context)}")
                        context = {}
                    
                    # 触发适应性决策
                    self.event_bus.publish("schedule_decision", {
                        "module_id": self.module_id,
                        "context": context,
                        "model": "adaptive",
                        "delay": random.randint(10, 30)  # 10-30秒后执行
                    })
        except Exception as e:
            logger.error_status(f"处理上下文变化事件失败: {str(e)}")
    
    def _load_decision_history(self):
        """加载决策历史"""
        history_path = os.path.join(root_dir, "data", "memory", "decisions", f"{self.module_id}_history.json")
        
        try:
            if os.path.exists(history_path):
                with open(history_path, "r", encoding="utf-8") as f:
                    self.decision_history = json.load(f)
                logger.info(f"已加载决策历史: {len(self.decision_history)}条记录")
        except Exception as e:
            logger.error_status(f"加载决策历史失败: {str(e)}")
            self.decision_history = []
    
    def _save_decision_history(self):
        """保存决策历史"""
        history_dir = os.path.join(root_dir, "data", "memory", "decisions")
        os.makedirs(history_dir, exist_ok=True)
        
        history_path = os.path.join(history_dir, f"{self.module_id}_history.json")
        
        try:
            with open(history_path, "w", encoding="utf-8") as f:
                json.dump(self.decision_history, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存决策历史: {len(self.decision_history)}条记录")
        except Exception as e:
            logger.error_status(f"保存决策历史失败: {str(e)}")

    def _initialize_module(self) -> bool:
        """
        初始化模块内部实现
        
        Returns:
            初始化是否成功
        """
        try:
            # 加载决策历史
            self._load_decision_history()
            
            # 启动决策线程
            self.decision_thread_active = True
            self.decision_thread = threading.Thread(target=self._background_decision, daemon=True)
            self.decision_thread.start()
            
            logger.success(f"自主决策模块 {self.module_id} 内部初始化完成")
            return True
        except Exception as e:
            logger.error_status(f"自主决策模块内部初始化失败: {str(e)}")
            return False
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据的内部实现
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        # 处理请求类型
        request_type = input_data.get("request_type", "")
        
        if request_type == "make_decision":
            # 生成决策
            context = input_data.get("context", {})
            options = input_data.get("options", [])
            model = input_data.get("model", "utility")
            return self._make_decision(options, context, model)
            
        elif request_type == "evaluate_decision":
            # 评估决策
            decision = input_data.get("decision", {})
            if not decision:
                return {"success": False, "error": "没有提供决策"}
            return self._evaluate_decision(decision)
            
        elif request_type == "get_decisions":
            # 获取决策历史
            count = input_data.get("count", 5)
            category = input_data.get("category", None)
            return self._get_decisions(count, category)
            
        else:
            # 默认行为：基于当前上下文做出决策
            context = self.life_context.get_all_context()
            return self._make_contextual_decision(context)
    
    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态的内部实现
        
        Args:
            context: 上下文信息
            
        Returns:
            更新是否成功
        """
        try:
            # 更新决策状态
            idle_time = time.time() - self.decision_state["last_decision_time"]
            if idle_time > 600:  # 10分钟没有决策
                # 可以进行一些维护操作
                pass
            
            # 清理过长的历史记录
            if len(self.decision_history) > self.max_history_size:
                self.decision_history = self.decision_history[-self.max_history_size:]
            
            return True
            
        except Exception as e:
            logger.error_status(f"更新自主决策模块状态失败: {str(e)}")
            return False
    
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块状态的内部实现
        
        Returns:
            模块状态
        """
        return {
            "decision_state": self.decision_state,
            "decision_history_count": len(self.decision_history),
            "last_decision_time": self.decision_state["last_decision_time"],
            "is_deciding": self.decision_state["is_deciding"]
        }
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        加载模块状态的内部实现
        
        Args:
            state: 模块状态
            
        Returns:
            加载是否成功
        """
        try:
            if "decision_state" in state:
                self.decision_state.update(state["decision_state"])
            
            return True
        except Exception as e:
            logger.error_status(f"加载自主决策模块状态失败: {str(e)}")
            return False
    
    def _shutdown_module(self) -> bool:
        """
        关闭模块的内部实现
        
        Returns:
            关闭是否成功
        """
        try:
            # 停止决策线程
            self.decision_thread_active = False
            if self.decision_thread and self.decision_thread.is_alive():
                self.decision_thread.join(timeout=2.0)
            
            # 保存数据
            self._save_decision_history()
            
            return True
            
        except Exception as e:
            logger.error_status(f"关闭自主决策模块失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态信息
        """
        status = super().get_status()
        status.update({
            "decision_count": len(self.decision_history),
            "last_decision_time": self.decision_state["last_decision_time"],
            "is_deciding": self.decision_state["is_deciding"]
        })
        return status

    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化模块
        
        Args:
            config: 配置信息
            
        Returns:
            初始化是否成功
        """
        if config:
            self.config.update(config)
        
        # 订阅相关事件
        self.event_bus.subscribe("decision_request", self._on_decision_request)
        self.event_bus.subscribe("goal_set", self._on_goal_set)
        self.event_bus.subscribe("context_changed", self._on_context_changed)
        
        # 初始化模块
        return self._initialize_module()
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        return self._process_input(input_data)
    
    def reason(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        推理
        
        Args:
            input_data: 输入数据
            
        Returns:
            推理结果
        """
        # 决策推理
        context = input_data.get("context", {})
        query = input_data.get("query", "")
        
        return self._perform_decision_reasoning(query, context)
    
    def decide(self, options: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        决策
        
        Args:
            options: 可选项列表
            
        Returns:
            决策结果
        """
        # 获取当前上下文
        context = self.life_context.get_all_context()
        
        # 使用最适合的决策模型
        decision_type = self._determine_decision_type(options, context)
        
        return self._make_decision(options, context, decision_type)
    
    def update(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
            
        Returns:
            更新是否成功
        """
        return self._update_module(context)
    
    def shutdown(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        return self._shutdown_module()

    def _determine_decision_type(self, options: List[Dict[str, Any]], context: Dict[str, Any]) -> str:
        """
        确定最适合的决策类型
        
        Args:
            options: 决策选项
            context: 上下文信息
            
        Returns:
            决策类型
        """
        # 检查是否有明确的高优先级目标
        goals = context.get("goals", [])
        if goals and any(g.get("priority", 0) > 7 for g in goals):
            return "goal"
            
        # 检查是否有风险选项
        if any("risk" in option for option in options):
            return "risk"
            
        # 检查是否涉及价值观
        if context.get("values", {}):
            return "value"
            
        # 默认使用效用决策
        return "utility"
    
    def _background_decision(self):
        """后台决策线程"""
        while self.decision_thread_active:
            try:
                # 检查是否需要做出主动决策
                if (not self.decision_state["is_deciding"] and 
                    time.time() - self.decision_state["last_decision_time"] > 1800):  # 30分钟无决策
                    
                    # 有10%的概率启动自主决策
                    if random.random() < 0.1:
                        self._trigger_autonomous_decision()
                
                # 等待一段时间
                time.sleep(60)  # 60秒检查一次
                
            except Exception as e:
                logger.error_status(f"后台决策线程异常: {str(e)}")
                time.sleep(10)  # 出错后短暂等待
    
    def _make_decision(self, options: List[Dict[str, Any]], context: Dict[str, Any], model: str = "utility") -> Dict[str, Any]:
        """
        生成决策
        
        Args:
            options: 可选项列表
            context: 上下文信息
            model: 决策模型
            
        Returns:
            决策结果
        """
        # 记录决策状态
        self.decision_state["is_deciding"] = True
        self.decision_state["current_decision"] = model
        self.decision_state["last_decision_time"] = time.time()
        
        # 如果没有选项，生成默认选项
        if not options or len(options) == 0:
            options = self._generate_default_options(context)
        
        # 使用指定的决策模型
        if model in self.decision_models:
            decision_result = self.decision_models[model](options, context)
        else:
            # 默认使用效用决策
            decision_result = self._utility_based_decision(options, context)
        
        # 创建决策记录
        decision = {
            "id": f"decision_{int(time.time())}",
            "model": model,
            "options": options,
            "selected": decision_result["decision"],
            "reasoning": decision_result["reasoning"],
            "confidence": decision_result["confidence"],
            "timestamp": time.time(),
            "context_snapshot": self._create_context_snapshot(context)
        }
        
        # 添加到决策历史
        self.decision_history.append(decision)
        
        # 发布决策事件
        self.event_bus.publish("decision_made", {
            "module_id": self.module_id,
            "decision": decision
        })
        
        # 重置决策状态
        self.decision_state["is_deciding"] = False
        self.decision_state["current_decision"] = None
        
        return {
            "success": True,
            "decision": decision
        }
    
    def _utility_based_decision(self, options: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于效用的决策
        
        Args:
            options: 可选项列表
            context: 上下文信息
            
        Returns:
            决策结果
        """
        # 效用评估
        evaluations = []
        for option in options:
            # 计算总效用
            utility = self._calculate_utility(option, context)
            
            evaluations.append({
                "option": option,
                "utility": utility,
                "reasoning": f"总效用值: {utility:.2f}"
            })
        
        # 按效用排序
        evaluations.sort(key=lambda x: x["utility"], reverse=True)
        
        # 选择效用最高的选项
        selected = evaluations[0]
        
        return {
            "decision": selected["option"],
            "reasoning": selected["reasoning"],
            "alternatives": [e["option"] for e in evaluations[1:3]] if len(evaluations) > 1 else [],
            "confidence": min(1.0, selected["utility"] / 10.0)  # 归一化为0-1的置信度
        }
    
    def _risk_based_decision(self, options: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于风险的决策
        
        Args:
            options: 可选项列表
            context: 上下文信息
            
        Returns:
            决策结果
        """
        # 风险评估
        risk_evaluations = []
        for option in options:
            # 评估风险和收益
            risk = self._assess_risk(option, context)
            benefit = self._assess_benefit(option, context)
            
            # 计算风险调整后的收益
            risk_adjusted_benefit = benefit * (1 - risk)
            
            risk_evaluations.append({
                "option": option,
                "risk": risk,
                "benefit": benefit,
                "risk_adjusted_benefit": risk_adjusted_benefit,
                "reasoning": f"风险评估: {risk:.2f}, 收益评估: {benefit:.2f}, 风险调整后收益: {risk_adjusted_benefit:.2f}"
            })
        
        # 按风险调整后的收益排序
        risk_evaluations.sort(key=lambda x: x["risk_adjusted_benefit"], reverse=True)
        
        # 选择风险调整后收益最高的选项
        selected = risk_evaluations[0]
        
        return {
            "decision": selected["option"],
            "reasoning": selected["reasoning"],
            "alternatives": [e["option"] for e in risk_evaluations[1:3]] if len(risk_evaluations) > 1 else [],
            "confidence": min(1.0, selected["risk_adjusted_benefit"] / 10.0)
        }
    
    def _value_based_decision(self, options: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于价值观的决策
        
        Args:
            options: 可选项列表
            context: 上下文信息
            
        Returns:
            决策结果
        """
        # 获取价值观
        values = context.get("values", {})
        if not values:
            # 如果没有价值观信息，退回到效用决策
            return self._utility_based_decision(options, context)
        
        # 价值观评估
        value_evaluations = []
        for option in options:
            # 计算价值观一致性
            value_alignment = self._calculate_value_alignment(option, values)
            
            value_evaluations.append({
                "option": option,
                "value_alignment": value_alignment["score"],
                "reasoning": value_alignment["reasoning"]
            })
        
        # 按价值观一致性排序
        value_evaluations.sort(key=lambda x: x["value_alignment"], reverse=True)
        
        # 选择价值观一致性最高的选项
        selected = value_evaluations[0]
        
        return {
            "decision": selected["option"],
            "reasoning": selected["reasoning"],
            "alternatives": [e["option"] for e in value_evaluations[1:3]] if len(value_evaluations) > 1 else [],
            "confidence": min(1.0, selected["value_alignment"] / 10.0)
        }
    
    def _goal_based_decision(self, options: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于目标的决策
        
        Args:
            options: 可选项列表
            context: 上下文信息
            
        Returns:
            决策结果
        """
        # 获取目标
        goals = context.get("goals", [])
        if not goals:
            # 如果没有目标信息，退回到效用决策
            return self._utility_based_decision(options, context)
        
        # 目标评估
        goal_evaluations = []
        for option in options:
            # 计算目标一致性
            goal_alignment = self._calculate_goal_alignment(option, goals)
            
            goal_evaluations.append({
                "option": option,
                "goal_alignment": goal_alignment["score"],
                "reasoning": goal_alignment["reasoning"]
            })
        
        # 按目标一致性排序
        goal_evaluations.sort(key=lambda x: x["goal_alignment"], reverse=True)
        
        # 选择目标一致性最高的选项
        selected = goal_evaluations[0]
        
        return {
            "decision": selected["option"],
            "reasoning": selected["reasoning"],
            "alternatives": [e["option"] for e in goal_evaluations[1:3]] if len(goal_evaluations) > 1 else [],
            "confidence": min(1.0, selected["goal_alignment"] / 10.0)
        }
    
    def _adaptive_decision(self, options: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        适应性决策
        
        Args:
            options: 可选项列表
            context: 上下文信息
            
        Returns:
            决策结果
        """
        # 获取历史决策反馈
        decision_feedback = context.get("decision_feedback", {})
        
        # 如果没有足够的历史反馈，使用混合决策
        if not decision_feedback or len(decision_feedback) < 3:
            # 综合多种决策模型的结果
            utility_result = self._utility_based_decision(options, context)
            risk_result = self._risk_based_decision(options, context)
            value_result = self._value_based_decision(options, context)
            goal_result = self._goal_based_decision(options, context)
            
            # 合并结果
            all_results = [utility_result, risk_result, value_result, goal_result]
            
            # 选择置信度最高的结果
            all_results.sort(key=lambda x: x["confidence"], reverse=True)
            best_result = all_results[0]
            
            return {
                "decision": best_result["decision"],
                "reasoning": f"适应性决策选择了置信度最高的结果: {best_result['reasoning']}",
                "alternatives": best_result["alternatives"],
                "confidence": best_result["confidence"]
            }
        
        # 分析历史反馈，确定最有效的决策模型
        model_performance = self._analyze_decision_feedback(decision_feedback)
        best_model = max(model_performance.items(), key=lambda x: x[1])[0]
        
        # 使用历史表现最好的模型
        return self.decision_models[best_model](options, context)

    def _calculate_utility(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """
        计算选项的效用
        
        Args:
            option: 选项
            context: 上下文
            
        Returns:
            效用值
        """
        # 基础效用
        base_utility = option.get("base_utility", 5.0)
        
        # 用户偏好调整
        user_preferences = context.get("user", {}).get("preferences", {})
        preference_bonus = 0
        
        if "category" in option and option["category"] in user_preferences:
            preference_level = user_preferences[option["category"]]
            preference_bonus = preference_level * 2  # 最大加分2分
        
        # 当前情境调整
        context_state = context.get("current_state", {})
        context_bonus = 0
        
        if "context_relevance" in option:
            relevance = option["context_relevance"]
            context_bonus = relevance * 1.5  # 最大加分1.5分
        
        # 创新性调整
        novelty_bonus = 0
        if option.get("is_novel", False):
            novelty_bonus = 1.0
        
        # 总效用
        total_utility = base_utility + preference_bonus + context_bonus + novelty_bonus
        
        # 确保效用在0-10范围内
        return max(0.0, min(10.0, total_utility))
    
    def _assess_risk(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """
        评估选项风险
        
        Args:
            option: 选项
            context: 上下文
            
        Returns:
            风险值（0-1，1为最高风险）
        """
        # 基础风险
        base_risk = option.get("risk", 0.3)  # 默认中低风险
        
        # 风险因素
        # 1. 不确定性
        uncertainty = option.get("uncertainty", 0.2)
        
        # 2. 潜在负面影响
        negative_impact = option.get("negative_impact", 0.2)
        
        # 3. 用户风险偏好
        user_risk_tolerance = context.get("user", {}).get("risk_tolerance", 0.5)
        risk_adjustment = 0.5 - user_risk_tolerance  # 风险偏好低会增加风险评估
        
        # 综合风险计算
        total_risk = base_risk + (uncertainty * 0.3) + (negative_impact * 0.4) + (risk_adjustment * 0.2)
        
        # 确保风险在0-1范围内
        return max(0.0, min(1.0, total_risk))
    
    def _assess_benefit(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """
        评估选项收益
        
        Args:
            option: 选项
            context: 上下文
            
        Returns:
            收益值（0-10，10为最高收益）
        """
        # 基础收益
        base_benefit = option.get("benefit", 5.0)
        
        # 长期价值
        long_term_value = option.get("long_term_value", 0.5) * 2
        
        # 用户满意度预估
        user_satisfaction = option.get("user_satisfaction", 0.7) * 3
        
        # 总收益
        total_benefit = base_benefit + long_term_value + user_satisfaction
        
        # 确保收益在0-10范围内
        return max(0.0, min(10.0, total_benefit))
    
    def _calculate_value_alignment(self, option: Dict[str, Any], values: Dict[str, float]) -> Dict[str, Any]:
        """
        计算选项与价值观的一致性
        
        Args:
            option: 选项
            values: 价值观
            
        Returns:
            价值观一致性结果
        """
        # 初始化得分和理由
        alignment_score = 5.0  # 默认中等一致性
        reasons = []
        
        # 考察每个价值观维度
        if "value_impacts" in option:
            value_impacts = option["value_impacts"]
            
            for value, strength in values.items():
                if value in value_impacts:
                    impact = value_impacts[value]
                    # 正面影响
                    if impact > 0:
                        alignment_score += (impact * strength * 2)
                        reasons.append(f"增强{value}价值观")
                    # 负面影响
                    elif impact < 0:
                        alignment_score -= (abs(impact) * strength * 2)
                        reasons.append(f"削弱{value}价值观")
        
        # 如果没有明确的价值观影响信息
        else:
            # 尝试从选项类型推断价值观影响
            option_type = option.get("option_type", "")
            
            if option_type == "helpful" and "helpfulness" in values:
                alignment_score += (values["helpfulness"] * 3)
                reasons.append("符合助人价值观")
                
            if option_type == "creative" and "creativity" in values:
                alignment_score += (values["creativity"] * 3)
                reasons.append("符合创造性价值观")
                
            if option_type == "ethical" and "ethics" in values:
                alignment_score += (values["ethics"] * 3)
                reasons.append("符合伦理价值观")
        
        # 确保得分在1-10范围内
        alignment_score = max(1.0, min(10.0, alignment_score))
        
        return {
            "score": alignment_score,
            "reasoning": "、".join(reasons) if reasons else "价值观一致性评估"
        }
    
    def _calculate_goal_alignment(self, option: Dict[str, Any], goals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算选项与目标的一致性
        
        Args:
            option: 选项
            goals: 目标列表
            
        Returns:
            目标一致性结果
        """
        # 初始化得分和理由
        alignment_score = 5.0  # 默认中等一致性
        reasons = []
        
        # 计算与每个目标的一致性
        for goal in goals:
            goal_content = goal.get("content", "").lower()
            goal_priority = goal.get("priority", 5) / 10.0  # 归一化为0-1
            
            # 检查选项描述与目标的关键词匹配
            option_description = option.get("description", "").lower()
            if not option_description and "content" in option:
                option_description = option.get("content", "").lower()
            
            # 提取目标关键词
            goal_keywords = [word for word in goal_content.split() if len(word) > 2]
            
            # 计算匹配度
            matches = 0
            for keyword in goal_keywords:
                if keyword in option_description:
                    matches += 1
            
            # 计算匹配率
            if goal_keywords:
                match_rate = matches / len(goal_keywords)
                # 根据匹配率和目标优先级调整得分
                if match_rate > 0:
                    adjustment = match_rate * goal_priority * 5  # 最大加分5分
                    alignment_score += adjustment
                    reasons.append(f"与目标'{goal_content}'相符")
        
        # 确保得分在1-10范围内
        alignment_score = max(1.0, min(10.0, alignment_score))
        
        return {
            "score": alignment_score,
            "reasoning": "、".join(reasons) if reasons else "目标一致性评估"
        }
    
    def _create_context_snapshot(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建上下文快照
        
        Args:
            context: 上下文信息
            
        Returns:
            上下文快照
        """
        # 提取关键上下文信息，避免存储过大的数据
        snapshot = {}
        
        if "current_state" in context:
            snapshot["current_state"] = context["current_state"]
            
        if "goals" in context:
            snapshot["goals"] = [
                {"id": g.get("id"), "content": g.get("content"), "priority": g.get("priority")}
                for g in context["goals"]
            ]
            
        if "values" in context:
            snapshot["values"] = context["values"]
            
        if "user" in context:
            snapshot["user"] = {
                "preferences": context["user"].get("preferences", {}),
                "risk_tolerance": context["user"].get("risk_tolerance", 0.5)
            }
        
        return snapshot

def get_instance(module_id: str, config: Dict[str, Any] = None) -> AutonomousDecision:
    """
    获取自主决策模块实例
    
    Args:
        module_id: 模块ID
        config: 配置信息
        
    Returns:
        自主决策模块实例
    """
    return AutonomousDecision(module_id, config) 