#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自适应对话策略模块 - Adaptive Dialog Strategy Module

该模块负责根据用户状态和交互历史动态调整对话策略，包括：
1. 用户状态跟踪：情绪状态识别、注意力水平评估、兴趣点检测
2. 对话策略选择：多策略定义与管理、上下文敏感策略选择、策略效果评估
3. 动态提示词调整：提示词模板系统、个性化提示词生成、提示词效果反馈

作者: Claude
创建日期: 2024-10-12
版本: 1.0
"""

import os
import sys
import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random
from typing import Dict, Any, List, Optional, Tuple, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "..", ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.base.cognitive_interface import ICognitionModule
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from core.prompt_manager import get_instance as get_prompt_manager

# 配置日志
logger = get_unified_logger("adaptive_dialog")

class AdaptiveDialogModule(CognitiveModuleBase, ICognitionModule):
    """
    自适应对话策略模块
    
    负责根据用户状态和交互历史动态调整对话策略，提供个性化的交互体验。
    """
    
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(AdaptiveDialogModule, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化自适应对话策略模块
        
        Args:
            config: 模块配置
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        super().__init__(
            module_id="adaptive_dialog",
            module_name="自适应对话策略模块",
            module_type="cognition"
        )
        
        self._initialized = True
        self.config = config or {}
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        self.prompt_manager = get_prompt_manager()
        
        # 对话策略定义
        self.dialog_strategies = {
            "informative": {
                "description": "信息型对话策略，注重提供准确、全面的信息",
                "parameters": {
                    "detail_level": 0.8,
                    "formality": 0.7,
                    "creativity": 0.3,
                    "emotion_expression": 0.2,
                    "personal_touch": 0.3
                }
            },
            "emotional": {
                "description": "情感型对话策略，注重情感共鸣和情感表达",
                "parameters": {
                    "detail_level": 0.5,
                    "formality": 0.3,
                    "creativity": 0.6,
                    "emotion_expression": 0.9,
                    "personal_touch": 0.8
                }
            },
            "casual": {
                "description": "随意型对话策略，轻松友好，类似朋友间的对话",
                "parameters": {
                    "detail_level": 0.4,
                    "formality": 0.2,
                    "creativity": 0.7,
                    "emotion_expression": 0.7,
                    "personal_touch": 0.9
                }
            },
            "professional": {
                "description": "专业型对话策略，注重准确性和专业性",
                "parameters": {
                    "detail_level": 0.9,
                    "formality": 0.9,
                    "creativity": 0.2,
                    "emotion_expression": 0.1,
                    "personal_touch": 0.2
                }
            },
            "creative": {
                "description": "创意型对话策略，注重创意表达和思维发散",
                "parameters": {
                    "detail_level": 0.6,
                    "formality": 0.4,
                    "creativity": 0.9,
                    "emotion_expression": 0.6,
                    "personal_touch": 0.5
                }
            }
        }
        
        # 用户状态跟踪
        self.user_states = {}
        
        # 策略使用历史
        self.strategy_history = []
        
        # 当前活跃策略
        self.active_strategy = "casual"  # 默认使用随意型对话策略
        
        # 策略切换冷却时间(秒)
        self.strategy_cooldown = self.config.get("strategy_cooldown", 300)  # 默认5分钟
        self.last_strategy_change = 0
        
        # 策略效果评估窗口
        self.evaluation_window = self.config.get("evaluation_window", 10)
        
        # 订阅事件
        self.event_bus.subscribe("user.message", self._handle_user_message)
        self.event_bus.subscribe("user.reaction", self._handle_user_reaction)
        self.event_bus.subscribe("dialog.pre_response", self._handle_pre_response)
        
        logger.success("自适应对话策略模块初始化完成")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态信息
        """
        return {
            "id": self.module_id,
            "name": self.module_name,
            "type": self.module_type,
            "active": self.is_active,
            "active_strategy": self.active_strategy,
            "strategies_count": len(self.dialog_strategies),
            "user_states_count": len(self.user_states),
            "strategy_history_count": len(self.strategy_history)
        }
    
    def _initialize_module(self) -> bool:
        """
        初始化模块内部组件
        
        Returns:
            初始化是否成功
        """
        try:
            # 加载策略历史和用户状态
            self._load_data()
            
            # 从生命上下文中加载对话策略偏好
            context = self.life_context.get_context()
            if "cognition" in context and "dialog_strategy_preference" in context["cognition"]:
                self.active_strategy = context["cognition"]["dialog_strategy_preference"]
            
            logger.success(f"自适应对话策略模块初始化成功，当前策略: {self.active_strategy}")
            return True
        except Exception as e:
            logger.error_status(f"自适应对话策略模块初始化失败: {e}")
            return False
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据，包含请求信息
            
        Returns:
            处理结果
        """
        try:
            if "request_type" not in input_data:
                return {"success": False, "error": "缺少请求类型"}
            
            request_type = input_data["request_type"]
            
            if request_type == "get_strategy":
                user_id = input_data.get("user_id")
                context = input_data.get("context", {})
                
                strategy = self.get_current_strategy(user_id)
                return {
                    "success": True,
                    "strategy": strategy
                }
            
            elif request_type == "set_strategy":
                if "strategy" not in input_data:
                    return {"success": False, "error": "缺少策略名称"}
                
                strategy = input_data["strategy"]
                user_id = input_data.get("user_id")
                force = input_data.get("force", False)
                
                success = self.set_strategy(strategy, user_id, force)
                return {
                    "success": success,
                    "active_strategy": self.active_strategy
                }
            
            elif request_type == "get_user_state":
                if "user_id" not in input_data:
                    return {"success": False, "error": "缺少用户ID"}
                
                user_id = input_data["user_id"]
                state = self.get_user_state(user_id)
                return {
                    "success": True,
                    "user_state": state
                }
            
            elif request_type == "generate_prompt":
                if "template_key" not in input_data:
                    return {"success": False, "error": "缺少模板键名"}
                
                template_key = input_data["template_key"]
                user_id = input_data.get("user_id")
                parameters = input_data.get("parameters", {})
                
                prompt = self.generate_adaptive_prompt(template_key, user_id, parameters)
                return {
                    "success": True,
                    "prompt": prompt
                }
            
            else:
                return {"success": False, "error": f"未知的请求类型: {request_type}"}
        
        except Exception as e:
            logger.error_status(f"处理请求失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
            
        Returns:
            更新是否成功
        """
        try:
            # 更新模块配置
            if "config" in context:
                new_config = context["config"]
                if "strategy_cooldown" in new_config:
                    self.strategy_cooldown = new_config["strategy_cooldown"]
                if "evaluation_window" in new_config:
                    self.evaluation_window = new_config["evaluation_window"]
            
            # 保存数据
            self._save_data()
            
            return True
        except Exception as e:
            logger.error_status(f"更新模块状态失败: {e}")
            return False
    
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块状态，用于持久化
        
        Returns:
            模块状态数据
        """
        return {
            "active_strategy": self.active_strategy,
            "user_states": self.user_states,
            "strategy_history": self.strategy_history,
            "last_strategy_change": self.last_strategy_change
        }
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        加载模块状态
        
        Args:
            state: 模块状态数据
            
        Returns:
            加载是否成功
        """
        try:
            if "active_strategy" in state:
                self.active_strategy = state["active_strategy"]
            if "user_states" in state:
                self.user_states = state["user_states"]
            if "strategy_history" in state:
                self.strategy_history = state["strategy_history"]
            if "last_strategy_change" in state:
                self.last_strategy_change = state["last_strategy_change"]
            
            return True
        except Exception as e:
            logger.error_status(f"加载模块状态失败: {e}")
            return False
    
    def _shutdown_module(self) -> bool:
        """
        关闭模块，执行清理操作
        
        Returns:
            关闭是否成功
        """
        try:
            # 保存模块数据
            self._save_data()
            
            # 取消事件订阅
            self.event_bus.unsubscribe("user.message", self._handle_user_message)
            self.event_bus.unsubscribe("user.reaction", self._handle_user_reaction)
            self.event_bus.unsubscribe("dialog.pre_response", self._handle_pre_response)
            
            logger.info("自适应对话策略模块已关闭")
            return True
        except Exception as e:
            logger.error_status(f"关闭自适应对话策略模块失败: {e}")
            return False
    
    def get_current_strategy(self, user_id: str = None) -> Dict[str, Any]:
        """
        获取当前对话策略
        
        Args:
            user_id: 用户ID，如果指定则返回针对该用户的策略
            
        Returns:
            当前对话策略信息
        """
        strategy_id = self.active_strategy
        
        # 如果指定了用户ID，检查是否有针对该用户的特定策略
        if user_id and user_id in self.user_states:
            user_state = self.user_states[user_id]
            if "preferred_strategy" in user_state:
                strategy_id = user_state["preferred_strategy"]
        
        if strategy_id not in self.dialog_strategies:
            strategy_id = "casual"  # 默认为随意型
        
        strategy = self.dialog_strategies[strategy_id].copy()
        strategy["id"] = strategy_id
        
        return strategy
    
    def set_strategy(self, strategy_id: str, user_id: str = None, force: bool = False) -> bool:
        """
        设置对话策略
        
        Args:
            strategy_id: 策略ID
            user_id: 用户ID，如果指定则为该用户设置特定策略
            force: 是否强制设置，忽略冷却时间
            
        Returns:
            设置是否成功
        """
        # 检查策略是否存在
        if strategy_id not in self.dialog_strategies:
            logger.error_status(f"策略不存在: {strategy_id}")
            return False
        
        # 检查冷却时间
        current_time = time.time()
        if not force and (current_time - self.last_strategy_change) < self.strategy_cooldown:
            logger.warning_status(f"策略切换冷却中，剩余{int(self.strategy_cooldown - (current_time - self.last_strategy_change))}秒")
            return False
        
        # 如果指定了用户ID，则为该用户设置特定策略
        if user_id:
            if user_id not in self.user_states:
                self.user_states[user_id] = {}
            
            self.user_states[user_id]["preferred_strategy"] = strategy_id
            logger.info(f"为用户{user_id}设置策略: {strategy_id}")
        else:
            # 否则更新全局策略
            self.active_strategy = strategy_id
            self.last_strategy_change = current_time
            
            # 记录策略变更
            self.strategy_history.append({
                "strategy": strategy_id,
                "timestamp": current_time,
                "reason": "manual"  # 手动设置
            })
            
            logger.info(f"全局策略已更新: {strategy_id}")
        
        # 发布策略变更事件
        self.event_bus.publish("dialog.strategy_changed", {
            "strategy_id": strategy_id,
            "user_id": user_id,
            "timestamp": current_time
        })
        
        return True
    
    def get_user_state(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户状态
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户状态信息
        """
        if user_id not in self.user_states:
            return {}
        
        return self.user_states[user_id]
    
    def update_user_state(self, user_id: str, state_update: Dict[str, Any]) -> bool:
        """
        更新用户状态
        
        Args:
            user_id: 用户ID
            state_update: 状态更新信息
            
        Returns:
            更新是否成功
        """
        if user_id not in self.user_states:
            self.user_states[user_id] = {}
        
        # 更新用户状态
        for key, value in state_update.items():
            self.user_states[user_id][key] = value
        
        # 更新时间戳
        self.user_states[user_id]["last_updated"] = time.time()
        
        return True
    
    def track_user_emotion(self, user_id: str, message: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        跟踪用户情绪状态
        
        Args:
            user_id: 用户ID
            message: 用户消息
            metadata: 元数据，可能包含情绪分析结果
            
        Returns:
            情绪状态信息
        """
        # 初始化情绪状态
        emotion_state = {
            "primary_emotion": "neutral",
            "emotion_intensity": 0.0,
            "secondary_emotions": [],
            "timestamp": time.time()
        }
        
        # 使用元数据中的情绪分析结果(如果有)
        if metadata and "emotion_analysis" in metadata:
            emotion_analysis = metadata["emotion_analysis"]
            if "primary_emotion" in emotion_analysis:
                emotion_state["primary_emotion"] = emotion_analysis["primary_emotion"]
            if "intensity" in emotion_analysis:
                emotion_state["emotion_intensity"] = emotion_analysis["intensity"]
            if "secondary_emotions" in emotion_analysis:
                emotion_state["secondary_emotions"] = emotion_analysis["secondary_emotions"]
        else:
            # 简单启发式分析(实际系统应该使用更复杂的情感分析)
            positive_words = ["开心", "高兴", "喜欢", "爱", "好", "棒", "赞", "感谢", "谢谢", "笑"]
            negative_words = ["难过", "伤心", "痛苦", "讨厌", "恨", "烦", "生气", "忧虑", "担心"]
            
            positive_count = sum(1 for word in positive_words if word in message)
            negative_count = sum(1 for word in negative_words if word in message)
            
            total_count = positive_count + negative_count
            if total_count > 0:
                if positive_count > negative_count:
                    emotion_state["primary_emotion"] = "positive"
                    emotion_state["emotion_intensity"] = min(1.0, positive_count / 3)
                elif negative_count > positive_count:
                    emotion_state["primary_emotion"] = "negative"
                    emotion_state["emotion_intensity"] = min(1.0, negative_count / 3)
        
        # 更新用户情绪状态
        if user_id not in self.user_states:
            self.user_states[user_id] = {}
        
        # 如果已有情绪记录，保存为历史
        if "emotion" in self.user_states[user_id]:
            if "emotion_history" not in self.user_states[user_id]:
                self.user_states[user_id]["emotion_history"] = []
            
            self.user_states[user_id]["emotion_history"].append(self.user_states[user_id]["emotion"])
            
            # 限制历史记录长度
            if len(self.user_states[user_id]["emotion_history"]) > 10:
                self.user_states[user_id]["emotion_history"] = self.user_states[user_id]["emotion_history"][-10:]
        
        # 更新当前情绪
        self.user_states[user_id]["emotion"] = emotion_state
        
        return emotion_state
    
    def track_user_attention(self, user_id: str, message: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        跟踪用户注意力水平
        
        Args:
            user_id: 用户ID
            message: 用户消息
            metadata: 元数据，可能包含注意力分析结果
            
        Returns:
            注意力状态信息
        """
        # 初始化注意力状态
        attention_state = {
            "level": 0.5,  # 0-1范围，默认中等注意力
            "focus_topic": None,
            "timestamp": time.time()
        }
        
        # 使用元数据中的注意力分析结果(如果有)
        if metadata and "attention_analysis" in metadata:
            attention_analysis = metadata["attention_analysis"]
            if "level" in attention_analysis:
                attention_state["level"] = attention_analysis["level"]
            if "focus_topic" in attention_analysis:
                attention_state["focus_topic"] = attention_analysis["focus_topic"]
        else:
            # 简单启发式分析
            # 消息长度可能反映注意力(长消息通常表示更高注意力)
            msg_length = len(message)
            if msg_length < 10:
                attention_state["level"] = 0.3  # 短消息可能表示低注意力
            elif msg_length > 50:
                attention_state["level"] = 0.8  # 长消息可能表示高注意力
            
            # 问号可能表示对话题的关注
            if "?" in message:
                attention_state["level"] = min(1.0, attention_state["level"] + 0.2)
        
        # 更新用户注意力状态
        if user_id not in self.user_states:
            self.user_states[user_id] = {}
        
        self.user_states[user_id]["attention"] = attention_state
        
        return attention_state
    
    def detect_interest_points(self, user_id: str, message: str, metadata: Dict[str, Any] = None) -> List[str]:
        """
        检测用户兴趣点
        
        Args:
            user_id: 用户ID
            message: 用户消息
            metadata: 元数据，可能包含兴趣点分析结果
            
        Returns:
            兴趣点列表
        """
        interest_points = []
        
        # 使用元数据中的兴趣点分析结果(如果有)
        if metadata and "interest_points" in metadata:
            interest_points = metadata["interest_points"]
        else:
            # 简单启发式分析(实际系统应该使用更复杂的主题提取)
            # 假设问题句表示兴趣点
            if "?" in message:
                parts = message.split("?")
                for part in parts[:-1]:  # 排除最后一个可能为空的部分
                    part = part.strip()
                    if part:
                        interest_points.append(part)
            
            # 提取包含"想"、"喜欢"、"感兴趣"等表达兴趣的短语
            interest_indicators = ["想", "喜欢", "感兴趣", "关注", "期待"]
            for indicator in interest_indicators:
                if indicator in message:
                    # 提取包含指示词的上下文
                    index = message.find(indicator)
                    start = max(0, index - 10)
                    end = min(len(message), index + 15)
                    context = message[start:end]
                    interest_points.append(context)
        
        # 更新用户兴趣点
        if user_id not in self.user_states:
            self.user_states[user_id] = {}
        
        if "interest_points" not in self.user_states[user_id]:
            self.user_states[user_id]["interest_points"] = []
        
        # 添加新兴趣点
        self.user_states[user_id]["interest_points"].extend(interest_points)
        
        # 限制兴趣点数量
        if len(self.user_states[user_id]["interest_points"]) > 20:
            self.user_states[user_id]["interest_points"] = self.user_states[user_id]["interest_points"][-20:]
        
        # 记录最新兴趣点更新时间
        self.user_states[user_id]["last_interest_update"] = time.time()
        
        return interest_points
    
    def select_optimal_strategy(self, user_id: str) -> str:
        """
        根据用户状态选择最优的对话策略
        
        Args:
            user_id: 用户ID
            
        Returns:
            最优策略ID
        """
        # 如果用户有特定偏好策略，优先使用
        if user_id in self.user_states and "preferred_strategy" in self.user_states[user_id]:
            return self.user_states[user_id]["preferred_strategy"]
        
        # 检查冷却时间
        current_time = time.time()
        if (current_time - self.last_strategy_change) < self.strategy_cooldown:
            return self.active_strategy
        
        # 基于用户状态选择策略
        if user_id in self.user_states:
            user_state = self.user_states[user_id]
            
            # 基于情绪选择策略
            if "emotion" in user_state:
                emotion = user_state["emotion"]
                primary_emotion = emotion.get("primary_emotion", "neutral")
                intensity = emotion.get("emotion_intensity", 0.0)
                
                # 如果情绪强烈，可能需要情感型对话
                if intensity > 0.7:
                    if primary_emotion == "negative":
                        return "emotional"  # 负面情绪时使用情感型对话
                    elif primary_emotion == "positive":
                        return "casual"  # 正面情绪时使用随意型对话
            
            # 基于注意力选择策略
            if "attention" in user_state:
                attention = user_state["attention"]
                level = attention.get("level", 0.5)
                
                # 高注意力可能需要更专业的对话
                if level > 0.8:
                    return "professional"
                # 低注意力可能需要更吸引人的对话
                elif level < 0.3:
                    return "creative"
        
        # 默认返回当前活跃策略
        return self.active_strategy
    
    def generate_adaptive_prompt(self, template_key: str, user_id: str = None, 
                                parameters: Dict[str, Any] = None) -> str:
        """
        生成自适应提示词
        
        Args:
            template_key: 提示词模板键名
            user_id: 用户ID
            parameters: 额外参数
            
        Returns:
            生成的提示词
        """
        # 获取当前策略
        strategy = self.get_current_strategy(user_id)
        strategy_params = strategy.get("parameters", {})
        
        # 初始化默认参数
        default_params = {
            "dialog_style": strategy.get("description", "随意型对话策略"),
            "detail_level": strategy_params.get("detail_level", 0.5),
            "formality": strategy_params.get("formality", 0.5),
            "creativity": strategy_params.get("creativity", 0.5),
            "emotion_expression": strategy_params.get("emotion_expression", 0.5),
            "personal_touch": strategy_params.get("personal_touch", 0.5),
            "user_emotion": "neutral",
            "emotion_intensity": 0.0,
            "attention_level": 0.5,
            "greeting_text": "很高兴见到你！",
            "farewell_text": "希望我们很快再聊！",
            "response_text": "这是一个默认回复。"
        }
        
        # 合并参数
        merged_params = default_params.copy()
        if parameters:
            merged_params.update(parameters)
        
        # 添加用户状态信息
        if user_id and user_id in self.user_states:
            user_state = self.user_states[user_id]
            
            # 添加情绪信息
            if "emotion" in user_state:
                emotion = user_state["emotion"]
                merged_params["user_emotion"] = emotion.get("primary_emotion", "neutral")
                merged_params["emotion_intensity"] = emotion.get("emotion_intensity", 0.0)
            
            # 添加注意力信息
            if "attention" in user_state:
                attention = user_state["attention"]
                merged_params["attention_level"] = attention.get("level", 0.5)
            
            # 添加兴趣点信息
            if "interest_points" in user_state and user_state["interest_points"]:
                interest_points = user_state["interest_points"][-3:]  # 最近3个兴趣点
                merged_params["interest_points"] = ", ".join(interest_points)
        
        # 使用提示词模板生成提示词
        if not hasattr(self.prompt_manager, 'templates'):
            self.prompt_manager.templates = {}
        
        template = self.prompt_manager.templates.get(template_key, "{response_text}")
        
        # 使用字符串format方法替换变量
        try:
            prompt = template.format(**merged_params)
        except KeyError as e:
            # 记录缺少的参数
            logger.warning_status(f"模板缺少参数: {e}")
            # 如果缺少某些参数，添加默认值
            merged_params[str(e).strip("'")] = f"<缺少参数: {e}>"
            prompt = template.format(**merged_params)
        
        return prompt
    
    def evaluate_strategy_effectiveness(self, user_id: str = None) -> Dict[str, Any]:
        """
        评估当前策略的有效性
        
        Args:
            user_id: 用户ID
            
        Returns:
            评估结果
        """
        # 获取策略历史记录中的最近N条记录
        recent_history = self.strategy_history[-self.evaluation_window:] if len(self.strategy_history) >= self.evaluation_window else self.strategy_history
        
        if not recent_history:
            return {
                "effectiveness": 0.5,
                "confidence": 0.0,
                "sample_size": 0
            }
        
        # 统计每个策略的效果
        strategy_stats = {}
        for record in recent_history:
            strategy_id = record.get("strategy")
            feedback = record.get("feedback", 0.0)
            
            if strategy_id not in strategy_stats:
                strategy_stats[strategy_id] = {
                    "count": 0,
                    "total_feedback": 0.0
                }
            
            strategy_stats[strategy_id]["count"] += 1
            strategy_stats[strategy_id]["total_feedback"] += feedback
        
        # 计算当前策略的有效性
        current_strategy = self.active_strategy
        if user_id and user_id in self.user_states and "preferred_strategy" in self.user_states[user_id]:
            current_strategy = self.user_states[user_id]["preferred_strategy"]
        
        if current_strategy in strategy_stats:
            count = strategy_stats[current_strategy]["count"]
            total_feedback = strategy_stats[current_strategy]["total_feedback"]
            effectiveness = total_feedback / count if count > 0 else 0.5
            confidence = min(1.0, count / 10)  # 样本数越多，置信度越高
        else:
            effectiveness = 0.5  # 没有数据时默认中等效果
            confidence = 0.0
            count = 0
        
        return {
            "effectiveness": effectiveness,
            "confidence": confidence,
            "sample_size": count,
            "strategy_id": current_strategy
        }
    
    def _handle_user_message(self, data: Dict[str, Any]):
        """
        处理用户消息事件
        
        Args:
            data: 事件数据
        """
        try:
            user_id = data.get("user_id")
            message = data.get("message", "")
            metadata = data.get("metadata", {})
            
            if not user_id or not message:
                return
            
            # 跟踪用户状态
            self.track_user_emotion(user_id, message, metadata)
            self.track_user_attention(user_id, message, metadata)
            self.detect_interest_points(user_id, message, metadata)
            
            # 选择最优策略
            optimal_strategy = self.select_optimal_strategy(user_id)
            
            # 如果策略不同且冷却时间已过，则更新策略
            current_time = time.time()
            if optimal_strategy != self.active_strategy and (current_time - self.last_strategy_change) >= self.strategy_cooldown:
                self.set_strategy(optimal_strategy, force=True)
            
            logger.debug(f"处理用户[{user_id}]消息，当前策略: {self.active_strategy}")
        except Exception as e:
            logger.error_status(f"处理用户消息事件失败: {e}")
    
    def _handle_user_reaction(self, data: Dict[str, Any]):
        """
        处理用户反应事件
        
        Args:
            data: 事件数据
        """
        try:
            user_id = data.get("user_id")
            reaction_type = data.get("reaction_type", "neutral")
            reaction_strength = data.get("reaction_strength", 0.5)
            response_id = data.get("response_id")
            
            if not user_id:
                return
            
            # 将反应转换为反馈值
            feedback = 0.0
            if reaction_type == "positive":
                feedback = reaction_strength
            elif reaction_type == "negative":
                feedback = -reaction_strength
            
            # 记录策略反馈
            if self.strategy_history:
                latest_strategy = self.strategy_history[-1]
                latest_strategy["feedback"] = feedback
            
            # 更新用户状态
            if user_id not in self.user_states:
                self.user_states[user_id] = {}
            
            if "reactions" not in self.user_states[user_id]:
                self.user_states[user_id]["reactions"] = []
            
            # 添加反应记录
            self.user_states[user_id]["reactions"].append({
                "reaction_type": reaction_type,
                "strength": reaction_strength,
                "response_id": response_id,
                "timestamp": time.time()
            })
            
            # 限制反应历史长度
            if len(self.user_states[user_id]["reactions"]) > 20:
                self.user_states[user_id]["reactions"] = self.user_states[user_id]["reactions"][-20:]
            
            logger.debug(f"记录用户[{user_id}]对响应[{response_id}]的反应: {reaction_type}({reaction_strength})")
        except Exception as e:
            logger.error_status(f"处理用户反应事件失败: {e}")
    
    def _handle_pre_response(self, data: Dict[str, Any]):
        """
        处理响应生成前事件，用于注入策略参数
        
        Args:
            data: 事件数据
        """
        try:
            user_id = data.get("user_id")
            prompt_data = data.get("prompt_data", {})
            
            if not user_id:
                return
            
            # 获取当前策略
            strategy = self.get_current_strategy(user_id)
            strategy_params = strategy.get("parameters", {})
            
            # 注入策略参数
            prompt_data["dialog_strategy"] = strategy.get("id", "casual")
            prompt_data["dialog_style"] = strategy.get("description", "随意型对话策略")
            
            for param, value in strategy_params.items():
                prompt_data[f"dialog_{param}"] = value
            
            # 注入用户状态
            if user_id in self.user_states:
                user_state = self.user_states[user_id]
                
                # 注入情绪状态
                if "emotion" in user_state:
                    emotion = user_state["emotion"]
                    prompt_data["user_emotion"] = emotion.get("primary_emotion", "neutral")
                    prompt_data["emotion_intensity"] = emotion.get("emotion_intensity", 0.0)
                
                # 注入注意力状态
                if "attention" in user_state:
                    attention = user_state["attention"]
                    prompt_data["attention_level"] = attention.get("level", 0.5)
                
                # 注入兴趣点
                if "interest_points" in user_state and user_state["interest_points"]:
                    prompt_data["interest_points"] = user_state["interest_points"][-3:]
            
            logger.debug(f"为用户[{user_id}]注入对话策略参数，策略: {strategy.get('id')}")
        except Exception as e:
            logger.error_status(f"处理响应生成前事件失败: {e}")
    
    def _load_data(self):
        """加载模块数据"""
        try:
            # 从存储加载数据(实际实现应使用存储管理器)
            data_dir = os.path.join(root_dir, "data", "adaptive_dialog")
            os.makedirs(data_dir, exist_ok=True)
            
            # 加载用户状态
            user_states_path = os.path.join(data_dir, "user_states.json")
            if os.path.exists(user_states_path):
                with open(user_states_path, "r", encoding="utf-8") as f:
                    self.user_states = json.load(f)
            
            # 加载策略历史
            history_path = os.path.join(data_dir, "strategy_history.json")
            if os.path.exists(history_path):
                with open(history_path, "r", encoding="utf-8") as f:
                    self.strategy_history = json.load(f)
            
            logger.success("加载自适应对话策略模块数据成功")
        except Exception as e:
            logger.error_status(f"加载自适应对话策略模块数据失败: {e}")
    
    def _save_data(self):
        """保存模块数据"""
        try:
            # 保存数据到存储(实际实现应使用存储管理器)
            data_dir = os.path.join(root_dir, "data", "adaptive_dialog")
            os.makedirs(data_dir, exist_ok=True)
            
            # 保存用户状态
            user_states_path = os.path.join(data_dir, "user_states.json")
            with open(user_states_path, "w", encoding="utf-8") as f:
                json.dump(self.user_states, f, ensure_ascii=False, indent=2)
            
            # 保存策略历史
            history_path = os.path.join(data_dir, "strategy_history.json")
            with open(history_path, "w", encoding="utf-8") as f:
                json.dump(self.strategy_history, f, ensure_ascii=False, indent=2)
            
            logger.success("保存自适应对话策略模块数据成功")
        except Exception as e:
            logger.error_status(f"保存自适应对话策略模块数据失败: {e}")

    def reason(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行推理
        
        Args:
            input_data: 输入数据
            
        Returns:
            推理结果
        """
        return self._process_input(input_data)
        
    def decide(self, options: List[Dict[str, Any]], context: Dict[str, Any] = None, strategy: str = "rational") -> Dict[str, Any]:
        """
        做出决策
        
        Args:
            options: 可选选项列表
            context: 上下文信息
            strategy: 决策策略
            
        Returns:
            选择的选项
        """
        # 简单实现：使用当前对话策略选择最佳选项
        if not options:
            return {"success": False, "error": "没有可选选项"}
        
        current_strategy = self.active_strategy
        
        # 基于不同策略评估选项
        if current_strategy == "informative":
            # 信息型策略偏好信息丰富的选项
            for option in options:
                if option.get("information_value", 0) > 0.7:
                    return {"success": True, "selected": option}
        
        elif current_strategy == "emotional":
            # 情感型策略偏好情感共鸣的选项
            for option in options:
                if option.get("emotional_value", 0) > 0.7:
                    return {"success": True, "selected": option}
        
        elif current_strategy == "creative":
            # 创意型策略偏好创新的选项
            for option in options:
                if option.get("creativity_value", 0) > 0.7:
                    return {"success": True, "selected": option}
        
        elif current_strategy == "professional":
            # 专业型策略偏好专业的选项
            for option in options:
                if option.get("professional_value", 0) > 0.7:
                    return {"success": True, "selected": option}
        
        # 默认选择第一个选项
        return {"success": True, "selected": options[0]}

def get_instance(config: Dict[str, Any] = None) -> AdaptiveDialogModule:
    """
    获取自适应对话策略模块实例（单例模式）
    
    Args:
        config: 模块配置
        
    Returns:
        自适应对话策略模块实例
    """
    return AdaptiveDialogModule(config) 