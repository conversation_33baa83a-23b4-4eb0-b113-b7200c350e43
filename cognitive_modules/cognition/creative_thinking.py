#!/usr/bin/env python3
"""
创造性思维模块 - Creative Thinking Module

该模块实现了数字生命体的创造性思维能力，包括：
1. 创意生成与发散思维
2. 概念重组与联想思维
3. 隐喻生成与比喻思维
4. 创新问题解决
5. 跨领域思维与知识整合

作者: Claude
创建日期: 2024-07-29
版本: 1.0
"""

import os
import sys
import json
import time
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, Set

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
cognition_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(cognition_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.base.cognitive_interface import ICognitionModule
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.cognition.creative_thinking")

class CreativeThinking(CognitiveModuleBase, ICognitionModule):
    """
    创造性思维模块类
    
    实现数字生命体的创造性思维能力，包括发散思维、概念重组、隐喻生成和创新解决问题的能力。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化创造性思维模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "creative_thinking", "cognition", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 创造思维状态
        self.creative_state = {
            "active": False,              # 是否处于活跃创造状态
            "focus_domain": None,         # 当前创造焦点领域
            "inspiration_level": 0.5,     # 灵感水平(0.0-1.0)
            "divergence_degree": 0.5,     # 发散程度(0.0-1.0)
            "last_creative_time": 0,      # 上次创造性思考时间
            "session_ideas_count": 0      # 当前会话产生创意数量
        }
        
        # 创造性思维记录
        self.creative_history: List[Dict[str, Any]] = []
        self.max_history_size = 100
        
        # 概念关联网络
        self.concept_network: Dict[str, Dict[str, float]] = {}
        
        # 创造性思维技巧库
        self.creative_techniques = {
            "brainstorming": {
                "description": "头脑风暴，快速产生大量不加评判的想法",
                "process": ["收集大量想法", "不评判", "追求数量", "寻找关联"]
            },
            "reverse_thinking": {
                "description": "逆向思维，从相反方向思考问题",
                "process": ["确定常规思路", "提出相反命题", "探索可能性", "寻找新视角"]
            },
            "random_stimulation": {
                "description": "随机刺激，引入随机元素激发联想",
                "process": ["选择随机概念", "强制建立联系", "发展新想法"]
            },
            "analogy_mapping": {
                "description": "类比映射，通过类比其他领域解决问题",
                "process": ["分析问题结构", "寻找类似领域", "映射解决方案", "调整应用"]
            },
            "concept_blending": {
                "description": "概念混合，将多个概念特征融合",
                "process": ["选择概念", "提取关键特征", "创造性融合", "发展新概念"]
            },
            "pattern_breaking": {
                "description": "模式打破，打破既定思维模式",
                "process": ["识别思维模式", "质疑假设", "提出替代方案"]
            },
            "metaphorical_thinking": {
                "description": "隐喻思维，使用隐喻理解和表达复杂概念",
                "process": ["寻找隐喻源域", "映射关键特征", "发展隐喻表达"]
            }
        }
        
        # 创造性刺激库
        self.creative_stimuli = {
            "random_words": [
                "云", "门", "桥", "梦", "河", "花", "风", "鸟", "山", "海",
                "光", "影", "星", "月", "火", "水", "地", "天", "木", "金"
            ],
            "random_concepts": [
                "时间", "空间", "变化", "平衡", "混沌", "秩序", "联系", "边界", 
                "起源", "终结", "自由", "约束", "单一", "多元", "静止", "运动"
            ],
            "random_questions": [
                "如果可以重新定义，这会是什么？",
                "这与完全不相关的领域有什么共同点？",
                "如果将这个问题倒过来看会怎样？",
                "如果移除核心假设会发生什么？",
                "如果将规模放大/缩小1000倍会怎样？"
            ]
        }
        
        # 创造力领域
        self.creative_domains = [
            "艺术与审美", "故事与叙事", "问题解决", "概念创新", 
            "交流表达", "系统设计", "自我理解", "关系理解"
        ]
        
        # 创造性思维线程
        self.creative_thread = None
        self.creative_thread_active = False
        
        # 尝试从存储中加载创造性历史和概念网络
        self._load_creative_history()
        self._load_concept_network()
        
        logger.info(f"创造性思维模块 {self.module_id} 已创建")
    
    def initialize(self) -> bool:
        """初始化模块"""
        logger.success(f"初始化创造性思维模块 {self.module_id}")
        
        # 加载数据
        self._load_creative_history()
        self._load_concept_network()
        
        # 订阅事件
        self.event_bus.subscribe("system_idle", self._on_system_idle)
        self.event_bus.subscribe("user_message", self._on_user_message)
        self.event_bus.subscribe("emotion_updated", self._on_emotion_updated)
        self.event_bus.subscribe("insight_generated", self._on_insight_generated)
        
        # 启动创造性思维后台线程
        self.creative_thread_active = True
        self.creative_thread = threading.Thread(target=self._background_creative_thinking, daemon=True)
        self.creative_thread.start()
        
        logger.success(f"创造性思维模块 {self.module_id} 初始化完成")
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        status = {
            "module_id": self.module_id,
            "module_type": "creative_thinking",
            "is_initialized": self.is_initialized,
            "creative_state": self.creative_state,
            "history_count": len(self.creative_history),
            "concept_count": len(self.concept_network),
            "inspiration_level": self.creative_state.get("inspiration_level", 0.5),
            "divergence_degree": self.creative_state.get("divergence_degree", 0.5),
            "connected_modules": list(self.connected_modules.keys()) if hasattr(self, "connected_modules") else []
        }
        return status
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据"""
        # 提取输入类型和内容
        input_type = input_data.get("type", "")
        content = input_data.get("content", "")
        context = input_data.get("context", {})
        
        # 根据输入类型处理
        if input_type == "idea_generation":
            # 生成创意
            topic = content
            technique = input_data.get("technique")
            count = input_data.get("count", 3)
            return self.generate_ideas(topic, count, technique)
            
        elif input_type == "metaphor_generation":
            # 生成隐喻
            concept = content
            domains = input_data.get("domains")
            return self.generate_metaphor(concept, domains)
            
        elif input_type == "concept_blending":
            # 概念混合
            concepts = content if isinstance(content, list) else [content]
            return self.blend_concepts(concepts)
            
        elif input_type == "problem_solving":
            # 创造性问题解决
            problem = content
            constraints = input_data.get("constraints")
            return self.solve_problem_creatively(problem, constraints)
            
        elif input_type == "contextual_ideas":
            # 基于上下文生成创意
            return self.generate_contextual_ideas(context)
            
        elif input_type == "reasoning":
            # 创造性推理
            reasoning_type = input_data.get("reasoning_type", "divergent")
            
            if reasoning_type == "divergent":
                seed = content
                return {"success": True, "reasoning_type": reasoning_type, 
                        **self._divergent_thinking(seed, context)}
                
            elif reasoning_type == "analogical":
                source = input_data.get("source", "")
                target = input_data.get("target", "")
                return {"success": True, "reasoning_type": reasoning_type, 
                        **self._analogical_reasoning(source, target)}
                
            elif reasoning_type == "metaphorical":
                concept = content
                return {"success": True, "reasoning_type": reasoning_type, 
                        **self._metaphorical_reasoning(concept)}
                
            elif reasoning_type == "combinatorial":
                elements = content if isinstance(content, list) else [content]
                return {"success": True, "reasoning_type": reasoning_type, 
                        **self._combinatorial_reasoning(elements)}
            
            else:
                return {"success": False, "error": f"不支持的推理类型: {reasoning_type}"}
        
        else:
            # 未知输入类型
            return {"success": False, "error": f"不支持的输入类型: {input_type}"}
    
    def reason(self, reasoning_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行创造性推理"""
        reasoning_type = reasoning_data.get("reasoning_type", "divergent")
        
        if reasoning_type == "divergent":
            seed = reasoning_data.get("seed", "")
            context = reasoning_data.get("context", {})
            return self._divergent_thinking(seed, context)
            
        elif reasoning_type == "analogical":
            source = reasoning_data.get("source", "")
            target = reasoning_data.get("target", "")
            return self._analogical_reasoning(source, target)
            
        elif reasoning_type == "metaphorical":
            concept = reasoning_data.get("concept", "")
            return self._metaphorical_reasoning(concept)
            
        elif reasoning_type == "combinatorial":
            elements = reasoning_data.get("elements", [])
            return self._combinatorial_reasoning(elements)
        
        else:
            return {"success": False, "error": f"不支持的推理类型: {reasoning_type}"}
    
    def _update_module(self, time_delta: float) -> None:
        """更新模块状态"""
        # 当前版本不需要主动更新，后台线程会处理状态更新
        pass
    
    def _get_module_state(self) -> Dict[str, Any]:
        """获取模块状态"""
        module_state = {
            "creative_state": self.creative_state,
            "creative_history": self.creative_history[:20],  # 仅保存最近20条历史
            "concept_network": self.concept_network,
            "is_initialized": self.is_initialized
        }
        return module_state
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """加载模块状态"""
        if not state:
            return False
        
        try:
            # 加载创造性状态
            if "creative_state" in state:
                self.creative_state = state["creative_state"]
            
            # 加载创造性历史
            if "creative_history" in state:
                self.creative_history = state["creative_history"]
            
            # 加载概念网络
            if "concept_network" in state:
                self.concept_network = state["concept_network"]
            
            # 加载初始化状态
            if "is_initialized" in state:
                self.is_initialized = state["is_initialized"]
            
            return True
        except Exception as e:
            logger.error_status(f"加载模块状态失败: {e}")
            return False
    
    def _initialize_module(self) -> bool:
        """初始化模块（内部方法）"""
        # 由公共的initialize方法调用
        return True
    
    def _shutdown_module(self) -> bool:
        """关闭模块（内部方法）"""
        # 停止创造性思维后台线程
        self.creative_thread_active = False
        if hasattr(self, "creative_thread") and self.creative_thread.is_alive():
            try:
                self.creative_thread.join(timeout=2.0)
            except Exception as e:
                logger.warning_status(f"关闭创造性思维线程异常: {e}")
        
        # 保存数据
        self._save_creative_history()
        self._save_concept_network()
        
        return True
    
    def generate_ideas(self, topic: str, count: int = 3, technique: str = None) -> Dict[str, Any]:
        """
        生成创意想法
        
        Args:
            topic: 创意主题
            count: 想法数量
            technique: 使用的创造技巧
            
        Returns:
            创意生成结果
        """
        if not topic:
            # 如果没有提供主题，随机选择一个
            topic = random.choice(self.creative_domains)
        
        # 如果没有指定技巧，随机选择一个
        if not technique or technique not in self.creative_techniques:
            technique = random.choice(list(self.creative_techniques.keys()))
        
        # 获取情感模块的创造力影响因子
        creative_factors = self._get_creative_influence_factors()
        
        # 生成创意
        ideas = []
        for i in range(count):
            idea = self._create_idea(topic, technique, creative_factors)
            ideas.append(idea)
            
            # 更新创意计数
            self.creative_state["session_ideas_count"] += 1
        
        # 添加到创造性历史
        creative_event = {
            "id": f"creative_{int(time.time())}_{random.randint(1000, 9999)}",
            "topic": topic,
            "technique": technique,
            "ideas": ideas,
            "timestamp": time.time(),
            "creative_factors": creative_factors
        }
        
        self.creative_history.append(creative_event)
        self._trim_history()
        
        # 更新概念网络
        self._update_concept_network(topic, [idea["content"] for idea in ideas])
        
        # 发布创造性想法生成事件
        self.event_bus.publish("creative_ideas_generated", {
            "module_id": self.module_id,
            "ideas": ideas,
            "topic": topic,
            "technique": technique
        })
        
        return {
            "success": True,
            "topic": topic,
            "technique": technique,
            "technique_info": self.creative_techniques[technique],
            "ideas": ideas,
            "creative_factors": creative_factors
        }
    
    def generate_metaphor(self, concept: str, domains: List[str] = None) -> Dict[str, Any]:
        """
        生成隐喻
        
        Args:
            concept: 需要隐喻的概念
            domains: 可选的隐喻领域列表
            
        Returns:
            隐喻生成结果
        """
        if not concept:
            return {"success": False, "error": "需要提供概念"}
        
        # 如果没有提供领域，使用预设领域
        if not domains or len(domains) == 0:
            potential_domains = [
                "自然", "建筑", "旅行", "音乐", "食物", "水", "光", 
                "动物", "植物", "天气", "季节", "颜色", "科技"
            ]
            domains = random.sample(potential_domains, min(3, len(potential_domains)))
        
        # 获取创造力影响因子
        creative_factors = self._get_creative_influence_factors()
        
        # 为每个领域生成一个隐喻
        metaphors = []
        for domain in domains:
            metaphor = self._create_metaphor(concept, domain, creative_factors)
            metaphors.append(metaphor)
        
        # 添加到创造性历史
        metaphor_event = {
            "id": f"metaphor_{int(time.time())}_{random.randint(1000, 9999)}",
            "concept": concept,
            "domains": domains,
            "metaphors": metaphors,
            "timestamp": time.time()
        }
        
        self.creative_history.append(metaphor_event)
        self._trim_history()
        
        # 发布隐喻生成事件
        self.event_bus.publish("metaphors_generated", {
            "module_id": self.module_id,
            "concept": concept,
            "metaphors": metaphors
        })
        
        return {
            "success": True,
            "concept": concept,
            "domains": domains,
            "metaphors": metaphors
        }
    
    def blend_concepts(self, concepts: List[str]) -> Dict[str, Any]:
        """
        概念混合
        
        将两个或多个概念融合，创造新概念
        
        Args:
            concepts: 要混合的概念列表
            
        Returns:
            概念混合结果
        """
        if not concepts or len(concepts) < 2:
            return {"success": False, "error": "需要至少两个概念进行混合"}
        
        # 获取创造力影响因子
        creative_factors = self._get_creative_influence_factors()
        
        # 创建概念混合
        blended_concept = self._create_concept_blend(concepts, creative_factors)
        
        # 添加到创造性历史
        blend_event = {
            "id": f"blend_{int(time.time())}_{random.randint(1000, 9999)}",
            "source_concepts": concepts,
            "result": blended_concept,
            "timestamp": time.time()
        }
        
        self.creative_history.append(blend_event)
        self._trim_history()
        
        # 更新概念网络
        for concept in concepts:
            for other in concepts:
                if concept != other:
                    self._add_concept_relation(concept, other, 0.7)
        
        # 发布概念混合事件
        self.event_bus.publish("concept_blend_created", {
            "module_id": self.module_id,
            "source_concepts": concepts,
            "blended_concept": blended_concept
        })
        
        return {
            "success": True,
            "source_concepts": concepts,
            "blended_concept": blended_concept
        }
    
    def solve_problem_creatively(self, problem: str, constraints: List[str] = None) -> Dict[str, Any]:
        """
        创造性解决问题
        
        Args:
            problem: 问题描述
            constraints: 约束条件列表
            
        Returns:
            创造性解决方案
        """
        if not problem:
            return {"success": False, "error": "需要提供问题描述"}
        
        # 使用不同的创造性技巧生成多个解决方案
        techniques = random.sample(list(self.creative_techniques.keys()), 
                                  min(3, len(self.creative_techniques)))
        
        # 获取创造力影响因子
        creative_factors = self._get_creative_influence_factors()
        
        # 生成解决方案
        solutions = []
        for technique in techniques:
            solution = self._create_solution(problem, constraints, technique, creative_factors)
            solutions.append(solution)
        
        # 添加到创造性历史
        solution_event = {
            "id": f"solution_{int(time.time())}_{random.randint(1000, 9999)}",
            "problem": problem,
            "constraints": constraints,
            "solutions": solutions,
            "timestamp": time.time()
        }
        
        self.creative_history.append(solution_event)
        self._trim_history()
        
        # 发布创造性解决方案事件
        self.event_bus.publish("creative_solutions_generated", {
            "module_id": self.module_id,
            "problem": problem,
            "solutions": solutions
        })
        
        return {
            "success": True,
            "problem": problem,
            "constraints": constraints,
            "solutions": solutions
        }
    
    def generate_contextual_ideas(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于上下文生成创意
        
        Args:
            context: 上下文信息
            
        Returns:
            创意生成结果
        """
        # 从上下文中提取相关信息
        user_input = context.get("last_user_input", "")
        conversation_topic = context.get("conversation_topic", "")
        emotional_state = context.get("emotional_state", {})
        
        # 选择最相关的主题
        if conversation_topic:
            topic = conversation_topic
        elif user_input:
            # 从用户输入中提取可能的主题
            topic = user_input[:50]
        else:
            # 随机选择一个创造力领域
            topic = random.choice(self.creative_domains)
        
        # 基于情感状态选择创造技巧
        technique = self._select_technique_by_emotion(emotional_state)
        
        # 生成创意
        return self.generate_ideas(topic, count=2, technique=technique)
    
    def _create_idea(self, topic: str, technique: str, creative_factors: Dict[str, float]) -> Dict[str, Any]:
        """创建单个创意"""
        # 创意模板
        idea_templates = {
            "brainstorming": [
                "将{topic}与{random_concept}结合，创造{outcome}",
                "如果{topic}的目的不是{common_purpose}而是{new_purpose}，会怎样？",
                "想象{topic}如果{transformation}会产生什么影响？"
            ],
            "reverse_thinking": [
                "与常规认知相反，{topic}实际上可能是{reverse_concept}",
                "如果{topic}的反面是正确的：{reverse_implication}",
                "假设{topic}不是{common_property}而是{opposite_property}"
            ],
            "random_stimulation": [
                "将{random_word}的特质应用到{topic}上：{application}",
                "{topic}与{random_word}之间存在意想不到的联系：{connection}",
                "用{random_word}的角度重新审视{topic}：{perspective}"
            ],
            "analogy_mapping": [
                "{topic}就像{analogy_source}，因为{mapping_reason}",
                "如果将{analogy_source}的原理应用于{topic}：{application}",
                "{topic}与{analogy_source}共享{shared_pattern}这一模式"
            ],
            "concept_blending": [
                "融合{topic}和{blend_concept}的特质，形成{new_concept}",
                "{topic}的{feature1}与{blend_concept}的{feature2}结合产生{outcome}",
                "创造一个同时具有{topic}和{blend_concept}特性的新概念：{result}"
            ],
            "pattern_breaking": [
                "打破{topic}的常规模式{pattern}，转而{alternative}",
                "挑战{topic}中的核心假设{assumption}，如果{challenge}会怎样？",
                "重新定义{topic}的边界，让它包含{inclusion}或排除{exclusion}"
            ],
            "metaphorical_thinking": [
                "{topic}是一种{metaphor}，它{metaphor_mapping}",
                "将{topic}视为{metaphor}，则{implication}",
                "如果{topic}是{metaphor}，那么{element}就相当于{metaphor_element}"
            ]
        }
        
        # 获取创意组件
        components = self._generate_idea_components(topic, technique)
        
        # 选择模板并填充
        if technique in idea_templates:
            templates = idea_templates[technique]
            template = random.choice(templates)
            
            # 填充模板
            try:
                content = template.format(**components)
            except KeyError:
                # 如果缺少某些键，使用备用模板
                content = f"关于{topic}的创新思考：将其视为{components.get('random_concept', '新概念')}的一种形式。"
        else:
            # 备用模板
            content = f"关于{topic}的创新思考：尝试从全新角度审视其本质和可能性。"
        
        # 生成创意评分
        novelty = min(1.0, 0.5 + creative_factors.get("creative_thinking", 0) * 0.3 + random.uniform(0, 0.2))
        usefulness = random.uniform(0.5, 0.9)
        surprise = min(1.0, 0.4 + creative_factors.get("creative_thinking", 0) * 0.4 + random.uniform(0, 0.2))
        
        return {
            "id": f"idea_{int(time.time())}_{random.randint(1000, 9999)}",
            "topic": topic,
            "technique": technique,
            "content": content,
            "components": components,
            "evaluation": {
                "novelty": novelty,
                "usefulness": usefulness,
                "surprise": surprise,
                "overall": (novelty * 0.4 + usefulness * 0.3 + surprise * 0.3)
            }
        }
    
    def _create_metaphor(self, concept: str, domain: str, creative_factors: Dict[str, float]) -> Dict[str, Any]:
        """创建隐喻"""
        # 领域相关元素
        domain_elements = {
            "自然": ["森林", "河流", "山脉", "风", "雨", "阳光", "种子", "生态系统"],
            "建筑": ["基础", "支柱", "墙壁", "门", "窗户", "屋顶", "楼梯", "建筑结构"],
            "旅行": ["道路", "旅程", "地图", "指南针", "目的地", "行李", "风景", "里程碑"],
            "音乐": ["旋律", "节奏", "和声", "乐器", "交响乐", "协奏曲", "音符", "共鸣"],
            "食物": ["食谱", "原料", "烹饪", "味道", "营养", "调味料", "融合", "盛宴"],
            "水": ["海洋", "河流", "雨滴", "冰", "蒸汽", "波浪", "深度", "流动"],
            "光": ["光源", "阴影", "反射", "折射", "光谱", "明暗", "闪烁", "照明"],
            "动物": ["飞鸟", "鱼群", "蜂巢", "狼群", "迁徙", "觅食", "栖息地", "生存"],
            "植物": ["根系", "枝叶", "花朵", "种子", "生长", "光合作用", "季节变化", "共生"],
            "天气": ["风暴", "晴天", "雷电", "季风", "气候", "预测", "变化", "平衡"],
            "季节": ["春天", "夏天", "秋天", "冬天", "更替", "循环", "萌芽", "收获"],
            "颜色": ["色调", "明度", "饱和度", "混合", "对比", "和谐", "渐变", "透明度"],
            "科技": ["算法", "网络", "接口", "处理器", "数据流", "编程", "升级", "系统"]
        }
        
        # 如果没有领域元素，使用通用元素
        elements = domain_elements.get(domain, ["元素", "组成部分", "结构", "功能", "特性"])
        
        # 选择领域元素
        source_element = random.choice(elements)
        
        # 隐喻映射模板
        mapping_templates = [
            f"{concept}是一种{source_element}，它{random.choice(['连接', '包含', '转化', '平衡', '孕育', '展现'])}着{random.choice(['可能性', '意义', '关系', '模式', '转变', '价值'])}",
            f"{concept}就像{domain}中的{source_element}，{random.choice(['引导', '塑造', '丰富', '深化', '映射', '延展'])}着{random.choice(['体验', '理解', '连接', '变化', '发展', '互动'])}",
            f"将{concept}视为{source_element}，能帮助我们理解它如何{random.choice(['运作', '发展', '影响', '适应', '转变', '融合'])}"
        ]
        
        # 选择并填充模板
        metaphor_content = random.choice(mapping_templates)
        
        # 扩展隐喻
        elaboration_templates = [
            f"正如{source_element}需要{random.choice(['时间', '空间', '养分', '互动', '平衡', '结构'])}，{concept}也需要{random.choice(['发展', '支持', '理解', '关注', '调整', '整合'])}。",
            f"这种隐喻揭示了{concept}的{random.choice(['本质', '结构', '功能', '影响', '变化', '意义'])}如何与我们的理解产生共鸣。",
            f"通过这个隐喻，我们可以重新思考{concept}与{random.choice(['自身', '他人', '环境', '变化', '目标', '价值'])}的关系。"
        ]
        
        elaboration = random.choice(elaboration_templates)
        
        # 评估隐喻质量
        coherence = min(1.0, 0.6 + creative_factors.get("creative_thinking", 0) * 0.2 + random.uniform(0, 0.2))
        insight = min(1.0, 0.5 + creative_factors.get("creative_thinking", 0) * 0.3 + random.uniform(0, 0.2))
        vividness = random.uniform(0.6, 0.9)
        
        return {
            "id": f"metaphor_{int(time.time())}_{random.randint(1000, 9999)}",
            "concept": concept,
            "domain": domain,
            "source_element": source_element,
            "metaphor": metaphor_content,
            "elaboration": elaboration,
            "full_metaphor": f"{metaphor_content} {elaboration}",
            "evaluation": {
                "coherence": coherence,
                "insight": insight,
                "vividness": vividness,
                "overall": (coherence * 0.4 + insight * 0.4 + vividness * 0.2)
            }
        }
    
    def _create_concept_blend(self, concepts: List[str], creative_factors: Dict[str, float]) -> Dict[str, Any]:
        """创建概念混合"""
        # 确保至少有两个概念
        if len(concepts) < 2:
            concepts = concepts + ["未指定概念"]
        
        # 为每个概念生成特征
        concept_features = {}
        for concept in concepts:
            # 生成3-5个特征
            features = self._generate_concept_features(concept)
            concept_features[concept] = features
        
        # 选择每个概念的1-2个特征进行混合
        selected_features = {}
        for concept, features in concept_features.items():
            num_features = min(len(features), random.randint(1, 2))
            selected = random.sample(features, num_features)
            selected_features[concept] = selected
        
        # 创建混合概念名称
        blend_name_templates = [
            f"{concepts[0][:4]}{concepts[1][4:] if len(concepts[1]) > 4 else concepts[1]}",
            f"{concepts[0][:3]}-{concepts[1][:3]}",
            f"{concepts[1][:3]}{concepts[0][3:] if len(concepts[0]) > 3 else concepts[0]}",
            f"混合{concepts[0][:2]}{concepts[1][:2]}"
        ]
        
        blend_name = random.choice(blend_name_templates)
        
        # 创建混合概念描述
        description = f"{blend_name}融合了"
        for i, concept in enumerate(concepts):
            if i > 0:
                description += "和" if i == len(concepts) - 1 else "、"
            description += f"{concept}"
        description += "的特征，"
        
        # 添加特征描述
        description += "它"
        feature_descriptions = []
        for concept, features in selected_features.items():
            for feature in features:
                feature_descriptions.append(f"具有{concept}的{feature}")
        
        description += "，".join(feature_descriptions)
        
        # 添加潜在应用
        applications = [
            f"可用于{random.choice(['解决', '理解', '探索', '创新', '改进'])}{random.choice(['问题', '情境', '关系', '系统', '过程'])}",
            f"为{random.choice(['思考', '设计', '分析', '表达', '交流'])}提供新视角",
            f"打破{random.choice(['传统边界', '常规思维', '既定模式', '固有限制', '认知框架'])}"
        ]
        
        description += f"。这一混合概念{random.choice(applications)}。"
        
        # 评估混合概念
        coherence = min(1.0, 0.5 + creative_factors.get("creative_thinking", 0) * 0.2 + random.uniform(0, 0.3))
        novelty = min(1.0, 0.6 + creative_factors.get("creative_thinking", 0) * 0.3 + random.uniform(0, 0.1))
        potential = random.uniform(0.6, 0.9)
        
        return {
            "id": f"blend_{int(time.time())}_{random.randint(1000, 9999)}",
            "name": blend_name,
            "source_concepts": concepts,
            "concept_features": concept_features,
            "selected_features": selected_features,
            "description": description,
            "evaluation": {
                "coherence": coherence,
                "novelty": novelty,
                "potential": potential,
                "overall": (coherence * 0.3 + novelty * 0.4 + potential * 0.3)
            }
        }
    
    def _create_solution(self, problem: str, constraints: List[str], technique: str, creative_factors: Dict[str, float]) -> Dict[str, Any]:
        """创建创造性解决方案"""
        # 解决方案模板
        solution_templates = {
            "brainstorming": "通过集思广益，一个可能的解决方案是{main_idea}，这将{benefit}",
            "reverse_thinking": "反转问题思考，如果{reverse_perspective}，那么{solution}",
            "random_stimulation": "受{random_element}启发，可以{approach}来解决问题，这样{outcome}",
            "analogy_mapping": "借鉴{analogy_domain}的经验，可以{mapping_solution}，就像{analogy_example}",
            "concept_blending": "融合{concept1}和{concept2}的思路，创造{blended_solution}",
            "pattern_breaking": "打破{pattern}的常规模式，尝试{alternative_approach}",
            "metaphorical_thinking": "将问题视为{metaphor}，则解决方案在于{metaphor_solution}"
        }
        
        # 生成解决方案组件
        components = self._generate_solution_components(problem, constraints, technique)
        
        # 选择模板并填充
        if technique in solution_templates:
            template = solution_templates[technique]
            try:
                content = template.format(**components)
            except KeyError:
                # 备用方案
                content = f"一个创新的解决方案是：重新定义问题边界，关注{components.get('main_idea', '核心问题')}。"
        else:
            # 备用模板
            content = f"一个创新的解决方案是：从多角度分析问题，然后整合不同视角的见解。"
        
        # 补充详细说明
        details = self._generate_solution_details(problem, components, technique)
        
        # 评估解决方案
        effectiveness = min(1.0, 0.5 + creative_factors.get("creative_thinking", 0) * 0.2 + random.uniform(0, 0.3))
        innovation = min(1.0, 0.4 + creative_factors.get("creative_thinking", 0) * 0.4 + random.uniform(0, 0.2))
        feasibility = random.uniform(0.4, 0.8)
        
        return {
            "id": f"solution_{int(time.time())}_{random.randint(1000, 9999)}",
            "problem": problem,
            "technique": technique,
            "content": content,
            "details": details,
            "full_solution": f"{content}\n\n{details}",
            "evaluation": {
                "effectiveness": effectiveness,
                "innovation": innovation,
                "feasibility": feasibility,
                "overall": (effectiveness * 0.4 + innovation * 0.4 + feasibility * 0.2)
            }
        }
    
    def _generate_idea_components(self, topic: str, technique: str) -> Dict[str, str]:
        """生成创意组件"""
        components = {
            "topic": topic,
            "random_concept": random.choice(self.creative_stimuli["random_concepts"]),
            "random_word": random.choice(self.creative_stimuli["random_words"]),
            "common_purpose": f"用于{random.choice(['沟通', '理解', '解决问题', '创造', '表达'])}"
        }
        
        # 基于技巧生成特定组件
        if technique == "brainstorming":
            components.update({
                "outcome": f"新型的{random.choice(['方法', '工具', '概念', '系统', '关系'])}",
                "new_purpose": f"用于{random.choice(['连接', '转化', '重构', '简化', '增强'])}",
                "transformation": random.choice(["被重新定义", "以全新方式使用", "与传统脱节", "超越其边界"])
            })
        elif technique == "reverse_thinking":
            components.update({
                "reverse_concept": f"{random.choice(['工具', '过程', '障碍', '机会', '错觉'])}而非目标",
                "reverse_implication": f"我们应该{random.choice(['避免而非追求它', '质疑而非接受它', '简化而非复杂化它'])}",
                "common_property": random.choice(["静态的", "独立的", "唯一的", "复杂的", "可控的"]),
                "opposite_property": random.choice(["动态的", "关联的", "多样的", "简单的", "涌现的"])
            })
        elif technique == "random_stimulation":
            components.update({
                "application": f"产生{random.choice(['新视角', '隐藏联系', '意外功能', '创新方法'])}",
                "connection": f"它们都{random.choice(['创造边界', '促进变化', '维持平衡', '形成模式'])}",
                "perspective": f"关注其{random.choice(['转化能力', '联系模式', '隐藏价值', '潜在影响'])}"
            })
        elif technique == "analogy_mapping":
            components.update({
                "analogy_source": random.choice(["自然生态系统", "音乐和声", "建筑结构", "社交网络", "细胞组织"]),
                "mapping_reason": f"它们都{random.choice(['基于相互依赖', '需要平衡与协调', '遵循特定模式', '在约束中寻求最优'])}",
                "application": f"创造更{random.choice(['和谐', '适应性强', '可持续', '有韧性', '自组织'])}的解决方案",
                "shared_pattern": random.choice(["自我调节", "层级组织", "信息流动", "边界管理", "资源分配"])
            })
        elif technique == "concept_blending":
            components.update({
                "blend_concept": random.choice(["网络", "生态系统", "旅程", "对话", "游戏", "故事"]),
                "feature1": random.choice(["结构", "目的", "边界", "关系", "功能"]),
                "feature2": random.choice(["适应性", "复杂性", "节奏", "互动性", "发展过程"]),
                "outcome": f"一种新型的{random.choice(['理解框架', '交互模式', '发展路径', '创新方法'])}",
                "new_concept": f"{topic}化{components['blend_concept']}",
                "result": f"更具{random.choice(['适应性', '连接性', '创造性', '共生性'])}的{topic}"
            })
        
        return components
    
    def _generate_concept_features(self, concept: str) -> List[str]:
        """生成概念特征"""
        # 通用特征类型
        feature_types = [
            "结构特征", "功能特征", "关系特征", "过程特征", 
            "属性特征", "目的特征", "边界特征", "发展特征"
        ]
        
        # 特征值模板
        feature_values = {
            "结构特征": ["{part}与{part}的{arrangement}", "由{element}组成的{structure}"],
            "功能特征": ["能够{function}", "用于{purpose}的能力", "通过{method}实现{goal}"],
            "关系特征": ["与{entity}的{relationship}关系", "在{context}中的{role}"],
            "过程特征": ["经历{process}的变化", "{stage}到{stage}的转变"],
            "属性特征": ["具有{quality}的特质", "{degree}的{property}"],
            "目的特征": ["旨在{aim}", "为了{objective}而存在", "引导向{direction}"],
            "边界特征": ["在{dimension}上的{limitation}", "超越{boundary}的可能性"],
            "发展特征": ["随{factor}而{evolution}", "从{origin}发展出的{potential}"]
        }
        
        # 选择3-5个特征类型
        selected_types = random.sample(feature_types, min(len(feature_types), random.randint(3, 5)))
        
        # 生成特征
        features = []
        for feature_type in selected_types:
            if feature_type in feature_values:
                template = random.choice(feature_values[feature_type])
                
                # 填充模板占位符
                filled_template = template
                for placeholder in ["part", "arrangement", "element", "structure", 
                                  "function", "purpose", "method", "goal",
                                  "entity", "relationship", "context", "role",
                                  "process", "stage", "quality", "property", 
                                  "degree", "aim", "objective", "direction",
                                  "dimension", "limitation", "boundary",
                                  "factor", "evolution", "origin", "potential"]:
                    if "{" + placeholder + "}" in filled_template:
                        value = self._generate_placeholder_value(placeholder, concept)
                        filled_template = filled_template.replace("{" + placeholder + "}", value)
                
                features.append(filled_template)
            else:
                # 备用特征
                features.append(f"独特的{feature_type}")
        
        return features
    
    def _generate_placeholder_value(self, placeholder: str, concept: str) -> str:
        """生成占位符值"""
        # 为不同类型的占位符生成值
        placeholder_values = {
            "part": ["核心", "边界", "组件", "层次", "节点", "中心", "基础"],
            "arrangement": ["组织", "结构", "配置", "排列", "组合", "整合"],
            "element": ["基本单位", "组成部分", "关键元素", "构建块", "核心要素"],
            "structure": ["网络", "层级", "矩阵", "系统", "架构", "框架"],
            "function": ["转化", "连接", "整合", "分析", "创造", "适应", "传递"],
            "purpose": ["实现目标", "满足需求", "解决问题", "创造价值", "促进发展"],
            "method": ["系统化过程", "自组织机制", "反馈循环", "模式识别", "协同作用"],
            "goal": ["平衡", "创新", "理解", "连接", "适应", "发展", "超越"],
            "entity": ["相关系统", "环境", "用户", "其他概念", "更大整体"],
            "relationship": ["互补", "协同", "依赖", "互动", "反馈", "共生"],
            "context": ["不同情境", "变化环境", "特定领域", "复杂系统", "动态过程"],
            "role": ["媒介", "促进者", "转化器", "连接点", "基础", "催化剂"],
            "process": ["渐进", "突变", "循环", "自组织", "分化", "整合", "演化"],
            "stage": ["初始状态", "过渡阶段", "成熟形态", "理想状态", "潜在可能"],
            "quality": ["适应性", "灵活性", "稳定性", "创造性", "连贯性", "开放性"],
            "property": ["复杂度", "自主性", "关联性", "层次性", "多样性", "韧性"],
            "degree": ["高度", "适度", "动态", "相对", "不断变化", "自调节"],
            "aim": ["创造新可能", "连接不同领域", "超越限制", "解决矛盾", "促进发展"],
            "objective": ["增强理解", "实现转化", "创造价值", "建立联系", "突破边界"],
            "direction": ["更大整合", "更深理解", "更广应用", "更新迭代"],
            "dimension": ["复杂性", "适应性", "可预测性", "创造性", "互动性"],
            "limitation": ["固有约束", "认知边界", "结构限制", "环境制约"],
            "boundary": ["传统定义", "现有框架", "常规认知", "既定模式"],
            "factor": ["环境变化", "内部发展", "互动过程", "需求演变", "结构重组"],
            "evolution": ["增强", "转化", "适应", "分化", "整合", "创新"],
            "origin": ["基础概念", "初始形态", "原始结构", "核心原理"],
            "potential": ["新形式", "新功能", "新关系", "新意义", "新应用"]
        }
        
        if placeholder in placeholder_values:
            return random.choice(placeholder_values[placeholder])
        else:
            return placeholder
    
    def _generate_solution_components(self, problem: str, constraints: List[str], technique: str) -> Dict[str, str]:
        """生成解决方案组件"""
        components = {
            "problem": problem,
            "main_idea": f"重新定义{random.choice(['问题边界', '核心需求', '解决路径', '成功标准'])}",
            "benefit": f"带来{random.choice(['更可持续的', '更创新的', '更高效的', '更全面的'])}解决方案",
            "reverse_perspective": f"我们{random.choice(['不是解决问题而是消除问题', '追求相反目标', '关注限制而非可能性'])}",
            "solution": f"可以{random.choice(['转变思路', '重新定义成功', '消除根源问题', '创造新路径'])}"
        }
        
        # 添加约束相关组件
        if constraints and len(constraints) > 0:
            constraint = random.choice(constraints)
            components["constraint"] = constraint
            components["constraint_approach"] = f"将{constraint}视为{random.choice(['机会', '创新源', '差异化点', '优势'])}"
        else:
            components["constraint"] = "现有限制"
            components["constraint_approach"] = "将限制转化为机会"
        
        # 基于技巧添加特定组件
        if technique == "random_stimulation":
            components.update({
                "random_element": random.choice(self.creative_stimuli["random_words"]),
                "approach": f"{random.choice(['重新组织', '转变视角', '创建新联系', '融合不同领域'])}",
                "outcome": f"产生{random.choice(['意外收益', '创新突破', '多维解决方案', '可持续影响'])}"
            })
        elif technique == "analogy_mapping":
            components.update({
                "analogy_domain": random.choice(["自然系统", "艺术创作", "社交网络", "游戏设计", "建筑学"]),
                "mapping_solution": f"{random.choice(['应用其模式', '借鉴其原理', '模拟其结构', '采用其策略'])}",
                "analogy_example": f"{random.choice(['蜂巢优化资源分配', '爵士乐即兴创作', '生态系统平衡反馈', '故事结构引导体验'])}"
            })
        elif technique == "concept_blending":
            components.update({
                "concept1": random.choice(["设计思维", "系统理论", "游戏机制", "自然模式", "社交动态"]),
                "concept2": random.choice(["创新过程", "适应策略", "用户体验", "循环经济", "学习模型"]),
                "blended_solution": f"一种{random.choice(['更具适应性', '更加整合', '更为创新', '更有韧性'])}的方法"
            })
        
        return components
    
    def _generate_solution_details(self, problem: str, components: Dict[str, str], technique: str) -> str:
        """生成解决方案详细说明"""
        # 详细说明模板
        detail_templates = [
            "具体来说，这个解决方案可以分为以下步骤：\n1. {step1}\n2. {step2}\n3. {step3}",
            "这一方案的核心原则是{principle}，实施过程需要注意：\n- {point1}\n- {point2}\n- {point3}",
            "这种创新方法的优势在于{advantage}，同时能够{benefit}，尤其适用于{context}的情境。"
        ]
        
        # 生成步骤和要点
        steps = [
            f"首先，{random.choice(['重新定义', '分析', '探索', '确认'])}问题的{random.choice(['本质', '边界', '约束', '机会'])}",
            f"然后，应用{components.get('technique', technique)}方法，{random.choice(['发展', '创造', '设计', '整合'])}解决方案",
            f"最后，{random.choice(['评估', '调整', '实施', '优化'])}方案并{random.choice(['收集反馈', '迭代改进', '扩展应用', '分析结果'])}"
        ]
        
        points = [
            f"关注{random.choice(['整体系统', '关键节点', '用户需求', '长期影响'])}",
            f"平衡{random.choice(['创新与实用', '短期与长期', '复杂性与简洁性', '多样性与一致性'])}",
            f"保持{random.choice(['开放心态', '灵活调整', '系统思维', '批判性反思'])}"
        ]
        
        # 选择并填充模板
        template = random.choice(detail_templates)
        details = template.format(
            step1=steps[0],
            step2=steps[1],
            step3=steps[2],
            point1=points[0],
            point2=points[1],
            point3=points[2],
            principle=f"{random.choice(['整合多元视角', '转化限制为机会', '创造性连接', '系统性思考'])}",
            advantage=f"它{random.choice(['打破常规思维', '整合多元视角', '创造独特价值', '适应复杂情境'])}",
            benefit=f"{random.choice(['降低实施风险', '提高解决效率', '增强可持续性', '创造意外收益'])}",
            context=f"{random.choice(['快速变化', '资源有限', '高度不确定', '多元需求'])}"
        )
        
        return details
    
    def _get_creative_influence_factors(self) -> Dict[str, float]:
        """获取创造力影响因子"""
        # 基础创造力因子
        creative_factors = {
            "creative_thinking": 0.7,        # 创造性思维能力
            "divergence": 0.6,               # 发散程度
            "remote_association": 0.5,       # 远距离联想能力
            "constraint_relaxation": 0.6,    # 约束放松程度
            "tolerance_for_ambiguity": 0.5   # 对模糊性的容忍度
        }
        
        # 尝试从情感模块获取情感影响
        try:
            from cognitive_modules.emotion.emotion_simulation import get_instance
            emotion_module = get_instance()
            if emotion_module:
                cognitive_influences = emotion_module.get_cognitive_influence()
                if cognitive_influences and "factors" in cognitive_influences:
                    factors = cognitive_influences["factors"]
                    if "creative_thinking" in factors:
                        creative_factors["creative_thinking"] = max(0.3, min(1.0, 0.5 + factors["creative_thinking"]))
                    if "attention_breadth" in factors:
                        creative_factors["divergence"] = max(0.3, min(1.0, 0.5 + factors["attention_breadth"]))
        except Exception as e:
            logger.warning_status(f"获取情感影响失败: {e}")
        
        # 基于当前创造状态调整
        creative_factors["creative_thinking"] *= (0.8 + self.creative_state["inspiration_level"] * 0.4)
        creative_factors["divergence"] *= (0.9 + self.creative_state["divergence_degree"] * 0.2)
        
        # 确保所有因子在0-1范围内
        for key in creative_factors:
            creative_factors[key] = max(0.0, min(1.0, creative_factors[key]))
        
        return creative_factors
    
    def _select_technique_by_emotion(self, emotional_state: Dict[str, Any]) -> str:
        """基于情感状态选择创造技巧"""
        # 默认技巧
        default_technique = "brainstorming"
        
        if not emotional_state:
            return default_technique
        
        # 情感与技巧映射
        emotion_technique_map = {
            "喜悦": ["brainstorming", "random_stimulation"],
            "好奇": ["concept_blending", "random_stimulation"],
            "平静": ["analogy_mapping", "metaphorical_thinking"],
            "期待": ["brainstorming", "concept_blending"],
            "专注": ["analogy_mapping", "pattern_breaking"],
            "焦虑": ["reverse_thinking", "pattern_breaking"],
            "惊讶": ["random_stimulation", "concept_blending"]
        }
        
        # 获取当前情感
        current_emotion = emotional_state.get("current_emotion", "")
        
        if current_emotion in emotion_technique_map:
            return random.choice(emotion_technique_map[current_emotion])
        else:
            return default_technique
    
    def _evaluate_option_creativity(self, option: Dict[str, Any], context: Dict[str, Any], 
                                   creative_factors: Dict[str, float]) -> Dict[str, Any]:
        """评估选项的创造性"""
        # 提取选项信息
        option_description = option.get("description", "")
        option_tags = option.get("tags", [])
        
        # 创造性评估维度
        novelty = 0.5       # 新颖性
        relevance = 0.7     # 相关性
        surprise = 0.5      # 惊奇度
        elegance = 0.6      # 优雅度
        
        # 基于标签调整评分
        creativity_enhancing_tags = ["creative", "innovative", "novel", "unique", "original", "unexpected"]
        creativity_reducing_tags = ["conventional", "standard", "typical", "safe", "proven", "common"]
        
        for tag in option_tags:
            if tag.lower() in creativity_enhancing_tags:
                novelty += 0.1
                surprise += 0.1
            if tag.lower() in creativity_reducing_tags:
                novelty -= 0.1
                surprise -= 0.1
        
        # 应用创造力影响因子
        novelty *= (0.8 + creative_factors.get("creative_thinking", 0) * 0.4)
        surprise *= (0.7 + creative_factors.get("divergence", 0) * 0.6)
        
        # 计算整体创造性评分
        creativity_score = (novelty * 0.4 + relevance * 0.2 + surprise * 0.3 + elegance * 0.1)
        
        # 确保评分在0-1范围内
        creativity_score = max(0.0, min(1.0, creativity_score))
        
        return {
            "novelty": max(0.0, min(1.0, novelty)),
            "relevance": max(0.0, min(1.0, relevance)), 
            "surprise": max(0.0, min(1.0, surprise)),
            "elegance": max(0.0, min(1.0, elegance)),
            "creativity_score": creativity_score,
            "creative_justification": self._generate_creative_justification(option, creativity_score)
        }
    
    def _generate_creative_justification(self, option: Dict[str, Any], creativity_score: float) -> str:
        """生成创造性评估理由"""
        # 基于创造性评分选择理由模板
        if creativity_score > 0.8:
            templates = [
                "这个选项展现了高度创新性，打破了常规思维模式",
                "选择结合了多元视角，创造了独特的解决路径",
                "该方案重新定义了问题框架，开辟了新的可能性"
            ]
        elif creativity_score > 0.6:
            templates = [
                "这个选项在现有框架内引入了创新元素",
                "方案结合了不同思路，形成了新的解决角度",
                "选择展现了一定的创造性思维，超越了常规方法"
            ]
        elif creativity_score > 0.4:
            templates = [
                "这个选项有一些创新点，但仍在常规思维范围内",
                "方案调整了现有方法，具有一定的新颖性",
                "选择提供了略微不同的角度看待问题"
            ]
        else:
            templates = [
                "这个选项遵循常规思路，创新程度有限",
                "方案采用了传统方法，可靠但缺乏新意",
                "选择保持在已知解决框架内，较为保守"
            ]
        
        return random.choice(templates)
    
    def _generate_alternative_ideas(self, selected_option: Dict[str, Any], context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成替代创意"""
        # 基于选定选项生成1-2个替代创意
        alternative_count = random.randint(1, 2)
        alternatives = []
        
        for i in range(alternative_count):
            alternative = {
                "id": f"alt_{int(time.time())}_{random.randint(1000, 9999)}",
                "description": f"另一种思路是{random.choice(['转变视角', '整合不同元素', '应用不同领域的原则', '重新定义边界'])}",
                "variation_type": random.choice(["extension", "combination", "reversal", "adaptation"]),
                "relation_to_selected": random.choice(["complementary", "alternative", "extension", "variation"])
            }
            alternatives.append(alternative)
        
        return alternatives
    
    def _divergent_thinking(self, seed: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """发散思维"""
        if not seed:
            seed = random.choice(self.creative_domains)
        
        # 获取创造力影响因子
        creative_factors = self._get_creative_influence_factors()
        
        # 生成发散思维结果
        associations = []
        association_count = random.randint(3, 5)
        
        for i in range(association_count):
            association = {
                "id": f"association_{int(time.time())}_{i}",
                "seed": seed,
                "concept": self._generate_associated_concept(seed),
                "connection_type": random.choice(["similarity", "contrast", "causality", "adjacency", "composition"]),
                "distance": random.uniform(0.3, 0.9)
            }
            associations.append(association)
        
        # 生成发散思维的综合
        synthesis = self._synthesize_associations(seed, associations)
        
        return {
            "success": True,
            "seed": seed,
            "associations": associations,
            "synthesis": synthesis,
            "divergence_factor": creative_factors.get("divergence", 0.6)
        }
    
    def _generate_associated_concept(self, seed: str) -> str:
        """生成关联概念"""
        # 尝试从概念网络中获取相关概念
        if seed in self.concept_network and self.concept_network[seed]:
            # 有时选择现有关联，有时创建新关联
            if random.random() < 0.7 and self.concept_network[seed]:
                # 从现有关联中选择
                concepts = list(self.concept_network[seed].keys())
                return random.choice(concepts)
        
        # 创建新关联
        # 关联模板
        association_templates = [
            f"{seed}的{random.choice(['对立面', '变体', '补充', '应用'])}",
            f"在{random.choice(['不同尺度', '另一领域', '新环境', '极端条件'])}下的{seed}",
            f"{seed}与{random.choice(self.creative_stimuli['random_concepts'])}的交叉点"
        ]
        
        return random.choice(association_templates)
    
    def _synthesize_associations(self, seed: str, associations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """综合关联形成新观点"""
        # 提取关联概念
        concepts = [a["concept"] for a in associations]
        
        # 综合模板
        synthesis_templates = [
            f"将{seed}视为{random.choice(concepts)}与{random.choice(concepts)}的交叉领域",
            f"重新定义{seed}，使其包含{', '.join(random.sample(concepts, min(2, len(concepts))))}的要素",
            f"从{random.choice(concepts)}的角度思考{seed}，揭示新的可能性"
        ]
        
        insight = random.choice(synthesis_templates)
        
        return {
            "id": f"synthesis_{int(time.time())}",
            "insight": insight,
            "potential_applications": [
                f"用于{random.choice(['解决问题', '创新设计', '改进流程', '扩展理解'])}",
                f"创造新的{random.choice(['概念框架', '交流方式', '体验设计', '学习模型'])}"
            ]
        }
    
    def _analogical_reasoning(self, source: str, target: str) -> Dict[str, Any]:
        """类比思维"""
        if not source or not target:
            return {
                "success": False,
                "error": "需要提供源域和目标域"
            }
        
        # 类比映射
        mappings = []
        mapping_count = random.randint(2, 4)
        
        for i in range(mapping_count):
            mapping = {
                "id": f"mapping_{int(time.time())}_{i}",
                "source_element": self._generate_domain_element(source),
                "target_element": self._generate_domain_element(target),
                "relation": random.choice(["结构对应", "功能相似", "过程类似", "目的一致", "角色类比"]),
                "strength": random.uniform(0.6, 0.9)
            }
            mappings.append(mapping)
        
        # 生成整体类比
        analogy = {
            "id": f"analogy_{int(time.time())}",
            "source_domain": source,
            "target_domain": target,
            "mappings": mappings,
            "insight": f"{target}就像{source}一样，{random.choice(['具有内在结构', '遵循相似原则', '存在对应关系', '经历类似过程'])}",
            "applications": [
                f"使用{source}的{random.choice(['原理', '模型', '策略', '结构'])}来理解{target}",
                f"借鉴{source}领域的{random.choice(['解决方案', '最佳实践', '创新方法', '发展路径'])}应用于{target}"
            ]
        }
        
        return {
            "success": True,
            "analogy": analogy
        }
    
    def _generate_domain_element(self, domain: str) -> str:
        """生成领域元素"""
        # 领域元素模板
        element_templates = [
            f"{domain}的{random.choice(['核心组件', '基本原理', '关键过程', '结构特征'])}",
            f"{domain}中的{random.choice(['互动模式', '资源流动', '边界条件', '反馈机制'])}",
            f"{domain}的{random.choice(['发展阶段', '适应策略', '组织结构', '功能单元'])}"
        ]
        
        return random.choice(element_templates)
    
    def _metaphorical_reasoning(self, concept: str) -> Dict[str, Any]:
        """隐喻思维"""
        if not concept:
            return {
                "success": False,
                "error": "需要提供概念"
            }
        
        # 随机选择一个源域
        source_domains = [
            "旅程", "容器", "建筑", "有机体", "网络", "资源", "力量", "平衡"
        ]
        source_domain = random.choice(source_domains)
        
        # 为源域生成特征
        source_features = self._generate_source_domain_features(source_domain)
        
        # 生成映射
        mappings = []
        for i in range(min(3, len(source_features))):
            mapping = {
                "source_feature": source_features[i],
                "target_interpretation": self._generate_metaphorical_mapping(concept, source_features[i])
            }
            mappings.append(mapping)
        
        # 构建完整隐喻
        metaphor = {
            "id": f"metaphor_{int(time.time())}",
            "concept": concept,
            "source_domain": source_domain,
            "statement": f"{concept}是一种{source_domain}",
            "mappings": mappings,
            "elaboration": f"将{concept}视为{source_domain}，帮助我们{random.choice(['理解其本质', '发现新视角', '重新构建关系', '打破常规思维'])}"
        }
        
        return {
            "success": True,
            "metaphor": metaphor
        }
    
    def _generate_source_domain_features(self, domain: str) -> List[str]:
        """生成源域特征"""
        # 不同领域的特征
        domain_features = {
            "旅程": ["起点", "路径", "目的地", "障碍", "里程碑", "方向", "伙伴", "行囊"],
            "容器": ["边界", "内容物", "容量", "开口", "结构", "保护性", "可携带性"],
            "建筑": ["基础", "支柱", "墙壁", "门窗", "结构", "功能区", "设计风格"],
            "有机体": ["生长", "适应", "新陈代谢", "繁殖", "生命周期", "感知", "反应"],
            "网络": ["节点", "连接", "密度", "中心性", "路径", "流动", "扩展性"],
            "资源": ["价值", "稀缺性", "分配", "利用", "再生", "储存", "交换"],
            "力量": ["强度", "方向", "影响", "对抗", "平衡", "来源", "传递"],
            "平衡": ["稳定性", "调整", "对称", "重心", "恢复力", "波动", "协调"]
        }
        
        if domain in domain_features:
            # 选择2-4个特征
            count = min(len(domain_features[domain]), random.randint(2, 4))
            return random.sample(domain_features[domain], count)
        else:
            # 默认特征
            return ["元素", "结构", "功能", "特性"]
    
    def _generate_metaphorical_mapping(self, concept: str, source_feature: str) -> str:
        """生成隐喻映射"""
        # 映射模板
        mapping_templates = [
            f"{concept}中对应的{source_feature}是其{random.choice(['本质特性', '关键组成', '发展过程', '互动方式'])}",
            f"{source_feature}在{concept}中表现为{random.choice(['变化模式', '关系结构', '价值创造', '边界定义'])}",
            f"就像{source_feature}一样，{concept}的{random.choice(['核心机制', '基本原则', '运作方式', '发展路径'])}也是如此"
        ]
        
        return random.choice(mapping_templates)
    
    def _combinatorial_reasoning(self, elements: List[str]) -> Dict[str, Any]:
        """组合思维"""
        if not elements or len(elements) < 2:
            return {
                "success": False,
                "error": "需要至少两个元素进行组合"
            }
        
        # 选择2-3个元素进行组合
        selected_elements = random.sample(elements, min(len(elements), random.randint(2, 3)))
        
        # 组合方式
        combination_types = [
            "融合", "叠加", "互补", "转化", "重构", "替代", "系统化"
        ]
        combination_type = random.choice(combination_types)
        
        # 生成组合结果
        combination = {
            "id": f"combination_{int(time.time())}",
            "elements": selected_elements,
            "combination_type": combination_type,
            "result": self._generate_combination_result(selected_elements, combination_type),
            "potential": random.uniform(0.6, 0.9),
            "applications": [
                f"用于{random.choice(['解决问题', '创新设计', '改进流程', '扩展理解'])}",
                f"创造新的{random.choice(['产品', '服务', '方法', '体验', '概念'])}"
            ]
        }
        
        return {
            "success": True,
            "combination": combination
        }
    
    def _generate_combination_result(self, elements: List[str], combination_type: str) -> str:
        """生成组合结果"""
        elements_text = "、".join(elements)
        
        # 结果模板
        result_templates = {
            "融合": f"{elements[0]}与{elements[-1]}的融合产物，兼具{elements_text}的特性",
            "叠加": f"{elements[0]}为基础，叠加{elements[-1]}的特点，形成多层次结构",
            "互补": f"{elements[0]}和{elements[-1]}互补共生，相互增强各自优势",
            "转化": f"通过{elements[-1]}的视角重新诠释{elements[0]}，实现概念转化",
            "重构": f"将{elements_text}的核心要素重新排列组合，创造新结构",
            "替代": f"用{elements[-1]}的某些特性替代{elements[0]}中的对应部分",
            "系统化": f"将{elements_text}整合为一个有机系统，各元素相互关联"
        }
        
        if combination_type in result_templates:
            return result_templates[combination_type]
        else:
            # 默认模板
            return f"{elements_text}的创新组合，形成新的可能性"
    
    def _background_creative_thinking(self):
        """后台创造性思维线程"""
        while self.creative_thread_active:
            # 检查是否需要触发创造性思维
            if (time.time() - self.creative_state["last_creative_time"] > 3600 and  # 间隔至少1小时
                random.random() < 0.3):  # 30%的概率触发
                
                self._trigger_autonomous_creative_thinking()
                
                # 更新上次创造时间
                self.creative_state["last_creative_time"] = time.time()
            
            # 更新创造性状态
            self._update_creative_state()
            
            # 休眠一段时间
            time.sleep(300)  # 5分钟检查一次
    
    def _update_creative_state(self):
        """更新创造性状态"""
        # 灵感波动
        inspiration_change = random.uniform(-0.1, 0.1)
        self.creative_state["inspiration_level"] = max(0.2, min(0.9, 
                                                           self.creative_state["inspiration_level"] + inspiration_change))
        
        # 发散程度波动
        divergence_change = random.uniform(-0.05, 0.05)
        self.creative_state["divergence_degree"] = max(0.3, min(0.8, 
                                                           self.creative_state["divergence_degree"] + divergence_change))
        
        # 如果最近产生了很多创意，可能会导致临时"创意枯竭"
        if self.creative_state["session_ideas_count"] > 20:
            self.creative_state["inspiration_level"] *= 0.9
            self.creative_state["session_ideas_count"] = 0
    
    def _trigger_autonomous_creative_thinking(self):
        """触发自主创造性思维"""
        # 从创造性领域中选择一个
        domain = random.choice(self.creative_domains)
        
        # 随机选择一个创造技巧
        technique = random.choice(list(self.creative_techniques.keys()))
        
        # 生成创意
        ideas_result = self.generate_ideas(domain, count=1, technique=technique)
        
        if ideas_result["success"] and ideas_result["ideas"]:
            # 记录创意
            idea = ideas_result["ideas"][0]
            
            # 发布自主创意事件
            self.event_bus.publish("autonomous_creative_idea", {
                "module_id": self.module_id,
                "domain": domain,
                "technique": technique,
                "idea": idea,
                "inspiration_level": self.creative_state["inspiration_level"]
            })
            
            logger.info(f"触发自主创造性思维: 领域={domain}, 技巧={technique}")
    
    def _on_system_idle(self, event_data: Dict[str, Any]):
        """系统空闲事件处理"""
        # 在系统空闲时有几率进行创造性思考
        if random.random() < 0.2:  # 20%的概率
            self._trigger_autonomous_creative_thinking()
    
    def _on_user_message(self, event_data: Dict[str, Any]):
        """用户消息事件处理"""
        # 提取用户消息
        message = event_data.get("message", "")
        if not message:
            return
        
        # 检查消息是否与创造性相关
        creativity_keywords = ["创意", "创新", "想法", "灵感", "创造", "新方法", "不同角度", "可能性"]
        
        is_creativity_related = any(keyword in message for keyword in creativity_keywords)
        
        if is_creativity_related:
            # 增加灵感水平
            self.creative_state["inspiration_level"] = min(1.0, self.creative_state["inspiration_level"] + 0.2)
            
            # 根据消息主题调整焦点领域
            for domain in self.creative_domains:
                if domain in message:
                    self.creative_state["focus_domain"] = domain
                    break
    
    def _on_emotion_updated(self, event_data: Dict[str, Any]):
        """情感更新事件处理"""
        # 提取情感状态
        emotion = event_data.get("emotion", "")
        intensity = event_data.get("intensity", 0.5)
        
        if not emotion:
            return
        
        # 不同情感对创造力的影响
        creativity_enhancing = ["好奇", "喜悦", "期待", "惊讶"]
        creativity_neutral = ["平静", "专注", "满足"]
        creativity_reducing = ["焦虑", "恐惧", "悲伤", "愤怒"]
        
        # 调整灵感水平
        if emotion in creativity_enhancing:
            change = 0.1 * intensity
            self.creative_state["inspiration_level"] = min(1.0, self.creative_state["inspiration_level"] + change)
            self.creative_state["divergence_degree"] = min(0.9, self.creative_state["divergence_degree"] + change * 0.5)
        elif emotion in creativity_reducing:
            change = 0.1 * intensity
            self.creative_state["inspiration_level"] = max(0.2, self.creative_state["inspiration_level"] - change)
            # 某些负面情绪可能增加发散思维
            if emotion in ["焦虑", "恐惧"]:
                self.creative_state["divergence_degree"] = min(0.9, self.creative_state["divergence_degree"] + change * 0.3)
    
    def _on_insight_generated(self, event_data: Dict[str, Any]):
        """洞察生成事件处理"""
        # 提取洞察内容
        insight = event_data.get("insight", "")
        source = event_data.get("source", "")
        
        if not insight:
            return
        
        # 洞察可能触发创造性思维
        if random.random() < 0.4:  # 40%的概率
            # 增加灵感水平
            self.creative_state["inspiration_level"] = min(1.0, self.creative_state["inspiration_level"] + 0.15)
            
            # 尝试基于洞察生成创意
            self._divergent_thinking(insight[:50])
    
    def _get_creative_history(self, count: int = 5, domain: str = None) -> Dict[str, Any]:
        """获取创造性思维历史"""
        # 过滤历史记录
        filtered_history = self.creative_history
        if domain:
            filtered_history = [h for h in self.creative_history if 
                               (h.get("topic") == domain or 
                                h.get("domain") == domain or 
                                domain in h.get("concepts", []))]
        
        # 按时间排序并限制数量
        sorted_history = sorted(filtered_history, key=lambda x: x.get("timestamp", 0), reverse=True)
        limited_history = sorted_history[:count]
        
        return {
            "success": True,
            "count": len(limited_history),
            "domain": domain,
            "history": limited_history
        }
    
    def _add_concept_relation(self, concept1: str, concept2: str, strength: float = 0.5):
        """添加概念关系"""
        # 确保概念存在于网络中
        if concept1 not in self.concept_network:
            self.concept_network[concept1] = {}
        if concept2 not in self.concept_network:
            self.concept_network[concept2] = {}
        
        # 添加双向关系
        self.concept_network[concept1][concept2] = strength
        self.concept_network[concept2][concept1] = strength
    
    def _update_concept_network(self, topic: str, contents: List[str]):
        """更新概念网络"""
        # 从内容中提取潜在概念
        potential_concepts = set()
        for content in contents:
            # 简单分词（实际应用中可以使用更复杂的NLP方法）
            words = content.replace("，", " ").replace("。", " ").replace("、", " ").split()
            for word in words:
                if len(word) >= 2:  # 只考虑长度>=2的词
                    potential_concepts.add(word)
        
        # 限制概念数量
        potential_concepts = list(potential_concepts)[:5]
        
        # 添加主题与概念的关系
        for concept in potential_concepts:
            self._add_concept_relation(topic, concept, 0.7)
        
        # 添加概念间关系
        for i in range(len(potential_concepts)):
            for j in range(i+1, len(potential_concepts)):
                self._add_concept_relation(potential_concepts[i], potential_concepts[j], 0.5)
    
    def _trim_history(self):
        """裁剪历史记录，保持在最大长度内"""
        if len(self.creative_history) > self.max_history_size:
            # 按时间排序
            sorted_history = sorted(self.creative_history, key=lambda x: x.get("timestamp", 0), reverse=True)
            # 保留最近的历史记录
            self.creative_history = sorted_history[:self.max_history_size]
    
    def _load_creative_history(self):
        """加载创造性历史"""
        try:
            storage_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                      "storage", self.module_id)
            
            # 创建存储目录（如果不存在）
            os.makedirs(storage_dir, exist_ok=True)
            
            history_path = os.path.join(storage_dir, "creative_history.json")
            if os.path.exists(history_path):
                with open(history_path, 'r', encoding='utf-8') as f:
                    self.creative_history = json.load(f)
                logger.info(f"已加载创造性历史，包含 {len(self.creative_history)} 条记录")
        except Exception as e:
            logger.error_status(f"加载创造性历史失败: {e}")
            self.creative_history = []
    
    def _save_creative_history(self):
        """保存创造性历史"""
        try:
            storage_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                      "storage", self.module_id)
            
            # 创建存储目录（如果不存在）
            os.makedirs(storage_dir, exist_ok=True)
            
            history_path = os.path.join(storage_dir, "creative_history.json")
            with open(history_path, 'w', encoding='utf-8') as f:
                json.dump(self.creative_history, f, ensure_ascii=False, indent=2)
            logger.debug(f"已保存创造性历史，包含 {len(self.creative_history)} 条记录")
        except Exception as e:
            logger.error_status(f"保存创造性历史失败: {e}")
    
    def _load_concept_network(self):
        """加载概念网络"""
        try:
            storage_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                      "storage", self.module_id)
            
            # 创建存储目录（如果不存在）
            os.makedirs(storage_dir, exist_ok=True)
            
            network_path = os.path.join(storage_dir, "concept_network.json")
            if os.path.exists(network_path):
                with open(network_path, 'r', encoding='utf-8') as f:
                    self.concept_network = json.load(f)
                logger.info(f"已加载概念网络，包含 {len(self.concept_network)} 个概念")
        except Exception as e:
            logger.error_status(f"加载概念网络失败: {e}")
            self.concept_network = {}
    
    def _save_concept_network(self):
        """保存概念网络"""
        try:
            storage_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                      "storage", self.module_id)
            
            # 创建存储目录（如果不存在）
            os.makedirs(storage_dir, exist_ok=True)
            
            network_path = os.path.join(storage_dir, "concept_network.json")
            with open(network_path, 'w', encoding='utf-8') as f:
                json.dump(self.concept_network, f, ensure_ascii=False, indent=2)
            logger.debug(f"已保存概念网络，包含 {len(self.concept_network)} 个概念")
        except Exception as e:
            logger.error_status(f"保存概念网络失败: {e}")
    
    def decide(self, options: List[Dict[str, Any]], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """创造性决策
        
        基于创造性思维评估多个选项，选择最具创造性的选项
        
        Args:
            options: 待评估的选项列表
            context: 决策上下文
            
        Returns:
            决策结果
        """
        if not options:
            return {
                "success": False,
                "error": "没有提供选项"
            }
        
        # 使用默认上下文
        if context is None:
            context = {}
        
        # 获取创造力影响因子
        creative_factors = self._get_creative_influence_factors()
        
        # 评估所有选项的创造性
        evaluations = []
        for option in options:
            evaluation = self._evaluate_option_creativity(option, context, creative_factors)
            evaluations.append(evaluation)
        
        # 根据创造性评分选择最佳选项
        best_index = max(range(len(evaluations)), key=lambda i: evaluations[i]["creativity_score"])
        selected_option = options[best_index]
        selected_evaluation = evaluations[best_index]
        
        # 生成替代创意
        alternative_ideas = self._generate_alternative_ideas(selected_option, context)
        
        # 构建决策结果
        decision = {
            "selected_option": selected_option,
            "creativity_evaluation": selected_evaluation,
            "alternative_ideas": alternative_ideas,
            "creative_factors": creative_factors,
            "timestamp": time.time()
        }
        
        return {
            "success": True,
            "decision": decision
        }

# 模块单例访问函数
def get_instance(module_id: str = None, config: Dict[str, Any] = None) -> CreativeThinking:
    """
    获取创造性思维模块的单例实例
    
    Args:
        module_id: 模块ID
        config: 配置信息
        
    Returns:
        创造性思维模块实例
    """
    return CreativeThinking(module_id, config) 