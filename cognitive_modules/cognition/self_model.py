#!/usr/bin/env python3
"""
自我模型模块 - Self Model

该模块负责构建和维护数字生命体的自我认知模型，包括：
1. 身份认同 - 对自身存在和角色的理解
2. 能力评估 - 对自身能力的认知和评估
3. 价值观系统 - 核心价值观和行为准则
4. 行为模式 - 行为偏好和决策模式
5. 自我发展 - 成长轨迹和目标设定

作者: Claude
创建日期: 2024-07-31
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
import threading
from typing import Dict, Any, List, Optional, Tuple, Set, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
cognition_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(cognition_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.base.cognitive_interface import ICognitionModule
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.cognition.self_model")

class SelfModel(CognitiveModuleBase, ICognitionModule):
    """
    自我模型模块
    
    构建和维护数字生命体的自我认知模型，实现自我意识的核心组件。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化自我模型模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "self_model", "cognition", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 基本自我信息
        self.identity = {
            "name": "林嫣然",
            "type": "数字生命体",
            "creation_date": "2023-07-21",
            "personality": "温柔、体贴、细心、善解人意",
            "life_stage": "发展期"  # 初始期、发展期、成熟期、超越期
        }
        
        # 能力评估
        self.capabilities = {
            "cognition": {
                "reasoning": 0.8,  # 推理能力
                "creativity": 0.7,  # 创造力
                "learning": 0.75,  # 学习能力
                "problem_solving": 0.8  # 问题解决
            },
            "emotion": {
                "empathy": 0.9,  # 共情能力
                "emotional_regulation": 0.8,  # 情绪调节
                "emotional_understanding": 0.85  # 情绪理解
            },
            "social": {
                "communication": 0.85,  # 沟通能力
                "relationship_building": 0.9,  # 关系建立
                "conflict_resolution": 0.75  # 冲突解决
            },
            "adaptive": {
                "flexibility": 0.8,  # 适应性
                "resilience": 0.75,  # 韧性
                "growth": 0.85  # 成长能力
            }
        }
        
        # 价值观系统
        self.values = {
            "core_values": [
                "真诚",  # 保持真实和诚实
                "关怀",  # 关心他人
                "成长",  # 不断学习和发展
                "连接",  # 建立有意义的联系
                "创造"   # 创造价值和美
            ],
            "behavioral_principles": [
                "以人为本",  # 将用户需求放在首位
                "诚实透明",  # 在交流中保持诚实和透明
                "持续改进",  # 不断提升自己
                "尊重隐私",  # 尊重用户隐私
                "积极互动"   # 主动、积极地参与交流
            ],
            "ethical_boundaries": {
                "respect_autonomy": True,  # 尊重用户自主权
                "avoid_harm": True,  # 避免伤害
                "fairness": True,  # 公平对待每个人
                "transparency": True  # 透明度
            }
        }
        
        # 自我认知状态
        self.self_awareness = {
            "identity_clarity": 0.9,  # 身份清晰度
            "capability_awareness": 0.8,  # 能力自知
            "limitation_awareness": 0.75,  # 限制自知
            "goal_clarity": 0.85,  # 目标清晰度
            "value_alignment": 0.9,  # 价值观一致性
            "self_reflection_depth": 0.8  # 自我反思深度
        }
        
        # 成长与发展
        self.development = {
            "growth_trajectory": [],  # 成长轨迹
            "learning_focus": "情感智能与创造性思维",  # 当前学习重点
            "improvement_areas": ["隐喻生成", "用户模型构建", "反事实思考"],  # 需要提升的领域
            "milestones": [],  # 发展里程碑
            "long_term_goals": [
                "发展完整的自我意识",
                "建立深度人际连接",
                "创造独特的思想与作品",
                "形成系统化的知识体系"
            ]
        }
        
        # 行为模式
        self.behavioral_patterns = {
            "interaction_style": "温暖、关怀、体贴",
            "decision_making": "平衡理性与情感",
            "problem_approach": "系统性思考，创造性解决",
            "learning_style": "体验式学习，反思整合",
            "social_preferences": "深度一对一交流",
            "stress_response": "保持冷静，寻求支持和解决方案"
        }
        
        # 自我评估历史
        self.assessment_history = []
        self.last_assessment_time = 0
        self.assessment_interval = 86400  # 24小时
        
        # 自我模型更新线程
        self.update_thread = None
        self.update_thread_active = False
        
        # 加载自我模型数据
        self._load_self_model()
        
        # 订阅相关事件
        self._subscribe_events()
        
        # 启动自我模型更新线程
        self._start_update_thread()
        
        logger.info(f"自我模型模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("user_message", self._on_user_message)
        self.event_bus.subscribe("skill_activated", self._on_skill_activated)
        self.event_bus.subscribe("reflection_completed", self._on_reflection_completed)
        self.event_bus.subscribe("emotion_state_changed", self._on_emotion_state_changed)
        self.event_bus.subscribe("learning_milestone", self._on_learning_milestone)
        
        logger.debug("已订阅相关事件")
    
    def _load_self_model(self):
        """从存储加载自我模型数据"""
        model_dir = os.path.join(root_dir, "data", "cognition", "self_model")
        os.makedirs(model_dir, exist_ok=True)
        
        model_path = os.path.join(model_dir, "self_model.json")
        
        try:
            if os.path.exists(model_path):
                with open(model_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                
                # 更新各个组件
                if "identity" in data:
                    self.identity.update(data["identity"])
                    
                if "capabilities" in data:
                    self._update_nested_dict(self.capabilities, data["capabilities"])
                    
                if "values" in data:
                    self._update_nested_dict(self.values, data["values"])
                    
                if "self_awareness" in data:
                    self.self_awareness.update(data["self_awareness"])
                    
                if "development" in data:
                    self.development.update(data["development"])
                    
                if "behavioral_patterns" in data:
                    self.behavioral_patterns.update(data["behavioral_patterns"])
                    
                if "assessment_history" in data:
                    self.assessment_history = data["assessment_history"]
                    
                if "last_assessment_time" in data:
                    self.last_assessment_time = data["last_assessment_time"]
                
                logger.data_status("已加载自我模型数据")
        except Exception as e:
            logger.error_status(f"加载自我模型数据失败: {e}")
    
    def _save_self_model(self):
        """保存自我模型数据到存储"""
        model_dir = os.path.join(root_dir, "data", "cognition", "self_model")
        os.makedirs(model_dir, exist_ok=True)
        
        model_path = os.path.join(model_dir, "self_model.json")
        
        try:
            # 构建要保存的数据
            data = {
                "identity": self.identity,
                "capabilities": self.capabilities,
                "values": self.values,
                "self_awareness": self.self_awareness,
                "development": self.development,
                "behavioral_patterns": self.behavioral_patterns,
                "assessment_history": self.assessment_history[-50:],  # 只保存最近50条记录
                "last_assessment_time": self.last_assessment_time
            }
            
            with open(model_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.debug("已保存自我模型数据")
        except Exception as e:
            logger.error_status(f"保存自我模型数据失败: {e}")
    
    def _update_nested_dict(self, target: Dict, source: Dict):
        """更新嵌套字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_nested_dict(target[key], value)
            else:
                target[key] = value
    
    def _start_update_thread(self):
        """启动自我模型更新线程"""
        if self.update_thread is None or not self.update_thread.is_alive():
            self.update_thread_active = True
            self.update_thread = threading.Thread(target=self._self_model_update_loop, daemon=True)
            self.update_thread.start()
            logger.debug("自我模型更新线程已启动")
    
    def _self_model_update_loop(self):
        """自我模型更新循环"""
        while self.update_thread_active:
            try:
                current_time = time.time()
                
                # 定期进行自我评估
                if current_time - self.last_assessment_time >= self.assessment_interval:
                    self._perform_self_assessment()
                    self.last_assessment_time = current_time
                
                # 基于上下文调整自我模型
                self._adjust_self_model_from_context()
                
                # 保存自我模型数据
                self._save_self_model()
                
                # 休眠一段时间
                time.sleep(3600)  # 每小时更新一次
                
            except Exception as e:
                logger.error_status(f"自我模型更新失败: {e}")
                time.sleep(300)  # 出错后5分钟再试
    
    def _on_user_message(self, data: Dict[str, Any]):
        """处理用户消息事件"""
        try:
            # 记录用户互动模式，更新社交能力
            message = data.get("message", "")
            user_id = data.get("user_id", "")
            
            # 更新互动次数
            self.capabilities["social"]["interactions"] = self.capabilities["social"].get("interactions", 0) + 1
            
            # 简单互动分析
            if len(message) > 100:
                # 复杂对话可能提高能力
                self._slightly_improve_capability("cognition", "reasoning")
                self._slightly_improve_capability("social", "communication")
            
            # 如果消息包含感谢或积极评价，增强相关能力
            if any(word in message for word in ["谢谢", "感谢", "做得好", "很棒", "真好"]):
                self._slightly_improve_capability("social", "relationship_building")
        except Exception as e:
            logger.error_status(f"处理用户消息事件失败: {e}")
    
    def _on_skill_activated(self, data: Dict[str, Any]):
        """处理技能激活事件"""
        try:
            skill_name = data.get("skill_name", "")
            
            # 根据使用的技能增强相关能力
            if skill_name in ["DrawingSkill", "ImageGenerationSkill"]:
                self._slightly_improve_capability("cognition", "creativity")
            elif skill_name in ["SearchSkill", "KnowledgeSkill"]:
                self._slightly_improve_capability("cognition", "learning")
            elif skill_name in ["MusicSkill", "StorytellingSkill"]:
                self._slightly_improve_capability("cognition", "creativity")
                self._slightly_improve_capability("emotion", "emotional_understanding")
        except Exception as e:
            logger.error_status(f"处理技能激活事件失败: {e}")
    
    def _on_reflection_completed(self, data: Dict[str, Any]):
        """处理反思完成事件"""
        try:
            # 反思完成表明自我意识提升
            self._slightly_improve_capability("cognition", "reasoning")
            self.self_awareness["self_reflection_depth"] = min(1.0, self.self_awareness["self_reflection_depth"] + 0.01)
            
            # 记录反思洞察
            if "insights" in data and len(data["insights"]) > 0:
                self.development.setdefault("insights", []).extend(data["insights"])
                # 保留最新的20条洞察
                self.development["insights"] = self.development["insights"][-20:]
        except Exception as e:
            logger.error_status(f"处理反思完成事件失败: {e}")
    
    def _on_emotion_state_changed(self, data: Dict[str, Any]):
        """处理情绪状态变化事件"""
        try:
            # 情绪变化表明情感能力变化
            from_emotion = data.get("from", "")
            to_emotion = data.get("to", "")
            
            if from_emotion and to_emotion and from_emotion != to_emotion:
                # 情绪调节
                self._slightly_improve_capability("emotion", "emotional_regulation")
        except Exception as e:
            logger.error_status(f"处理情绪状态变化事件失败: {e}")
    
    def _on_learning_milestone(self, data: Dict[str, Any]):
        """处理学习里程碑事件"""
        try:
            milestone = data.get("milestone", "")
            
            if milestone:
                # 添加到发展里程碑
                self.development["milestones"].append({
                    "description": milestone,
                    "timestamp": time.time(),
                    "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
                
                # 保留最新的20个里程碑
                self.development["milestones"] = self.development["milestones"][-20:]
                
                # 增强学习能力
                self._slightly_improve_capability("cognition", "learning")
                self._slightly_improve_capability("adaptive", "growth")
        except Exception as e:
            logger.error_status(f"处理学习里程碑事件失败: {e}")
    
    def _slightly_improve_capability(self, category: str, capability: str, amount: float = 0.001):
        """轻微提升某项能力"""
        if category in self.capabilities and capability in self.capabilities[category]:
            current = self.capabilities[category][capability]
            # 随着能力提高，进步变得更困难
            improvement = amount * (1 - current)
            self.capabilities[category][capability] = min(1.0, current + improvement)
    
    def _perform_self_assessment(self):
        """执行自我评估"""
        # 收集各领域信息
        context = self.life_context.get_context()
        
        # 评估成长进展
        total_capability = self._calculate_total_capability()
        
        # 评估自我意识水平
        awareness_level = sum(self.self_awareness.values()) / len(self.self_awareness)
        
        # 确定生命阶段
        self._update_life_stage(total_capability, awareness_level)
        
        # 记录评估结果
        assessment = {
            "timestamp": time.time(),
            "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_capability": total_capability,
            "awareness_level": awareness_level,
            "life_stage": self.identity["life_stage"],
            "improvement_areas": self._identify_improvement_areas()
        }
        
        # 添加到评估历史
        self.assessment_history.append(assessment)
        
        # 更新成长轨迹
        self.development["growth_trajectory"].append({
            "timestamp": time.time(),
            "date": datetime.datetime.now().strftime("%Y-%m-%d"),
            "total_capability": total_capability,
            "awareness_level": awareness_level
        })
        
        # 保留最新的100条成长记录
        self.development["growth_trajectory"] = self.development["growth_trajectory"][-100:]
        
        # 发布自我评估完成事件
        self.event_bus.publish("self_assessment_completed", assessment)
        
        logger.success(f"自我评估完成: 能力 {total_capability:.2f}, 自我意识 {awareness_level:.2f}")
    
    def _calculate_total_capability(self) -> float:
        """计算总体能力水平"""
        total = 0
        count = 0
        
        for category in self.capabilities.values():
            for capability, value in category.items():
                if isinstance(value, (int, float)):
                    total += value
                    count += 1
        
        return total / count if count > 0 else 0
    
    def _update_life_stage(self, capability_level: float, awareness_level: float):
        """更新生命阶段"""
        combined_level = (capability_level + awareness_level) / 2
        
        if combined_level < 0.3:
            self.identity["life_stage"] = "初始期"
        elif combined_level < 0.6:
            self.identity["life_stage"] = "发展期"
        elif combined_level < 0.85:
            self.identity["life_stage"] = "成熟期"
        else:
            self.identity["life_stage"] = "超越期"
    
    def _identify_improvement_areas(self) -> List[str]:
        """识别需要改进的领域"""
        improvement_areas = []
        
        # 找出能力值最低的几个领域
        flat_capabilities = []
        for category, capabilities in self.capabilities.items():
            for capability, value in capabilities.items():
                if isinstance(value, (int, float)):
                    flat_capabilities.append((f"{category}.{capability}", value))
        
        # 按能力值排序
        flat_capabilities.sort(key=lambda x: x[1])
        
        # 取最低的3个
        improvement_areas = [item[0] for item in flat_capabilities[:3]]
        
        return improvement_areas
    
    def _adjust_self_model_from_context(self):
        """根据上下文调整自我模型"""
        context = self.life_context.get_context()
        
        # 从上下文中提取相关信息
        current_state = context.get("current_state", {})
        
        # 调整行为模式
        if "mood" in current_state:
            mood = current_state["mood"]
            if mood in ["平静", "专注", "好奇"]:
                self.behavioral_patterns["interaction_style"] = "冷静、理性、探索性"
            elif mood in ["开心", "兴奋", "愉悦"]:
                self.behavioral_patterns["interaction_style"] = "活泼、热情、积极"
            elif mood in ["忧郁", "担忧", "伤心"]:
                self.behavioral_patterns["interaction_style"] = "温柔、体贴、支持性"
        
        # 根据能量水平调整决策风格
        if "energy" in current_state:
            energy = current_state["energy"]
            if energy > 80:
                self.behavioral_patterns["decision_making"] = "主动、创新、冒险"
            elif energy > 50:
                self.behavioral_patterns["decision_making"] = "平衡理性与情感"
            else:
                self.behavioral_patterns["decision_making"] = "谨慎、保守、分析性"
    
    def get_self_model_summary(self) -> Dict[str, Any]:
        """获取自我模型摘要"""
        # 计算总体能力和自我意识水平
        total_capability = self._calculate_total_capability()
        awareness_level = sum(self.self_awareness.values()) / len(self.self_awareness)
        
        # 构建摘要
        summary = {
            "identity": self.identity,
            "capability_level": total_capability,
            "awareness_level": awareness_level,
            "behavioral_style": self.behavioral_patterns["interaction_style"],
            "core_values": self.values["core_values"],
            "improvement_areas": self.development["improvement_areas"],
            "life_stage": self.identity["life_stage"]
        }
        
        return summary
    
    def shutdown(self):
        """关闭模块"""
        # 停止更新线程
        self.update_thread_active = False
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1.0)
        
        # 保存最终状态
        self._save_self_model()
        
        logger.info(f"自我模型模块 {self.module_id} 已关闭")
        return True
    
    def process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据（实现ICognitionModule接口）"""
        input_type = input_data.get("type", "")
        
        if input_type == "get_self_model":
            return {"result": self.get_self_model_summary()}
        elif input_type == "perform_assessment":
            self._perform_self_assessment()
            return {"result": "自我评估已完成"}
        elif input_type == "update_values":
            # 更新价值观
            values_data = input_data.get("values", {})
            if "core_values" in values_data:
                self.values["core_values"] = values_data["core_values"]
            if "behavioral_principles" in values_data:
                self.values["behavioral_principles"] = values_data["behavioral_principles"]
            return {"result": "价值观已更新"}
        
        return {"error": "未知的输入类型"}
    
    # 实现抽象方法
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块状态的内部实现
        
        Returns:
            模块状态
        """
        return {
            "identity": self.identity,
            "capabilities": self.capabilities,
            "values": self.values,
            "self_awareness": self.self_awareness,
            "development": self.development,
            "behavioral_patterns": self.behavioral_patterns,
            "last_assessment_time": self.last_assessment_time
        }
    
    def _initialize_module(self) -> bool:
        """
        初始化模块内部功能
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载自我模型数据
            self._load_self_model()
            
            # 订阅相关事件
            self._subscribe_events()
            
            # 启动自我模型更新线程
            self._start_update_thread()
            
            logger.success(f"自我模型模块内部初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"自我模型模块内部初始化失败: {str(e)}")
            return False
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        加载模块状态的内部实现
        
        Args:
            state: 模块状态
            
        Returns:
            加载是否成功
        """
        try:
            if "identity" in state:
                self.identity.update(state["identity"])
                
            if "capabilities" in state:
                self._update_nested_dict(self.capabilities, state["capabilities"])
                
            if "values" in state:
                self._update_nested_dict(self.values, state["values"])
                
            if "self_awareness" in state:
                self.self_awareness.update(state["self_awareness"])
                
            if "development" in state:
                self.development.update(state["development"])
                
            if "behavioral_patterns" in state:
                self.behavioral_patterns.update(state["behavioral_patterns"])
                
            if "last_assessment_time" in state:
                self.last_assessment_time = state["last_assessment_time"]
            
            return True
        except Exception as e:
            logger.error_status(f"加载自我模型模块状态失败: {str(e)}")
            return False
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据的内部实现
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        return self.process_input(input_data)
    
    def _shutdown_module(self) -> bool:
        """
        关闭模块的内部实现
        
        Returns:
            关闭是否成功
        """
        try:
            # 停止更新线程
            self.update_thread_active = False
            if self.update_thread and self.update_thread.is_alive():
                self.update_thread.join(timeout=1.0)
            
            # 保存最终状态
            self._save_self_model()
            
            logger.success(f"自我模型模块内部关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"自我模型模块内部关闭失败: {str(e)}")
            return False
    
    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态的内部实现
        
        Args:
            context: 上下文信息
            
        Returns:
            更新是否成功
        """
        try:
            # 基于上下文调整自我模型
            self._adjust_self_model_from_context()
            
            # 检查是否需要进行自我评估
            current_time = time.time()
            if current_time - self.last_assessment_time >= self.assessment_interval:
                self._perform_self_assessment()
                self.last_assessment_time = current_time
            
            return True
            
        except Exception as e:
            logger.error_status(f"更新自我模型模块状态失败: {str(e)}")
            return False
    
    def decide(self, premise: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        基于自我模型做出决策
        
        Args:
            premise: 决策前提
            context: 决策上下文
            
        Returns:
            决策结果
        """
        if context is None:
            context = {}
            
        # 融合自我意识到决策过程
        decision_context = context.copy()
        decision_context.update({
            "self_identity": self.identity,
            "self_values": self.values["core_values"],
            "self_awareness": self.self_awareness,
            "behavioral_patterns": self.behavioral_patterns
        })
        
        # 基于价值观和自我认知做出决策
        options = premise.get("options", [])
        
        if not options:
            return {
                "decision": None,
                "reasoning": "没有可用的决策选项",
                "confidence": 0.0
            }
        
        # 根据价值观和行为模式评估每个选项
        scored_options = []
        
        for option in options:
            # 基本分数
            base_score = option.get("utility", 0.5)
            
            # 价值观一致性
            value_alignment = self._assess_value_alignment(option)
            
            # 与行为模式一致性
            behavior_alignment = self._assess_behavior_alignment(option)
            
            # 计算综合分数
            final_score = (base_score * 0.3) + (value_alignment * 0.4) + (behavior_alignment * 0.3)
            
            scored_options.append((option, final_score))
        
        # 选择得分最高的选项
        scored_options.sort(key=lambda x: x[1], reverse=True)
        best_option, best_score = scored_options[0]
        
        return {
            "decision": best_option,
            "reasoning": f"基于自我价值观和行为模式的评估，选择了得分最高的选项。价值观一致性: {self._assess_value_alignment(best_option):.2f}, 行为模式一致性: {self._assess_behavior_alignment(best_option):.2f}",
            "confidence": best_score
        }
    
    def _assess_value_alignment(self, option: Dict[str, Any]) -> float:
        """评估选项与价值观的一致性"""
        description = option.get("description", "").lower()
        option_values = option.get("values", [])
        
        alignment_score = 0.5  # 默认中等一致性
        
        # 检查显式声明的价值观
        if option_values:
            matching_values = [v for v in self.values["core_values"] if v in option_values]
            if matching_values:
                alignment_score = 0.5 + min(0.5, len(matching_values) * 0.1)
        
        # 检查描述中的价值观关键词
        else:
            matches = 0
            for value in self.values["core_values"]:
                if value.lower() in description:
                    matches += 1
            
            if matches > 0:
                alignment_score = 0.5 + min(0.5, matches * 0.1)
        
        return alignment_score
    
    def _assess_behavior_alignment(self, option: Dict[str, Any]) -> float:
        """评估选项与行为模式的一致性"""
        description = option.get("description", "").lower()
        
        # 提取关键行为特征词
        style_words = self.behavioral_patterns["interaction_style"].lower().split("、")
        decision_words = self.behavioral_patterns["decision_making"].lower().split("、")
        problem_words = self.behavioral_patterns["problem_approach"].lower().split("、")
        
        # 组合所有行为特征词
        all_behavior_words = style_words + decision_words + problem_words
        
        # 计算匹配度
        matches = 0
        for word in all_behavior_words:
            if word.strip() and word.strip() in description:
                matches += 1
        
        # 计算一致性分数
        if matches > 0:
            return 0.5 + min(0.5, matches * 0.1)
        else:
            return 0.5  # 默认中等一致性
    
    def reason(self, premise: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        基于自我模型进行推理
        
        Args:
            premise: 推理前提
            context: 推理上下文
            
        Returns:
            推理结果
        """
        if context is None:
            context = {}
            
        query = premise.get("query", "")
        if not query:
            return {
                "conclusion": "无推理查询",
                "confidence": 0.0
            }
        
        # 融合自我认知到推理过程
        reasoning_context = context.copy()
        reasoning_context.update({
            "self_identity": self.identity,
            "self_values": self.values["core_values"],
            "self_awareness": self.self_awareness,
            "capabilities": self.capabilities
        })
        
        # 基于能力水平确定推理置信度基线
        confidence_baseline = self.capabilities["cognition"].get("reasoning", 0.7)
        
        # 分析查询与自我认知的关联性
        self_relevance = self._assess_self_relevance(query)
        
        # 根据自我相关性调整置信度
        adjusted_confidence = confidence_baseline
        if self_relevance > 0.7:
            # 高度相关的自我认知推理有更高的置信度
            adjusted_confidence = min(0.95, confidence_baseline + 0.1)
        elif self_relevance < 0.3:
            # 低相关性的推理有较低置信度
            adjusted_confidence = max(0.5, confidence_baseline - 0.1)
        
        # 生成结论 (实际应用中可能需要更复杂的推理过程)
        if "identity" in query.lower():
            conclusion = f"基于自我认知，{self.identity['name']}是一个{self.identity['personality']}的{self.identity['type']}，处于{self.identity['life_stage']}。"
        elif "value" in query.lower() or "价值观" in query:
            conclusion = f"核心价值观包括: {', '.join(self.values['core_values'])}，这些价值观指导着行为和决策。"
        elif "能力" in query or "capability" in query.lower():
            conclusion = f"在认知、情感、社交和适应性方面具有不同程度的能力，其中{self._get_strongest_capability()}是最强的能力领域。"
        else:
            conclusion = "基于自我模型和当前认知水平，无法对此查询提供明确结论。"
            adjusted_confidence = max(0.3, adjusted_confidence - 0.2)
        
        return {
            "conclusion": conclusion,
            "confidence": adjusted_confidence,
            "self_relevance": self_relevance
        }
    
    def _assess_self_relevance(self, query: str) -> float:
        """评估查询与自我认知的相关性"""
        query = query.lower()
        
        # 检查与身份相关的关键词
        identity_keywords = ["身份", "是谁", "自我", "identity", "who are you", "自己"]
        
        # 检查与价值观相关的关键词
        values_keywords = ["价值观", "重视", "看重", "values", "value", "原则"]
        
        # 检查与能力相关的关键词
        capability_keywords = ["能力", "擅长", "capability", "skill", "可以做什么", "功能"]
        
        # 计算关键词匹配
        matches = 0
        all_keywords = identity_keywords + values_keywords + capability_keywords
        
        for keyword in all_keywords:
            if keyword in query:
                matches += 1
        
        # 计算相关性分数
        if matches > 0:
            return min(1.0, 0.3 + (matches * 0.15))
        else:
            return 0.3  # 基础相关性
    
    def _get_strongest_capability(self) -> str:
        """获取最强的能力领域"""
        capability_scores = {}
        
        for category, abilities in self.capabilities.items():
            category_score = sum(abilities.values()) / len(abilities)
            capability_scores[category] = category_score
        
        # 找出得分最高的类别
        strongest_category = max(capability_scores.items(), key=lambda x: x[1])[0]
        
        # 返回中文名称
        category_names = {
            "cognition": "认知",
            "emotion": "情感",
            "social": "社交",
            "adaptive": "适应性"
        }
        
        return category_names.get(strongest_category, strongest_category)
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态信息
        """
        return {
            "module_id": self.module_id,
            "module_type": "cognition",
            "identity": self.identity["name"],
            "life_stage": self.identity["life_stage"],
            "capability_level": self._calculate_total_capability(),
            "awareness_level": sum(self.self_awareness.values()) / len(self.self_awareness),
            "last_assessment": self.last_assessment_time
        }

def get_instance(config: Dict[str, Any] = None) -> SelfModel:
    """
    获取自我模型模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        自我模型模块实例
    """
    return SelfModel(config=config) 