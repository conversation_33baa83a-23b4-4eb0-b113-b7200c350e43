#!/usr/bin/env python3
"""
增强版用户感知模块 - Enhanced User Perception Module

该模块负责感知和分析用户相关信息，包括用户基本信息、偏好、行为模式等。
基于原有的UserPerception模块，但使用了新的认知模块架构，增强了功能和性能。

作者: Claude
创建日期: 2024-07-15
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
import threading
from typing import Dict, Any, List, Optional, Tuple, Set

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.perception.user_perception import UserPerception

# 配置日志记录
setup_unified_logging()

# 尝试导入事件总线
try:
    from core.enhanced_event_bus import get_instance as get_event_bus
except ImportError:
    try:
        from core.event_bus import get_instance as get_event_bus
    except ImportError:
        logger = get_unified_logger(__name__)
        logger.warning_status("无法导入事件总线，将使用模拟版本")
        
        class DummyEventBus:
            def publish(self, *args, **kwargs): pass
            def subscribe(self, *args, **kwargs): pass
            
        def get_event_bus():
            return DummyEventBus()

class EnhancedUserPerception(CognitiveModuleBase):
    """
    增强版用户感知模块
    
    继承自认知模块基类，负责感知和分析用户相关信息，包括：
    - 用户基本信息（ID、名称、年龄、性别等）
    - 用户偏好（兴趣、习惯、喜好等）
    - 用户行为模式（活跃时间、交互频率等）
    - 用户情绪状态（当前情绪、情绪变化等）
    
    增强特性：
    - 事件驱动的用户状态更新
    - 用户行为模式分析
    - 用户兴趣主题自动发现
    - 缓存优化策略
    """
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls, config: Dict[str, Any] = None) -> 'EnhancedUserPerception':
        """
        获取模块实例（单例模式）
        
        参数:
            config: 配置信息
            
        返回:
            EnhancedUserPerception: 模块实例
        """
        with cls._lock:
            if cls._instance is None:
                cls._instance = EnhancedUserPerception("enhanced_user_perception", "perception", config)
                
        return cls._instance
    
    def __init__(self, module_id: str, module_type: str, config: Dict[str, Any] = None):
        """
        初始化增强版用户感知模块
        
        参数:
            module_id: 模块ID
            module_type: 模块类型
            config: 配置信息
        """
        # 调用父类初始化
        super().__init__(module_id, module_type, config)
        
        # 设置模块描述
        self.description = "增强版用户感知模块 - 负责感知和分析用户信息，包括基本信息、偏好、行为模式和情绪状态"
        
        # 获取事件总线
        self.event_bus = get_event_bus()
        
        # 初始化内部组件
        self.legacy_perception = None
        
        # 用户数据缓存
        self.users_cache = {}
        self.users_cache_time = {}
        self.users_cache_expiry = self.config.get('users_cache_expiry', 300)  # 默认5分钟
        
        # 活跃用户跟踪
        self.active_users = set()
        self.active_time = {}
        
        # 用户数据存储路径
        self.users_dir = self.config.get('users_dir', os.path.join('data', 'users'))
        
        # 行为分析数据
        self.behavior_patterns = {}
        
        # 标记模块已创建
        self.logger.info("增强版用户感知模块已创建")
    
    def _do_initialize(self) -> None:
        """
        执行初始化逻辑
        """
        try:
            # 初始化原有的用户感知模块
            self.legacy_perception = UserPerception(self.config)
            
            # 确保用户目录存在
            if not os.path.exists(self.users_dir):
                os.makedirs(self.users_dir)
                
            # 加载活跃用户
            self._load_active_users()
            
            # 加载行为模式分析数据
            self._load_behavior_patterns()
            
            # 订阅相关事件
            self._subscribe_events()
            
            # 发布模块初始化事件
            self.event_bus.publish(
                "perception.user.initialized",
                {
                    "module_id": self.module_id,
                    "time": time.time()
                },
                source=self.module_id
            )
            
            self.logger.success("增强版用户感知模块初始化完成")
        except Exception as e:
            self.logger.error_status(f"增强版用户感知模块初始化失败: {str(e)}")
            raise
    
    def _subscribe_events(self) -> None:
        """
        订阅相关事件
        """
        # 用户消息事件
        self.event_bus.subscribe("user.message.sent", self._handle_user_message)
        
        # 用户活动事件
        self.event_bus.subscribe("user.login", self._handle_user_login)
        self.event_bus.subscribe("user.logout", self._handle_user_logout)
        
        # 用户情绪变化事件
        self.event_bus.subscribe("user.emotion.changed", self._handle_emotion_change)
        
        # 系统关闭事件
        self.event_bus.subscribe("system.shutdown", self._handle_system_shutdown)
    
    def _handle_user_message(self, event_data: Dict[str, Any]) -> None:
        """
        处理用户消息事件
        
        参数:
            event_data: 事件数据
        """
        if "user_id" in event_data and "content" in event_data:
            user_id = event_data["user_id"]
            content = event_data["content"]
            content_length = len(content) if isinstance(content, str) else 0
            
            # 记录交互
            self.record_interaction(user_id, "message", content_length)
            
            # 更新活跃用户
            self._update_active_user(user_id)
            
            # 分析消息内容（简单实现，实际应使用更复杂的NLP）
            if content_length > 0 and isinstance(content, str):
                self._analyze_message_content(user_id, content)
    
    def _handle_user_login(self, event_data: Dict[str, Any]) -> None:
        """
        处理用户登录事件
        
        参数:
            event_data: 事件数据
        """
        if "user_id" in event_data:
            user_id = event_data["user_id"]
            
            # 更新用户活跃状态
            self._update_active_user(user_id)
            
            # 记录登录活动
            self.record_interaction(user_id, "login", 0)
    
    def _handle_user_logout(self, event_data: Dict[str, Any]) -> None:
        """
        处理用户登出事件
        
        参数:
            event_data: 事件数据
        """
        if "user_id" in event_data:
            user_id = event_data["user_id"]
            
            # 从活跃用户集合中移除
            if user_id in self.active_users:
                self.active_users.remove(user_id)
            
            # 记录登出活动
            self.record_interaction(user_id, "logout", 0)
    
    def _handle_emotion_change(self, event_data: Dict[str, Any]) -> None:
        """
        处理用户情绪变化事件
        
        参数:
            event_data: 事件数据
        """
        if "user_id" in event_data and "emotion" in event_data:
            user_id = event_data["user_id"]
            emotion = event_data["emotion"]
            
            # 更新用户情绪状态
            user_data = self.get_user_info(user_id)
            
            # 记录当前情绪
            user_data["emotion"]["current"] = emotion
            
            # 添加到情绪历史
            emotion_history = user_data["emotion"].get("history", [])
            emotion_history.append({
                "emotion": emotion,
                "time": datetime.datetime.now().isoformat()
            })
            
            # 限制历史记录长度
            max_history = self.config.get("max_emotion_history", 100)
            if len(emotion_history) > max_history:
                emotion_history = emotion_history[-max_history:]
                
            user_data["emotion"]["history"] = emotion_history
            
            # 保存用户数据
            self._save_user_data(user_id, user_data)
    
    def _handle_system_shutdown(self, event_data: Dict[str, Any]) -> None:
        """
        处理系统关闭事件
        
        参数:
            event_data: 事件数据
        """
        # 保存所有缓存的用户数据
        for user_id in list(self.users_cache.keys()):
            if user_id in self.users_cache:
                self._save_user_data(user_id, self.users_cache[user_id])
        
        # 保存行为模式分析数据
        self._save_behavior_patterns()
    
    def _do_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        根据输入的操作类型执行不同的处理。
        
        参数:
            input_data: 输入数据，包含操作类型和相关参数
            
        返回:
            Dict[str, Any]: 处理结果
        """
        operation = input_data.get("operation", "")
        
        if operation == "get_user_info":
            if "user_id" in input_data:
                return {
                    "result": self.get_user_info(input_data["user_id"])
                }
        
        elif operation == "add_user":
            if "user_id" in input_data and "name" in input_data:
                basic_info = input_data.get("basic_info", {})
                success = self.add_user(input_data["user_id"], input_data["name"], basic_info)
                return {
                    "success": success
                }
        
        elif operation == "update_user_info":
            if "user_id" in input_data and "info" in input_data:
                success = self.update_user_info(input_data["user_id"], input_data["info"])
                return {
                    "success": success
                }
        
        elif operation == "get_active_users":
            minutes = input_data.get("minutes", 60)
            return {
                "result": self.get_active_users(minutes)
            }
        
        elif operation == "analyze_user":
            if "user_id" in input_data:
                return {
                    "result": self.analyze_user(input_data["user_id"])
                }
        
        elif operation == "search_users":
            criteria = input_data.get("criteria", {})
            return {
                "result": self.search_users(criteria)
            }
        
        return {
            "error": "unknown_operation",
            "message": f"未知的操作: {operation}"
        }
    
    def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户信息
        
        参数:
            user_id: 用户ID
            
        返回:
            Dict[str, Any]: 用户信息
        """
        # 使用legacy模块获取基本信息
        return self.legacy_perception.get_user_info(user_id)
    
    def add_user(self, user_id: str, name: str, basic_info: Dict[str, Any] = None) -> bool:
        """
        添加用户
        
        参数:
            user_id: 用户ID
            name: 用户名称
            basic_info: 基本信息
            
        返回:
            bool: 是否成功
        """
        # 使用legacy模块添加用户
        success = self.legacy_perception.add_user(user_id, name, basic_info)
        
        if success:
            # 发布用户添加事件
            self.event_bus.publish(
                "perception.user.added",
                {
                    "user_id": user_id,
                    "name": name,
                    "time": time.time()
                },
                source=self.module_id
            )
        
        return success
    
    def update_user_info(self, user_id: str, info: Dict[str, Any]) -> bool:
        """
        更新用户信息
        
        参数:
            user_id: 用户ID
            info: 更新的信息
            
        返回:
            bool: 是否成功
        """
        # 使用legacy模块更新用户信息
        return self.legacy_perception.update_user_info(user_id, info)
    
    def record_interaction(self, user_id: str, interaction_type: str = "text", content_length: int = 0) -> bool:
        """
        记录用户交互
        
        参数:
            user_id: 用户ID
            interaction_type: 交互类型
            content_length: 内容长度
            
        返回:
            bool: 是否成功
        """
        # 使用legacy模块记录交互
        return self.legacy_perception.record_interaction(user_id, interaction_type, content_length)
    
    def get_active_users(self, minutes: int = 60) -> List[str]:
        """
        获取活跃用户
        
        参数:
            minutes: 时间窗口（分钟）
            
        返回:
            List[str]: 活跃用户ID列表
        """
        # 首先检查内部活跃用户集合
        current_time = time.time()
        active_users = []
        
        for user_id in self.active_users:
            last_active = self.active_time.get(user_id, 0)
            if current_time - last_active <= minutes * 60:
                active_users.append(user_id)
        
        # 如果内部集合为空，使用legacy模块获取
        if not active_users:
            return self.legacy_perception.get_active_users(minutes)
        
        return active_users
    
    def analyze_user(self, user_id: str) -> Dict[str, Any]:
        """
        分析用户
        
        提供更全面的用户分析，包括活动模式、情绪趋势、兴趣主题等。
        
        参数:
            user_id: 用户ID
            
        返回:
            Dict[str, Any]: 分析结果
        """
        # 获取用户信息
        user_info = self.get_user_info(user_id)
        
        # 获取行为模式
        behavior = self.behavior_patterns.get(user_id, {})
        
        # 分析情绪趋势
        emotion_trend = self._analyze_emotion_trend(user_info)
        
        # 获取活动分析
        activity_analysis = self.legacy_perception.analyze_user_activity(user_id)
        
        # 整合分析结果
        analysis = {
            "user_id": user_id,
            "name": user_info.get("name", "未知用户"),
            "activity_pattern": activity_analysis,
            "emotion_trend": emotion_trend,
            "behavior_pattern": behavior,
            "interests": self._extract_interests(user_info),
            "engagement_level": self._calculate_engagement(user_info)
        }
        
        return analysis
    
    def search_users(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        搜索用户
        
        参数:
            criteria: 搜索条件
            
        返回:
            List[Dict[str, Any]]: 匹配的用户列表
        """
        # 使用legacy模块搜索用户
        return self.legacy_perception.search_users(criteria)
    
    def _update_active_user(self, user_id: str) -> None:
        """
        更新活跃用户
        
        参数:
            user_id: 用户ID
        """
        self.active_users.add(user_id)
        self.active_time[user_id] = time.time()
    
    def _load_active_users(self) -> None:
        """
        加载活跃用户
        """
        # 实际实现可能会从数据库或缓存中加载
        pass
    
    def _load_behavior_patterns(self) -> None:
        """
        加载行为模式分析数据
        """
        behavior_file = os.path.join(self.users_dir, "_behavior_patterns.json")
        
        if os.path.exists(behavior_file):
            try:
                with open(behavior_file, 'r', encoding='utf-8') as f:
                    self.behavior_patterns = json.load(f)
            except Exception as e:
                self.logger.error_status(f"加载行为模式数据失败: {str(e)}")
                self.behavior_patterns = {}
    
    def _save_behavior_patterns(self) -> None:
        """
        保存行为模式分析数据
        """
        behavior_file = os.path.join(self.users_dir, "_behavior_patterns.json")
        
        try:
            with open(behavior_file, 'w', encoding='utf-8') as f:
                json.dump(self.behavior_patterns, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error_status(f"保存行为模式数据失败: {str(e)}")
    
    def _analyze_message_content(self, user_id: str, content: str) -> None:
        """
        分析消息内容
        
        参数:
            user_id: 用户ID
            content: 消息内容
        """
        # 这里只是简单实现，实际应使用NLP技术进行更深入分析
        if not content:
            return
            
        # 获取用户信息
        user_info = self.get_user_info(user_id)
        
        # 更新消息统计
        stats = user_info.get("statistics", {})
        stats["total_messages"] = stats.get("total_messages", 0) + 1
        
        # 更新平均消息长度
        total_msgs = stats.get("total_messages", 1)
        avg_length = stats.get("average_message_length", 0)
        new_avg = ((avg_length * (total_msgs - 1)) + len(content)) / total_msgs
        stats["average_message_length"] = new_avg
        
        # 简单的主题分析（实际应使用更复杂的主题模型）
        topics = stats.get("topics_of_interest", {})
        
        # 简单的关键词匹配
        keywords = [
            "科技", "技术", "电脑", "编程", 
            "艺术", "音乐", "电影", "书籍",
            "运动", "健身", "旅行", "美食",
            "金融", "投资", "经济", "工作"
        ]
        
        for keyword in keywords:
            if keyword in content:
                topics[keyword] = topics.get(keyword, 0) + 1
        
        stats["topics_of_interest"] = topics
        user_info["statistics"] = stats
        
        # 保存用户数据
        self._save_user_data(user_id, user_info)
    
    def _analyze_emotion_trend(self, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析情绪趋势
        
        参数:
            user_info: 用户信息
            
        返回:
            Dict[str, Any]: 情绪趋势分析
        """
        emotion_data = user_info.get("emotion", {})
        emotion_history = emotion_data.get("history", [])
        
        if not emotion_history:
            return {
                "trend": "stable",
                "predominant_emotion": "neutral",
                "variability": "low"
            }
        
        # 统计情绪出现次数
        emotion_counts = {}
        for entry in emotion_history:
            emotion = entry.get("emotion", "neutral")
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        # 找出最常见的情绪
        predominant_emotion = max(emotion_counts.items(), key=lambda x: x[1])[0]
        
        # 计算情绪多样性
        variability = "low"
        if len(emotion_counts) > 3:
            variability = "high"
        elif len(emotion_counts) > 1:
            variability = "medium"
        
        # 分析趋势（简化版）
        trend = "stable"
        if len(emotion_history) > 5:
            recent_emotions = [e.get("emotion") for e in emotion_history[-5:]]
            positive_emotions = ["happy", "excited", "satisfied", "relaxed"]
            negative_emotions = ["sad", "angry", "frustrated", "anxious"]
            
            positive_count = sum(1 for e in recent_emotions if e in positive_emotions)
            negative_count = sum(1 for e in recent_emotions if e in negative_emotions)
            
            if positive_count > 3:
                trend = "positive"
            elif negative_count > 3:
                trend = "negative"
        
        return {
            "trend": trend,
            "predominant_emotion": predominant_emotion,
            "variability": variability,
            "emotion_distribution": emotion_counts
        }
    
    def _extract_interests(self, user_info: Dict[str, Any]) -> Dict[str, float]:
        """
        提取用户兴趣
        
        参数:
            user_info: 用户信息
            
        返回:
            Dict[str, float]: 兴趣及其强度
        """
        # 从统计数据中提取主题
        stats = user_info.get("statistics", {})
        topics = stats.get("topics_of_interest", {})
        
        # 计算兴趣强度
        interests = {}
        total_mentions = sum(topics.values()) if topics else 1
        
        for topic, count in topics.items():
            interests[topic] = count / total_mentions
        
        return interests
    
    def _calculate_engagement(self, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算用户参与度
        
        参数:
            user_info: 用户信息
            
        返回:
            Dict[str, Any]: 参与度指标
        """
        activity = user_info.get("activity", {})
        stats = user_info.get("statistics", {})
        
        total_interactions = activity.get("total_interactions", 0)
        avg_daily = activity.get("average_daily_interactions", 0)
        active_days = activity.get("active_days", 0)
        
        # 计算参与度分数（简化版）
        engagement_score = 0
        if active_days > 0:
            engagement_score = (total_interactions / (active_days + 1)) / 10
            engagement_score = min(10, engagement_score)  # 最高10分
        
        # 参与度级别
        level = "低"
        if engagement_score > 7:
            level = "高"
        elif engagement_score > 3:
            level = "中"
        
        return {
            "score": engagement_score,
            "level": level,
            "daily_average": avg_daily,
            "active_days": active_days,
            "total_interactions": total_interactions
        }
    
    def _save_user_data(self, user_id: str, user_data: Dict[str, Any]) -> bool:
        """
        保存用户数据
        
        参数:
            user_id: 用户ID
            user_data: 用户数据
            
        返回:
            bool: 是否成功
        """
        # 更新缓存
        self.users_cache[user_id] = user_data
        self.users_cache_time[user_id] = time.time()
        
        # 保存到文件
        user_file = os.path.join(self.users_dir, f"{user_id}.json")
        
        try:
            with open(user_file, 'w', encoding='utf-8') as f:
                json.dump(user_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error_status(f"保存用户数据失败: {str(e)}")
            return False
    
    def _do_shutdown(self) -> None:
        """
        执行关闭逻辑
        """
        # 保存所有缓存的用户数据
        for user_id in list(self.users_cache.keys()):
            if user_id in self.users_cache:
                self._save_user_data(user_id, self.users_cache[user_id])
        
        # 保存行为模式分析数据
        self._save_behavior_patterns()
        
        # 发布模块关闭事件
        self.event_bus.publish(
            "perception.user.shutdown",
            {
                "module_id": self.module_id,
                "time": time.time()
            },
            source=self.module_id
        )
        
        self.logger.info("增强版用户感知模块已关闭")

# 模块单例获取函数
def get_instance(config: Dict[str, Any] = None) -> EnhancedUserPerception:
    """
    获取模块实例
    
    参数:
        config: 配置信息
        
    返回:
        EnhancedUserPerception: 模块实例
    """
    return EnhancedUserPerception.get_instance(config) 