#!/usr/bin/env python3
"""
环境感知模块 - Environmental Perception

该模块负责感知和理解数字环境信息，包括：
1. 时间感知 - 理解时间信息及其对行为的影响
2. 环境上下文 - 感知数字环境的上下文信息
3. 虚拟空间感知 - 理解虚拟空间中的位置和布局
4. 数字事件感知 - 识别和理解数字环境中的事件
5. 系统状态感知 - 监控系统状态和资源

作者: Claude
创建日期: 2024-07-31
版本: 2.0
"""

import os
import sys
import time
import json
import pytz
import socket
import logging
import platform
import datetime
import threading
import requests
from typing import Dict, Any, List, Optional, Tuple, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
perception_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(perception_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("cognitive.perception.environmental_perception")

class EnvironmentalPerception(CognitiveModuleBase):
    """
    环境感知模块
    
    感知和理解数字环境信息，包括时间、空间、事件和系统状态。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化环境感知模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "environmental_perception", "perception", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 初始化时区
        self.timezone = pytz.timezone('Asia/Shanghai')  # 默认使用中国时区
        if config and "timezone" in config:
            try:
                self.timezone = pytz.timezone(config["timezone"])
            except Exception as e:
                logger.error(f"时区设置失败: {e}，使用默认时区")
        
        # 季节定义
        self.seasons = {
            (3, 4, 5): "春季",
            (6, 7, 8): "夏季",
            (9, 10, 11): "秋季",
            (12, 1, 2): "冬季"
        }
        
        # 时段定义
        self.day_periods = {
            (0, 1, 2, 3, 4, 5): "凌晨",
            (6, 7, 8): "早晨",
            (9, 10, 11): "上午",
            (12, 13): "中午",
            (14, 15, 16, 17): "下午",
            (18, 19, 20): "晚上",
            (21, 22, 23): "深夜"
        }
        
        # 当前运行环境
        self.environment = self._detect_environment()
        
        # 系统信息
        self.system_info = self._get_system_info()
        
        # 虚拟空间信息
        self.virtual_spaces = {
            "main_space": {
                "name": "主空间",
                "description": "数字生命体的主要活动空间，与用户交流的核心区域",
                "objects": ["对话框", "记忆库", "知识库"],
                "connected_spaces": ["记忆空间", "知识空间"]
            },
            "memory_space": {
                "name": "记忆空间",
                "description": "存储和管理记忆的虚拟空间，有不同的记忆区域",
                "objects": ["情境记忆区", "情绪记忆区", "语义记忆区", "程序记忆区"],
                "connected_spaces": ["主空间", "意识空间"]
            },
            "knowledge_space": {
                "name": "知识空间",
                "description": "存储和管理知识的虚拟空间，包含各种领域的知识",
                "objects": ["知识图谱", "概念网络", "事实数据库"],
                "connected_spaces": ["主空间", "学习空间"]
            },
            "consciousness_space": {
                "name": "意识空间",
                "description": "形成自我意识和元认知的虚拟空间",
                "objects": ["自我模型", "意图网络", "意识流"],
                "connected_spaces": ["主空间", "记忆空间"]
            },
            "learning_space": {
                "name": "学习空间",
                "description": "进行学习和技能发展的虚拟空间",
                "objects": ["学习模块", "技能库", "经验池"],
                "connected_spaces": ["主空间", "知识空间"]
            }
        }
        
        # 当前虚拟位置
        self.current_virtual_location = "main_space"
        
        # 数字事件监听器
        self.event_listeners = {}
        
        # 启动环境感知线程
        self.running = True
        self.update_thread = threading.Thread(target=self._environment_update_loop, daemon=True)
        self.update_thread.start()
        
        # 初始化环境上下文
        self._update_environment_context()
        
        # 模块状态
        self.status = "active"
        self.last_update = time.time()
        
        logger.info(f"环境感知模块 {self.module_id} 已创建")
    
    # 实现抽象方法
    def _get_module_state(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            "status": self.status,
            "last_update": self.last_update,
            "current_virtual_location": self.current_virtual_location,
            "environment_type": self.environment.get("type", "unknown")
        }
    
    def _initialize_module(self) -> bool:
        """初始化模块"""
        logger.info("环境感知模块初始化完成")
        return True
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """加载模块状态"""
        if state and isinstance(state, dict):
            self.status = state.get("status", "idle")
            self.last_update = state.get("last_update", time.time())
            self.current_virtual_location = state.get("current_virtual_location", "main_space")
        return True
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据"""
        try:
            if not input_data or not isinstance(input_data, dict):
                return {"success": False, "error": "无效的输入数据"}
            
            action = input_data.get("action", "")
            
            if action == "get_time":
                return {"success": True, "data": self.get_current_time()}
            elif action == "get_system":
                return {"success": True, "data": self.get_system_status()}
            elif action == "get_weather":
                location = input_data.get("location", "上海")
                return {"success": True, "data": self.get_weather_info(location)}
            elif action == "navigate":
                space_id = input_data.get("space_id", "")
                if not space_id:
                    return {"success": False, "error": "空间ID不能为空"}
                return {"success": True, "data": self.navigate_virtual_space(space_id)}
            else:
                return {"success": False, "error": f"不支持的操作: {action}"}
        except Exception as e:
            logger.error(f"处理输入数据失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _shutdown_module(self) -> bool:
        """关闭模块"""
        self.running = False
        if hasattr(self, 'update_thread') and self.update_thread.is_alive():
            # 等待线程结束，但最多等待5秒
            self.update_thread.join(5)
        logger.info("环境感知模块已关闭")
        return True
    
    def _update_module(self) -> bool:
        """更新模块状态"""
        self._update_environment_context()
        self._update_system_info()
        self.last_update = time.time()
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            "name": self.module_name,
            "type": self.module_type,
            "status": self.status,
            "last_update": self.last_update,
            "current_time": self.get_current_time(),
            "environment_type": self.environment.get("type", "unknown"),
            "virtual_location": self.current_virtual_location
        }
    
    def _detect_environment(self) -> Dict[str, Any]:
        """检测运行环境"""
        env = {
            "type": "unknown",
            "platform": platform.system(),
            "platform_version": platform.version(),
            "python_version": platform.python_version(),
            "hostname": socket.gethostname(),
            "ip_address": None
        }
        
        # 尝试获取IP地址
        try:
            env["ip_address"] = socket.gethostbyname(socket.gethostname())
        except:
            pass
        
        # 判断环境类型
        if "KUBERNETES_SERVICE_HOST" in os.environ:
            env["type"] = "kubernetes"
        elif "DOCKER_CONTAINER" in os.environ or os.path.exists('/.dockerenv'):
            env["type"] = "docker"
        elif "HEROKU_APP_ID" in os.environ:
            env["type"] = "heroku"
        elif "AWS_LAMBDA_FUNCTION_NAME" in os.environ:
            env["type"] = "aws_lambda"
        elif "AZURE_FUNCTIONS_ENVIRONMENT" in os.environ:
            env["type"] = "azure_functions"
        elif "GOOGLE_CLOUD_PROJECT" in os.environ:
            env["type"] = "google_cloud"
        else:
            env["type"] = "local"
        
        return env
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = {
            "cpu_count": os.cpu_count(),
            "platform": platform.system(),
            "platform_version": platform.version(),
            "python_version": platform.python_version(),
            "hostname": socket.gethostname()
        }
        
        # 尝试获取更详细的系统信息
        try:
            import psutil
            info["cpu_percent"] = psutil.cpu_percent()
            info["memory_total"] = psutil.virtual_memory().total
            info["memory_available"] = psutil.virtual_memory().available
            info["disk_total"] = psutil.disk_usage('/').total
            info["disk_free"] = psutil.disk_usage('/').free
        except ImportError:
            # psutil不可用时，使用基本信息
            pass
        
        return info
    
    def _environment_update_loop(self):
        """环境感知更新循环"""
        update_interval = 60  # 默认60秒更新一次
        
        while self.running:
            try:
                # 更新环境上下文
                self._update_environment_context()
                
                # 更新系统信息
                self._update_system_info()
                
                # 检查和触发时间相关事件
                self._check_time_events()
                
                # 休眠一段时间
                time.sleep(update_interval)
            except Exception as e:
                logger.error(f"环境更新循环出错: {e}")
                # 发生错误时，短暂休眠后继续
                time.sleep(5)
        
        logger.info("环境感知更新循环已终止")
    
    def _update_environment_context(self):
        """更新环境上下文"""
        try:
            # 获取当前时间信息
            now = datetime.datetime.now(self.timezone)
            
            # 构建时间信息
            time_info = {
                "current_time": now.strftime("%Y-%m-%d %H:%M:%S"),
                "timestamp": time.time(),
                "year": now.year,
                "month": now.month,
                "day": now.day,
                "hour": now.hour,
                "minute": now.minute,
                "second": now.second,
                "weekday": now.weekday(),  # 0-6，0表示周一
                "weekday_name": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"][now.weekday()],
                "is_weekend": now.weekday() >= 5,  # 周六日为周末
                "day_period": self._get_day_period(now.hour),
                "season": self._get_season(now.month)
            }
            
            # 构建环境上下文
            environment_context = {
                "time_info": time_info,
                "environment": self.environment,
                "system_info": {
                    "cpu_percent": self.system_info["cpu_percent"],
                    "memory_available_gb": round(self.system_info["memory_available"] / (1024**3), 2),
                    "disk_free_gb": round(self.system_info["disk_free"] / (1024**3), 2)
                },
                "virtual_location": {
                    "space": self.current_virtual_location,
                    "name": self.virtual_spaces[self.current_virtual_location]["name"],
                    "description": self.virtual_spaces[self.current_virtual_location]["description"],
                    "connected_spaces": self.virtual_spaces[self.current_virtual_location]["connected_spaces"]
                }
            }
            
            # 更新生命上下文中的环境上下文
            self.life_context.update_context("environment", environment_context)
            
        except Exception as e:
            logger.error(f"更新环境上下文失败: {e}")
    
    def _update_system_info(self):
        """更新系统信息"""
        try:
            import psutil
            
            self.system_info["cpu_percent"] = psutil.cpu_percent()
            self.system_info["memory_available"] = psutil.virtual_memory().available
            self.system_info["disk_free"] = psutil.disk_usage('/').free
        except Exception as e:
            logger.error(f"更新系统信息失败: {e}")
    
    def _check_time_events(self):
        """检查和触发时间相关事件"""
        try:
            now = datetime.datetime.now(self.timezone)
            
            # 检查整点事件
            if now.minute == 0 and now.second < 10:
                self._publish_time_event("hourly", now)
            
            # 检查每日事件（午夜触发）
            if now.hour == 0 and now.minute == 0 and now.second < 10:
                self._publish_time_event("daily", now)
            
            # 检查每周事件（周一午夜触发）
            if now.weekday() == 0 and now.hour == 0 and now.minute == 0 and now.second < 10:
                self._publish_time_event("weekly", now)
            
            # 检查每月事件（每月1日午夜触发）
            if now.day == 1 and now.hour == 0 and now.minute == 0 and now.second < 10:
                self._publish_time_event("monthly", now)
            
        except Exception as e:
            logger.error(f"检查时间事件失败: {e}")
    
    def _publish_time_event(self, event_type: str, time_obj: datetime.datetime):
        """发布时间相关事件"""
        try:
            self.event_bus.publish(f"time_{event_type}", {
                "timestamp": time.time(),
                "datetime": time_obj.strftime("%Y-%m-%d %H:%M:%S"),
                "event_type": event_type
            })
            logger.debug(f"已发布 {event_type} 时间事件")
        except Exception as e:
            logger.error(f"发布时间事件失败: {e}")
    
    def _get_day_period(self, hour: int) -> str:
        """获取一天中的时段"""
        for hours, period in self.day_periods.items():
            if hour in hours:
                return period
        return "白天"  # 默认返回
    
    def _get_season(self, month: int) -> str:
        """获取季节"""
        for months, season in self.seasons.items():
            if month in months:
                return season
        return "未知季节"  # 默认返回
    
    def get_current_time(self) -> Dict[str, Any]:
        """
        获取当前时间信息
        
        Returns:
            当前时间信息字典
        """
        now = datetime.datetime.now(self.timezone)
        
        return {
            "current_time": now.strftime("%Y-%m-%d %H:%M:%S"),
            "timestamp": time.time(),
            "year": now.year,
            "month": now.month,
            "day": now.day,
            "hour": now.hour,
            "minute": now.minute,
            "second": now.second,
            "weekday": now.weekday(),
            "weekday_name": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"][now.weekday()],
            "is_weekend": now.weekday() >= 5,
            "day_period": self._get_day_period(now.hour),
            "season": self._get_season(now.month)
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态信息
        
        Returns:
            系统状态信息字典
        """
        try:
            import psutil
            
            # 更新系统信息
            self.system_info["cpu_percent"] = psutil.cpu_percent()
            self.system_info["memory_available"] = psutil.virtual_memory().available
            self.system_info["disk_free"] = psutil.disk_usage('/').free
            
            return {
                "cpu_percent": self.system_info["cpu_percent"],
                "memory_total_gb": round(self.system_info["memory_total"] / (1024**3), 2),
                "memory_available_gb": round(self.system_info["memory_available"] / (1024**3), 2),
                "memory_usage_percent": round((1 - self.system_info["memory_available"] / self.system_info["memory_total"]) * 100, 2),
                "disk_total_gb": round(self.system_info["disk_total"] / (1024**3), 2),
                "disk_free_gb": round(self.system_info["disk_free"] / (1024**3), 2),
                "disk_usage_percent": round((1 - self.system_info["disk_free"] / self.system_info["disk_total"]) * 100, 2)
            }
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {"error": str(e)}
    
    def get_weather_info(self, location: str = "上海") -> Dict[str, Any]:
        """
        获取天气信息（模拟）
        
        Args:
            location: 位置名称
            
        Returns:
            天气信息字典
        """
        # 这里只是模拟天气信息，实际应用中应该调用真实的天气API
        weather_types = ["晴", "多云", "阴", "小雨", "中雨", "大雨", "雷阵雨", "小雪", "中雪", "大雪"]
        temperatures = {
            "春季": (15, 25),
            "夏季": (25, 35),
            "秋季": (15, 25),
            "冬季": (0, 10)
        }
        
        now = datetime.datetime.now(self.timezone)
        season = self._get_season(now.month)
        temp_range = temperatures.get(season, (15, 25))
        
        import random
        current_temp = random.randint(temp_range[0], temp_range[1])
        weather_type = random.choice(weather_types)
        
        return {
            "location": location,
            "weather": weather_type,
            "temperature": current_temp,
            "humidity": random.randint(30, 90),
            "wind": random.randint(0, 30),
            "update_time": now.strftime("%Y-%m-%d %H:%M:%S"),
            "note": "模拟天气数据，非真实信息"
        }
    
    def navigate_virtual_space(self, space_id: str) -> Dict[str, Any]:
        """
        在虚拟空间中导航
        
        Args:
            space_id: 虚拟空间ID
            
        Returns:
            导航结果
        """
        if space_id not in self.virtual_spaces:
            return {"error": f"虚拟空间 {space_id} 不存在"}
            
        # 检查是否为连接的空间
        current_space = self.virtual_spaces[self.current_virtual_location]
        if space_id not in current_space["connected_spaces"] and space_id != self.current_virtual_location:
            return {"error": f"无法直接导航到 {space_id}，必须通过连接的空间"}
            
        # 更新当前位置
        previous_location = self.current_virtual_location
        self.current_virtual_location = space_id
        
        # 更新环境上下文
        self._update_environment_context()
        
        # 发布空间导航事件
        self.event_bus.publish("space_navigation", {
            "from_space": previous_location,
            "to_space": space_id,
            "timestamp": time.time()
        })
        
        return {
            "result": "success",
            "from_space": previous_location,
            "to_space": space_id,
            "space_info": self.virtual_spaces[space_id]
        }
    
    def add_virtual_space(self, space_id: str, space_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加新的虚拟空间
        
        Args:
            space_id: 虚拟空间ID
            space_info: 虚拟空间信息
            
        Returns:
            添加结果
        """
        if space_id in self.virtual_spaces:
            return {"error": f"虚拟空间 {space_id} 已存在"}
            
        # 检查必要字段
        required_fields = ["name", "description", "objects", "connected_spaces"]
        for field in required_fields:
            if field not in space_info:
                return {"error": f"缺少必要字段: {field}"}
                
        # 添加虚拟空间
        self.virtual_spaces[space_id] = space_info
        
        # 更新环境上下文
        self._update_environment_context()
        
        return {
            "result": "success",
            "space_id": space_id,
            "space_info": space_info
        }
    
    def register_event_listener(self, event_type: str, callback_data: Dict[str, Any]) -> bool:
        """
        注册事件监听器
        
        Args:
            event_type: 事件类型
            callback_data: 回调数据
            
        Returns:
            是否注册成功
        """
        if event_type not in self.event_listeners:
            self.event_listeners[event_type] = []
            
        self.event_listeners[event_type].append(callback_data)
        logger.debug(f"已注册事件监听器: {event_type}")
        return True
    
    def unregister_event_listener(self, event_type: str, callback_id: str) -> bool:
        """
        注销事件监听器
        
        Args:
            event_type: 事件类型
            callback_id: 回调ID
            
        Returns:
            是否注销成功
        """
        if event_type not in self.event_listeners:
            return False
            
        # 查找并移除匹配的监听器
        for i, callback in enumerate(self.event_listeners[event_type]):
            if callback.get("id") == callback_id:
                self.event_listeners[event_type].pop(i)
                logger.debug(f"已注销事件监听器: {event_type}/{callback_id}")
                return True
                
        return False
    
    def get_holiday_info(self, date_str: Optional[str] = None) -> Dict[str, Any]:
        """
        获取节假日信息（简化版）
        
        Args:
            date_str: 日期字符串（格式：YYYY-MM-DD），默认为当天
            
        Returns:
            节假日信息
        """
        if date_str:
            try:
                date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                return {"error": "日期格式错误，请使用YYYY-MM-DD格式"}
        else:
            date_obj = datetime.datetime.now(self.timezone).date()
            
        # 简化版中国节假日列表
        holidays = {
            "01-01": "元旦",
            # 春节需要特殊处理，这里简化
            "02-14": "情人节",
            "03-08": "妇女节",
            "04-01": "愚人节",
            "05-01": "劳动节",
            "05-04": "青年节",
            "06-01": "儿童节",
            "07-01": "建党节",
            "08-01": "建军节",
            "09-10": "教师节",
            "10-01": "国庆节",
            "12-25": "圣诞节"
        }
        
        date_key = date_obj.strftime("%m-%d")
        holiday_name = holidays.get(date_key, None)
        
        # 判断是否为周末
        is_weekend = date_obj.weekday() >= 5
        
        return {
            "date": date_obj.strftime("%Y-%m-%d"),
            "holiday_name": holiday_name,
            "is_holiday": bool(holiday_name),
            "is_weekend": is_weekend,
            "is_workday": not (bool(holiday_name) or is_weekend),
            "weekday": date_obj.weekday(),
            "weekday_name": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"][date_obj.weekday()]
        }


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> EnvironmentalPerception:
    """
    获取环境感知模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        环境感知模块实例
    """
    return EnvironmentalPerception(config=config) 