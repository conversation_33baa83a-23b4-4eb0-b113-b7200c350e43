#!/usr/bin/env python3
"""
用户模型模块 - User Model Module

该模块负责构建和维护用户模型，包括：
1. 用户特征提取 - 从交互中提取用户特征
2. 用户偏好分析 - 分析用户的偏好和兴趣
3. 用户行为模式 - 识别用户的行为模式
4. 用户情感状态 - 跟踪用户的情感变化
5. 用户知识图谱 - 构建用户相关的知识图谱

作者: Claude
创建日期: 2024-07-31
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
import threading
from typing import Dict, Any, List, Optional, Tuple, Set, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
perception_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(perception_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.perception.user_model")

class UserModel(CognitiveModuleBase):
    """
    用户模型模块
    
    构建和维护用户模型，分析用户特征、偏好和行为模式。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化用户模型模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "user_model", "perception", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 用户模型数据
        self.user_models = {}  # 用户模型映射: {user_id: user_model}
        
        # 用户模型更新锁
        self.model_locks = {}  # 用户模型更新锁: {user_id: lock}
        
        # 模型更新时间戳
        self.last_update_time = {}  # 最后更新时间: {user_id: timestamp}
        
        # 加载用户模型数据
        self._load_user_models()
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"用户模型模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("user_message", self._on_user_message)
        self.event_bus.subscribe("intention_analyzed", self._on_intention_analyzed)
        self.event_bus.subscribe("emotion_detected", self._on_emotion_detected)
        self.event_bus.subscribe("user_preference_detected", self._on_user_preference_detected)
        
        logger.debug("已订阅相关事件")
    
    def _load_user_models(self):
        """从存储加载用户模型数据"""
        model_dir = os.path.join(root_dir, "data", "perception", "user_models")
        os.makedirs(model_dir, exist_ok=True)
        
        try:
            # 遍历用户模型文件
            for filename in os.listdir(model_dir):
                if filename.endswith(".json"):
                    user_id = filename[:-5]  # 去除.json后缀
                    model_path = os.path.join(model_dir, filename)
                    
                    with open(model_path, "r", encoding="utf-8") as f:
                        self.user_models[user_id] = json.load(f)
                        self.last_update_time[user_id] = time.time()
                        self.model_locks[user_id] = threading.Lock()
                        
            logger.info(f"已加载 {len(self.user_models)} 个用户模型")
        except Exception as e:
            logger.error_status(f"加载用户模型数据失败: {e}")
    
    def _save_user_model(self, user_id: str):
        """保存用户模型数据到存储"""
        if user_id not in self.user_models:
            return
            
        model_dir = os.path.join(root_dir, "data", "perception", "user_models")
        os.makedirs(model_dir, exist_ok=True)
        
        model_path = os.path.join(model_dir, f"{user_id}.json")
        
        try:
            with open(model_path, "w", encoding="utf-8") as f:
                json.dump(self.user_models[user_id], f, ensure_ascii=False, indent=2)
                
            logger.debug(f"已保存用户 {user_id} 的模型")
        except Exception as e:
            logger.error_status(f"保存用户 {user_id} 的模型失败: {e}")
    
    def _ensure_user_model(self, user_id: str) -> Dict[str, Any]:
        """确保用户模型存在"""
        if user_id not in self.user_models:
            # 创建新的用户模型
            self.user_models[user_id] = {
                "user_id": user_id,
                "created_at": time.time(),
                "last_active": time.time(),
                "basic_info": {
                    "name": "",
                    "gender": "",
                    "age_group": "",
                    "location": "",
                    "occupation": "",
                    "education": "",
                    "language": "zh-CN"
                },
                "preferences": {
                    "topics": {},
                    "interaction_style": "",
                    "response_length": "medium",
                    "formality_level": "neutral",
                    "humor_level": "medium"
                },
                "behavior_patterns": {
                    "activity_times": {},
                    "message_frequency": "medium",
                    "response_rate": 0,
                    "conversation_length": "medium",
                    "initiative_level": "medium"
                },
                "emotional_profile": {
                    "baseline_mood": "neutral",
                    "emotional_volatility": "medium",
                    "recent_emotions": [],
                    "emotional_triggers": {}
                },
                "knowledge_graph": {
                    "entities": {},
                    "relationships": {}
                },
                "interaction_history": {
                    "total_interactions": 0,
                    "last_interaction": None,
                    "conversation_topics": {},
                    "significant_events": []
                }
            }
            
            # 创建用户模型锁
            self.model_locks[user_id] = threading.Lock()
            self.last_update_time[user_id] = time.time()
            
            logger.info(f"已创建用户 {user_id} 的新模型")
        
        return self.user_models[user_id]
    
    def _on_user_message(self, data: Dict[str, Any]):
        """处理用户消息事件"""
        try:
            message = data.get("message", "")
            user_id = data.get("user_id", "")
            user_name = data.get("user_name", "")
            
            if not message or not user_id:
                return
            
            # 确保用户模型存在
            user_model = self._ensure_user_model(user_id)
            
            # 更新基本信息
            if user_name and not user_model["basic_info"]["name"]:
                user_model["basic_info"]["name"] = user_name
            
            # 获取锁
            with self.model_locks[user_id]:
                # 更新最后活跃时间
                user_model["last_active"] = time.time()
                
                # 更新交互历史
                user_model["interaction_history"]["total_interactions"] += 1
                user_model["interaction_history"]["last_interaction"] = {
                    "timestamp": time.time(),
                    "message": message,
                    "direction": "incoming"
                }
                
                # 定期保存用户模型
                if time.time() - self.last_update_time.get(user_id, 0) > 300:  # 5分钟
                    self._save_user_model(user_id)
                    self.last_update_time[user_id] = time.time()
        except Exception as e:
            logger.error_status(f"处理用户消息事件失败: {e}")
    
    def _on_intention_analyzed(self, data: Dict[str, Any]):
        """处理意图分析事件"""
        try:
            intention = data.get("intention", {})
            user_id = data.get("user_id", "")
            message = data.get("message", "")
            
            if not intention or not user_id:
                return
            
            # 确保用户模型存在
            user_model = self._ensure_user_model(user_id)
            
            # 获取意图
            main_intent = intention.get("main_intent", "默认")
            
            # 获取锁
            with self.model_locks[user_id]:
                # 更新交互历史中的对话主题
                topics = user_model["interaction_history"]["conversation_topics"]
                topics[main_intent] = topics.get(main_intent, 0) + 1
                
                # 如果是信息查询或推荐请求，更新用户兴趣主题
                if main_intent in ["信息查询", "推荐请求"]:
                    self._update_user_topics(user_id, message)
                
                # 如果是偏好设置，更新用户偏好
                if main_intent == "个人助理任务" and "设置" in message:
                    self._extract_preferences(user_id, message)
        except Exception as e:
            logger.error_status(f"处理意图分析事件失败: {e}")
    
    def _on_emotion_detected(self, data: Dict[str, Any]):
        """处理情绪检测事件"""
        try:
            emotion = data.get("emotion", "")
            user_id = data.get("user_id", "")
            intensity = data.get("intensity", 0.5)
            message = data.get("message", "")
            
            if not emotion or not user_id:
                return
            
            # 确保用户模型存在
            user_model = self._ensure_user_model(user_id)
            
            # 获取锁
            with self.model_locks[user_id]:
                # 更新情感配置
                emotional_profile = user_model["emotional_profile"]
                
                # 添加到最近情绪
                emotional_profile["recent_emotions"].append({
                    "emotion": emotion,
                    "intensity": intensity,
                    "timestamp": time.time(),
                    "trigger": message[:50] if message else ""  # 保存触发情绪的消息前50个字符
                })
                
                # 保留最近的10条情绪记录
                emotional_profile["recent_emotions"] = emotional_profile["recent_emotions"][-10:]
                
                # 更新情绪触发因素
                if message:
                    triggers = emotional_profile["emotional_triggers"]
                    triggers[emotion] = triggers.get(emotion, [])
                    triggers[emotion].append({
                        "message": message[:50],  # 保存消息前50个字符
                        "timestamp": time.time()
                    })
                    
                    # 每种情绪最多保留5个触发因素
                    triggers[emotion] = triggers[emotion][-5:]
                
                # 更新基线情绪
                if len(emotional_profile["recent_emotions"]) >= 5:
                    # 统计最近的情绪
                    emotion_counts = {}
                    for e in emotional_profile["recent_emotions"]:
                        emotion_counts[e["emotion"]] = emotion_counts.get(e["emotion"], 0) + 1
                    
                    # 找出最常见的情绪
                    baseline_mood = max(emotion_counts.items(), key=lambda x: x[1])[0]
                    emotional_profile["baseline_mood"] = baseline_mood
        except Exception as e:
            logger.error_status(f"处理情绪检测事件失败: {e}")
    
    def _on_user_preference_detected(self, data: Dict[str, Any]):
        """处理用户偏好检测事件"""
        try:
            preference_type = data.get("type", "")
            preference_value = data.get("value", "")
            user_id = data.get("user_id", "")
            
            if not preference_type or not preference_value or not user_id:
                return
            
            # 确保用户模型存在
            user_model = self._ensure_user_model(user_id)
            
            # 获取锁
            with self.model_locks[user_id]:
                # 更新用户偏好
                preferences = user_model["preferences"]
                
                if preference_type == "interaction_style":
                    preferences["interaction_style"] = preference_value
                elif preference_type == "response_length":
                    preferences["response_length"] = preference_value
                elif preference_type == "formality_level":
                    preferences["formality_level"] = preference_value
                elif preference_type == "humor_level":
                    preferences["humor_level"] = preference_value
                elif preference_type == "topic":
                    topics = preferences["topics"]
                    topics[preference_value] = topics.get(preference_value, 0) + 1
        except Exception as e:
            logger.error_status(f"处理用户偏好检测事件失败: {e}")
    
    def _update_user_topics(self, user_id: str, message: str):
        """更新用户兴趣主题"""
        if not message:
            return
            
        # 简单的关键词匹配
        topic_keywords = {
            "科技": ["电脑", "手机", "软件", "编程", "AI", "人工智能", "技术", "互联网", "智能", "数字"],
            "娱乐": ["电影", "音乐", "游戏", "综艺", "电视剧", "明星", "娱乐", "爱好", "休闲"],
            "体育": ["运动", "足球", "篮球", "健身", "比赛", "体育", "奥运", "锻炼"],
            "教育": ["学习", "教育", "课程", "学校", "大学", "知识", "培训", "考试", "专业"],
            "美食": ["美食", "菜谱", "餐厅", "食物", "美味", "烹饪", "厨师", "食谱"],
            "旅游": ["旅游", "旅行", "景点", "度假", "旅程", "旅馆", "酒店", "风景"],
            "文学": ["书籍", "阅读", "文学", "诗词", "小说", "作家", "故事", "文化"],
            "商业": ["商业", "创业", "投资", "金融", "经济", "市场", "公司", "股票"],
            "健康": ["健康", "医疗", "医院", "疾病", "保健", "养生", "医生", "药物"],
            "时尚": ["时尚", "服装", "穿搭", "美妆", "潮流", "设计", "品牌", "流行"]
        }
        
        user_model = self.user_models.get(user_id)
        if not user_model:
            return
            
        topics = user_model["preferences"]["topics"]
        
        # 匹配关键词
        for topic, keywords in topic_keywords.items():
            for keyword in keywords:
                if keyword in message:
                    topics[topic] = topics.get(topic, 0) + 1
                    break
    
    def _extract_preferences(self, user_id: str, message: str):
        """从消息中提取用户偏好"""
        if not message:
            return
            
        user_model = self.user_models.get(user_id)
        if not user_model:
            return
            
        preferences = user_model["preferences"]
        
        # 提取交互风格偏好
        interaction_styles = {
            "简洁": ["简洁", "简短", "直接", "要点", "不要废话"],
            "详细": ["详细", "详尽", "解释", "全面", "完整"],
            "友好": ["友好", "亲切", "温暖", "关怀", "体贴"],
            "专业": ["专业", "正式", "学术", "精确", "精准"],
            "幽默": ["幽默", "有趣", "风趣", "搞笑", "轻松"]
        }
        
        for style, keywords in interaction_styles.items():
            for keyword in keywords:
                if keyword in message:
                    preferences["interaction_style"] = style
                    break
        
        # 提取回复长度偏好
        if any(word in message for word in ["简短", "简洁", "短一点"]):
            preferences["response_length"] = "short"
        elif any(word in message for word in ["详细", "详尽", "长一点"]):
            preferences["response_length"] = "long"
        
        # 提取正式度偏好
        if any(word in message for word in ["正式", "严肃", "专业"]):
            preferences["formality_level"] = "formal"
        elif any(word in message for word in ["随意", "轻松", "非正式"]):
            preferences["formality_level"] = "informal"
        
        # 提取幽默度偏好
        if any(word in message for word in ["幽默", "搞笑", "有趣"]):
            preferences["humor_level"] = "high"
        elif any(word in message for word in ["严肃", "认真", "不要开玩笑"]):
            preferences["humor_level"] = "low"
    
    def get_user_model(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户模型
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户模型数据
        """
        # 确保用户模型存在
        return self._ensure_user_model(user_id)
    
    def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户偏好
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户偏好数据
        """
        user_model = self._ensure_user_model(user_id)
        return user_model["preferences"]
    
    def get_user_emotional_profile(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户情感配置
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户情感配置数据
        """
        user_model = self._ensure_user_model(user_id)
        return user_model["emotional_profile"]
    
    def update_user_basic_info(self, user_id: str, info: Dict[str, str]) -> bool:
        """
        更新用户基本信息
        
        Args:
            user_id: 用户ID
            info: 基本信息数据
            
        Returns:
            更新是否成功
        """
        try:
            user_model = self._ensure_user_model(user_id)
            
            with self.model_locks[user_id]:
                for key, value in info.items():
                    if key in user_model["basic_info"]:
                        user_model["basic_info"][key] = value
                
                self._save_user_model(user_id)
                self.last_update_time[user_id] = time.time()
                
            return True
        except Exception as e:
            logger.error_status(f"更新用户基本信息失败: {e}")
            return False
    
    def add_significant_event(self, user_id: str, event_type: str, description: str) -> bool:
        """
        添加用户重要事件
        
        Args:
            user_id: 用户ID
            event_type: 事件类型
            description: 事件描述
            
        Returns:
            添加是否成功
        """
        try:
            user_model = self._ensure_user_model(user_id)
            
            with self.model_locks[user_id]:
                user_model["interaction_history"]["significant_events"].append({
                    "type": event_type,
                    "description": description,
                    "timestamp": time.time(),
                    "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
                
                self._save_user_model(user_id)
                self.last_update_time[user_id] = time.time()
                
            return True
        except Exception as e:
            logger.error_status(f"添加用户重要事件失败: {e}")
            return False
    
    def add_knowledge_entity(self, user_id: str, entity_type: str, entity_name: str, properties: Dict[str, Any]) -> bool:
        """
        添加用户知识图谱实体
        
        Args:
            user_id: 用户ID
            entity_type: 实体类型
            entity_name: 实体名称
            properties: 实体属性
            
        Returns:
            添加是否成功
        """
        try:
            user_model = self._ensure_user_model(user_id)
            
            with self.model_locks[user_id]:
                entities = user_model["knowledge_graph"]["entities"]
                
                if entity_type not in entities:
                    entities[entity_type] = {}
                    
                entities[entity_type][entity_name] = {
                    "properties": properties,
                    "last_mentioned": time.time(),
                    "mention_count": 1
                }
                
                self._save_user_model(user_id)
                self.last_update_time[user_id] = time.time()
                
            return True
        except Exception as e:
            logger.error_status(f"添加用户知识图谱实体失败: {e}")
            return False
    
    def add_knowledge_relationship(self, user_id: str, source: str, relation: str, target: str) -> bool:
        """
        添加用户知识图谱关系
        
        Args:
            user_id: 用户ID
            source: 源实体
            relation: 关系类型
            target: 目标实体
            
        Returns:
            添加是否成功
        """
        try:
            user_model = self._ensure_user_model(user_id)
            
            with self.model_locks[user_id]:
                relationships = user_model["knowledge_graph"]["relationships"]
                
                relationship_id = f"{source}_{relation}_{target}"
                relationships[relationship_id] = {
                    "source": source,
                    "relation": relation,
                    "target": target,
                    "last_mentioned": time.time(),
                    "confidence": 0.8  # 初始置信度
                }
                
                self._save_user_model(user_id)
                self.last_update_time[user_id] = time.time()
                
            return True
        except Exception as e:
            logger.error_status(f"添加用户知识图谱关系失败: {e}")
            return False
    
    def shutdown(self):
        """关闭模块"""
        # 保存所有用户模型
        for user_id in self.user_models:
            self._save_user_model(user_id)
        
        logger.info(f"用户模型模块 {self.module_id} 已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> UserModel:
    """
    获取用户模型模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        用户模型模块实例
    """
    return UserModel(config=config) 