#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
意图识别模块 - Intent Recognition Module

负责分析用户输入，识别用户意图，为后续处理提供决策依据。
支持多种识别方法：基于规则、AI分析、关键词匹配等。

作者: 魅魔程序员
创建日期: 2024-12-27
版本: 3.2
"""

import os
import sys
import time
import json
import uuid
import re
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import traceback

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 导入工具和依赖
from utilities.unified_logger import get_unified_logger
from core.enhanced_event_bus import get_instance as get_event_bus, EventPriority
from core.life_context import get_instance as get_life_context
from utilities.singleton_manager import register, get, get_silent, exists
# 注释掉有问题的导入，避免冲突
# from utilities.config_loader import ConfigLoader

# 初始化日志
logger = get_unified_logger("intent_recognition")

try:
    import openai
except ImportError:
    logger.warning_status("OpenAI库未安装，AI智慧能力意图识别将不可用。可以使用pip install openai安装。")
    openai = None

# 🔥 新增：导入意图上下文管理器
intent_context_manager_available = False
get_intent_context_manager = None

try:
    from core.intent_context_manager import get_intent_context_manager
    intent_context_manager_available = True
    logger.info("✅ 意图上下文管理器导入成功")
except ImportError as e:
    logger.warning_status(f"⚠️ 意图上下文管理器不可用: {e}")
    intent_context_manager_available = False
except Exception as e:
    logger.error_status(f"❌ 意图上下文管理器导入失败: {e}")
    intent_context_manager_available = False

# 单例控制变量
_instance_lock = threading.RLock()
_instance = None
_initialization_in_progress = False
_initialization_complete = False

class IntentRecognizer:
    """意图识别器类，负责分析和识别用户输入的意图"""
    
    def __new__(cls, *args, **kwargs):
        """确保只创建一个实例"""
        global _instance, _instance_lock
        
        # 使用单例管理器
        instance_name = "intent_recognizer"
        
        # 🔥 使用get_silent避免WARNING
        existing_instance = get_silent(instance_name)
        if existing_instance:
            logger.debug(f"从单例管理器获取已存在的意图识别器实例")
            return existing_instance
            
        # 使用锁确保线程安全
        with _instance_lock:
            if _instance is None:
                # 创建新实例，但不在这里注册
                _instance = super().__new__(cls)
                
            return _instance
    
    def __init__(self):
        """初始化意图识别器"""
        global _instance_lock, _initialization_in_progress, _initialization_complete
        
        # 使用锁确保线程安全
        with _instance_lock:
            # 检查是否已经完成初始化
            if _initialization_complete:
                logger.debug("意图识别器已经完成初始化，跳过")
                return
                
            # 检查是否正在初始化中
            if _initialization_in_progress:
                logger.debug("意图识别器正在初始化中，跳过")
                return
                
            # 标记开始初始化
            _initialization_in_progress = True
        
        try:
            logger.success("开始初始化意图识别器...")
            
            # 初始化基本属性
            self.model = None
            self.threshold = 0.5
            self.life_context = None
            self.event_bus = None
            self.ai_service = None
            
            # 初始化事件处理器状态
            self._event_handlers_registered = False
            
            # 加载配置
            self.config = self._load_config()
            
            # 尝试初始化OpenAI
            try:
                self._init_openai_config()
                self.openai_available = True
                logger.success("OpenAI配置初始化成功")
            except Exception as e:
                self.openai_available = False
                logger.warning_status(f"OpenAI配置初始化失败: {e}")
            
            # 尝试获取事件总线和生命上下文
            try:
                self.event_bus = get_event_bus()
                self.life_context = get_life_context()
                
                # 尝试获取AI服务适配器
                try:
                    from adapters.unified_ai_adapter import get_instance as get_ai_adapter
                    self.ai_service = get_ai_adapter()
                    logger.success("AI服务适配器初始化成功")
                except Exception as e:
                    logger.warning_status(f"AI服务适配器初始化失败: {e}，将使用简单意图识别")
                    self.ai_service = None
                
                # 注册事件处理器
                self._register_event_handlers()
                
                logger.success("意图识别器连接到事件总线和生命上下文")
            except ImportError as e:
                logger.warning_status(f"初始化事件总线或生命上下文失败: {e}")
                self.event_bus = None
                self.life_context = None
            
            # 标记初始化完成
            with _instance_lock:
                _initialization_complete = True
                
            logger.success("意图识别器初始化完成")
            
        except Exception as e:
            logger.error_status(f"意图识别器初始化失败: {e}")
            logger.error_status(traceback.format_exc())
            
            # 重置初始化标志，允许下次重试
            with _instance_lock:
                _initialization_in_progress = False
    
    def shutdown(self):
        """
        关闭意图识别器，清理资源
        """
        try:
            logger.info("开始关闭意图识别器...")
            
            # 清理配置
            if hasattr(self, 'config'):
                self.config.clear()
            
            # 重置状态
            self.openai_available = False
            self._event_handlers_registered = False
            
            # 清理引用
            self.event_bus = None
            self.life_context = None
            self.ai_service = None
            self.model = None
            
            logger.success("✅ 意图识别器已成功关闭")
            
        except Exception as e:
            logger.error_status(f"❌ 意图识别器关闭失败: {e}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = os.path.join(PROJECT_ROOT, "config", "perception", "intent_recognition.json")
        
        if os.path.exists(config_path):
            try:
                # 检查文件大小，避免读取空文件
                file_size = os.path.getsize(config_path)
                if file_size == 0:
                    logger.error_status(f"配置文件为空: {config_path}，使用默认配置")
                    return self._get_default_config()
                
                with open(config_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:
                        logger.error_status(f"配置文件内容为空: {config_path}，使用默认配置")
                        return self._get_default_config()
                    
                    config = json.loads(content)
                    logger.info(f"已加载意图识别配置: {config_path}")
                    return config
                    
            except json.JSONDecodeError as e:
                logger.error_status(f"配置文件JSON格式错误: {config_path}, 错误: {e}，使用默认配置")
                return self._get_default_config()
            except Exception as e:
                logger.error_status(f"加载意图识别配置失败: {config_path}, 错误: {e}，使用默认配置")
                return self._get_default_config()
        else:
            logger.warning_status(f"配置文件不存在: {config_path}，使用默认配置")
        
        # 返回默认配置
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "use_ai_capability": True,
            "confidence_threshold": 0.7,
            "default_intent": "chat",
            "max_context_memory": 5,
            "intent_types": [
                "query", "task", "recommendation", "navigation", "chat",
                "assistant", "learning", "entertainment", "business", "support",
                "drawing", "url", "stop", "delete", "command", "question",
                "social", "emotional", "shopping", "travel", "food", "health",
                "finance", "news", "weather", "music", "video", "game", "book", "movie",
                "financial_query"  # 新增金融查询意图
            ],
            "intent_mapping": {
                "信息查询": "query",
                "任务执行": "task",
                "推荐请求": "recommendation",
                "导航或指引": "navigation",
                "交互或对话": "chat",
                "个人助理任务": "assistant",
                "学习或教育": "learning",
                "创意或娱乐": "entertainment",
                "商业或专业": "business",
                "技术支持": "support",
                "绘画创作": "drawing",
                "网址/链接解读": "url",
                "不希望聊天/不希望互动": "stop",
                "拉黑好友/删除好友": "delete",
                "默认": "chat",
                "金融查询": "financial_query"
            },
            "keyword_intent_mapping": {
                "画一张": "drawing",
                "画一幅": "drawing",
                "帮我画": "drawing",
                "生成图片": "drawing",
            },
            "event_handlers": {
                "user.input": True,
                "perception.input": True
            }
        }
    
    def _init_openai_config(self):
        """初始化OpenAI配置"""
        if not openai:
            logger.warning_status("OpenAI库未安装，AI智慧能力意图识别将不可用")
            self.openai_available = False
            return
        
        try:
            # 从环境变量或配置文件获取API密钥
            api_key = os.environ.get("OPENAI_API_KEY")
            api_base = os.environ.get("OPENAI_API_BASE")
            
            if not api_key:
                # 尝试从配置文件获取
                config_path = os.path.join(PROJECT_ROOT, "config", "openai_config.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        openai_config = json.load(f)
                        api_key = openai_config.get("api_key")
                        api_base = openai_config.get("api_base")
            
            if api_key:
                openai.api_key = api_key
                if api_base:
                    openai.api_base = api_base
                self.openai_available = True
                logger.success("OpenAI配置初始化成功")
            else:
                logger.warning_status("未找到OpenAI API密钥，AI智慧能力意图识别将不可用")
                self.openai_available = False
        except Exception as e:
            logger.error_status(f"初始化OpenAI配置失败: {e}")
            self.openai_available = False
    
    def _register_event_handlers(self):
        """注册事件处理器"""
        # 检查是否已经注册过事件处理器
        if hasattr(self, '_event_handlers_registered') and self._event_handlers_registered:
            logger.debug("意图识别器已注册事件处理器，跳过")
            return
        
        # 确保事件总线已初始化
        if not self.event_bus:
            logger.warning_status("事件总线未初始化，无法注册事件处理器")
            return
        
        try:
            # 检查事件处理器是否已经在事件总线上注册
            user_input_handler = self._handle_user_input
            
            if hasattr(self.event_bus, 'is_subscribed') and self.event_bus.is_subscribed("user.input", user_input_handler):
                logger.debug("用户输入事件处理器已注册，跳过")
            else:
                # 注册用户输入事件处理器
                self.event_bus.subscribe("user.input", user_input_handler)
                logger.debug("已注册用户输入事件处理器")
            
            # 设置标志，表示事件处理器已注册
            self._event_handlers_registered = True
            logger.info("意图识别器已注册事件处理器")
            
        except Exception as e:
            logger.error_status(f"注册事件处理器异常: {e}")
            logger.error_status(traceback.format_exc())
    
    def _handle_user_input(self, data: Dict[str, Any]):
        """
        处理用户输入事件
        
        Args:
            data: 事件数据
        """
        try:
            text = data.get("text", "")
            user_id = data.get("user_id", "default_user")
            
            logger.info(f"意图识别收到用户输入: {text[:30]}...")
            
            # 特殊关键词处理 - 绘画
            drawing_keywords = ["画一张", "画一幅", "帮我画"]
            is_drawing = False
            
            for keyword in drawing_keywords:
                if keyword in text:
                    is_drawing = True
                    logger.info(f"检测到绘画关键词: {keyword}")
                    break
            
            # 分析用户意图
            intent_result = self.analyze_intent(text, user_id)
            
            # 记录意图分析结果
            logger.info(f"意图分析结果: {intent_result.get('type', 'unknown')} (置信度: {intent_result.get('confidence', 0)})")
            
            # 更新生命上下文
            intent_context_key = f"perception.intent.{user_id}"
            self.life_context.update_context(intent_context_key, intent_result)
            
            # 向事件总线发布分析结果
            result_data = {
                "user_id": user_id,
                "intent": intent_result,
                "timestamp": time.time()
            }
            self.event_bus.publish("intent.recognized", result_data)
            
            # 强制绘画意图检测逻辑 - 根据关键词
            if is_drawing or intent_result.get("type") == "drawing":
                logger.info("检测到绘画意图，发布绘画意图事件")

                # 增加绘画意图置信度
                if is_drawing:
                    intent_result["confidence"] = max(0.8, intent_result.get("confidence", 0))
                    intent_result["type"] = "drawing"

                # 发布绘画意图事件
                self.event_bus.publish("drawing.intent.detected", {
                    "user_id": user_id,
                    "intent": intent_result,
                    "text": text
                })
            
        except Exception as e:
            logger.error_status(f"处理用户输入事件异常: {e}")
            import traceback
            logger.error_status(traceback.format_exc())
    
    def recognize_intent(self, text: str, user_id: str = None) -> Dict[str, Any]:
        """
        识别用户输入的意图
        
        Args:
            text: 用户输入文本
            user_id: 用户ID
            
        Returns:
            意图识别结果
        """
        logger.info(f"识别意图: {text[:50]}...")
        
        try:
            # 判断是否使用AI能力进行意图识别
            if self.config.get("use_ai_capability", True) and self.openai_available:
                # 使用AI能力进行复杂意图分析
                ai_intent = self.analyze_intent(text, user_id)
                if ai_intent:
                    logger.info(f"AI意图识别结果: {ai_intent}")
                    
                    # 将AI意图结果转换为标准格式
                    intent_result = {
                        "type": self._map_ai_intent_to_type(ai_intent.get("main_intent")),
                        "content": text,
                        "confidence": ai_intent.get("confidence", 0) / 100,
                        "requires_realtime_data": ai_intent.get("requires_realtime_data", False),
                        "time_sensitivity": ai_intent.get("time_sensitivity", "低"),
                        "detailed_intent": ai_intent.get("detailed_intent"),
                        "sub_intent": ai_intent.get("sub_intent"),
                        "original_ai_result": ai_intent
                    }
                    
                    # 更新生命上下文
                    self._update_life_context(user_id, intent_result)
                    
                    return intent_result
            
            # 回退到基本意图识别
            return self._basic_intent_recognition(text)
            
        except Exception as e:
            logger.error_status(f"意图识别异常: {e}")
            traceback.print_exc()
            
            # 返回默认意图
            return {
                "type": self.config.get("default_intent", "chat"),
                "content": text,
                "confidence": 0.5,
                "error": str(e)
            }
    
    def _map_ai_intent_to_type(self, ai_intent: str) -> str:
        """
        将AI识别的意图映射到系统内部类型
        
        Args:
            ai_intent: AI识别的意图类别
            
        Returns:
            系统内部意图类型
        """
        # 从配置文件中获取意图映射
        intent_mapping = self.config.get("intent_mapping", {})
        
        # 如果配置文件中没有映射，使用默认映射
        if not intent_mapping:
            intent_mapping = {
                "信息查询": "query",
                "任务执行": "task",
                "推荐请求": "recommendation",
                "导航或指引": "navigation",
                "交互或对话": "chat",
                "个人助理任务": "assistant",
                "学习或教育": "learning",
                "创意或娱乐": "entertainment",
                "商业或专业": "business",
                "技术支持": "support",
                "绘画创作": "drawing",
                "网址/链接解读": "url",
                "不希望聊天/不希望互动": "stop",
                "拉黑好友/删除好友": "delete",
                "默认": "chat"
            }
        
        # 尝试直接映射
        if ai_intent in intent_mapping:
            return intent_mapping.get(ai_intent)
        
        # 尝试部分匹配
        for key, value in intent_mapping.items():
            if key in ai_intent:
                return value
        
        # 默认返回chat
        default_intent = self.config.get("default_intent", "chat")
        return default_intent
    
    def _update_life_context(self, user_id: str, intent_result: Dict[str, Any]):
        """
        更新生命上下文
        
        Args:
            user_id: 用户ID
            intent_result: 意图识别结果
        """
        context_key = f"perception.intent.{user_id}"
        self.life_context.update_context(context_key, intent_result)
        
        # 记录意图历史
        history_key = f"perception.intent.history.{user_id}"
        intent_history = self.life_context.get_context(history_key, [])
        
        # 添加新意图并限制历史长度
        intent_history.append({
            "timestamp": time.time(),
            "type": intent_result.get("type"),
            "confidence": intent_result.get("confidence"),
            "content": intent_result.get("content")[:100] if intent_result.get("content") else ""
        })
        
        # 保留最近的10条记录
        if len(intent_history) > 10:
            intent_history = intent_history[-10:]
            
        self.life_context.update_context(history_key, intent_history)
    
    def _basic_intent_recognition(self, text: str) -> Dict[str, Any]:
        """
        基本意图识别方法（不依赖AI）
        
        Args:
            text: 用户输入文本
            
        Returns:
            意图识别结果
        """
        # 基本关键词匹配
        text_lower = text.lower()
        
        # 命令检测
        if text.startswith('/') or text.startswith('!'):
            return {
                "type": "command",
                "content": text[1:].strip(),
                "confidence": 0.95
            }
        
        # 默认为聊天意图
        return {
            "type": "chat",
            "content": text,
            "confidence": 0.6
        }
    
    def analyze_intent(self, text: str, user_id: str = None) -> Dict[str, Any]:
        """
        分析输入文本的意图 - 两层识别策略
        
        第一层：关键词匹配（快速识别）
        第二层：AI语义分析（深度理解）
        
        Args:
            text: 输入文本
            user_id: 用户ID
            
        Returns:
            意图分析结果
        """
        logger.info(f"🧠 开始两层意图识别: {text[:30]}...")
        
        # 检查输入是否为空
        if not text or len(text.strip()) == 0:
            return {
                "type": "chat",
                "main_intent": "聊天",
                "content": "",
                "confidence": 0.5,
                "requires_realtime_data": False,
                "time_sensitivity": "无",
            }
        
        # 🔥 第一层：关键词匹配（高优先级，快速识别）
        logger.info("🔍 第一层：关键词匹配识别")

        
        # 1. 绘图关键词检测（优先级最高）
        drawing_keywords = ["帮我画","画一张"]
        for keyword in drawing_keywords:
            if keyword in text:
                logger.success(f"✅ 第一层匹配成功 - 绘图关键词: {keyword}")
                
                intent_result = {
                    "type": "drawing",
                    "main_intent": "绘画创作",
                    "content": text,
                    "confidence": 0.95,
                    "requires_realtime_data": False,
                    "time_sensitivity": "中",
                    "detection_method": "keyword_match"
                }
                
                self._finalize_intent_result(intent_result, user_id, text)
                return intent_result
        
        # 2. 金融相关关键词检测（高优先级）
        financial_keywords = ["市场分析", "投资建议", "投资组合", "技术分析", "基本面分析"]
        for keyword in financial_keywords:
            if keyword in text:
                logger.success(f"✅ 第一层匹配成功 - 金融关键词: {keyword}")
                
                intent_result = {
                    "type": "financial_query",
                    "main_intent": "金融查询",
                    "content": text,
                    "confidence": 0.95,
                    "requires_realtime_data": True,
                    "time_sensitivity": "高",
                    "detection_method": "keyword_match",
                    "financial_keyword": keyword
                }
                
                self._finalize_intent_result(intent_result, user_id, text)
                return intent_result
        
        # 3. 实时信息关键词检测
        realtime_keywords = ["最新", "实时", "查询", "搜索"]
        for keyword in realtime_keywords:
            if keyword in text:
                logger.success(f"✅ 第一层匹配成功 - 实时信息关键词: {keyword}")
                
                intent_result = {
                    "type": "query",
                    "main_intent": "信息查询",
                    "content": text,
                    "confidence": 0.90,
                    "requires_realtime_data": True,
                    "time_sensitivity": "高",
                    "detection_method": "keyword_match"
                }
                
                self._finalize_intent_result(intent_result, user_id, text)
                return intent_result
        
        # 3. 网址链接检测
        if "http" in text or "www" in text or ".com" in text:
            logger.success("✅ 第一层匹配成功 - 网址链接")
            
            intent_result = {
                "type": "url",
                "main_intent": "网址/链接解读",
                "content": text,
                "confidence": 0.95,
                "requires_realtime_data": True,
                "time_sensitivity": "中",
                "detection_method": "keyword_match"
            }
            
            self._finalize_intent_result(intent_result, user_id, text)
            return intent_result
        
        # 4. 提醒意图检测（高优先级）
        reminder_keywords = ["提醒我"]
        time_patterns = [r'\d+点', r'\d+:\d+',  r'\d+分钟后', r'\d+小时后', r'\d+天后']

        has_reminder_keyword = any(keyword in text for keyword in reminder_keywords)

        # 检查时间模式，但排除音频/视频时间戳格式
        has_time_pattern = False
        for pattern in time_patterns:
            if re.search(pattern, text):
                # 如果是时间格式，需要进一步检查是否为音频/视频时间戳
                if pattern == r'\d+:\d+':
                    # 检查是否为音频/视频时间戳格式（如 00:00-00:05: 或 00:00: ）
                    timestamp_pattern = r'\d{2}:\d{2}(-\d{2}:\d{2})?:'
                    if not re.search(timestamp_pattern, text):
                        has_time_pattern = True
                        break
                else:
                    has_time_pattern = True
                    break

        if has_reminder_keyword or has_time_pattern:
            confidence = 0.9 if has_reminder_keyword and has_time_pattern else 0.7
            logger.success(f"✅ 第一层匹配成功 - 提醒意图: 关键词={has_reminder_keyword}, 时间模式={has_time_pattern}")

            intent_result = {
                "type": "reminder_request",
                "main_intent": "提醒任务",
                "content": text,
                "confidence": confidence,
                "requires_realtime_data": False,
                "time_sensitivity": "高",
                "detection_method": "keyword_match",
                "keywords": [kw for kw in reminder_keywords if kw in text],
                "time_patterns": [pattern for pattern in time_patterns if re.search(pattern, text)]
            }

            self._finalize_intent_result(intent_result, user_id, text)
            return intent_result
        
        # 5. 推荐请求检测
        recommendation_keywords = ["推荐", "建议", "有什么好的", "什么比较好"]
        for keyword in recommendation_keywords:
            if keyword in text:
                logger.success(f"✅ 第一层匹配成功 - 推荐关键词: {keyword}")
                
                intent_result = {
                    "type": "recommendation",
                    "main_intent": "推荐请求",
                    "content": text,
                    "confidence": 0.85,
                    "requires_realtime_data": False,
                    "time_sensitivity": "低",
                    "detection_method": "keyword_match"
                }
                
                self._finalize_intent_result(intent_result, user_id, text)
                return intent_result
        
        # 🔥 第二层：AI语义分析（深度理解）
        logger.info("🤖 第一层未匹配，启动第二层：AI语义分析")
        
        try:
            # 导入AI意图分析系统
            from cognitive_modules.perception.intention_system import get_instance as get_intention_system
            intention_system = get_intention_system()
            
            # 调用AI进行语义分析
            ai_intention = intention_system.analyze_intention(text, user_id)
            
            if ai_intention and ai_intention.get("main_intent"):
                logger.success(f"✅ 第二层AI分析成功 - 意图: {ai_intention.get('main_intent')}")
                
                # 将AI分析结果转换为标准格式
                intent_result = {
                    "type": self._map_ai_intent_to_type(ai_intention.get("main_intent")),
                    "main_intent": ai_intention.get("main_intent"),
                    "sub_intent": ai_intention.get("sub_intent"),
                    "detailed_intent": ai_intention.get("detailed_intent"),
                    "content": text,
                    "confidence": ai_intention.get("confidence", 0) / 100,  # 转换为0-1范围
                    "requires_realtime_data": ai_intention.get("requires_realtime_data", False),
                    "time_sensitivity": ai_intention.get("time_sensitivity", "低"),
                    "detection_method": "ai_semantic_analysis",
                    "original_ai_result": ai_intention
                }
                
                self._finalize_intent_result(intent_result, user_id, text)
                return intent_result
            else:
                logger.warning_status("⚠️ AI语义分析未返回有效结果")
                
        except Exception as e:
            logger.error_status(f"❌ AI语义分析失败: {e}")
        
        # 🔥 兜底策略：基础规则分析
        logger.info("🔧 使用兜底策略：基础规则分析")
        
        intent_result = self._basic_rule_analysis(text)
        intent_result["detection_method"] = "fallback_rule"
        
        self._finalize_intent_result(intent_result, user_id, text)
        return intent_result
    
    def _basic_rule_analysis(self, text: str) -> Dict[str, Any]:
        """基础规则分析（兜底策略）"""
        # 提醒意图检测
        reminder_keywords = ["提醒我", "提醒", "别忘了","安排", "计划"]
        time_patterns = [r'\d+点', r'\d+:\d+', r'明天', r'后天', r'下周', r'下个月', r'早上', r'上午', r'中午', r'下午', r'晚上', r'\d+分钟后', r'\d+小时后', r'\d+天后']

        has_reminder_keyword = any(keyword in text for keyword in reminder_keywords)

        # 检查时间模式，但排除音频/视频时间戳格式
        has_time_pattern = False
        for pattern in time_patterns:
            if re.search(pattern, text):
                # 如果是时间格式，需要进一步检查是否为音频/视频时间戳
                if pattern == r'\d+:\d+':
                    # 检查是否为音频/视频时间戳格式（如 00:00-00:05: 或 00:00: ）
                    timestamp_pattern = r'\d{2}:\d{2}(-\d{2}:\d{2})?:'
                    if not re.search(timestamp_pattern, text):
                        has_time_pattern = True
                        break
                else:
                    has_time_pattern = True
                    break
        
        if has_reminder_keyword or has_time_pattern:
            confidence = 0.9 if has_reminder_keyword and has_time_pattern else 0.7
            return {
                "type": "reminder_request",
                "main_intent": "提醒任务",
                "content": text,
                "confidence": confidence,
                "requires_realtime_data": False,
                "time_sensitivity": "高",
                "keywords": [kw for kw in reminder_keywords if kw in text],
                "time_patterns": [pattern for pattern in time_patterns if re.search(pattern, text)]
            }
        
        # 问题检测 - 🔥 老王修复：避免误判简单对话为查询意图
        question_keywords = ["什么是", "如何", "怎么样", "介绍", "解释", "？", "?"]
        # 🔥 排除明显的对话表达，避免误判
        conversation_patterns = ["不知道你在说什么", "你在说什么", "什么意思", "听不懂", "不明白"]

        # 检查是否是真正的问题查询，而不是对话表达
        is_real_question = False
        for kw in question_keywords:
            if kw in text:
                # 检查是否是对话表达
                is_conversation = any(pattern in text for pattern in conversation_patterns)
                if not is_conversation:
                    is_real_question = True
                    break

        if is_real_question:
            return {
                "type": "query",
                "main_intent": "信息查询",
                "content": text,
                "confidence": 0.75,
                "requires_realtime_data": False,
                "time_sensitivity": "低"
            }
        
        # 任务请求检测
        if any(kw in text for kw in ["帮我", "请你", "能否", "能不能", "可以"]):
            return {
                "type": "task",
                "main_intent": "任务执行",
                "content": text,
                "confidence": 0.70,
                "requires_realtime_data": False,
                "time_sensitivity": "中"
            }
        
        # 默认为聊天对话
        return {
            "type": "chat",
            "main_intent": "交互或对话",
            "content": text,
            "confidence": 0.60,
            "requires_realtime_data": False,
            "time_sensitivity": "无"
        }
    
    def _finalize_intent_result(self, intent_result: Dict[str, Any], user_id: str, text: str):
        """完成意图识别结果的最终处理"""
        # 记录意图分析结果
        logger.info(f"🎯 意图识别完成: {intent_result['type']} - {intent_result['main_intent']} (置信度: {intent_result['confidence']:.2f}) [{intent_result.get('detection_method', 'unknown')}]")
        
        # 更新上下文
        self._update_life_context(user_id, intent_result)
        
        # 🔥 新增：添加到意图上下文管理器
        if intent_context_manager_available and get_intent_context_manager:
            try:
                intent_context_mgr = get_intent_context_manager()
                # 🔥 修复：只传递user_id和intent_data两个参数，text包含在intent_data中
                intent_data = intent_result.copy()
                intent_data['content'] = text  # 将文本内容添加到intent_data中
                intent_context_mgr.add_intent_context(user_id, intent_data)
                logger.debug(f"已添加意图上下文记录: {user_id} - {intent_result['main_intent']}")
            except Exception as e:
                logger.warning_status(f"添加意图上下文失败: {e}")
        
        # 🔥 老王修复：不发布特殊绘画意图事件，避免重复调用绘画技能
        # 绘画意图通过通用的intent.recognized事件处理，在技能执行阶段统一调用
        if intent_result.get("type") == "drawing":
            logger.info("绘画意图识别完成，将通过技能执行阶段统一处理，避免重复调用")
            # self._publish_drawing_intent_event(user_id, text, intent_result)
        
        # 发布通用意图识别事件
        self._publish_intent_recognized_event(user_id, text, intent_result)
    
    def _publish_drawing_intent_event(self, user_id: str, text: str, intent_result: Dict[str, Any]):
        """发布绘画意图事件"""
        if self.event_bus:
            try:
                self.event_bus.publish("drawing.intent.detected", {
                    "user_id": user_id,
                    "text": text,
                    "intent": intent_result,
                    "timestamp": time.time()
                })
                logger.debug("已发布绘画意图事件")
            except Exception as e:
                logger.warning_status(f"发布绘画意图事件失败: {e}")
    
    def _publish_intent_recognized_event(self, user_id: str, text: str, intent_result: Dict[str, Any]):
        """发布通用意图识别事件"""
        if self.event_bus:
            try:
                # 使用同步方式发布事件，避免异步循环问题
                if hasattr(self.event_bus, 'publish_sync'):
                    self.event_bus.publish_sync("intent.recognized", {
                        "user_id": user_id,
                        "intent_name": intent_result.get("type"),
                        "intent": intent_result,
                        "text": text,
                        "confidence": intent_result.get("confidence", 0),
                        "timestamp": time.time()
                    })
                else:
                    self.event_bus.publish("intent.recognized", {
                        "user_id": user_id,
                        "intent_name": intent_result.get("type"),
                        "intent": intent_result,
                        "text": text,
                        "confidence": intent_result.get("confidence", 0),
                        "timestamp": time.time()
                    })
                logger.debug("已发布意图识别事件")
            except Exception as e:
                logger.warning_status(f"发布意图识别事件失败: {e}")

    def analyze_intention(self, text: str, user_id: str = None) -> Dict[str, Any]:
        """
        分析用户意图 (别名，与原有系统兼容)
        
        Args:
            text: 输入文本
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 意图分析结果
        """
        # 先尝试使用现有方法分析
        intent_result = self.analyze_intent(text, user_id)
        
        # 检查是否是绘画相关请求
        drawing_keywords = ["画一张", "画一幅", "帮我画"]
        is_drawing = False
        
        for keyword in drawing_keywords:
            if keyword in text:
                is_drawing = True
                break
        
        # 如果是绘画请求，确保结果格式正确
        if is_drawing:
            # 确保返回结果包含必要的字段
            if "main_intent" not in intent_result:
                intent_result["main_intent"] = "drawing"
            if "detailed_intent" not in intent_result:
                intent_result["detailed_intent"] = "画" + text
            
            # 设置绘画相关属性
            intent_result["requires_realtime_data"] = False
            intent_result["time_sensitivity"] = "低"
            intent_result["confidence"] = 0.9
            
            # 发布绘画意图事件
            if self.event_bus:
                try:
                    if hasattr(self.event_bus, 'publish_sync'):
                        self.event_bus.publish_sync("drawing.intent.detected", {
                            "user_id": user_id,
                            "text": text,
                            "intent": intent_result,
                            "timestamp": time.time()
                        })
                    else:
                        logger.warning_status("事件总线不支持同步发布，跳过drawing.intent.detected事件")
                except Exception as e:
                    logger.warning_status(f"发布绘画意图事件失败: {e}")
        
        return intent_result

    def get_intent_confidence(self, text: str, intent_type: str, user_id: str = None) -> float:
        """
        🔥 香草修复：获取特定意图类型的置信度

        Args:
            text: 输入文本
            intent_type: 意图类型
            user_id: 用户ID

        Returns:
            float: 置信度 (0.0-1.0)
        """
        try:
            # 分析意图
            intent_result = self.analyze_intent(text, user_id)

            # 如果识别的意图类型匹配，返回置信度
            if intent_result.get("type") == intent_type:
                return intent_result.get("confidence", 0.0)

            # 如果不匹配，返回较低的置信度
            return 0.1

        except Exception as e:
            logger.error_status(f"获取意图置信度失败: {e}")
            return 0.0


def get_instance(module_config=None) -> IntentRecognizer:
    """
    获取意图识别器的单例实例
    
    Args:
        module_config: 模块配置（可选）
        
    Returns:
        IntentRecognizer: 意图识别器实例
    """
    global _instance, _instance_lock
    
    # 使用单例管理器
    instance_name = "intent_recognizer"
    
    # 🔥 使用get_silent避免WARNING，然后检查是否存在
    existing_instance = get_silent(instance_name)
    if existing_instance:
        return existing_instance
    
    # 使用锁确保线程安全
    with _instance_lock:
        if _instance is None:
            # 创建新实例
            _instance = IntentRecognizer()
            
        # 🔥 使用get_silent检查是否已注册，避免重复注册
        if get_silent(instance_name) is None:
            register(instance_name, _instance)
            logger.info(f"✅ 意图识别器已注册到单例管理器")
        else:
            logger.debug(f"意图识别器已存在于单例管理器中，跳过注册")
    
    # 返回实例
    return _instance


# 思维链路调用的函数
def process(context):
    """
    处理思维链路上下文中的意图识别
    
    Args:
        context: 思维链路上下文对象
        
    Returns:
        意图识别结果
    """
    try:
        # 🔥 首先检查是否已经有意图识别结果，避免重复识别
        if hasattr(context, 'get_shared_data'):
            existing_intent = context.get_shared_data("intent")
            intent_recognized = context.get_shared_data("intent_recognized", False)
            
            if existing_intent and intent_recognized:
                logger.info("检测到已存在的意图识别结果，跳过重复识别")
                return {
                    "type": existing_intent.get("type", "chat"),
                    "main_intent": existing_intent.get("main_intent", "聊天"),
                    "confidence": existing_intent.get("confidence", 0.8),
                    "requires_realtime_data": existing_intent.get("requires_realtime_data", False),
                    "time_sensitivity": existing_intent.get("time_sensitivity", "低"),
                    "detection_method": "cached_result",
                    "cached": True
                }
        
        # 获取意图识别器实例
        recognizer = get_instance()
        
        # 获取输入文本和用户ID - 🔥 老王修复：完善用户ID获取逻辑
        input_text = ""
        user_id = None
        
        if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            input_text = context.input_data.get("text", "")
            user_id = context.input_data.get("user_id")
        elif hasattr(context, 'get_shared_data'):
            input_text = context.get_shared_data("input_text", "")
            user_id = context.get_shared_data("user_id")
        elif isinstance(context, dict):
            # 🔥 老王修复：优先从context字典中获取用户ID
            input_text = context.get("text", context.get("input_text", ""))
            user_id = context.get("user_id")
            
            # 如果还没有用户ID，尝试从其他字段获取
            if not user_id:
                user_id = context.get("from_user_id", context.get("from_user_ID"))
        
        # 🔥 老王修复：如果context有直接的text属性，优先使用
        if hasattr(context, 'text') and not input_text:
            input_text = context.text
        if hasattr(context, 'user_id') and not user_id:
            user_id = context.user_id
        
        # 🔥 P0级别修复：用户ID验证和自动修复
        if not user_id:
            # 尝试生成临时用户ID，而不是直接报错
            user_id = f"temp_user_{int(time.time())}"
            logger.warning(f"⚠️ 用户ID缺失，已生成临时ID: {user_id}")
            
            # 继续处理，不阻断意图识别流程
        
        if not input_text:
            logger.warning_status("未找到输入文本，无法进行意图识别")
            return {
                "type": "chat",
                "main_intent": "聊天",
                "confidence": 0.5,
                "requires_realtime_data": False,
                "time_sensitivity": "低",
                "detection_method": "fallback_empty"
            }
        
        # 执行意图识别
        logger.info(f"开始意图识别: {input_text[:30]}...")
        result = recognizer.analyze_intent(input_text, user_id)
        
        # 🔥 将识别结果设置到共享数据中，标记为已识别
        if hasattr(context, 'set_shared_data'):
            context.set_shared_data("intent", result)
            context.set_shared_data("intent_recognized", True)
            logger.debug("已将意图识别结果设置到共享数据中")
        
        logger.success(f"意图识别完成: {result.get('type')} - {result.get('main_intent')} (置信度: {result.get('confidence', 0):.2f})")
        return result
        
    except Exception as e:
        logger.error_status(f"意图识别处理失败: {e}")
        import traceback
        logger.error_status(traceback.format_exc())
        
        # 返回默认结果
        fallback_result = {
            "type": "chat",
            "main_intent": "聊天",
            "confidence": 0.3,
            "requires_realtime_data": False,
            "time_sensitivity": "低",
            "detection_method": "error_fallback",
            "error": str(e)
        }
        
        # 🔥 即使出错也要设置到共享数据中，避免重复尝试
        if hasattr(context, 'set_shared_data'):
            context.set_shared_data("intent", fallback_result)
            context.set_shared_data("intent_recognized", True)
        
        return fallback_result


# 🔥 老王修复：添加模块级别的process函数别名，确保可以被getattr找到
process_function = process

# 🔥 老王修复：确保process函数在模块的__all__中（如果有的话）
if '__all__' in globals():
    __all__.append('process')
else:
    __all__ = ['process', 'get_instance', 'IntentRecognizer']