"""
趋势感知模块 - 阶段二实施
实现热搜数据的智能分析、趋势识别、热点检测和趋势预测
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import re
import jieba
import numpy as np

# 🔥 老王修复：sklearn可选依赖处理
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.cluster import KMeans
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    # 创建回退类
    class TfidfVectorizer:
        def __init__(self, *args, **kwargs):
            self.max_features = kwargs.get('max_features', 1000)
            self.stop_words = kwargs.get('stop_words', [])
        
        def fit_transform(self, texts):
            # 简单的词频统计回退方案
            return [[1.0] * len(texts)]
    
    class KMeans:
        def __init__(self, *args, **kwargs):
            self.n_clusters = kwargs.get('n_clusters', 8)
            self.random_state = kwargs.get('random_state', 42)
        
        def fit_predict(self, X):
            # 简单的聚类回退方案：随机分配
            import random
            random.seed(self.random_state)
            n_samples = len(X) if hasattr(X, '__len__') else 10
            return [random.randint(0, self.n_clusters-1) for _ in range(n_samples)]
    
    def cosine_similarity(X, Y=None):
        # 简单的相似度计算回退方案
        if Y is None:
            Y = X
        return [[0.5] * len(Y) for _ in range(len(X))]

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TrendData:
    """趋势数据结构"""
    topic: str
    platform: str
    score: float
    timestamp: datetime
    category: str
    keywords: List[str]
    heat_level: str
    growth_rate: float
    related_topics: List[str]
    sentiment: str
    influence_score: float
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result

@dataclass
class TrendCluster:
    """趋势聚类结构"""
    cluster_id: str
    topics: List[str]
    center_topic: str
    category: str
    total_score: float
    platforms: List[str]
    keywords: List[str]
    created_at: datetime
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        result = asdict(self)
        result['created_at'] = self.created_at.isoformat()
        return result

@dataclass
class TrendInsight:
    """趋势洞察结构"""
    insight_type: str  # emerging, declining, stable, viral
    topic: str
    description: str
    confidence: float
    evidence: List[str]
    prediction: str
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result

class TrendPerceptionEngine:
    """趋势感知引擎"""
    
    def __init__(self, config_path: str = "config/hot_topics_sources.json"):
        self.config_path = config_path
        self.config = self._load_config()
        
        # 趋势数据存储
        self.trend_history = deque(maxlen=10000)  # 保存最近10000条趋势数据
        self.trend_clusters = {}  # 趋势聚类
        self.trend_insights = deque(maxlen=1000)  # 趋势洞察
        
        # 分析配置
        self.analysis_config = {
            "keyword_extraction": {
                "min_length": 2,
                "max_keywords": 10,
                "stop_words": self._load_stop_words()
            },
            "clustering": {
                "n_clusters": 8,
                "similarity_threshold": 0.7
            },
            "trend_detection": {
                "growth_threshold": 0.3,
                "decline_threshold": -0.2,
                "viral_threshold": 0.8,
                "time_window": 3600  # 1小时
            },
            "sentiment_analysis": {
                "positive_words": ["好", "棒", "赞", "优秀", "喜欢", "支持"],
                "negative_words": ["差", "烂", "垃圾", "讨厌", "反对", "失望"]
            }
        }
        
        # TF-IDF向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=self.analysis_config["keyword_extraction"]["stop_words"]
        )
        
        # 聚类器
        self.kmeans = KMeans(
            n_clusters=self.analysis_config["clustering"]["n_clusters"],
            random_state=42
        )
        
        logger.info("趋势感知引擎初始化完成")
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _load_stop_words(self) -> List[str]:
        """加载停用词"""
        return [
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个",
            "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好",
            "自己", "这", "那", "什么", "可以", "为", "但是", "因为", "所以", "如果", "这样",
            "还", "用", "第", "样", "想", "又", "当", "从", "他", "她", "它", "我们", "你们",
            "他们", "这个", "那个", "这些", "那些", "怎么", "为什么", "多少", "哪里", "什么时候"
        ]
    
    async def analyze_hot_topics(self, hot_topics_data: List[Dict]) -> List[TrendData]:
        """分析热搜数据，提取趋势信息"""
        try:
            trend_data_list = []
            
            for topic_data in hot_topics_data:
                # 提取基本信息
                topic = topic_data.get('title', '').strip()
                platform = topic_data.get('platform', 'unknown')
                score = float(topic_data.get('score', 0))
                
                if not topic:
                    continue
                
                # 关键词提取
                keywords = self._extract_keywords(topic)
                
                # 分类识别
                category = self._classify_topic(topic, keywords)
                
                # 热度等级
                heat_level = self._calculate_heat_level(score)
                
                # 增长率计算
                growth_rate = self._calculate_growth_rate(topic, platform, score)
                
                # 相关话题
                related_topics = self._find_related_topics(topic, keywords)
                
                # 情感分析
                sentiment = self._analyze_sentiment(topic)
                
                # 影响力评分
                influence_score = self._calculate_influence_score(
                    score, platform, category, keywords
                )
                
                # 创建趋势数据
                trend_data = TrendData(
                    topic=topic,
                    platform=platform,
                    score=score,
                    timestamp=datetime.now(),
                    category=category,
                    keywords=keywords,
                    heat_level=heat_level,
                    growth_rate=growth_rate,
                    related_topics=related_topics,
                    sentiment=sentiment,
                    influence_score=influence_score
                )
                
                trend_data_list.append(trend_data)
            
            # 保存到历史记录
            self.trend_history.extend(trend_data_list)
            
            logger.info(f"分析完成，处理了 {len(trend_data_list)} 个趋势数据")
            return trend_data_list
            
        except Exception as e:
            logger.error(f"分析热搜数据失败: {e}")
            return []
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        try:
            # 使用jieba分词
            words = jieba.cut(text)
            
            # 过滤停用词和短词
            keywords = []
            stop_words = self.analysis_config["keyword_extraction"]["stop_words"]
            min_length = self.analysis_config["keyword_extraction"]["min_length"]
            
            for word in words:
                word = word.strip()
                if (len(word) >= min_length and 
                    word not in stop_words and 
                    not word.isdigit()):
                    keywords.append(word)
            
            # 限制关键词数量
            max_keywords = self.analysis_config["keyword_extraction"]["max_keywords"]
            return keywords[:max_keywords]
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
    
    def _classify_topic(self, topic: str, keywords: List[str]) -> str:
        """话题分类"""
        try:
            # 定义分类关键词
            categories = {
                "科技": ["AI", "人工智能", "技术", "科技", "互联网", "手机", "电脑", "软件", "硬件", "数码"],
                "娱乐": ["明星", "电影", "电视剧", "综艺", "音乐", "娱乐", "演员", "导演", "歌手"],
                "体育": ["比赛", "运动", "足球", "篮球", "奥运", "世界杯", "体育", "运动员", "球员"],
                "财经": ["股票", "经济", "金融", "投资", "市场", "公司", "企业", "IPO", "财报"],
                "社会": ["新闻", "事件", "社会", "民生", "政策", "法律", "教育", "医疗", "环境"],
                "生活": ["美食", "旅游", "健康", "时尚", "购物", "生活", "家居", "汽车", "宠物"],
                "游戏": ["游戏", "电竞", "手游", "网游", "主机", "Steam", "游戏机", "玩家"],
                "其他": []
            }
            
            # 计算每个分类的匹配分数
            category_scores = {}
            text_lower = topic.lower()
            keywords_lower = [kw.lower() for kw in keywords]
            
            for category, category_keywords in categories.items():
                if category == "其他":
                    continue
                    
                score = 0
                for kw in category_keywords:
                    if kw.lower() in text_lower:
                        score += 2
                    for topic_kw in keywords_lower:
                        if kw.lower() in topic_kw:
                            score += 1
                
                if score > 0:
                    category_scores[category] = score
            
            # 返回得分最高的分类
            if category_scores:
                return max(category_scores.items(), key=lambda x: x[1])[0]
            else:
                return "其他"
                
        except Exception as e:
            logger.error(f"话题分类失败: {e}")
            return "其他"
    
    def _calculate_heat_level(self, score: float) -> str:
        """计算热度等级"""
        try:
            if score >= 100000:
                return "火爆"
            elif score >= 50000:
                return "热门"
            elif score >= 10000:
                return "普通"
            else:
                return "冷门"
        except:
            return "未知"
    
    def _calculate_growth_rate(self, topic: str, platform: str, current_score: float) -> float:
        """计算增长率"""
        try:
            # 查找历史数据
            historical_scores = []
            current_time = datetime.now()
            time_window = timedelta(hours=1)
            
            for trend_data in reversed(self.trend_history):
                if (trend_data.topic == topic and 
                    trend_data.platform == platform and
                    current_time - trend_data.timestamp <= time_window):
                    historical_scores.append(trend_data.score)
            
            if len(historical_scores) < 2:
                return 0.0
            
            # 计算平均历史分数
            avg_historical = sum(historical_scores) / len(historical_scores)
            
            if avg_historical == 0:
                return 0.0
            
            # 计算增长率
            growth_rate = (current_score - avg_historical) / avg_historical
            return round(growth_rate, 3)
            
        except Exception as e:
            logger.error(f"计算增长率失败: {e}")
            return 0.0
    
    def _find_related_topics(self, topic: str, keywords: List[str]) -> List[str]:
        """查找相关话题"""
        try:
            related_topics = []
            current_time = datetime.now()
            time_window = timedelta(hours=6)
            
            # 从历史数据中查找相关话题
            for trend_data in reversed(self.trend_history):
                if (current_time - trend_data.timestamp <= time_window and
                    trend_data.topic != topic):
                    
                    # 计算关键词相似度
                    common_keywords = set(keywords) & set(trend_data.keywords)
                    if len(common_keywords) >= 2:
                        related_topics.append(trend_data.topic)
            
            # 去重并限制数量
            related_topics = list(set(related_topics))[:5]
            return related_topics
            
        except Exception as e:
            logger.error(f"查找相关话题失败: {e}")
            return []
    
    def _analyze_sentiment(self, text: str) -> str:
        """情感分析"""
        try:
            positive_words = self.analysis_config["sentiment_analysis"]["positive_words"]
            negative_words = self.analysis_config["sentiment_analysis"]["negative_words"]
            
            positive_count = sum(1 for word in positive_words if word in text)
            negative_count = sum(1 for word in negative_words if word in text)
            
            if positive_count > negative_count:
                return "积极"
            elif negative_count > positive_count:
                return "消极"
            else:
                return "中性"
                
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return "中性"
    
    def _calculate_influence_score(self, score: float, platform: str, 
                                 category: str, keywords: List[str]) -> float:
        """计算影响力评分"""
        try:
            # 基础分数
            base_score = min(score / 100000, 1.0)  # 归一化到0-1
            
            # 平台权重
            platform_weights = {
                "微博": 1.0, "知乎": 0.9, "百度": 0.8, "头条": 0.8,
                "抖音": 0.9, "B站": 0.8, "小红书": 0.7, "豆瓣": 0.6
            }
            platform_weight = platform_weights.get(platform, 0.5)
            
            # 分类权重
            category_weights = {
                "社会": 1.0, "科技": 0.9, "财经": 0.8, "娱乐": 0.7,
                "体育": 0.6, "生活": 0.5, "游戏": 0.4, "其他": 0.3
            }
            category_weight = category_weights.get(category, 0.3)
            
            # 关键词权重（关键词越多，影响力越大）
            keyword_weight = min(len(keywords) / 10, 1.0)
            
            # 综合计算
            influence_score = (base_score * 0.4 + 
                             platform_weight * 0.3 + 
                             category_weight * 0.2 + 
                             keyword_weight * 0.1)
            
            return round(influence_score, 3)
            
        except Exception as e:
            logger.error(f"计算影响力评分失败: {e}")
            return 0.0
    
    async def detect_trends(self) -> List[TrendInsight]:
        """检测趋势洞察"""
        try:
            insights = []
            current_time = datetime.now()
            
            # 检测新兴趋势
            emerging_insights = self._detect_emerging_trends()
            insights.extend(emerging_insights)
            
            # 检测衰落趋势
            declining_insights = self._detect_declining_trends()
            insights.extend(declining_insights)
            
            # 检测病毒式传播
            viral_insights = self._detect_viral_trends()
            insights.extend(viral_insights)
            
            # 保存洞察
            self.trend_insights.extend(insights)
            
            logger.info(f"检测到 {len(insights)} 个趋势洞察")
            return insights
            
        except Exception as e:
            logger.error(f"趋势检测失败: {e}")
            return []
    
    def _detect_emerging_trends(self) -> List[TrendInsight]:
        """检测新兴趋势"""
        try:
            insights = []
            current_time = datetime.now()
            time_window = timedelta(hours=2)
            growth_threshold = self.analysis_config["trend_detection"]["growth_threshold"]
            
            # 统计话题增长率
            topic_growth = defaultdict(list)
            
            for trend_data in reversed(self.trend_history):
                if current_time - trend_data.timestamp <= time_window:
                    if trend_data.growth_rate > growth_threshold:
                        topic_growth[trend_data.topic].append(trend_data)
            
            # 识别新兴趋势
            for topic, data_list in topic_growth.items():
                if len(data_list) >= 3:  # 至少3个数据点
                    avg_growth = sum(d.growth_rate for d in data_list) / len(data_list)
                    max_score = max(d.score for d in data_list)
                    
                    insight = TrendInsight(
                        insight_type="emerging",
                        topic=topic,
                        description=f"话题'{topic}'正在快速兴起，平均增长率{avg_growth:.1%}",
                        confidence=min(avg_growth * 2, 1.0),
                        evidence=[f"增长率: {avg_growth:.1%}", f"最高热度: {max_score}"],
                        prediction="预计将在未来2-4小时内达到热度峰值",
                        timestamp=current_time
                    )
                    insights.append(insight)
            
            return insights[:5]  # 限制数量
            
        except Exception as e:
            logger.error(f"检测新兴趋势失败: {e}")
            return []
    
    def _detect_declining_trends(self) -> List[TrendInsight]:
        """检测衰落趋势"""
        try:
            insights = []
            current_time = datetime.now()
            time_window = timedelta(hours=4)
            decline_threshold = self.analysis_config["trend_detection"]["decline_threshold"]
            
            # 统计话题衰落率
            topic_decline = defaultdict(list)
            
            for trend_data in reversed(self.trend_history):
                if current_time - trend_data.timestamp <= time_window:
                    if trend_data.growth_rate < decline_threshold:
                        topic_decline[trend_data.topic].append(trend_data)
            
            # 识别衰落趋势
            for topic, data_list in topic_decline.items():
                if len(data_list) >= 3:  # 至少3个数据点
                    avg_decline = sum(d.growth_rate for d in data_list) / len(data_list)
                    
                    insight = TrendInsight(
                        insight_type="declining",
                        topic=topic,
                        description=f"话题'{topic}'热度正在快速下降，平均衰落率{abs(avg_decline):.1%}",
                        confidence=min(abs(avg_decline) * 2, 1.0),
                        evidence=[f"衰落率: {avg_decline:.1%}"],
                        prediction="预计将在未来1-2小时内退出热搜",
                        timestamp=current_time
                    )
                    insights.append(insight)
            
            return insights[:3]  # 限制数量
            
        except Exception as e:
            logger.error(f"检测衰落趋势失败: {e}")
            return []
    
    def _detect_viral_trends(self) -> List[TrendInsight]:
        """检测病毒式传播趋势"""
        try:
            insights = []
            current_time = datetime.now()
            time_window = timedelta(hours=1)
            viral_threshold = self.analysis_config["trend_detection"]["viral_threshold"]
            
            # 统计跨平台传播
            topic_platforms = defaultdict(set)
            topic_scores = defaultdict(list)
            
            for trend_data in reversed(self.trend_history):
                if current_time - trend_data.timestamp <= time_window:
                    topic_platforms[trend_data.topic].add(trend_data.platform)
                    topic_scores[trend_data.topic].append(trend_data.score)
            
            # 识别病毒式传播
            for topic, platforms in topic_platforms.items():
                if len(platforms) >= 4:  # 至少4个平台
                    scores = topic_scores[topic]
                    avg_score = sum(scores) / len(scores)
                    max_score = max(scores)
                    
                    if avg_score > 50000:  # 高热度
                        insight = TrendInsight(
                            insight_type="viral",
                            topic=topic,
                            description=f"话题'{topic}'正在病毒式传播，已覆盖{len(platforms)}个平台",
                            confidence=min(len(platforms) / 10, 1.0),
                            evidence=[
                                f"覆盖平台: {len(platforms)}个",
                                f"平均热度: {avg_score:.0f}",
                                f"最高热度: {max_score:.0f}"
                            ],
                            prediction="预计将成为全网热点，持续时间6-12小时",
                            timestamp=current_time
                        )
                        insights.append(insight)
            
            return insights[:2]  # 限制数量
            
        except Exception as e:
            logger.error(f"检测病毒式传播失败: {e}")
            return []
    
    async def cluster_trends(self, trend_data_list: List[TrendData]) -> List[TrendCluster]:
        """趋势聚类分析"""
        try:
            if len(trend_data_list) < 3:
                return []
            
            # 准备文本数据
            texts = [f"{td.topic} {' '.join(td.keywords)}" for td in trend_data_list]
            
            # TF-IDF向量化
            tfidf_matrix = self.vectorizer.fit_transform(texts)
            
            # K-means聚类
            cluster_labels = self.kmeans.fit_predict(tfidf_matrix)
            
            # 构建聚类结果
            clusters = defaultdict(list)
            for i, label in enumerate(cluster_labels):
                clusters[label].append(trend_data_list[i])
            
            # 生成聚类对象
            trend_clusters = []
            for cluster_id, cluster_trends in clusters.items():
                if len(cluster_trends) < 2:  # 忽略单个元素的聚类
                    continue
                
                # 找到中心话题（得分最高的）
                center_trend = max(cluster_trends, key=lambda x: x.score)
                
                # 聚合信息
                all_keywords = []
                all_platforms = set()
                total_score = 0
                
                for trend in cluster_trends:
                    all_keywords.extend(trend.keywords)
                    all_platforms.add(trend.platform)
                    total_score += trend.score
                
                # 去重关键词并排序
                keyword_freq = defaultdict(int)
                for kw in all_keywords:
                    keyword_freq[kw] += 1
                
                top_keywords = sorted(keyword_freq.items(), 
                                    key=lambda x: x[1], reverse=True)[:10]
                top_keywords = [kw[0] for kw in top_keywords]
                
                # 创建聚类对象
                trend_cluster = TrendCluster(
                    cluster_id=f"cluster_{cluster_id}_{int(time.time())}",
                    topics=[t.topic for t in cluster_trends],
                    center_topic=center_trend.topic,
                    category=center_trend.category,
                    total_score=total_score,
                    platforms=list(all_platforms),
                    keywords=top_keywords,
                    created_at=datetime.now()
                )
                
                trend_clusters.append(trend_cluster)
            
            # 保存聚类结果
            for cluster in trend_clusters:
                self.trend_clusters[cluster.cluster_id] = cluster
            
            logger.info(f"生成了 {len(trend_clusters)} 个趋势聚类")
            return trend_clusters
            
        except Exception as e:
            logger.error(f"趋势聚类失败: {e}")
            return []
    
    def get_trend_summary(self, hours: int = 24) -> Dict:
        """获取趋势摘要"""
        try:
            current_time = datetime.now()
            time_window = timedelta(hours=hours)
            
            # 筛选时间范围内的数据
            recent_trends = [
                td for td in self.trend_history
                if current_time - td.timestamp <= time_window
            ]
            
            if not recent_trends:
                return {"message": "暂无趋势数据"}
            
            # 统计分析
            total_trends = len(recent_trends)
            categories = defaultdict(int)
            platforms = defaultdict(int)
            sentiments = defaultdict(int)
            heat_levels = defaultdict(int)
            
            top_topics = []
            total_influence = 0
            
            for trend in recent_trends:
                categories[trend.category] += 1
                platforms[trend.platform] += 1
                sentiments[trend.sentiment] += 1
                heat_levels[trend.heat_level] += 1
                total_influence += trend.influence_score
                
                top_topics.append({
                    "topic": trend.topic,
                    "score": trend.score,
                    "platform": trend.platform,
                    "category": trend.category
                })
            
            # 排序并取前10
            top_topics.sort(key=lambda x: x["score"], reverse=True)
            top_topics = top_topics[:10]
            
            # 生成摘要
            summary = {
                "time_range": f"最近{hours}小时",
                "total_trends": total_trends,
                "avg_influence": round(total_influence / total_trends, 3) if total_trends > 0 else 0,
                "categories": dict(sorted(categories.items(), key=lambda x: x[1], reverse=True)),
                "platforms": dict(sorted(platforms.items(), key=lambda x: x[1], reverse=True)),
                "sentiments": dict(sentiments),
                "heat_levels": dict(heat_levels),
                "top_topics": top_topics,
                "insights_count": len([i for i in self.trend_insights 
                                    if current_time - i.timestamp <= time_window]),
                "clusters_count": len([c for c in self.trend_clusters.values() 
                                     if current_time - c.created_at <= time_window])
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"生成趋势摘要失败: {e}")
            return {"error": str(e)}
    
    def get_recent_insights(self, limit: int = 10) -> List[Dict]:
        """获取最近的趋势洞察"""
        try:
            recent_insights = list(self.trend_insights)[-limit:]
            return [insight.to_dict() for insight in reversed(recent_insights)]
        except Exception as e:
            logger.error(f"获取趋势洞察失败: {e}")
            return []
    
    def get_trend_clusters(self, limit: int = 5) -> List[Dict]:
        """获取趋势聚类"""
        try:
            clusters = list(self.trend_clusters.values())
            clusters.sort(key=lambda x: x.total_score, reverse=True)
            return [cluster.to_dict() for cluster in clusters[:limit]]
        except Exception as e:
            logger.error(f"获取趋势聚类失败: {e}")
            return []

def get_trend_perception_engine() -> TrendPerceptionEngine:
    """获取趋势感知引擎实例"""
    if not hasattr(get_trend_perception_engine, '_instance'):
        get_trend_perception_engine._instance = TrendPerceptionEngine()
    return get_trend_perception_engine._instance

def get_instance() -> TrendPerceptionEngine:
    """标准的单例工厂方法"""
    return get_trend_perception_engine()

async def analyze_trends(hot_topics_data: List[Dict]) -> Dict:
    """分析趋势数据的异步接口"""
    try:
        engine = get_trend_perception_engine()
        
        # 分析热搜数据
        trend_data_list = await engine.analyze_hot_topics(hot_topics_data)
        
        # 检测趋势洞察
        insights = await engine.detect_trends()
        
        # 生成聚类
        clusters = await engine.cluster_trends(trend_data_list)
        
        # 生成摘要
        summary = engine.get_trend_summary(hours=6)
        
        return {
            "success": True,
            "trend_count": len(trend_data_list),
            "insights_count": len(insights),
            "clusters_count": len(clusters),
            "summary": summary,
            "recent_insights": engine.get_recent_insights(5),
            "top_clusters": engine.get_trend_clusters(3)
        }
        
    except Exception as e:
        logger.error(f"趋势分析失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "trend_count": 0,
            "insights_count": 0,
            "clusters_count": 0
        }

if __name__ == "__main__":
    # 测试代码
    async def test_trend_perception():
        # 模拟热搜数据
        test_data = [
            {"title": "人工智能最新突破", "platform": "微博", "score": 85000},
            {"title": "AI技术发展趋势", "platform": "知乎", "score": 65000},
            {"title": "机器学习应用案例", "platform": "头条", "score": 45000},
            {"title": "明星最新动态", "platform": "微博", "score": 120000},
            {"title": "电影票房排行", "platform": "豆瓣", "score": 35000}
        ]
        
        result = await analyze_trends(test_data)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 运行测试
    asyncio.run(test_trend_perception())