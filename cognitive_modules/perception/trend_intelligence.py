"""
智能分析模块 - 阶段三实施
基于趋势感知的深度智能分析、用户兴趣匹配和个性化推荐
"""

import asyncio
import json
import logging
import time
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import numpy as np

# 🔥 老王修复：sklearn可选依赖处理
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    # 创建回退类
    class TfidfVectorizer:
        def __init__(self, *args, **kwargs):
            self.max_features = kwargs.get('max_features', 2000)
            self.ngram_range = kwargs.get('ngram_range', (1, 3))
            self.stop_words = kwargs.get('stop_words', [])
        
        def fit_transform(self, texts):
            # 简单的词频统计回退方案
            return [[1.0] * len(texts)]
    
    def cosine_similarity(X, Y=None):
        # 简单的相似度计算回退方案
        if Y is None:
            Y = X
        return [[0.5] * len(Y) for _ in range(len(X))]

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class UserProfile:
    """用户画像数据结构"""
    user_id: str
    interests: List[str]                    # 兴趣标签
    preferred_categories: Dict[str, float]  # 偏好分类权重
    interaction_history: List[Dict]         # 交互历史
    personality_traits: Dict[str, float]    # 性格特征
    activity_patterns: Dict[str, Any]       # 活动模式
    last_updated: datetime
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        result = asdict(self)
        result['last_updated'] = self.last_updated.isoformat()
        return result

@dataclass
class IntelligentInsight:
    """智能洞察数据结构"""
    insight_id: str
    insight_type: str                       # 洞察类型
    title: str                             # 洞察标题
    content: str                           # 洞察内容
    relevance_score: float                 # 相关性评分
    confidence: float                      # 置信度
    evidence: List[str]                    # 支撑证据
    recommendations: List[str]             # 推荐行动
    target_users: List[str]                # 目标用户
    created_at: datetime
    expires_at: Optional[datetime]
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        result = asdict(self)
        result['created_at'] = self.created_at.isoformat()
        if self.expires_at:
            result['expires_at'] = self.expires_at.isoformat()
        return result

@dataclass
class PersonalizedRecommendation:
    """个性化推荐数据结构"""
    recommendation_id: str
    user_id: str
    topic: str                             # 推荐话题
    reason: str                            # 推荐理由
    relevance_score: float                 # 相关性评分
    trend_data: Dict[str, Any]             # 关联趋势数据
    interaction_suggestions: List[str]      # 交互建议
    created_at: datetime
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        result = asdict(self)
        result['created_at'] = self.created_at.isoformat()
        return result

class TrendIntelligenceEngine:
    """智能分析引擎"""
    
    def __init__(self, config_path: str = "config/trend_perception_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        
        # 数据存储
        self.user_profiles = {}                    # 用户画像
        self.intelligent_insights = []             # 智能洞察
        self.recommendations = defaultdict(list)   # 个性化推荐
        
        # 分析配置
        self.analysis_config = {
            "user_profiling": {
                "interest_decay_days": 30,
                "min_interaction_count": 3,
                "profile_update_threshold": 0.1
            },
            "insight_generation": {
                "min_confidence": 0.6,
                "max_insights_per_hour": 10,
                "insight_expiry_hours": 24
            },
            "recommendation": {
                "max_recommendations_per_user": 5,
                "min_relevance_score": 0.5,
                "personalization_weight": 0.7
            },
            "nlp_analysis": {
                "entity_extraction": True,
                "sentiment_analysis": True,
                "topic_modeling": True,
                "semantic_similarity": True
            }
        }
        
        # NLP工具
        self.vectorizer = TfidfVectorizer(
            max_features=2000,
            ngram_range=(1, 3),
            stop_words=self._get_stop_words()
        )
        
        # 趋势感知引擎引用
        self.trend_engine = None
        self._initialize_trend_engine()
        
        logger.info("智能分析引擎初始化完成")
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _get_stop_words(self) -> List[str]:
        """获取停用词列表"""
        return [
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个",
            "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好",
            "自己", "这", "那", "什么", "可以", "为", "但是", "因为", "所以", "如果", "这样"
        ]
    
    def _initialize_trend_engine(self):
        """初始化趋势感知引擎引用"""
        try:
            from cognitive_modules.perception.trend_perception import get_trend_perception_engine
            self.trend_engine = get_trend_perception_engine()
            logger.info("趋势感知引擎引用初始化完成")
        except Exception as e:
            logger.error(f"初始化趋势感知引擎引用失败: {e}")
            self.trend_engine = None
    
    async def analyze_user_interests(self, user_id: str, interaction_data: List[Dict]) -> UserProfile:
        """分析用户兴趣和构建用户画像"""
        try:
            # 获取或创建用户画像
            if user_id in self.user_profiles:
                profile = self.user_profiles[user_id]
            else:
                profile = UserProfile(
                    user_id=user_id,
                    interests=[],
                    preferred_categories={},
                    interaction_history=[],
                    personality_traits={},
                    activity_patterns={},
                    last_updated=datetime.now()
                )
            
            # 更新交互历史
            profile.interaction_history.extend(interaction_data)
            
            # 保留最近的交互记录
            max_history = 1000
            if len(profile.interaction_history) > max_history:
                profile.interaction_history = profile.interaction_history[-max_history:]
            
            # 分析兴趣标签
            interests = self._extract_user_interests(profile.interaction_history)
            profile.interests = interests
            
            # 分析分类偏好
            category_preferences = self._analyze_category_preferences(profile.interaction_history)
            profile.preferred_categories = category_preferences
            
            # 分析性格特征
            personality = self._analyze_personality_traits(profile.interaction_history)
            profile.personality_traits = personality
            
            # 分析活动模式
            activity_patterns = self._analyze_activity_patterns(profile.interaction_history)
            profile.activity_patterns = activity_patterns
            
            # 更新时间戳
            profile.last_updated = datetime.now()
            
            # 保存用户画像
            self.user_profiles[user_id] = profile
            
            logger.info(f"用户 {user_id} 画像分析完成，兴趣标签: {len(interests)} 个")
            return profile
            
        except Exception as e:
            logger.error(f"分析用户兴趣失败: {e}")
            return None
    
    def _extract_user_interests(self, interaction_history: List[Dict]) -> List[str]:
        """提取用户兴趣标签"""
        try:
            interest_counter = Counter()
            
            # 从交互历史中提取关键词
            for interaction in interaction_history:
                content = interaction.get('content', '')
                keywords = interaction.get('keywords', [])
                category = interaction.get('category', '')
                
                # 统计关键词频率
                for keyword in keywords:
                    if len(keyword) >= 2:
                        interest_counter[keyword] += 1
                
                # 统计分类
                if category:
                    interest_counter[category] += 2
                
                # 从内容中提取关键词
                if content:
                    # 简单的关键词提取
                    words = re.findall(r'\b\w{2,}\b', content)
                    for word in words:
                        if word not in self._get_stop_words():
                            interest_counter[word] += 0.5
            
            # 获取最常见的兴趣标签
            top_interests = [item[0] for item in interest_counter.most_common(20)]
            return top_interests
            
        except Exception as e:
            logger.error(f"提取用户兴趣失败: {e}")
            return []
    
    def _analyze_category_preferences(self, interaction_history: List[Dict]) -> Dict[str, float]:
        """分析分类偏好"""
        try:
            category_counter = Counter()
            total_interactions = len(interaction_history)
            
            if total_interactions == 0:
                return {}
            
            # 统计各分类的交互次数
            for interaction in interaction_history:
                category = interaction.get('category', '其他')
                engagement_score = interaction.get('engagement_score', 1.0)
                category_counter[category] += engagement_score
            
            # 计算偏好权重
            preferences = {}
            for category, count in category_counter.items():
                preferences[category] = count / total_interactions
            
            return preferences
            
        except Exception as e:
            logger.error(f"分析分类偏好失败: {e}")
            return {}
    
    def _analyze_personality_traits(self, interaction_history: List[Dict]) -> Dict[str, float]:
        """分析性格特征"""
        try:
            traits = {
                "openness": 0.5,        # 开放性
                "conscientiousness": 0.5, # 尽责性
                "extraversion": 0.5,    # 外向性
                "agreeableness": 0.5,   # 宜人性
                "neuroticism": 0.5      # 神经质
            }
            
            if not interaction_history:
                return traits
            
            # 基于交互模式分析性格特征
            total_interactions = len(interaction_history)
            
            # 分析开放性：对新话题的接受度
            new_topics = set()
            for interaction in interaction_history:
                category = interaction.get('category', '')
                if category:
                    new_topics.add(category)
            
            if total_interactions > 0:
                traits["openness"] = min(len(new_topics) / 10, 1.0)
            
            # 分析外向性：交互频率和社交话题偏好
            social_categories = ["娱乐", "体育", "社会"]
            social_interactions = sum(1 for i in interaction_history 
                                    if i.get('category') in social_categories)
            
            if total_interactions > 0:
                traits["extraversion"] = social_interactions / total_interactions
            
            # 分析尽责性：对严肃话题的关注
            serious_categories = ["科技", "财经", "教育", "社会"]
            serious_interactions = sum(1 for i in interaction_history 
                                     if i.get('category') in serious_categories)
            
            if total_interactions > 0:
                traits["conscientiousness"] = serious_interactions / total_interactions
            
            return traits
            
        except Exception as e:
            logger.error(f"分析性格特征失败: {e}")
            return {"openness": 0.5, "conscientiousness": 0.5, "extraversion": 0.5,
                   "agreeableness": 0.5, "neuroticism": 0.5}
    
    def _analyze_activity_patterns(self, interaction_history: List[Dict]) -> Dict[str, Any]:
        """分析活动模式"""
        try:
            patterns = {
                "active_hours": [],      # 活跃时段
                "peak_days": [],         # 活跃天数
                "interaction_frequency": 0.0,  # 交互频率
                "average_session_length": 0.0,  # 平均会话长度
                "preferred_content_length": "medium"  # 偏好内容长度
            }
            
            if not interaction_history:
                return patterns
            
            # 分析活跃时段
            hour_counter = Counter()
            day_counter = Counter()
            
            for interaction in interaction_history:
                timestamp = interaction.get('timestamp')
                if timestamp:
                    try:
                        if isinstance(timestamp, str):
                            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        else:
                            dt = timestamp
                        
                        hour_counter[dt.hour] += 1
                        day_counter[dt.weekday()] += 1
                    except:
                        continue
            
            # 获取最活跃的时段
            if hour_counter:
                top_hours = [hour for hour, _ in hour_counter.most_common(8)]
                patterns["active_hours"] = sorted(top_hours)
            
            # 获取最活跃的天数
            if day_counter:
                patterns["peak_days"] = [day for day, _ in day_counter.most_common(3)]
            
            # 计算交互频率（每天平均交互次数）
            if interaction_history:
                time_span_days = max((datetime.now() - 
                                    datetime.fromisoformat(interaction_history[0].get('timestamp', 
                                    datetime.now().isoformat()).replace('Z', '+00:00'))).days, 1)
                patterns["interaction_frequency"] = len(interaction_history) / time_span_days
            
            return patterns
            
        except Exception as e:
            logger.error(f"分析活动模式失败: {e}")
            return {"active_hours": [], "peak_days": [], "interaction_frequency": 0.0,
                   "average_session_length": 0.0, "preferred_content_length": "medium"}
    
    async def generate_intelligent_insights(self, trend_data_list: List[Dict]) -> List[IntelligentInsight]:
        """生成智能洞察"""
        try:
            insights = []
            current_time = datetime.now()
            
            # 生成不同类型的洞察
            
            # 1. 趋势关联洞察
            correlation_insights = await self._generate_trend_correlation_insights(trend_data_list)
            insights.extend(correlation_insights)
            
            # 2. 用户兴趣匹配洞察
            interest_insights = await self._generate_interest_matching_insights(trend_data_list)
            insights.extend(interest_insights)
            
            # 3. 跨领域影响洞察
            cross_domain_insights = await self._generate_cross_domain_insights(trend_data_list)
            insights.extend(cross_domain_insights)
            
            # 4. 时间模式洞察
            temporal_insights = await self._generate_temporal_insights(trend_data_list)
            insights.extend(temporal_insights)
            
            # 过滤和排序洞察
            filtered_insights = self._filter_and_rank_insights(insights)
            
            # 保存洞察
            self.intelligent_insights.extend(filtered_insights)
            
            logger.info(f"生成智能洞察完成: {len(filtered_insights)} 个洞察")
            return filtered_insights
            
        except Exception as e:
            logger.error(f"生成智能洞察失败: {e}")
            return []
    
    async def _generate_trend_correlation_insights(self, trend_data_list: List[Dict]) -> List[IntelligentInsight]:
        """生成趋势关联洞察"""
        try:
            insights = []
            
            # 分析趋势之间的关联性
            if len(trend_data_list) < 2:
                return insights
            
            # 按分类分组
            category_groups = defaultdict(list)
            for trend in trend_data_list:
                category = trend.get('category', '其他')
                category_groups[category].append(trend)
            
            # 分析分类间的关联
            for category, trends in category_groups.items():
                if len(trends) >= 3:  # 至少3个相关趋势
                    # 计算平均热度和增长率
                    avg_score = sum(t.get('score', 0) for t in trends) / len(trends)
                    avg_growth = sum(t.get('growth_rate', 0) for t in trends) / len(trends)
                    
                    if avg_growth > 0.2:  # 显著增长
                        insight = IntelligentInsight(
                            insight_id=f"correlation_{category}_{int(time.time())}",
                            insight_type="trend_correlation",
                            title=f"{category}领域热度集体上升",
                            content=f"{category}领域出现{len(trends)}个相关热点，平均增长率{avg_growth:.1%}，表明该领域正受到广泛关注。",
                            relevance_score=min(avg_growth * 2, 1.0),
                            confidence=0.8,
                            evidence=[f"相关话题数量: {len(trends)}", f"平均热度: {avg_score:.0f}", f"平均增长率: {avg_growth:.1%}"],
                            recommendations=[f"关注{category}领域的后续发展", "准备相关内容和观点", "考虑参与相关讨论"],
                            target_users=[],
                            created_at=datetime.now(),
                            expires_at=datetime.now() + timedelta(hours=12)
                        )
                        insights.append(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"生成趋势关联洞察失败: {e}")
            return []
    
    async def _generate_interest_matching_insights(self, trend_data_list: List[Dict]) -> List[IntelligentInsight]:
        """生成用户兴趣匹配洞察"""
        try:
            insights = []
            
            # 为每个用户生成个性化洞察
            for user_id, profile in self.user_profiles.items():
                user_insights = []
                
                # 匹配用户兴趣的趋势
                for trend in trend_data_list:
                    relevance_score = self._calculate_user_trend_relevance(profile, trend)
                    
                    if relevance_score > 0.7:  # 高相关性
                        insight = IntelligentInsight(
                            insight_id=f"interest_{user_id}_{trend.get('topic', 'unknown')}_{int(time.time())}",
                            insight_type="interest_matching",
                            title=f"发现符合您兴趣的热点：{trend.get('topic', '未知话题')}",
                            content=f"基于您的兴趣偏好分析，这个话题与您的关注点高度匹配（匹配度{relevance_score:.1%}）。",
                            relevance_score=relevance_score,
                            confidence=0.75,
                            evidence=[f"兴趣匹配度: {relevance_score:.1%}", f"话题分类: {trend.get('category', '未知')}"],
                            recommendations=["查看详细内容", "参与相关讨论", "关注后续发展"],
                            target_users=[user_id],
                            created_at=datetime.now(),
                            expires_at=datetime.now() + timedelta(hours=6)
                        )
                        user_insights.append(insight)
                
                # 限制每个用户的洞察数量
                user_insights.sort(key=lambda x: x.relevance_score, reverse=True)
                insights.extend(user_insights[:3])
            
            return insights
            
        except Exception as e:
            logger.error(f"生成用户兴趣匹配洞察失败: {e}")
            return []
    
    async def _generate_cross_domain_insights(self, trend_data_list: List[Dict]) -> List[IntelligentInsight]:
        """生成跨领域影响洞察"""
        try:
            insights = []
            
            # 分析不同领域间的影响关系
            cross_domain_patterns = {
                ("科技", "财经"): "技术创新对金融市场的影响",
                ("科技", "社会"): "技术发展对社会生活的改变",
                ("财经", "社会"): "经济动态对社会民生的影响",
                ("娱乐", "社会"): "娱乐事件的社会影响力",
                ("体育", "社会"): "体育赛事的社会关注度"
            }
            
            # 统计各领域的热度
            category_stats = defaultdict(list)
            for trend in trend_data_list:
                category = trend.get('category', '其他')
                score = trend.get('score', 0)
                category_stats[category].append(score)
            
            # 分析跨领域关联
            for (domain1, domain2), description in cross_domain_patterns.items():
                if domain1 in category_stats and domain2 in category_stats:
                    avg_score1 = sum(category_stats[domain1]) / len(category_stats[domain1])
                    avg_score2 = sum(category_stats[domain2]) / len(category_stats[domain2])
                    
                    if avg_score1 > 50000 and avg_score2 > 50000:  # 两个领域都很热
                        insight = IntelligentInsight(
                            insight_id=f"cross_domain_{domain1}_{domain2}_{int(time.time())}",
                            insight_type="cross_domain",
                            title=f"{domain1}与{domain2}领域同时活跃",
                            content=f"当前{domain1}和{domain2}领域都出现高热度话题，可能存在{description}的关联性。",
                            relevance_score=min((avg_score1 + avg_score2) / 200000, 1.0),
                            confidence=0.65,
                            evidence=[f"{domain1}平均热度: {avg_score1:.0f}", f"{domain2}平均热度: {avg_score2:.0f}"],
                            recommendations=["关注两个领域的交叉点", "分析潜在的影响关系", "准备跨领域的观点"],
                            target_users=[],
                            created_at=datetime.now(),
                            expires_at=datetime.now() + timedelta(hours=8)
                        )
                        insights.append(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"生成跨领域影响洞察失败: {e}")
            return []
    
    async def _generate_temporal_insights(self, trend_data_list: List[Dict]) -> List[IntelligentInsight]:
        """生成时间模式洞察"""
        try:
            insights = []
            current_hour = datetime.now().hour
            
            # 分析当前时段的趋势特点
            time_patterns = {
                (6, 9): "早晨资讯时段",
                (12, 14): "午间休闲时段", 
                (18, 21): "晚间娱乐时段",
                (21, 23): "深度阅读时段"
            }
            
            current_pattern = None
            for (start, end), pattern_name in time_patterns.items():
                if start <= current_hour < end:
                    current_pattern = pattern_name
                    break
            
            if current_pattern and trend_data_list:
                # 分析当前时段的热点特征
                high_score_trends = [t for t in trend_data_list if t.get('score', 0) > 80000]
                
                if high_score_trends:
                    categories = [t.get('category', '其他') for t in high_score_trends]
                    most_common_category = Counter(categories).most_common(1)[0][0]
                    
                    insight = IntelligentInsight(
                        insight_id=f"temporal_{current_hour}_{int(time.time())}",
                        insight_type="temporal_pattern",
                        title=f"{current_pattern}的热点特征",
                        content=f"在{current_pattern}，{most_common_category}类话题表现突出，共有{len(high_score_trends)}个高热度话题。",
                        relevance_score=0.6,
                        confidence=0.7,
                        evidence=[f"时间段: {current_pattern}", f"高热话题数: {len(high_score_trends)}", f"主要分类: {most_common_category}"],
                        recommendations=["关注时段特征", "调整内容策略", "优化互动时机"],
                        target_users=[],
                        created_at=datetime.now(),
                        expires_at=datetime.now() + timedelta(hours=2)
                    )
                    insights.append(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"生成时间模式洞察失败: {e}")
            return []
    
    def _calculate_user_trend_relevance(self, user_profile: UserProfile, trend: Dict) -> float:
        """计算用户与趋势的相关性"""
        try:
            relevance_score = 0.0
            
            # 兴趣标签匹配
            trend_keywords = trend.get('keywords', [])
            user_interests = user_profile.interests
            
            if trend_keywords and user_interests:
                common_interests = set(trend_keywords) & set(user_interests)
                interest_score = len(common_interests) / len(set(trend_keywords) | set(user_interests))
                relevance_score += interest_score * 0.4
            
            # 分类偏好匹配
            trend_category = trend.get('category', '其他')
            category_preference = user_profile.preferred_categories.get(trend_category, 0)
            relevance_score += category_preference * 0.3
            
            # 情感倾向匹配
            trend_sentiment = trend.get('sentiment', '中性')
            # 简化处理：假设用户偏好积极内容
            if trend_sentiment == '积极':
                relevance_score += 0.1
            elif trend_sentiment == '中性':
                relevance_score += 0.05
            
            # 热度权重
            trend_score = trend.get('score', 0)
            if trend_score > 100000:
                relevance_score += 0.1
            elif trend_score > 50000:
                relevance_score += 0.05
            
            # 个性化调整
            personality = user_profile.personality_traits
            if personality.get('openness', 0.5) > 0.7:  # 开放性高的用户
                relevance_score += 0.05
            
            return min(relevance_score, 1.0)
            
        except Exception as e:
            logger.error(f"计算用户趋势相关性失败: {e}")
            return 0.0
    
    def _filter_and_rank_insights(self, insights: List[IntelligentInsight]) -> List[IntelligentInsight]:
        """过滤和排序洞察"""
        try:
            # 过滤低质量洞察
            min_confidence = self.analysis_config["insight_generation"]["min_confidence"]
            filtered_insights = [i for i in insights if i.confidence >= min_confidence]
            
            # 按相关性和置信度排序
            filtered_insights.sort(key=lambda x: (x.relevance_score * x.confidence), reverse=True)
            
            # 限制数量
            max_insights = self.analysis_config["insight_generation"]["max_insights_per_hour"]
            return filtered_insights[:max_insights]
            
        except Exception as e:
            logger.error(f"过滤和排序洞察失败: {e}")
            return insights
    
    async def generate_personalized_recommendations(self, user_id: str, trend_data_list: List[Dict]) -> List[PersonalizedRecommendation]:
        """生成个性化推荐"""
        try:
            recommendations = []
            
            # 获取用户画像
            if user_id not in self.user_profiles:
                logger.warning(f"用户 {user_id} 画像不存在，无法生成推荐")
                return recommendations
            
            user_profile = self.user_profiles[user_id]
            
            # 为用户匹配相关趋势
            relevant_trends = []
            for trend in trend_data_list:
                relevance_score = self._calculate_user_trend_relevance(user_profile, trend)
                if relevance_score >= self.analysis_config["recommendation"]["min_relevance_score"]:
                    relevant_trends.append((trend, relevance_score))
            
            # 按相关性排序
            relevant_trends.sort(key=lambda x: x[1], reverse=True)
            
            # 生成推荐
            max_recommendations = self.analysis_config["recommendation"]["max_recommendations_per_user"]
            for trend, relevance_score in relevant_trends[:max_recommendations]:
                recommendation = PersonalizedRecommendation(
                    recommendation_id=f"rec_{user_id}_{trend.get('topic', 'unknown')}_{int(time.time())}",
                    user_id=user_id,
                    topic=trend.get('topic', '未知话题'),
                    reason=self._generate_recommendation_reason(user_profile, trend, relevance_score),
                    relevance_score=relevance_score,
                    trend_data=trend,
                    interaction_suggestions=self._generate_interaction_suggestions(trend),
                    created_at=datetime.now()
                )
                recommendations.append(recommendation)
            
            # 保存推荐
            self.recommendations[user_id].extend(recommendations)
            
            # 限制用户推荐历史长度
            if len(self.recommendations[user_id]) > 50:
                self.recommendations[user_id] = self.recommendations[user_id][-50:]
            
            logger.info(f"为用户 {user_id} 生成 {len(recommendations)} 个个性化推荐")
            return recommendations
            
        except Exception as e:
            logger.error(f"生成个性化推荐失败: {e}")
            return []
    
    def _generate_recommendation_reason(self, user_profile: UserProfile, trend: Dict, relevance_score: float) -> str:
        """生成推荐理由"""
        try:
            reasons = []
            
            # 兴趣匹配理由
            trend_keywords = trend.get('keywords', [])
            user_interests = user_profile.interests
            common_interests = set(trend_keywords) & set(user_interests)
            
            if common_interests:
                reasons.append(f"与您的兴趣 {', '.join(list(common_interests)[:3])} 相关")
            
            # 分类偏好理由
            trend_category = trend.get('category', '其他')
            if trend_category in user_profile.preferred_categories:
                preference_score = user_profile.preferred_categories[trend_category]
                if preference_score > 0.3:
                    reasons.append(f"属于您关注的{trend_category}领域")
            
            # 热度理由
            trend_score = trend.get('score', 0)
            if trend_score > 100000:
                reasons.append("当前热度极高")
            elif trend_score > 50000:
                reasons.append("热度持续上升")
            
            # 个性化理由
            personality = user_profile.personality_traits
            if personality.get('openness', 0.5) > 0.7:
                reasons.append("符合您的探索精神")
            
            if not reasons:
                reasons.append(f"匹配度{relevance_score:.1%}")
            
            return "，".join(reasons)
            
        except Exception as e:
            logger.error(f"生成推荐理由失败: {e}")
            return f"相关性评分: {relevance_score:.1%}"
    
    def _generate_interaction_suggestions(self, trend: Dict) -> List[str]:
        """生成交互建议"""
        try:
            suggestions = []
            
            trend_category = trend.get('category', '其他')
            trend_sentiment = trend.get('sentiment', '中性')
            
            # 基于分类的建议
            category_suggestions = {
                "科技": ["了解技术细节", "关注发展趋势", "思考应用场景"],
                "娱乐": ["查看相关内容", "参与讨论", "关注后续动态"],
                "财经": ["分析市场影响", "关注相关股票", "了解政策背景"],
                "社会": ["关注事件进展", "了解多方观点", "思考社会影响"],
                "体育": ["查看比赛结果", "关注运动员表现", "参与话题讨论"],
                "生活": ["获取实用建议", "分享个人经验", "寻找相关资源"]
            }
            
            if trend_category in category_suggestions:
                suggestions.extend(category_suggestions[trend_category])
            
            # 基于情感的建议
            if trend_sentiment == '积极':
                suggestions.append("分享正能量")
            elif trend_sentiment == '消极':
                suggestions.append("理性分析问题")
            
            # 通用建议
            suggestions.extend(["保存感兴趣的内容", "与朋友讨论"])
            
            return suggestions[:5]  # 限制建议数量
            
        except Exception as e:
            logger.error(f"生成交互建议失败: {e}")
            return ["查看详细内容", "关注相关话题"]
    
    def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """获取用户画像"""
        return self.user_profiles.get(user_id)
    
    def get_recent_insights(self, limit: int = 10) -> List[Dict]:
        """获取最近的智能洞察"""
        try:
            # 过滤未过期的洞察
            current_time = datetime.now()
            valid_insights = [
                insight for insight in self.intelligent_insights
                if not insight.expires_at or insight.expires_at > current_time
            ]
            
            # 按创建时间排序
            valid_insights.sort(key=lambda x: x.created_at, reverse=True)
            
            return [insight.to_dict() for insight in valid_insights[:limit]]
            
        except Exception as e:
            logger.error(f"获取智能洞察失败: {e}")
            return []
    
    def get_user_recommendations(self, user_id: str, limit: int = 5) -> List[Dict]:
        """获取用户的个性化推荐"""
        try:
            user_recommendations = self.recommendations.get(user_id, [])
            
            # 按创建时间排序
            user_recommendations.sort(key=lambda x: x.created_at, reverse=True)
            
            return [rec.to_dict() for rec in user_recommendations[:limit]]
            
        except Exception as e:
            logger.error(f"获取用户推荐失败: {e}")
            return []
    
    def get_analysis_summary(self) -> Dict:
        """获取分析摘要"""
        try:
            current_time = datetime.now()
            
            # 统计有效洞察
            valid_insights = [
                insight for insight in self.intelligent_insights
                if not insight.expires_at or insight.expires_at > current_time
            ]
            
            # 统计洞察类型
            insight_types = Counter(insight.insight_type for insight in valid_insights)
            
            # 统计用户数量
            total_users = len(self.user_profiles)
            
            # 统计推荐数量
            total_recommendations = sum(len(recs) for recs in self.recommendations.values())
            
            return {
                "total_users": total_users,
                "total_insights": len(valid_insights),
                "insight_types": dict(insight_types),
                "total_recommendations": total_recommendations,
                "active_users": len([uid for uid, profile in self.user_profiles.items() 
                                   if (current_time - profile.last_updated).days < 7]),
                "last_updated": current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取分析摘要失败: {e}")
            return {"error": str(e)}

# 全局实例
_trend_intelligence_engine = None

def get_trend_intelligence_engine() -> TrendIntelligenceEngine:
    """获取智能分析引擎实例（单例模式）"""
    global _trend_intelligence_engine
    if _trend_intelligence_engine is None:
        _trend_intelligence_engine = TrendIntelligenceEngine()
    return _trend_intelligence_engine

def get_instance() -> TrendIntelligenceEngine:
    """标准的单例工厂方法"""
    return get_trend_intelligence_engine()

# 异步接口函数
async def analyze_user_behavior(user_id: str, interaction_data: List[Dict]) -> Dict:
    """分析用户行为的异步接口"""
    try:
        engine = get_trend_intelligence_engine()
        profile = await engine.analyze_user_interests(user_id, interaction_data)
        
        if profile:
            return {
                "success": True,
                "user_id": user_id,
                "interests_count": len(profile.interests),
                "categories_count": len(profile.preferred_categories),
                "profile": profile.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "用户行为分析失败",
                "user_id": user_id
            }
            
    except Exception as e:
        logger.error(f"用户行为分析失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "user_id": user_id
        }

async def generate_insights_and_recommendations(user_id: str, trend_data_list: List[Dict]) -> Dict:
    """生成洞察和推荐的异步接口"""
    try:
        engine = get_trend_intelligence_engine()
        
        # 生成智能洞察
        insights = await engine.generate_intelligent_insights(trend_data_list)
        
        # 生成个性化推荐
        recommendations = await engine.generate_personalized_recommendations(user_id, trend_data_list)
        
        return {
            "success": True,
            "insights_count": len(insights),
            "recommendations_count": len(recommendations),
            "insights": [insight.to_dict() for insight in insights[:5]],
            "recommendations": [rec.to_dict() for rec in recommendations],
            "summary": engine.get_analysis_summary()
        }
        
    except Exception as e:
        logger.error(f"生成洞察和推荐失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "insights_count": 0,
            "recommendations_count": 0
        }

if __name__ == "__main__":
    # 测试代码
    async def test_intelligence_engine():
        # 模拟用户交互数据
        test_interaction_data = [
            {
                "content": "人工智能技术发展很快",
                "keywords": ["人工智能", "技术", "发展"],
                "category": "科技",
                "engagement_score": 1.5,
                "timestamp": datetime.now().isoformat()
            },
            {
                "content": "新电影很好看",
                "keywords": ["电影", "娱乐"],
                "category": "娱乐", 
                "engagement_score": 1.0,
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        # 模拟趋势数据
        test_trend_data = [
            {
                "topic": "AI技术突破",
                "category": "科技",
                "score": 85000,
                "keywords": ["AI", "技术", "突破"],
                "sentiment": "积极",
                "growth_rate": 0.5
            },
            {
                "topic": "热门电影上映",
                "category": "娱乐",
                "score": 65000,
                "keywords": ["电影", "上映", "票房"],
                "sentiment": "积极",
                "growth_rate": 0.3
            }
        ]
        
        # 测试用户行为分析
        user_result = await analyze_user_behavior("test_user", test_interaction_data)
        print("用户行为分析结果:", json.dumps(user_result, ensure_ascii=False, indent=2))
        
        # 测试洞察和推荐生成
        insights_result = await generate_insights_and_recommendations("test_user", test_trend_data)
        print("洞察和推荐结果:", json.dumps(insights_result, ensure_ascii=False, indent=2))
    
    # 运行测试
    asyncio.run(test_intelligence_engine())