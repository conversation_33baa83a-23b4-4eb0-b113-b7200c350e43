#!/usr/bin/env python3
"""
用户模型构建器模块 - User Model Builder

该模块负责主动分析用户交互历史，构建和完善用户模型，包括：
1. 交互历史分析 - 分析用户的历史交互记录
2. 特征提取 - 从历史交互中提取用户特征
3. 偏好推断 - 推断用户偏好和兴趣
4. 行为模式识别 - 识别用户的行为模式
5. 模型更新 - 更新和完善用户模型

作者: Claude
创建日期: 2024-07-31
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random
import datetime
import threading
from typing import Dict, Any, List, Optional, Tuple, Set, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
perception_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(perception_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from cognitive_modules.perception.user_model import get_instance as get_user_model

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.perception.user_model_builder")

class UserModelBuilder(CognitiveModuleBase):
    """
    用户模型构建器模块
    
    主动分析用户交互历史，构建和完善用户模型。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化用户模型构建器模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "user_model_builder", "perception", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取用户模型模块
        self.user_model = get_user_model()
        
        # 分析间隔
        self.analysis_interval = 3600  # 默认1小时
        if config and "analysis_interval" in config:
            self.analysis_interval = config["analysis_interval"]
        
        # 最后分析时间
        self.last_analysis_time = {}
        
        # 分析队列
        self.analysis_queue = set()
        self.queue_lock = threading.Lock()
        
        # 启动分析线程
        self.running = True
        self.builder_thread = threading.Thread(target=self._builder_loop, daemon=True)
        self.builder_thread.start()
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"用户模型构建器模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("user_message", self._on_user_message)
        self.event_bus.subscribe("conversation_ended", self._on_conversation_ended)
        
        logger.debug("已订阅相关事件")
    
    def _on_user_message(self, data: Dict[str, Any]):
        """处理用户消息事件"""
        try:
            user_id = data.get("user_id", "")
            
            if not user_id:
                return
            
            # 将用户添加到分析队列
            with self.queue_lock:
                self.analysis_queue.add(user_id)
        except Exception as e:
            logger.error_status(f"处理用户消息事件失败: {e}")
    
    def _on_conversation_ended(self, data: Dict[str, Any]):
        """处理对话结束事件"""
        try:
            user_id = data.get("user_id", "")
            
            if not user_id:
                return
            
            # 优先分析刚结束对话的用户
            self._analyze_user(user_id)
        except Exception as e:
            logger.error_status(f"处理对话结束事件失败: {e}")
    
    def _builder_loop(self):
        """模型构建循环"""
        while self.running:
            try:
                # 处理分析队列
                users_to_analyze = []
                with self.queue_lock:
                    # 复制队列并清空
                    users_to_analyze = list(self.analysis_queue)
                    self.analysis_queue.clear()
                
                for user_id in users_to_analyze:
                    # 检查是否需要分析
                    last_time = self.last_analysis_time.get(user_id, 0)
                    if time.time() - last_time >= self.analysis_interval:
                        self._analyze_user(user_id)
                
                # 休眠一段时间
                time.sleep(60)
            except Exception as e:
                logger.error_status(f"模型构建循环异常: {e}")
                time.sleep(10)
    
    def _analyze_user(self, user_id: str):
        """分析用户数据并更新模型"""
        try:
            # 获取用户模型
            user_model_data = self.user_model.get_user_model(user_id)
            
            # 获取用户上下文
            user_context = self.life_context.get_user_context(user_id)
            
            # 获取用户对话历史
            dialogue_history = user_context.get("dialogue_history", [])
            
            # 分析用户行为模式
            self._analyze_behavior_patterns(user_id, user_model_data, dialogue_history)
            
            # 分析用户兴趣和偏好
            self._analyze_interests_and_preferences(user_id, user_model_data, dialogue_history)
            
            # 分析用户情感模式
            self._analyze_emotional_patterns(user_id, user_model_data, dialogue_history)
            
            # 更新知识图谱
            self._update_knowledge_graph(user_id, user_model_data, dialogue_history)
            
            # 更新最后分析时间
            self.last_analysis_time[user_id] = time.time()
            
            # 发布模型更新事件
            self.event_bus.publish("user_model_updated", {
                "user_id": user_id,
                "timestamp": time.time()
            })
            
            logger.debug(f"已完成用户 {user_id} 的模型分析")
        except Exception as e:
            logger.error_status(f"分析用户 {user_id} 数据失败: {e}")
    
    def _analyze_behavior_patterns(self, user_id: str, user_model: Dict[str, Any], history: List[Dict[str, Any]]):
        """分析用户行为模式"""
        if not history:
            return
            
        behavior_patterns = user_model["behavior_patterns"]
        
        # 分析活动时间
        activity_times = {}
        for msg in history:
            if "timestamp" in msg:
                hour = datetime.datetime.fromtimestamp(msg["timestamp"]).hour
                activity_times[hour] = activity_times.get(hour, 0) + 1
        
        # 更新活动时间
        if activity_times:
            for hour, count in activity_times.items():
                hour_str = str(hour)
                if hour_str in behavior_patterns["activity_times"]:
                    behavior_patterns["activity_times"][hour_str] += count
                else:
                    behavior_patterns["activity_times"][hour_str] = count
        
        # 分析消息频率
        if len(history) >= 10:
            # 计算平均消息间隔
            timestamps = [msg["timestamp"] for msg in history if "timestamp" in msg]
            timestamps.sort()
            
            if len(timestamps) >= 2:
                intervals = [timestamps[i+1] - timestamps[i] for i in range(len(timestamps)-1)]
                avg_interval = sum(intervals) / len(intervals)
                
                # 根据平均间隔确定消息频率
                if avg_interval < 60:  # 小于1分钟
                    behavior_patterns["message_frequency"] = "high"
                elif avg_interval < 300:  # 小于5分钟
                    behavior_patterns["message_frequency"] = "medium"
                else:
                    behavior_patterns["message_frequency"] = "low"
        
        # 分析对话长度
        user_messages = [msg for msg in history if msg.get("role") == "user"]
        if user_messages:
            avg_length = sum(len(msg.get("content", "")) for msg in user_messages) / len(user_messages)
            
            if avg_length < 10:
                behavior_patterns["conversation_length"] = "short"
            elif avg_length < 50:
                behavior_patterns["conversation_length"] = "medium"
            else:
                behavior_patterns["conversation_length"] = "long"
        
        # 分析主动性水平
        # 简单判断：连续多条用户消息表示较高主动性
        consecutive_user_msgs = 0
        max_consecutive = 0
        
        for msg in history:
            if msg.get("role") == "user":
                consecutive_user_msgs += 1
                max_consecutive = max(max_consecutive, consecutive_user_msgs)
            else:
                consecutive_user_msgs = 0
        
        if max_consecutive >= 3:
            behavior_patterns["initiative_level"] = "high"
        elif max_consecutive >= 2:
            behavior_patterns["initiative_level"] = "medium"
        else:
            behavior_patterns["initiative_level"] = "low"
    
    def _analyze_interests_and_preferences(self, user_id: str, user_model: Dict[str, Any], history: List[Dict[str, Any]]):
        """分析用户兴趣和偏好"""
        if not history:
            return
            
        preferences = user_model["preferences"]
        user_messages = [msg.get("content", "") for msg in history if msg.get("role") == "user"]
        
        if not user_messages:
            return
            
        # 简单的关键词匹配来推断兴趣主题
        topic_keywords = {
            "科技": ["电脑", "手机", "软件", "编程", "AI", "人工智能", "技术", "互联网", "智能", "数字"],
            "娱乐": ["电影", "音乐", "游戏", "综艺", "电视剧", "明星", "娱乐", "爱好", "休闲"],
            "体育": ["运动", "足球", "篮球", "健身", "比赛", "体育", "奥运", "锻炼"],
            "教育": ["学习", "教育", "课程", "学校", "大学", "知识", "培训", "考试", "专业"],
            "美食": ["美食", "菜谱", "餐厅", "食物", "美味", "烹饪", "厨师", "食谱"],
            "旅游": ["旅游", "旅行", "景点", "度假", "旅程", "旅馆", "酒店", "风景"],
            "文学": ["书籍", "阅读", "文学", "诗词", "小说", "作家", "故事", "文化"],
            "商业": ["商业", "创业", "投资", "金融", "经济", "市场", "公司", "股票"],
            "健康": ["健康", "医疗", "医院", "疾病", "保健", "养生", "医生", "药物"],
            "时尚": ["时尚", "服装", "穿搭", "美妆", "潮流", "设计", "品牌", "流行"]
        }
        
        # 分析所有用户消息
        all_text = " ".join(user_messages)
        topics = preferences.get("topics", {})
        
        for topic, keywords in topic_keywords.items():
            for keyword in keywords:
                if keyword in all_text:
                    topics[topic] = topics.get(topic, 0) + 1
        
        # 分析交互风格偏好
        interaction_style_indicators = {
            "简洁": ["简洁", "简短", "直接", "要点", "不要废话"],
            "详细": ["详细", "详尽", "解释", "全面", "完整"],
            "友好": ["友好", "亲切", "温暖", "关怀", "体贴"],
            "专业": ["专业", "正式", "学术", "精确", "精准"],
            "幽默": ["幽默", "有趣", "风趣", "搞笑", "轻松"]
        }
        
        style_counts = {style: 0 for style in interaction_style_indicators}
        
        for msg in user_messages:
            for style, indicators in interaction_style_indicators.items():
                for indicator in indicators:
                    if indicator in msg:
                        style_counts[style] += 1
        
        # 如果有明显偏好，更新交互风格
        if style_counts:
            max_style = max(style_counts.items(), key=lambda x: x[1])
            if max_style[1] > 0:
                preferences["interaction_style"] = max_style[0]
    
    def _analyze_emotional_patterns(self, user_id: str, user_model: Dict[str, Any], history: List[Dict[str, Any]]):
        """分析用户情感模式"""
        if not history:
            return
            
        emotional_profile = user_model["emotional_profile"]
        recent_emotions = emotional_profile.get("recent_emotions", [])
        
        if not recent_emotions:
            return
            
        # 计算情感波动性
        if len(recent_emotions) >= 3:
            emotions = [e["emotion"] for e in recent_emotions]
            unique_emotions = len(set(emotions))
            
            if unique_emotions >= 3:
                emotional_profile["emotional_volatility"] = "high"
            elif unique_emotions == 2:
                emotional_profile["emotional_volatility"] = "medium"
            else:
                emotional_profile["emotional_volatility"] = "low"
        
        # 分析情感触发因素
        triggers = emotional_profile.get("emotional_triggers", {})
        
        for emotion_record in recent_emotions:
            emotion = emotion_record.get("emotion")
            trigger = emotion_record.get("trigger")
            
            if emotion and trigger:
                if emotion not in triggers:
                    triggers[emotion] = []
                
                # 避免重复添加相同的触发因素
                if not any(t["message"] == trigger for t in triggers[emotion]):
                    triggers[emotion].append({
                        "message": trigger,
                        "timestamp": emotion_record.get("timestamp", time.time())
                    })
                    
                    # 限制每种情绪的触发因素数量
                    triggers[emotion] = triggers[emotion][-5:]
    
    def _update_knowledge_graph(self, user_id: str, user_model: Dict[str, Any], history: List[Dict[str, Any]]):
        """更新用户知识图谱"""
        if not history:
            return
            
        knowledge_graph = user_model["knowledge_graph"]
        entities = knowledge_graph.get("entities", {})
        relationships = knowledge_graph.get("relationships", {})
        
        user_messages = [msg.get("content", "") for msg in history if msg.get("role") == "user"]
        
        if not user_messages:
            return
            
        # 简单的实体提取示例（仅作为演示）
        # 实际实现可能需要使用NLP库或调用AI服务
        
        # 人物实体提取
        person_patterns = [
            "我的朋友", "我的家人", "我的同事", "我的老师", "我的学生",
            "我的父母", "我的孩子", "我的伙伴", "我的上司", "我的男朋友", "我的女朋友"
        ]
        
        # 地点实体提取
        place_patterns = [
            "我去过", "我想去", "我喜欢去", "我曾经在", "我现在在", "我住在"
        ]
        
        for msg in user_messages:
            # 提取人物实体
            for pattern in person_patterns:
                if pattern in msg:
                    start_idx = msg.find(pattern) + len(pattern)
                    end_idx = min(start_idx + 20, len(msg))
                    person_text = msg[start_idx:end_idx].split("，")[0].split("。")[0].strip()
                    
                    if person_text and len(person_text) <= 10:
                        if "person" not in entities:
                            entities["person"] = {}
                        
                        relation_type = pattern.replace("我的", "")
                        
                        if person_text not in entities["person"]:
                            entities["person"][person_text] = {
                                "properties": {"relation": relation_type},
                                "last_mentioned": time.time(),
                                "mention_count": 1
                            }
                        else:
                            entities["person"][person_text]["mention_count"] += 1
                            entities["person"][person_text]["last_mentioned"] = time.time()
            
            # 提取地点实体
            for pattern in place_patterns:
                if pattern in msg:
                    start_idx = msg.find(pattern) + len(pattern)
                    end_idx = min(start_idx + 20, len(msg))
                    place_text = msg[start_idx:end_idx].split("，")[0].split("。")[0].strip()
                    
                    if place_text and len(place_text) <= 15:
                        if "place" not in entities:
                            entities["place"] = {}
                        
                        relation_type = pattern.replace("我", "").strip()
                        
                        if place_text not in entities["place"]:
                            entities["place"][place_text] = {
                                "properties": {"activity": relation_type},
                                "last_mentioned": time.time(),
                                "mention_count": 1
                            }
                        else:
                            entities["place"][place_text]["mention_count"] += 1
                            entities["place"][place_text]["last_mentioned"] = time.time()
        
        # 添加一些简单的关系
        for entity_type, entity_dict in entities.items():
            for entity_name, entity_data in entity_dict.items():
                if entity_type == "person":
                    relation = entity_data.get("properties", {}).get("relation", "认识")
                    relationship_id = f"user_{relation}_{entity_name}"
                    
                    if relationship_id not in relationships:
                        relationships[relationship_id] = {
                            "source": "user",
                            "relation": relation,
                            "target": entity_name,
                            "last_mentioned": entity_data["last_mentioned"],
                            "confidence": min(0.5 + 0.1 * entity_data["mention_count"], 0.9)
                        }
    
    def trigger_user_analysis(self, user_id: str) -> bool:
        """
        触发用户分析
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否成功触发
        """
        try:
            with self.queue_lock:
                self.analysis_queue.add(user_id)
            return True
        except Exception as e:
            logger.error_status(f"触发用户分析失败: {e}")
            return False
    
    def force_analyze_user(self, user_id: str) -> bool:
        """
        强制立即分析用户数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            分析是否成功
        """
        try:
            self._analyze_user(user_id)
            return True
        except Exception as e:
            logger.error_status(f"强制分析用户失败: {e}")
            return False
    
    def shutdown(self):
        """关闭模块"""
        self.running = False
        
        if self.builder_thread.is_alive():
            self.builder_thread.join(timeout=1.0)
        
        logger.info(f"用户模型构建器模块 {self.module_id} 已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> UserModelBuilder:
    """
    获取用户模型构建器模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        用户模型构建器模块实例
    """
    return UserModelBuilder(config=config) 