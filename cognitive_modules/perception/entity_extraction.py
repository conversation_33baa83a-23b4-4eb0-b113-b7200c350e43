"""
实体提取模块 - Entity Extraction

该模块负责从用户输入中提取关键实体。
"""

from utilities.unified_logger import get_unified_logger, setup_unified_logging
import re
from typing import Dict, Any, List

logger = get_unified_logger("cognitive_modules.perception.entity_extraction")

# 实体类型
ENTITY_TYPES = [
    "person",       # 人物
    "location",     # 地点
    "time",         # 时间
    "date",         # 日期
    "organization", # 组织
    "product",      # 产品
    "number",       # 数字
    "percentage",   # 百分比
    "money",        # 金额
    "keyword"       # 关键词
]

# 简单的实体模式
ENTITY_PATTERNS = {
    "time": [
        r'\d{1,2}[:：]\d{1,2}',                # 12:30
        r'[早中下晚]午',                        # 上午，下午
        r'[零一二三四五六七八九十]{1,2}点',      # 三点
        r'[零一二三四五六七八九十]{1,2}时'       # 三时
    ],
    "date": [
        r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}',   # 2024-06-09
        r'\d{1,2}[-/月]\d{1,2}',              # 06-09
        r'[昨今明]天',                          # 昨天，今天，明天
        r'[前后]天',                            # 前天，后天
        r'[周星期][一二三四五六日天]'            # 周一，星期二
    ],
    "number": [
        r'\d+',                                # 123
        r'[零一二三四五六七八九十百千万亿]+',     # 一百二十三
    ],
    "percentage": [
        r'\d+%',                               # 50%
        r'\d+\s?percent',                      # 50 percent
        r'百分之[零一二三四五六七八九十]+'        # 百分之五十
    ],
    "money": [
        r'\d+元',                              # 100元
        r'\d+块[钱]?',                         # 100块，100块钱
        r'\d+美元',                            # 100美元
        r'\$\d+',                              # $100
        r'￥\d+'                               # ￥100
    ]
}

# 常见地点关键词
LOCATION_KEYWORDS = [
    "北京", "上海", "广州", "深圳", "杭州", "南京", "成都", "重庆", "武汉", "西安",
    "中国", "美国", "日本", "韩国", "英国", "法国", "德国", "意大利", "俄罗斯", "加拿大",
    "家", "公司", "学校", "医院", "超市", "商场", "公园", "餐厅", "酒店", "机场"
]

# 常见组织关键词
ORG_KEYWORDS = [
    "公司", "集团", "企业", "学校", "大学", "机构", "部门", "协会", "联盟", "银行"
]

def process(context):
    """
    处理函数
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.info("执行实体提取处理")
    
    # 获取输入文本，尝试多种位置
    text = ""
    
    # 方法1: 从input_data中获取
    input_data = context.get("input_data", {})
    if isinstance(input_data, dict):
        text = input_data.get("text", "")
    
    # 方法2: 从shared_data中获取
    if not text and hasattr(context, "shared_data"):
        shared_data = context.shared_data
        if isinstance(shared_data, dict):
            text = shared_data.get("input_text", "")
    
    # 方法3: 从字符串中获取
    if not text and isinstance(context, dict):
        text = context.get("text", "")
        if not text:
            text = context.get("input_text", "")
    
    # 方法4: 直接使用上下文
    if not text and isinstance(context, str):
        text = context
    
    if not text:
        logger.warning_status("输入文本为空")
        return {"entities": [], "count": 0}
    
    # 提取实体
    entities = _extract_entities(text)
    
    result = {
        "entities": entities,
        "count": len(entities),
        "input_text": text
    }
    
    logger.info(f"提取实体 {len(entities)} 个")
    
    return result

def _extract_entities(text):
    """
    从文本中提取实体
    
    Args:
        text: 输入文本
        
    Returns:
        提取的实体列表
    """
    entities = []
    
    # 使用正则表达式提取实体
    for entity_type, patterns in ENTITY_PATTERNS.items():
        for pattern in patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entities.append({
                    "type": entity_type,
                    "value": match.group(),
                    "start": match.start(),
                    "end": match.end()
                })
    
    # 提取地点实体
    for location in LOCATION_KEYWORDS:
        if location in text:
            start = text.find(location)
            entities.append({
                "type": "location",
                "value": location,
                "start": start,
                "end": start + len(location)
            })
    
    # 提取组织实体
    for index, word in enumerate(text):
        if index < len(text) - 1:
            for org_keyword in ORG_KEYWORDS:
                if text[index:].startswith(org_keyword):
                    # 寻找组织名称的起始位置
                    start = index
                    while start > 0 and text[start-1] not in "，。！？；：,.!?;: ":
                        start -= 1
                    
                    if start < index:
                        org_name = text[start:index+len(org_keyword)]
                        entities.append({
                            "type": "organization",
                            "value": org_name,
                            "start": start,
                            "end": index + len(org_keyword)
                        })
    
    # 提取关键词
    words = list(set(text.split()))
    for word in words:
        if len(word) >= 2 and word not in "的了是在我你他她它们我们你们他们":
            start = text.find(word)
            if start >= 0:  # 确保找到了
                entities.append({
                    "type": "keyword",
                    "value": word,
                    "start": start,
                    "end": start + len(word)
                })
    
    # 移除重叠实体，保留更具体的类型
    entities = _remove_overlapping_entities(entities)
    
    return entities

def _remove_overlapping_entities(entities):
    """
    移除重叠的实体，保留更具体的类型
    
    Args:
        entities: 实体列表
        
    Returns:
        处理后的实体列表
    """
    # 实体类型优先级（数字越大优先级越高）
    type_priority = {
        "keyword": 1,
        "number": 2,
        "person": 3,
        "location": 3,
        "organization": 3,
        "time": 4,
        "date": 4,
        "percentage": 5,
        "money": 5
    }
    
    # 按起始位置排序
    sorted_entities = sorted(entities, key=lambda x: (x["start"], -type_priority.get(x["type"], 0)))
    
    # 移除重叠
    result = []
    last_end = -1
    
    for entity in sorted_entities:
        if entity["start"] >= last_end:
            result.append(entity)
            last_end = entity["end"]
    
    return result 