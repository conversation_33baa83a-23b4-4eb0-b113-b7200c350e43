#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境感知模块 - Environmental Perception Module

该模块负责感知和处理环境相关信息，包括：
- 天气状况
- 时间环境
- 地理位置
- 季节变化
- 自然环境因素

作者: 魅魔
版本: 1.0.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import threading

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "..", ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from utilities.unified_logger import get_unified_logger
from core.enhanced_event_bus import get_instance as get_event_bus
from connectors.database.mysql_connector import get_instance as get_mysql_connector

logger = get_unified_logger('environmental_perception')

class EnvironmentalPerception:
    """环境感知模块"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化环境感知模块"""
        self.config = config or {}
        self.event_bus = get_event_bus()
        self.db_connector = get_mysql_connector()
        
        # 感知数据缓存
        self.perception_cache = {
            'weather': None,
            'time_environment': None,
            'seasonal_info': None,
            'location_context': None
        }
        
        # 配置参数
        self.update_interval = self.config.get('update_interval', 300)  # 5分钟
        self.cache_duration = self.config.get('cache_duration', 600)   # 10分钟
        
        # 运行状态
        self.is_active = False
        self.last_update_time = 0
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.success("环境感知模块初始化完成")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("data.weather.updated", self.handle_weather_update)
        self.event_bus.subscribe("system.time_changed", self.handle_time_change)
        self.event_bus.subscribe("perception.environmental.request", self.handle_perception_request)
    
    async def start(self):
        """启动环境感知模块"""
        if self.is_active:
            logger.warning_status("环境感知模块已在运行中")
            return
        
        logger.success("启动环境感知模块...")
        self.is_active = True
        
        # 立即进行一次感知
        await self.update_perception()
        
        # 发布启动事件
        self.event_bus.publish("perception.environmental.started", {
            "timestamp": time.time(),
            "module": "environmental_perception"
        })
        
        logger.success("环境感知模块启动完成")
    
    async def stop(self):
        """停止环境感知模块"""
        if not self.is_active:
            logger.warning_status("环境感知模块未在运行")
            return
        
        logger.info("停止环境感知模块...")
        self.is_active = False
        
        # 发布停止事件
        self.event_bus.publish("perception.environmental.stopped", {
            "timestamp": time.time(),
            "module": "environmental_perception"
        })
        
        logger.info("环境感知模块已停止")
    
    async def update_perception(self):
        """更新环境感知数据"""
        current_time = time.time()
        
        # 检查是否需要更新
        if (current_time - self.last_update_time) < self.update_interval:
            return
        
        try:
            logger.debug("开始更新环境感知数据...")
            
            # 更新天气感知
            await self._update_weather_perception()
            
            # 更新时间环境感知
            await self._update_time_environment()
            
            # 更新季节信息
            await self._update_seasonal_info()
            
            # 更新位置上下文
            await self._update_location_context()
            
            self.last_update_time = current_time
            
            # 发布感知更新事件
            self.event_bus.publish("perception.environmental.updated", {
                "timestamp": current_time,
                "perception_data": self.get_perception_data()
            })
            
            logger.debug("环境感知数据更新完成")
            
        except Exception as e:
            logger.error_status(f"更新环境感知数据时发生错误: {e}")
    
    async def _update_weather_perception(self):
        """更新天气感知"""
        try:
            # 从数据库获取最新天气数据
            success, weather_data, error = self.db_connector.get_latest_weather(3)
            
            if success and weather_data:
                processed_weather = self._process_weather_data(weather_data)
                self.perception_cache['weather'] = processed_weather
                logger.debug("天气感知数据更新完成")
            else:
                logger.warning_status(f"获取天气数据失败: {error}")
                
        except Exception as e:
            logger.error_status(f"更新天气感知时发生错误: {e}")
    
    def _process_weather_data(self, weather_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理天气数据，提取感知信息"""
        if not weather_data:
            return {}
        
        # 取最新的天气数据
        latest_weather = weather_data[0]
        
        # 解析温度
        temperature = self._parse_temperature(latest_weather.get('temperature', '0'))
        
        # 分析天气感知
        weather_perception = {
            'temperature': temperature,
            'temperature_feeling': self._analyze_temperature_feeling(temperature),
            'humidity': self._parse_humidity(latest_weather.get('humidity', '0')),
            'air_quality': self._analyze_air_quality(latest_weather.get('pm25', '0')),
            'wind_condition': {
                'direction': latest_weather.get('wind_direction', ''),
                'power': latest_weather.get('wind_power', ''),
                'comfort_level': self._analyze_wind_comfort(latest_weather.get('wind_power', ''))
            },
            'visibility': latest_weather.get('visibility', ''),
            'weather_mood': self._analyze_weather_mood(temperature, latest_weather.get('pm25', '0')),
            'outdoor_suitability': self._analyze_outdoor_suitability(temperature, latest_weather.get('pm25', '0')),
            'city': latest_weather.get('city_name', '未知'),
            'update_time': latest_weather.get('update_time', datetime.now())
        }
        
        return weather_perception
    
    def _parse_temperature(self, temp_str: str) -> float:
        """解析温度字符串"""
        try:
            # 移除可能的单位符号
            temp_clean = str(temp_str).replace('℃', '').replace('°C', '').replace('°', '').strip()
            return float(temp_clean)
        except:
            return 20.0  # 默认值
    
    def _parse_humidity(self, humidity_str: str) -> float:
        """解析湿度字符串"""
        try:
            humidity_clean = str(humidity_str).replace('%', '').strip()
            return float(humidity_clean)
        except:
            return 50.0  # 默认值
    
    def _analyze_temperature_feeling(self, temperature: float) -> str:
        """分析温度感受"""
        if temperature < 0:
            return "严寒"
        elif temperature < 10:
            return "寒冷"
        elif temperature < 15:
            return "凉爽"
        elif temperature < 25:
            return "舒适"
        elif temperature < 30:
            return "温暖"
        elif temperature < 35:
            return "炎热"
        else:
            return "酷热"
    
    def _analyze_air_quality(self, pm25_str: str) -> Dict[str, Any]:
        """分析空气质量"""
        try:
            pm25 = float(str(pm25_str))
            
            if pm25 <= 35:
                level = "优"
                description = "空气质量令人满意，基本无空气污染"
            elif pm25 <= 75:
                level = "良"
                description = "空气质量可接受，但某些污染物可能对极少数异常敏感人群健康有较弱影响"
            elif pm25 <= 115:
                level = "轻度污染"
                description = "易感人群症状有轻度加剧，健康人群出现刺激症状"
            elif pm25 <= 150:
                level = "中度污染"
                description = "进一步加剧易感人群症状，可能对健康人群心脏、呼吸系统有影响"
            elif pm25 <= 250:
                level = "重度污染"
                description = "心脏病和肺病患者症状显著加剧，运动耐受力降低"
            else:
                level = "严重污染"
                description = "健康人群运动耐受力降低，有明显强烈症状"
            
            return {
                'pm25_value': pm25,
                'level': level,
                'description': description,
                'health_advice': self._get_health_advice(level)
            }
            
        except:
            return {
                'pm25_value': 50,
                'level': '良',
                'description': '空气质量数据不可用',
                'health_advice': '建议正常户外活动'
            }
    
    def _get_health_advice(self, air_level: str) -> str:
        """根据空气质量等级给出健康建议"""
        advice_map = {
            '优': '空气质量令人满意，各类人群可多参加户外活动，多呼吸新鲜空气',
            '良': '空气质量可接受，正常户外活动',
            '轻度污染': '儿童、老年人及心脏病、呼吸系统疾病患者应减少长时间、高强度的户外锻炼',
            '中度污染': '儿童、老年人及心脏病、呼吸系统疾病患者避免长时间、高强度的户外锻炼，一般人群适量减少户外运动',
            '重度污染': '儿童、老年人和患有心脏病、肺病等易感人群应当留在室内，停止户外运动，一般人群减少户外运动',
            '严重污染': '儿童、老年人和病人应当留在室内，避免体力消耗，一般人群应避免户外活动'
        }
        return advice_map.get(air_level, '建议关注空气质量变化')
    
    def _analyze_wind_comfort(self, wind_power: str) -> str:
        """分析风力舒适度"""
        wind_comfort_map = {
            '0级': '无风，非常舒适',
            '1级': '软风，非常舒适',
            '2级': '轻风，舒适',
            '3级': '微风，舒适',
            '4级': '和风，较舒适',
            '5级': '清风，一般',
            '6级': '强风，略感不适',
            '7级': '疾风，不适',
            '8级': '大风，很不适'
        }
        return wind_comfort_map.get(wind_power, '风力条件未知')
    
    def _analyze_weather_mood(self, temperature: float, pm25_str: str) -> str:
        """分析天气对情绪的影响"""
        try:
            pm25 = float(str(pm25_str))
            
            # 综合温度和空气质量分析情绪影响
            if 18 <= temperature <= 26 and pm25 <= 75:
                return "愉悦舒适"
            elif temperature < 5 or temperature > 35:
                return "焦躁不安"
            elif pm25 > 150:
                return "压抑沉闷"
            elif 15 <= temperature <= 30 and pm25 <= 115:
                return "平静放松"
            else:
                return "一般状态"
                
        except:
            return "情绪影响不明"
    
    def _analyze_outdoor_suitability(self, temperature: float, pm25_str: str) -> Dict[str, Any]:
        """分析户外活动适宜性"""
        try:
            pm25 = float(str(pm25_str))
            
            # 基于温度的适宜性
            temp_score = 0
            if 18 <= temperature <= 26:
                temp_score = 100
            elif 15 <= temperature <= 30:
                temp_score = 80
            elif 10 <= temperature <= 35:
                temp_score = 60
            elif 5 <= temperature <= 38:
                temp_score = 40
            else:
                temp_score = 20
            
            # 基于空气质量的适宜性
            air_score = 0
            if pm25 <= 35:
                air_score = 100
            elif pm25 <= 75:
                air_score = 80
            elif pm25 <= 115:
                air_score = 60
            elif pm25 <= 150:
                air_score = 40
            else:
                air_score = 20
            
            # 综合评分
            overall_score = (temp_score + air_score) / 2
            
            if overall_score >= 80:
                suitability = "非常适宜"
                recommendation = "非常适合户外活动，建议多出门走走"
            elif overall_score >= 60:
                suitability = "较适宜"
                recommendation = "适合户外活动，但要注意防护"
            elif overall_score >= 40:
                suitability = "一般"
                recommendation = "可以短时间户外活动，建议做好防护"
            else:
                suitability = "不适宜"
                recommendation = "不建议户外活动，建议室内休息"
            
            return {
                'suitability': suitability,
                'score': overall_score,
                'recommendation': recommendation,
                'temperature_factor': temp_score,
                'air_quality_factor': air_score
            }
            
        except:
            return {
                'suitability': '未知',
                'score': 50,
                'recommendation': '请根据实际情况判断',
                'temperature_factor': 50,
                'air_quality_factor': 50
            }
    
    async def _update_time_environment(self):
        """更新时间环境感知"""
        try:
            now = datetime.now()
            
            time_perception = {
                'current_time': now,
                'hour': now.hour,
                'time_period': self._get_time_period(now.hour),
                'weekday': now.weekday(),
                'weekday_name': self._get_weekday_name(now.weekday()),
                'is_weekend': self._is_weekend(),
                'is_workday': not self._is_weekend(),
                'time_energy': self._analyze_time_energy(now.hour),
                'activity_suggestion': self._suggest_time_activity(now.hour)
            }
            
            self.perception_cache['time_environment'] = time_perception
            logger.debug("时间环境感知数据更新完成")
            
        except Exception as e:
            logger.error_status(f"更新时间环境感知时发生错误: {e}")
    
    def _get_time_period(self, hour: int) -> str:
        """获取时间段描述"""
        if 5 <= hour < 8:
            return "清晨"
        elif 8 <= hour < 12:
            return "上午"
        elif 12 <= hour < 14:
            return "中午"
        elif 14 <= hour < 18:
            return "下午"
        elif 18 <= hour < 21:
            return "傍晚"
        elif 21 <= hour < 24:
            return "晚上"
        else:
            return "深夜"
    
    def _get_weekday_name(self, weekday: int) -> str:
        """获取星期名称"""
        weekday_names = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
        return weekday_names[weekday] if 0 <= weekday < 7 else '未知'
    
    def _analyze_time_energy(self, hour: int) -> str:
        """分析时间对应的能量状态"""
        if 6 <= hour < 10:
            return "精力充沛"
        elif 10 <= hour < 14:
            return "活跃状态"
        elif 14 <= hour < 16:
            return "稍显疲惫"
        elif 16 <= hour < 19:
            return "恢复活力"
        elif 19 <= hour < 22:
            return "放松状态"
        else:
            return "低能量期"
    
    def _suggest_time_activity(self, hour: int) -> str:
        """根据时间建议合适的活动"""
        if 6 <= hour < 9:
            return "适合晨练、阅读、规划一天"
        elif 9 <= hour < 12:
            return "适合高强度工作、学习"
        elif 12 <= hour < 14:
            return "适合用餐、短暂休息"
        elif 14 <= hour < 17:
            return "适合创造性工作、沟通交流"
        elif 17 <= hour < 19:
            return "适合运动、娱乐活动"
        elif 19 <= hour < 22:
            return "适合社交、家庭时光"
        else:
            return "适合休息、冥想、准备睡眠"
    
    async def _update_seasonal_info(self):
        """更新季节信息"""
        try:
            now = datetime.now()
            month = now.month
            
            seasonal_info = {
                'season': self._get_season(month),
                'season_feeling': self._get_season_feeling(month),
                'seasonal_characteristics': self._get_seasonal_characteristics(month),
                'seasonal_activities': self._get_seasonal_activities(month)
            }
            
            self.perception_cache['seasonal_info'] = seasonal_info
            logger.debug("季节信息感知数据更新完成")
            
        except Exception as e:
            logger.error_status(f"更新季节信息时发生错误: {e}")
    
    def _get_season(self, month: int) -> str:
        """根据月份获取季节"""
        if month in [12, 1, 2]:
            return "冬季"
        elif month in [3, 4, 5]:
            return "春季"
        elif month in [6, 7, 8]:
            return "夏季"
        elif month in [9, 10, 11]:
            return "秋季"
        else:
            return "未知季节"
    
    def _get_season_feeling(self, month: int) -> str:
        """获取季节感受"""
        season_feelings = {
            1: "寒冷萧瑟", 2: "春意萌动", 3: "生机勃勃",
            4: "花香鸟语", 5: "绿意盎然", 6: "热情似火",
            7: "炎炎夏日", 8: "蝉鸣阵阵", 9: "秋高气爽",
            10: "层林尽染", 11: "秋意渐浓", 12: "岁末寒冬"
        }
        return season_feelings.get(month, "季节变换")
    
    def _get_seasonal_characteristics(self, month: int) -> List[str]:
        """获取季节特征"""
        characteristics_map = {
            1: ["严寒", "雪花", "萧瑟", "安静"],
            2: ["回暖", "萌芽", "希望", "活力"],
            3: ["温暖", "花开", "生机", "清新"],
            4: ["舒适", "绿叶", "鸟鸣", "和煦"],
            5: ["温热", "繁花", "青翠", "活跃"],
            6: ["炎热", "夏至", "蝉鸣", "活力"],
            7: ["酷热", "烈日", "游泳", "休闲"],
            8: ["闷热", "暴雨", "蝉声", "慵懒"],
            9: ["凉爽", "丰收", "金黄", "宁静"],
            10: ["清冷", "枫叶", "收获", "思考"],
            11: ["寒冷", "凋零", "肃杀", "收敛"],
            12: ["严寒", "雪景", "团聚", "温暖"]
        }
        return characteristics_map.get(month, ["季节变化"])
    
    def _get_seasonal_activities(self, month: int) -> List[str]:
        """获取季节适宜活动"""
        activities_map = {
            1: ["室内运动", "读书", "泡茶", "火锅"],
            2: ["散步", "踏青", "放风筝", "赏花"],
            3: ["郊游", "植树", "赏花", "春游"],
            4: ["户外运动", "野餐", "登山", "摄影"],
            5: ["旅行", "户外烧烤", "游园", "运动"],
            6: ["游泳", "避暑", "夜市", "啤酒"],
            7: ["海边度假", "避暑山庄", "夜宵", "游泳"],
            8: ["室内活动", "看电影", "喝冷饮", "午休"],
            9: ["登高", "赏菊", "采摘", "户外运动"],
            10: ["赏枫", "登山", "摄影", "品茶"],
            11: ["温泉", "室内运动", "美食", "读书"],
            12: ["滑雪", "温泉", "团聚", "年终总结"]
        }
        return activities_map.get(month, ["适应季节活动"])
    
    async def _update_location_context(self):
        """更新位置上下文"""
        try:
            # 从天气数据中获取位置信息
            weather_data = self.perception_cache.get('weather') or {}
            city = weather_data.get('city', '未知城市') if isinstance(weather_data, dict) else '未知城市'
            
            location_context = {
                'current_city': city,
                'location_characteristics': self._get_city_characteristics(city),
                'local_culture': self._get_local_culture(city),
                'lifestyle_suggestions': self._get_lifestyle_suggestions(city)
            }
            
            self.perception_cache['location_context'] = location_context
            logger.debug("位置上下文感知数据更新完成")
            
        except Exception as e:
            logger.error_status(f"更新位置上下文时发生错误: {e}")
    
    def _get_city_characteristics(self, city: str) -> List[str]:
        """获取城市特征"""
        # 这里可以根据实际城市数据扩展
        city_chars = {
            '北京': ['首都', '文化中心', '历史悠久', '政治中心'],
            '上海': ['经济中心', '国际大都市', '时尚前沿', '金融中心'],
            '深圳': ['科技创新', '年轻活力', '改革开放', '创业热土'],
            '广州': ['商贸中心', '美食之都', '岭南文化', '包容开放']
        }
        return city_chars.get(city, ['现代城市', '发展活力', '地方特色'])
    
    def _get_local_culture(self, city: str) -> str:
        """获取本地文化特色"""
        culture_map = {
            '北京': '京味文化，注重传统礼仪，喜爱京剧、相声等传统艺术',
            '上海': '海派文化，中西融合，精致生活，商业气息浓厚',
            '深圳': '移民文化，创新开放，快节奏生活，科技氛围浓厚',
            '广州': '岭南文化，重商务实，美食文化发达，包容性强'
        }
        return culture_map.get(city, '地方文化特色丰富，具有独特的人文魅力')
    
    def _get_lifestyle_suggestions(self, city: str) -> List[str]:
        """获取生活方式建议"""
        lifestyle_map = {
            '北京': ['品尝老北京小吃', '参观故宫博物院', '听相声看京剧', '胡同散步'],
            '上海': ['外滩观光', '品尝上海菜', '购物体验', '咖啡文化'],
            '深圳': ['科技体验', '创业交流', '海边休闲', '现代购物'],
            '广州': ['品尝粤菜', '珠江夜游', '传统茶楼', '花城观光']
        }
        return lifestyle_map.get(city, ['探索本地特色', '体验地方文化', '品尝当地美食', '参与社区活动'])
    
    def get_perception_data(self) -> Dict[str, Any]:
        """获取完整的环境感知数据"""
        return {
            'weather': self.perception_cache.get('weather', {}),
            'time_environment': self.perception_cache.get('time_environment', {}),
            'seasonal_info': self.perception_cache.get('seasonal_info', {}),
            'location_context': self.perception_cache.get('location_context', {}),
            'last_update': self.last_update_time,
            'is_active': self.is_active
        }
    
    def get_current_environment_summary(self) -> str:
        """获取当前环境状况摘要"""
        try:
            weather = self.perception_cache.get('weather', {})
            time_env = self.perception_cache.get('time_environment', {})
            seasonal = self.perception_cache.get('seasonal_info', {})
            location = self.perception_cache.get('location_context', {})
            
            summary_parts = []
            
            # 时间环境
            if time_env:
                period = time_env.get('time_period', '')
                energy = time_env.get('time_energy', '')
                summary_parts.append(f"现在是{period}，{energy}")
            
            # 天气状况
            if weather:
                temp_feeling = weather.get('temperature_feeling', '')
                weather_mood = weather.get('weather_mood', '')
                city = weather.get('city', '')
                if city and temp_feeling:
                    summary_parts.append(f"{city}天气{temp_feeling}，让人感到{weather_mood}")
            
            # 季节信息
            if seasonal:
                season = seasonal.get('season', '')
                season_feeling = seasonal.get('season_feeling', '')
                if season and season_feeling:
                    summary_parts.append(f"正值{season}，{season_feeling}")
            
            # 空气质量
            if weather and 'air_quality' in weather:
                air_level = weather['air_quality'].get('level', '')
                if air_level:
                    summary_parts.append(f"空气质量{air_level}")
            
            return "，".join(summary_parts) if summary_parts else "环境感知数据暂不可用"
            
        except Exception as e:
            logger.error_status(f"生成环境摘要时发生错误: {e}")
            return "环境感知数据获取异常"
    
    # 事件处理方法
    async def handle_weather_update(self, event_data: Dict[str, Any]):
        """处理天气数据更新事件"""
        logger.debug("收到天气数据更新事件，重新进行天气感知")
        await self._update_weather_perception()
    
    async def handle_time_change(self, event_data: Dict[str, Any]):
        """处理时间变化事件"""
        logger.debug("收到时间变化事件，更新时间环境感知")
        await self._update_time_environment()
    
    async def handle_perception_request(self, event_data: Dict[str, Any]):
        """处理感知请求事件"""
        request_type = event_data.get('type', 'full')
        
        if request_type == 'full':
            await self.update_perception()
        elif request_type == 'weather':
            await self._update_weather_perception()
        elif request_type == 'time':
            await self._update_time_environment()
        elif request_type == 'seasonal':
            await self._update_seasonal_info()
        elif request_type == 'location':
            await self._update_location_context()
        
        # 发布感知响应事件
        self.event_bus.publish("perception.environmental.response", {
            "request_type": request_type,
            "perception_data": self.get_perception_data(),
            "timestamp": time.time()
        })
    
    def _is_weekend(self) -> bool:
        """判断是否为周末/休息日（基于数据库t_calendar表）"""
        try:
            from utilities.workday_service import is_workday
            return not is_workday()
        except Exception as e:
            logger.error_status(f"获取工作日信息失败，使用默认判断: {e}")
            # 降级到默认判断
            return datetime.now().weekday() >= 5


# 单例模式
_environmental_perception_instance = None
_environmental_perception_lock = threading.RLock()

def get_instance(config: Dict[str, Any] = None) -> EnvironmentalPerception:
    """获取环境感知模块单例"""
    global _environmental_perception_instance
    with _environmental_perception_lock:
        if _environmental_perception_instance is None:
            _environmental_perception_instance = EnvironmentalPerception(config)
        return _environmental_perception_instance

# 便捷函数
async def start_environmental_perception(config: Dict[str, Any] = None):
    """启动环境感知模块"""
    perception = get_instance(config)
    await perception.start()
    return perception

def get_environmental_summary() -> str:
    """获取环境感知摘要"""
    perception = get_instance()
    return perception.get_current_environment_summary()

def get_environmental_data() -> Dict[str, Any]:
    """获取环境感知数据"""
    perception = get_instance()
    return perception.get_perception_data() 