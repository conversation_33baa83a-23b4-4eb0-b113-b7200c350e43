#!/usr/bin/env python3
"""
多模态感知模块 - Multimodal Perception

该模块负责感知和处理多种模态的信息，包括：
1. 图像感知 - 识别和分析图像内容
2. 语音感知 - 处理语音信息
3. 视频感知 - 理解视频内容
4. 模态融合 - 整合多模态信息
5. 多模态表达 - 生成多模态内容

作者: Claude
创建日期: 2024-08-01
版本: 1.0
"""

import os
import sys
import json
import time
import base64
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, ByteString

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
perception_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(perception_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from adapters.ai_service_adapter import get_instance as get_ai_service

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.perception.multimodal_perception")

class MultimodalPerception(CognitiveModuleBase):
    """
    多模态感知模块
    
    感知和处理多种模态的信息，实现多模态交互能力。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化多模态感知模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "multimodal_perception", "perception", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取AI服务适配器
        self.ai_service = get_ai_service()
        
        # 支持的模态类型
        self.supported_modalities = {
            "image": self._process_image,
            "audio": self._process_audio,
            "video": self._process_video,
            "text": self._process_text
        }
        
        # 多模态处理配置
        self.modality_config = {
            "image": {
                "max_size": 1024 * 1024 * 5,  # 5MB
                "supported_formats": ["jpg", "jpeg", "png", "webp", "gif"],
                "default_model": "default"  # 使用AI服务的默认视觉模型
            },
            "audio": {
                "max_duration": 60,  # 60秒
                "supported_formats": ["mp3", "wav", "ogg", "m4a"],
                "default_model": "default"  # 使用AI服务的默认语音模型
            },
            "video": {
                "max_duration": 30,  # 30秒
                "supported_formats": ["mp4", "webm", "mov"],
                "default_model": "default"  # 使用AI服务的默认视频模型
            }
        }
        
        # 处理历史
        self.processing_history = []
        
        # 模态处理计数
        self.modality_counts = {modality: 0 for modality in self.supported_modalities}
        
        # 多模态缓存
        self.multimodal_cache = {}
        self.cache_lock = threading.Lock()
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"多模态感知模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("multimodal_input", self._on_multimodal_input)
        self.event_bus.subscribe("image_received", self._on_image_received)
        self.event_bus.subscribe("audio_received", self._on_audio_received)
        self.event_bus.subscribe("video_received", self._on_video_received)
        
        logger.debug("已订阅相关事件")
    
    def _on_multimodal_input(self, data: Dict[str, Any]):
        """处理多模态输入事件"""
        try:
            modality = data.get("modality", "")
            content = data.get("content", "")
            user_id = data.get("user_id", "")
            session_id = data.get("session_id", "")
            
            if not modality or not content or not user_id:
                logger.warning_status("多模态输入数据不完整")
                return
            
            # 处理多模态输入
            if modality in self.supported_modalities:
                result = self.supported_modalities[modality](content, user_id, session_id)
                
                # 更新处理计数
                self.modality_counts[modality] += 1
                
                # 记录处理历史
                self.processing_history.append({
                    "modality": modality,
                    "user_id": user_id,
                    "session_id": session_id,
                    "timestamp": time.time(),
                    "success": result.get("success", False)
                })
                
                # 如果处理成功，发布处理结果事件
                if result.get("success", False):
                    self.event_bus.publish(f"{modality}_processed", {
                        "user_id": user_id,
                        "session_id": session_id,
                        "result": result,
                        "timestamp": time.time()
                    })
        except Exception as e:
            logger.error_status(f"处理多模态输入失败: {e}")
    
    def _on_image_received(self, data: Dict[str, Any]):
        """处理图像接收事件"""
        try:
            image_data = data.get("image_data", "")
            user_id = data.get("user_id", "")
            session_id = data.get("session_id", "")
            
            if not image_data or not user_id:
                logger.warning_status("图像数据不完整")
                return
            
            # 构造多模态输入事件
            multimodal_data = {
                "modality": "image",
                "content": image_data,
                "user_id": user_id,
                "session_id": session_id
            }
            
            # 发布多模态输入事件
            self.event_bus.publish("multimodal_input", multimodal_data)
        except Exception as e:
            logger.error_status(f"处理图像接收事件失败: {e}")
    
    def _on_audio_received(self, data: Dict[str, Any]):
        """处理音频接收事件"""
        try:
            audio_data = data.get("audio_data", "")
            user_id = data.get("user_id", "")
            session_id = data.get("session_id", "")
            
            if not audio_data or not user_id:
                logger.warning_status("音频数据不完整")
                return
            
            # 构造多模态输入事件
            multimodal_data = {
                "modality": "audio",
                "content": audio_data,
                "user_id": user_id,
                "session_id": session_id
            }
            
            # 发布多模态输入事件
            self.event_bus.publish("multimodal_input", multimodal_data)
        except Exception as e:
            logger.error_status(f"处理音频接收事件失败: {e}")
    
    def _on_video_received(self, data: Dict[str, Any]):
        """处理视频接收事件"""
        try:
            video_data = data.get("video_data", "")
            user_id = data.get("user_id", "")
            session_id = data.get("session_id", "")
            
            if not video_data or not user_id:
                logger.warning_status("视频数据不完整")
                return
            
            # 构造多模态输入事件
            multimodal_data = {
                "modality": "video",
                "content": video_data,
                "user_id": user_id,
                "session_id": session_id
            }
            
            # 发布多模态输入事件
            self.event_bus.publish("multimodal_input", multimodal_data)
        except Exception as e:
            logger.error_status(f"处理视频接收事件失败: {e}")
    
    def _process_image(self, image_data: str, user_id: str, session_id: str = "") -> Dict[str, Any]:
        """
        处理图像数据
        
        Args:
            image_data: 图像数据（base64编码或URL）
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        try:
            # 缓存检查
            cache_key = f"img:{user_id}:{hash(image_data) % 1000000:06d}"
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"使用缓存的图像分析结果")
                return cached_result
            
            # 基本图像信息分析提示
            analysis_prompt = "请详细分析这张图片中的内容，包括主体对象、场景、颜色、情绪氛围等，并提供一个全面的描述。"
            
            # 使用新的AI服务适配器分析图像
            result = self.ai_service.analyze_image(
                image_data=image_data,
                prompt=analysis_prompt
            )
            
            if result.get("success", False):
                analysis = result.get("analysis", "")
                
                # 解析分析结果，提取关键信息
                processed_result = {
                    "success": True,
                    "content_type": "image",
                    "description": analysis,
                    "timestamp": time.time(),
                    "user_id": user_id,
                    "session_id": session_id,
                    "model": result.get("model", "")
                }
                
                # 更新用户上下文
                self._update_user_context(user_id, "last_image_analysis", {
                    "timestamp": time.time(),
                    "description": analysis
                })
                
                # 更新缓存
                self._add_to_cache(cache_key, processed_result)
                
                # 发布事件
                self.event_bus.publish("image_analyzed", {
                    "user_id": user_id,
                    "session_id": session_id,
                    "result": processed_result,
                    "timestamp": time.time()
                })
                
                return processed_result
            else:
                logger.error_status(f"图像分析失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "图像分析失败"),
                    "user_id": user_id,
                    "session_id": session_id
                }
        
        except Exception as e:
            logger.error_status(f"处理图像异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id,
                "session_id": session_id
            }
    
    def _process_audio(self, audio_data: str, user_id: str, session_id: str = "") -> Dict[str, Any]:
        """
        处理音频数据
        
        Args:
            audio_data: 音频数据（Base64编码或URL）
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        try:
            # 检查缓存
            cache_key = f"audio_{hash(audio_data)}"
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.debug("使用缓存的音频处理结果")
                return cached_result
            
            # 判断是URL还是Base64数据
            is_url = audio_data.startswith(("http://", "https://"))
            
            # 如果是Base64数据，需要先保存为临时文件
            temp_file = None
            if not is_url:
                import tempfile
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
                temp_file.write(base64.b64decode(audio_data))
                temp_file.close()
                audio_path = temp_file.name
            else:
                audio_path = audio_data
            
            # 调用AI服务转录音频
            response = self.ai_service.get_audio_transcription(audio_path)
            
            # 清理临时文件
            if temp_file:
                os.unlink(temp_file.name)
            
            if not response or "text" not in response:
                return {"success": False, "error": "音频转录失败"}
            
            # 提取转录结果
            transcription_result = {
                "success": True,
                "transcription": response["text"],
                "modality": "audio",
                "timestamp": time.time()
            }
            
            # 添加到缓存
            self._add_to_cache(cache_key, transcription_result)
            
            # 更新用户上下文
            self._update_user_context(user_id, "latest_audio_transcription", {
                "transcription": response["text"],
                "timestamp": time.time()
            })
            
            # 如果是用户消息，发布用户消息事件
            self.event_bus.publish("user_message", {
                "user_id": user_id,
                "message": response["text"],
                "source": "audio",
                "timestamp": time.time()
            })
            
            return transcription_result
        except Exception as e:
            logger.error_status(f"处理音频数据失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _process_video(self, video_data: str, user_id: str, session_id: str = "") -> Dict[str, Any]:
        """
        处理视频数据
        
        Args:
            video_data: 视频数据（base64编码或URL）
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        try:
            # 缓存检查
            cache_key = f"vid:{user_id}:{hash(video_data) % 1000000:06d}"
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"使用缓存的视频分析结果")
                return cached_result
            
            # 视频分析提示
            analysis_prompt = "请详细分析这段视频的内容，包括场景变化、主要活动、对象、动作和情感变化等。提供一个全面的视频描述。"
            
            # 使用新的AI服务适配器分析视频
            result = self.ai_service.analyze_video(
                video_data=video_data,
                prompt=analysis_prompt
            )
            
            if result.get("success", False):
                analysis = result.get("analysis", "")
                
                # 解析分析结果
                processed_result = {
                    "success": True,
                    "content_type": "video",
                    "description": analysis,
                    "timestamp": time.time(),
                    "user_id": user_id,
                    "session_id": session_id,
                    "model": result.get("model", "")
                }
                
                # 更新用户上下文
                self._update_user_context(user_id, "last_video_analysis", {
                    "timestamp": time.time(),
                    "description": analysis
                })
                
                # 更新缓存
                self._add_to_cache(cache_key, processed_result)
                
                # 发布事件
                self.event_bus.publish("video_analyzed", {
                    "user_id": user_id,
                    "session_id": session_id,
                    "result": processed_result,
                    "timestamp": time.time()
                })
                
                return processed_result
            else:
                logger.error_status(f"视频分析失败: {result.get('error', '未知错误')}")
                
                # 如果不支持直接视频分析，尝试提取关键帧进行分析
                if "不支持直接视频分析" in result.get("error", ""):
                    logger.info("尝试通过关键帧分析视频")
                    return self._process_video_by_keyframes(video_data, user_id, session_id)
                
                return {
                    "success": False,
                    "error": result.get("error", "视频分析失败"),
                    "user_id": user_id,
                    "session_id": session_id
                }
        
        except Exception as e:
            logger.error_status(f"处理视频异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id,
                "session_id": session_id
            }
            
    def _process_video_by_keyframes(self, video_data: str, user_id: str, session_id: str = "") -> Dict[str, Any]:
        """
        通过关键帧分析视频（备选方案）
        
        Args:
            video_data: 视频数据
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        try:
            # TODO: 实现视频关键帧提取和分析
            # 1. 提取关键帧
            # 2. 分析每个关键帧
            # 3. 整合分析结果
            
            # 暂时返回不支持的消息
            return {
                "success": False,
                "error": "视频关键帧分析功能尚未实现",
                "user_id": user_id,
                "session_id": session_id
            }
        except Exception as e:
            logger.error_status(f"通过关键帧分析视频失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id,
                "session_id": session_id
            }
    
    def _process_text(self, text_data: str, user_id: str, session_id: str = "") -> Dict[str, Any]:
        """
        处理文本数据（作为多模态处理的一部分）
        
        Args:
            text_data: 文本数据
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        try:
            # 提取文本特征
            return {
                "success": True,
                "text": text_data,
                "modality": "text",
                "length": len(text_data),
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error_status(f"处理文本数据失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _get_from_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """从缓存中获取数据"""
        with self.cache_lock:
            if key in self.multimodal_cache:
                # 检查缓存是否过期（默认1小时）
                cache_item = self.multimodal_cache[key]
                if time.time() - cache_item["timestamp"] < 3600:
                    return cache_item["data"]
                else:
                    # 移除过期缓存
                    del self.multimodal_cache[key]
        return None
    
    def _add_to_cache(self, key: str, data: Dict[str, Any]):
        """添加数据到缓存"""
        with self.cache_lock:
            self.multimodal_cache[key] = {
                "data": data,
                "timestamp": time.time()
            }
            
            # 限制缓存大小
            if len(self.multimodal_cache) > 100:
                # 删除最旧的缓存项
                oldest_key = min(self.multimodal_cache.items(), key=lambda x: x[1]["timestamp"])[0]
                del self.multimodal_cache[oldest_key]
    
    def _update_user_context(self, user_id: str, key: str, value: Any):
        """更新用户上下文"""
        try:
            # 获取用户上下文
            user_context = self.life_context.get_user_context(user_id)
            
            # 更新多模态数据
            multimodal_data = user_context.get("multimodal_data", {})
            multimodal_data[key] = value
            
            # 更新上下文
            self.life_context.update_user_context(user_id, "multimodal_data", multimodal_data)
        except Exception as e:
            logger.error_status(f"更新用户上下文失败: {e}")
    
    def process_multimodal_input(self, modality: str, content: str, user_id: str, session_id: str = "") -> Dict[str, Any]:
        """
        处理多模态输入
        
        Args:
            modality: 模态类型（image, audio, video, text）
            content: 内容数据（Base64编码或URL）
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        try:
            if modality not in self.supported_modalities:
                return {"success": False, "error": f"不支持的模态类型: {modality}"}
            
            # 处理输入
            result = self.supported_modalities[modality](content, user_id, session_id)
            
            # 更新处理计数
            self.modality_counts[modality] += 1
            
            # 记录处理历史
            self.processing_history.append({
                "modality": modality,
                "user_id": user_id,
                "session_id": session_id,
                "timestamp": time.time(),
                "success": result.get("success", False)
            })
            
            # 如果处理成功，发布处理结果事件
            if result.get("success", False):
                self.event_bus.publish(f"{modality}_processed", {
                    "user_id": user_id,
                    "session_id": session_id,
                    "result": result,
                    "timestamp": time.time()
                })
            
            return result
        except Exception as e:
            logger.error_status(f"处理多模态输入失败: {e}")
            return {"success": False, "error": str(e)}
    
    def generate_multimodal_content(self, content_type: str, prompt: str, user_id: str, additional_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成多模态内容
        
        Args:
            content_type: 内容类型，如"image", "audio", "video"
            prompt: 生成提示
            user_id: 用户ID
            additional_params: 额外参数
            
        Returns:
            生成结果
        """
        additional_params = additional_params or {}
        
        try:
            if not prompt:
                return {"success": False, "error": "提示不能为空"}
                
            if content_type == "image":
                return self._generate_image(prompt, user_id, additional_params)
                
            elif content_type == "audio":
                return self._generate_audio(prompt, user_id, additional_params)
                
            elif content_type == "video":
                return self._generate_video(prompt, user_id, additional_params)
                
            else:
                logger.warning_status(f"不支持的内容类型: {content_type}")
                return {"success": False, "error": f"不支持的内容类型: {content_type}"}
                
        except Exception as e:
            logger.error_status(f"生成多模态内容失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_image(self, prompt: str, user_id: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成图像
        
        Args:
            prompt: 图像生成提示
            user_id: 用户ID
            params: 额外参数
            
        Returns:
            生成结果
        """
        params = params or {}
        
        try:
            # 提取参数
            size = params.get("size", "1024x1024")
            style = params.get("style", "natural")
            model = params.get("model", None)
            
            # 使用AI服务生成图像
            result = self.ai_service.generate_image(
                prompt=prompt,
                size=size,
                style=style,
                model=model
            )
            
            if result.get("success", False):
                image_data = result.get("image_data", "")
                
                generation_result = {
                    "success": True,
                    "content_type": "image",
                    "image_data": image_data,
                    "prompt": prompt,
                    "model": result.get("model", ""),
                    "timestamp": time.time(),
                    "user_id": user_id
                }
                
                # 更新用户上下文
                self._update_user_context(user_id, "last_generated_image", {
                    "timestamp": time.time(),
                    "prompt": prompt
                })
                
                # 发布事件
                self.event_bus.publish("image_generated", {
                    "user_id": user_id,
                    "result": generation_result,
                    "timestamp": time.time()
                })
                
                return generation_result
            else:
                logger.error_status(f"图像生成失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "图像生成失败"),
                    "user_id": user_id
                }
                
        except Exception as e:
            logger.error_status(f"生成图像异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id
            }
    
    def _generate_audio(self, text: str, user_id: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成音频（文本转语音）"""
        try:
            params = params or {}
            
            # 默认参数
            voice = params.get("voice", "default")
            model = params.get("model")  # 可选
            
            # 调用AI服务生成音频
            response = self.ai_service.generate_speech(text, voice, model)
            
            if not response or "audio_data" not in response:
                return {"success": False, "error": "音频生成失败"}
            
            # 构建结果
            result = {
                "success": True,
                "audio_data": response["audio_data"],
                "content_type": "audio",
                "text": text,
                "parameters": {
                    "voice": voice,
                    "model": model
                },
                "timestamp": time.time()
            }
            
            # 更新用户上下文
            self._update_user_context(user_id, "latest_generated_audio", {
                "text": text,
                "timestamp": time.time()
            })
            
            return result
        except Exception as e:
            logger.error_status(f"生成音频失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_video(self, prompt: str, user_id: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成视频（简化版）"""
        # 注意：真实的视频生成通常需要专门的API和更复杂的处理
        # 这里提供一个简化版实现
        try:
            params = params or {}
            
            # 默认参数
            duration = params.get("duration", 5)  # 默认5秒
            
            # 模拟视频生成（实际应用中应调用专门的视频生成API）
            return {
                "success": False,
                "error": "视频生成功能尚未实现",
                "content_type": "video",
                "prompt": prompt,
                "parameters": {
                    "duration": duration
                },
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error_status(f"生成视频失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_modality_stats(self) -> Dict[str, Any]:
        """
        获取模态处理统计
        
        Returns:
            模态处理统计数据
        """
        return {
            "modality_counts": self.modality_counts,
            "total_processed": sum(self.modality_counts.values()),
            "history_count": len(self.processing_history),
            "cache_size": len(self.multimodal_cache)
        }
    
    def shutdown(self):
        """关闭模块"""
        # 清理缓存
        with self.cache_lock:
            self.multimodal_cache.clear()
        
        logger.info(f"多模态感知模块 {self.module_id} 已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> MultimodalPerception:
    """
    获取多模态感知模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        多模态感知模块实例
    """
    return MultimodalPerception(config=config) 