#!/usr/bin/env python3
"""
社交感知模块 - Social Perception

该模块负责感知和分析社交环境和社交关系，包括：
1. 社交关系感知 - 识别和理解社交关系
2. 社交环境感知 - 理解社交环境和氛围
3. 群体动态感知 - 感知群体互动和群体情绪
4. 社交规范理解 - 理解和适应不同社交环境的规范
5. 社交网络分析 - 分析社交网络结构和关系强度

作者: Claude
创建日期: 2024-08-01
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, Set
from datetime import datetime

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
perception_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(perception_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from adapters.ai_service_adapter import get_instance as get_ai_service

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.perception.social_perception")

class SocialPerception(CognitiveModuleBase):
    """
    社交感知模块
    
    感知和分析社交环境和社交关系。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化社交感知模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "social_perception", "perception", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取AI服务适配器
        self.ai_service = get_ai_service()
        
        # 社交关系类型
        self.relationship_types = {
            "friend": "朋友关系",
            "family": "家庭关系",
            "colleague": "同事关系",
            "acquaintance": "认识的人",
            "stranger": "陌生人",
            "romantic": "浪漫关系",
            "mentor": "导师关系",
            "student": "学生关系",
            "professional": "专业关系",
            "service": "服务关系"
        }
        
        # 社交环境类型
        self.social_contexts = {
            "personal": "个人聊天",
            "group_chat": "群聊",
            "professional": "专业环境",
            "educational": "教育环境",
            "casual": "日常休闲",
            "formal": "正式场合",
            "public": "公开场合",
            "private": "私人场合"
        }
        
        # 社交信号词汇
        self.social_signal_vocabulary = {
            "greeting": ["你好", "早上好", "晚上好", "嗨", "您好"],
            "farewell": ["再见", "拜拜", "晚安", "下次见", "回头见"],
            "agreement": ["是的", "同意", "没错", "理解", "确实"],
            "disagreement": ["不是", "不同意", "并非如此", "恐怕不是", "不这么认为"],
            "appreciation": ["谢谢", "感谢", "感激", "谢谢你", "多谢"],
            "apology": ["抱歉", "对不起", "不好意思", "请原谅", "很遗憾"]
        }
        
        # 社交规范知识库
        self.social_norms = {
            "general": [
                "尊重他人的观点和意见",
                "避免打断别人说话",
                "使用礼貌用语",
                "避免过度分享个人信息",
                "适当表达感谢和欣赏"
            ],
            "group_chat": [
                "避免在群里发私人消息",
                "避免刷屏或发送大量消息",
                "尊重群规和管理员决定",
                "参与讨论但不要垄断话题",
                "避免群内争吵和冲突"
            ],
            "professional": [
                "使用正式语言和专业术语",
                "尊重时间和效率",
                "明确表达工作相关信息",
                "保持专业边界",
                "遵循行业惯例和标准"
            ]
        }
        
        # 社交感知历史
        self.perception_history = []
        
        # 社交环境缓存
        self.social_context_cache = {}
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"社交感知模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("user_message", self._on_user_message)
        self.event_bus.subscribe("group_message", self._on_group_message)
        self.event_bus.subscribe("user_joined", self._on_user_joined)
        self.event_bus.subscribe("user_left", self._on_user_left)
        self.event_bus.subscribe("relationship_updated", self._on_relationship_updated)
        
        logger.debug("已订阅相关事件")
    
    def _on_user_message(self, data: Dict[str, Any]):
        """处理用户消息事件"""
        try:
            message = data.get("message", "")
            user_id = data.get("user_id", "")
            session_id = data.get("session_id", "")
            
            if not message or not user_id:
                return
            
            # 分析社交信号
            social_signals = self._analyze_social_signals(message)
            
            # 分析社交关系
            relationship = self._get_user_relationship(user_id)
            
            # 更新社交环境缓存
            self._update_social_context(user_id, session_id, message, social_signals)
            
            # 发布社交感知事件
            self.event_bus.publish("social_perception_updated", {
                "user_id": user_id,
                "session_id": session_id,
                "social_signals": social_signals,
                "relationship": relationship,
                "timestamp": time.time()
            })
            
            # 记录感知历史
            self.perception_history.append({
                "type": "user_message",
                "user_id": user_id,
                "session_id": session_id,
                "social_signals": social_signals,
                "timestamp": time.time()
            })
        except Exception as e:
            logger.error_status(f"处理用户消息事件失败: {e}")
    
    def _on_group_message(self, data: Dict[str, Any]):
        """处理群聊消息事件"""
        try:
            message = data.get("message", "")
            user_id = data.get("user_id", "")
            group_id = data.get("group_id", "")
            
            if not message or not user_id or not group_id:
                return
            
            # 分析社交信号
            social_signals = self._analyze_social_signals(message)
            
            # 分析群体动态
            group_dynamics = self._analyze_group_dynamics(group_id, user_id, message)
            
            # 发布群体感知事件
            self.event_bus.publish("group_perception_updated", {
                "user_id": user_id,
                "group_id": group_id,
                "social_signals": social_signals,
                "group_dynamics": group_dynamics,
                "timestamp": time.time()
            })
            
            # 记录感知历史
            self.perception_history.append({
                "type": "group_message",
                "user_id": user_id,
                "group_id": group_id,
                "social_signals": social_signals,
                "timestamp": time.time()
            })
        except Exception as e:
            logger.error_status(f"处理群聊消息事件失败: {e}")
    
    def _on_user_joined(self, data: Dict[str, Any]):
        """处理用户加入事件"""
        try:
            user_id = data.get("user_id", "")
            group_id = data.get("group_id", "")
            
            if not user_id or not group_id:
                return
            
            # 更新群体动态
            self._update_group_member(group_id, user_id, "joined")
            
            # 发布群体更新事件
            self.event_bus.publish("group_membership_updated", {
                "user_id": user_id,
                "group_id": group_id,
                "action": "joined",
                "timestamp": time.time()
            })
        except Exception as e:
            logger.error_status(f"处理用户加入事件失败: {e}")
    
    def _on_user_left(self, data: Dict[str, Any]):
        """处理用户离开事件"""
        try:
            user_id = data.get("user_id", "")
            group_id = data.get("group_id", "")
            
            if not user_id or not group_id:
                return
            
            # 更新群体动态
            self._update_group_member(group_id, user_id, "left")
            
            # 发布群体更新事件
            self.event_bus.publish("group_membership_updated", {
                "user_id": user_id,
                "group_id": group_id,
                "action": "left",
                "timestamp": time.time()
            })
        except Exception as e:
            logger.error_status(f"处理用户离开事件失败: {e}")
    
    def _on_relationship_updated(self, data: Dict[str, Any]):
        """处理关系更新事件"""
        try:
            user_id = data.get("user_id", "")
            relationship_type = data.get("relationship_type", "")
            
            if not user_id or not relationship_type:
                return
            
            # 更新用户关系
            self._update_user_relationship(user_id, relationship_type)
            
            logger.info(f"用户 {user_id} 的关系更新为 {relationship_type}")
        except Exception as e:
            logger.error_status(f"处理关系更新事件失败: {e}")
    
    def _analyze_social_signals(self, message: str) -> Dict[str, List[str]]:
        """
        分析社交信号
        
        Args:
            message: 消息文本
            
        Returns:
            社交信号分析结果
        """
        signals = {}
        
        # 检测社交信号词汇
        for signal_type, keywords in self.social_signal_vocabulary.items():
            found_keywords = []
            for keyword in keywords:
                if keyword in message:
                    found_keywords.append(keyword)
            
            if found_keywords:
                signals[signal_type] = found_keywords
        
        return signals
    
    def _get_user_relationship(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户关系信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户关系信息
        """
        try:
            # 从生命上下文获取用户信息
            user_context = self.life_context.get_user_context(user_id)
            
            # 获取关系信息
            relationship = user_context.get("relationship", {})
            if not relationship:
                # 默认为陌生人关系
                relationship = {
                    "type": "stranger",
                    "description": self.relationship_types.get("stranger", "陌生人"),
                    "familiarity": 0.1,
                    "last_updated": time.time()
                }
                
                # 更新用户上下文
                self._update_user_relationship(user_id, "stranger")
            
            return relationship
        except Exception as e:
            logger.error_status(f"获取用户关系信息失败: {e}")
            return {
                "type": "unknown",
                "description": "未知关系",
                "familiarity": 0.0,
                "last_updated": time.time()
            }
    
    def _update_user_relationship(self, user_id: str, relationship_type: str) -> bool:
        """
        更新用户关系
        
        Args:
            user_id: 用户ID
            relationship_type: 关系类型
            
        Returns:
            是否更新成功
        """
        try:
            if relationship_type not in self.relationship_types:
                relationship_type = "stranger"
            
            # 构建关系信息
            relationship = {
                "type": relationship_type,
                "description": self.relationship_types.get(relationship_type, "陌生人"),
                "familiarity": self._get_familiarity_level(relationship_type),
                "last_updated": time.time()
            }
            
            # 更新用户上下文
            self.life_context.update_user_context(user_id, "relationship", relationship)
            
            return True
        except Exception as e:
            logger.error_status(f"更新用户关系失败: {e}")
            return False
    
    def _get_familiarity_level(self, relationship_type: str) -> float:
        """
        获取关系熟悉度级别
        
        Args:
            relationship_type: 关系类型
            
        Returns:
            熟悉度级别 (0-1)
        """
        familiarity_map = {
            "stranger": 0.1,
            "acquaintance": 0.3,
            "service": 0.4,
            "colleague": 0.5,
            "professional": 0.6,
            "friend": 0.7,
            "mentor": 0.8,
            "student": 0.8,
            "family": 0.9,
            "romantic": 0.95
        }
        
        return familiarity_map.get(relationship_type, 0.1)
    
    def _update_social_context(self, user_id: str, session_id: str, message: str, social_signals: Dict[str, List[str]]):
        """
        更新社交环境缓存
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            message: 消息内容
            social_signals: 社交信号
        """
        try:
            # 生成缓存键
            cache_key = f"{user_id}_{session_id}"
            
            # 获取现有社交环境或创建新的
            social_context = self.social_context_cache.get(cache_key, {
                "context_type": "personal",
                "formality_level": 0.5,
                "social_signals": {},
                "message_count": 0,
                "last_updated": time.time()
            })
            
            # 更新社交信号
            for signal_type, signals in social_signals.items():
                if signal_type not in social_context["social_signals"]:
                    social_context["social_signals"][signal_type] = []
                social_context["social_signals"][signal_type].extend(signals)
            
            # 更新消息计数
            social_context["message_count"] += 1
            
            # 更新时间戳
            social_context["last_updated"] = time.time()
            
            # 更新缓存
            self.social_context_cache[cache_key] = social_context
        except Exception as e:
            logger.error_status(f"更新社交环境缓存失败: {e}")
    
    def _analyze_group_dynamics(self, group_id: str, user_id: str, message: str) -> Dict[str, Any]:
        """
        分析群体动态
        
        Args:
            group_id: 群组ID
            user_id: 用户ID
            message: 消息内容
            
        Returns:
            群体动态分析结果
        """
        try:
            # 获取群组信息
            group_context = self.life_context.get_group_context(group_id)
            
            # 获取活跃成员
            active_members = group_context.get("active_members", [])
            if user_id not in active_members:
                active_members.append(user_id)
                # 更新群组上下文
                self.life_context.update_group_context(group_id, "active_members", active_members)
            
            # 获取消息历史
            message_history = group_context.get("message_history", [])
            message_history.append({
                "user_id": user_id,
                "timestamp": time.time()
            })
            # 只保留最近50条消息
            message_history = message_history[-50:]
            # 更新群组上下文
            self.life_context.update_group_context(group_id, "message_history", message_history)
            
            # 分析群体活跃度
            activity_level = len(message_history) / 50
            
            # 分析用户参与度
            user_messages = [m for m in message_history if m["user_id"] == user_id]
            participation_level = len(user_messages) / max(1, len(message_history))
            
            # 构建分析结果
            return {
                "group_id": group_id,
                "active_members_count": len(active_members),
                "activity_level": activity_level,
                "user_participation": participation_level,
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error_status(f"分析群体动态失败: {e}")
            return {
                "group_id": group_id,
                "active_members_count": 1,
                "activity_level": 0.1,
                "user_participation": 1.0,
                "timestamp": time.time()
            }
    
    def _update_group_member(self, group_id: str, user_id: str, action: str):
        """
        更新群组成员
        
        Args:
            group_id: 群组ID
            user_id: 用户ID
            action: 操作类型 (joined/left)
        """
        try:
            # 获取群组信息
            group_context = self.life_context.get_group_context(group_id)
            
            # 获取成员列表
            members = group_context.get("members", [])
            
            if action == "joined":
                if user_id not in members:
                    members.append(user_id)
            elif action == "left":
                if user_id in members:
                    members.remove(user_id)
            
            # 更新群组上下文
            self.life_context.update_group_context(group_id, "members", members)
            
            # 更新活跃成员
            active_members = group_context.get("active_members", [])
            if action == "joined":
                if user_id not in active_members:
                    active_members.append(user_id)
            elif action == "left":
                if user_id in active_members:
                    active_members.remove(user_id)
            
            # 更新群组上下文
            self.life_context.update_group_context(group_id, "active_members", active_members)
        except Exception as e:
            logger.error_status(f"更新群组成员失败: {e}")
    
    def get_social_context(self, user_id: str, session_id: str = "") -> Dict[str, Any]:
        """
        获取社交环境信息
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            社交环境信息
        """
        try:
            # 生成缓存键
            cache_key = f"{user_id}_{session_id}"
            
            # 获取社交环境
            social_context = self.social_context_cache.get(cache_key, {
                "context_type": "personal",
                "formality_level": 0.5,
                "social_signals": {},
                "message_count": 0,
                "last_updated": time.time()
            })
            
            return social_context
        except Exception as e:
            logger.error_status(f"获取社交环境信息失败: {e}")
            return {
                "context_type": "personal",
                "formality_level": 0.5,
                "social_signals": {},
                "message_count": 0,
                "last_updated": time.time()
            }
    
    def get_relationship_type(self, user_id: str) -> str:
        """
        获取用户关系类型
        
        Args:
            user_id: 用户ID
            
        Returns:
            关系类型
        """
        try:
            # 获取用户关系信息
            relationship = self._get_user_relationship(user_id)
            
            return relationship.get("type", "stranger")
        except Exception as e:
            logger.error_status(f"获取用户关系类型失败: {e}")
            return "stranger"
    
    def set_relationship_type(self, user_id: str, relationship_type: str) -> bool:
        """
        设置用户关系类型
        
        Args:
            user_id: 用户ID
            relationship_type: 关系类型
            
        Returns:
            是否设置成功
        """
        try:
            return self._update_user_relationship(user_id, relationship_type)
        except Exception as e:
            logger.error_status(f"设置用户关系类型失败: {e}")
            return False
    
    def get_social_norms(self, context_type: str = "general") -> List[str]:
        """
        获取社交规范
        
        Args:
            context_type: 环境类型
            
        Returns:
            社交规范列表
        """
        try:
            return self.social_norms.get(context_type, self.social_norms["general"])
        except Exception as e:
            logger.error_status(f"获取社交规范失败: {e}")
            return self.social_norms["general"]
    
    def analyze_message(self, message: str, user_id: str, session_id: str = "") -> Dict[str, Any]:
        """
        分析消息的社交特征
        
        Args:
            message: 消息内容
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            社交特征分析结果
        """
        try:
            # 分析社交信号
            social_signals = self._analyze_social_signals(message)
            
            # 获取用户关系
            relationship = self._get_user_relationship(user_id)
            
            # 获取社交环境
            social_context = self.get_social_context(user_id, session_id)
            
            # 构建分析结果
            return {
                "user_id": user_id,
                "session_id": session_id,
                "social_signals": social_signals,
                "relationship": relationship,
                "social_context": social_context,
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error_status(f"分析消息社交特征失败: {e}")
            return {
                "user_id": user_id,
                "session_id": session_id,
                "social_signals": {},
                "relationship": self._get_user_relationship(user_id),
                "social_context": self.get_social_context(user_id, session_id),
                "timestamp": time.time()
            }
    
    def shutdown(self):
        """关闭模块"""
        logger.info(f"社交感知模块 {self.module_id} 已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> SocialPerception:
    """
    获取社交感知模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        社交感知模块实例
    """
    return SocialPerception(config=config) 