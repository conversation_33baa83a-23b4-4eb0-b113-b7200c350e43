#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能调度器 - Intelligent Scheduler

该模块负责管理67个平台的智能化数据收集调度，包括：
- 时间段差异化调度 (活跃期7:00-23:00, 安静期23:00-7:00)
- 平台分层管理 (4个层级，不同优先级)
- 紧急事件检测和响应
- 动态调整策略
- 资源保护机制

作者: 魅魔
版本: 1.0.0
"""

import os
import sys
import json
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Set, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import threading
from concurrent.futures import ThreadPoolExecutor
import pytz

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "../.."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from utilities.unified_logger import get_unified_logger
from core.enhanced_event_bus import get_instance as get_event_bus

logger = get_unified_logger('intelligent_scheduler')

class ScheduleMode(Enum):
    """调度模式枚举"""
    ACTIVE = "active"        # 活跃模式 (7:00-23:00)
    QUIET = "quiet"          # 安静模式 (23:00-7:00)
    EMERGENCY = "emergency"  # 紧急模式 (突发事件)

class PlatformTier(Enum):
    """平台层级枚举"""
    TIER_1_CORE = "tier_1_core"           # 核心平台
    TIER_2_IMPORTANT = "tier_2_important" # 重要平台
    TIER_3_SPECIALIZED = "tier_3_specialized" # 专业平台
    TIER_4_NICHE = "tier_4_niche"         # 小众平台

@dataclass
class PlatformSchedule:
    """平台调度信息"""
    platform_name: str
    tier: PlatformTier
    weight: float
    category: str
    endpoint: str
    max_topics: int
    active_interval: int
    quiet_interval: int
    last_update: float
    next_update: float
    consecutive_failures: int
    is_throttled: bool
    throttle_until: float

@dataclass
class SchedulerMetrics:
    """调度器指标"""
    total_platforms: int
    active_platforms: int
    throttled_platforms: int
    failed_platforms: int
    success_rate: float
    average_response_time: float
    requests_per_minute: int
    emergency_triggers: int

class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 300):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "closed"  # closed, open, half-open
        
    def call(self, func, *args, **kwargs):
        """执行调用并处理熔断逻辑"""
        if self.state == "open":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "half-open"
            else:
                raise Exception("Circuit breaker is open")
        
        try:
            result = func(*args, **kwargs)
            if self.state == "half-open":
                self.state = "closed"
                self.failure_count = 0
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "open"
            
            raise e

class IntelligentScheduler:
    """智能调度器"""
    
    def __init__(self, config_path: str = None):
        """初始化智能调度器"""
        # 🔥 防止重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            logger.debug("智能调度器已经初始化，跳过")
            return
        
        self.config_path = config_path or "config/hot_topics_sources.json"
        self.config = self._load_config()
        self.event_bus = get_event_bus()
        
        # 调度状态
        self.is_running = False
        self.current_mode = ScheduleMode.ACTIVE
        self.platform_schedules: Dict[str, PlatformSchedule] = {}
        self.metrics = SchedulerMetrics(0, 0, 0, 0, 0.0, 0.0, 0, 0)
        
        # 时区设置
        self.timezone = pytz.timezone(
            self.config.get('time_schedule', {}).get('active_hours', {}).get('timezone', 'Asia/Shanghai')
        )
        
        # 资源保护
        self.max_concurrent = self.config.get('resource_protection', {}).get('max_concurrent_platforms', 8)
        self.rate_limit = self.config.get('resource_protection', {}).get('request_rate_limit', 60)
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        
        # 请求统计
        self.request_times: List[float] = []
        self.request_lock = threading.Lock()
        
        # 紧急模式状态
        self.emergency_mode_until = 0
        self.emergency_platforms: Set[str] = set()
        
        # 初始化平台调度
        self._initialize_platform_schedules()
        
        # 🔥 标记初始化完成
        self._initialized = True
        
        logger.success("智能调度器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning_status(f"配置文件不存在: {self.config_path}")
                return {}
        except Exception as e:
            logger.error_status(f"加载配置文件失败: {e}")
            return {}
    
    def _initialize_platform_schedules(self):
        """初始化平台调度信息"""
        platform_tiers = self.config.get('platform_tiers', {})
        current_time = time.time()
        
        for tier_name, tier_config in platform_tiers.items():
            tier_enum = PlatformTier(tier_name)
            platforms = tier_config.get('platforms', {})
            active_interval = tier_config.get('active_interval', 1800)
            quiet_interval = tier_config.get('quiet_interval', 3600)
            
            for platform_name, platform_config in platforms.items():
                schedule = PlatformSchedule(
                    platform_name=platform_name,
                    tier=tier_enum,
                    weight=platform_config.get('weight', 0.5),
                    category=platform_config.get('category', 'general'),
                    endpoint=platform_config.get('endpoint', f'/{platform_name}'),
                    max_topics=platform_config.get('max_topics', 5),
                    active_interval=active_interval,
                    quiet_interval=quiet_interval,
                    last_update=0,
                    next_update=current_time,
                    consecutive_failures=0,
                    is_throttled=False,
                    throttle_until=0
                )
                
                self.platform_schedules[platform_name] = schedule
                
                # 初始化熔断器
                self.circuit_breakers[platform_name] = CircuitBreaker(
                    failure_threshold=self.config.get('resource_protection', {}).get('circuit_breaker', {}).get('failure_threshold', 5),
                    recovery_timeout=self.config.get('resource_protection', {}).get('circuit_breaker', {}).get('recovery_timeout', 300)
                )
        
        logger.success(f"初始化了 {len(self.platform_schedules)} 个平台的调度信息")
    
    def get_current_schedule_mode(self) -> ScheduleMode:
        """获取当前调度模式"""
        if self.emergency_mode_until > time.time():
            return ScheduleMode.EMERGENCY
        
        now = datetime.now(self.timezone)
        current_hour = now.hour
        
        # 解析活跃时间范围
        active_range = self.config.get('time_schedule', {}).get('active_hours', {}).get('time_range', '07:00-23:00')
        start_hour, end_hour = map(lambda x: int(x.split(':')[0]), active_range.split('-'))
        
        if start_hour <= current_hour < end_hour:
            return ScheduleMode.ACTIVE
        else:
            return ScheduleMode.QUIET
    
    def get_platforms_to_update(self) -> List[PlatformSchedule]:
        """获取需要更新的平台列表"""
        current_time = time.time()
        current_mode = self.get_current_schedule_mode()
        platforms_to_update = []
        
        for platform_name, schedule in self.platform_schedules.items():
            # 检查是否被限流
            if schedule.is_throttled and schedule.throttle_until > current_time:
                continue
            
            # 检查是否到了更新时间
            if schedule.next_update <= current_time:
                # 紧急模式下只更新指定平台
                if current_mode == ScheduleMode.EMERGENCY:
                    emergency_platforms = set(self.config.get('time_schedule', {}).get('emergency_mode', {}).get('platforms', []))
                    if platform_name not in emergency_platforms:
                        continue
                
                platforms_to_update.append(schedule)
        
        # 按权重排序，优先处理高权重平台
        platforms_to_update.sort(key=lambda x: x.weight, reverse=True)
        
        # 限制并发数量
        return platforms_to_update[:self.max_concurrent]
    
    def update_platform_schedule(self, platform_name: str, success: bool, response_time: float = 0):
        """更新平台调度信息"""
        if platform_name not in self.platform_schedules:
            return
        
        schedule = self.platform_schedules[platform_name]
        current_time = time.time()
        current_mode = self.get_current_schedule_mode()
        
        # 更新最后更新时间
        schedule.last_update = current_time
        
        # 记录请求时间
        with self.request_lock:
            self.request_times.append(current_time)
            # 清理1分钟前的记录
            self.request_times = [t for t in self.request_times if current_time - t <= 60]
        
        if success:
            # 成功情况
            schedule.consecutive_failures = 0
            
            # 取消限流
            if schedule.is_throttled:
                schedule.is_throttled = False
                schedule.throttle_until = 0
                logger.success(f"平台 {platform_name} 恢复正常调度")
            
            # 计算下次更新时间
            if current_mode == ScheduleMode.EMERGENCY:
                interval = self.config.get('time_schedule', {}).get('emergency_mode', {}).get('interval', 600)
            elif current_mode == ScheduleMode.ACTIVE:
                interval = schedule.active_interval
            else:
                interval = schedule.quiet_interval
            
            schedule.next_update = current_time + interval
            
        else:
            # 失败情况
            schedule.consecutive_failures += 1
            
            # 自适应限流
            if self._should_throttle_platform(schedule):
                self._apply_throttling(schedule)
            
            # 计算重试时间（指数退避）
            retry_config = self.config.get('resource_protection', {}).get('retry_policy', {})
            backoff_factor = retry_config.get('backoff_factor', 2)
            max_backoff = retry_config.get('max_backoff', 60)
            
            backoff_time = min(backoff_factor ** schedule.consecutive_failures, max_backoff)
            schedule.next_update = current_time + backoff_time
        
        # 发布调度更新事件
        self.event_bus.publish("scheduler.platform.updated", {
            "platform": platform_name,
            "success": success,
            "response_time": response_time,
            "next_update": schedule.next_update,
            "mode": current_mode.value,
            "timestamp": current_time
        })
    
    def _should_throttle_platform(self, schedule: PlatformSchedule) -> bool:
        """判断是否应该限流平台"""
        adaptive_config = self.config.get('resource_protection', {}).get('adaptive_throttling', {})
        if not adaptive_config.get('enabled', True):
            return False
        
        # 连续失败次数超过阈值
        failure_threshold = 3
        return schedule.consecutive_failures >= failure_threshold
    
    def _apply_throttling(self, schedule: PlatformSchedule):
        """应用限流策略"""
        adaptive_config = self.config.get('resource_protection', {}).get('adaptive_throttling', {})
        throttle_factor = adaptive_config.get('throttle_factor', 1.5)
        max_interval = adaptive_config.get('max_interval', 600)
        
        # 计算限流时间
        current_interval = schedule.active_interval if self.get_current_schedule_mode() == ScheduleMode.ACTIVE else schedule.quiet_interval
        throttle_interval = min(current_interval * throttle_factor, max_interval)
        
        schedule.is_throttled = True
        schedule.throttle_until = time.time() + throttle_interval
        
        logger.warning_status(f"平台 {schedule.platform_name} 被限流 {throttle_interval} 秒")
        
        # 发布限流事件
        self.event_bus.publish("scheduler.platform.throttled", {
            "platform": schedule.platform_name,
            "throttle_duration": throttle_interval,
            "consecutive_failures": schedule.consecutive_failures,
            "timestamp": time.time()
        })
    
    def detect_emergency_events(self, recent_data: Dict[str, Any]) -> bool:
        """检测紧急事件"""
        trigger_conditions = self.config.get('time_schedule', {}).get('emergency_mode', {}).get('trigger_conditions', [])
        
        # 检查热度激增
        if self._detect_heat_surge(recent_data):
            logger.warning_status("检测到热度激增，触发紧急模式")
            self._activate_emergency_mode("热度激增")
            return True
        
        # 检查突发事件关键词
        if self._detect_breaking_news(recent_data):
            logger.warning_status("检测到突发事件，触发紧急模式")
            self._activate_emergency_mode("突发事件")
            return True
        
        # 检查异常波动
        if self._detect_abnormal_fluctuation(recent_data):
            logger.warning_status("检测到异常波动，触发紧急模式")
            self._activate_emergency_mode("异常波动")
            return True
        
        return False
    
    def _detect_heat_surge(self, data: Dict[str, Any]) -> bool:
        """检测热度激增"""
        # 简单的热度激增检测逻辑
        # 实际实现中可以使用更复杂的算法
        for platform_data in data.values():
            if isinstance(platform_data, dict) and 'topics' in platform_data:
                topics = platform_data['topics']
                if isinstance(topics, list):
                    for topic in topics:
                        if isinstance(topic, dict) and topic.get('hot_score', 0) > 100000:
                            return True
        return False
    
    def _detect_breaking_news(self, data: Dict[str, Any]) -> bool:
        """检测突发事件"""
        breaking_keywords = ['突发', '紧急', '重大', '震惊', '爆炸', '地震', '事故', '灾难']
        
        for platform_data in data.values():
            if isinstance(platform_data, dict) and 'topics' in platform_data:
                topics = platform_data['topics']
                if isinstance(topics, list):
                    for topic in topics:
                        if isinstance(topic, dict):
                            title = topic.get('title', '')
                            if any(keyword in title for keyword in breaking_keywords):
                                return True
        return False
    
    def _detect_abnormal_fluctuation(self, data: Dict[str, Any]) -> bool:
        """检测异常波动"""
        # 检查平台数据量的异常变化
        for platform_name, platform_data in data.items():
            if isinstance(platform_data, dict) and 'topics' in platform_data:
                topics = platform_data['topics']
                if isinstance(topics, list):
                    # 如果某个平台的热搜数量异常多，可能是异常情况
                    if len(topics) > 50:  # 正常情况下不应该有这么多热搜
                        return True
        return False
    
    def _activate_emergency_mode(self, reason: str):
        """激活紧急模式"""
        emergency_config = self.config.get('time_schedule', {}).get('emergency_mode', {})
        duration = emergency_config.get('duration', 7200)  # 默认2小时
        
        self.emergency_mode_until = time.time() + duration
        self.emergency_platforms = set(emergency_config.get('platforms', []))
        self.metrics.emergency_triggers += 1
        
        # 发布紧急模式事件
        self.event_bus.publish("scheduler.emergency.triggered", {
            "reason": reason,
            "duration": duration,
            "platforms": list(self.emergency_platforms),
            "timestamp": time.time()
        })
        
        logger.warning_status(f"紧急模式已激活，原因: {reason}，持续时间: {duration}秒")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        current_time = time.time()
        current_mode = self.get_current_schedule_mode()
        
        # 统计平台状态
        active_platforms = 0
        throttled_platforms = 0
        failed_platforms = 0
        
        for schedule in self.platform_schedules.values():
            if schedule.next_update <= current_time:
                active_platforms += 1
            if schedule.is_throttled:
                throttled_platforms += 1
            if schedule.consecutive_failures > 0:
                failed_platforms += 1
        
        # 计算成功率
        total_requests = len(self.request_times)
        success_rate = 1.0  # 简化计算，实际应该基于成功/失败统计
        
        # 计算平均响应时间
        avg_response_time = 0.0  # 需要实际统计
        
        # 更新指标
        self.metrics = SchedulerMetrics(
            total_platforms=len(self.platform_schedules),
            active_platforms=active_platforms,
            throttled_platforms=throttled_platforms,
            failed_platforms=failed_platforms,
            success_rate=success_rate,
            average_response_time=avg_response_time,
            requests_per_minute=len(self.request_times),
            emergency_triggers=self.metrics.emergency_triggers
        )
        
        return {
            "is_running": self.is_running,
            "current_mode": current_mode.value,
            "emergency_mode_until": self.emergency_mode_until,
            "metrics": {
                "total_platforms": self.metrics.total_platforms,
                "active_platforms": self.metrics.active_platforms,
                "throttled_platforms": self.metrics.throttled_platforms,
                "failed_platforms": self.metrics.failed_platforms,
                "success_rate": self.metrics.success_rate,
                "requests_per_minute": self.metrics.requests_per_minute,
                "emergency_triggers": self.metrics.emergency_triggers
            },
            "platform_schedules": {
                name: {
                    "tier": schedule.tier.value,
                    "weight": schedule.weight,
                    "category": schedule.category,
                    "next_update": schedule.next_update,
                    "is_throttled": schedule.is_throttled,
                    "consecutive_failures": schedule.consecutive_failures
                }
                for name, schedule in self.platform_schedules.items()
            }
        }
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning_status("智能调度器已在运行中")
            return
        
        self.is_running = True
        logger.success("智能调度器启动完成")
        
        # 发布启动事件
        self.event_bus.publish("scheduler.started", {
            "total_platforms": len(self.platform_schedules),
            "timestamp": time.time()
        })
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning_status("智能调度器未在运行")
            return
        
        self.is_running = False
        logger.success("智能调度器已停止")
        
        # 发布停止事件
        self.event_bus.publish("scheduler.stopped", {
            "timestamp": time.time()
        })

# 全局实例
_scheduler_instance = None
_scheduler_lock = threading.Lock()

def get_scheduler_instance(config_path: str = None) -> IntelligentScheduler:
    """获取调度器单例"""
    global _scheduler_instance
    with _scheduler_lock:
        if _scheduler_instance is None:
            _scheduler_instance = IntelligentScheduler(config_path)
    return _scheduler_instance

def get_instance(config_path: str = None) -> IntelligentScheduler:
    """标准的单例工厂方法"""
    return get_scheduler_instance(config_path)

async def start_intelligent_scheduler(config_path: str = None):
    """启动智能调度器"""
    scheduler = get_scheduler_instance(config_path)
    await scheduler.start()

async def stop_intelligent_scheduler():
    """停止智能调度器"""
    global _scheduler_instance
    if _scheduler_instance:
        await _scheduler_instance.stop()

def get_scheduler_status() -> Dict[str, Any]:
    """获取调度器状态"""
    global _scheduler_instance
    if _scheduler_instance:
        return _scheduler_instance.get_scheduler_status()
    return {"error": "调度器未初始化"} 