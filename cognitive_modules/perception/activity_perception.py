#!/usr/bin/env python3
"""
活动感知模块 - Activity Perception Module

负责感知和分析数字生命的当前活动状态，结合剧本数据、生理状态、
环境因素等多维度信息，为决策系统提供活动感知数据。

作者: 魅魔程序员
创建日期: 2025-06-16
版本: 1.0
"""

import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque
from enum import Enum

# 🔥 老王修复：移除直接导入，改为延迟导入避免循环依赖
# from services.script_integration_service import (
#     ScriptIntegrationService, get_scripts_integration_service
# )

# 🔥 老王修复：延迟导入避免循环依赖
def _get_activity_classes():
    """延迟获取活动相关类，避免循环导入"""
    try:
        # 延迟导入
        from services.script_integration_service import EnhancedActivity, ActivityExecutionPlan
        return EnhancedActivity, ActivityExecutionPlan
    except ImportError:
        # 创建基础的活动类作为降级
        from dataclasses import dataclass
        from typing import Dict, Any

        @dataclass
        class EnhancedActivity:
            title: str
            description: str
            location: str = ""
            duration: int = 60
            metadata: Dict[str, Any] = None

        @dataclass
        class ActivityExecutionPlan:
            activity: EnhancedActivity
            execution_steps: list = None
            estimated_duration: int = 60

        return EnhancedActivity, ActivityExecutionPlan

# 全局变量存储类引用
_EnhancedActivity = None
_ActivityExecutionPlan = None

def get_enhanced_activity_class():
    """获取EnhancedActivity类"""
    global _EnhancedActivity, _ActivityExecutionPlan
    if _EnhancedActivity is None:
        _EnhancedActivity, _ActivityExecutionPlan = _get_activity_classes()
    return _EnhancedActivity

def get_activity_execution_plan_class():
    """获取ActivityExecutionPlan类"""
    global _EnhancedActivity, _ActivityExecutionPlan
    if _ActivityExecutionPlan is None:
        _EnhancedActivity, _ActivityExecutionPlan = _get_activity_classes()
    return _ActivityExecutionPlan
from perception.physical_world.vital_signs_simulator import VitalSignsSimulator
from perception.physical_world.hardware_monitor import HardwareMonitor

# 配置日志
logger = get_unified_logger("cognitive_modules.perception.activity_perception")

class ActivityState(Enum):
    """活动状态枚举"""
    IDLE = "idle"                    # 空闲
    PLANNING = "planning"            # 计划中
    EXECUTING = "executing"          # 执行中
    PAUSED = "paused"               # 暂停
    COMPLETED = "completed"          # 已完成
    INTERRUPTED = "interrupted"      # 被中断

class ActivityType(Enum):
    """活动类型枚举"""
    REST = "rest"                   # 休息
    WORK = "work"                   # 工作
    STUDY = "study"                 # 学习
    EXERCISE = "exercise"           # 运动
    ENTERTAINMENT = "entertainment" # 娱乐
    SOCIAL = "social"              # 社交
    CREATIVE = "creative"          # 创作
    MAINTENANCE = "maintenance"     # 维护

@dataclass
class ActivityContext:
    """活动上下文"""
    current_time: datetime
    time_slot: str
    weather_condition: str
    energy_level: float
    stress_level: float
    mood_state: str
    social_context: str
    hardware_status: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['current_time'] = self.current_time.isoformat()
        return data

@dataclass
class ActivityPerception:
    """活动感知数据"""
    activity_id: str
    activity_type: ActivityType
    activity_state: ActivityState
    current_activity: Optional[Any]  # 🔥 老王修复：使用Any避免循环导入
    execution_plan: Optional[Any]    # 🔥 老王修复：使用Any避免循环导入
    context: ActivityContext
    perception_confidence: float  # 感知置信度 0-1
    attention_focus: float       # 注意力集中度 0-1
    engagement_level: float      # 参与度 0-1
    interruption_risk: float     # 中断风险 0-1
    completion_progress: float   # 完成进度 0-1
    next_activity_suggestions: List[Any]  # 🔥 老王修复：使用Any避免循环导入
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['activity_type'] = self.activity_type.value
        data['activity_state'] = self.activity_state.value
        data['current_activity'] = self.current_activity.to_dict() if self.current_activity else None
        data['execution_plan'] = self.execution_plan.to_dict() if self.execution_plan else None
        data['context'] = self.context.to_dict()
        data['next_activity_suggestions'] = [
            activity.to_dict() for activity in self.next_activity_suggestions
        ]
        return data

class ActivityPerceptionModule:
    """活动感知模块"""
    
    def __init__(self,
                 script_service,  # ScriptsIntegrationService or compatible
                 vital_signs_simulator: VitalSignsSimulator,
                 hardware_monitor: HardwareMonitor,
                 perception_interval: float = 300.0):  # 🔥 香草优化：感知间隔改为5分钟，减少资源占用
        """
        初始化活动感知模块

        Args:
            script_service: 剧本集成服务
            vital_signs_simulator: 生理信号模拟器
            hardware_monitor: 硬件监控器
            perception_interval: 感知更新间隔（秒）
                - 默认300秒（5分钟）：平衡感知能力和资源占用
                - 相对于30分钟的活动生成周期，5分钟感知是合理的
                - 避免过度频繁的资源消耗
        """
        self.script_service = script_service
        self.vital_signs_simulator = vital_signs_simulator
        self.hardware_monitor = hardware_monitor
        self.perception_interval = perception_interval
        
        # 当前状态
        self.current_perception: Optional[ActivityPerception] = None
        self.current_execution_plan: Optional[Any] = None  # 🔥 老王修复：使用Any避免循环导入
        self.activity_history = deque(maxlen=50)  # 活动历史
        
        # 感知状态
        self.is_perceiving = False
        self.perception_thread = None
        self.last_perception_time = None
        
        # 统计信息
        self.stats = {
            'total_perceptions': 0,
            'activity_transitions': 0,
            'interruptions_detected': 0,
            'completion_predictions': 0,
            'perception_accuracy': 0.0,
            'average_engagement': 0.0,
            'last_perception_duration': 0.0
        }
        
        # 线程锁
        self._lock = threading.RLock()
        
        logger.success("活动感知模块初始化完成")
    
    def shutdown(self):
        """
        关闭活动感知模块，清理资源
        """
        try:
            logger.info("开始关闭活动感知模块...")
            
            # 停止感知
            self.stop_perception()
            
            # 清理历史数据
            with self._lock:
                if hasattr(self, 'activity_history'):
                    self.activity_history.clear()
                
                # 清理统计信息
                if hasattr(self, 'stats'):
                    self.stats.clear()
                
                # 清理当前状态
                self.current_perception = None
                self.current_execution_plan = None
            
            # 重置状态
            self.is_perceiving = False
            self.perception_thread = None
            
            logger.success("✅ 活动感知模块已成功关闭")
            
        except Exception as e:
            logger.error_status(f"❌ 活动感知模块关闭失败: {e}")
    
    def start_perception(self) -> bool:
        """开始活动感知"""
        if self.is_perceiving:
            logger.warning_status("活动感知已在运行中")
            return True
        
        try:
            self.is_perceiving = True
            
            # 启动感知线程
            self.perception_thread = threading.Thread(
                target=self._perception_loop,
                daemon=True
            )
            self.perception_thread.start()
            
            logger.success("活动感知已启动")
            return True
            
        except Exception as e:
            logger.error_status(f"启动活动感知失败: {e}")
            self.is_perceiving = False
            return False
    
    def stop_perception(self) -> None:
        """停止活动感知"""
        if not self.is_perceiving:
            return
        
        self.is_perceiving = False
        
        # 等待感知线程结束
        if self.perception_thread and self.perception_thread.is_alive():
            self.perception_thread.join(timeout=5.0)
        
        logger.info("活动感知已停止")
    
    def _perception_loop(self) -> None:
        """感知循环"""
        while self.is_perceiving:
            try:
                start_time = time.time()
                
                # 执行感知
                self._perform_perception()
                
                # 更新统计信息
                perception_duration = time.time() - start_time
                with self._lock:
                    self.stats['total_perceptions'] += 1
                    self.stats['last_perception_duration'] = perception_duration
                    self.last_perception_time = datetime.now()
                
                logger.debug(f"活动感知完成，耗时: {perception_duration:.3f}秒")
                
                # 等待下次感知
                time.sleep(self.perception_interval)
                
            except Exception as e:
                logger.error_status(f"活动感知循环异常: {e}")
                time.sleep(self.perception_interval)
    
    def _perform_perception(self) -> None:
        """执行活动感知"""
        try:
            # 收集上下文信息
            context = self._collect_activity_context()
            
            # 获取当前活动建议
            activity_suggestions = self.script_service.get_current_activity_suggestions()
            
            # 分析当前活动状态
            activity_state = self._analyze_activity_state(context)
            
            # 确定当前活动
            current_activity = self._determine_current_activity(
                activity_suggestions, context, activity_state
            )
            
            # 计算感知指标
            perception_metrics = self._calculate_perception_metrics(
                current_activity, context, activity_state
            )
            
            # 生成活动感知数据
            activity_perception = ActivityPerception(
                activity_id=f"perception_{int(time.time())}",
                activity_type=self._classify_activity_type(current_activity),
                activity_state=activity_state,
                current_activity=current_activity,
                execution_plan=self.current_execution_plan,
                context=context,
                perception_confidence=perception_metrics['confidence'],
                attention_focus=perception_metrics['attention'],
                engagement_level=perception_metrics['engagement'],
                interruption_risk=perception_metrics['interruption_risk'],
                completion_progress=perception_metrics['progress'],
                next_activity_suggestions=activity_suggestions[:3]  # 前3个建议
            )
            
            # 更新当前感知
            with self._lock:
                old_perception = self.current_perception
                self.current_perception = activity_perception
                
                # 检测状态转换
                if old_perception and old_perception.activity_state != activity_state:
                    self.stats['activity_transitions'] += 1
                    logger.info(f"活动状态转换: {old_perception.activity_state.value} -> {activity_state.value}")
                
                # 添加到历史
                self.activity_history.append(activity_perception)
            
        except Exception as e:
            logger.error_status(f"执行活动感知异常: {e}")
    
    def _collect_activity_context(self) -> ActivityContext:
        """收集活动上下文"""
        try:
            # 获取当前时间
            current_time = datetime.now()
            
            # 确定时间段
            hour = current_time.hour
            if 6 <= hour < 12:
                time_slot = "morning"
            elif 12 <= hour < 18:
                time_slot = "afternoon"
            else:
                time_slot = "evening"
            
            # 获取生理状态
            vital_signs = self.vital_signs_simulator.get_current_vitals()
            
            # 获取硬件状态
            hardware_status = self.hardware_monitor.get_current_snapshot()
            
            # 处理生理状态数据
            if hasattr(vital_signs, 'energy_level'):
                energy_level = vital_signs.energy_level
                stress_level = vital_signs.stress_level
                mood_state = vital_signs.mood_score
            else:
                # 如果是字典格式
                energy_level = vital_signs.get('energy_level', 0.5)
                stress_level = vital_signs.get('stress_level', 0.3)
                mood_state = vital_signs.get('mood_state', 'neutral')
            
            # 处理硬件状态数据
            if hasattr(hardware_status, 'to_dict'):
                hardware_dict = hardware_status.to_dict()
            elif hasattr(hardware_status, '__dict__'):
                hardware_dict = hardware_status.__dict__
            else:
                hardware_dict = hardware_status if isinstance(hardware_status, dict) else {}
            
            return ActivityContext(
                current_time=current_time,
                time_slot=time_slot,
                weather_condition="适宜",  # 可以后续集成天气API
                energy_level=energy_level,
                stress_level=stress_level,
                mood_state=str(mood_state),
                social_context="独处",  # 可以后续扩展
                hardware_status=hardware_dict
            )
            
        except Exception as e:
            logger.error_status(f"收集活动上下文失败: {e}")
            # 返回默认上下文
            return ActivityContext(
                current_time=datetime.now(),
                time_slot="afternoon",
                weather_condition="适宜",
                energy_level=0.5,
                stress_level=0.3,
                mood_state="neutral",
                social_context="独处",
                hardware_status={}
            )
    
    def _analyze_activity_state(self, context: ActivityContext) -> ActivityState:
        """分析活动状态"""
        try:
            # 基于能量水平和压力水平判断状态
            energy = context.energy_level
            stress = context.stress_level
            
            # 如果有执行计划，检查执行状态
            if self.current_execution_plan:
                plan_status = self.current_execution_plan.completion_status
                if plan_status == "executing":
                    return ActivityState.EXECUTING
                elif plan_status == "paused":
                    return ActivityState.PAUSED
                elif plan_status == "completed":
                    return ActivityState.COMPLETED
            
            # 基于生理状态判断
            if energy < 0.3:
                return ActivityState.IDLE  # 能量不足，处于空闲状态
            elif stress > 0.7:
                return ActivityState.INTERRUPTED  # 压力过大，可能被中断
            elif energy > 0.7 and stress < 0.4:
                return ActivityState.EXECUTING  # 状态良好，正在执行
            else:
                return ActivityState.PLANNING  # 中等状态，计划中
                
        except Exception as e:
            logger.error_status(f"分析活动状态失败: {e}")
            return ActivityState.IDLE
    
    def _determine_current_activity(self,
                                  activity_suggestions: List[Any],  # 🔥 老王修复
                                  context: ActivityContext,
                                  state: ActivityState) -> Optional[Any]:  # 🔥 老王修复
        """确定当前活动"""
        try:
            if not activity_suggestions:
                return None
            
            # 如果正在执行计划，返回计划中的活动
            if (self.current_execution_plan and 
                state in [ActivityState.EXECUTING, ActivityState.PAUSED]):
                return self.current_execution_plan.enhanced_activity
            
            # 根据上下文选择最合适的活动
            best_activity = None
            best_score = 0.0
            
            for activity in activity_suggestions:
                score = self._calculate_activity_suitability(activity, context)
                if score > best_score:
                    best_score = score
                    best_activity = activity
            
            return best_activity
            
        except Exception as e:
            logger.error_status(f"确定当前活动失败: {e}")
            return activity_suggestions[0] if activity_suggestions else None
    
    def _calculate_activity_suitability(self,
                                      activity: Any,  # 🔥 老王修复
                                      context: ActivityContext) -> float:
        """计算活动适合度"""
        try:
            score = 0.0
            
            # 能量水平匹配度
            energy_match = 1.0 - abs(activity.energy_level - context.energy_level)
            score += energy_match * 0.3
            
            # 执行优先级
            priority_score = activity.execution_priority / 10.0
            score += priority_score * 0.2
            
            # 时间适合度（基于预估持续时间）
            time_score = 1.0 if activity.estimated_duration <= 60 else 0.5
            score += time_score * 0.2
            
            # 情绪匹配度
            mood_score = 0.8 if context.mood_state in activity.mood_context else 0.5
            score += mood_score * 0.3
            
            return min(1.0, score)
            
        except Exception as e:
            logger.error_status(f"计算活动适合度失败: {e}")
            return 0.5
    
    def _classify_activity_type(self, activity: Optional[Any]) -> ActivityType:  # 🔥 老王修复
        """分类活动类型"""
        if not activity:
            return ActivityType.REST
        
        activity_text = activity.base_activity.lower()
        
        if any(word in activity_text for word in ['休息', '睡觉', '放松', '冥想']):
            return ActivityType.REST
        elif any(word in activity_text for word in ['工作', '编程', '开发', '任务']):
            return ActivityType.WORK
        elif any(word in activity_text for word in ['学习', '阅读', '研究', '学']):
            return ActivityType.STUDY
        elif any(word in activity_text for word in ['运动', '锻炼', '健身', '跑步']):
            return ActivityType.EXERCISE
        elif any(word in activity_text for word in ['娱乐', '游戏', '看剧', '音乐']):
            return ActivityType.ENTERTAINMENT
        elif any(word in activity_text for word in ['社交', '聊天', '交流', '会面']):
            return ActivityType.SOCIAL
        elif any(word in activity_text for word in ['创作', '写作', '绘画', '设计']):
            return ActivityType.CREATIVE
        elif any(word in activity_text for word in ['维护', '清理', '整理', '修复']):
            return ActivityType.MAINTENANCE
        else:
            return ActivityType.REST
    
    def _calculate_perception_metrics(self,
                                    current_activity: Optional[Any],  # 🔥 老王修复
                                    context: ActivityContext,
                                    state: ActivityState) -> Dict[str, float]:
        """计算感知指标"""
        try:
            metrics = {
                'confidence': 0.8,      # 基础置信度
                'attention': 0.6,       # 基础注意力
                'engagement': 0.5,      # 基础参与度
                'interruption_risk': 0.3, # 基础中断风险
                'progress': 0.0         # 基础进度
            }
            
            # 根据活动状态调整指标
            if state == ActivityState.EXECUTING:
                metrics['attention'] = 0.8
                metrics['engagement'] = 0.9
                metrics['confidence'] = 0.9
            elif state == ActivityState.IDLE:
                metrics['attention'] = 0.3
                metrics['engagement'] = 0.2
                metrics['interruption_risk'] = 0.1
            elif state == ActivityState.INTERRUPTED:
                metrics['attention'] = 0.4
                metrics['engagement'] = 0.3
                metrics['interruption_risk'] = 0.8
            
            # 根据生理状态调整
            if context.energy_level > 0.7:
                metrics['attention'] += 0.2
                metrics['engagement'] += 0.2
            
            if context.stress_level > 0.6:
                metrics['interruption_risk'] += 0.3
                metrics['attention'] -= 0.2
            
            # 计算进度（如果有执行计划）
            if self.current_execution_plan:
                start_time = self.current_execution_plan.execution_time
                duration = self.current_execution_plan.enhanced_activity.estimated_duration
                elapsed = (datetime.now() - start_time).total_seconds() / 60  # 分钟
                metrics['progress'] = min(1.0, elapsed / duration)
            
            # 确保所有指标在0-1范围内
            for key in metrics:
                metrics[key] = max(0.0, min(1.0, metrics[key]))
            
            return metrics
            
        except Exception as e:
            logger.error_status(f"计算感知指标失败: {e}")
            return {
                'confidence': 0.5,
                'attention': 0.5,
                'engagement': 0.5,
                'interruption_risk': 0.5,
                'progress': 0.0
            }
    
    def get_current_perception(self) -> Optional[ActivityPerception]:
        """获取当前活动感知"""
        with self._lock:
            return self.current_perception
    
    def set_execution_plan(self, plan: Any) -> None:  # 🔥 老王修复：使用Any避免循环导入
        """设置执行计划"""
        with self._lock:
            self.current_execution_plan = plan
        logger.info(f"设置活动执行计划: {plan.activity_id}")
    
    def clear_execution_plan(self) -> None:
        """清除执行计划"""
        with self._lock:
            self.current_execution_plan = None
        logger.info("清除活动执行计划")
    
    def get_activity_history(self, limit: int = 10) -> List[ActivityPerception]:
        """获取活动历史"""
        with self._lock:
            return list(self.activity_history)[-limit:]
    
    def get_perception_stats(self) -> Dict[str, Any]:
        """获取感知统计信息"""
        with self._lock:
            stats = self.stats.copy()
            stats['is_perceiving'] = self.is_perceiving
            stats['current_state'] = (
                self.current_perception.activity_state.value 
                if self.current_perception else None
            )
            stats['last_perception_time'] = (
                self.last_perception_time.isoformat() 
                if self.last_perception_time else None
            )
            
            # 计算平均参与度
            if self.activity_history:
                avg_engagement = sum(
                    p.engagement_level for p in self.activity_history
                ) / len(self.activity_history)
                stats['average_engagement'] = avg_engagement
            
            return stats

    def get_current_activity_suggestions(self) -> List[Any]:
        """
        获取当前活动建议 - 核心功能方法

        Returns:
            当前活动建议列表
        """
        try:
            # 🔥 老王修复：添加缺失的核心功能方法
            if self.script_service is None:
                logger.warning("script_service为None，返回空建议列表")
                return []

            # 检查script_service是否有get_current_activity_suggestions方法
            if hasattr(self.script_service, 'get_current_activity_suggestions'):
                suggestions = self.script_service.get_current_activity_suggestions()
                logger.info(f"从script_service获取到 {len(suggestions)} 个活动建议")
                return suggestions
            else:
                logger.warning("script_service没有get_current_activity_suggestions方法")
                return []

        except Exception as e:
            logger.error_status(f"获取活动建议失败: {e}")
            return []

    def __del__(self):
        """析构函数"""
        self.stop_perception()

# 单例管理
_activity_perception_instance = None

def get_instance() -> ActivityPerceptionModule:
    """获取活动感知模块实例"""
    global _activity_perception_instance
    if _activity_perception_instance is None:
        # 创建默认的依赖项
        try:
            # 🔥 老王修复：延迟导入避免循环依赖
            def get_scripts_integration_service():
                try:
                    from services.script_integration_service import get_scripts_integration_service as _get_service
                    return _get_service()
                except ImportError as e:
                    logger.warning(f"无法导入script_integration_service: {e}")
                    return None

            from perception.physical_world.vital_signs_simulator import VitalSignsSimulator
            from perception.physical_world.hardware_monitor import HardwareMonitor
            
            # 🔥 修复：创建硬件监控器实例
            hardware_monitor = HardwareMonitor()
            hardware_monitor.start_monitoring()
            
            vital_signs_simulator = VitalSignsSimulator(hardware_monitor)

            # 🔥 修复：获取ScriptsIntegrationService实例
            script_service = get_scripts_integration_service()

            # 如果script_service获取失败，使用None
            if script_service is None:
                logger.warning("script_service获取失败，使用None")

            # 创建ActivityPerceptionModule实例
            _activity_perception_instance = ActivityPerceptionModule(
                script_service=script_service,
                vital_signs_simulator=vital_signs_simulator,
                hardware_monitor=hardware_monitor
            )

            logger.info("✅ 活动感知模块使用完整依赖项创建成功")
            
        except Exception as e:
            logger.warning(f"⚠️ 创建活动感知模块依赖项失败: {e}，使用简化版本")
            # 使用简化版本的依赖项
            try:
                from perception.physical_world.hardware_monitor import HardwareMonitor

                # 创建简化的硬件监控器
                hardware_monitor = HardwareMonitor()

                # 简化的生命体征模拟器（不需要复杂的生物配置）
                class SimpleVitalSignsSimulator:
                    def __init__(self, hardware_monitor):
                        self.hardware_monitor = hardware_monitor
                        self.is_running = False

                    def start_simulation(self):
                        self.is_running = True

                    def stop_simulation(self):
                        self.is_running = False

                    def get_current_vitals(self):
                        return {
                            'heartrate': 72.0,
                            'energy_level': 80.0,
                            'stress_level': 20.0,
                            'timestamp': datetime.now()
                        }

                vital_signs_simulator = SimpleVitalSignsSimulator(hardware_monitor)

                # 🔥 修复：创建简化的脚本服务
                class SimpleScriptService:
                    def get_current_activity_suggestions(self):
                        return []

                script_service = SimpleScriptService()

                logger.info("✅ 活动感知模块使用简化版本创建成功")

            except Exception as e2:
                logger.error(f"❌ 创建简化版本也失败: {e2}")
                script_service = None
                vital_signs_simulator = None
                hardware_monitor = None

            _activity_perception_instance = ActivityPerceptionModule(
                script_service=script_service,
                vital_signs_simulator=vital_signs_simulator,
                hardware_monitor=hardware_monitor
            )
            
            logger.success("✅ 活动感知模块初始化完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 创建活动感知模块依赖项失败: {e}，使用简化版本")
            _activity_perception_instance = create_simplified_activity_perception_module()
    
    return _activity_perception_instance

def create_simplified_script_service():
    """创建简化版脚本服务"""
    class SimplifiedScriptService:
        def get_current_activity_suggestions(self):
            """获取当前活动建议"""
            # 🔥 老王修复：使用延迟导入获取活动类
            try:
                from services.script_integration_service import ActivityPriority
            except (ImportError, Exception) as e:
                # 创建简单的ActivityPriority枚举
                from enum import Enum
                class ActivityPriority(Enum):
                    LOW = "low"
                    NORMAL = "normal"
                    MEDIUM = "medium"  # 🔥 老王修复：添加MEDIUM优先级
                    HIGH = "high"

            # 🔥 获取活动类
            EnhancedActivity = get_enhanced_activity_class()

            return [
                EnhancedActivity(
                    activity_id="default_1",
                    name="休息",
                    description="短暂休息，恢复精力",
                    category="rest",
                    priority=ActivityPriority.MEDIUM,
                    estimated_duration=15,
                    energy_cost=0.1,
                    prerequisites=[],
                    outcomes=["energy_recovery"]
                ),
                EnhancedActivity(
                    activity_id="default_2",
                    name="学习",
                    description="阅读或学习新知识",
                    category="study",
                    priority=ActivityPriority.HIGH,
                    estimated_duration=30,
                    energy_cost=0.3,
                    prerequisites=[],
                    outcomes=["knowledge_gain"]
                )
            ]
    
    return SimplifiedScriptService()

def create_simplified_activity_perception_module() -> ActivityPerceptionModule:
    """创建简化的活动感知模块"""
    # 创建简化的依赖项
    class SimplifiedScriptService:
        def get_current_activity_suggestions(self):
            return []
    
    class SimplifiedVitalSigns:
        def get_current_vitals(self):
            return {
                'energy_level': 0.7,
                'stress_level': 0.3,
                'mood_state': 'neutral'
            }
    
    class SimplifiedHardwareMonitor:
        def get_current_snapshot(self):
            return {}
    
    return ActivityPerceptionModule(
        script_service=SimplifiedScriptService(),
        vital_signs_simulator=SimplifiedVitalSigns(),
        hardware_monitor=SimplifiedHardwareMonitor()
    )

def create_activity_perception_module(script_service,  # ScriptsIntegrationService or compatible
                                    vital_signs_simulator: VitalSignsSimulator,
                                    hardware_monitor: HardwareMonitor,
                                    perception_interval: float = 300.0) -> ActivityPerceptionModule:  # 🔥 香草优化：默认5分钟
    """创建活动感知模块实例"""
    return ActivityPerceptionModule(
        script_service=script_service,
        vital_signs_simulator=vital_signs_simulator,
        hardware_monitor=hardware_monitor,
        perception_interval=perception_interval
    ) 