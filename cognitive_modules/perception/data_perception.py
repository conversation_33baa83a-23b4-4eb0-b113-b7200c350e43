#!/usr/bin/env python3
"""
数据感知模块
作为数据感知器官的接口模块
"""

import os
import sys
from typing import Dict, Any, Optional

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.append(project_root)

from utilities.unified_logger import get_unified_logger

logger = get_unified_logger(__name__)

class DataPerceptionModule:
    """数据感知模块"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logger
        
        # 尝试导入数据感知器官
        self.data_perception_organ = None
        self._initialize_organ()
        
        logger.success("数据感知模块初始化完成")
    
    def _initialize_organ(self):
        """初始化数据感知器官"""
        try:
            from cognitive_modules.organs.data_perception_organ import DataPerceptionOrgan
            from utilities.singleton_manager import get_silent
            
            # 尝试获取现有实例
            existing_organ = get_silent("data_perception_organ")
            if existing_organ:
                self.data_perception_organ = existing_organ
                logger.info("✅ 使用现有数据感知器官实例")
            else:
                # 创建新实例
                self.data_perception_organ = DataPerceptionOrgan()
                logger.info("✅ 创建新的数据感知器官实例")
                
        except Exception as e:
            logger.warning(f"⚠️ 数据感知器官初始化失败: {e}")
            self.data_perception_organ = None
    
    def perceive_data(self, data_source: str, data: Any) -> Dict[str, Any]:
        """感知数据"""
        try:
            if self.data_perception_organ and hasattr(self.data_perception_organ, 'perceive_data'):
                return self.data_perception_organ.perceive_data(data_source, data)
            else:
                # 简化版数据感知
                return {
                    "source": data_source,
                    "data_type": type(data).__name__,
                    "perception_confidence": 0.5,
                    "insights": ["数据已接收"],
                    "timestamp": __import__('time').time()
                }
        except Exception as e:
            logger.error(f"数据感知失败: {e}")
            return {
                "source": data_source,
                "error": str(e),
                "perception_confidence": 0.0,
                "timestamp": __import__('time').time()
            }
    
    def get_perception_state(self) -> Dict[str, Any]:
        """获取感知状态"""
        try:
            if self.data_perception_organ and hasattr(self.data_perception_organ, 'get_state'):
                return self.data_perception_organ.get_state()
            else:
                return {
                    "status": "active",
                    "perception_count": 0,
                    "last_perception": None
                }
        except Exception as e:
            logger.error(f"获取感知状态失败: {e}")
            return {"status": "error", "error": str(e)}


# 单例实例
_data_perception_instance = None

def get_instance() -> DataPerceptionModule:
    """获取数据感知模块实例"""
    global _data_perception_instance
    if _data_perception_instance is None:
        _data_perception_instance = DataPerceptionModule()
    return _data_perception_instance 