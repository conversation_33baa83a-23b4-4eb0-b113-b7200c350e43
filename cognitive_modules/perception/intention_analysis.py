"""
意图分析模块

负责分析用户输入的意图，包括主要意图、次要意图、情感倾向等。
从legacy系统的intention_system演化而来，但增加了更多的意图分析能力。
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
from typing import Dict, Any, List, Optional, Tuple

# 导入OpenAI（可选，用于类型提示）
try:
    import openai
except ImportError:
    openai = None

# 导入AI服务适配器
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
from adapters.ai_service_adapter import get_instance as get_ai_adapter

# 配置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

class IntentionAnalysis:
    """
    意图分析模块类
    
    负责分析用户输入的意图，包括：
    - 主要意图识别
    - 次要意图识别
    - 情感倾向分析
    - 紧急程度判断
    - 需求类型分类
    - 实时需求判断
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化意图分析模块
        
        Args:
            config: 模块配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        self.config = config or {}
        
        # API配置
        self.api_key = self.config.get('api_key', '')
        self.api_base = self.config.get('api_base', 'https://oneapi.xiongmaodaxia.online/v1')
        self.api_model = self.config.get('api_model', 'qwen-plus-latest')
        
        # 缓存设置
        self.intention_cache = {}
        self.intention_cache_time = {}
        self.intention_cache_expiry = self.config.get('intention_cache_expiry', 300)  # 默认5分钟
        
        # 上下文记忆
        self.user_contexts = {}
        self.max_context_length = self.config.get('max_context_length', 5)
        
        # 意图类别
        self.intent_categories = {
            "信息查询": ["实时信息需求", "历史信息查询", "解释或定义请求"],
            "任务执行": ["简单计算或转换", "复杂计算或分析", "创建或修改内容"],
            "推荐请求": ["产品推荐", "地点或活动推荐", "决策支持"],
            "导航或指引": ["地理导航", "流程指导", "故障排除"],
            "交互或对话": ["闲聊或社交互动", "情感支持", "角色扮演", "模拟对话"],
            "个人助理任务": ["日程安排", "个人管理", "设置或偏好调整"],
            "学习或教育": ["知识获取", "技能学习", "考试或测试准备"],
            "创意或娱乐": ["故事或内容创作", "游戏或益智活动", "艺术或音乐相关"],
            "商业或专业": ["市场分析", "专业建议", "商业策略"],
            "技术支持": ["软件问题解决", "使用指导", "兼容性查询"],
            "绘画创作": ["图片创作请求", "自拍照请求", "绘画艺术需求"],
            "网址解读": ["链接内容分析", "网页内容提取", "链接安全检查"],
            "不想互动": ["拒绝交流", "结束对话", "保持沉默"],
            "关系变更": ["拉黑请求", "删除好友", "修改关系状态"],
            "默认": ["未分类需求"]
        }
        
        # 初始化AI接口
        self._initialize_ai_interface()
        
        logger.success("意图分析模块初始化完成")
    
    def _initialize_ai_interface(self):
        """初始化AI接口"""
        # 获取AI服务适配器实例
        try:
            self.ai_adapter = get_ai_adapter()
            if not self.ai_adapter.is_initialized:
                self.ai_adapter.initialize()
            logger.success("OpenAI接口初始化完成")
        except Exception as e:
            logger.error_status(f"AI服务适配器初始化失败: {e}")
            logger.warning_status("将使用模拟分析")
            self.ai_adapter = None
    
    def analyze_intention(self, user_id: str, user_input: str, user_history: List[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        分析用户输入的意图
        
        Args:
            user_id: 用户ID
            user_input: 用户输入的文本
            user_history: 用户的历史对话记录
            
        Returns:
            意图分析结果
        """
        # 缓存键
        cache_key = f"{user_id}:{user_input}"
        
        # 检查缓存
        if cache_key in self.intention_cache:
            cache_time = self.intention_cache_time.get(cache_key, 0)
            if time.time() - cache_time < self.intention_cache_expiry:
                logger.debug(f"使用缓存的意图分析: {cache_key}")
                return self.intention_cache[cache_key]
        
        # 获取用户上下文
        context = self._get_user_context(user_id)
        
        # 尝试使用AI分析
        try:
            result = self._analyze_with_ai(user_id, user_input, context)
        except Exception as e:
            logger.error_status(f"AI分析失败: {e}")
            # 降级到规则分析
            result = self._analyze_with_rules(user_input)
        
        # 更新上下文
        self._update_user_context(user_id, user_input, result)
        
        # 更新缓存
        self.intention_cache[cache_key] = result
        self.intention_cache_time[cache_key] = time.time()
        
        return result
    
    def _get_user_context(self, user_id: str) -> List[Dict[str, str]]:
        """获取用户上下文"""
        return self.user_contexts.get(user_id, [])
    
    def _update_user_context(self, user_id: str, user_input: str, result: Dict[str, Any]):
        """更新用户上下文"""
        if user_id not in self.user_contexts:
            self.user_contexts[user_id] = []
        
        # 添加用户消息
        self.user_contexts[user_id].append({"role": "user", "content": user_input})
        
        # 添加分析结果
        context_result = f"主要意图: {result['main_intent']}\n次要意图: {result['sub_intent']}"
        self.user_contexts[user_id].append({"role": "assistant", "content": context_result})
        
        # 限制上下文长度
        if len(self.user_contexts[user_id]) > self.max_context_length * 2:  # 乘以2是因为每次添加两条记录
            self.user_contexts[user_id] = self.user_contexts[user_id][-self.max_context_length*2:]
    
    def _analyze_with_ai(self, user_id: str, user_input: str, context: List[Dict[str, str]]) -> Dict[str, Any]:
        """使用AI分析意图"""
        if self.ai_adapter is None:
            raise ImportError("AI服务适配器未初始化")
        
        # 获取当前时间信息
        current_time = datetime.datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 构建系统提示词
        system_prompt = self._build_system_prompt(formatted_time)
        
        # 构建消息列表
        messages = [{"role": "system", "content": system_prompt}]
        
        # 添加上下文
        if context:
            messages.extend(context)
        
        # 添加当前用户输入
        messages.append({"role": "user", "content": f"用户输入的是:\n{user_input}"})
        
        # 🔥 老王新增：神经网络意识增强集成
        enhanced_messages = self._enhance_intention_analysis_with_neural_consciousness(
            user_id, user_input, messages
        )

        # 使用AI服务适配器调用API
        response = self.ai_adapter.get_completion(
            messages=enhanced_messages,
            model=self.api_model,
            temperature=0.3,
            max_tokens=1000
        )
        
        if not response.get("success", False):
            raise Exception(response.get("error", "未知错误"))
            
        # 解析响应
        response_text = response.get("content", "").strip()
        return self._parse_ai_response(response_text)

    def _enhance_intention_analysis_with_neural_consciousness(self, user_id: str, user_input: str, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """🔥 老王新增：使用神经网络增强意图分析"""
        try:
            # 获取神经网络增强系统
            try:
                from core.neural_consciousness_enhancer import get_instance as get_neural_enhancer
                from core.advanced_neural_consciousness import get_instance as get_advanced_neural
                neural_enhancer = get_neural_enhancer()
                advanced_neural = get_advanced_neural()
            except Exception as e:
                logger.debug(f"神经网络系统不可用: {e}")
                return messages

            # 准备意图分析状态
            intention_state = {
                "user_id": user_id,
                "user_input": user_input,
                "context_length": len(messages),
                "input_complexity": len(user_input.split()),
                "analysis_depth": 0.7,
                "timestamp": time.time()
            }

            # 准备环境因子
            environmental_factors = {
                "linguistic_complexity": min(len(user_input.split()) / 15.0, 1.0),
                "context_richness": min(len(messages) / 10.0, 1.0),
                "learning_opportunity": 0.8,
                "analysis_challenge": 0.7,
                "cognitive_load": min(len(user_input) / 200.0, 1.0)
            }

            neural_insights = {}

            # 🧠 基础神经网络增强
            if neural_enhancer:
                try:
                    enhanced_intention = neural_enhancer.enhance_consciousness(intention_state, environmental_factors)
                    neural_insights["basic_neural"] = enhanced_intention
                    logger.debug("🧠 意图分析神经网络增强完成")
                except Exception as e:
                    logger.warning(f"意图分析神经网络增强失败: {e}")

            # 🚀 高级神经网络增强
            if advanced_neural:
                try:
                    ultimate_intention = advanced_neural.ultimate_consciousness_enhancement(intention_state, environmental_factors)
                    neural_insights["advanced_neural"] = ultimate_intention
                    logger.debug("🚀 意图分析高级神经网络增强完成")
                except Exception as e:
                    logger.warning(f"意图分析高级神经网络增强失败: {e}")

            # 🔥 老王优化：数据流集成增强
            if neural_insights:
                # 提取关键洞察用于意图分析
                intention_insights = {}
                for key, insight in neural_insights.items():
                    if isinstance(insight, dict):
                        consciousness_level = insight.get("consciousness_level", 0.5)
                        cognitive_complexity = insight.get("cognitive_complexity", 0.5)
                        quantum_coherence = insight.get("quantum_coherence", 0.0)

                        intention_insights[f"{key}_consciousness"] = consciousness_level
                        intention_insights[f"{key}_complexity"] = cognitive_complexity
                        if quantum_coherence > 0:
                            intention_insights[f"{key}_quantum"] = quantum_coherence

                # 构建神经网络增强的上下文
                neural_context = "🧠 神经网络意图洞察：\n"
                neural_context += f"- 意识深度: {intention_insights.get('basic_neural_consciousness', 0.5):.2f}\n"
                neural_context += f"- 认知复杂度: {intention_insights.get('basic_neural_complexity', 0.5):.2f}\n"
                if 'advanced_neural_quantum' in intention_insights:
                    neural_context += f"- 量子相干性: {intention_insights['advanced_neural_quantum']:.2f}\n"
                neural_context += f"- 语言复杂度: {environmental_factors['linguistic_complexity']:.2f}\n"
                neural_context += f"- 上下文丰富度: {environmental_factors['context_richness']:.2f}\n"
                neural_context += f"- 数据流版本: v2.0\n"

                # 在系统消息后添加神经网络上下文
                enhanced_messages = messages.copy()
                enhanced_messages.insert(1, {
                    "role": "system",
                    "content": neural_context
                })

                # 🔥 老王新增：记录数据流状态
                logger.debug(f"🔗 意图分析神经网络数据流集成完成: {intention_insights}")

                return enhanced_messages

            return messages

        except Exception as e:
            logger.error(f"意图分析神经网络增强失败: {e}")
            return messages
    
    def _build_system_prompt(self, formatted_time: str) -> str:
        """构建系统提示词"""
        return f"""
# 用户意图分析

你是一个特定功能的先进语义分析助手，专门负责全面分析用户输入，识别多种可能的意图。请根据提供的辅助信息，仔细分析用户的每一句话，并确定用户的主要意图类别以及可能的次要意图。

## 辅助信息

在进行分析时，请考虑以下提供的辅助信息：

1. 'now_time': 当前的准确时间：{formatted_time}
2. 'user_location': 中国，亚太
3. 'user_timezone': CST(UTC+8)
4. 'user_language': zh_CN
5. 'device_type': 手机/平板/电脑
6. 'app_context': 聊天应用
7. 'user_preferences': 普通聊天/需要联网查询服务

## 意图类别

1. 信息查询(需要实时数据)
   - 实时信息需求（比如：天气、交通、新闻、路线规划、问题咨询）
   - 历史或固定信息查询（如历史事件、科学知识）
   - 解释或定义请求
   - 人物身份查询（如"这俩是不是同一个人"、"某某是谁"等）

2. 任务执行
   - 简单计算或转换（如货币转换、单位换算）
   - 复杂计算或分析请求
   - 创建或修改内容（如写作、编码）

3. 推荐请求
   - 产品推荐
   - 地点或活动推荐
   - 决策支持

4. 导航或指引
   - 地理导航
   - 流程指导
   - 故障排除

5. 交互或对话
   - 闲聊或社交互动
   - 情感支持或倾听
   - 角色扮演
   - 模拟对话

6. 个人助理任务
   - 日程安排或提醒
   - 个人管理（如待办事项、笔记）
   - 设置或偏好调整

7. 学习或教育
   - 知识获取
   - 技能学习
   - 考试或测试准备

8. 创意或娱乐
   - 故事或内容创作
   - 游戏或益智活动
   - 艺术或音乐相关请求

9. 商业或专业
   - 市场分析
   - 专业建议（法律、财务等）
   - 商业策略

10. 技术支持
    - 软件/硬件问题解决
    - 使用指导
    - 兼容性查询

11. 绘画创作
    - 画图/需要提供自拍照/绘画艺术等图片创作的请求

12. 网址/链接解读
    - 需要联网查看网址/链接里的内容
    - 注意：仅当输入包含实际网址（如http、www、.com等）时才使用此类别

13. 不希望聊天/不希望互动
    - 用户表达不想聊天，或者让角色保持安静
    
14. 拉黑好友/删除好友
    - 表达要删除角色的社交联系，或者拉黑角色

15. 默认
    - 前面未列出的类别，默认不需要联网

## 输出格式

主要意图类别: [从上述类别中选择]
次要意图类别: [如果有，可以列出1-2个]
意图细分: [主要意图的具体子类别]
置信度: [0-100的数字，表示您对判断的确信程度]
是否需要实时数据: [是/否]
时间敏感度: [极高/高/中/低/无]
情感倾向: [积极/中性/消极]
紧急程度: [紧急/一般/非紧急]

请根据以上指南分析用户输入，然后提供您的全面意图判断。
        """
    
    def _parse_ai_response(self, response_text: str) -> Dict[str, Any]:
        """解析AI响应文本"""
        intention = {
            "main_intent": None,
            "sub_intent": None,
            "detailed_intent": None,
            "confidence": 0,
            "requires_realtime_data": False,
            "time_sensitivity": "低",
            "emotional_tendency": "中性",
            "urgency": "一般"
        }
        
        try:
            lines = response_text.split("\n")
            for line in lines:
                if "：" in line or ":" in line:
                    key, value = line.replace("：", ":").split(":", 1)
                    key = key.strip()
                    value = value.strip()
                    
                    if key == "主要意图类别":
                        intention["main_intent"] = value
                    elif key == "次要意图类别":
                        intention["sub_intent"] = value
                    elif key == "意图细分":
                        intention["detailed_intent"] = value
                    elif key == "置信度":
                        try:
                            intention["confidence"] = int(value)
                        except ValueError:
                            intention["confidence"] = 0
                    elif key == "是否需要实时数据":
                        intention["requires_realtime_data"] = value == "是"
                    elif key == "时间敏感度":
                        intention["time_sensitivity"] = value
                    elif key == "情感倾向":
                        intention["emotional_tendency"] = value
                    elif key == "紧急程度":
                        intention["urgency"] = value
            
            return intention
        except Exception as e:
            logger.error_status(f"解析AI响应失败: {e}")
            return {
                "main_intent": "默认",
                "sub_intent": None,
                "detailed_intent": "解析失败",
                "confidence": 0,
                "requires_realtime_data": False,
                "time_sensitivity": "低",
                "emotional_tendency": "中性",
                "urgency": "一般"
            }
    
    def _analyze_with_rules(self, user_input: str) -> Dict[str, Any]:
        """使用规则分析意图（作为降级策略）"""
        # 初始化结果
        result = {
            "main_intent": "默认",
            "sub_intent": None,
            "detailed_intent": "规则分析",
            "confidence": 50,
            "requires_realtime_data": False,
            "time_sensitivity": "低",
            "emotional_tendency": "中性",
            "urgency": "一般"
        }
        
        # 关键词映射
        keyword_maps = {
            "信息查询": ["什么", "怎么", "如何", "为什么", "是谁", "几点", "哪里", "多少", "查询", "搜索"],
            "任务执行": ["计算", "转换", "帮我", "请帮", "能否", "可以", "创建", "生成", "编写"],
            "推荐请求": ["推荐", "建议", "有什么好", "哪个好", "选择", "该不该"],
            "导航或指引": ["地图", "位置", "方向", "怎么走", "路线", "指导", "步骤"],
            "交互或对话": ["你好", "嗨", "嘿", "在吗", "聊天", "聊聊", "说说"],
            "个人助理任务": ["提醒", "记住", "记录", "备忘", "安排", "行程", "待办"],
            "学习或教育": ["学习", "教我", "课程", "知识", "考试", "复习"],
            "创意或娱乐": ["故事", "笑话", "游戏", "音乐", "歌曲", "电影", "娱乐"],
            "商业或专业": ["市场", "公司", "业务", "利润", "投资", "股票"],
            "技术支持": ["错误", "问题", "故障", "不工作", "修复", "解决"],
            "绘画创作": ["画", "图片", "自拍", "照片", "绘制", "设计"],
            "网址解读": ["网址", "链接", "网站", "url", "http", "www"],
            "不想互动": ["闭嘴", "别说", "不想聊", "结束", "停止", "不想听"],
            "关系变更": ["拉黑", "删除", "不要联系", "屏蔽", "拜拜", "再见"]
        }
        
        # 情感关键词
        emotion_keywords = {
            "积极": ["喜欢", "爱", "感谢", "谢谢", "开心", "高兴", "棒", "好", "赞", "优秀"],
            "消极": ["讨厌", "恨", "烦", "生气", "难过", "伤心", "糟糕", "差", "坏", "失望"]
        }
        
        # 紧急关键词
        urgency_keywords = {
            "紧急": ["立即", "马上", "紧急", "现在", "急需", "赶快", "快点", "立刻"],
            "非紧急": ["有时间", "闲", "无所谓", "慢慢", "随意", "无需着急"]
        }
        
        # 实时数据关键词
        realtime_keywords = ["现在", "今天", "当前", "实时", "最新", "天气", "路况", "新闻"]
        
        # 优先检查不想互动和关系变更，这些通常有明确表达
        for intent, keywords in {**{k: keyword_maps[k] for k in ["不想互动", "关系变更"] if k in keyword_maps}}.items():
            for keyword in keywords:
                if keyword in user_input:
                    result["main_intent"] = intent
                    result["confidence"] = 80
                    return result
        
        # 检查其他意图
        max_matches = 0
        main_intent = "默认"
        
        for intent, keywords in keyword_maps.items():
            matches = sum(1 for keyword in keywords if keyword in user_input)
            if matches > max_matches:
                max_matches = matches
                main_intent = intent
                # 如果匹配度高，提高置信度
                result["confidence"] = min(50 + matches * 10, 90)
        
        result["main_intent"] = main_intent
        
        # 检查是否需要实时数据
        if main_intent == "信息查询" or any(keyword in user_input for keyword in realtime_keywords):
            result["requires_realtime_data"] = True
            result["time_sensitivity"] = "高"
        
        # 检查情感倾向
        for emotion, keywords in emotion_keywords.items():
            if any(keyword in user_input for keyword in keywords):
                result["emotional_tendency"] = emotion
                break
        
        # 检查紧急程度
        for urgency, keywords in urgency_keywords.items():
            if any(keyword in user_input for keyword in keywords):
                result["urgency"] = urgency
                break
        
        return result
    
    def get_intent_categories(self) -> Dict[str, List[str]]:
        """获取意图类别"""
        return self.intent_categories
    
    def get_user_intent_history(self, user_id: str, count: int = 5) -> List[Dict[str, Any]]:
        """
        获取用户历史意图
        
        Args:
            user_id: 用户ID
            count: 返回的历史记录数量
            
        Returns:
            历史意图列表
        """
        # 这里可以接入数据库存储，本示例仅返回上下文中的内容
        context = self._get_user_context(user_id)
        
        # 提取用户意图历史
        intent_history = []
        for i in range(0, len(context), 2):
            if i+1 < len(context):
                user_input = context[i]["content"]
                analysis = context[i+1]["content"]
                
                # 简单解析分析结果
                main_intent = None
                sub_intent = None
                
                for line in analysis.split("\n"):
                    if line.startswith("主要意图:"):
                        main_intent = line.split(":", 1)[1].strip()
                    elif line.startswith("次要意图:"):
                        sub_intent = line.split(":", 1)[1].strip()
                
                intent_history.append({
                    "user_input": user_input,
                    "main_intent": main_intent,
                    "sub_intent": sub_intent,
                    "timestamp": time.time()  # 实际应用中应该存储真实的时间戳
                })
        
        # 返回最近的n条记录
        return intent_history[-count:]
    
    def clear_cache(self, user_id: str = None):
        """
        清除缓存
        
        Args:
            user_id: 用户ID，如果提供则只清除该用户的缓存
        """
        if user_id:
            # 清除特定用户的缓存
            keys_to_remove = []
            for key in self.intention_cache:
                if key.startswith(f"{user_id}:"):
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                if key in self.intention_cache:
                    del self.intention_cache[key]
                if key in self.intention_cache_time:
                    del self.intention_cache_time[key]
            
            # 清除用户上下文
            if user_id in self.user_contexts:
                del self.user_contexts[user_id]
                
            logger.info(f"已清除用户 {user_id} 的缓存")
        else:
            # 清除所有缓存
            self.intention_cache.clear()
            self.intention_cache_time.clear()
            self.user_contexts.clear()
            logger.info("已清除所有缓存")
    
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化或重新初始化模块
        
        Args:
            config: 新的配置信息
            
        Returns:
            初始化是否成功
        """
        if config:
            self.config = config
            
            # 更新配置项
            self.intention_cache_expiry = self.config.get('intention_cache_expiry', self.intention_cache_expiry)
            self.max_context_length = self.config.get('max_context_length', self.max_context_length)
            
            # 不再直接设置api_key和api_base，而是使用AI服务适配器的配置
            self.api_model = self.config.get('api_model', self.api_model)
            
            # 重新初始化AI接口
            self._initialize_ai_interface()
            
            logger.success("意图分析模块重新初始化完成")
            
        return True
    
    def shutdown(self) -> bool:
        """
        关闭模块，执行必要的清理工作
        
        Returns:
            关闭是否成功
        """
        # 清空缓存
        self.intention_cache.clear()
        self.intention_cache_time.clear()
        
        logger.info("意图分析模块已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> IntentionAnalysis:
    """
    获取意图分析模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        意图分析模块实例
    """
    return IntentionAnalysis(config) 