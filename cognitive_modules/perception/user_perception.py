"""
用户感知模块

负责感知和分析用户相关信息，包括用户基本信息、偏好、行为模式等。
从legacy系统的user_system演化而来，但增加了更多的用户洞察能力。
"""

import os
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
from typing import Dict, Any, List, Optional, Tuple

# 配置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

class UserPerception:
    """
    用户感知模块类
    
    负责感知和分析用户相关信息，包括：
    - 用户基本信息（ID、名称、年龄、性别等）
    - 用户偏好（兴趣、习惯、喜好等）
    - 用户行为模式（活跃时间、交互频率等）
    - 用户情绪状态（当前情绪、情绪变化等）
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化用户感知模块
        
        Args:
            config: 模块配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        self.config = config or {}
        
        # 用户数据存储路径
        self.users_dir = self.config.get('users_dir', os.path.join('data', 'users'))
        
        # 用户数据缓存
        self.users_cache = {}
        self.users_cache_time = {}
        self.users_cache_expiry = self.config.get('users_cache_expiry', 300)  # 默认5分钟
        
        # 用户活跃度数据
        self.activity_data = {}
        
        # 用户数据默认结构
        self.default_user_data = {
            "user_id": "",
            "name": "未知用户",
            "basic_info": {
                "gender": "未知",
                "age": 0,
                "location": "",
                "occupation": "",
                "created_at": "",
                "last_active": ""
            },
            "preferences": {},
            "activity": {
                "total_interactions": 0,
                "last_interaction": "",
                "average_daily_interactions": 0,
                "active_days": 0,
                "active_hours": []
            },
            "emotion": {
                "current": "中性",
                "history": []
            },
            "statistics": {
                "total_messages": 0,
                "average_message_length": 0,
                "topics_of_interest": {},
                "response_rate": 0
            }
        }
        
        # 确保用户目录存在
        if not os.path.exists(self.users_dir):
            try:
                os.makedirs(self.users_dir)
            except Exception as e:
                logger.error_status(f"创建用户数据目录失败: {e}")
        
        logger.success("用户感知模块初始化完成")
    
    def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息字典
        """
        # 检查缓存
        if user_id in self.users_cache:
            cache_time = self.users_cache_time.get(user_id, 0)
            if time.time() - cache_time < self.users_cache_expiry:
                logger.debug(f"使用缓存的用户数据: {user_id}")
                return self.users_cache[user_id]
        
        # 尝试从文件加载
        user_data = self._load_user_data(user_id)
        
        # 如果用户不存在，返回默认数据
        if not user_data:
            user_data = self.default_user_data.copy()
            user_data["user_id"] = user_id
            user_data["basic_info"]["created_at"] = datetime.datetime.now().isoformat()
            user_data["basic_info"]["last_active"] = datetime.datetime.now().isoformat()
        
        # 更新缓存
        self.users_cache[user_id] = user_data
        self.users_cache_time[user_id] = time.time()
        
        return user_data
    
    def _load_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """从文件加载用户数据"""
        user_file = os.path.join(self.users_dir, f"{user_id}.json")
        
        if os.path.exists(user_file):
            try:
                with open(user_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error_status(f"加载用户数据失败: {e}")
                return None
        else:
            logger.debug(f"用户数据文件不存在: {user_file}")
            return None
    
    def _save_user_data(self, user_id: str, user_data: Dict[str, Any]) -> bool:
        """保存用户数据到文件"""
        user_file = os.path.join(self.users_dir, f"{user_id}.json")
        
        try:
            with open(user_file, 'w', encoding='utf-8') as f:
                json.dump(user_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error_status(f"保存用户数据失败: {e}")
            return False
    
    def user_exists(self, user_id: str) -> bool:
        """
        检查用户是否存在
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户是否存在
        """
        return os.path.exists(os.path.join(self.users_dir, f"{user_id}.json"))
    
    def add_user(self, user_id: str, name: str, basic_info: Dict[str, Any] = None) -> bool:
        """
        添加新用户
        
        Args:
            user_id: 用户ID
            name: 用户名称
            basic_info: 用户基本信息
            
        Returns:
            是否添加成功
        """
        # 如果用户已存在，返回失败
        if self.user_exists(user_id):
            logger.warning_status(f"用户已存在: {user_id}")
            return False
        
        # 创建新用户数据
        user_data = self.default_user_data.copy()
        user_data["user_id"] = user_id
        user_data["name"] = name
        
        # 更新基本信息
        if basic_info:
            user_data["basic_info"].update(basic_info)
        
        # 设置创建时间和最后活跃时间
        current_time = datetime.datetime.now().isoformat()
        user_data["basic_info"]["created_at"] = current_time
        user_data["basic_info"]["last_active"] = current_time
        
        # 保存到文件
        success = self._save_user_data(user_id, user_data)
        
        # 更新缓存
        if success:
            self.users_cache[user_id] = user_data
            self.users_cache_time[user_id] = time.time()
        
        return success
    
    def update_user_info(self, user_id: str, info: Dict[str, Any]) -> bool:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            info: 要更新的用户信息
            
        Returns:
            是否更新成功
        """
        # 获取当前用户数据
        user_data = self.get_user_info(user_id)
        
        # 递归更新字典
        def update_dict_recursive(target, source):
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    update_dict_recursive(target[key], value)
                else:
                    target[key] = value
        
        # 更新用户数据
        update_dict_recursive(user_data, info)
        
        # 保存到文件
        success = self._save_user_data(user_id, user_data)
        
        # 更新缓存
        if success:
            self.users_cache[user_id] = user_data
            self.users_cache_time[user_id] = time.time()
        
        return success
    
    def record_interaction(self, user_id: str, message_type: str = "text", content_length: int = 0) -> bool:
        """
        记录用户交互
        
        Args:
            user_id: 用户ID
            message_type: 消息类型
            content_length: 内容长度
            
        Returns:
            是否记录成功
        """
        # 获取当前用户数据
        user_data = self.get_user_info(user_id)
        
        # 当前时间
        current_time = datetime.datetime.now()
        current_time_iso = current_time.isoformat()
        
        # 更新基本统计信息
        user_data["activity"]["total_interactions"] += 1
        user_data["activity"]["last_interaction"] = current_time_iso
        user_data["basic_info"]["last_active"] = current_time_iso
        user_data["statistics"]["total_messages"] += 1
        
        # 更新消息长度统计
        current_avg = user_data["statistics"]["average_message_length"]
        current_total = current_avg * (user_data["statistics"]["total_messages"] - 1)
        new_avg = (current_total + content_length) / user_data["statistics"]["total_messages"]
        user_data["statistics"]["average_message_length"] = round(new_avg, 2)
        
        # 更新活跃小时统计
        hour = current_time.hour
        if "active_hours" not in user_data["activity"]:
            user_data["activity"]["active_hours"] = [0] * 24
        
        if isinstance(user_data["activity"]["active_hours"], list) and len(user_data["activity"]["active_hours"]) == 24:
            user_data["activity"]["active_hours"][hour] += 1
        
        # 保存到文件
        success = self._save_user_data(user_id, user_data)
        
        # 更新缓存
        if success:
            self.users_cache[user_id] = user_data
            self.users_cache_time[user_id] = time.time()
        
        return success
    
    def get_active_users(self, minutes: int = 60) -> List[str]:
        """
        获取最近活跃的用户
        
        Args:
            minutes: 最近多少分钟内活跃
            
        Returns:
            活跃用户ID列表
        """
        active_users = []
        current_time = datetime.datetime.now()
        cutoff_time = current_time - datetime.timedelta(minutes=minutes)
        cutoff_time_iso = cutoff_time.isoformat()
        
        # 遍历用户文件
        for filename in os.listdir(self.users_dir):
            if filename.endswith('.json'):
                user_id = filename[:-5]  # 去掉.json后缀
                
                # 优先使用缓存
                if user_id in self.users_cache:
                    user_data = self.users_cache[user_id]
                    last_active = user_data["basic_info"]["last_active"]
                    if last_active > cutoff_time_iso:
                        active_users.append(user_id)
                    continue
                
                # 如果不在缓存中，读取文件
                try:
                    with open(os.path.join(self.users_dir, filename), 'r', encoding='utf-8') as f:
                        user_data = json.load(f)
                        last_active = user_data["basic_info"]["last_active"]
                        if last_active > cutoff_time_iso:
                            active_users.append(user_id)
                except Exception as e:
                    logger.error_status(f"读取用户数据失败: {e}")
        
        return active_users
    
    def get_user_preference(self, user_id: str, key: str, default: Any = None) -> Any:
        """
        获取用户偏好
        
        Args:
            user_id: 用户ID
            key: 偏好键名
            default: 默认值
            
        Returns:
            偏好值
        """
        user_data = self.get_user_info(user_id)
        return user_data["preferences"].get(key, default)
    
    def set_user_preference(self, user_id: str, key: str, value: Any) -> bool:
        """
        设置用户偏好
        
        Args:
            user_id: 用户ID
            key: 偏好键名
            value: 偏好值
            
        Returns:
            是否设置成功
        """
        user_data = self.get_user_info(user_id)
        user_data["preferences"][key] = value
        
        # 保存到文件
        success = self._save_user_data(user_id, user_data)
        
        # 更新缓存
        if success:
            self.users_cache[user_id] = user_data
            self.users_cache_time[user_id] = time.time()
        
        return success
    
    def analyze_user_activity(self, user_id: str) -> Dict[str, Any]:
        """
        分析用户活动模式
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户活动分析结果
        """
        user_data = self.get_user_info(user_id)
        
        # 计算活跃小时
        active_hours = user_data["activity"].get("active_hours", [0] * 24)
        if not isinstance(active_hours, list) or len(active_hours) != 24:
            active_hours = [0] * 24
        
        # 找出最活跃的时段
        max_hour = 0
        max_count = 0
        for hour, count in enumerate(active_hours):
            if count > max_count:
                max_count = count
                max_hour = hour
        
        # 确定活跃时段类型
        if 5 <= max_hour < 12:
            active_period = "上午"
        elif 12 <= max_hour < 18:
            active_period = "下午"
        elif 18 <= max_hour < 22:
            active_period = "晚上"
        else:
            active_period = "深夜"
        
        # 计算总交互次数
        total_interactions = user_data["activity"]["total_interactions"]
        
        # 计算平均每日交互次数
        created_time = datetime.datetime.fromisoformat(user_data["basic_info"]["created_at"])
        current_time = datetime.datetime.now()
        days_since_creation = max(1, (current_time - created_time).days)
        avg_daily = total_interactions / days_since_creation
        
        # 确定活跃度级别
        if avg_daily < 1:
            activity_level = "低活跃"
        elif avg_daily < 5:
            activity_level = "正常活跃"
        elif avg_daily < 20:
            activity_level = "高活跃"
        else:
            activity_level = "非常活跃"
        
        return {
            "most_active_hour": max_hour,
            "active_period": active_period,
            "total_interactions": total_interactions,
            "average_daily": round(avg_daily, 2),
            "activity_level": activity_level,
            "hourly_distribution": active_hours
        }
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """
        获取所有用户的基本信息
        
        Returns:
            用户基本信息列表
        """
        users = []
        
        # 遍历用户文件
        for filename in os.listdir(self.users_dir):
            if filename.endswith('.json'):
                user_id = filename[:-5]  # 去掉.json后缀
                
                try:
                    user_data = self.get_user_info(user_id)
                    users.append({
                        "user_id": user_id,
                        "name": user_data["name"],
                        "gender": user_data["basic_info"]["gender"],
                        "last_active": user_data["basic_info"]["last_active"],
                        "total_interactions": user_data["activity"]["total_interactions"]
                    })
                except Exception as e:
                    logger.error_status(f"读取用户数据失败: {e}")
        
        return users
    
    def search_users(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        根据条件搜索用户
        
        Args:
            criteria: 搜索条件
            
        Returns:
            匹配的用户列表
        """
        matching_users = []
        
        # 获取所有用户
        all_users = self.get_all_users()
        
        # 根据条件筛选
        for user in all_users:
            match = True
            
            for key, value in criteria.items():
                if key in user:
                    # 字符串模糊匹配
                    if isinstance(user[key], str) and isinstance(value, str):
                        if value.lower() not in user[key].lower():
                            match = False
                            break
                    # 精确匹配
                    elif user[key] != value:
                        match = False
                        break
                else:
                    # 如果条件字段不存在，获取完整用户数据检查
                    user_data = self.get_user_info(user["user_id"])
                    
                    # 检查嵌套字段
                    field_parts = key.split(".")
                    current = user_data
                    
                    for part in field_parts:
                        if isinstance(current, dict) and part in current:
                            current = current[part]
                        else:
                            match = False
                            break
                    
                    # 最终字段值检查
                    if match:
                        if isinstance(current, str) and isinstance(value, str):
                            if value.lower() not in current.lower():
                                match = False
                        elif current != value:
                            match = False
            
            if match:
                matching_users.append(user)
        
        return matching_users
    
    def clear_cache(self, user_id: str = None):
        """
        清除缓存
        
        Args:
            user_id: 用户ID，如果提供则只清除该用户的缓存
        """
        if user_id:
            # 清除特定用户的缓存
            if user_id in self.users_cache:
                del self.users_cache[user_id]
            if user_id in self.users_cache_time:
                del self.users_cache_time[user_id]
            logger.info(f"已清除用户 {user_id} 的缓存")
        else:
            # 清除所有缓存
            self.users_cache.clear()
            self.users_cache_time.clear()
            logger.info("已清除所有用户缓存")
    
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化或重新初始化模块
        
        Args:
            config: 新的配置信息
            
        Returns:
            初始化是否成功
        """
        if config:
            self.config = config
            
            # 更新配置项
            self.users_dir = self.config.get('users_dir', self.users_dir)
            self.users_cache_expiry = self.config.get('users_cache_expiry', self.users_cache_expiry)
            
            # 确保用户目录存在
            if not os.path.exists(self.users_dir):
                try:
                    os.makedirs(self.users_dir)
                except Exception as e:
                    logger.error_status(f"创建用户数据目录失败: {e}")
                    return False
            
            logger.success("用户感知模块重新初始化完成")
            
        return True
    
    def shutdown(self) -> bool:
        """
        关闭模块，执行必要的清理工作
        
        Returns:
            关闭是否成功
        """
        # 清空缓存
        self.users_cache.clear()
        self.users_cache_time.clear()
        
        logger.info("用户感知模块已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> UserPerception:
    """
    获取用户感知模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        用户感知模块实例
    """
    return UserPerception(config) 