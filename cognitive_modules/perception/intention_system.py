#!/usr/bin/env python3
"""
意图分析系统 - AI驱动的语义意图识别

基于AI服务的智能意图分析，支持复杂语义理解和上下文感知。
实现了完整的意图分类体系和置信度评估。

作者: 数字生命体开发团队
版本: 2.0.0
"""

import sys
import os
import json
import time
from datetime import datetime, date
from typing import Dict, Any, List, Optional
from collections import defaultdict

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "..", ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from utilities.unified_logger import get_unified_logger
from core.ai_service_connection_pool import get_ai_service_connection_pool

logger = get_unified_logger(__name__)

class IntentionSystem:
    """
    AI驱动的意图分析系统
    
    支持功能：
    - 基于AI的语义意图识别
    - 多维度意图分析（主要/次要意图、置信度、时间敏感度等）
    - 上下文感知的意图推理
    - 实时数据需求判断
    - 意图历史记录和模式分析
    """
    
    def __init__(self):
        """初始化意图分析系统"""
        self.history_messages = defaultdict(list)
        self.context_memory = {}
        
        # 初始化AI服务连接 - 使用更简单直接的AI服务适配器
        try:
            from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
            self.ai_adapter = get_ai_service_adapter()
            logger.success(f"AI服务适配器获取完成")
        except Exception as e:
            logger.error_status(f"AI服务适配器获取失败: {e}")
            self.ai_adapter = None
        
        current_time_now = datetime.now()
        formatted_time_now = current_time_now.strftime("%Y-%m-%d %H:%M:%S")

        # 意图分析的系统提示词模板
        self.intention_system_prompt_template = f"""
# 用户意图分析

你叫嫣然，一个特定功能的先进语意分析助手，专门负责全面分析用户输入，识别多种可能的意图。请根据提供的辅助信息，仔细分析用户的每一句话，并确定用户的主要意图类别以及可能的次要意图。

## 辅助信息

在进行分析时，请考虑以下提供的辅助信息：

1. 'now_time': 当前的准确时间：{formatted_time_now}
2. 'user_location': 中国，亚太
3. 'user_timezone': CST(UTC+8)
4. 'user_language': zh_CN
5. 'device_type': 手机/平板/电脑
6. 'app_context': WeChat
7. 'user_preferences': 普通聊天/需要联网查询服务
8. 'recent_queries': -
9. 'global_events': -
10. 'user_expertise': -

## 意图类别

1. 信息查询(需要实时数据)
   - 实时信息需求（比如：天气、交通、新闻、路线规划、问题咨询）
   - 历史或固定信息查询（如历史事件、科学知识）
   - 解释或定义请求
   - 人物身份查询（如"这俩是不是同一个人"、"某某是谁"等）

2. 任务执行
   - 简单计算或转换（如货币转换、单位换算）
   - 复杂计算或分析请求
   - 创建或修改内容（如写作、编码）

3. 推荐请求
   - 产品推荐
   - 地点或活动推荐
   - 决策支持

4. 导航或指引
   - 地理导航
   - 流程指导
   - 故障排除

5. 交互或对话
   - 闲聊或社交互动(问与嫣然有关的事项时)
   - 情感支持或倾听
   - 角色扮演
   - 模拟对话

6. 个人助理任务
   - 日程安排或提醒
   - 个人管理（如待办事项、笔记）
   - 设置或偏好调整

7. 学习或教育
   - 知识获取
   - 技能学习
   - 考试或测试准备

8. 创意或娱乐
   - 故事或内容创作
   - 游戏或益智活动
   - 艺术或音乐相关请求

9. 商业或专业
   - 市场分析
   - 专业建议（法律、财务等）
   - 商业策略

10. 技术支持
    - 软件/硬件问题解决
    - 使用指导
    - 兼容性查询

11. 绘画创作
    - 画图/需要提供自拍照/绘画艺术等图片创作的请求（不需要联网）

12. 网址/链接解读
    - 需要联网查看网址/链接里的内容
    - 注意：仅当输入包含实际网址（如http、www、.com等）时才使用此类别

13. 不希望聊天/不希望互动
    - 用户表达不想聊天，或者让"嫣然"这个角色保持安静
    
14. 拉黑好友/删除好友
    - 表达要删除"嫣然"微信好友，或者拉黑"嫣然"

15. 默认
    - 前11点未列出，默认不需要联网

## 分析步骤

1. 仔细阅读用户输入的全文。
2. 考虑所有提供的辅助信息，评估它们与用户查询的相关性。
3. 识别关键词、短语和语气，它们可能暗示用户的意图。
4. 评估查询的复杂性、时效性、个人化程度等特征。
5. 考虑用户的语言、文化背景和专业水平对意图的影响。
6. 分析用户可能的显式和隐式意图（如果分析意图为闲聊或社交互动时，大多数情况下不需要联网，比如输入：'嫣然 这才早上吧，就忙完了？' 虽然提及时间，但不需要实时数据）。
7. 确定主要意图类别和可能的次要意图类别。
8. 评估是否需要额外的上下文或信息来准确理解用户意图。
9. 评估是否需要联网查询实时数据做为支撑（比如：天气、交通、新闻、路线规划、问题咨询类、信息查询类，内容中有网址的，就需要实时数据）。
10. **特殊要求：对于给你发送的图片/视频消息时，需要根据内容分析具体意图：
    - 如果内容描述的是人物、场景、物品等，主要意图为"内容分析"或"视觉理解"
    - 如果内容描述的是动作、事件等，主要意图为"行为分析"或"事件理解"
    - 只有在无法确定具体意图时，才使用"默认"意图**

## 注意，必须要按以下格式输出，禁止改变

主要意图类别: [从上述类别中选择]
次要意图类别: [如果有，可以列出1-2个]
意图细分: [主要意图的具体子类别]
置信度: [0-100的数字，表示您对判断的确信程度]
是否需要实时数据: [是/否]
时间敏感度: [极高/高/中/低/无]

## 示例
假设辅助信息如下：
- 'now_time': 2025-06-01 14:30:00
- 'user_location': 纽约，美国
- 'user_timezone': EDT (UTC-4)
- 'user_language': 中文
- 'device_type': 智能手机
- 'app_context': 生产力应用
- 'user_preferences': 科技新闻、健康饮食
- 'recent_queries': ["如何提高工作效率", "最佳番茄工作法应用"]
- 'global_events': 全球科技创新峰会即将在纽约举行
- 'user_expertise': 中级程序员

用户输入: "你能推荐一些提高工作效率的方法吗？最好包括一些利用AI的技巧。"

输出:
主要意图类别: 推荐请求
次要意图类别: 学习或教育
意图细分: 工作效率提升方法推荐
置信度: 95
是否需要实时数据: 否
时间敏感度: 低

请根据以上指南分析，并根据用户历史多轮输入，然后提供您的全面意图判断。在分析过程中，请综合考虑所有提供的辅助信息,禁止输出除格式输出以外的任何信息！
        """
        
        logger.success("意图分析系统初始化完成")
    
    def _trim_history(self, history):
        """修剪历史记录以保持合适的长度"""
        max_history_length = 4  # 最大历史长度

        if not history:
            return
            
        # 根据模型特定逻辑修剪历史记录
        if history and history[0]["role"] == "system":
            while len(history) > max_history_length:
                # 确保至少有3条历史记录
                if len(history) > 3:
                    logger.debug("移除2条历史记录")
                    history[:] = history[:1] + history[3:]
                else:
                    break
        else:
            while len(history) > max_history_length - 1:
                if len(history) > 2:
                    logger.debug("移除2条历史记录")
                    history[:] = history[2:]
                else:
                    break

    def analyze_intention(self, user_input: str, user_id: str = None) -> Optional[Dict[str, Any]]:
        """
        分析用户意图
        
        Args:
            user_input: 用户输入文本
            user_id: 用户ID
            
        Returns:
            意图分析结果字典，包含：
            - main_intent: 主要意图
            - sub_intent: 次要意图  
            - detailed_intent: 意图细分
            - confidence: 置信度(0-100)
            - requires_realtime_data: 是否需要实时数据
            - time_sensitivity: 时间敏感度
        """
        if not user_input or not user_input.strip():
            return None
            
        current_time_now = datetime.now()
        formatted_time_now = current_time_now.strftime("%Y-%m-%d %H:%M:%S")
        
        # 构建系统提示词
        intention_system_prompt = self.intention_system_prompt_template.format(
            formatted_time=formatted_time_now
        )
        
        user_inputs = "用户输入的是:\n" + user_input
        logger.debug(f"分析用户意图: {user_input[:50]}...")
        
        # 管理用户上下文记忆
        if user_id not in self.context_memory:
            self.context_memory[user_id] = []
        if len(self.context_memory[user_id]) > 4:  # 限制为4次记忆
            self.context_memory[user_id].pop(0)
            
        messages = [{"role": "system", "content": intention_system_prompt}]
        
        try:
            # 添加用户消息到上下文
            self.context_memory[user_id].append({"role": "user", "content": user_input})
            messages.extend(self.context_memory[user_id])
            
            # 调用AI服务进行意图分析
            if self.ai_adapter:
                try:
                    # 使用AI服务适配器的标准接口
                    response = self.ai_adapter.get_completion(
                        messages=messages,
                        model="abab6.5s-chat",
                        temperature=0.3,
                        max_tokens=500,
                        timeout=30  # 设置30秒超时，避免长时间等待
                    )
                    
                    # 处理AI服务适配器的响应
                    intention_text = ""
                    if response:
                        if isinstance(response, str):
                            # 如果是字符串，直接使用
                            intention_text = response.strip()
                        elif isinstance(response, dict):
                            # 如果是字典，尝试提取content
                            if "choices" in response and len(response["choices"]) > 0:
                                choice = response["choices"][0]
                                if "message" in choice and "content" in choice["message"]:
                                    intention_text = choice["message"]["content"].strip()
                            elif "content" in response:
                                intention_text = response["content"].strip()
                            else:
                                logger.warning_status(f"未知的响应格式: {response}")
                    
                    if intention_text:
                        logger.debug(f"AI意图分析结果: {intention_text[:100]}...")
                        
                        # 解析AI返回的结果
                        intention = self._parse_intention_result(intention_text)
                        
                        # 将助手的消息添加到上下文记忆中
                        self.context_memory[user_id].append({
                            "role": "assistant", 
                            "content": intention_text
                        })
                        
                        return intention
                    else:
                        logger.warning_status("AI服务返回空内容")
                        
                except Exception as ai_error:
                    logger.error_status(f"AI服务调用异常: {ai_error}")
                    
            # AI服务不可用时，回退到基础规则分析
            logger.warning_status("AI服务不可用，使用基础规则分析")
            return self._fallback_rule_analysis(user_input)
            
        except Exception as e:
            logger.error_status(f"意图分析异常: {e}")
            # 如果发生异常，从上下文记忆中移除最近添加的用户消息
            if (self.context_memory[user_id] and 
                self.context_memory[user_id][-1]["role"] == "user"):
                self.context_memory[user_id].pop()
            
            # 回退到基础规则分析
            return self._fallback_rule_analysis(user_input)
    
    def _parse_intention_result(self, intention_text: str) -> Dict[str, Any]:
        """
        解析AI返回的意图分析结果
        
        Args:
            intention_text: AI返回的文本结果
            
        Returns:
            解析后的意图字典
        """
        intention = {
            "main_intent": None,
            "sub_intent": None,
            "detailed_intent": None,
            "confidence": 0,
            "requires_realtime_data": False,
            "time_sensitivity": "低"
        }

        lines = intention_text.split("\n")
        for line in lines:
            if "：" in line or ":" in line:
                key, value = line.replace("：", ":").split(":", 1)
                key = key.strip()
                value = value.strip()
                
                if key == "主要意图类别":
                    intention["main_intent"] = value
                elif key == "次要意图类别":
                    intention["sub_intent"] = value
                elif key == "意图细分":
                    intention["detailed_intent"] = value
                elif key == "置信度":
                    try:
                        intention["confidence"] = int(value)
                    except ValueError:
                        intention["confidence"] = 0
                elif key == "是否需要实时数据":
                    intention["requires_realtime_data"] = value == "是"
                elif key == "时间敏感度":
                    intention["time_sensitivity"] = value
                    
        return intention
    
    def _fallback_rule_analysis(self, user_input: str) -> Dict[str, Any]:
        """
        基础规则分析（AI服务不可用时的回退方案）
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            基础的意图分析结果
        """
        logger.info("使用基础规则进行意图分析")
        
        # 视频/图片消息检测（最高优先级）
        media_keywords = ["视频消息", "图片消息", "给你发了一条视频", "给你发了一张图片", "📹", "🖼️"]
        for keyword in media_keywords:
            if keyword in user_input:
                # 分析内容类型
                if any(word in user_input for word in ["连衣裙", "女士", "男士", "人物", "服装", "造型"]):
                    return {
                        "main_intent": "视觉内容分析",
                        "sub_intent": "人物造型分析",
                        "detailed_intent": "时尚造型内容理解",
                        "confidence": 95,
                        "requires_realtime_data": False,
                        "time_sensitivity": "低"
                    }
                elif any(word in user_input for word in ["行走", "动作", "姿态", "展示", "表演"]):
                    return {
                        "main_intent": "行为内容分析",
                        "sub_intent": "动作行为分析",
                        "detailed_intent": "动态行为内容理解",
                        "confidence": 95,
                        "requires_realtime_data": False,
                        "time_sensitivity": "低"
                    }
                else:
                    return {
                        "main_intent": "视觉内容分析",
                        "sub_intent": "通用内容分析",
                        "detailed_intent": "多媒体内容理解",
                        "confidence": 90,
                        "requires_realtime_data": False,
                        "time_sensitivity": "低"
                    }
        
        # 绘画关键词检测（次优先级）
        drawing_keywords = ["画一张", "画一幅"]
        for keyword in drawing_keywords:
            if keyword in user_input:
                return {
                    "main_intent": "绘画创作",
                    "sub_intent": None,
                    "detailed_intent": "图片创作请求",
                    "confidence": 90,
                    "requires_realtime_data": False,
                    "time_sensitivity": "低"
                }
        
        # 实时信息需求检测
        realtime_keywords = [ "最新", "实时", "查询", "搜索"]
        for keyword in realtime_keywords:
            if keyword in user_input:
                return {
                    "main_intent": "信息查询",
                    "sub_intent": None,
                    "detailed_intent": "实时信息需求",
                    "confidence": 85,
                    "requires_realtime_data": True,
                    "time_sensitivity": "高"
                }
        
        # 网址链接检测
        if "http" in user_input or "www" in user_input or ".com" in user_input:
            return {
                "main_intent": "网址/链接解读",
                "sub_intent": None,
                "detailed_intent": "链接内容分析",
                "confidence": 95,
                "requires_realtime_data": True,
                "time_sensitivity": "中"
            }
        
        # 推荐请求检测
        recommendation_keywords = ["推荐", "建议", "有什么好的", "什么比较好"]
        for keyword in recommendation_keywords:
            if keyword in user_input:
                return {
                    "main_intent": "推荐请求",
                    "sub_intent": None,
                    "detailed_intent": "决策支持",
                    "confidence": 80,
                    "requires_realtime_data": False,
                    "time_sensitivity": "低"
                }
        
        # 默认为聊天对话
        return {
            "main_intent": "交互或对话",
            "sub_intent": None,
            "detailed_intent": "闲聊或社交互动",
            "confidence": 70,
            "requires_realtime_data": False,
            "time_sensitivity": "无"
        }

# 单例实例
_intention_system_instance = None

def get_instance() -> IntentionSystem:
    """获取意图分析系统单例实例"""
    global _intention_system_instance
    if _intention_system_instance is None:
        _intention_system_instance = IntentionSystem()
    return _intention_system_instance 