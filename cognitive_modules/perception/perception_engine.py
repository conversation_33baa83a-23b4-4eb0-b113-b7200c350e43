# 感知引擎模块
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import asyncio
import time
import threading
from typing import Dict, Any, List, Optional

# 单例模式
_instance = None

class PerceptionEngine:
    """感知引擎类，整合各种感知能力"""
    
    def __init__(self):
        """初始化感知引擎"""
        self.logger = get_unified_logger("perception_engine")
        self.logger.success("初始化感知引擎...")
        
        # 已加载的感知子模块
        self.modules = {}
        self.active_modules = set()
        
        # 感知状态
        self.perception_state = {
            "modules_loaded": 0,
            "modules_active": 0,
            "last_perception_time": 0,
            "perception_quality": 0.0,
            "integration_level": 0.0
        }
        
        # 事件总线
        try:
            from core.enhanced_event_bus import get_instance as get_event_bus
            self.event_bus = get_event_bus()
            self.logger.success("已连接事件总线")
            
            # 注册事件处理器
            self.event_bus.subscribe("perception.input", self._handle_perception_input)
            self.event_bus.subscribe("system.status_check", self._handle_status_check)
        except Exception as e:
            self.logger.warning_status(f"连接事件总线失败: {e}，部分功能可能受限")
            self.event_bus = None
        
        # 初始化感知子模块
        self._initialize_perception_modules()
        
        # 启动感知监控
        self._start_perception_monitoring()
        
        # 初始化完成标志
        self.initialized = True
        self.logger.success("感知引擎初始化完成")
    
    def _initialize_perception_modules(self):
        """初始化感知子模块"""
        try:
            # 加载用户感知模块
            try:
                from cognitive_modules.perception.enhanced_user_perception import get_instance as get_user_perception
                self.modules["user_perception"] = get_user_perception()
                self.logger.success("用户感知模块加载成功")
            except Exception as e:
                self.logger.warning_status(f"用户感知模块加载失败: {e}")
            
            # 加载环境感知模块
            try:
                from cognitive_modules.perception.environmental_perception import get_instance as get_env_perception
                self.modules["environmental_perception"] = get_env_perception()
                self.logger.success("环境感知模块加载成功")
            except Exception as e:
                self.logger.warning_status(f"环境感知模块加载失败: {e}")
            
            # 加载活动感知模块
            try:
                from cognitive_modules.perception.activity_perception import get_instance as get_activity_perception
                self.modules["activity_perception"] = get_activity_perception()
                self.logger.success("活动感知模块加载成功")
            except Exception as e:
                self.logger.warning_status(f"活动感知模块加载失败: {e}")
            
            # 加载多模态感知模块
            try:
                from cognitive_modules.perception.multimodal_perception import get_instance as get_multimodal_perception
                self.modules["multimodal_perception"] = get_multimodal_perception()
                self.logger.success("多模态感知模块加载成功")
            except Exception as e:
                self.logger.warning_status(f"多模态感知模块加载失败: {e}")
            
            # 加载数据感知模块
            try:
                from cognitive_modules.perception.data_perception import get_instance as get_data_perception
                self.modules["data_perception"] = get_data_perception()
                self.logger.success("数据感知模块加载成功")
            except Exception as e:
                self.logger.warning_status(f"数据感知模块加载失败: {e}")
            
            # 更新感知状态
            self.perception_state["modules_loaded"] = len(self.modules)
            self.logger.success(f"感知子模块加载完成，共加载 {len(self.modules)} 个模块")
            
        except Exception as e:
            self.logger.error_status(f"感知子模块初始化失败: {e}")
    
    def _start_perception_monitoring(self):
        """启动感知监控"""
        def monitoring_loop():
            while True:
                try:
                    self._update_perception_state()
                    time.sleep(30)  # 每30秒更新一次
                except Exception as e:
                    self.logger.error_status(f"感知监控异常: {e}")
                    time.sleep(60)
        
        thread = threading.Thread(target=monitoring_loop, daemon=True)
        thread.start()
        self.logger.info("感知监控已启动")
    
    def _update_perception_state(self):
        """更新感知状态"""
        try:
            # 检查模块活跃状态
            active_count = 0
            total_quality = 0.0
            
            for name, module in self.modules.items():
                try:
                    if hasattr(module, 'is_active') and module.is_active:
                        active_count += 1
                        self.active_modules.add(name)
                    elif hasattr(module, 'get_state'):
                        state = module.get_state()
                        if state and state.get('active', False):
                            active_count += 1
                            self.active_modules.add(name)
                    
                    # 计算感知质量
                    if hasattr(module, 'get_perception_quality'):
                        quality = module.get_perception_quality()
                        total_quality += quality
                    else:
                        total_quality += 0.7  # 默认质量
                        
                except Exception as e:
                    self.logger.debug(f"检查模块 {name} 状态失败: {e}")
            
            # 更新状态
            self.perception_state.update({
                "modules_active": active_count,
                "perception_quality": total_quality / len(self.modules) if self.modules else 0.0,
                "integration_level": active_count / len(self.modules) if self.modules else 0.0,
                "last_perception_time": time.time()
            })
            
        except Exception as e:
            self.logger.error_status(f"更新感知状态失败: {e}")
    
    def _handle_perception_input(self, event_data):
        """处理感知输入事件"""
        try:
            self.logger.debug(f"处理感知输入: {event_data}")
            
            # 分发到各个感知模块
            for name, module in self.modules.items():
                try:
                    if hasattr(module, 'process_input'):
                        asyncio.create_task(module.process_input(event_data))
                    elif hasattr(module, 'handle_input'):
                        module.handle_input(event_data)
                except Exception as e:
                    self.logger.debug(f"模块 {name} 处理输入失败: {e}")
            
        except Exception as e:
            self.logger.error_status(f"处理感知输入失败: {e}")
    
    def _handle_status_check(self, event_data):
        """处理状态检查事件"""
        try:
            self._update_perception_state()
            
            # 发布状态更新事件
            if self.event_bus:
                self.event_bus.publish("perception.status_updated", self.perception_state)
                
        except Exception as e:
            self.logger.error_status(f"处理状态检查失败: {e}")
    
    async def process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据"""
        try:
            self.logger.data_status(f"感知引擎处理输入: {input_data}")
            
            # 分发到各个感知模块
            results = {}
            for name, module in self.modules.items():
                try:
                    if hasattr(module, 'process_input'):
                        result = await module.process_input(input_data)
                        results[name] = result
                    elif hasattr(module, 'handle_input'):
                        result = module.handle_input(input_data)
                        results[name] = result
                except Exception as e:
                    self.logger.debug(f"模块 {name} 处理失败: {e}")
            
            # 通过事件总线发布结果
            if self.event_bus:
                self.event_bus.publish("perception.processed", {
                    "input_data": input_data,
                    "results": results,
                    "timestamp": time.time()
                })
            
            return {
                "success": True,
                "results": results,
                "modules_processed": len(results)
            }
            
        except Exception as e:
            self.logger.error_status(f"感知引擎处理输入失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_perception_state(self) -> Dict[str, Any]:
        """获取感知状态"""
        return self.perception_state.copy()
    
    def get_active_modules(self) -> List[str]:
        """获取活跃模块列表"""
        return list(self.active_modules)
    
    def get_module_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        status = {}
        for name, module in self.modules.items():
            try:
                if hasattr(module, 'get_state'):
                    status[name] = module.get_state()
                elif hasattr(module, 'is_active'):
                    status[name] = {"active": module.is_active}
                else:
                    status[name] = {"loaded": True}
            except Exception as e:
                status[name] = {"error": str(e)}
        
        return status

    def get_perception_result(self, input_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        🔥 香草修复：获取感知结果

        Args:
            input_data: 输入数据（可选）

        Returns:
            Dict[str, Any]: 感知结果
        """
        try:
            # 如果有输入数据，先处理
            if input_data:
                # 同步处理输入数据
                results = {}
                for name, module in self.modules.items():
                    try:
                        if hasattr(module, 'process_input'):
                            # 对于异步方法，使用同步包装
                            import asyncio
                            try:
                                loop = asyncio.get_event_loop()
                                result = loop.run_until_complete(module.process_input(input_data))
                            except RuntimeError:
                                # 如果没有事件循环，创建新的
                                result = asyncio.run(module.process_input(input_data))
                            results[name] = result
                        elif hasattr(module, 'handle_input'):
                            result = module.handle_input(input_data)
                            results[name] = result
                    except Exception as e:
                        self.logger.debug(f"模块 {name} 处理失败: {e}")
                        results[name] = {"error": str(e)}
            else:
                # 获取当前状态
                results = self.get_module_status()

            # 构建感知结果
            perception_result = {
                "timestamp": time.time(),
                "perception_state": self.perception_state.copy(),
                "module_results": results,
                "active_modules": list(self.active_modules),
                "perception_quality": self.perception_state.get("perception_quality", 0.0),
                "integration_level": self.perception_state.get("integration_level", 0.0)
            }

            return perception_result

        except Exception as e:
            self.logger.error_status(f"获取感知结果失败: {e}")
            return {
                "error": str(e),
                "timestamp": time.time(),
                "perception_state": self.perception_state.copy()
            }

    def start_monitoring(self, interval: float = 30.0) -> bool:
        """
        🔥 香草修复：启动感知监控

        Args:
            interval: 监控间隔（秒）

        Returns:
            bool: 启动成功返回True
        """
        try:
            self.logger.info(f"启动感知监控，间隔: {interval}秒")

            # 检查是否已经在监控
            if hasattr(self, '_monitoring_active') and self._monitoring_active:
                self.logger.warning_status("感知监控已在运行中")
                return True

            # 启动监控线程
            def enhanced_monitoring_loop():
                self._monitoring_active = True
                while self._monitoring_active:
                    try:
                        # 更新感知状态
                        self._update_perception_state()

                        # 检查模块健康状态
                        self._check_module_health()

                        # 发布监控事件
                        if self.event_bus:
                            self.event_bus.publish("perception.monitoring_update", {
                                "timestamp": time.time(),
                                "state": self.perception_state.copy(),
                                "active_modules": list(self.active_modules)
                            })

                        time.sleep(interval)

                    except Exception as e:
                        self.logger.error_status(f"感知监控异常: {e}")
                        time.sleep(min(interval, 60))  # 出错时最多等待60秒

            # 启动监控线程
            self._monitoring_thread = threading.Thread(target=enhanced_monitoring_loop, daemon=True)
            self._monitoring_thread.start()

            self.logger.success("感知监控已启动")
            return True

        except Exception as e:
            self.logger.error_status(f"启动感知监控失败: {e}")
            return False

    def _check_module_health(self):
        """检查模块健康状态"""
        try:
            unhealthy_modules = []

            for name, module in self.modules.items():
                try:
                    # 检查模块是否响应
                    if hasattr(module, 'health_check'):
                        if not module.health_check():
                            unhealthy_modules.append(name)
                    elif hasattr(module, 'get_state'):
                        state = module.get_state()
                        if not state or state.get('error'):
                            unhealthy_modules.append(name)
                except Exception as e:
                    self.logger.debug(f"模块 {name} 健康检查失败: {e}")
                    unhealthy_modules.append(name)

            # 如果有不健康的模块，记录警告
            if unhealthy_modules:
                self.logger.warning_status(f"检测到不健康的感知模块: {unhealthy_modules}")

                # 发布健康状态事件
                if self.event_bus:
                    self.event_bus.publish("perception.health_warning", {
                        "unhealthy_modules": unhealthy_modules,
                        "timestamp": time.time()
                    })

        except Exception as e:
            self.logger.error_status(f"模块健康检查失败: {e}")

def get_instance():
    """获取感知引擎实例（同步版本）"""
    global _instance
    if _instance is None:
        _instance = PerceptionEngine()
    return _instance
