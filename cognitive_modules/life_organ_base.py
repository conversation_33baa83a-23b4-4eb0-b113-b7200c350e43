#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生命器官基类

为所有生命器官提供基础功能和接口定义。

作者: Claude
创建日期: 2025-06-30
版本: 1.0
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional
from utilities.unified_logger import get_unified_logger

logger = get_unified_logger("life_organ_base")

class LifeOrganBase(ABC):
    """生命器官基类"""
    
    def __init__(self, organ_name: str, description: str, dependencies: List[str] = None):
        """
        初始化生命器官
        
        Args:
            organ_name: 器官名称
            description: 器官描述
            dependencies: 依赖的其他器官列表
        """
        self.organ_name = organ_name
        self.description = description
        self.dependencies = dependencies or []
        self.is_active = False
        self.created_at = datetime.now()
        self.last_activity = None
        self.error_count = 0
        self.activity_count = 0
        
        logger.info(f"器官 {organ_name} 初始化完成: {description}")
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """
        获取器官状态
        
        Returns:
            器官状态信息
        """
        pass
    
    def activate(self) -> bool:
        """激活器官"""
        try:
            self.is_active = True
            self.last_activity = datetime.now()
            logger.success(f"器官 {self.organ_name} 已激活")
            return True
        except Exception as e:
            logger.error(f"激活器官 {self.organ_name} 失败: {e}")
            self.error_count += 1
            return False
    
    def deactivate(self) -> bool:
        """停用器官"""
        try:
            self.is_active = False
            logger.info(f"器官 {self.organ_name} 已停用")
            return True
        except Exception as e:
            logger.error(f"停用器官 {self.organ_name} 失败: {e}")
            self.error_count += 1
            return False
    
    def record_activity(self):
        """记录器官活动"""
        self.last_activity = datetime.now()
        self.activity_count += 1
    
    def get_basic_status(self) -> Dict[str, Any]:
        """获取基础状态信息"""
        return {
            "organ_name": self.organ_name,
            "description": self.description,
            "is_active": self.is_active,
            "dependencies": self.dependencies,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "error_count": self.error_count,
            "activity_count": self.activity_count,
            "uptime": (datetime.now() - self.created_at).total_seconds()
        }
    
    def check_dependencies(self, available_organs: List[str]) -> List[str]:
        """
        检查依赖关系
        
        Args:
            available_organs: 可用器官列表
            
        Returns:
            缺失的依赖器官列表
        """
        missing_deps = []
        for dep in self.dependencies:
            if dep not in available_organs:
                missing_deps.append(dep)
        
        if missing_deps:
            logger.warning(f"器官 {self.organ_name} 缺少依赖: {missing_deps}")
        
        return missing_deps
    
    def shutdown(self):
        """关闭器官"""
        try:
            self.deactivate()
            logger.info(f"器官 {self.organ_name} 已关闭")
        except Exception as e:
            logger.error(f"关闭器官 {self.organ_name} 失败: {e}")
            self.error_count += 1

class AdvancedLifeOrganBase(LifeOrganBase):
    """高级生命器官基类"""
    
    def __init__(self, organ_name: str, description: str, dependencies: List[str] = None):
        """初始化高级生命器官"""
        super().__init__(organ_name, description, dependencies)
        
        self.metrics = {
            "performance_score": 0.7,
            "efficiency_rating": 0.8,
            "reliability_score": 0.9,
            "last_performance_check": None
        }
        
        self.configuration = {}
        self.state_history = []
        
    def update_metrics(self, performance: float = None, efficiency: float = None, reliability: float = None):
        """更新器官指标"""
        if performance is not None:
            self.metrics["performance_score"] = max(0.0, min(1.0, performance))
        
        if efficiency is not None:
            self.metrics["efficiency_rating"] = max(0.0, min(1.0, efficiency))
        
        if reliability is not None:
            self.metrics["reliability_score"] = max(0.0, min(1.0, reliability))
        
        self.metrics["last_performance_check"] = datetime.now()
        self.record_activity()
    
    def save_state(self, state_data: Dict[str, Any]):
        """保存器官状态"""
        state_record = {
            "timestamp": datetime.now(),
            "state": state_data.copy(),
            "metrics": self.metrics.copy()
        }
        
        self.state_history.append(state_record)
        
        # 保留最近100条状态记录
        if len(self.state_history) > 100:
            self.state_history = self.state_history[-100:]
    
    def get_state_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取状态历史"""
        return self.state_history[-limit:]
    
    def configure(self, config: Dict[str, Any]) -> bool:
        """配置器官"""
        try:
            self.configuration.update(config)
            logger.info(f"器官 {self.organ_name} 配置已更新")
            self.record_activity()
            return True
        except Exception as e:
            logger.error(f"配置器官 {self.organ_name} 失败: {e}")
            self.error_count += 1
            return False
    
    def get_advanced_status(self) -> Dict[str, Any]:
        """获取高级状态信息"""
        basic_status = self.get_basic_status()
        basic_status.update({
            "metrics": self.metrics.copy(),
            "configuration": self.configuration.copy(),
            "state_history_count": len(self.state_history),
            "health_score": self._calculate_health_score()
        })
        return basic_status
    
    def _calculate_health_score(self) -> float:
        """计算健康评分"""
        if not self.is_active:
            return 0.0
        
        # 基于各项指标计算健康评分
        performance = self.metrics["performance_score"]
        efficiency = self.metrics["efficiency_rating"]
        reliability = self.metrics["reliability_score"]
        
        # 错误率影响
        error_factor = max(0.1, 1.0 - (self.error_count * 0.1))
        
        # 活动频率影响
        activity_factor = min(1.0, self.activity_count / 100.0)
        
        health_score = (performance * 0.3 + efficiency * 0.3 + reliability * 0.4) * error_factor * activity_factor
        
        return max(0.0, min(1.0, health_score))
    
    def get_status(self) -> Dict[str, Any]:
        """获取器官状态（实现抽象方法）"""
        return self.get_advanced_status()

# 向后兼容的别名
LifeOrgan = LifeOrganBase 