"""
增强版记忆集成模块
实现Redis记忆系统的深度集成，优化记忆获取效率，增强情景记忆关联分析
Phase 2: 记忆-思维集成的核心组件
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from connectors.database.redis_cluster_connector import get_instance as get_redis_connector
from connectors.database.mysql_connector import get_instance as get_mysql_connector

logger = get_unified_logger(__name__)

class EnhancedMemoryIntegration:
    """增强版记忆集成器"""
    
    def __init__(self):
        self.redis_connector = None
        self.mysql_connector = None
        self.is_initialized = False
        
        # Redis数据结构键名
        self.redis_keys = {
            "proactive_behavior": "proactive_behavior:{user_id}",
            "user_boundaries": "user_boundaries:{user_id}",
            "conversation_history": "conversation_history:{user_id}",
            "emotion_states": "emotion_states:{user_id}",
            "user_preferences": "user_preferences:{user_id}",
            "interaction_stats": "interaction_stats:{user_id}",
            "memory_consolidation": "memory_consolidation:{user_id}"
        }
        
        # 记忆权重配置（优化版）
        self.memory_weights = {
            "redis_recent_conversations": 0.35,    # 最近对话记忆
            "redis_emotional_context": 0.25,      # 情感上下文记忆
            "redis_behavioral_patterns": 0.20,    # 行为模式记忆
            "mysql_relationship_data": 0.15,      # MySQL关系数据
            "vector_historical_memory": 0.05       # 远程向量库（最低权重）
        }
        
    async def initialize(self):
        """初始化增强版记忆集成器"""
        try:
            # 初始化连接器
            self.redis_connector = get_redis_connector()
            self.mysql_connector = get_mysql_connector()
            
            # 验证Redis连接
            await self._verify_redis_connection()
            
            # 验证MySQL连接
            await self._verify_mysql_connection()
            
            self.is_initialized = True
            logger.info("✅ 增强版记忆集成器初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 增强版记忆集成器初始化失败: {e}")
            raise
    
    async def get_enhanced_user_memory(self, user_id: str) -> Dict:
        """
        获取增强版用户记忆
        优化记忆获取效率，增强情景记忆关联分析
        """
        try:
            enhanced_memory = {
                "user_id": user_id,
                "timestamp": time.time(),
                "memory_layers": {},
                "consolidated_insights": {},
                "proactive_triggers": {},
                "relationship_context": {},
                "emotional_intelligence": {}
            }
            
            # 并发获取多层记忆数据
            memory_tasks = [
                self._get_redis_recent_conversations(user_id),
                self._get_redis_emotional_context(user_id),
                self._get_redis_behavioral_patterns(user_id),
                self._get_mysql_enhanced_relationship_data(user_id)
            ]
            
            memory_results = await asyncio.gather(*memory_tasks, return_exceptions=True)
            
            # 处理记忆获取结果
            enhanced_memory["memory_layers"] = {
                "recent_conversations": memory_results[0] if not isinstance(memory_results[0], Exception) else {},
                "emotional_context": memory_results[1] if not isinstance(memory_results[1], Exception) else {},
                "behavioral_patterns": memory_results[2] if not isinstance(memory_results[2], Exception) else {},
                "relationship_data": memory_results[3] if not isinstance(memory_results[3], Exception) else {}
            }
            
            # 生成整合洞察
            enhanced_memory["consolidated_insights"] = await self._generate_enhanced_insights(enhanced_memory["memory_layers"])
            
            # 分析主动触发机会
            enhanced_memory["proactive_triggers"] = await self._analyze_enhanced_proactive_triggers(enhanced_memory["memory_layers"])
            
            # 生成关系上下文
            enhanced_memory["relationship_context"] = await self._generate_enhanced_relationship_context(enhanced_memory["memory_layers"])
            
            # 情感智能分析
            enhanced_memory["emotional_intelligence"] = await self._analyze_emotional_intelligence(enhanced_memory["memory_layers"])
            
            logger.info(f"✅ 成功获取用户 {user_id} 的增强版记忆")
            return enhanced_memory
            
        except Exception as e:
            logger.error(f"❌ 获取增强版用户记忆失败 (用户: {user_id}): {e}")
            return self._create_empty_enhanced_memory(user_id)
    
    async def record_proactive_behavior_enhanced(self, user_id: str, behavior_data: Dict) -> bool:
        """
        记录主动行为到增强版记忆系统
        实现实时记忆更新机制
        """
        try:
            # 生成行为ID
            behavior_id = f"pb_{int(time.time() * 1000)}"
            
            # 增强行为数据
            enhanced_behavior = {
                "id": behavior_id,
                "timestamp": time.time(),
                "user_id": user_id,
                "type": behavior_data.get("type", "unknown"),
                "content": behavior_data.get("content", ""),
                "trigger_reason": behavior_data.get("trigger_reason", ""),
                "context": behavior_data.get("context", {}),
                "yanran_personality_applied": behavior_data.get("yanran_personality_applied", False),
                "expected_response": behavior_data.get("expected_response", ""),
                "effectiveness_score": None,  # 待用户响应后更新
                "user_response": None,
                "follow_up_needed": False,
                "emotional_impact": behavior_data.get("emotional_impact", "neutral"),
                "relationship_effect": behavior_data.get("relationship_effect", 0)
            }
            
            # 并发更新Redis和MySQL
            update_tasks = [
                self._update_redis_proactive_behavior(user_id, enhanced_behavior),
                self._update_mysql_proactive_behavior_log(user_id, enhanced_behavior),
                self._update_user_interaction_stats(user_id, enhanced_behavior)
            ]
            
            results = await asyncio.gather(*update_tasks, return_exceptions=True)
            
            # 检查更新结果
            success_count = sum(1 for result in results if not isinstance(result, Exception))
            
            if success_count >= 2:  # 至少2个更新成功
                logger.info(f"✅ 成功记录用户 {user_id} 的主动行为: {behavior_id}")
                return True
            else:
                logger.warning(f"⚠️ 部分记录失败，用户 {user_id} 的主动行为: {behavior_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 记录主动行为失败 (用户: {user_id}): {e}")
            return False
    
    async def analyze_memory_consolidation_opportunities(self, user_id: str) -> List[Dict]:
        """
        分析记忆整合机会
        识别可以整合的记忆片段，提升记忆质量
        """
        try:
            opportunities = []
            
            # 获取用户的记忆数据
            memory_data = await self.get_enhanced_user_memory(user_id)
            
            # 1. 分析重复主题的对话
            topic_clusters = await self._analyze_conversation_topic_clusters(memory_data)
            for cluster in topic_clusters:
                if cluster["frequency"] >= 3:  # 出现3次以上的主题
                    opportunities.append({
                        "type": "topic_consolidation",
                        "topic": cluster["topic"],
                        "frequency": cluster["frequency"],
                        "conversations": cluster["conversations"],
                        "consolidation_potential": cluster["frequency"] * 0.2,
                        "suggested_insight": f"用户对{cluster['topic']}话题很感兴趣"
                    })
            
            # 2. 分析情感状态变化模式
            emotion_patterns = await self._analyze_emotion_patterns(memory_data)
            for pattern in emotion_patterns:
                if pattern["significance"] > 0.6:
                    opportunities.append({
                        "type": "emotion_pattern_consolidation",
                        "pattern": pattern["pattern"],
                        "significance": pattern["significance"],
                        "time_range": pattern["time_range"],
                        "consolidation_potential": pattern["significance"],
                        "suggested_insight": f"用户在{pattern['context']}时通常表现为{pattern['emotion']}"
                    })
            
            # 3. 分析行为习惯模式
            behavior_patterns = await self._analyze_behavior_patterns(memory_data)
            for pattern in behavior_patterns:
                if pattern["consistency"] > 0.7:
                    opportunities.append({
                        "type": "behavior_pattern_consolidation",
                        "pattern": pattern["pattern"],
                        "consistency": pattern["consistency"],
                        "examples": pattern["examples"],
                        "consolidation_potential": pattern["consistency"],
                        "suggested_insight": f"用户习惯在{pattern['condition']}时{pattern['behavior']}"
                    })
            
            # 按整合潜力排序
            opportunities.sort(key=lambda x: x["consolidation_potential"], reverse=True)
            
            logger.info(f"✅ 为用户 {user_id} 分析出 {len(opportunities)} 个记忆整合机会")
            return opportunities[:10]  # 返回前10个机会
            
        except Exception as e:
            logger.error(f"❌ 分析记忆整合机会失败 (用户: {user_id}): {e}")
            return []
    
    async def trigger_memory_consolidation(self, user_id: str, consolidation_data: Dict) -> bool:
        """
        触发记忆整合
        将分散的记忆片段整合为更高层次的洞察
        """
        try:
            consolidation_id = f"mc_{int(time.time() * 1000)}"
            
            # 创建整合记录
            consolidation_record = {
                "id": consolidation_id,
                "user_id": user_id,
                "timestamp": time.time(),
                "type": consolidation_data.get("type", "unknown"),
                "source_memories": consolidation_data.get("source_memories", []),
                "consolidated_insight": consolidation_data.get("consolidated_insight", ""),
                "confidence_score": consolidation_data.get("confidence_score", 0.0),
                "applications": consolidation_data.get("applications", []),
                "created_by": "enhanced_memory_integration"
            }
            
            # 保存到Redis
            redis_key = self.redis_keys["memory_consolidation"].format(user_id=user_id)
            await self._redis_list_push(redis_key, consolidation_record)
            
            # 更新MySQL记录
            await self._update_mysql_memory_consolidation(user_id, consolidation_record)
            
            logger.info(f"✅ 成功触发用户 {user_id} 的记忆整合: {consolidation_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 触发记忆整合失败 (用户: {user_id}): {e}")
            return False
    
    # ==================== 私有方法 ====================
    
    async def _verify_redis_connection(self):
        """验证Redis连接"""
        try:
            # 测试Redis连接
            test_key = "test_connection"
            self.redis_connector.set(test_key, "test_value", ttl=5)
            result = self.redis_connector.get(test_key)
            if result == "test_value":
                self.redis_connector.delete(test_key)
                logger.info("✅ Redis连接验证成功")
            else:
                raise Exception("Redis连接测试失败")
        except Exception as e:
            logger.error(f"❌ Redis连接验证失败: {e}")
            raise
    
    async def _verify_mysql_connection(self):
        """验证MySQL连接"""
        try:
            # 测试MySQL连接
            success, result, error = self.mysql_connector.query("SELECT 1 as test")
            if success and result:
                logger.info("✅ MySQL连接验证成功")
            else:
                raise Exception(f"MySQL连接测试失败: {error}")
        except Exception as e:
            logger.error(f"❌ MySQL连接验证失败: {e}")
            raise
    
    async def _get_redis_recent_conversations(self, user_id: str) -> Dict:
        """获取Redis最近对话记忆"""
        try:
            redis_key = self.redis_keys["conversation_history"].format(user_id=user_id)
            conversations = self.redis_connector.lrange(redis_key, 0, 49)  # 最近50条
            
            if conversations:
                parsed_conversations = []
                for conv in conversations:
                    try:
                        parsed_conv = json.loads(conv) if isinstance(conv, str) else conv
                        parsed_conversations.append(parsed_conv)
                    except json.JSONDecodeError:
                        continue
                
                return {
                    "total_count": len(parsed_conversations),
                    "conversations": parsed_conversations,
                    "time_range": self._calculate_time_range(parsed_conversations),
                    "topics": self._extract_conversation_topics(parsed_conversations),
                    "sentiment_distribution": self._analyze_conversation_sentiment(parsed_conversations)
                }
            
            return {"total_count": 0, "conversations": [], "time_range": None, "topics": [], "sentiment_distribution": {}}
            
        except Exception as e:
            logger.error(f"❌ 获取Redis最近对话记忆失败: {e}")
            return {}
    
    async def _get_redis_emotional_context(self, user_id: str) -> Dict:
        """获取Redis情感上下文记忆"""
        try:
            redis_key = self.redis_keys["emotion_states"].format(user_id=user_id)
            emotion_data = self.redis_connector.get(redis_key)
            
            if emotion_data:
                if isinstance(emotion_data, str):
                    emotion_data = json.loads(emotion_data)
                
                return {
                    "current_emotion": emotion_data.get("current_emotion", "neutral"),
                    "emotion_history": emotion_data.get("emotion_history", []),
                    "emotion_triggers": emotion_data.get("emotion_triggers", {}),
                    "emotional_stability": emotion_data.get("emotional_stability", 0.5),
                    "dominant_emotions": self._analyze_dominant_emotions(emotion_data.get("emotion_history", []))
                }
            
            return {"current_emotion": "neutral", "emotion_history": [], "emotion_triggers": {}, "emotional_stability": 0.5, "dominant_emotions": []}
            
        except Exception as e:
            logger.error(f"❌ 获取Redis情感上下文记忆失败: {e}")
            return {}
    
    async def _get_redis_behavioral_patterns(self, user_id: str) -> Dict:
        """获取Redis行为模式记忆"""
        try:
            redis_key = self.redis_keys["user_preferences"].format(user_id=user_id)
            preferences_data = self.redis_connector.get(redis_key)
            
            stats_key = self.redis_keys["interaction_stats"].format(user_id=user_id)
            stats_data = self.redis_connector.get(stats_key)
            
            behavioral_patterns = {}
            
            if preferences_data:
                if isinstance(preferences_data, str):
                    preferences_data = json.loads(preferences_data)
                behavioral_patterns["preferences"] = preferences_data
            
            if stats_data:
                if isinstance(stats_data, str):
                    stats_data = json.loads(stats_data)
                behavioral_patterns["interaction_stats"] = stats_data
            
            # 分析行为模式
            behavioral_patterns["patterns"] = self._extract_behavioral_patterns(behavioral_patterns)
            
            return behavioral_patterns
            
        except Exception as e:
            logger.error(f"❌ 获取Redis行为模式记忆失败: {e}")
            return {}
    
    async def _get_mysql_enhanced_relationship_data(self, user_id: str) -> Dict:
        """获取MySQL增强版关系数据"""
        try:
            # 获取基础关系数据
            relationship_query = """
            SELECT user_id, intimacy_level, relationship_type, emotional_weight, 
                   interaction_frequency, last_interaction_time, created_at, updated_at
            FROM ai_user_relationships 
            WHERE user_id = %s
            """
            
            success, relationship_data, error = self.mysql_connector.query_one(relationship_query, (user_id,))
            
            if not success or not relationship_data:
                return self._create_default_enhanced_relationship(user_id)
            
            return {
                "relationship": relationship_data,
                "interaction_stats": [],
                "proactive_behavior_history": [],
                "relationship_development": self._analyze_relationship_development(relationship_data, []),
                "boundary_analysis": self._analyze_user_boundaries(relationship_data)
            }
            
        except Exception as e:
            logger.error(f"❌ 获取MySQL增强版关系数据失败: {e}")
            return self._create_default_enhanced_relationship(user_id)
    
    def _create_empty_enhanced_memory(self, user_id: str) -> Dict:
        """创建空的增强版记忆结构"""
        return {
            "user_id": user_id,
            "timestamp": time.time(),
            "memory_layers": {
                "recent_conversations": {},
                "emotional_context": {},
                "behavioral_patterns": {},
                "relationship_data": {}
            },
            "consolidated_insights": {},
            "proactive_triggers": {},
            "relationship_context": {},
            "emotional_intelligence": {}
        }
    
    def _create_default_enhanced_relationship(self, user_id: str) -> Dict:
        """创建默认的增强版关系数据"""
        return {
            "relationship": {
                "user_id": user_id,
                "intimacy_level": 0,
                "relationship_type": "stranger",
                "emotional_weight": 0.0,
                "interaction_frequency": 0,
                "last_interaction_time": None
            },
            "interaction_stats": [],
            "proactive_behavior_history": [],
            "relationship_development": {"trend": "stable", "growth_rate": 0.0},
            "boundary_analysis": {"comfort_level": "low", "sharing_depth": "surface"}
        }
    
    # 辅助分析方法
    def _calculate_time_range(self, conversations: List[Dict]) -> Optional[Dict]:
        """计算对话时间范围"""
        if not conversations:
            return None
        
        timestamps = [conv.get("timestamp", 0) for conv in conversations if conv.get("timestamp")]
        if not timestamps:
            return None
        
        return {
            "start_time": min(timestamps),
            "end_time": max(timestamps),
            "duration_hours": (max(timestamps) - min(timestamps)) / 3600
        }
    
    def _extract_conversation_topics(self, conversations: List[Dict]) -> List[str]:
        """提取对话主题"""
        topics = []
        for conv in conversations:
            content = conv.get("user_message", "") + " " + conv.get("ai_response", "")
            # 简单的主题提取逻辑
            if "工作" in content or "职业" in content:
                topics.append("工作")
            if "生活" in content or "日常" in content:
                topics.append("生活")
            if "感情" in content or "情感" in content:
                topics.append("情感")
            if "学习" in content or "知识" in content:
                topics.append("学习")
        
        return list(set(topics))
    
    def _analyze_conversation_sentiment(self, conversations: List[Dict]) -> Dict:
        """分析对话情感分布"""
        sentiments = {"positive": 0, "neutral": 0, "negative": 0}
        
        for conv in conversations:
            sentiment = conv.get("sentiment", "neutral")
            if sentiment in sentiments:
                sentiments[sentiment] += 1
        
        total = sum(sentiments.values())
        if total > 0:
            return {k: v/total for k, v in sentiments.items()}
        
        return {"positive": 0.33, "neutral": 0.34, "negative": 0.33}
    
    def _analyze_dominant_emotions(self, emotion_history: List[Dict]) -> List[str]:
        """分析主导情感"""
        emotion_counts = {}
        for emotion_record in emotion_history:
            emotion = emotion_record.get("emotion", "neutral")
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        # 返回出现频率最高的前3个情感
        sorted_emotions = sorted(emotion_counts.items(), key=lambda x: x[1], reverse=True)
        return [emotion for emotion, count in sorted_emotions[:3]]
    
    def _extract_behavioral_patterns(self, behavioral_data: Dict) -> List[Dict]:
        """提取行为模式"""
        patterns = []
        
        preferences = behavioral_data.get("preferences", {})
        stats = behavioral_data.get("interaction_stats", {})
        
        # 分析活跃时间模式
        if "active_hours" in stats:
            patterns.append({
                "type": "active_hours",
                "pattern": stats["active_hours"],
                "description": "用户活跃时间模式"
            })
        
        # 分析话题偏好模式
        if "preferred_topics" in preferences:
            patterns.append({
                "type": "topic_preference",
                "pattern": preferences["preferred_topics"],
                "description": "用户话题偏好模式"
            })
        
        return patterns
    
    def _analyze_relationship_development(self, relationship_data: Dict, stats_data: List[Dict]) -> Dict:
        """分析关系发展趋势"""
        if not relationship_data:
            return {"trend": "stable", "growth_rate": 0.0}
        
        # 简单的关系发展分析
        current_intimacy = relationship_data.get("intimacy_level", 0)
        interaction_frequency = relationship_data.get("interaction_frequency", 0)
        
        if current_intimacy > 10000 and interaction_frequency > 5:
            return {"trend": "growing", "growth_rate": 0.1}
        elif current_intimacy > 1000:
            return {"trend": "stable", "growth_rate": 0.0}
        else:
            return {"trend": "new", "growth_rate": 0.05}
    
    def _analyze_user_boundaries(self, relationship_data: Dict) -> Dict:
        """分析用户边界"""
        if not relationship_data:
            return {"comfort_level": "low", "sharing_depth": "surface"}
        
        intimacy_level = relationship_data.get("intimacy_level", 0)
        
        if intimacy_level > 50000:
            return {"comfort_level": "very_high", "sharing_depth": "intimate"}
        elif intimacy_level > 20000:
            return {"comfort_level": "high", "sharing_depth": "personal"}
        elif intimacy_level > 10000:
            return {"comfort_level": "medium", "sharing_depth": "friendly"}
        elif intimacy_level > 1000:
            return {"comfort_level": "low", "sharing_depth": "casual"}
        else:
            return {"comfort_level": "very_low", "sharing_depth": "surface"}
    
    async def _generate_enhanced_insights(self, memory_layers: Dict) -> Dict:
        """生成增强版洞察"""
        insights = {
            "user_profile_summary": "",
            "interaction_patterns": [],
            "emotional_tendencies": [],
            "relationship_status": "",
            "proactive_opportunities": []
        }
        
        # 基于记忆层生成洞察
        conversations = memory_layers.get("recent_conversations", {})
        emotions = memory_layers.get("emotional_context", {})
        relationship = memory_layers.get("relationship_data", {})
        
        # 生成用户画像摘要
        if relationship.get("relationship", {}):
            rel_data = relationship["relationship"]
            insights["user_profile_summary"] = f"关系类型: {rel_data.get('relationship_type', 'unknown')}, 亲密度: {rel_data.get('intimacy_level', 0)}"
        
        # 分析交互模式
        if conversations.get("conversations"):
            insights["interaction_patterns"] = [
                f"最近{conversations.get('total_count', 0)}次对话",
                f"主要话题: {', '.join(conversations.get('topics', []))}"
            ]
        
        # 分析情感倾向
        if emotions.get("dominant_emotions"):
            insights["emotional_tendencies"] = emotions["dominant_emotions"]
        
        return insights
    
    async def _analyze_enhanced_proactive_triggers(self, memory_layers: Dict) -> Dict:
        """分析增强版主动触发机会"""
        triggers = {
            "curiosity_triggers": [],
            "empathy_triggers": [],
            "creativity_triggers": [],
            "relationship_triggers": [],
            "timing_triggers": []
        }
        
        # 基于记忆层分析触发机会
        relationship_data = memory_layers.get("relationship_data", {})
        if relationship_data.get("relationship", {}):
            rel = relationship_data["relationship"]
            intimacy_level = rel.get("intimacy_level", 0)
            
            # 根据亲密度生成不同的触发机会
            if intimacy_level > 10000:
                triggers["empathy_triggers"].append({
                    "type": "care_check",
                    "reason": "关系较好，可以主动关怀",
                    "score": 0.7
                })
            
            if intimacy_level > 1000:
                triggers["curiosity_triggers"].append({
                    "type": "topic_exploration",
                    "reason": "可以探索新话题",
                    "score": 0.6
                })
        
        return triggers
    
    async def _generate_enhanced_relationship_context(self, memory_layers: Dict) -> Dict:
        """生成增强版关系上下文"""
        context = {
            "relationship_type": "stranger",
            "intimacy_level": 0,
            "communication_style": "formal",
            "boundary_preferences": {},
            "interaction_history": {}
        }
        
        relationship_data = memory_layers.get("relationship_data", {})
        if relationship_data.get("relationship"):
            rel = relationship_data["relationship"]
            context.update({
                "relationship_type": rel.get("relationship_type", "stranger"),
                "intimacy_level": rel.get("intimacy_level", 0),
                "communication_style": self._determine_communication_style(rel.get("intimacy_level", 0))
            })
        
        return context
    
    def _determine_communication_style(self, intimacy_level: int) -> str:
        """确定沟通风格"""
        if intimacy_level > 50000:
            return "intimate"
        elif intimacy_level > 20000:
            return "casual"
        elif intimacy_level > 10000:
            return "friendly"
        elif intimacy_level > 1000:
            return "polite"
        else:
            return "formal"
    
    async def _analyze_emotional_intelligence(self, memory_layers: Dict) -> Dict:
        """分析情感智能"""
        ei_analysis = {
            "emotional_awareness": 0.5,
            "empathy_level": 0.5,
            "emotional_regulation": 0.5,
            "social_skills": 0.5,
            "recommendations": []
        }
        
        # 基于记忆层分析情感智能
        emotions = memory_layers.get("emotional_context", {})
        if emotions.get("emotional_stability"):
            ei_analysis["emotional_regulation"] = emotions["emotional_stability"]
        
        return ei_analysis
    
    # Redis操作辅助方法
    async def _redis_list_push(self, key: str, data: Dict):
        """向Redis列表推送数据"""
        json_data = json.dumps(data, ensure_ascii=False)
        self.redis_connector.lpush(key, json_data)
        # 限制列表长度
        self.redis_connector.ltrim(key, 0, 99)  # 保留最近100条
    
    async def _update_redis_proactive_behavior(self, user_id: str, behavior_data: Dict):
        """更新Redis中的主动行为记录"""
        redis_key = self.redis_keys["proactive_behavior"].format(user_id=user_id)
        await self._redis_list_push(redis_key, behavior_data)
    
    async def _update_mysql_proactive_behavior_log(self, user_id: str, behavior_data: Dict):
        """更新MySQL中的主动行为日志"""
        try:
            insert_data = {
                "user_id": user_id,
                "behavior_type": behavior_data.get("type", "unknown"),
                "content": behavior_data.get("content", ""),
                "trigger_reason": behavior_data.get("trigger_reason", ""),
                "effectiveness_score": behavior_data.get("effectiveness_score"),
                "user_response": behavior_data.get("user_response"),
                "created_at": datetime.fromtimestamp(behavior_data.get("timestamp", time.time()))
            }
            
            success, insert_id, error = self.mysql_connector.insert("proactive_behavior_log", insert_data)
            if not success:
                logger.warning(f"MySQL主动行为日志插入失败: {error}")
            
        except Exception as e:
            logger.error(f"更新MySQL主动行为日志失败: {e}")
    
    async def _update_user_interaction_stats(self, user_id: str, behavior_data: Dict):
        """更新用户交互统计"""
        try:
            # 更新今日统计
            today = datetime.now().date()
            update_query = """
            INSERT INTO user_interaction_stats (user_id, stat_date, proactive_count, last_proactive_time)
            VALUES (%s, %s, 1, %s)
            ON DUPLICATE KEY UPDATE 
            proactive_count = proactive_count + 1,
            last_proactive_time = %s
            """
            
            timestamp = datetime.fromtimestamp(behavior_data.get("timestamp", time.time()))
            params = (user_id, today, timestamp, timestamp)
            
            success, result, error = self.mysql_connector.query(update_query, params)
            if not success:
                logger.warning(f"更新用户交互统计失败: {error}")
                
        except Exception as e:
            logger.error(f"更新用户交互统计失败: {e}")
    
    async def _update_mysql_memory_consolidation(self, user_id: str, consolidation_record: Dict):
        """更新MySQL中的记忆整合记录"""
        try:
            insert_data = {
                "user_id": user_id,
                "consolidation_type": consolidation_record.get("type", "unknown"),
                "source_memories": json.dumps(consolidation_record.get("source_memories", [])),
                "consolidated_insight": consolidation_record.get("consolidated_insight", ""),
                "confidence_score": consolidation_record.get("confidence_score", 0.0),
                "created_at": datetime.fromtimestamp(consolidation_record.get("timestamp", time.time()))
            }
            
            # 如果表不存在，先创建
            await self._ensure_memory_consolidation_table_exists()
            
            success, insert_id, error = self.mysql_connector.insert("memory_consolidation_log", insert_data)
            if not success:
                logger.warning(f"MySQL记忆整合记录插入失败: {error}")
                
        except Exception as e:
            logger.error(f"更新MySQL记忆整合记录失败: {e}")
    
    async def _ensure_memory_consolidation_table_exists(self):
        """确保记忆整合表存在"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS memory_consolidation_log (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(255) NOT NULL,
                consolidation_type VARCHAR(50) NOT NULL,
                source_memories TEXT,
                consolidated_insight TEXT NOT NULL,
                confidence_score DECIMAL(3,2) DEFAULT 0.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            success, result, error = self.mysql_connector.query(create_table_sql)
            if not success:
                logger.warning(f"创建记忆整合表失败: {error}")
                
        except Exception as e:
            logger.error(f"确保记忆整合表存在失败: {e}")
    
    # 高级分析方法
    async def _analyze_conversation_topic_clusters(self, memory_data: Dict) -> List[Dict]:
        """分析对话主题聚类"""
        clusters = []
        
        conversations = memory_data.get("memory_layers", {}).get("recent_conversations", {}).get("conversations", [])
        
        # 简单的主题聚类逻辑
        topic_counts = {}
        for conv in conversations:
            topics = conv.get("topics", [])
            for topic in topics:
                if topic not in topic_counts:
                    topic_counts[topic] = {"count": 0, "conversations": []}
                topic_counts[topic]["count"] += 1
                topic_counts[topic]["conversations"].append(conv)
        
        for topic, data in topic_counts.items():
            clusters.append({
                "topic": topic,
                "frequency": data["count"],
                "conversations": data["conversations"]
            })
        
        return clusters
    
    async def _analyze_emotion_patterns(self, memory_data: Dict) -> List[Dict]:
        """分析情感模式"""
        patterns = []
        
        emotion_context = memory_data.get("memory_layers", {}).get("emotional_context", {})
        emotion_history = emotion_context.get("emotion_history", [])
        
        # 分析情感变化模式
        if len(emotion_history) >= 3:
            patterns.append({
                "pattern": "emotion_stability",
                "significance": 0.7,
                "time_range": "recent",
                "context": "日常交流",
                "emotion": emotion_context.get("current_emotion", "neutral")
            })
        
        return patterns
    
    async def _analyze_behavior_patterns(self, memory_data: Dict) -> List[Dict]:
        """分析行为模式"""
        patterns = []
        
        behavioral_data = memory_data.get("memory_layers", {}).get("behavioral_patterns", {})
        extracted_patterns = behavioral_data.get("patterns", [])
        
        for pattern in extracted_patterns:
            patterns.append({
                "pattern": pattern.get("type", "unknown"),
                "consistency": 0.8,  # 简化的一致性评分
                "examples": [pattern.get("description", "")],
                "condition": "特定情况",
                "behavior": pattern.get("pattern", "")
            })
        
        return patterns


# 单例管理
_enhanced_memory_integration_instance = None

def get_enhanced_memory_integration() -> EnhancedMemoryIntegration:
    """获取增强版记忆集成器单例"""
    global _enhanced_memory_integration_instance
    if _enhanced_memory_integration_instance is None:
        _enhanced_memory_integration_instance = EnhancedMemoryIntegration()
    return _enhanced_memory_integration_instance 