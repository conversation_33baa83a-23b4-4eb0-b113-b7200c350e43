#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序记忆模块 (Procedural Memory)

该模块负责存储和检索数字生命体的技能、动作序列和行为模式，实现技能的
学习、改进和自动化等功能。程序记忆是数字生命体行为能力的重要基础。

作者: Claude
创建日期: 2024-07-17
版本: 1.0
"""

import os
import sys
import json
import time
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union
import threading

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from utilities.storage_manager import get_instance as get_storage_instance
from core.integrated_event_bus import get_instance as get_event_bus
from cognitive_modules.base.module_base import CognitiveModuleBase

# 设置日志
logger = get_unified_logger("procedural_memory")

class ProceduralMemory(CognitiveModuleBase):
    """程序记忆模块，负责存储和检索数字生命体的技能和行为模式"""
    
    def __init__(self, module_id: str = "default", config: Dict = None):
        """
        初始化程序记忆模块
        
        Args:
            module_id: 模块ID
            config: 配置参数
        """
        super().__init__(module_id, "程序记忆模块", config)
        self.skills = {}  # 内存中的技能记忆
        self.memory_config = {
            "storage_enabled": True,           # 是否启用持久化存储
            "practice_effect": 0.05,           # 练习增强系数
            "skill_decay_rate": 0.01,          # 技能衰减率(每天)
            "mastery_threshold": 0.8,          # 技能掌握阈值
            "automation_threshold": 0.9,       # 技能自动化阈值
            "max_difficulty": 5.0,             # 最大技能难度
        }
        
        # 更新配置
        if config:
            # 🔥 老王修复：确保config是字典类型
            if isinstance(config, dict):
                self.memory_config.update(config)
            else:
                logger.warning_status(f"程序记忆模块配置跳过更新: config不是字典类型，而是{type(config)}")
        
        # 存储管理器
        self.storage = None
        
        # 事件总线
        self.event_bus = get_event_bus()
        
        # 技能分类
        self.skill_categories = {
            "对话": ["问候", "回答", "闲聊", "讲故事", "辩论"],
            "分析": ["信息处理", "推理", "决策", "问题解决"],
            "情感": ["情绪表达", "情感调节", "共情"],
            "创造": ["写作", "绘画描述", "音乐欣赏", "创意思考"],
            "社交": ["礼仪", "关系维护", "冲突处理"],
            "学习": ["记忆技巧", "知识获取", "技能学习"],
            "自我管理": ["时间规划", "目标设定", "自我反思"],
        }
        
        # 上次技能维护时间
        self.last_maintenance = time.time()
    
    def initialize(self, config: Dict = None) -> bool:
        """
        初始化程序记忆模块
        
        Args:
            config: 配置参数
        
        Returns:
            bool: 初始化是否成功
        """
        logger.success(f"正在初始化程序记忆模块 (ID: {self.module_id})...")
        
        try:
            # 🔥 老王修复：先调用父类的initialize方法
            if not super().initialize(config):
                return False
            
            # 初始化存储管理器
            if self.memory_config["storage_enabled"]:
                self.storage = get_storage_instance()
                
                # 加载持久化技能
                self._load_skills()
            
            # 订阅事件
            self._subscribe_events()
            
            # 启动技能维护
            self._schedule_skill_maintenance()
            
            logger.success(f"程序记忆模块初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"程序记忆模块初始化失败: {str(e)}")
            return False
    
    def shutdown(self) -> bool:
        """
        关闭程序记忆模块
        
        Returns:
            bool: 关闭是否成功
        """
        logger.info(f"正在关闭程序记忆模块 (ID: {self.module_id})...")
        
        try:
            # 持久化技能
            if self.memory_config["storage_enabled"] and self.storage:
                self._save_skills()
            
            # 取消事件订阅
            self._unsubscribe_events()
            
            logger.success(f"程序记忆模块关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"程序记忆模块关闭失败: {str(e)}")
            return False
    
    def add_skill(self, skill_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加新的技能
        
        Args:
            skill_data: 技能数据，必须包含name和description字段
        
        Returns:
            Dict: 添加的技能对象
        """
        if "name" not in skill_data or "description" not in skill_data:
            raise ValueError("技能数据必须包含name和description字段")
        
        # 创建技能ID
        skill_id = f"skill_{int(time.time())}_{random.randint(1000, 9999)}"
        
        # 构建技能对象
        skill = {
            "id": skill_id,
            "name": skill_data["name"],
            "description": skill_data["description"],
            "created_at": time.time(),
            "last_used": time.time(),
            "use_count": 0,
            "proficiency": skill_data.get("proficiency", 0.1),  # 初始熟练度
            "difficulty": skill_data.get("difficulty", 1.0),    # 技能难度
            "is_automated": False,  # 是否自动化
            "steps": skill_data.get("steps", []),  # 技能步骤
            "prerequisites": skill_data.get("prerequisites", []),  # 前置技能
            "metadata": {}
        }
        
        # 添加可选字段
        optional_fields = [
            "category", "subcategory", "tags", "related_skills",
            "learning_source", "importance"
        ]
        
        for field in optional_fields:
            if field in skill_data:
                skill[field] = skill_data[field]
        
        # 存储技能
        self.skills[skill_id] = skill
        
        # 发布技能创建事件
        self.event_bus.publish("skill_created", {
            "skill_id": skill_id,
            "skill_name": skill["name"],
            "skill_category": skill.get("category", "未分类")
        })
        
        logger.debug(f"添加了新的技能: {skill_id} - {skill['name']}")
        
        return skill
    
    def get_skill(self, skill_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定ID的技能
        
        Args:
            skill_id: 技能ID
        
        Returns:
            Dict或None: 技能对象，如果不存在则返回None
        """
        skill = self.skills.get(skill_id)
        
        if skill:
            # 更新使用信息
            skill["last_used"] = time.time()
            skill["use_count"] += 1
            
            # 发布技能访问事件
            self.event_bus.publish("skill_accessed", {
                "skill_id": skill_id,
                "skill_name": skill["name"]
            })
        
        return skill
    
    def search_skills(self, query: str, 
                      filters: Dict = None, 
                      limit: int = 5) -> List[Dict[str, Any]]:
        """
        搜索技能
        
        Args:
            query: 搜索查询
            filters: 过滤条件
            limit: 结果数量限制
        
        Returns:
            List: 匹配的技能列表
        """
        results = []
        
        # 处理空查询
        if not query and not filters:
            # 返回所有技能(按使用频率排序)
            sorted_skills = sorted(
                self.skills.values(),
                key=lambda x: x.get("use_count", 0),
                reverse=True
            )
            return sorted_skills[:limit]
        
        # 关键词搜索
        if query:
            # 提取查询关键词
            keywords = query.lower().split()
            
            # 搜索所有技能
            for skill in self.skills.values():
                name = skill.get("name", "").lower()
                description = skill.get("description", "").lower()
                score = 0
                
                # 计算关键词匹配分数
                for keyword in keywords:
                    if keyword in name:
                        score += 2  # 名称匹配权重更高
                    if keyword in description:
                        score += 1
                
                if score > 0:
                    # 计算最终得分(考虑熟练度)
                    final_score = score * skill.get("proficiency", 0.1)
                    results.append((skill, final_score))
            
            # 排序并取top结果
            results = [s for s, _ in sorted(results, key=lambda x: x[1], reverse=True)]
        else:
            # 无关键词时使用所有技能
            results = list(self.skills.values())
        
        # 应用过滤条件
        if filters:
            filtered_results = []
            for skill in results:
                match = True
                for key, value in filters.items():
                    if key in skill:
                        if isinstance(skill[key], list):
                            if value not in skill[key]:
                                match = False
                                break
                        elif skill[key] != value:
                            match = False
                            break
                if match:
                    filtered_results.append(skill)
            results = filtered_results
        
        # 限制结果数量
        return results[:limit]
    
    def update_skill(self, skill_id: str, 
                    updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        更新现有技能
        
        Args:
            skill_id: 技能ID
            updates: 要更新的字段
        
        Returns:
            Dict或None: 更新后的技能对象，如果不存在则返回None
        """
        if skill_id not in self.skills:
            return None
        
        skill = self.skills[skill_id]
        
        # 更新字段
        for key, value in updates.items():
            if key not in ["id", "created_at"]:  # 保护不可变字段
                skill[key] = value
        
        # 检查是否达到自动化阈值
        if (skill.get("proficiency", 0) >= self.memory_config["automation_threshold"] and
            not skill.get("is_automated", False)):
            skill["is_automated"] = True
            
            # 发布技能自动化事件
            self.event_bus.publish("skill_automated", {
                "skill_id": skill_id,
                "skill_name": skill["name"]
            })
        
        # 发布技能更新事件
        self.event_bus.publish("skill_updated", {
            "skill_id": skill_id,
            "skill_name": skill["name"],
            "updates": list(updates.keys())
        })
        
        return skill
    
    def delete_skill(self, skill_id: str) -> bool:
        """
        删除技能
        
        Args:
            skill_id: 技能ID
        
        Returns:
            bool: 删除是否成功
        """
        if skill_id not in self.skills:
            return False
        
        skill = self.skills[skill_id]
        
        # 删除技能
        del self.skills[skill_id]
        
        # 发布技能删除事件
        self.event_bus.publish("skill_deleted", {
            "skill_id": skill_id,
            "skill_name": skill["name"]
        })
        
        return True
    
    def practice_skill(self, skill_id: str, 
                      success_level: float = 0.5) -> Optional[Dict[str, Any]]:
        """
        练习技能，提高熟练度
        
        Args:
            skill_id: 技能ID
            success_level: 练习成功程度(0.0-1.0)
        
        Returns:
            Dict或None: 更新后的技能对象，如果不存在则返回None
        """
        if skill_id not in self.skills:
            return None
        
        skill = self.skills[skill_id]
        
        # 获取当前熟练度
        current_proficiency = skill.get("proficiency", 0.0)
        
        # 计算难度系数(越难进步越慢)
        difficulty_factor = 1.0 / max(1.0, skill.get("difficulty", 1.0))
        
        # 计算练习效果
        practice_effect = (
            self.memory_config["practice_effect"] * 
            success_level * 
            difficulty_factor
        )
        
        # 更新熟练度(考虑熟练度上限为1.0)
        new_proficiency = min(1.0, current_proficiency + practice_effect)
        
        # 更新技能
        updates = {
            "proficiency": new_proficiency,
            "last_used": time.time(),
            "use_count": skill.get("use_count", 0) + 1
        }
        
        # 检查是否达到掌握阈值
        if (new_proficiency >= self.memory_config["mastery_threshold"] and
            current_proficiency < self.memory_config["mastery_threshold"]):
            # 发布技能掌握事件
            self.event_bus.publish("skill_mastered", {
                "skill_id": skill_id,
                "skill_name": skill["name"]
            })
        
        # 检查是否达到自动化阈值
        if (new_proficiency >= self.memory_config["automation_threshold"] and
            not skill.get("is_automated", False)):
            updates["is_automated"] = True
            
            # 发布技能自动化事件
            self.event_bus.publish("skill_automated", {
                "skill_id": skill_id,
                "skill_name": skill["name"]
            })
        
        # 更新技能
        return self.update_skill(skill_id, updates)

    def _load_skills(self) -> None:
        """从持久化存储加载技能"""
        try:
            # 从JSON文件加载
            skills_data = self.storage.load_json(f"memories/{self.module_id}/procedural_skills.json", default={})
            
            if skills_data and isinstance(skills_data, dict):
                self.skills = skills_data
                
                logger.info(f"已从存储加载 {len(self.skills)} 个技能")
        except Exception as e:
            logger.error_status(f"加载技能失败: {str(e)}")

    def _save_skills(self) -> None:
        """将技能保存到持久化存储"""
        try:
            if self.skills:
                # 保存到JSON文件
                self.storage.save_json(f"memories/{self.module_id}/procedural_skills.json", self.skills)
                logger.info(f"已将 {len(self.skills)} 个技能保存到存储")
        except Exception as e:
            logger.error_status(f"保存技能失败: {str(e)}")

    def _subscribe_events(self) -> None:
        """订阅相关事件"""
        # 订阅技能使用事件
        self.event_bus.subscribe("skill_used", self._on_skill_used)
        
        # 订阅学习事件
        self.event_bus.subscribe("new_knowledge", self._on_new_knowledge)
        
        # 订阅时间事件
        self.event_bus.subscribe("daily_maintenance", self._on_daily_maintenance)

    def _unsubscribe_events(self) -> None:
        """取消事件订阅"""
        self.event_bus.unsubscribe("skill_used", self._on_skill_used)
        self.event_bus.unsubscribe("new_knowledge", self._on_new_knowledge)
        self.event_bus.unsubscribe("daily_maintenance", self._on_daily_maintenance)

    def _on_skill_used(self, event_data: Dict) -> None:
        """
        处理技能使用事件
        
        Args:
            event_data: 事件数据
        """
        skill_id = event_data.get("skill_id")
        success_level = event_data.get("success_level", 0.5)
        
        if not skill_id or skill_id not in self.skills:
            return
        
        # 练习技能(使用即练习)
        self.practice_skill(skill_id, success_level)

    def _on_new_knowledge(self, event_data: Dict) -> None:
        """
        处理新知识事件，可能导致新技能的形成
        
        Args:
            event_data: 事件数据
        """
        content = event_data.get("content")
        category = event_data.get("category")
        
        if not content:
            return
        
        # TODO: 实现知识转化为技能的逻辑
        # 这需要更复杂的认知处理，可能需要AI辅助
        pass

    def _on_daily_maintenance(self, event_data: Dict) -> None:
        """
        执行每日维护任务
        
        Args:
            event_data: 事件数据
        """
        # 执行技能衰减
        self._decay_skills()
        
        # 保存技能
        if self.memory_config["storage_enabled"] and self.storage:
            self._save_skills()

    def _schedule_skill_maintenance(self) -> None:
        """安排技能维护任务"""
        # 技能维护(每天)
        threading.Timer(24 * 3600, self._on_daily_maintenance, args=[{}]).start()

    def _decay_skills(self) -> None:
        """技能衰减过程"""
        # 获取衰减率
        daily_decay_rate = self.memory_config["skill_decay_rate"]
        
        # 对每个技能应用衰减
        for skill_id, skill in self.skills.items():
            # 计算上次使用天数
            days_since_used = (time.time() - skill.get("last_used", 0)) / (24 * 3600)
            
            # 只对长时间未使用的技能进行衰减
            if days_since_used < 1:
                continue
            
            # 自动化技能衰减较慢
            decay_factor = 0.5 if skill.get("is_automated", False) else 1.0
            
            # 计算衰减量(天数越长，衰减越多)
            decay_amount = daily_decay_rate * decay_factor * min(days_since_used, 30) / 30
            
            # 应用衰减
            current_proficiency = skill.get("proficiency", 0.0)
            new_proficiency = max(0.1, current_proficiency - decay_amount)  # 不会低于0.1
            
            # 更新熟练度
            skill["proficiency"] = new_proficiency
            
            # 如果熟练度下降到阈值以下，取消自动化状态
            if (new_proficiency < self.memory_config["automation_threshold"] and
                skill.get("is_automated", False)):
                skill["is_automated"] = False
                
                # 发布技能退化事件
                self.event_bus.publish("skill_degraded", {
                    "skill_id": skill_id,
                    "skill_name": skill["name"],
                    "proficiency": new_proficiency
                })
    
    def get_related_skills(self, skill_id: str) -> List[Dict[str, Any]]:
        """
        获取与指定技能相关的技能
        
        Args:
            skill_id: 技能ID
        
        Returns:
            List: 相关技能列表
        """
        if skill_id not in self.skills:
            return []
        
        skill = self.skills[skill_id]
        related_skill_ids = skill.get("related_skills", [])
        
        # 添加同类别技能
        category = skill.get("category")
        if category:
            for s_id, s in self.skills.items():
                if s_id != skill_id and s.get("category") == category:
                    if s_id not in related_skill_ids:
                        related_skill_ids.append(s_id)
        
        # 获取相关技能对象
        related_skills = []
        for rel_id in related_skill_ids:
            if rel_id in self.skills:
                related_skills.append(self.skills[rel_id])
        
        return related_skills
    
    def get_automated_skills(self, category: str = None) -> List[Dict[str, Any]]:
        """
        获取已自动化的技能
        
        Args:
            category: 可选的技能类别过滤
        
        Returns:
            List: 已自动化的技能列表
        """
        automated_skills = []
        
        for skill in self.skills.values():
            if skill.get("is_automated", False):
                if category is None or skill.get("category") == category:
                    automated_skills.append(skill)
        
        return automated_skills
    
    def get_skill_categories(self) -> Dict[str, List[str]]:
        """
        获取技能分类
        
        Returns:
            Dict: 技能分类字典
        """
        return self.skill_categories
    
    def get_skill_proficiency_stats(self) -> Dict[str, Dict]:
        """
        获取技能熟练度统计
        
        Returns:
            Dict: 各类别的技能熟练度统计
        """
        stats = {}
        
        # 初始化统计数据
        for category in self.skill_categories.keys():
            stats[category] = {
                "count": 0,
                "avg_proficiency": 0.0,
                "mastered_count": 0,
                "automated_count": 0
            }
        
        # 统计各类别技能
        for skill in self.skills.values():
            category = skill.get("category", "未分类")
            if category not in stats:
                stats[category] = {
                    "count": 0,
                    "avg_proficiency": 0.0,
                    "mastered_count": 0,
                    "automated_count": 0
                }
            
            # 更新计数
            stats[category]["count"] += 1
            
            # 更新熟练度总和
            proficiency = skill.get("proficiency", 0.0)
            stats[category]["avg_proficiency"] += proficiency
            
            # 检查是否掌握
            if proficiency >= self.memory_config["mastery_threshold"]:
                stats[category]["mastered_count"] += 1
            
            # 检查是否自动化
            if skill.get("is_automated", False):
                stats[category]["automated_count"] += 1
        
        # 计算平均熟练度
        for category, data in stats.items():
            if data["count"] > 0:
                data["avg_proficiency"] = data["avg_proficiency"] / data["count"]
        
        return stats
    
    def get_module_info(self) -> Dict[str, Any]:
        """
        获取模块信息
        
        Returns:
            Dict: 模块信息
        """
        # 计算总体统计
        automated_count = len(self.get_automated_skills())
        mastered_count = sum(1 for skill in self.skills.values() 
                           if skill.get("proficiency", 0) >= self.memory_config["mastery_threshold"])
        
        # 按类别统计
        category_stats = {
            category: len([s for s in self.skills.values() if s.get("category") == category])
            for category in self.skill_categories.keys()
        }
        
        return {
            "id": self.module_id,
            "type": self.module_type,
            "skills_count": len(self.skills),
            "automated_count": automated_count,
            "mastered_count": mastered_count,
            "category_stats": category_stats,
            "last_maintenance": self.last_maintenance
        }

    # 静态方法: 获取模块实例
    @staticmethod
    def get_instance(module_id: str = "default", config: Dict = None) -> 'ProceduralMemory':
        """
        获取程序记忆模块实例
        
        Args:
            module_id: 模块ID
            config: 配置参数
        
        Returns:
            ProceduralMemory: 模块实例
        """
        # 实例字典
        if not hasattr(ProceduralMemory, "_instances"):
            ProceduralMemory._instances = {}
        
        # 如果实例不存在，创建新实例
        if module_id not in ProceduralMemory._instances:
            instance = ProceduralMemory(module_id, config)
            ProceduralMemory._instances[module_id] = instance
        
        return ProceduralMemory._instances[module_id]

    def _initialize_module(self) -> bool:
        """
        初始化模块内部功能
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化存储管理器
            if self.memory_config["storage_enabled"]:
                self.storage = get_storage_instance()
                
                # 加载持久化技能
                self._load_skills()
            
            # 订阅事件
            self._subscribe_events()
            
            # 启动技能维护
            self._schedule_skill_maintenance()
            
            logger.success(f"程序记忆模块初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"程序记忆模块初始化失败: {str(e)}")
            return False

    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
        
        Returns:
            Dict: 处理结果
        """
        action = input_data.get("action", "")
        result = {"success": False, "message": "未知操作"}
        
        try:
            if action == "add_skill":
                skill_data = input_data.get("skill_data", {})
                skill = self.add_skill(skill_data)
                result = {"success": True, "skill": skill}
                
            elif action == "get_skill":
                skill_id = input_data.get("skill_id", "")
                skill = self.get_skill(skill_id)
                result = {"success": True if skill else False, "skill": skill}
                
            elif action == "search_skills":
                query = input_data.get("query", "")
                filters = input_data.get("filters", {})
                limit = input_data.get("limit", 5)
                skills = self.search_skills(query, filters, limit)
                result = {"success": True, "skills": skills}
                
            elif action == "update_skill":
                skill_id = input_data.get("skill_id", "")
                updates = input_data.get("updates", {})
                skill = self.update_skill(skill_id, updates)
                result = {"success": True if skill else False, "skill": skill}
                
            elif action == "delete_skill":
                skill_id = input_data.get("skill_id", "")
                success = self.delete_skill(skill_id)
                result = {"success": success}
                
            elif action == "practice_skill":
                skill_id = input_data.get("skill_id", "")
                success_level = input_data.get("success_level", 0.5)
                skill = self.practice_skill(skill_id, success_level)
                result = {"success": True if skill else False, "skill": skill}
                
            elif action == "get_related_skills":
                skill_id = input_data.get("skill_id", "")
                related_skills = self.get_related_skills(skill_id)
                result = {"success": True, "related_skills": related_skills}
                
            elif action == "get_automated_skills":
                category = input_data.get("category")
                automated_skills = self.get_automated_skills(category)
                result = {"success": True, "automated_skills": automated_skills}
                
            elif action == "get_skill_categories":
                categories = self.get_skill_categories()
                result = {"success": True, "categories": categories}
                
            elif action == "get_skill_proficiency_stats":
                stats = self.get_skill_proficiency_stats()
                result = {"success": True, "stats": stats}
                
            elif action == "get_module_info":
                info = self.get_module_info()
                result = {"success": True, "info": info}
                
            else:
                result = {"success": False, "message": f"不支持的操作: {action}"}
                
        except Exception as e:
            result = {"success": False, "message": str(e)}
            
        return result

    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
        
        Returns:
            bool: 更新是否成功
        """
        try:
            # 如果上下文中有特定事件，处理它们
            if "events" in context:
                for event in context["events"]:
                    event_type = event.get("type")
                    
                    if event_type == "skill_used":
                        # 处理技能使用事件
                        self._on_skill_used(event)
                        
                    elif event_type == "new_knowledge":
                        # 处理新知识事件
                        self._on_new_knowledge(event)
                        
                    elif event_type == "daily_maintenance":
                        # 执行每日维护
                        self._on_daily_maintenance(event)
            
            # 检查是否需要进行技能维护
            current_time = time.time()
            days_since_maintenance = (current_time - self.last_maintenance) / (24 * 3600)
            
            if days_since_maintenance >= 1:
                # 执行技能维护
                self._on_daily_maintenance({})
                self.last_maintenance = current_time
            
            return True
            
        except Exception as e:
            logger.error_status(f"更新程序记忆模块失败: {str(e)}")
            return False

    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块当前状态
        
        Returns:
            Dict: 模块状态
        """
        return {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "skills": self.skills,
            "last_maintenance": self.last_maintenance,
            "memory_config": self.memory_config
        }

    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        从保存的状态加载模块
        
        Args:
            state: 模块状态
        
        Returns:
            bool: 加载是否成功
        """
        try:
            # 加载配置
            if "memory_config" in state:
                # 🔥 老王修复：确保memory_config是字典类型
                memory_config = state["memory_config"]
                if isinstance(memory_config, dict):
                    self.memory_config.update(memory_config)
                else:
                    logger.warning_status(f"程序记忆模块状态加载跳过配置: memory_config不是字典类型，而是{type(memory_config)}")
                
            # 加载上次维护时间
            if "last_maintenance" in state:
                self.last_maintenance = state["last_maintenance"]
                
            # 加载技能
            if "skills" in state:
                self.skills = state["skills"]
                
            return True
            
        except Exception as e:
            logger.error_status(f"加载程序记忆模块状态失败: {str(e)}")
            return False

    def _shutdown_module(self) -> bool:
        """
        关闭模块
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            # 持久化技能
            if self.memory_config["storage_enabled"] and self.storage:
                self._save_skills()
            
            # 取消事件订阅
            self._unsubscribe_events()
            
            logger.success(f"程序记忆模块关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"程序记忆模块关闭失败: {str(e)}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态信息
        
        Returns:
            Dict: 状态信息
        """
        return {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "is_active": self.is_active,
            "skills_count": len(self.skills),
            "last_maintenance": self.last_maintenance,
            "memory_config": self.memory_config
        }

# 模块级别的get_instance函数
def get_instance(module_id: str = "default", config: Dict = None) -> ProceduralMemory:
    """
    获取程序记忆模块实例（模块级别函数）
    
    Args:
        module_id: 模块ID
        config: 配置参数
    
    Returns:
        ProceduralMemory: 模块实例
    """
    return ProceduralMemory.get_instance(module_id, config) 