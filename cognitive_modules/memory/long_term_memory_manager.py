#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长期记忆管理器 (Long Term Memory Manager)

该模块提供统一的长期记忆管理接口，协调情境记忆、语义记忆等模块，
实现长期记忆的存储、检索、整合和维护功能。

作者: Claude
创建日期: 2024-08-08
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from utilities.storage_manager import get_instance as get_storage_instance
from core.integrated_event_bus import get_instance as get_event_bus
from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.memory.memory_integration import MemoryIntegration
from cognitive_modules.memory.episodic_memory import EpisodicMemory

# 设置日志
logger = get_unified_logger("long_term_memory_manager")

class LongTermMemoryManager(CognitiveModuleBase):
    """长期记忆管理器，提供统一的长期记忆访问接口"""
    
    def __init__(self, module_id: str = "default", config: Dict = None):
        """
        初始化长期记忆管理器
        
        Args:
            module_id: 模块ID
            config: 配置参数
        """
        # 🔥 香草修复：正确的父类构造函数参数顺序
        super().__init__(module_id, "long_term_memory_manager", config)
        
        # 配置参数
        self.ltm_config = {
            "storage_enabled": True,             # 是否启用持久化存储
            "memory_integration_id": module_id,  # 记忆整合模块ID
            "core_memory_limit": 10,             # 核心记忆数量上限
            "long_term_memory_limit": 100,       # 长期记忆检索数量上限
            "memory_retrieval_boost": 0.1,       # 记忆检索强度提升
            "consolidation_interval": 86400,     # 记忆巩固间隔(秒)，默认1天
            "enable_memory_reflection": True,    # 是否启用记忆反思
        }
        
        # 更新配置
        if config:
            self.ltm_config.update(config)
        
        # 存储管理器
        self.storage = None
        
        # 事件总线
        self.event_bus = get_event_bus()
        
        # 记忆整合模块引用
        self.memory_integration = None
        
        # 长期记忆缓存
        self.core_memories_cache = []
        self.long_term_memories_cache = []
        
        # 上次缓存更新时间
        self.last_cache_update = 0
        
        # 上次记忆巩固时间
        self.last_consolidation = time.time()

        # 🔥 香草修复：添加初始化状态属性
        self.is_initialized = False

    def initialize(self) -> bool:
        """
        初始化长期记忆管理器
        
        Returns:
            bool: 初始化是否成功
        """
        logger.success(f"正在初始化长期记忆管理器 (ID: {self.module_id})...")
        
        try:
            # 初始化存储管理器
            if self.ltm_config["storage_enabled"]:
                self.storage = get_storage_instance()
            
            # 获取记忆整合模块实例
            integration_id = self.ltm_config["memory_integration_id"]
            self.memory_integration = MemoryIntegration.get_instance(integration_id)
            
            # 初始化记忆整合模块
            self.memory_integration.initialize()
            
            # 更新记忆缓存
            self._update_memory_cache()
            
            # 订阅事件
            self._subscribe_events()

            # 🔥 香草修复：设置初始化状态为True
            self.is_initialized = True

            logger.success(f"长期记忆管理器初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"长期记忆管理器初始化失败: {str(e)}")
            return False
    
    def shutdown(self) -> bool:
        """
        关闭长期记忆管理器
        
        Returns:
            bool: 关闭是否成功
        """
        logger.info(f"正在关闭长期记忆管理器 (ID: {self.module_id})...")
        
        try:
            # 取消事件订阅
            self._unsubscribe_events()
            
            logger.success(f"长期记忆管理器关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"长期记忆管理器关闭失败: {str(e)}")
            return False
    
    def get_core_memories(self) -> List[Dict[str, Any]]:
        """
        获取核心记忆列表
        
        Returns:
            List: 核心记忆列表
        """
        # 检查缓存是否需要更新
        if self._should_update_cache():
            self._update_memory_cache()
        
        return self.core_memories_cache
    
    def get_long_term_memories(self, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取长期记忆列表
        
        Args:
            limit: 返回的记忆数量限制，如果为None则使用配置的默认值
            
        Returns:
            List: 长期记忆列表
        """
        # 检查缓存是否需要更新
        if self._should_update_cache():
            self._update_memory_cache()
        
        # 使用配置的默认限制或指定的限制
        if limit is None:
            limit = self.ltm_config["long_term_memory_limit"]
        
        return self.long_term_memories_cache[:limit]
    
    def search_long_term_memories(self, 
                                query: str, 
                                filters: Dict = None, 
                                limit: int = None) -> List[Dict[str, Any]]:
        """
        搜索长期记忆
        
        Args:
            query: 搜索查询
            filters: 过滤条件
            limit: 结果数量限制，如果为None则使用配置的默认值
            
        Returns:
            List: 匹配的长期记忆列表
        """
        if not self.memory_integration or not self.memory_integration.episodic_memory:
            return []
        
        # 使用配置的默认限制或指定的限制
        if limit is None:
            limit = self.ltm_config["long_term_memory_limit"]
        
        # 准备过滤条件
        if filters is None:
            filters = {}
        
        # 添加长期记忆过滤
        filters["is_long_term"] = True
        
        # 使用情境记忆模块搜索
        results = self.memory_integration.episodic_memory.search_memories(
            query, filters, limit
        )
        
        # 增强检索到的记忆
        for memory in results:
            self._enhance_memory_on_retrieval(memory)
        
        return results
    
    def get_memories_by_emotion(self, 
                              emotion: str, 
                              limit: int = None) -> List[Dict[str, Any]]:
        """
        按情感获取长期记忆
        
        Args:
            emotion: 情感类型
            limit: 结果数量限制，如果为None则使用配置的默认值
            
        Returns:
            List: 匹配情感的长期记忆列表
        """
        if not self.memory_integration or not self.memory_integration.episodic_memory:
            return []
        
        # 使用配置的默认限制或指定的限制
        if limit is None:
            limit = self.ltm_config["long_term_memory_limit"]
        
        # 准备过滤条件
        filters = {
            "is_long_term": True,
            "emotion": emotion
        }
        
        # 使用情境记忆模块搜索
        results = self.memory_integration.episodic_memory.search_memories(
            "", filters, limit
        )
        
        # 增强检索到的记忆
        for memory in results:
            self._enhance_memory_on_retrieval(memory)
        
        return results
    
    def get_memories_by_tag(self, 
                          tag: str, 
                          limit: int = None) -> List[Dict[str, Any]]:
        """
        按标签获取长期记忆
        
        Args:
            tag: 标签
            limit: 结果数量限制，如果为None则使用配置的默认值
            
        Returns:
            List: 匹配标签的长期记忆列表
        """
        if not self.memory_integration or not self.memory_integration.episodic_memory:
            return []
        
        # 使用配置的默认限制或指定的限制
        if limit is None:
            limit = self.ltm_config["long_term_memory_limit"]
        
        # 准备过滤条件
        filters = {
            "is_long_term": True,
            "tags": tag
        }
        
        # 使用情境记忆模块搜索
        results = self.memory_integration.episodic_memory.search_memories(
            "", filters, limit
        )
        
        # 增强检索到的记忆
        for memory in results:
            self._enhance_memory_on_retrieval(memory)
        
        return results
    
    def get_memory_by_id(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取长期记忆
        
        Args:
            memory_id: 记忆ID
            
        Returns:
            Dict或None: 长期记忆对象，如果不存在则返回None
        """
        if not self.memory_integration or not self.memory_integration.episodic_memory:
            return None
        
        # 从情境记忆获取
        memory = self.memory_integration.episodic_memory.get_memory(memory_id)
        
        # 验证是否为长期记忆
        if memory and memory.get("is_long_term", False):
            # 增强检索到的记忆
            self._enhance_memory_on_retrieval(memory)
            return memory
        
        return None

    def store_long_term_memory(self, memory_data: Dict[str, Any]) -> bool:
        """
        🔥 香草修复：存储长期记忆

        Args:
            memory_data: 记忆数据

        Returns:
            bool: 存储成功返回True
        """
        try:
            if not self.memory_integration or not self.memory_integration.episodic_memory:
                logger.warning_status("记忆整合模块未初始化，无法存储长期记忆")
                return False

            # 确保记忆标记为长期记忆
            memory_data["is_long_term"] = True
            memory_data["stored_time"] = time.time()

            # 如果没有ID，生成一个
            if "id" not in memory_data:
                memory_data["id"] = f"ltm_{int(time.time())}_{hash(str(memory_data))}"

            # 使用情境记忆模块存储
            success = self.memory_integration.episodic_memory.store_memory(memory_data)

            if success:
                # 更新缓存
                self._update_memory_cache()

                # 发布存储事件
                self.event_bus.publish("long_term_memory_stored", {
                    "memory_id": memory_data["id"],
                    "content": memory_data.get("content", ""),
                    "timestamp": memory_data["stored_time"]
                })

                logger.info(f"长期记忆存储成功: {memory_data['id']}")

            return success

        except Exception as e:
            logger.error_status(f"存储长期记忆失败: {e}")
            return False

    def retrieve_long_term_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """
        🔥 香草修复：检索长期记忆

        Args:
            memory_id: 记忆ID

        Returns:
            Dict或None: 记忆数据，如果不存在则返回None
        """
        try:
            # 使用现有的get_memory_by_id方法
            memory = self.get_memory_by_id(memory_id)

            if memory:
                logger.info(f"长期记忆检索成功: {memory_id}")

                # 发布检索事件
                self.event_bus.publish("long_term_memory_retrieved", {
                    "memory_id": memory_id,
                    "content": memory.get("content", ""),
                    "timestamp": time.time()
                })

            return memory

        except Exception as e:
            logger.error_status(f"检索长期记忆失败: {e}")
            return None

    def consolidate_memory(self, memory_data: Dict[str, Any]) -> bool:
        """
        🔥 香草修复：巩固记忆

        Args:
            memory_data: 记忆数据

        Returns:
            bool: 巩固成功返回True
        """
        try:
            if not memory_data or "id" not in memory_data:
                logger.warning_status("无效的记忆数据，无法巩固")
                return False

            # 增强记忆强度
            current_strength = memory_data.get("strength", 0.5)
            memory_data["strength"] = min(1.0, current_strength + 0.1)

            # 更新巩固时间
            memory_data["last_consolidated"] = time.time()
            memory_data["consolidation_count"] = memory_data.get("consolidation_count", 0) + 1

            # 如果记忆强度足够高，标记为长期记忆
            if memory_data["strength"] >= 0.8:
                memory_data["is_long_term"] = True

            # 更新记忆
            if self.memory_integration and self.memory_integration.episodic_memory:
                success = self.memory_integration.episodic_memory.update_memory(
                    memory_data["id"], memory_data
                )

                if success:
                    # 更新缓存
                    self._update_memory_cache()

                    # 发布巩固事件
                    self.event_bus.publish("memory_consolidated", {
                        "memory_id": memory_data["id"],
                        "strength": memory_data["strength"],
                        "is_long_term": memory_data.get("is_long_term", False),
                        "timestamp": memory_data["last_consolidated"]
                    })

                    logger.info(f"记忆巩固成功: {memory_data['id']}")

                return success

            return False

        except Exception as e:
            logger.error_status(f"巩固记忆失败: {e}")
            return False

    def trigger_memory_consolidation(self) -> bool:
        """
        触发记忆巩固过程
        
        Returns:
            bool: 巩固是否成功
        """
        if not self.memory_integration:
            return False
        
        try:
            # 执行长期记忆整合
            success = self.memory_integration.integrate_long_term_memories()
            
            if success:
                # 更新上次巩固时间
                self.last_consolidation = time.time()
                
                # 更新缓存
                self._update_memory_cache()
                
                # 发送巩固完成事件
                self.event_bus.publish("memory_consolidation_completed", {
                    "module_id": self.module_id,
                    "timestamp": self.last_consolidation
                })
            
            return success
            
        except Exception as e:
            logger.error_status(f"触发记忆巩固失败: {str(e)}")
            return False
    
    def generate_memory_reflection(self, 
                                limit: int = 5) -> Dict[str, Any]:
        """
        生成记忆反思
        
        Args:
            limit: 用于反思的记忆数量限制
            
        Returns:
            Dict: 反思结果
        """
        if not self.memory_integration:
            return {"success": False, "message": "记忆整合模块未初始化"}
        
        # 检查是否启用记忆反思
        if not self.ltm_config["enable_memory_reflection"]:
            return {"success": False, "message": "记忆反思功能未启用"}
        
        try:
            # 获取核心记忆和长期记忆
            core_memories = self.get_core_memories()
            long_term_memories = self.get_long_term_memories(limit)
            
            # 组合核心记忆和长期记忆
            memories_for_reflection = core_memories + [m for m in long_term_memories 
                                                     if m not in core_memories]
            
            # 限制数量
            memories_for_reflection = memories_for_reflection[:limit]
            
            # 构建反思结果
            reflection = {
                "success": True,
                "timestamp": time.time(),
                "memories_analyzed": len(memories_for_reflection),
                "core_memories_count": len(core_memories),
                "reflections": []
            }
            
            # 按主题分组记忆
            topic_groups = {}
            for memory in memories_for_reflection:
                tags = memory.get("tags", [])
                for tag in tags:
                    if tag not in topic_groups:
                        topic_groups[tag] = []
                    topic_groups[tag].append(memory)
            
            # 生成主题反思
            for topic, memories in topic_groups.items():
                if len(memories) >= 2:  # 至少有2个记忆才形成主题反思
                    reflection["reflections"].append({
                        "type": "topic",
                        "topic": topic,
                        "memories_count": len(memories),
                        "summary": f"关于'{topic}'的记忆反思",
                        "memory_ids": [m["id"] for m in memories]
                    })
            
            # 情感分析
            emotions = {}
            for memory in memories_for_reflection:
                if "emotion" in memory:
                    emotion = memory["emotion"]
                    if emotion not in emotions:
                        emotions[emotion] = 0
                    emotions[emotion] += 1
            
            # 找出主导情感
            if emotions:
                dominant_emotion = max(emotions.items(), key=lambda x: x[1])
                reflection["reflections"].append({
                    "type": "emotion",
                    "emotion": dominant_emotion[0],
                    "count": dominant_emotion[1],
                    "summary": f"主导情感为'{dominant_emotion[0]}'",
                })
            
            return reflection
            
        except Exception as e:
            logger.error_status(f"生成记忆反思失败: {str(e)}")
            return {"success": False, "message": str(e)}
    
    def _update_memory_cache(self) -> None:
        """更新长期记忆缓存"""
        if not self.memory_integration:
            return
        
        try:
            # 更新核心记忆缓存
            self.core_memories_cache = self.memory_integration.get_core_memories()
            
            # 更新长期记忆缓存
            self.long_term_memories_cache = self.memory_integration.get_long_term_memories(
                self.ltm_config["long_term_memory_limit"]
            )
            
            # 更新缓存时间
            self.last_cache_update = time.time()
            
        except Exception as e:
            logger.error_status(f"更新长期记忆缓存失败: {str(e)}")
    
    def _should_update_cache(self) -> bool:
        """检查是否应该更新缓存"""
        # 如果从未更新过缓存
        if self.last_cache_update == 0:
            return True
        
        # 如果缓存为空
        if not self.core_memories_cache and not self.long_term_memories_cache:
            return True
        
        # 如果距离上次更新超过5分钟
        if time.time() - self.last_cache_update > 300:
            return True
        
        return False
    
    def _enhance_memory_on_retrieval(self, memory: Dict[str, Any]) -> None:
        """
        增强检索到的记忆
        
        Args:
            memory: 记忆对象
        """
        if not memory:
            return
        
        # 更新访问信息
        memory["last_accessed"] = time.time()
        memory["access_count"] = memory.get("access_count", 0) + 1
        
        # 增强记忆强度
        boost = self.ltm_config["memory_retrieval_boost"]
        memory["strength"] = min(1.0, memory.get("strength", 0.5) + boost)
        
        # 发布记忆检索事件
        self.event_bus.publish("long_term_memory_retrieved", {
            "memory_id": memory["id"],
            "content": memory.get("content", ""),
            "memory_type": memory.get("memory_type", "episodic")
        })
    
    def _subscribe_events(self) -> None:
        """订阅相关事件"""
        # 订阅每日维护事件
        self.event_bus.subscribe("daily_maintenance", self._on_daily_maintenance)
        
        # 订阅长期记忆整合完成事件
        self.event_bus.subscribe("long_term_memory_integrated", self._on_long_term_memory_integrated)
        
        # 订阅核心记忆识别事件
        self.event_bus.subscribe("core_memory_identified", self._on_core_memory_identified)
    
    def _unsubscribe_events(self) -> None:
        """取消事件订阅"""
        self.event_bus.unsubscribe("daily_maintenance", self._on_daily_maintenance)
        self.event_bus.unsubscribe("long_term_memory_integrated", self._on_long_term_memory_integrated)
        self.event_bus.unsubscribe("core_memory_identified", self._on_core_memory_identified)
    
    def _on_daily_maintenance(self, event_data: Dict) -> None:
        """
        处理每日维护事件
        
        Args:
            event_data: 事件数据
        """
        # 检查是否需要执行记忆巩固
        current_time = time.time()
        time_since_last = current_time - self.last_consolidation
        
        if time_since_last >= self.ltm_config["consolidation_interval"]:
            # 触发记忆巩固
            self.trigger_memory_consolidation()
    
    def _on_long_term_memory_integrated(self, event_data: Dict) -> None:
        """
        处理长期记忆整合完成事件
        
        Args:
            event_data: 事件数据
        """
        # 更新记忆缓存
        self._update_memory_cache()
    
    def _on_core_memory_identified(self, event_data: Dict) -> None:
        """
        处理核心记忆识别事件
        
        Args:
            event_data: 事件数据
        """
        # 更新记忆缓存
        self._update_memory_cache()
    
    def get_module_info(self) -> Dict[str, Any]:
        """
        获取模块信息
        
        Returns:
            Dict: 模块信息
        """
        return {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "module_name": self.module_name,
            "core_memories_count": len(self.core_memories_cache),
            "long_term_memories_count": len(self.long_term_memories_cache),
            "last_consolidation": datetime.fromtimestamp(self.last_consolidation).strftime("%Y-%m-%d %H:%M:%S"),
            "last_cache_update": datetime.fromtimestamp(self.last_cache_update).strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def _initialize_module(self) -> bool:
        """
        初始化模块内部功能
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化存储管理器
            if self.ltm_config["storage_enabled"]:
                self.storage = get_storage_instance()
            
            # 获取记忆整合模块实例
            integration_id = self.ltm_config["memory_integration_id"]
            self.memory_integration = MemoryIntegration.get_instance(integration_id)
            
            # 初始化记忆整合模块
            self.memory_integration.initialize()
            
            # 更新记忆缓存
            self._update_memory_cache()
            
            # 订阅事件
            self._subscribe_events()
            
            logger.success(f"长期记忆管理器初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"长期记忆管理器初始化失败: {str(e)}")
            return False
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
        
        Returns:
            Dict: 处理结果
        """
        action = input_data.get("action", "")
        result = {"success": False, "message": "未知操作"}
        
        try:
            if action == "get_core_memories":
                memories = self.get_core_memories()
                result = {"success": True, "memories": memories}
                
            elif action == "get_long_term_memories":
                limit = input_data.get("limit")
                memories = self.get_long_term_memories(limit)
                result = {"success": True, "memories": memories}
                
            elif action == "search_long_term_memories":
                query = input_data.get("query", "")
                filters = input_data.get("filters")
                limit = input_data.get("limit")
                memories = self.search_long_term_memories(query, filters, limit)
                result = {"success": True, "memories": memories}
                
            elif action == "get_memories_by_emotion":
                emotion = input_data.get("emotion", "")
                limit = input_data.get("limit")
                memories = self.get_memories_by_emotion(emotion, limit)
                result = {"success": True, "memories": memories}
                
            elif action == "get_memories_by_tag":
                tag = input_data.get("tag", "")
                limit = input_data.get("limit")
                memories = self.get_memories_by_tag(tag, limit)
                result = {"success": True, "memories": memories}
                
            elif action == "get_memory_by_id":
                memory_id = input_data.get("memory_id", "")
                memory = self.get_memory_by_id(memory_id)
                result = {"success": True if memory else False, "memory": memory}
                
            elif action == "trigger_memory_consolidation":
                success = self.trigger_memory_consolidation()
                result = {"success": success}
                
            elif action == "generate_memory_reflection":
                limit = input_data.get("limit", 5)
                reflection = self.generate_memory_reflection(limit)
                result = reflection
                
            elif action == "get_module_info":
                info = self.get_module_info()
                result = {"success": True, "info": info}
                
            else:
                result = {"success": False, "message": f"不支持的操作: {action}"}
                
        except Exception as e:
            result = {"success": False, "message": str(e)}
            
        return result
    
    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
        
        Returns:
            bool: 更新是否成功
        """
        try:
            # 检查是否需要进行记忆巩固
            current_time = time.time()
            time_since_last = current_time - self.last_consolidation
            
            if time_since_last >= self.ltm_config["consolidation_interval"]:
                # 触发记忆巩固
                self.trigger_memory_consolidation()
            
            # 如果上下文中有特定事件，处理它们
            if "events" in context:
                for event in context["events"]:
                    event_type = event.get("type")
                    
                    if event_type == "daily_maintenance":
                        # 执行每日维护
                        self._on_daily_maintenance(event)
                    
                    elif event_type == "long_term_memory_integrated":
                        # 处理长期记忆整合完成
                        self._on_long_term_memory_integrated(event)
                    
                    elif event_type == "core_memory_identified":
                        # 处理核心记忆识别
                        self._on_core_memory_identified(event)
            
            return True
            
        except Exception as e:
            logger.error_status(f"更新长期记忆管理器失败: {str(e)}")
            return False
    
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块当前状态
        
        Returns:
            Dict: 模块状态
        """
        return {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "last_consolidation": self.last_consolidation,
            "last_cache_update": self.last_cache_update,
            "ltm_config": self.ltm_config
        }
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        从保存的状态加载模块
        
        Args:
            state: 模块状态
        
        Returns:
            bool: 加载是否成功
        """
        try:
            # 加载配置
            if "ltm_config" in state:
                self.ltm_config.update(state["ltm_config"])
                
            # 加载上次巩固时间
            if "last_consolidation" in state:
                self.last_consolidation = state["last_consolidation"]
                
            # 加载上次缓存更新时间
            if "last_cache_update" in state:
                self.last_cache_update = state["last_cache_update"]
                
            return True
            
        except Exception as e:
            logger.error_status(f"加载长期记忆管理器状态失败: {str(e)}")
            return False
    
    def _shutdown_module(self) -> bool:
        """
        关闭模块
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            # 取消事件订阅
            self._unsubscribe_events()
            
            logger.success(f"长期记忆管理器关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"长期记忆管理器关闭失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            Dict: 模块状态信息
        """
        status = {
            'module_id': self.module_id,
            'module_type': self.module_type,
            'module_name': self.module_name,
            'is_active': self.is_active,
            'core_memories_count': len(self.core_memories_cache),
            'long_term_memories_count': len(self.long_term_memories_cache),
            'last_consolidation': datetime.fromtimestamp(self.last_consolidation).strftime('%Y-%m-%d %H:%M:%S'),
            'last_cache_update': datetime.fromtimestamp(self.last_cache_update).strftime('%Y-%m-%d %H:%M:%S'),
            'last_error': getattr(self, 'last_error', None)
        }
        return status

# 获取模块实例的全局函数
def get_instance(module_id: str = "default", config: Dict = None) -> LongTermMemoryManager:
    """
    🔥 香草修复：获取长期记忆管理器实例，增强参数验证

    Args:
        module_id: 模块ID
        config: 配置参数（必须是字典类型或None）

    Returns:
        LongTermMemoryManager: 模块实例
    """
    # 🔥 香草修复：参数验证和修正
    if not isinstance(module_id, str):
        # 如果第一个参数不是字符串，可能是参数顺序错误
        if isinstance(module_id, dict) and isinstance(config, str):
            # 参数顺序颠倒了，交换回来
            module_id, config = config, module_id
        else:
            # 其他情况，使用默认值
            module_id = "default"

    # 验证config参数
    if config is not None and not isinstance(config, dict):
        # 如果config不是字典类型，重置为None
        config = None

    # 实例字典
    if not hasattr(LongTermMemoryManager, "_instances"):
        LongTermMemoryManager._instances = {}

    # 如果实例不存在，创建新实例
    if module_id not in LongTermMemoryManager._instances:
        instance = LongTermMemoryManager(module_id, config)
        LongTermMemoryManager._instances[module_id] = instance

    return LongTermMemoryManager._instances[module_id]