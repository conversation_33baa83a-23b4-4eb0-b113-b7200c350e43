#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆整合模块 (Memory Integration)

该模块负责整合数字生命体的各种记忆类型(情境、语义、程序记忆)，
提供统一的记忆访问接口，实现记忆间的关联建立和交叉引用。

作者: Claude
创建日期: 2024-07-17
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union
import threading

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from utilities.storage_manager import get_instance as get_storage_instance
from core.integrated_event_bus import get_instance as get_event_bus
from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.memory.episodic_memory import EpisodicMemory
from cognitive_modules.memory.semantic_memory import get_instance as get_semantic_memory
from cognitive_modules.memory.procedural_memory import ProceduralMemory

# 设置日志
logger = get_unified_logger("memory_integration")

class MemoryIntegration(CognitiveModuleBase):
    """记忆整合模块，负责整合各类记忆并提供统一的记忆访问接口"""
    
    def __init__(self, module_id: str = "default", config: Dict = None):
        """
        初始化记忆整合模块
        
        Args:
            module_id: 模块ID
            config: 配置参数
        """
        super().__init__(module_id, "记忆整合模块", config)
        
        # 配置参数
        self.integration_config = {
            "storage_enabled": True,                # 是否启用持久化存储
            "episodic_memory_id": module_id,        # 情境记忆模块ID
            "procedural_memory_id": module_id,      # 程序记忆模块ID
            "association_threshold": 0.5,           # 记忆关联阈值
            "max_association_results": 10,          # 最大关联结果数
            "max_memory_age_days": 365,             # 最大记忆保留天数
            "daily_consolidation_enabled": True,    # 是否启用每日记忆巩固
            "enable_cross_memory_search": True,     # 是否启用跨记忆类型搜索
        }
        
        # 更新配置
        if config:
            # 🔥 老王修复：确保config是字典类型
            if isinstance(config, dict):
                self.integration_config.update(config)
            else:
                logger.warning_status(f"记忆整合模块配置跳过更新: config不是字典类型，而是{type(config)}")
        
        # 存储管理器
        self.storage = None
        
        # 事件总线
        self.event_bus = get_event_bus()
        
        # 记忆模块引用
        self.episodic_memory = None     # 情境记忆
        self.semantic_memory = None     # 语义记忆
        self.procedural_memory = None   # 程序记忆
        
        # 记忆关联缓存
        self.memory_associations = {
            "episodic_to_semantic": {},   # 情境记忆到语义记忆的关联
            "episodic_to_procedural": {}, # 情境记忆到程序记忆的关联
            "semantic_to_episodic": {},   # 语义记忆到情境记忆的关联
            "semantic_to_procedural": {}, # 语义记忆到程序记忆的关联
            "procedural_to_episodic": {}, # 程序记忆到情境记忆的关联
            "procedural_to_semantic": {}, # 程序记忆到语义记忆的关联
        }
        
        # 上次记忆整合时间
        self.last_integration = time.time()
        
        # 添加last_error属性
        self.last_error = None
    
    def initialize(self, config: Dict = None) -> bool:
        """
        初始化记忆整合模块
        
        Args:
            config: 配置参数
        
        Returns:
            bool: 初始化是否成功
        """
        logger.success(f"正在初始化记忆整合模块 (ID: {self.module_id})...")
        
        try:
            # 🔥 老王修复：先调用父类的initialize方法
            if not super().initialize(config):
                return False
            
            # 初始化存储管理器
            if self.integration_config["storage_enabled"]:
                self.storage = get_storage_instance()
                
                # 加载关联缓存
                self._load_associations()
            
            # 获取各记忆模块实例
            episodic_memory_id = self.integration_config["episodic_memory_id"]
            self.episodic_memory = EpisodicMemory.get_instance(episodic_memory_id)
            
            procedural_memory_id = self.integration_config["procedural_memory_id"]
            self.procedural_memory = ProceduralMemory.get_instance(procedural_memory_id)
            
            self.semantic_memory = get_semantic_memory()
            
            # 初始化各记忆模块
            self.episodic_memory.initialize(config)
            self.procedural_memory.initialize(config)
            self.semantic_memory.initialize(config)
            
            # 订阅事件
            self._subscribe_events()
            
            # 启动定期整合任务
            self._schedule_integration()
            
            logger.success(f"记忆整合模块初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"记忆整合模块初始化失败: {str(e)}")
            return False
    
    def shutdown(self) -> bool:
        """
        关闭记忆整合模块
        
        Returns:
            bool: 关闭是否成功
        """
        logger.info(f"正在关闭记忆整合模块 (ID: {self.module_id})...")
        
        try:
            # 保存关联缓存
            if self.integration_config["storage_enabled"] and self.storage:
                self._save_associations()
            
            # 关闭各记忆模块
            if self.episodic_memory:
                self.episodic_memory.shutdown()
            
            if self.procedural_memory:
                self.procedural_memory.shutdown()
            
            if self.semantic_memory:
                self.semantic_memory.shutdown()
            
            # 取消事件订阅
            self._unsubscribe_events()
            
            logger.success(f"记忆整合模块关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"记忆整合模块关闭失败: {str(e)}")
            return False
    
    def add_episodic_memory(self, memory_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加情境记忆
        
        Args:
            memory_data: 记忆数据
        
        Returns:
            Dict: 添加的记忆对象
        """
        if not self.episodic_memory:
            raise ValueError("情境记忆模块未初始化")
        
        # 添加情境记忆
        memory = self.episodic_memory.add_memory(memory_data)
        
        # 建立记忆关联
        self._create_memory_associations(memory, "episodic")
        
        return memory
    
    def add_semantic_memory(self, memory_data: Dict[str, Any]) -> str:
        """
        添加语义记忆
        
        Args:
            memory_data: 记忆数据
        
        Returns:
            str: 记忆ID
        """
        if not self.semantic_memory:
            raise ValueError("语义记忆模块未初始化")
        
        # 添加语义记忆
        content = memory_data.get("content")
        metadata = {k: v for k, v in memory_data.items() if k != "content"}
        
        memory_id = self.semantic_memory.add_memory(content, metadata)
        
        # 获取完整记忆对象
        memory = self.semantic_memory.get_memory(memory_id)
        
        # 建立记忆关联
        self._create_memory_associations(memory, "semantic")
        
        return memory_id
    
    def add_procedural_memory(self, memory_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加程序记忆
        
        Args:
            memory_data: 记忆数据
        
        Returns:
            Dict: 添加的记忆对象
        """
        if not self.procedural_memory:
            raise ValueError("程序记忆模块未初始化")
        
        # 添加程序记忆
        memory = self.procedural_memory.add_skill(memory_data)
        
        # 建立记忆关联
        self._create_memory_associations(memory, "procedural")
        
        return memory
    
    def get_episodic_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """
        获取情境记忆
        
        Args:
            memory_id: 记忆ID
        
        Returns:
            Dict或None: 记忆对象
        """
        if not self.episodic_memory:
            raise ValueError("情境记忆模块未初始化")
        
        return self.episodic_memory.get_memory(memory_id)
    
    def get_semantic_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """
        获取语义记忆
        
        Args:
            memory_id: 记忆ID
        
        Returns:
            Dict或None: 记忆对象
        """
        if not self.semantic_memory:
            raise ValueError("语义记忆模块未初始化")
        
        return self.semantic_memory.get_memory(memory_id)
    
    def get_procedural_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """
        获取程序记忆
        
        Args:
            memory_id: 记忆ID
        
        Returns:
            Dict或None: 记忆对象
        """
        if not self.procedural_memory:
            raise ValueError("程序记忆模块未初始化")
        
        return self.procedural_memory.get_skill(memory_id)
    
    def search_memory(self, 
                     query: str, 
                     memory_types: List[str] = None,
                     filters: Dict = None, 
                     limit: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """
        搜索记忆
        
        Args:
            query: 搜索查询
            memory_types: 要搜索的记忆类型列表，可选值: "episodic", "semantic", "procedural"
            filters: 过滤条件
            limit: 每种记忆类型的结果数量限制
        
        Returns:
            Dict: 各类型的记忆搜索结果
        """
        if memory_types is None:
            memory_types = ["episodic", "semantic", "procedural"]
        
        results = {}
        
        # 搜索情境记忆
        if "episodic" in memory_types and self.episodic_memory:
            try:
                episodic_results = self.episodic_memory.search_memories(query, filters, limit)
                results["episodic"] = episodic_results
            except Exception as e:
                logger.error_status(f"搜索情境记忆失败: {str(e)}")
                results["episodic"] = []
        
        # 搜索语义记忆
        if "semantic" in memory_types and self.semantic_memory:
            try:
                semantic_categories = filters.get("category", []) if filters else []
                semantic_results = self.semantic_memory.search_by_content(query, semantic_categories, limit)
                results["semantic"] = semantic_results
            except Exception as e:
                logger.error_status(f"搜索语义记忆失败: {str(e)}")
                results["semantic"] = []
        
        # 搜索程序记忆
        if "procedural" in memory_types and self.procedural_memory:
            try:
                procedural_results = self.procedural_memory.search_skills(query, filters, limit)
                results["procedural"] = procedural_results
            except Exception as e:
                logger.error_status(f"搜索程序记忆失败: {str(e)}")
                results["procedural"] = []
        
        return results
    
    def get_associated_memories(self, 
                               memory_id: str, 
                               memory_type: str,
                               target_types: List[str] = None,
                               limit: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取与指定记忆相关联的记忆
        
        Args:
            memory_id: 记忆ID
            memory_type: 记忆类型，可选值: "episodic", "semantic", "procedural"
            target_types: 目标记忆类型列表，可选值: "episodic", "semantic", "procedural"
            limit: 每种类型的结果数量限制
        
        Returns:
            Dict: 各类型的关联记忆
        """
        if target_types is None:
            # 排除自身类型
            target_types = [t for t in ["episodic", "semantic", "procedural"] if t != memory_type]
        
        results = {}
        
        # 获取关联记忆
        for target_type in target_types:
            association_key = f"{memory_type}_to_{target_type}"
            
            if association_key not in self.memory_associations:
                results[target_type] = []
                continue
            
            # 获取该记忆的关联
            associated_ids = self.memory_associations[association_key].get(memory_id, [])
            
            # 获取关联记忆对象
            associated_memories = []
            for assoc_id in associated_ids[:limit]:
                if target_type == "episodic":
                    memory = self.get_episodic_memory(assoc_id)
                elif target_type == "semantic":
                    memory = self.get_semantic_memory(assoc_id)
                elif target_type == "procedural":
                    memory = self.get_procedural_memory(assoc_id)
                
                if memory:
                    associated_memories.append(memory)
            
            results[target_type] = associated_memories
        
        return results
    
    def create_association(self, 
                          source_id: str, 
                          source_type: str,
                          target_id: str, 
                          target_type: str,
                          strength: float = 1.0) -> bool:
        """
        创建记忆关联
        
        Args:
            source_id: 源记忆ID
            source_type: 源记忆类型
            target_id: 目标记忆ID
            target_type: 目标记忆类型
            strength: 关联强度
        
        Returns:
            bool: 创建是否成功
        """
        if source_type == target_type and source_id == target_id:
            return False  # 不允许自关联
        
        # 构建关联键
        association_key = f"{source_type}_to_{target_type}"
        
        if association_key not in self.memory_associations:
            logger.warning_status(f"不支持的关联类型: {association_key}")
            return False
        
        # 初始化源记忆的关联列表(如果不存在)
        if source_id not in self.memory_associations[association_key]:
            self.memory_associations[association_key][source_id] = []
        
        # 检查是否已存在关联
        if target_id in self.memory_associations[association_key][source_id]:
            return True  # 已存在关联
        
        # 添加关联
        self.memory_associations[association_key][source_id].append(target_id)
        
        # 发布记忆关联事件
        self.event_bus.publish("memory_associated", {
            "source_id": source_id,
            "source_type": source_type,
            "target_id": target_id,
            "target_type": target_type,
            "strength": strength
        })
        
        return True
    
    def remove_association(self, 
                          source_id: str, 
                          source_type: str,
                          target_id: str, 
                          target_type: str) -> bool:
        """
        移除记忆关联
        
        Args:
            source_id: 源记忆ID
            source_type: 源记忆类型
            target_id: 目标记忆ID
            target_type: 目标记忆类型
        
        Returns:
            bool: 移除是否成功
        """
        # 构建关联键
        association_key = f"{source_type}_to_{target_type}"
        
        if association_key not in self.memory_associations:
            return False
        
        # 检查源记忆是否存在关联
        if (source_id not in self.memory_associations[association_key] or
            target_id not in self.memory_associations[association_key][source_id]):
            return False
        
        # 移除关联
        self.memory_associations[association_key][source_id].remove(target_id)
        
        # 发布记忆关联移除事件
        self.event_bus.publish("memory_association_removed", {
            "source_id": source_id,
            "source_type": source_type,
            "target_id": target_id,
            "target_type": target_type
        })
        
        return True
    
    def _create_memory_associations(self, memory: Dict[str, Any], memory_type: str) -> None:
        """
        为新记忆创建关联
        
        Args:
            memory: 记忆对象
            memory_type: 记忆类型
        """
        if not memory or "id" not in memory:
            return
        
        memory_id = memory["id"]
        
        # 获取记忆内容
        if memory_type == "episodic":
            content = memory.get("content", "")
        elif memory_type == "semantic":
            content = memory.get("content", "")
        elif memory_type == "procedural":
            content = memory.get("name", "") + " " + memory.get("description", "")
        else:
            return
        
        # 根据内容搜索其他类型的记忆
        for target_type in ["episodic", "semantic", "procedural"]:
            if target_type == memory_type:
                continue  # 跳过相同类型
            
            # 搜索目标类型的相关记忆
            search_results = self.search_memory(
                content, 
                memory_types=[target_type], 
                limit=self.integration_config["max_association_results"]
            )
            
            if target_type not in search_results or not search_results[target_type]:
                continue
            
            # 为每个搜索结果创建关联
            for result in search_results[target_type]:
                # 计算关联强度(简化版)
                strength = 0.7  # 默认强度
                
                # 根据阈值创建关联
                if strength >= self.integration_config["association_threshold"]:
                    target_id = result["id"]
                    self.create_association(memory_id, memory_type, target_id, target_type, strength)
    
    def _integrate_memories(self) -> None:
        """记忆整合过程"""
        logger.info("开始记忆整合过程...")
        
        try:
            # 清理和更新现有关联
            self._clean_associations()
            
            # 发现新的关联
            self._discover_new_associations()
            
            # 更新上次整合时间
            self.last_integration = time.time()
            
            # 保存关联
            if self.integration_config["storage_enabled"] and self.storage:
                self._save_associations()
                
            logger.success("记忆整合完成")
            
        except Exception as e:
            logger.error_status(f"记忆整合失败: {str(e)}")
    
    def _clean_associations(self) -> None:
        """清理无效的记忆关联"""
        for assoc_type, associations in self.memory_associations.items():
            # 解析关联类型
            source_type, target_type = assoc_type.split("_to_")
            
            invalid_sources = []
            
            # 检查每个源记忆
            for source_id, target_ids in associations.items():
                # 验证源记忆是否存在
                source_exists = False
                if source_type == "episodic":
                    source_exists = self.get_episodic_memory(source_id) is not None
                elif source_type == "semantic":
                    source_exists = self.get_semantic_memory(source_id) is not None
                elif source_type == "procedural":
                    source_exists = self.get_procedural_memory(source_id) is not None
                
                if not source_exists:
                    invalid_sources.append(source_id)
                    continue
                
                # 验证目标记忆
                valid_targets = []
                for target_id in target_ids:
                    target_exists = False
                    if target_type == "episodic":
                        target_exists = self.get_episodic_memory(target_id) is not None
                    elif target_type == "semantic":
                        target_exists = self.get_semantic_memory(target_id) is not None
                    elif target_type == "procedural":
                        target_exists = self.get_procedural_memory(target_id) is not None
                    
                    if target_exists:
                        valid_targets.append(target_id)
                
                # 更新有效目标
                associations[source_id] = valid_targets
            
            # 移除无效源
            for source_id in invalid_sources:
                if source_id in associations:
                    del associations[source_id]
    
    def _discover_new_associations(self) -> None:
        """发现新的记忆关联"""
        # 这个方法可以使用更复杂的算法来发现记忆之间的潜在关联
        # 例如，基于共同主题、情感、时间接近度等
        # 此处仅实现一个简化版本
        
        # 随机选择一些记忆进行关联分析
        # 在实际应用中，可以使用更智能的选择策略
        
        try:
            # 获取一些情境记忆进行分析
            if self.episodic_memory:
                sample_episodic = list(self.episodic_memory.memories.values())[:10]
                
                for memory in sample_episodic:
                    # 为每个样本记忆创建关联
                    self._create_memory_associations(memory, "episodic")
            
            # 获取一些程序记忆进行分析
            if self.procedural_memory:
                sample_procedural = list(self.procedural_memory.skills.values())[:10]
                
                for skill in sample_procedural:
                    # 为每个样本技能创建关联
                    self._create_memory_associations(skill, "procedural")
                    
        except Exception as e:
            logger.error_status(f"发现新关联失败: {str(e)}")
    
    def _load_associations(self) -> None:
        """从持久化存储加载记忆关联"""
        try:
            # 从JSON文件加载
            associations = self.storage.load_json(
                f"memories/{self.module_id}/memory_associations.json", 
                default={}
            )
            
            if associations and isinstance(associations, dict):
                self.memory_associations = associations
                
                logger.info(f"已从存储加载记忆关联")
        except Exception as e:
            logger.error_status(f"加载记忆关联失败: {str(e)}")
    
    def _save_associations(self) -> None:
        """将记忆关联保存到持久化存储"""
        try:
            # 保存到JSON文件
            self.storage.save_json(
                f"memories/{self.module_id}/memory_associations.json", 
                self.memory_associations
            )
            
            logger.info(f"已将记忆关联保存到存储")
        except Exception as e:
            logger.error_status(f"保存记忆关联失败: {str(e)}")
    
    def _subscribe_events(self) -> None:
        """订阅相关事件"""
        # 订阅记忆创建事件
        self.event_bus.subscribe("memory_created", self._on_memory_created)
        
        # 订阅每日维护事件
        self.event_bus.subscribe("daily_maintenance", self._on_daily_maintenance)
    
    def _unsubscribe_events(self) -> None:
        """取消事件订阅"""
        self.event_bus.unsubscribe("memory_created", self._on_memory_created)
        self.event_bus.unsubscribe("daily_maintenance", self._on_daily_maintenance)
    
    def _on_memory_created(self, event_data: Dict) -> None:
        """
        处理记忆创建事件
        
        Args:
            event_data: 事件数据
        """
        memory_id = event_data.get("memory_id")
        memory_type = event_data.get("memory_type")
        
        if not memory_id or not memory_type:
            return
        
        # 获取完整记忆对象
        memory = None
        if memory_type == "episodic":
            memory = self.get_episodic_memory(memory_id)
        elif memory_type == "semantic":
            memory = self.get_semantic_memory(memory_id)
        elif memory_type == "skill":  # 程序记忆的事件类型为skill
            memory_type = "procedural"  # 内部统一使用procedural
            memory = self.get_procedural_memory(memory_id)
        
        if memory:
            # 为新记忆创建关联
            self._create_memory_associations(memory, memory_type)
    
    def _on_daily_maintenance(self, event_data: Dict) -> None:
        """
        执行每日维护任务
        
        Args:
            event_data: 事件数据
        """
        # 如果启用每日巩固
        if self.integration_config["daily_consolidation_enabled"]:
            # 执行长期记忆整合
            self.integrate_long_term_memories()
            
            # 发送整合完成事件
            self.event_bus.publish("long_term_memory_integrated", {
                "module_id": self.module_id,
                "timestamp": time.time()
            })
    
    def _schedule_integration(self) -> None:
        """安排记忆整合任务"""
        # 每天执行一次记忆整合
        if self.integration_config["daily_consolidation_enabled"]:
            threading.Timer(24 * 3600, self._integrate_memories).start()
    
    # 静态方法: 获取模块实例
    @staticmethod
    def get_instance(module_id: str = "default", config: Dict = None) -> 'MemoryIntegration':
        """
        获取记忆整合模块实例
        
        Args:
            module_id: 模块ID
            config: 配置参数
        
        Returns:
            MemoryIntegration: 模块实例
        """
        # 实例字典
        if not hasattr(MemoryIntegration, "_instances"):
            MemoryIntegration._instances = {}
        
        # 如果实例不存在，创建新实例
        if module_id not in MemoryIntegration._instances:
            instance = MemoryIntegration(module_id, config)
            MemoryIntegration._instances[module_id] = instance
        
        return MemoryIntegration._instances[module_id]

    def _initialize_module(self) -> bool:
        """
        初始化模块内部功能
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化存储管理器
            if self.integration_config["storage_enabled"]:
                self.storage = get_storage_instance()
                
                # 加载关联缓存
                self._load_associations()
            
            # 获取各记忆模块实例
            episodic_memory_id = self.integration_config["episodic_memory_id"]
            self.episodic_memory = EpisodicMemory.get_instance(episodic_memory_id)
            
            procedural_memory_id = self.integration_config["procedural_memory_id"]
            self.procedural_memory = ProceduralMemory.get_instance(procedural_memory_id)
            
            self.semantic_memory = get_semantic_memory()
            
            # 初始化各记忆模块
            self.episodic_memory.initialize(config)
            self.procedural_memory.initialize(config)
            self.semantic_memory.initialize(config)
            
            # 订阅事件
            self._subscribe_events()
            
            # 启动定期整合任务
            self._schedule_integration()
            
            logger.success(f"记忆整合模块初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"记忆整合模块初始化失败: {str(e)}")
            return False

    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
        
        Returns:
            Dict: 处理结果
        """
        action = input_data.get("action", "")
        result = {"success": False, "message": "未知操作"}
        
        try:
            if action == "add_episodic_memory":
                memory_data = input_data.get("memory_data", {})
                memory = self.add_episodic_memory(memory_data)
                result = {"success": True, "memory": memory}
                
            elif action == "add_semantic_memory":
                memory_data = input_data.get("memory_data", {})
                memory_id = self.add_semantic_memory(memory_data)
                result = {"success": True, "memory_id": memory_id}
                
            elif action == "add_procedural_memory":
                memory_data = input_data.get("memory_data", {})
                memory = self.add_procedural_memory(memory_data)
                result = {"success": True, "memory": memory}
                
            elif action == "get_episodic_memory":
                memory_id = input_data.get("memory_id", "")
                memory = self.get_episodic_memory(memory_id)
                result = {"success": True if memory else False, "memory": memory}
                
            elif action == "get_semantic_memory":
                memory_id = input_data.get("memory_id", "")
                memory = self.get_semantic_memory(memory_id)
                result = {"success": True if memory else False, "memory": memory}
                
            elif action == "get_procedural_memory":
                memory_id = input_data.get("memory_id", "")
                memory = self.get_procedural_memory(memory_id)
                result = {"success": True if memory else False, "memory": memory}
                
            elif action == "search_memory":
                query = input_data.get("query", "")
                memory_types = input_data.get("memory_types")
                filters = input_data.get("filters", {})
                limit = input_data.get("limit", 5)
                memories = self.search_memory(query, memory_types, filters, limit)
                result = {"success": True, "memories": memories}
                
            elif action == "get_associated_memories":
                memory_id = input_data.get("memory_id", "")
                memory_type = input_data.get("memory_type", "")
                target_types = input_data.get("target_types")
                limit = input_data.get("limit", 5)
                associated = self.get_associated_memories(memory_id, memory_type, target_types, limit)
                result = {"success": True, "associated_memories": associated}
                
            elif action == "create_association":
                source_id = input_data.get("source_id", "")
                source_type = input_data.get("source_type", "")
                target_id = input_data.get("target_id", "")
                target_type = input_data.get("target_type", "")
                strength = input_data.get("strength", 1.0)
                success = self.create_association(source_id, source_type, target_id, target_type, strength)
                result = {"success": success}
                
            elif action == "remove_association":
                source_id = input_data.get("source_id", "")
                source_type = input_data.get("source_type", "")
                target_id = input_data.get("target_id", "")
                target_type = input_data.get("target_type", "")
                success = self.remove_association(source_id, source_type, target_id, target_type)
                result = {"success": success}
                
            elif action == "integrate_memories":
                self._integrate_memories()
                result = {"success": True}
                
            else:
                result = {"success": False, "message": f"不支持的操作: {action}"}
                
        except Exception as e:
            result = {"success": False, "message": str(e)}
            
        return result

    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
        
        Returns:
            bool: 更新是否成功
        """
        try:
            # 如果上下文中有特定事件，处理它们
            if "events" in context:
                for event in context["events"]:
                    event_type = event.get("type")
                    
                    if event_type == "memory_created":
                        # 处理记忆创建事件
                        self._on_memory_created(event)
                    
                    elif event_type == "daily_maintenance":
                        # 执行每日维护
                        self._on_daily_maintenance(event)
            
            # 检查是否需要进行记忆整合
            current_time = time.time()
            days_since_integration = (current_time - self.last_integration) / (24 * 3600)
            
            if days_since_integration >= 1 and self.integration_config["daily_consolidation_enabled"]:
                # 执行记忆整合
                self._integrate_memories()
            
            return True
            
        except Exception as e:
            logger.error_status(f"更新记忆整合模块失败: {str(e)}")
            return False

    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块当前状态
        
        Returns:
            Dict: 模块状态
        """
        return {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "memory_associations": self.memory_associations,
            "last_integration": self.last_integration,
            "integration_config": self.integration_config
        }

    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        从保存的状态加载模块
        
        Args:
            state: 模块状态
        
        Returns:
            bool: 加载是否成功
        """
        try:
            # 加载配置
            if "integration_config" in state:
                # 🔥 老王修复：确保integration_config是字典类型
                integration_config = state["integration_config"]
                if isinstance(integration_config, dict):
                    self.integration_config.update(integration_config)
                else:
                    logger.warning_status(f"记忆整合模块状态加载跳过配置: integration_config不是字典类型，而是{type(integration_config)}")
                
            # 加载上次整合时间
            if "last_integration" in state:
                self.last_integration = state["last_integration"]
                
            # 加载记忆关联
            if "memory_associations" in state:
                self.memory_associations = state["memory_associations"]
                
            return True
            
        except Exception as e:
            logger.error_status(f"加载记忆整合模块状态失败: {str(e)}")
            return False

    def _shutdown_module(self) -> bool:
        """
        关闭模块
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            # 保存关联缓存
            if self.integration_config["storage_enabled"] and self.storage:
                self._save_associations()
            
            # 关闭各记忆模块
            if self.episodic_memory:
                self.episodic_memory.shutdown()
            
            if self.procedural_memory:
                self.procedural_memory.shutdown()
            
            if self.semantic_memory:
                self.semantic_memory.shutdown()
            
            # 取消事件订阅
            self._unsubscribe_events()
            
            logger.success(f"记忆整合模块关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"记忆整合模块关闭失败: {str(e)}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            Dict: 模块状态信息
        """
        status = {
            'module_id': self.module_id,
            'module_type': self.module_type,
            'module_name': self.module_name,
            'is_active': self.is_active,
            'episodic_memory_count': len(self.episodic_memory.memories) if self.episodic_memory else 0,
            'procedural_memory_count': len(self.procedural_memory.skills) if self.procedural_memory else 0,
            'association_count': sum(len(assocs) for assocs in self.memory_associations.values()),
            'last_integration': datetime.fromtimestamp(self.last_integration).strftime('%Y-%m-%d %H:%M:%S'),
            'last_error': self.last_error
        }
        return status

    def integrate_long_term_memories(self) -> bool:
        """
        整合长期记忆，增强跨记忆类型的关联
        
        Returns:
            bool: 整合是否成功
        """
        try:
            logger.info("开始长期记忆整合...")
            
            # 1. 获取标记为长期记忆的情境记忆
            long_term_episodic = self._get_long_term_episodic_memories()
            
            if long_term_episodic:
                logger.info(f"找到 {len(long_term_episodic)} 条长期情境记忆")
                
                # 2. 为长期记忆创建语义摘要
                self._create_semantic_summaries(long_term_episodic)
                
                # 3. 增强长期记忆间的关联
                self._enhance_long_term_associations(long_term_episodic)
                
                # 4. 创建情感模式分析
                self._analyze_emotional_patterns(long_term_episodic)
                
                # 5. 识别核心记忆并永久保存
                self._identify_core_memories(long_term_episodic)
            
            # 保存关联缓存
            if self.integration_config["storage_enabled"] and self.storage:
                self._save_associations()
                
            logger.success("长期记忆整合完成")
            return True
            
        except Exception as e:
            logger.error_status(f"长期记忆整合失败: {str(e)}")
            return False
    
    def _get_long_term_episodic_memories(self) -> List[Dict[str, Any]]:
        """获取标记为长期记忆的情境记忆"""
        if not self.episodic_memory:
            return []
            
        # 从情境记忆中筛选长期记忆
        long_term_memories = []
        
        for memory_id, memory in self.episodic_memory.memories.items():
            if memory.get("is_long_term", False):
                long_term_memories.append(memory)
                
        return long_term_memories
    
    def _create_semantic_summaries(self, memories: List[Dict[str, Any]]) -> None:
        """为长期情境记忆创建语义摘要，增强知识提取"""
        if not self.semantic_memory:
            return
            
        # 按主题分组记忆
        topic_groups = {}
        
        for memory in memories:
            # 提取主题(使用标签或主题字段)
            topics = memory.get("tags", []) or [memory.get("topic", "未分类")]
            
            # 确保至少有一个主题
            if not topics:
                topics = ["未分类"]
                
            # 将记忆添加到对应主题组
            for topic in topics:
                if topic not in topic_groups:
                    topic_groups[topic] = []
                topic_groups[topic].append(memory)
        
        # 为每个主题创建语义摘要
        for topic, topic_memories in topic_groups.items():
            # 如果主题下记忆数量足够
            if len(topic_memories) >= 3:
                # 提取记忆内容
                contents = [mem.get("content", "") for mem in topic_memories]
                combined_content = "\n- ".join([""] + contents)
                
                # 创建主题摘要
                summary_content = f"关于'{topic}'的长期记忆总结: {combined_content}"
                
                # 准备语义记忆数据
                summary_data = {
                    "content": summary_content,
                    "category": "知识",
                    "subcategory": "记忆摘要",
                    "importance": 0.8,
                    "tags": ["长期记忆", "摘要", topic],
                    "source": "memory_integration",
                    "source_type": "episodic_summary",
                    "source_count": len(topic_memories),
                    "source_ids": [mem["id"] for mem in topic_memories]
                }
                
                # 添加到语义记忆
                summary_id = self.add_semantic_memory(summary_data)
                
                # 为每个情境记忆添加到语义记忆的关联
                for memory in topic_memories:
                    memory_id = memory["id"]
                    
                    # 添加情境到语义的关联
                    if memory_id not in self.memory_associations["episodic_to_semantic"]:
                        self.memory_associations["episodic_to_semantic"][memory_id] = []
                        
                    # 检查是否已存在该关联
                    if not any(assoc["target_id"] == summary_id for assoc in 
                             self.memory_associations["episodic_to_semantic"][memory_id]):
                        # 添加关联
                        self.memory_associations["episodic_to_semantic"][memory_id].append({
                            "target_id": summary_id,
                            "target_type": "semantic",
                            "relation_type": "summarized_in",
                            "strength": 0.9,
                            "created_at": time.time()
                        })
    
    def _enhance_long_term_associations(self, memories: List[Dict[str, Any]]) -> None:
        """增强长期记忆间的关联，构建记忆网络"""
        # 记忆关联阈值
        association_threshold = self.integration_config["association_threshold"]
        
        # 遍历每对长期记忆
        for i in range(len(memories)):
            for j in range(i + 1, len(memories)):
                memory_a = memories[i]
                memory_b = memories[j]
                
                # 计算记忆相似度(简化版)
                similarity = self._calculate_memory_similarity(memory_a, memory_b)
                
                # 如果相似度超过阈值，建立关联
                if similarity >= association_threshold:
                    # 获取记忆ID
                    id_a = memory_a["id"]
                    id_b = memory_b["id"]
                    
                    # 为记忆A添加关联
                    if "associations" not in memory_a:
                        memory_a["associations"] = []
                        
                    if not any(assoc["id"] == id_b for assoc in memory_a["associations"]):
                        memory_a["associations"].append({
                            "id": id_b,
                            "type": "related_memory",
                            "similarity": similarity,
                            "created_at": time.time()
                        })
                    
                    # 为记忆B添加关联
                    if "associations" not in memory_b:
                        memory_b["associations"] = []
                        
                    if not any(assoc["id"] == id_a for assoc in memory_b["associations"]):
                        memory_b["associations"].append({
                            "id": id_a,
                            "type": "related_memory",
                            "similarity": similarity,
                            "created_at": time.time()
                        })
    
    def _calculate_memory_similarity(self, memory_a: Dict, memory_b: Dict) -> float:
        """计算两个记忆间的相似度"""
        # 初始相似度为0
        similarity = 0.0
        
        # 1. 共享标签相似度
        tags_a = set(memory_a.get("tags", []))
        tags_b = set(memory_b.get("tags", []))
        
        if tags_a and tags_b:
            common_tags = tags_a.intersection(tags_b)
            tags_similarity = len(common_tags) / max(len(tags_a), len(tags_b))
            similarity += tags_similarity * 0.3
        
        # 2. 情感相似度
        if "emotion" in memory_a and "emotion" in memory_b:
            emotion_similarity = 1.0 if memory_a["emotion"] == memory_b["emotion"] else 0.0
            similarity += emotion_similarity * 0.2
        
        # 3. 人物相似度
        persons_a = set(memory_a.get("persons", []))
        persons_b = set(memory_b.get("persons", []))
        
        if persons_a and persons_b:
            common_persons = persons_a.intersection(persons_b)
            persons_similarity = len(common_persons) / max(len(persons_a), len(persons_b))
            similarity += persons_similarity * 0.3
        
        # 4. 地点相似度
        location_a = memory_a.get("location", "")
        location_b = memory_b.get("location", "")
        
        if location_a and location_b:
            location_similarity = 1.0 if location_a == location_b else 0.0
            similarity += location_similarity * 0.2
        
        return similarity
    
    def _analyze_emotional_patterns(self, memories: List[Dict[str, Any]]) -> None:
        """分析长期记忆中的情感模式"""
        # 提取带情感标记的记忆
        emotional_memories = [m for m in memories if "emotion" in m]
        
        if len(emotional_memories) < 3:
            return  # 记忆不足，无法分析模式
            
        # 按情感分组
        emotion_groups = {}
        for memory in emotional_memories:
            emotion = memory["emotion"]
            if emotion not in emotion_groups:
                emotion_groups[emotion] = []
            emotion_groups[emotion].append(memory)
        
        # 分析主导情感
        dominant_emotions = sorted(
            [(emotion, len(mems)) for emotion, mems in emotion_groups.items()],
            key=lambda x: x[1],
            reverse=True
        )
        
        # 如果有明显主导情感
        if dominant_emotions and dominant_emotions[0][1] >= 3:
            dominant_emotion, count = dominant_emotions[0]
            
            # 创建情感模式记录
            pattern_data = {
                "content": f"长期记忆情感模式分析: 主导情感为'{dominant_emotion}' ({count}次出现)",
                "type": "analysis",
                "importance": 0.75,
                "is_long_term": True,
                "emotion": dominant_emotion,
                "tags": ["情感模式", "分析", dominant_emotion],
                "related_memories": [m["id"] for m in emotion_groups[dominant_emotion]],
                "pattern_type": "emotional_tendency"
            }
            
            # 添加到情境记忆
            self.episodic_memory.add_memory(pattern_data)
    
    def _identify_core_memories(self, memories: List[Dict[str, Any]]) -> None:
        """识别核心记忆并永久保存"""
        # 核心记忆标准
        core_threshold = 0.9  # 重要性阈值
        
        # 筛选核心记忆候选
        core_candidates = [
            m for m in memories if (
                m.get("importance", 0) >= core_threshold or
                m.get("consolidation_count", 0) >= 3 or  # 多次巩固
                m.get("access_count", 0) >= 5  # 频繁访问
            )
        ]
        
        # 进一步筛选(限制数量)
        if len(core_candidates) > 10:
            # 按重要性排序
            core_candidates = sorted(
                core_candidates,
                key=lambda x: (
                    x.get("importance", 0) * 0.5 + 
                    x.get("consolidation_count", 0) * 0.3 + 
                    x.get("access_count", 0) * 0.2
                ),
                reverse=True
            )[:10]  # 只保留前10个
        
        # 将核心记忆标记为永久记忆
        for memory in core_candidates:
            memory["is_permanent"] = True
            memory["is_core_memory"] = True
            memory["core_identified_at"] = time.time()
            
            # 确保核心记忆不会被遗忘
            memory["strength"] = 1.0
            
            logger.info(f"已识别核心记忆: {memory.get('content', '')[:50]}...")
            
            # 发布核心记忆事件
            self.event_bus.publish("core_memory_identified", {
                "memory_id": memory["id"],
                "content": memory.get("content", ""),
                "importance": memory.get("importance", 0)
            })
            
    def get_long_term_memories(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取长期记忆列表
        
        Args:
            limit: 返回的记忆数量限制
            
        Returns:
            List: 长期记忆列表
        """
        long_term_memories = []
        
        # 获取情境长期记忆
        if self.episodic_memory:
            for memory in self.episodic_memory.memories.values():
                if memory.get("is_long_term", False):
                    # 添加记忆类型标记
                    memory_copy = memory.copy()
                    memory_copy["memory_type"] = "episodic"
                    long_term_memories.append(memory_copy)
        
        # 获取程序长期记忆(高熟练度技能)
        if self.procedural_memory:
            for skill in self.procedural_memory.skills.values():
                if skill.get("mastery", 0) >= 0.8:  # 高熟练度视为长期记忆
                    # 添加记忆类型标记
                    skill_copy = skill.copy()
                    skill_copy["memory_type"] = "procedural"
                    long_term_memories.append(skill_copy)
        
        # 按重要性排序并限制数量
        sorted_memories = sorted(
            long_term_memories,
            key=lambda x: x.get("importance", 0),
            reverse=True
        )
        
        return sorted_memories[:limit]
        
    def get_core_memories(self) -> List[Dict[str, Any]]:
        """
        获取核心记忆列表
        
        Returns:
            List: 核心记忆列表
        """
        core_memories = []
        
        # 获取情境核心记忆
        if self.episodic_memory:
            for memory in self.episodic_memory.memories.values():
                if memory.get("is_core_memory", False):
                    # 添加记忆类型标记
                    memory_copy = memory.copy()
                    memory_copy["memory_type"] = "episodic"
                    core_memories.append(memory_copy)
        
        # 按重要性排序
        sorted_memories = sorted(
            core_memories,
            key=lambda x: x.get("importance", 0),
            reverse=True
        )
        
        return sorted_memories

def get_instance(module_id: str = "default", config: Dict = None) -> MemoryIntegration:
    """
    获取记忆整合模块实例的全局函数
    
    Args:
        module_id: 模块ID
        config: 配置参数
    
    Returns:
        MemoryIntegration: 模块实例
    """
    return MemoryIntegration.get_instance(module_id, config) 