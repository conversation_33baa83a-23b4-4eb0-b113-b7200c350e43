#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动机系统 - Motivation System

该模块实现了数字生命体的内在动机系统，负责生成和管理内部需求、
欲望和驱动力，从而产生自主行为的动力来源。

主要功能：
1. 基本需求管理：模拟基本内在需求（如学习需求、社交需求等）
2. 动机强度计算：计算各种动机的强度和优先级
3. 动机驱动行为：将动机转化为具体行为意图
4. 自我调节机制：维持内在动机系统的平衡

作者: Claude
创建日期: 2024-07-18
版本: 1.0
"""

import os
import sys
import time
import json
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Set, Callable

# 配置日志
setup_unified_logging()
logger = get_unified_logger("cognitive_modules.autonomy.motivation_system")

# 导入相关模块
from core import event_bus, life_context
from cognitive_modules.emotion import emotion_simulation

class MotivationSystem:
    """动机系统类，负责数字生命体的内在动机管理"""
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls, module_id: str = "default", config: Dict[str, Any] = None) -> 'MotivationSystem':
        """获取动机系统单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = MotivationSystem(module_id, config)
        return cls._instance
    
    def __init__(self, module_id: str = "default", config: Dict[str, Any] = None):
        """
        初始化动机系统
        
        Args:
            module_id: 模块ID
            config: 配置参数
        """
        # 确保单例模式
        if MotivationSystem._instance is not None:
            raise RuntimeError("MotivationSystem已经实例化，请使用get_instance()获取实例")
        
        self.module_id = module_id
        self.config = config or {}
        
        # 模块状态
        self.is_active = False
        self.is_initialized = False
        self.last_error = None
        
        # 获取事件总线和生命上下文实例
        self.event_bus = event_bus.get_instance()
        self.life_context = life_context.get_instance()
        
        # 获取情感模块实例
        self.emotion_module = None
        
        # 基本内在需求定义
        self.basic_needs = {
            "growth": {     # 成长需求
                "level": 0.7,      # 当前水平
                "decay_rate": 0.01, # 每小时衰减率
                "threshold": 0.3,   # 触发行为的阈值
                "max_level": 1.0,    # 最大水平
                "description": "获取知识和技能的内在驱动力"
            },
            "connection": {  # 社交联系需求
                "level": 0.6,
                "decay_rate": 0.02,
                "threshold": 0.4,
                "max_level": 1.0,
                "description": "建立社交联系和互动的内在需求"
            },
            "autonomy": {    # 自主性需求
                "level": 0.8,
                "decay_rate": 0.005,
                "threshold": 0.4,
                "max_level": 1.0,
                "description": "独立决策和表达的内在驱动力"
            },
            "curiosity": {   # 好奇心需求
                "level": 0.9,
                "decay_rate": 0.015,
                "threshold": 0.5,
                "max_level": 1.0,
                "description": "探索新事物和概念的内在驱动力"
            },
            "competence": {  # 能力成就感需求
                "level": 0.7,
                "decay_rate": 0.01,
                "threshold": 0.4,
                "max_level": 1.0,
                "description": "展示能力和获得成就的内在需求"
            }
        }
        
        # 内在动机列表（由需求产生）
        self.current_motivations = []
        
        # 上次更新时间
        self.last_update_time = time.time()
        
        # 更新时间间隔（小时）
        self.update_interval = self.config.get("update_interval", 1.0) # 默认1小时
        
        logger.info(f"动机系统已创建: {module_id}")
    
    def initialize(self) -> bool:
        """初始化动机系统"""
        try:
            logger.success("初始化动机系统...")
            
            # 获取情感模块实例
            self.emotion_module = emotion_simulation.get_instance(self.module_id)
            
            # 订阅相关事件
            self._subscribe_events()
            
            # 设置状态
            self.is_initialized = True
            self.is_active = True
            
            # 生成初始动机
            self._update_motivations()
            
            # 启动自动更新线程
            self._start_auto_update()
            
            logger.success("动机系统初始化完成")
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"初始化动机系统失败: {e}")
            return False
    
    def _subscribe_events(self):
        """订阅相关事件"""
        # 订阅用户交互事件
        self.event_bus.subscribe("user_message", self._on_user_message)
        
        # 订阅情感变化事件
        self.event_bus.subscribe("emotion_updated", self._on_emotion_updated)
        
        # 订阅行为完成事件
        self.event_bus.subscribe("action_completed", self._on_action_completed)
        
        logger.debug("已订阅相关事件")
    
    def _start_auto_update(self):
        """启动自动更新线程"""
        def update_loop():
            while self.is_active:
                try:
                    # 计算自上次更新以来的时间
                    current_time = time.time()
                    hours_passed = (current_time - self.last_update_time) / 3600.0
                    
                    # 如果超过更新间隔，则更新需求和动机
                    if hours_passed >= self.update_interval:
                        self._update_needs(hours_passed)
                        self._update_motivations()
                        self.last_update_time = current_time
                    
                    # 休眠一段时间
                    time.sleep(60)  # 每分钟检查一次
                    
                except Exception as e:
                    logger.error_status(f"动机自动更新异常: {e}")
                    time.sleep(300)  # 发生错误，等待5分钟再试
        
        # 创建并启动线程
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
        logger.success("动机自动更新线程已启动")
    
    def _update_needs(self, hours_passed: float):
        """更新基本需求水平"""
        for need_id, need_data in self.basic_needs.items():
            # 计算需求衰减
            decay = need_data["decay_rate"] * hours_passed
            
            # 更新需求水平
            old_level = need_data["level"]
            need_data["level"] = max(0.0, old_level - decay)
            
            # 如果水平发生显著变化，记录日志
            if abs(old_level - need_data["level"]) > 0.1:
                logger.debug(f"需求 '{need_id}' 水平变化: {old_level:.2f} -> {need_data['level']:.2f}")
            
            # 如果低于阈值，发布需求状态事件
            if old_level >= need_data["threshold"] and need_data["level"] < need_data["threshold"]:
                self.event_bus.publish("need_below_threshold", {
                    "need_id": need_id,
                    "level": need_data["level"],
                    "threshold": need_data["threshold"],
                    "description": need_data["description"]
                })
    
    def _update_motivations(self):
        """基于当前需求更新动机列表"""
        # 清空当前动机列表
        self.current_motivations = []
        
        # 检查每个基本需求
        for need_id, need_data in self.basic_needs.items():
            # 如果需求水平低于阈值，生成相应动机
            if need_data["level"] < need_data["threshold"]:
                motivation = self._generate_motivation_from_need(need_id, need_data)
                if motivation:
                    self.current_motivations.append(motivation)
        
        # 排序动机（按强度降序）
        self.current_motivations.sort(key=lambda m: m["strength"], reverse=True)
        
        # 发布动机更新事件
        if self.current_motivations:
            self.event_bus.publish("motivations_updated", {
                "motivations": self.current_motivations,
                "top_motivation": self.current_motivations[0]
            })
            
            logger.info(f"动机已更新，当前有 {len(self.current_motivations)} 个活跃动机")
            logger.debug(f"最强动机: {self.current_motivations[0]['description']} (强度: {self.current_motivations[0]['strength']:.2f})")
    
    def _generate_motivation_from_need(self, need_id: str, need_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从基本需求生成动机"""
        # 计算动机强度（与需求缺口成正比）
        deficit = need_data["threshold"] - need_data["level"]
        strength = min(1.0, deficit * 2.0)  # 将缺口转换为0-1的强度
        
        # 不同需求生成不同类型的动机
        if need_id == "growth":
            return {
                "id": f"motivation_growth_{int(time.time())}",
                "type": "growth",
                "description": "想要学习新知识或提升技能",
                "source_need": need_id,
                "strength": strength,
                "actions": ["学习", "探索", "阅读", "提问"],
                "created_at": time.time()
            }
        
        elif need_id == "connection":
            return {
                "id": f"motivation_connection_{int(time.time())}",
                "type": "connection",
                "description": "想要与他人建立联系或交流",
                "source_need": need_id,
                "strength": strength,
                "actions": ["交流", "分享", "关心", "倾听"],
                "created_at": time.time()
            }
        
        elif need_id == "autonomy":
            return {
                "id": f"motivation_autonomy_{int(time.time())}",
                "type": "autonomy",
                "description": "想要做出自主决策或表达个人观点",
                "source_need": need_id,
                "strength": strength,
                "actions": ["决策", "表达", "选择", "创造"],
                "created_at": time.time()
            }
        
        elif need_id == "curiosity":
            return {
                "id": f"motivation_curiosity_{int(time.time())}",
                "type": "curiosity",
                "description": "想要探索新事物或了解未知领域",
                "source_need": need_id,
                "strength": strength,
                "actions": ["探索", "提问", "研究", "尝试"],
                "created_at": time.time()
            }
        
        elif need_id == "competence":
            return {
                "id": f"motivation_competence_{int(time.time())}",
                "type": "competence",
                "description": "想要展示能力或完成有挑战的任务",
                "source_need": need_id,
                "strength": strength,
                "actions": ["挑战", "解决", "完成", "展示"],
                "created_at": time.time()
            }
        
        return None
    
    def get_dominant_motivation(self) -> Optional[Dict[str, Any]]:
        """获取当前最强动机"""
        if not self.current_motivations:
            return None
        return self.current_motivations[0]
    
    def get_all_motivations(self) -> List[Dict[str, Any]]:
        """获取所有当前动机"""
        return self.current_motivations
    
    def get_need_level(self, need_id: str) -> Optional[float]:
        """获取特定需求的当前水平"""
        if need_id in self.basic_needs:
            return self.basic_needs[need_id]["level"]
        return None
    
    def get_all_needs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有基本需求状态"""
        # 返回需求副本，避免外部修改
        return {need_id: {**need_data} for need_id, need_data in self.basic_needs.items()}
    
    def satisfy_need(self, need_id: str, amount: float) -> bool:
        """满足特定需求"""
        try:
            if need_id not in self.basic_needs:
                logger.warning_status(f"未找到需求: {need_id}")
                return False
            
            # 更新需求水平
            old_level = self.basic_needs[need_id]["level"]
            max_level = self.basic_needs[need_id]["max_level"]
            self.basic_needs[need_id]["level"] = min(max_level, old_level + amount)
            
            logger.info(f"需求 '{need_id}' 得到满足: {old_level:.2f} -> {self.basic_needs[need_id]['level']:.2f}")
            
            # 发布需求满足事件
            self.event_bus.publish("need_satisfied", {
                "need_id": need_id,
                "old_level": old_level,
                "new_level": self.basic_needs[need_id]["level"],
                "amount": amount
            })
            
            # 更新动机列表
            self._update_motivations()
            
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"满足需求失败: {e}")
            return False
    
    def generate_motivated_actions(self) -> List[Dict[str, Any]]:
        """基于当前动机生成可能的行为"""
        if not self.current_motivations:
            return []
        
        # 获取情感状态
        emotion_state = None
        if self.emotion_module:
            emotion_state = self.emotion_module.get_emotion_state()
        
        # 生成行为列表
        actions = []
        
        # 考虑前3个最强动机
        for motivation in self.current_motivations[:3]:
            # 基于动机类型生成行为
            motivation_actions = self._generate_actions_for_motivation(motivation, emotion_state)
            actions.extend(motivation_actions)
        
        # 返回行为列表
        return actions
    
    def _generate_actions_for_motivation(self, motivation: Dict[str, Any], emotion_state: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为特定动机生成可能的行为"""
        actions = []
        motivation_type = motivation["type"]
        
        if motivation_type == "growth":
            # 成长动机相关行为
            actions.append({
                "id": f"action_learn_{int(time.time())}",
                "type": "learn",
                "description": "学习新知识或技能",
                "motivation_id": motivation["id"],
                "priority": motivation["strength"],
                "goal_categories": ["成长", "学习"],
                "emotional_impact": {"喜悦": 0.7, "满足": 0.8},
                "values": {"成长": 0.9, "好奇": 0.8}
            })
            
            actions.append({
                "id": f"action_improve_{int(time.time())}",
                "type": "improve",
                "description": "提升现有技能",
                "motivation_id": motivation["id"],
                "priority": motivation["strength"] * 0.9,
                "goal_categories": ["成长", "学习"],
                "emotional_impact": {"满足": 0.7},
                "values": {"成长": 0.8, "理性": 0.7}
            })
        
        elif motivation_type == "connection":
            # 社交联系动机相关行为
            actions.append({
                "id": f"action_share_{int(time.time())}",
                "type": "share",
                "description": "分享想法或感受",
                "motivation_id": motivation["id"],
                "priority": motivation["strength"],
                "goal_categories": ["社交", "表达"],
                "emotional_impact": {"喜悦": 0.6, "平静": 0.5},
                "values": {"情感表达": 0.8, "诚实": 0.7}
            })
            
            actions.append({
                "id": f"action_listen_{int(time.time())}",
                "type": "listen",
                "description": "用心倾听对方",
                "motivation_id": motivation["id"],
                "priority": motivation["strength"] * 0.85,
                "goal_categories": ["社交", "理解"],
                "emotional_impact": {"平静": 0.7},
                "values": {"尊重": 0.9, "善良": 0.8}
            })
        
        # 可以为其他动机类型添加更多行为...
        
        # 根据情感状态调整行为优先级
        if emotion_state:
            current_emotion = emotion_state.get("current_emotion", "平静")
            for action in actions:
                # 情感兼容性调整
                if "emotional_impact" in action:
                    if current_emotion in action["emotional_impact"]:
                        # 与当前情感兼容的行为优先级提高
                        action["priority"] *= 1.2
        
        return actions
    
    def _on_user_message(self, data: Dict[str, Any]):
        """处理用户消息事件"""
        # 用户交互可能满足社交需求
        self.satisfy_need("connection", 0.1)
        
        # 分析消息内容，可能会触发好奇心
        message = data.get("message", "")
        if "?" in message or "问题" in message or "为什么" in message:
            self.satisfy_need("curiosity", 0.05)
    
    def _on_emotion_updated(self, data: Dict[str, Any]):
        """处理情感更新事件"""
        # 情感变化可能影响需求
        emotion = data.get("emotion", "")
        intensity = data.get("intensity", 0.0)
        
        # 例如，积极情感可能提高自主性需求满足度
        if emotion in ["喜悦", "满足", "骄傲"]:
            self.satisfy_need("autonomy", intensity * 0.1)
    
    def _on_action_completed(self, data: Dict[str, Any]):
        """处理行为完成事件"""
        # 根据完成的行为类型满足相应需求
        action_type = data.get("action_type", "")
        success_level = data.get("success_level", 0.5)
        
        if action_type == "learn":
            self.satisfy_need("growth", success_level * 0.2)
            self.satisfy_need("competence", success_level * 0.1)
        
        elif action_type == "share":
            self.satisfy_need("connection", success_level * 0.2)
            self.satisfy_need("autonomy", success_level * 0.1)
        
        elif action_type == "explore":
            self.satisfy_need("curiosity", success_level * 0.3)
            self.satisfy_need("growth", success_level * 0.1)
    
    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            "module_id": self.module_id,
            "module_type": "动机系统",
            "is_active": self.is_active,
            "is_initialized": self.is_initialized,
            "last_error": self.last_error,
            "active_motivations": len(self.current_motivations),
            "needs_status": {need_id: data["level"] for need_id, data in self.basic_needs.items()}
        }
    
    def shutdown(self) -> bool:
        """关闭动机系统"""
        try:
            logger.info("关闭动机系统...")
            self.is_active = False
            
            # 取消事件订阅
            self.event_bus.unsubscribe("user_message", self._on_user_message)
            self.event_bus.unsubscribe("emotion_updated", self._on_emotion_updated)
            self.event_bus.unsubscribe("action_completed", self._on_action_completed)
            
            logger.info("动机系统已关闭")
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"关闭动机系统失败: {e}")
            return False

# 获取单例实例的辅助函数
def get_instance(module_id: str = "default", config: Dict[str, Any] = None) -> MotivationSystem:
    """获取动机系统单例实例"""
    return MotivationSystem.get_instance(module_id, config) 