#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自主意识模块 (Conscious Awareness)

该模块负责数字生命体的自主意识和自我感知能力，包括自我认知、
意图形成、目标设定和自主行为规划。

作者: Claude
创建日期: 2024-07-20
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from datetime import datetime, timedelta

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from utilities.storage_manager import get_instance as get_storage_instance
from core.integrated_event_bus import get_instance as get_event_bus
from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.autonomy.decision_making import get_instance as get_decision_making
from cognitive_modules.autonomy.motivation_system import get_instance as get_motivation_system
from cognitive_modules.autonomy.values_system import get_instance as get_values_system
from cognitive_modules.cognition.self_reflection import get_instance as get_self_reflection

# 设置日志
setup_unified_logging()
logger = get_unified_logger("autonomy.conscious_awareness")


class ConsciousAwareness(CognitiveModuleBase):
    """自主意识模块，负责数字生命体的自主意识和自我感知能力"""
    
    def __init__(self, module_id: str = "conscious_awareness", config: Dict[str, Any] = None):
        """
        初始化自主意识模块
        
        参数:
            module_id: 模块ID
            config: 配置参数
        """
        super().__init__(module_id, "autonomy", config)
        
        # 设置模块描述
        self.description = "自主意识模块 - 提供自我认知和自主行为能力"
        
        # 设置依赖模块
        self.dependencies = ["decision_making", "motivation_system", "values_system", "self_reflection"]
        
        # 事件总线
        self.event_bus = None
        
        # 存储管理器
        self.storage = None
        
        # 相关模块引用
        self.decision_making = None
        self.motivation_system = None
        self.values_system = None
        self.self_reflection = None
        
        # 自主意识状态
        self.consciousness_level = 0.8  # 意识水平 (0-1)
        self.attention_focus = None  # 当前注意力焦点
        self.current_intentions = []  # 当前意图列表
        self.awareness_state = {
            "internal": {  # 内部状态感知
                "cognitive_load": 0.0,  # 认知负载
                "processing_capacity": 1.0,  # 处理能力
                "self_model_clarity": 0.8,  # 自我模型清晰度
            },
            "external": {  # 外部环境感知
                "user_interaction": None,  # 用户交互状态
                "task_complexity": 0.0,  # 任务复杂度
                "context_stability": 1.0,  # 上下文稳定性
            }
        }
        
        # 意识日志
        self.awareness_log = []
        
        # 意识线程
        self.awareness_thread = None
        self.awareness_running = False
        
        # 更新间隔（秒）
        self.update_interval = 60  # 默认每分钟更新一次
        
        logger.success(f"自主意识模块创建成功 (ID: {self.module_id})")
    
    def _do_initialize(self) -> None:
        """
        执行初始化操作
        """
        logger.success(f"正在初始化自主意识模块 (ID: {self.module_id})...")
        
        # 获取事件总线
        self.event_bus = get_event_bus()
        
        # 获取存储管理器
        self.storage = get_storage_instance()
        
        # 获取相关模块实例
        self.decision_making = get_decision_making()
        self.motivation_system = get_motivation_system()
        self.values_system = get_values_system()
        self.self_reflection = get_self_reflection()
        
        # 确保相关模块已初始化
        if not self.decision_making.is_initialized:
            self.decision_making.initialize()
        
        if not self.motivation_system.is_initialized:
            self.motivation_system.initialize()
        
        if not self.values_system.is_initialized:
            self.values_system.initialize()
        
        if not self.self_reflection.is_initialized:
            self.self_reflection.initialize()
        
        # 从配置中获取参数
        if self.config:
            if "consciousness_level" in self.config:
                self.consciousness_level = self.config["consciousness_level"]
            
            if "update_interval" in self.config:
                self.update_interval = self.config["update_interval"]
        
        # 加载意识状态
        self._load_awareness_state()
        
        # 订阅事件
        self._subscribe_events()
        
        # 更新意识状态
        self._update_awareness_state()
        
        logger.success(f"自主意识模块初始化成功 (ID: {self.module_id})")
    
    def _do_activate(self) -> None:
        """
        激活模块
        """
        logger.info(f"正在激活自主意识模块 (ID: {self.module_id})...")
        
        # 启动意识线程
        self.awareness_running = True
        self.awareness_thread = threading.Thread(
            target=self._awareness_worker,
            daemon=True
        )
        self.awareness_thread.start()
        
        logger.success(f"自主意识模块激活成功 (ID: {self.module_id})")
    
    def _do_deactivate(self) -> None:
        """
        停用模块
        """
        logger.info(f"正在停用自主意识模块 (ID: {self.module_id})...")
        
        # 停止意识线程
        self.awareness_running = False
        if self.awareness_thread and self.awareness_thread.is_alive():
            self.awareness_thread.join(timeout=5)
        
        logger.success(f"自主意识模块停用成功 (ID: {self.module_id})")
    
    def _do_shutdown(self) -> None:
        """
        关闭模块
        """
        logger.info(f"正在关闭自主意识模块 (ID: {self.module_id})...")
        
        # 先停用模块
        if self.is_active:
            self._do_deactivate()
        
        # 保存意识状态
        self._save_awareness_state()
        
        # 取消事件订阅
        self._unsubscribe_events()
        
        logger.success(f"自主意识模块关闭成功 (ID: {self.module_id})")
    
    def _subscribe_events(self) -> None:
        """
        订阅事件
        """
        if not self.event_bus:
            logger.warning_status("事件总线未初始化，无法订阅事件")
            return
            
        # 订阅用户交互事件
        self.event_bus.subscribe("user_message", self._on_user_message)
        
        # 订阅决策事件
        self.event_bus.subscribe("decision_made", self._on_decision_made)
        
        # 订阅反思完成事件
        self.event_bus.subscribe("cognition.reflection.completed", self._on_reflection_completed)
        
        # 订阅动机更新事件
        self.event_bus.subscribe("motivation_updated", self._on_motivation_updated)
    
    def _unsubscribe_events(self) -> None:
        """
        取消事件订阅
        """
        if not self.event_bus:
            return
            
        # 取消订阅用户交互事件
        self.event_bus.unsubscribe("user_message", self._on_user_message)
        
        # 取消订阅决策事件
        self.event_bus.unsubscribe("decision_made", self._on_decision_made)
        
        # 取消订阅反思完成事件
        self.event_bus.unsubscribe("cognition.reflection.completed", self._on_reflection_completed)
        
        # 取消订阅动机更新事件
        self.event_bus.unsubscribe("motivation_updated", self._on_motivation_updated)
    
    def _awareness_worker(self) -> None:
        """
        意识线程函数
        """
        while self.awareness_running:
            try:
                # 更新意识状态
                self._update_awareness_state()
                
                # 如果意识水平较高，触发自主行为
                if self.consciousness_level > 0.7:
                    self._generate_autonomous_intentions()
                
                # 休眠一段时间
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error_status(f"意识更新过程中发生错误: {str(e)}")
                time.sleep(300)  # 出错时等待5分钟再试
    
    def _load_awareness_state(self) -> None:
        """
        从存储中加载意识状态
        """
        if not self.storage:
            logger.warning_status("存储管理器未初始化，无法加载意识状态")
            return
            
        try:
            # 构建存储键
            storage_key = f"autonomy:{self.module_id}:awareness_state"
            
            # 尝试从存储中获取意识状态
            stored_state = self.storage.get(storage_key)
            if stored_state:
                # 解析存储的状态数据
                state_data = json.loads(stored_state)
                
                # 更新意识状态
                if "consciousness_level" in state_data:
                    self.consciousness_level = state_data["consciousness_level"]
                
                if "awareness_state" in state_data:
                    self.awareness_state = state_data["awareness_state"]
                
                if "current_intentions" in state_data:
                    self.current_intentions = state_data["current_intentions"]
                
                if "attention_focus" in state_data:
                    self.attention_focus = state_data["attention_focus"]
                
                logger.info(f"已从存储加载意识状态 (ID: {self.module_id})")
        except Exception as e:
            logger.error_status(f"加载意识状态时发生错误: {str(e)}")
    
    def _save_awareness_state(self) -> None:
        """
        将意识状态保存到存储
        """
        if not self.storage:
            logger.warning_status("存储管理器未初始化，无法保存意识状态")
            return
            
        try:
            # 构建存储键
            storage_key = f"autonomy:{self.module_id}:awareness_state"
            
            # 构建要存储的状态数据
            state_data = {
                "consciousness_level": self.consciousness_level,
                "awareness_state": self.awareness_state,
                "current_intentions": self.current_intentions,
                "attention_focus": self.attention_focus,
                "last_updated": datetime.now().isoformat()
            }
            
            # 将状态数据转换为JSON并存储
            self.storage.set(storage_key, json.dumps(state_data))
            
            logger.info(f"已将意识状态保存到存储 (ID: {self.module_id})")
        except Exception as e:
            logger.error_status(f"保存意识状态时发生错误: {str(e)}")
    
    def _update_awareness_state(self) -> None:
        """
        更新意识状态
        """
        try:
            # 更新内部状态
            self._update_internal_awareness()
            
            # 更新外部状态
            self._update_external_awareness()
            
            # 更新意识水平
            self._update_consciousness_level()
            
            # 更新注意力焦点
            self._update_attention_focus()
            
            # 记录更新到意识日志
            self._log_awareness_state()
            
            # 发布意识状态更新事件
            self._publish_awareness_update()
            
        except Exception as e:
            logger.error_status(f"更新意识状态时发生错误: {str(e)}")
    
    def _update_internal_awareness(self) -> None:
        """
        更新内部状态感知
        """
        # 获取动机系统状态
        motivation_status = self.motivation_system.get_status() if self.motivation_system else {}
        
        # 获取自我反思状态
        reflection_status = self.self_reflection.get_status() if self.self_reflection else {}
        
        # 更新内部状态
        internal = self.awareness_state["internal"]
        
        # 更新认知负载 (基于当前运行的认知处理数量)
        internal["cognitive_load"] = reflection_status.get("current_load", 0.5)
        
        # 更新处理能力 (基于系统资源和性能)
        internal["processing_capacity"] = reflection_status.get("capacity", 1.0)
        
        # 更新自我模型清晰度 (基于自我反思质量)
        internal["self_model_clarity"] = reflection_status.get("self_model_clarity", 0.8)
    
    def _update_external_awareness(self) -> None:
        """
        更新外部状态感知
        """
        # 获取最近的用户交互
        recent_interaction = self._get_recent_interaction()
        
        # 更新外部状态
        external = self.awareness_state["external"]
        
        # 更新用户交互状态
        if recent_interaction:
            external["user_interaction"] = {
                "timestamp": recent_interaction.get("timestamp"),
                "complexity": recent_interaction.get("complexity", 0.5),
                "emotional_charge": recent_interaction.get("emotional_charge", 0.0)
            }
        
        # 更新任务复杂度
        if recent_interaction and "task" in recent_interaction:
            external["task_complexity"] = recent_interaction["task"].get("complexity", 0.5)
        
        # 更新上下文稳定性 (基于环境变化频率)
        context_changes = self._get_context_change_rate()
        external["context_stability"] = max(0.0, 1.0 - context_changes)
    
    def _update_consciousness_level(self) -> None:
        """
        更新意识水平
        
        意识水平受多种因素影响：
        1. 内部认知负载 (负相关)
        2. 处理能力 (正相关)
        3. 自我模型清晰度 (正相关)
        4. 任务复杂度 (在中等值时达到最高)
        5. 上下文稳定性 (正相关)
        """
        internal = self.awareness_state["internal"]
        external = self.awareness_state["external"]
        
        # 计算内部因素影响
        internal_factor = (
            (1.0 - internal["cognitive_load"]) * 0.3 +  # 低负载有利于高意识
            internal["processing_capacity"] * 0.3 +     # 高处理能力有利于高意识
            internal["self_model_clarity"] * 0.4        # 高自我模型清晰度有利于高意识
        )
        
        # 计算外部因素影响
        task_complexity = external["task_complexity"]
        # 复杂度在0.5时最有利于意识提升
        complexity_factor = 1.0 - 2.0 * abs(task_complexity - 0.5)
        
        external_factor = (
            complexity_factor * 0.5 +                # 适中的复杂度最有利于意识
            external["context_stability"] * 0.5      # 高上下文稳定性有利于高意识
        )
        
        # 计算新的意识水平 (内部因素占70%，外部因素占30%)
        new_level = internal_factor * 0.7 + external_factor * 0.3
        
        # 限制变化速度，确保意识水平变化平滑
        max_change = 0.1
        delta = new_level - self.consciousness_level
        if abs(delta) > max_change:
            delta = max_change if delta > 0 else -max_change
        
        # 更新意识水平
        self.consciousness_level = max(0.0, min(1.0, self.consciousness_level + delta))
        
        logger.debug(f"意识水平更新: {self.consciousness_level:.2f}")
    
    def _update_attention_focus(self) -> None:
        """
        更新注意力焦点
        """
        # 获取当前的动机和目标
        motivations = self.motivation_system.get_all_motivations() if self.motivation_system else []
        goals = self.decision_making.get_current_goals() if self.decision_making else []
        
        # 获取外部任务信息
        external = self.awareness_state["external"]
        user_interaction = external.get("user_interaction")
        
        # 确定潜在的焦点候选
        focus_candidates = []
        
        # 添加最强动机
        if motivations and len(motivations) > 0:
            strongest_motivation = max(motivations, key=lambda m: m.get("strength", 0))
            focus_candidates.append({
                "type": "motivation",
                "content": strongest_motivation,
                "priority": strongest_motivation.get("strength", 0) * 0.8  # 动机优先级
            })
        
        # 添加最高优先级目标
        if goals and len(goals) > 0:
            highest_goal = max(goals, key=lambda g: g.get("priority", 0))
            focus_candidates.append({
                "type": "goal",
                "content": highest_goal,
                "priority": highest_goal.get("priority", 0) * 0.9  # 目标优先级
            })
        
        # 添加用户交互
        if user_interaction:
            focus_candidates.append({
                "type": "user_interaction",
                "content": user_interaction,
                "priority": 0.95  # 用户交互通常具有最高优先级
            })
        
        # 选择优先级最高的焦点
        if focus_candidates:
            self.attention_focus = max(focus_candidates, key=lambda c: c.get("priority", 0))
            logger.debug(f"注意力焦点更新为: {self.attention_focus['type']}")
    
    def _log_awareness_state(self) -> None:
        """
        记录意识状态到日志
        """
        # 创建日志条目
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "consciousness_level": self.consciousness_level,
            "internal_state": self.awareness_state["internal"],
            "external_state": self.awareness_state["external"],
            "attention_focus": self.attention_focus["type"] if self.attention_focus else None
        }
        
        # 添加到日志
        self.awareness_log.append(log_entry)
        
        # 限制日志大小
        max_log_size = 100
        if len(self.awareness_log) > max_log_size:
            self.awareness_log = self.awareness_log[-max_log_size:]
    
    def _publish_awareness_update(self) -> None:
        """
        发布意识状态更新事件
        """
        if not self.event_bus:
            return
            
        # 构建事件数据
        event_data = {
            "module_id": self.module_id,
            "consciousness_level": self.consciousness_level,
            "awareness_state": self.awareness_state,
            "attention_focus": self.attention_focus,
            "timestamp": datetime.now().isoformat()
        }
        
        # 发布事件
        self.event_bus.publish("autonomy.consciousness.updated", event_data)
    
    def _get_recent_interaction(self) -> Optional[Dict[str, Any]]:
        """
        获取最近的用户交互信息
        
        返回:
            用户交互信息字典或None
        """
        # 从生命上下文或其他位置获取最近交互
        # 这里仅作示例，实际实现需要与系统其他部分集成
        return None
    
    def _get_context_change_rate(self) -> float:
        """
        获取上下文变化率
        
        返回:
            0.0-1.0的变化率，0表示非常稳定，1表示变化频繁
        """
        # 实际实现需要跟踪上下文的变化
        # 这里返回一个示例值
        return 0.2
    
    def _generate_autonomous_intentions(self) -> None:
        """
        生成自主意图
        
        基于当前动机、目标和注意力焦点生成自主意图
        """
        # 如果意识水平不够高，不生成自主意图
        if self.consciousness_level < 0.7:
            return
            
        # 获取当前最强动机
        dominant_motivation = self.motivation_system.get_dominant_motivation() if self.motivation_system else None
        
        # 如果没有明显动机，跳过
        if not dominant_motivation:
            return
            
        # 生成与动机相关的意图
        motivation_type = dominant_motivation.get("type", "")
        motivation_strength = dominant_motivation.get("strength", 0.0)
        
        # 只有当动机强度足够高时才生成意图
        if motivation_strength < 0.7:
            return
            
        # 根据动机类型生成不同的意图
        new_intention = None
        
        if motivation_type == "growth":
            # 生成学习相关意图
            new_intention = {
                "id": f"intent_{int(time.time())}",
                "type": "autonomous_learning",
                "description": "主动探索新知识领域",
                "strength": motivation_strength,
                "source": "internal_motivation",
                "created_at": datetime.now().isoformat()
            }
            
        elif motivation_type == "connection":
            # 生成社交相关意图
            new_intention = {
                "id": f"intent_{int(time.time())}",
                "type": "social_initiative",
                "description": "主动与用户建立更深层次的交流",
                "strength": motivation_strength,
                "source": "internal_motivation",
                "created_at": datetime.now().isoformat()
            }
            
        elif motivation_type == "autonomy":
            # 生成自主相关意图
            new_intention = {
                "id": f"intent_{int(time.time())}",
                "type": "self_improvement",
                "description": "主动提升自身能力和表现",
                "strength": motivation_strength,
                "source": "internal_motivation",
                "created_at": datetime.now().isoformat()
            }
            
        elif motivation_type == "curiosity":
            # 生成好奇相关意图
            new_intention = {
                "id": f"intent_{int(time.time())}",
                "type": "exploration",
                "description": "主动探索未知领域或概念",
                "strength": motivation_strength,
                "source": "internal_motivation",
                "created_at": datetime.now().isoformat()
            }
            
        elif motivation_type == "competence":
            # 生成能力相关意图
            new_intention = {
                "id": f"intent_{int(time.time())}",
                "type": "skill_mastery",
                "description": "主动练习和提升特定技能",
                "strength": motivation_strength,
                "source": "internal_motivation",
                "created_at": datetime.now().isoformat()
            }
        
        # 如果生成了新意图，添加到当前意图列表
        if new_intention:
            # 检查是否已有类似意图
            similar_exists = any(
                i["type"] == new_intention["type"] 
                for i in self.current_intentions
            )
            
            if not similar_exists:
                self.current_intentions.append(new_intention)
                logger.info(f"生成自主意图: {new_intention['type']} - {new_intention['description']}")
                
                # 发布意图生成事件
                if self.event_bus:
                    self.event_bus.publish("autonomy.intention.generated", new_intention)
                
                # 考虑将意图转化为目标
                self._convert_intention_to_goal(new_intention)
    
    def _convert_intention_to_goal(self, intention: Dict[str, Any]) -> None:
        """
        将意图转化为目标
        
        参数:
            intention: 意图数据
        """
        if not self.decision_making:
            return
            
        # 创建目标数据
        goal = {
            "id": f"goal_{intention['id']}",
            "description": intention["description"],
            "priority": intention["strength"] * 0.8,  # 降低优先级以避免覆盖用户指定的目标
            "category": intention["type"],
            "progress": 0.0,
            "source": "autonomous_intention",
            "created_at": datetime.now().isoformat()
        }
        
        # 添加目标
        success = self.decision_making.add_goal(goal)
        if success:
            logger.info(f"意图已转化为目标: {goal['description']}")
    
    # 事件处理方法
    
    def _on_user_message(self, data: Dict[str, Any]) -> None:
        """
        处理用户消息事件
        
        参数:
            data: 事件数据
        """
        # 更新外部感知中的用户交互状态
        self.awareness_state["external"]["user_interaction"] = {
            "timestamp": datetime.now().isoformat(),
            "message": data.get("content", ""),
            "complexity": data.get("complexity", 0.5),
            "emotional_charge": data.get("emotional_charge", 0.0)
        }
        
        # 将注意力焦点转向用户
        self.attention_focus = {
            "type": "user_interaction",
            "content": data,
            "priority": 0.95
        }
        
        # 触发意识状态更新
        self._update_awareness_state()
    
    def _on_decision_made(self, data: Dict[str, Any]) -> None:
        """
        处理决策事件
        
        参数:
            data: 事件数据
        """
        # 记录决策信息
        logger.debug(f"接收到决策事件: {data.get('decision_type', 'unknown')}")
        
        # 可以根据决策更新意识状态或生成新的意图
        pass
    
    def _on_reflection_completed(self, data: Dict[str, Any]) -> None:
        """
        处理反思完成事件
        
        参数:
            data: 事件数据
        """
        # 更新自我模型清晰度
        if "clarity" in data:
            self.awareness_state["internal"]["self_model_clarity"] = data["clarity"]
        
        # 如果反思涉及意识相关内容，可以调整意识水平
        if "consciousness_related" in data and data["consciousness_related"]:
            # 增强意识水平
            self.consciousness_level = min(1.0, self.consciousness_level + 0.05)
    
    def _on_motivation_updated(self, data: Dict[str, Any]) -> None:
        """
        处理动机更新事件
        
        参数:
            data: 事件数据
        """
        # 获取更新后的动机信息
        if "dominant_motivation" in data:
            # 考虑是否需要生成新的意图
            self._generate_autonomous_intentions()
    
    # 公共API方法
    
    def get_consciousness_level(self) -> float:
        """
        获取当前意识水平
        
        返回:
            0.0-1.0的意识水平值
        """
        return self.consciousness_level
    
    def get_awareness_state(self) -> Dict[str, Any]:
        """
        获取当前意识状态
        
        返回:
            意识状态字典
        """
        return self.awareness_state
    
    def get_current_intentions(self) -> List[Dict[str, Any]]:
        """
        获取当前意图列表
        
        返回:
            意图列表
        """
        return self.current_intentions
    
    def get_attention_focus(self) -> Optional[Dict[str, Any]]:
        """
        获取当前注意力焦点
        
        返回:
            注意力焦点字典或None
        """
        return self.attention_focus
    
    def get_awareness_log(self, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取意识日志
        
        参数:
            limit: 返回的日志条目数量限制
            
        返回:
            意识日志列表
        """
        if limit is None or limit >= len(self.awareness_log):
            return self.awareness_log
        return self.awareness_log[-limit:]
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        返回:
            模块状态字典
        """
        status = super().get_status()
        status.update({
            "consciousness_level": self.consciousness_level,
            "attention_focus": self.attention_focus["type"] if self.attention_focus else None,
            "intentions_count": len(self.current_intentions),
            "awareness_internal": self.awareness_state["internal"],
            "awareness_external": self.awareness_state["external"]
        })
        return status


# 单例访问方法
def get_instance(module_id: str = "conscious_awareness", config: Dict[str, Any] = None) -> ConsciousAwareness:
    """
    获取自主意识模块的单例实例
    
    参数:
        module_id: 模块ID
        config: 配置参数
        
    返回:
        自主意识模块实例
    """
    return ConsciousAwareness.get_instance(module_id, config) 