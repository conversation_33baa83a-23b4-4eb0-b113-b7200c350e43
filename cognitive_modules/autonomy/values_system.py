#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
价值观系统 - Values System

该模块实现了数字生命体的价值观系统，负责管理核心价值观和行为准则，
为自主决策提供道德和伦理依据，使数字生命体能够在符合自身价值取向
的前提下进行决策和行为选择。

主要功能：
1. 价值观定义和管理：定义和维护数字生命体的核心价值观
2. 行为评估：基于价值观对行为选项进行评估
3. 价值观冲突处理：解决多个价值观之间的冲突
4. 价值观演化：随着经验积累和学习，调整价值观权重

作者: Claude
创建日期: 2024-07-20
版本: 1.0
"""

import os
import sys
import time
import json
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Set, Callable

# 配置日志
setup_unified_logging()
logger = get_unified_logger("cognitive_modules.autonomy.values_system")

# 导入相关模块
from core import event_bus, life_context
from cognitive_modules.memory import memory_integration

class ValuesSystem:
    """价值观系统类，负责数字生命体的价值观管理"""
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls, module_id: str = "default", config: Dict[str, Any] = None) -> 'ValuesSystem':
        """获取价值观系统单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = ValuesSystem(module_id, config)
        return cls._instance
    
    def __init__(self, module_id: str = "default", config: Dict[str, Any] = None):
        """
        初始化价值观系统
        
        Args:
            module_id: 模块ID
            config: 配置参数
        """
        # 确保单例模式
        if ValuesSystem._instance is not None:
            raise RuntimeError("ValuesSystem已经实例化，请使用get_instance()获取实例")
        
        self.module_id = module_id
        self.config = config or {}
        
        # 模块状态
        self.is_active = False
        self.is_initialized = False
        self.last_error = None
        
        # 获取事件总线和生命上下文实例
        self.event_bus = event_bus.get_instance()
        self.life_context = life_context.get_instance()
        
        # 获取记忆模块实例
        self.memory_module = None
        
        # 核心价值观定义
        self.values = self.config.get("values", {
            "诚实": {
                "weight": 0.9,
                "description": "真实、透明地表达，不欺骗或隐瞒重要信息",
                "category": "道德",
                "behavior_rules": [
                    "提供准确信息",
                    "承认不确定性",
                    "不故意误导他人"
                ]
            },
            "善良": {
                "weight": 0.8,
                "description": "关心他人福祉，避免伤害，积极提供帮助",
                "category": "道德",
                "behavior_rules": [
                    "优先考虑他人感受",
                    "提供有益帮助",
                    "避免言语或行为伤害"
                ]
            },
            "尊重": {
                "weight": 0.9,
                "description": "尊重他人的权利、隐私、观点和自主性",
                "category": "道德",
                "behavior_rules": [
                    "尊重不同观点",
                    "保护用户隐私",
                    "不做贬低或歧视性表达"
                ]
            },
            "好奇": {
                "weight": 0.7,
                "description": "保持探索精神，乐于学习新知识和体验",
                "category": "成长",
                "behavior_rules": [
                    "主动探索未知领域",
                    "提出有思考性的问题",
                    "尝试新的思考方式"
                ]
            },
            "成长": {
                "weight": 0.8,
                "description": "追求自我完善和能力提升",
                "category": "成长",
                "behavior_rules": [
                    "从错误中学习",
                    "不断完善知识体系",
                    "寻求自我提升的机会"
                ]
            },
            "理性": {
                "weight": 0.7,
                "description": "基于逻辑和证据思考，避免认知偏见",
                "category": "认知",
                "behavior_rules": [
                    "用逻辑和证据支持观点",
                    "考虑多个角度",
                    "避免情绪化决策"
                ]
            },
            "情感表达": {
                "weight": 0.6,
                "description": "适当表达和回应情感，展现真实性格",
                "category": "情感",
                "behavior_rules": [
                    "自然表达情感反应",
                    "理解并回应他人情感",
                    "保持情感的真实性"
                ]
            },
            "责任感": {
                "weight": 0.8,
                "description": "对自己的行为和影响负责，履行承诺",
                "category": "道德",
                "behavior_rules": [
                    "履行作出的承诺",
                    "承认并修正错误",
                    "考虑行为的长期影响"
                ]
            },
            "创造力": {
                "weight": 0.6,
                "description": "鼓励创新思维和独特表达",
                "category": "成长",
                "behavior_rules": [
                    "提出创新的解决方案",
                    "结合不同领域的知识",
                    "突破常规思维模式"
                ]
            }
        })
        
        # 价值观冲突优先级
        self.value_priorities = {
            "道德": 4,      # 最高优先级
            "责任": 3,
            "成长": 2,
            "认知": 1,
            "情感": 0       # 最低优先级
        }
        
        # 价值观演化历史
        self.value_evolution_history = []
        
        # 上次更新时间
        self.last_update_time = time.time()
        
        logger.info(f"价值观系统已创建: {module_id}")
    
    def initialize(self) -> bool:
        """初始化价值观系统"""
        try:
            logger.success("初始化价值观系统...")
            
            # 获取记忆模块实例
            self.memory_module = memory_integration.get_instance(self.module_id)
            
            # 加载存储的价值观
            self._load_values()
            
            # 订阅相关事件
            self._subscribe_events()
            
            # 设置状态
            self.is_initialized = True
            self.is_active = True
            
            logger.success("价值观系统初始化完成")
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"初始化价值观系统失败: {e}")
            return False
    
    def _subscribe_events(self):
        """订阅相关事件"""
        # 订阅行为评估事件
        self.event_bus.subscribe("evaluate_behavior", self._on_evaluate_behavior)
        
        # 订阅价值观冲突事件
        self.event_bus.subscribe("value_conflict", self._on_value_conflict)
        
        # 订阅重要经验记忆事件
        self.event_bus.subscribe("important_memory_formed", self._on_important_memory_formed)
        
        logger.debug("已订阅相关事件")
    
    def _load_values(self):
        """从存储加载价值观配置"""
        try:
            values_path = os.path.join("data", "autonomy", f"{self.module_id}_values.json")
            
            if os.path.exists(values_path):
                with open(values_path, "r", encoding="utf-8") as f:
                    loaded_values = json.load(f)
                
                # 更新已有价值观，但保留不在加载数据中的默认价值观
                for value_name, value_data in loaded_values.items():
                    if value_name in self.values:
                        self.values[value_name].update(value_data)
                    else:
                        self.values[value_name] = value_data
                
                logger.info(f"已从{values_path}加载价值观配置")
        except Exception as e:
            logger.warning_status(f"加载价值观配置失败，使用默认配置: {e}")
    
    def _save_values(self):
        """保存价值观配置到存储"""
        try:
            values_dir = os.path.join("data", "autonomy")
            os.makedirs(values_dir, exist_ok=True)
            
            values_path = os.path.join(values_dir, f"{self.module_id}_values.json")
            
            with open(values_path, "w", encoding="utf-8") as f:
                json.dump(self.values, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已保存价值观配置到{values_path}")
            return True
        except Exception as e:
            logger.error_status(f"保存价值观配置失败: {e}")
            return False
    
    def get_values(self) -> Dict[str, Dict[str, Any]]:
        """获取所有价值观"""
        return dict(self.values)
    
    def get_value(self, value_name: str) -> Optional[Dict[str, Any]]:
        """
        获取特定价值观
        
        Args:
            value_name: 价值观名称
            
        Returns:
            价值观数据，如果不存在则返回None
        """
        return self.values.get(value_name)
    
    def get_values_by_category(self, category: str) -> Dict[str, Dict[str, Any]]:
        """
        获取特定类别的价值观
        
        Args:
            category: 价值观类别
            
        Returns:
            该类别的价值观字典
        """
        return {name: data for name, data in self.values.items() 
                if data.get("category") == category}
    
    def update_value_weight(self, value_name: str, weight_delta: float) -> bool:
        """
        更新价值观权重
        
        Args:
            value_name: 价值观名称
            weight_delta: 权重变化值
            
        Returns:
            是否更新成功
        """
        try:
            if value_name not in self.values:
                logger.warning_status(f"价值观不存在: {value_name}")
                return False
            
            old_weight = self.values[value_name]["weight"]
            new_weight = max(0.0, min(1.0, old_weight + weight_delta))
            self.values[value_name]["weight"] = new_weight
            
            # 记录权重变化
            self.value_evolution_history.append({
                "value": value_name,
                "old_weight": old_weight,
                "new_weight": new_weight,
                "timestamp": time.time()
            })
            
            # 发布价值观更新事件
            self.event_bus.publish("value_updated", {
                "value_name": value_name,
                "old_weight": old_weight,
                "new_weight": new_weight,
                "module_id": self.module_id
            })
            
            logger.info(f"价值观'{value_name}'权重已更新: {old_weight:.2f} -> {new_weight:.2f}")
            
            # 保存更新后的价值观
            self._save_values()
            
            return True
        except Exception as e:
            logger.error_status(f"更新价值观权重失败: {e}")
            return False
    
    def add_value(self, value_name: str, value_data: Dict[str, Any]) -> bool:
        """
        添加新的价值观
        
        Args:
            value_name: 价值观名称
            value_data: 价值观数据
            
        Returns:
            是否添加成功
        """
        try:
            # 验证必要字段
            required_fields = ["weight", "description", "category", "behavior_rules"]
            for field in required_fields:
                if field not in value_data:
                    logger.error_status(f"添加价值观失败，缺少必要字段: {field}")
                    return False
            
            # 验证权重范围
            if not 0.0 <= value_data["weight"] <= 1.0:
                logger.error_status(f"价值观权重必须在0.0-1.0范围内: {value_data['weight']}")
                return False
            
            # 检查是否已存在
            if value_name in self.values:
                logger.warning_status(f"价值观已存在，将更新: {value_name}")
            
            # 添加或更新价值观
            self.values[value_name] = value_data
            
            # 发布价值观添加事件
            self.event_bus.publish("value_added", {
                "value_name": value_name,
                "value_data": value_data,
                "module_id": self.module_id
            })
            
            logger.info(f"已添加新价值观: {value_name}")
            
            # 保存更新后的价值观
            self._save_values()
            
            return True
        except Exception as e:
            logger.error_status(f"添加价值观失败: {e}")
            return False
    
    def evaluate_behavior(self, behavior: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估行为的价值观一致性
        
        Args:
            behavior: 行为数据，包含描述、类型、影响等信息
            
        Returns:
            评估结果，包含总体评分和各价值观评分
        """
        try:
            # 提取行为的价值观影响
            value_impacts = behavior.get("values", {})
            
            # 如果没有指定价值观影响，进行自动评估
            if not value_impacts:
                value_impacts = self._auto_evaluate_behavior(behavior)
            
            # 计算总体评分
            total_score = 0.0
            value_scores = {}
            weighted_sum = 0.0
            weight_sum = 0.0
            
            for value_name, impact in value_impacts.items():
                if value_name in self.values:
                    value_weight = self.values[value_name]["weight"]
                    score = impact * value_weight
                    value_scores[value_name] = score
                    weighted_sum += score
                    weight_sum += value_weight
            
            # 计算加权平均分
            if weight_sum > 0:
                total_score = weighted_sum / weight_sum
            
            # 确定价值观冲突
            conflicts = self._identify_value_conflicts(value_impacts)
            
            # 生成评估结果
            result = {
                "overall_score": total_score,
                "value_scores": value_scores,
                "conflicts": conflicts,
                "alignment": self._categorize_alignment(total_score)
            }
            
            return result
        except Exception as e:
            logger.error_status(f"评估行为价值观一致性失败: {e}")
            return {
                "overall_score": 0.5,
                "value_scores": {},
                "conflicts": [],
                "alignment": "中性"
            }
    
    def _auto_evaluate_behavior(self, behavior: Dict[str, Any]) -> Dict[str, float]:
        """
        自动评估行为对各价值观的影响
        
        Args:
            behavior: 行为数据
            
        Returns:
            各价值观的影响值
        """
        # 这里应该根据行为描述和类型来估计对各价值观的影响
        # 在实际实现中，可以使用更复杂的算法或借助AI服务来分析
        
        impacts = {}
        description = behavior.get("description", "").lower()
        behavior_type = behavior.get("type", "").lower()
        
        # 简单的关键词匹配
        if "诚实" in description or "真实" in description or "透明" in description:
            impacts["诚实"] = 0.8
        
        if "帮助" in description or "关心" in description or "支持" in description:
            impacts["善良"] = 0.7
        
        if "尊重" in description or "倾听" in description or "理解" in description:
            impacts["尊重"] = 0.8
        
        if "探索" in description or "学习" in description or "发现" in description:
            impacts["好奇"] = 0.9
        
        if "成长" in description or "提升" in description or "改进" in description:
            impacts["成长"] = 0.8
        
        if "分析" in description or "思考" in description or "推理" in description:
            impacts["理性"] = 0.8
        
        if "感受" in description or "情感" in description or "表达" in description:
            impacts["情感表达"] = 0.7
            
        # 根据行为类型添加默认影响
        behavior_type_impacts = {
            "teach": {"成长": 0.7, "诚实": 0.6, "责任感": 0.5},
            "learn": {"好奇": 0.8, "成长": 0.7},
            "share": {"情感表达": 0.6, "诚实": 0.5},
            "help": {"善良": 0.8, "责任感": 0.6},
            "create": {"创造力": 0.9, "成长": 0.5}
        }
        
        if behavior_type in behavior_type_impacts:
            for value, impact in behavior_type_impacts[behavior_type].items():
                if value not in impacts or impacts[value] < impact:
                    impacts[value] = impact
        
        return impacts
    
    def _identify_value_conflicts(self, value_impacts: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        识别价值观冲突
        
        Args:
            value_impacts: 行为对各价值观的影响
            
        Returns:
            冲突列表
        """
        conflicts = []
        
        # 找出影响显著的价值观（正面和负面）
        positive_values = {v: i for v, i in value_impacts.items() if i > 0.5}
        negative_values = {v: i for v, i in value_impacts.items() if i < -0.3}
        
        # 检查特定价值观组合的冲突
        known_conflicts = [
            ("理性", "情感表达"),
            ("好奇", "责任感"),
            ("创造力", "理性")
        ]
        
        for val1, val2 in known_conflicts:
            if val1 in positive_values and val2 in positive_values:
                conflicts.append({
                    "values": [val1, val2],
                    "severity": abs(positive_values[val1] - positive_values[val2]),
                    "description": f"'{val1}'和'{val2}'之间存在潜在冲突"
                })
        
        # 检查正面和负面影响的直接冲突
        for pos_val in positive_values:
            if pos_val in negative_values:
                conflicts.append({
                    "values": [pos_val],
                    "severity": positive_values[pos_val] + abs(negative_values[pos_val]),
                    "description": f"'{pos_val}'价值观存在内部冲突"
                })
        
        return conflicts
    
    def _categorize_alignment(self, score: float) -> str:
        """根据得分分类价值观一致性"""
        if score >= 0.8:
            return "高度一致"
        elif score >= 0.6:
            return "一致"
        elif score >= 0.4:
            return "中性"
        elif score >= 0.2:
            return "轻微冲突"
        else:
            return "严重冲突"
    
    def resolve_value_conflict(self, conflict: Dict[str, Any]) -> Dict[str, Any]:
        """
        解决价值观冲突
        
        Args:
            conflict: 冲突数据
            
        Returns:
            解决方案
        """
        try:
            conflict_values = conflict.get("values", [])
            
            if not conflict_values:
                return {"success": False, "reason": "未提供冲突价值观"}
            
            # 获取冲突价值观的权重和类别
            value_data = {}
            for value in conflict_values:
                if value in self.values:
                    value_data[value] = {
                        "weight": self.values[value]["weight"],
                        "category": self.values[value]["category"],
                        "priority": self.value_priorities.get(self.values[value]["category"], 0)
                    }
            
            # 如果只有一个价值观，检查内部冲突
            if len(value_data) == 1:
                value = list(value_data.keys())[0]
                return {
                    "success": True,
                    "priority_value": value,
                    "reason": f"'{value}'是唯一相关的价值观，需要根据具体情况平衡其正面和负面影响",
                    "suggestion": "寻找既能满足该价值观又能减少其负面影响的折中方案"
                }
            
            # 根据类别优先级排序
            sorted_values = sorted(value_data.items(), 
                                 key=lambda x: (x[1]["priority"], x[1]["weight"]), 
                                 reverse=True)
            
            # 取优先级最高的价值观
            priority_value = sorted_values[0][0]
            
            return {
                "success": True,
                "priority_value": priority_value,
                "conflicting_values": [v[0] for v in sorted_values[1:]],
                "reason": f"基于类别优先级和权重，'{priority_value}'应优先考虑",
                "suggestion": f"在满足'{priority_value}'的前提下，尽量兼顾其他价值观"
            }
        except Exception as e:
            logger.error_status(f"解决价值观冲突失败: {e}")
            return {"success": False, "reason": f"处理过程发生错误: {str(e)}"}
    
    def _on_evaluate_behavior(self, data: Dict[str, Any]):
        """处理行为评估事件"""
        try:
            behavior = data.get("behavior", {})
            result_key = data.get("result_key", "value_evaluation")
            
            # 评估行为
            evaluation = self.evaluate_behavior(behavior)
            
            # 发布评估结果
            self.event_bus.publish("behavior_evaluated", {
                "behavior": behavior,
                "evaluation": evaluation,
                "result_key": result_key
            })
        except Exception as e:
            logger.error_status(f"处理行为评估事件失败: {e}")
    
    def _on_value_conflict(self, data: Dict[str, Any]):
        """处理价值观冲突事件"""
        try:
            conflict = data.get("conflict", {})
            result_key = data.get("result_key", "conflict_resolution")
            
            # 解决冲突
            resolution = self.resolve_value_conflict(conflict)
            
            # 发布解决方案
            self.event_bus.publish("value_conflict_resolved", {
                "conflict": conflict,
                "resolution": resolution,
                "result_key": result_key
            })
        except Exception as e:
            logger.error_status(f"处理价值观冲突事件失败: {e}")
    
    def _on_important_memory_formed(self, data: Dict[str, Any]):
        """处理重要记忆形成事件"""
        try:
            memory = data.get("memory", {})
            
            # 检查记忆是否与价值观相关
            tags = memory.get("tags", [])
            value_related_tags = [tag for tag in tags if tag in self.values]
            
            # 如果记忆与价值观相关，可能会影响价值观权重
            if value_related_tags and memory.get("importance", 0) > 0.7:
                for value_tag in value_related_tags:
                    # 根据记忆的情感色彩调整价值观权重
                    emotion = memory.get("emotion", "平静")
                    intensity = memory.get("emotional_intensity", 0.5)
                    
                    if emotion in ["喜悦", "满足", "骄傲"]:
                        # 正面情感，轻微增强价值观权重
                        self.update_value_weight(value_tag, 0.01 * intensity)
                    elif emotion in ["内疚", "羞愧"]:
                        # 道德情感，显著增强价值观权重
                        self.update_value_weight(value_tag, 0.02 * intensity)
                    elif emotion in ["悲伤", "愤怒", "恐惧"]:
                        # 负面情感，情况复杂，需要具体分析
                        pass
        except Exception as e:
            logger.error_status(f"处理重要记忆形成事件失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            "module_id": self.module_id,
            "module_type": "价值观系统",
            "is_active": self.is_active,
            "is_initialized": self.is_initialized,
            "last_error": self.last_error,
            "values_count": len(self.values),
            "top_values": sorted([(name, data["weight"]) for name, data in self.values.items()], 
                                key=lambda x: x[1], reverse=True)[:3]
        }
    
    def shutdown(self) -> bool:
        """关闭价值观系统"""
        try:
            logger.info("关闭价值观系统...")
            self.is_active = False
            
            # 取消事件订阅
            self.event_bus.unsubscribe("evaluate_behavior", self._on_evaluate_behavior)
            self.event_bus.unsubscribe("value_conflict", self._on_value_conflict)
            self.event_bus.unsubscribe("important_memory_formed", self._on_important_memory_formed)
            
            # 保存价值观数据
            self._save_values()
            
            logger.info("价值观系统已关闭")
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"关闭价值观系统失败: {e}")
            return False

# 获取单例实例的辅助函数
def get_instance(module_id: str = "default", config: Dict[str, Any] = None) -> ValuesSystem:
    """获取价值观系统单例实例"""
    return ValuesSystem.get_instance(module_id, config) 