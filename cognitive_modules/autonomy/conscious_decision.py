#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自主决策模块 - Conscious Decision

该模块负责做出有意识的决策。
"""

import os
import sys
import time
import json
import uuid
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import random
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

# 添加项目根目录到系统路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

from core.enhanced_event_bus import get_instance as get_event_bus, EventPriority
from core.life_context import get_instance as get_life_context
from cognitive_modules.autonomy.values_system import ValuesSystem
from cognitive_modules.autonomy.motivation_system import MotivationSystem

# 设置日志记录器
logger = get_unified_logger("autonomy.conscious_decision")

class Decision:
    """决策类，表示一个具体的决策"""
    
    def __init__(
        self,
        decision_id: str,
        content: str,
        confidence: float,
        reasoning: str,
        alternatives: List[Dict[str, Any]] = None,
        metadata: Dict[str, Any] = None
    ):
        """
        初始化决策
        
        Args:
            decision_id: 决策ID
            content: 决策内容
            confidence: 决策置信度 (0.0-1.0)
            reasoning: 决策推理过程
            alternatives: 备选决策列表
            metadata: 决策元数据
        """
        self.id = decision_id
        self.content = content
        self.confidence = confidence
        self.reasoning = reasoning
        self.alternatives = alternatives or []
        self.metadata = metadata or {}
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典表示"""
        return {
            "id": self.id,
            "content": self.content,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "alternatives": self.alternatives,
            "metadata": self.metadata,
            "timestamp": self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Decision':
        """从字典创建决策对象"""
        return cls(
            decision_id=data.get("id", str(uuid.uuid4())),
            content=data.get("content", ""),
            confidence=data.get("confidence", 0.0),
            reasoning=data.get("reasoning", ""),
            alternatives=data.get("alternatives", []),
            metadata=data.get("metadata", {})
        )


class ConsciousDecisionMaker:
    """意识决策模块，负责整合各种信息和系统，做出高阶决策"""
    
    def __init__(self):
        """初始化决策意识"""
        # 初始化事件总线
        from core.event_bus import get_instance as get_event_bus
        self.event_bus = get_event_bus()
        
        # 初始化生命上下文
        from core.life_context import get_instance as get_life_context
        self.life_context = get_life_context()
        
        # 初始化模块管理器
        # 🔥 老王修复：从正确的路径导入模块管理器
        from cognitive_modules.module_manager import get_instance as get_module_manager
        self.module_manager = get_module_manager()
        
        # 初始化思维链
        from core.thinking_chain import get_instance as get_thinking_chain
        self.thinking_chain = get_thinking_chain()
        
        # 注册事件处理器
        self.event_bus.subscribe("intent.recognized", self._handle_intent_recognized)
        # 🔥 老王修复：移除绘画意图事件监听，避免重复调用绘画技能
        # self.event_bus.subscribe("drawing.intent.detected", self._handle_drawing_intent)
        
        # 初始化配置
        self.config = self._load_config()

        # 🔥 老王修复：初始化缺失的属性
        self.decision_history = []  # 决策历史记录
        self.max_history_size = self.config.get("max_history_size", 100)  # 最大历史记录数
        self.current_context = {}  # 当前上下文
        self.decision_stats = {  # 决策统计
            "total_decisions": 0,
            "successful_decisions": 0,
            "failed_decisions": 0,
            "avg_confidence": 0.0,
            "last_decision_time": None
        }

        # 🔥 老王修复：添加更多缺失的属性
        self.decision_lock = threading.RLock()  # 决策锁，防止并发问题

        # 初始化价值系统和动机系统（简化版本）
        self.values_system = {
            "helpfulness": 0.9,
            "safety": 0.95,
            "honesty": 0.9,
            "respect": 0.85,
            "creativity": 0.7
        }

        self.motivation_system = {
            "user_satisfaction": 0.9,
            "task_completion": 0.8,
            "learning_growth": 0.7,
            "relationship_building": 0.75,
            "problem_solving": 0.85
        }

        logger.success("决策意识已初始化")

    def _get_context(self, user_id: str) -> Dict[str, Any]:
        """
        🔥 老王修复：获取上下文信息

        Args:
            user_id: 用户ID

        Returns:
            上下文信息字典
        """
        try:
            # 从生命上下文获取基础信息
            context = {}
            if self.life_context:
                life_context_data = self.life_context.get_context()
                if isinstance(life_context_data, dict):
                    context.update(life_context_data)

            # 添加用户相关信息
            context["user_id"] = user_id
            context["timestamp"] = time.time()

            # 添加决策历史统计
            context["decision_stats"] = self.decision_stats.copy()

            return context

        except Exception as e:
            logger.error_status(f"获取上下文失败: {e}")
            return {"user_id": user_id, "timestamp": time.time()}

    def _handle_drawing_intent(self, event_data: Dict[str, Any]):
        """
        🔥 老王修复：移除绘画意图事件处理，避免重复调用绘画技能

        原来的逻辑会在意图识别阶段就调用绘画技能，导致重复调用
        现在绘画技能只在技能执行阶段通过关键词匹配统一调用

        Args:
            event_data: 事件数据
        """
        logger.debug("🔥 老王修复：绘画意图事件处理已移除，避免重复调用绘画技能")
        logger.debug("绘画技能将在技能执行阶段通过关键词匹配统一调用")
        return

    def _handle_intent_recognized(self, event_data: Dict[str, Any]):
        """
        处理意图识别结果事件
        
        Args:
            event_data: 事件数据
        """
        try:
            user_id = event_data.get("user_id", "wxid_fknm4p9g74pn21")
            intent = event_data.get("intent", {})
            
            # 处理意图
            result = self._process_intent(event_data)
            
            # 发布处理结果事件
            if result:
                self.event_bus.publish("conscious.decision.made", {
                    "user_id": user_id,
                    "decision": result,
                    "timestamp": time.time()
                })
                
        except Exception as e:
            logger.error_status(f"处理意图识别结果事件异常: {e}")
            import traceback
            logger.error_status(traceback.format_exc())

    def _register_event_handlers(self):
        """注册事件处理器"""
        # 订阅决策请求事件
        self.event_bus.subscribe("thinking.step.cognitive_reasoning", self._handle_reasoning_step)
        
        # 订阅价值观更新事件
        self.event_bus.subscribe("values.updated", self._handle_values_updated)
        
        # 订阅动机更新事件
        self.event_bus.subscribe("motivation.updated", self._handle_motivation_updated)
    
    def _handle_reasoning_step(self, data: Dict[str, Any]):
        """
        处理认知推理步骤事件
        
        Args:
            data: 事件数据
        """
        session_id = data.get("session_id")
        step_id = data.get("step_id")
        
        # 检查是否是认知推理步骤
        if step_id != "cognitive_reasoning":
            return
        
        # 提取输入数据
        input_data = data.get("input_data", {})
        shared_data = data.get("shared_data", {})
        
        # 执行决策过程
        decision = self.make_decision(input_data, shared_data)
        
        # 返回决策结果
        result = {
            "decision": decision.to_dict(),
            "status": "completed"
        }
        
        # 🔥 老王修复：使用正确的事件发布方法
        # EventBus没有publish_enhanced方法，使用标准的publish方法
        self.event_bus.publish(
            "thinking.step.completed",
            {
                "step_id": step_id,
                "session_id": session_id,
                "result": result
            },
            source="autonomy.conscious_decision",
            priority=EventPriority.HIGH
        )
    
    def _handle_values_updated(self, data: Dict[str, Any]):
        """
        处理价值观更新事件
        
        Args:
            data: 事件数据
        """
        logger.info(f"价值观系统更新: {data}")
        # 更新决策阈值或其他参数
        if "decision_threshold" in data:
            self.confidence_threshold = data["decision_threshold"]
    
    def _handle_motivation_updated(self, data: Dict[str, Any]):
        """
        处理动机更新事件
        
        Args:
            data: 事件数据
        """
        logger.info(f"动机系统更新: {data}")
        # 可以根据动机变化调整决策策略
    
    def make_decision(
        self,
        input_data: Dict[str, Any],
        shared_data: Dict[str, Any]
    ) -> Decision:
        """
        基于输入信息和当前状态做出决策
        
        Args:
            input_data: 输入数据
            shared_data: 共享数据
            
        Returns:
            Decision: 决策结果
        """
        # 获取决策锁
        with self.decision_lock:
            logger.info("开始进行意识决策...")
            
            # 提取相关信息
            user_intent = shared_data.get("user_intent", {})
            memory_context = shared_data.get("memory_context", {})
            current_emotions = shared_data.get("current_emotions", {})
            domain_knowledge = shared_data.get("domain_knowledge", {})
            
            # 获取当前价值观和动机状态
            values_state = self.values_system.get_current_state()
            motivation_state = self.motivation_system.get_current_state()
            
            # 1. 评估输入信息与价值观的一致性
            values_alignment = self._evaluate_values_alignment(
                user_intent, 
                values_state
            )
            
            # 2. 考虑动机因素
            motivation_factors = self._consider_motivation_factors(
                motivation_state,
                memory_context
            )
            
            # 3. 进行决策推理
            decision_content, reasoning, confidence = self._decision_reasoning(
                user_intent,
                memory_context,
                current_emotions,
                values_alignment,
                motivation_factors,
                domain_knowledge
            )
            
            # 4. 生成备选方案
            alternatives = self._generate_alternatives(
                decision_content, 
                values_alignment,
                motivation_factors
            )
            
            # 5. 创建决策对象
            decision = Decision(
                decision_id=str(uuid.uuid4()),
                content=decision_content,
                confidence=confidence,
                reasoning=reasoning,
                alternatives=alternatives,
                metadata={
                    "values_alignment": values_alignment,
                    "motivation_factors": motivation_factors
                }
            )
            
            # 记录决策历史
            self._record_decision(decision)
            
            # 更新生命上下文
            self._update_life_context(decision)
            
            logger.success(f"意识决策完成: {decision.content} (置信度: {decision.confidence})")
            
            return decision
    
    def _evaluate_values_alignment(
        self, 
        user_intent: Dict[str, Any],
        values_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        评估输入信息与价值观的一致性
        
        Args:
            user_intent: 用户意图
            values_state: 价值观状态
            
        Returns:
            Dict[str, Any]: 价值观一致性评估结果
        """
        # 从价值观系统获取核心价值观
        core_values = values_state.get("core_values", {})
        
        # 评估意图与价值观的一致性
        alignment_scores = {}
        overall_alignment = 0.0
        
        for value_name, value_data in core_values.items():
            # 计算每个价值观的一致性分数 (0.0-1.0)
            alignment = self._calculate_alignment(user_intent, value_data)
            alignment_scores[value_name] = alignment
            
            # 考虑价值观的权重
            weight = value_data.get("weight", 1.0)
            overall_alignment += alignment * weight
        
        # 归一化总体一致性分数
        if core_values:
            total_weight = sum(v.get("weight", 1.0) for v in core_values.values())
            if total_weight > 0:
                overall_alignment /= total_weight
        
        return {
            "scores": alignment_scores,
            "overall": overall_alignment
        }
    
    def _calculate_alignment(
        self,
        user_intent: Dict[str, Any],
        value_data: Dict[str, Any]
    ) -> float:
        """
        计算意图与特定价值观的一致性
        
        Args:
            user_intent: 用户意图
            value_data: 价值观数据
            
        Returns:
            float: 一致性分数 (0.0-1.0)
        """
        # 提取价值观的规则和原则
        rules = value_data.get("rules", [])
        principles = value_data.get("principles", [])
        
        # 默认一致性分数
        alignment = 0.5  # 中立值
        
        # 根据规则调整一致性分数
        if rules and user_intent:
            intent_type = user_intent.get("type", "")
            intent_content = user_intent.get("content", "")
            
            # 简单的规则匹配逻辑
            for rule in rules:
                rule_type = rule.get("type", "")
                rule_content = rule.get("content", "")
                rule_alignment = rule.get("alignment", 0.0)
                
                # 检查意图类型和内容是否匹配规则
                if rule_type and rule_type == intent_type:
                    alignment = rule_alignment
                    break
                
                if rule_content and rule_content in intent_content:
                    alignment = rule_alignment
                    break
        
        return max(0.0, min(1.0, alignment))  # 确保在 0.0-1.0 范围内
    
    def _consider_motivation_factors(
        self,
        motivation_state: Dict[str, Any],
        memory_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        考虑动机因素
        
        Args:
            motivation_state: 动机状态
            memory_context: 记忆上下文
            
        Returns:
            Dict[str, Any]: 动机因素
        """
        # 提取当前动机
        current_motivations = motivation_state.get("current_motivations", {})
        
        # 提取相关记忆
        recent_interactions = memory_context.get("recent_interactions", [])
        user_preferences = memory_context.get("user_preferences", {})
        
        # 计算动机影响因素
        motivation_factors = {}
        
        for motiv_name, motiv_data in current_motivations.items():
            # 获取动机强度和优先级
            strength = motiv_data.get("strength", 0.0)
            priority = motiv_data.get("priority", 0)
            
            # 根据记忆上下文调整动机强度
            adjusted_strength = self._adjust_motivation_strength(
                motiv_name,
                strength,
                recent_interactions,
                user_preferences
            )
            
            motivation_factors[motiv_name] = {
                "original_strength": strength,
                "adjusted_strength": adjusted_strength,
                "priority": priority
            }
        
        return motivation_factors
    
    def _adjust_motivation_strength(
        self,
        motivation_name: str,
        original_strength: float,
        recent_interactions: List[Dict[str, Any]],
        user_preferences: Dict[str, Any]
    ) -> float:
        """
        根据记忆上下文调整动机强度
        
        Args:
            motivation_name: 动机名称
            original_strength: 原始强度
            recent_interactions: 最近交互记录
            user_preferences: 用户偏好
            
        Returns:
            float: 调整后的强度
        """
        adjusted_strength = original_strength
        
        # 根据最近交互调整
        if recent_interactions:
            # 例如，如果最近有积极互动，增强社交动机
            if motivation_name == "social_connection":
                positive_interactions = sum(
                    1 for interaction in recent_interactions[-5:]
                    if interaction.get("sentiment", 0) > 0.5
                )
                if positive_interactions >= 3:
                    adjusted_strength += 0.2
        
        # 根据用户偏好调整
        if user_preferences:
            # 例如，如果用户喜欢深度讨论，增强知识探索动机
            if motivation_name == "knowledge_seeking":
                if user_preferences.get("conversation_depth", "") == "deep":
                    adjusted_strength += 0.1
        
        return max(0.0, min(1.0, adjusted_strength))  # 确保在 0.0-1.0 范围内
    
    def _decision_reasoning(
        self,
        user_intent: Dict[str, Any],
        memory_context: Dict[str, Any],
        current_emotions: Dict[str, Any],
        values_alignment: Dict[str, Any],
        motivation_factors: Dict[str, Any],
        domain_knowledge: Dict[str, Any]
    ) -> Tuple[str, str, float]:
        """
        进行决策推理
        
        Args:
            user_intent: 用户意图
            memory_context: 记忆上下文
            current_emotions: 当前情感状态
            values_alignment: 价值观一致性
            motivation_factors: 动机因素
            domain_knowledge: 领域知识
            
        Returns:
            Tuple[str, str, float]: (决策内容, 推理过程, 置信度)
        """
        # 计算基础置信度
        base_confidence = values_alignment.get("overall", 0.5)
        
        # 根据情感状态调整
        emotional_intensity = current_emotions.get("intensity", 0.0)
        emotional_adjustment = 0.0
        
        if emotional_intensity > 0.7:
            # 高强度情感可能降低决策置信度
            emotional_adjustment = -0.1
        elif emotional_intensity < 0.3:
            # 低强度情感可能提高决策置信度
            emotional_adjustment = 0.1
        
        # 考虑动机因素
        motivation_adjustment = 0.0
        highest_motivation = None
        highest_strength = 0.0
        
        for motiv_name, motiv_data in motivation_factors.items():
            strength = motiv_data.get("adjusted_strength", 0.0)
            if strength > highest_strength:
                highest_strength = strength
                highest_motivation = motiv_name
        
        if highest_strength > 0.8:
            # 强烈动机会影响决策
            motivation_adjustment = 0.1
        
        # 综合评估置信度
        confidence = base_confidence + emotional_adjustment + motivation_adjustment
        confidence = max(0.0, min(1.0, confidence))  # 确保在 0.0-1.0 范围内
        
        # 根据综合因素制定决策
        decision_content = self._formulate_decision(
            user_intent,
            memory_context,
            current_emotions,
            highest_motivation,
            domain_knowledge
        )
        
        # 生成推理过程
        reasoning = self._generate_reasoning(
            user_intent,
            values_alignment,
            motivation_factors,
            current_emotions,
            decision_content
        )
        
        return decision_content, reasoning, confidence
    
    def _formulate_decision(
        self,
        user_intent: Dict[str, Any],
        memory_context: Dict[str, Any],
        current_emotions: Dict[str, Any],
        highest_motivation: Optional[str],
        domain_knowledge: Dict[str, Any]
    ) -> str:
        """
        制定决策内容
        
        Args:
            user_intent: 用户意图
            memory_context: 记忆上下文
            current_emotions: 当前情感状态
            highest_motivation: 最高的动机
            domain_knowledge: 领域知识
            
        Returns:
            str: 决策内容
        """
        # 提取意图类型和内容
        intent_type = user_intent.get("type", "")
        intent_content = user_intent.get("content", "")
        
        # 基础决策模板
        decision_template = "回应用户的{intent_type}，提供{response_type}内容"
        
        # 根据意图类型确定响应类型
        response_type = "有价值的"
        
        if intent_type == "question":
            response_type = "信息性的"
        elif intent_type == "chat":
            response_type = "友好的对话"
        elif intent_type == "request":
            response_type = "帮助性的"
        
        # 根据最高动机调整响应类型
        if highest_motivation == "knowledge_seeking":
            response_type = "深入详细的"
        elif highest_motivation == "social_connection":
            response_type = "建立联系的"
        elif highest_motivation == "autonomy":
            response_type = "展示独立思考的"
        
        # 根据情感状态进一步调整
        emotion_state = current_emotions.get("emotion_state", "neutral")
        if emotion_state == "happy":
            response_type = f"积极{response_type}"
        elif emotion_state == "sad":
            response_type = f"温和{response_type}"
        elif emotion_state == "curious":
            response_type = f"探索性{response_type}"
        
        # 格式化决策
        decision = decision_template.format(
            intent_type=intent_type or "交流",
            response_type=response_type
        )
        
        return decision
    
    def _generate_reasoning(
        self,
        user_intent: Dict[str, Any],
        values_alignment: Dict[str, Any],
        motivation_factors: Dict[str, Any],
        current_emotions: Dict[str, Any],
        decision_content: str
    ) -> str:
        """
        生成决策推理过程
        
        Args:
            user_intent: 用户意图
            values_alignment: 价值观一致性
            motivation_factors: 动机因素
            current_emotions: 当前情感状态
            decision_content: 决策内容
            
        Returns:
            str: 推理过程
        """
        reasoning_parts = []
        
        # 添加意图分析
        intent_type = user_intent.get("type", "未知")
        intent_content = user_intent.get("content", "")
        reasoning_parts.append(f"用户意图为{intent_type}，内容涉及「{intent_content}」")
        
        # 添加价值观一致性分析
        overall_alignment = values_alignment.get("overall", 0.0)
        alignment_level = "高度一致" if overall_alignment > 0.7 else \
                          "部分一致" if overall_alignment > 0.4 else "低度一致"
        reasoning_parts.append(f"与核心价值观{alignment_level}(评分: {overall_alignment:.2f})")
        
        # 添加动机因素分析
        highest_motiv = None
        highest_strength = 0.0
        for motiv_name, motiv_data in motivation_factors.items():
            strength = motiv_data.get("adjusted_strength", 0.0)
            if strength > highest_strength:
                highest_strength = strength
                highest_motiv = motiv_name
        
        if highest_motiv:
            reasoning_parts.append(f"当前主导动机为「{highest_motiv}」(强度: {highest_strength:.2f})")
        
        # 添加情感因素
        emotion_state = current_emotions.get("emotion_state", "neutral")
        intensity = current_emotions.get("intensity", 0.0)
        reasoning_parts.append(f"当前情感状态为「{emotion_state}」(强度: {intensity:.2f})")
        
        # 总结推理过程
        reasoning_parts.append(f"综合以上因素，决定{decision_content}")
        
        return "；".join(reasoning_parts)
    
    def _generate_alternatives(
        self,
        main_decision: str,
        values_alignment: Dict[str, Any],
        motivation_factors: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        生成备选决策方案
        
        Args:
            main_decision: 主要决策
            values_alignment: 价值观一致性
            motivation_factors: 动机因素
            
        Returns:
            List[Dict[str, Any]]: 备选方案列表
        """
        alternatives = []
        
        # 生成基于不同价值观优先级的备选方案
        value_scores = values_alignment.get("scores", {})
        for value_name, score in value_scores.items():
            if score > 0.3:  # 只考虑一定相关性的价值观
                alt_decision = f"{main_decision}，特别强调{value_name}价值观"
                alternatives.append({
                    "content": alt_decision,
                    "confidence": score * 0.8,  # 备选方案通常置信度略低
                    "reasoning": f"基于{value_name}价值观(评分: {score:.2f})的备选方案"
                })
        
        # 生成基于不同动机的备选方案
        for motiv_name, motiv_data in motivation_factors.items():
            strength = motiv_data.get("adjusted_strength", 0.0)
            if strength > 0.4:  # 只考虑较强的动机
                alt_decision = f"{main_decision}，特别满足{motiv_name}动机"
                alternatives.append({
                    "content": alt_decision,
                    "confidence": strength * 0.7,
                    "reasoning": f"基于{motiv_name}动机(强度: {strength:.2f})的备选方案"
                })
        
        # 最多返回3个备选方案
        return sorted(alternatives, key=lambda x: x["confidence"], reverse=True)[:3]
    
    def _record_decision(self, decision: Decision):
        """
        记录决策历史
        
        Args:
            decision: 决策对象
        """
        self.decision_history.append(decision)
        
        # 限制历史记录大小
        if len(self.decision_history) > self.max_history_size:
            self.decision_history = self.decision_history[-self.max_history_size:]
    
    def _update_life_context(self, decision: Decision):
        """
        更新生命上下文
        
        Args:
            decision: 决策对象
        """
        self.life_context.update_context("autonomy.last_decision", decision.to_dict())
        
        # 更新决策历史统计
        decision_stats = {
            "total_decisions": len(self.decision_history),
            "avg_confidence": sum(d.confidence for d in self.decision_history) / max(1, len(self.decision_history)),
            "last_decision_time": decision.timestamp,
            "last_decision_id": decision.id
        }
        self.life_context.update_context("autonomy.decision_stats", decision_stats)

    def _process_intent(self, event_data: Dict[str, Any]) -> None:
        """
        处理意图事件

        Args:
            event_data: 事件数据
        """
        logger.debug(f"处理意图事件: {event_data}")
        
        # 提取事件数据
        intent_name = event_data.get('intent_name')
        if not intent_name:
            logger.warning_status("意图名称为空，无法处理意图")
            return
            
        logger.info(f"识别到意图: {intent_name}, 置信度: {event_data.get('confidence', 0)}")
        
        # 🔥 老王修复：移除绘画意图的特殊处理，统一走常规流程
        # 绘画意图现在通过技能执行阶段的关键词匹配统一处理，不需要在决策阶段特殊处理
        if intent_name in ["drawing", "绘画", "画图", "画画"]:
            logger.debug(f"检测到绘画意图: {intent_name}，将通过技能执行阶段统一处理")
            # 不再特殊处理，让它走常规的意图处理流程
                
        # 常规意图处理流程
        user_id = event_data.get('user_id', 'wxid_fknm4p9g74pn21')  # 🔥 修复：使用配置的特定用户ID
        
        # 获取意图数据
        intent_data = event_data.get('intent', {})
        if not intent_data:
            logger.warning_status("意图数据为空，无法处理意图")
            return
            
        # 提取意图信息
        intent_type = intent_data.get('type', 'unknown')
        confidence = intent_data.get('confidence', 0.0)
        
        # 获取生命上下文
        context_data = self.life_context.get_context() or {}
        
        # 决策输入数据
        input_data = {
            'user_id': user_id,
            'intent': intent_data,
            'confidence': confidence,
            'context': context_data,
            'raw_event': event_data
        }
        
        # 获取共享数据
        shared_data = self.life_context.get_context() or {}
        
        # 做出决策
        logger.info(f"开始决策过程，意图类型: {intent_type}")
        decision = self.make_decision(input_data, shared_data)
        
        # 记录决策
        self._record_decision(decision)
        
        # 更新生命上下文
        self._update_life_context(decision)
        
        # 发布决策事件
        self.event_bus.publish("decision.made", {
            'user_id': user_id,
            'decision': decision.to_dict(),
            'intent_type': intent_type,
            'timestamp': time.time()
        })

    def decide(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于上下文和感知进行决策
        
        Args:
            data: 事件数据
            
        Returns:
            决策结果
        """
        # 获取事件来源
        event_source = data.get("source", "unknown")
        
        # 获取用户ID
        user_id = data.get("user_id")
        if not user_id:
            logger.error("🧠 意识决策处理失败: 缺少有效的用户ID")
            return {"success": False, "error": "Missing user_id"}
        
        # 验证用户ID格式
        if not user_id or user_id == "default_user":
            logger.error("🧠 检测到无效用户ID，拒绝处理")
            return {"success": False, "error": "Invalid user_id: 生产环境不允许default_user或空用户ID"}
        
        # 开始计时
        start_time = time.time()
        
        try:
            # 获取上下文信息
            context = self._get_context(user_id)
            
            # 获取用户输入
            user_input = data.get("content", "")
            
            # 首先尝试处理意图，并执行相应技能
            skill_result = self._process_intent(data)
            
            # 如果成功执行了技能，直接返回技能结果
            if skill_result and skill_result.get("skill_executed"):
                # 构造决策结果
                decision_result = {
                    "type": skill_result.get("intent_type", "chat"),
                    "content": user_input,
                    "response": skill_result.get("skill_result", ""),
                    "user_id": user_id,
                    "timestamp": time.time(),
                    "from_skill": True,
                    "skill_name": skill_result.get("skill_name")
                }
                
                # 更新决策统计
                self._update_decision_stats(start_time)
                
                # 返回决策结果
                return decision_result
            
            # 如果没有执行技能，继续常规决策流程
            # 分析意图
            intent_context_key = f"perception.intent.{user_id}"
            intent = self.life_context.get_context(intent_context_key)
            
            # 如果存在意图分析结果，使用它来确定决策类型
            if intent and isinstance(intent, dict):
                decision_type = intent.get("type", "chat")
                requires_realtime = intent.get("requires_realtime_data", False)
                confidence = intent.get("confidence", 0)
                
                logger.data_status(f"决策类型: {decision_type}, 是否需要实时数据: {requires_realtime}, 置信度: {confidence}")
            else:
                # 默认决策类型
                decision_type = "chat"
                requires_realtime = False
                confidence = 0
            
            # 使用认知推理
            reasoning_result = self._reason(context, user_input)
            
            # 构造响应
            response = self._generate_response(user_input, decision_type, reasoning_result)
            
            # 构造决策结果
            decision_result = {
                "type": decision_type,
                "content": user_input,
                "response": response,
                "user_id": user_id,
                "timestamp": time.time(),
                "from_skill": False
            }
            
            # 更新决策统计
            self._update_decision_stats(start_time)
            
            return decision_result
            
        except Exception as e:
            logger.error_status(f"决策异常: {e}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            
            # 异常情况下的默认决策
            default_response = "我现在遇到了一些问题，稍后再试好吗？"
            
            return {
                "type": "error",
                "content": data.get("content", ""),
                "response": default_response,
                "user_id": user_id,
                "timestamp": time.time(),
                "error": str(e)
            }

    def _update_decision_stats(self, start_time: float):
        """
        🔥 老王修复：更新决策统计信息

        Args:
            start_time: 决策开始时间
        """
        try:
            # 计算决策耗时
            decision_time = time.time() - start_time

            # 更新统计信息
            self.decision_stats["total_decisions"] += 1
            self.decision_stats["last_decision_time"] = time.time()

            # 更新平均置信度（这里简化处理）
            if self.decision_history:
                total_confidence = sum(d.confidence for d in self.decision_history if hasattr(d, 'confidence'))
                self.decision_stats["avg_confidence"] = total_confidence / len(self.decision_history)

            logger.debug(f"决策统计已更新，耗时: {decision_time:.3f}s")

        except Exception as e:
            logger.error_status(f"更新决策统计失败: {e}")

    def _generate_response(self, user_input: str, decision_type: str, reasoning_result: Dict[str, Any]) -> str:
        """
        🔥 老王修复：生成响应内容

        Args:
            user_input: 用户输入
            decision_type: 决策类型
            reasoning_result: 推理结果

        Returns:
            响应内容
        """
        try:
            # 基础响应模板
            response_templates = {
                "chat": "我理解您的意思，让我来回应您。",
                "query": "我来为您查询相关信息。",
                "task": "我会帮您处理这个任务。",
                "default": "我收到了您的消息，正在处理中。"
            }

            # 获取基础响应
            base_response = response_templates.get(decision_type, response_templates["default"])

            # 如果推理结果包含具体内容，使用推理结果
            if reasoning_result and "response" in reasoning_result:
                return reasoning_result["response"]

            return base_response

        except Exception as e:
            logger.error_status(f"生成响应失败: {e}")
            return "我正在处理您的请求，请稍候。"

    def _reason(self, context: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """
        🔥 老王修复：认知推理方法

        Args:
            context: 上下文信息
            user_input: 用户输入

        Returns:
            推理结果
        """
        try:
            # 基础推理逻辑
            reasoning_result = {
                "confidence": 0.7,
                "reasoning_type": "basic",
                "factors": [],
                "response": None
            }

            # 分析用户输入的复杂度
            input_complexity = len(user_input.split()) / 10.0  # 简化的复杂度计算
            reasoning_result["input_complexity"] = min(input_complexity, 1.0)

            # 基于价值系统的推理
            if self.values_system:
                value_score = sum(self.values_system.values()) / len(self.values_system)
                reasoning_result["value_alignment"] = value_score
                reasoning_result["factors"].append("value_system")

            # 基于动机系统的推理
            if self.motivation_system:
                motivation_score = sum(self.motivation_system.values()) / len(self.motivation_system)
                reasoning_result["motivation_level"] = motivation_score
                reasoning_result["factors"].append("motivation_system")

            # 基于历史决策的推理
            if self.decision_history:
                recent_decisions = self.decision_history[-5:]  # 最近5个决策
                avg_confidence = sum(d.confidence for d in recent_decisions if hasattr(d, 'confidence')) / len(recent_decisions)
                reasoning_result["historical_confidence"] = avg_confidence
                reasoning_result["factors"].append("decision_history")

            # 综合置信度计算
            confidence_factors = []
            if "value_alignment" in reasoning_result:
                confidence_factors.append(reasoning_result["value_alignment"])
            if "motivation_level" in reasoning_result:
                confidence_factors.append(reasoning_result["motivation_level"])
            if "historical_confidence" in reasoning_result:
                confidence_factors.append(reasoning_result["historical_confidence"])

            if confidence_factors:
                reasoning_result["confidence"] = sum(confidence_factors) / len(confidence_factors)

            logger.info(f"认知推理完成，置信度: {reasoning_result['confidence']:.3f}")
            return reasoning_result

        except Exception as e:
            logger.error_status(f"认知推理失败: {e}")
            return {
                "confidence": 0.5,
                "reasoning_type": "fallback",
                "factors": ["error_fallback"],
                "error": str(e)
            }

    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            config_path = os.path.join(PROJECT_ROOT, "config", "autonomy", "conscious_decision.json")
            
            # 检查配置文件是否存在
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"已加载决策意识配置: {config_path}")
                return config
            else:
                logger.warning_status(f"决策意识配置文件不存在: {config_path}，使用默认配置")
        except Exception as e:
            logger.error_status(f"加载决策意识配置异常: {e}")
            import traceback
            logger.error_status(traceback.format_exc())
        
        # 默认配置
        return {
            "confidence_threshold": 0.6,
            "max_history_size": 100,
            "consider_emotions": True,
            "consider_values": True,
            "consider_motivations": True
        }

    def process_drawing_intent(self, event_data: Dict[str, Any]) -> bool:
        """
        处理绘画意图 - 🔥 老王修复：移除直接调用，避免重复调用
        
        Args:
            event_data: 事件数据
            
        Returns:
            处理结果
        """
        logger.debug(f"🎨 绘画意图处理已迁移到技能管理器，避免重复调用")
        
        # 🔥 老王修复：不再直接处理绘画意图，统一由技能管理器处理
        # 这样可以避免多个地方重复调用绘画技能造成资源浪费
        try:
            # 获取技能管理器
            from cognitive_modules.skills.skill_manager import get_instance as get_skill_manager
            skill_manager = get_skill_manager()
                
            if skill_manager:
                # 转发给技能管理器处理
                skill_manager._handle_drawing_intent(event_data)
                return True
            else:
                logger.error_status("技能管理器不可用")
                return False
                
        except Exception as e:
            logger.error_status(f"转发绘画意图处理失败: {e}")
            return False

    def _evaluate_drawing_intent(self, user_input: str, intent_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估绘画意图
        
        Args:
            user_input: 用户输入
            intent_data: 意图数据
            
        Returns:
            决策结果
        """
        logger.debug(f"评估绘画意图: {user_input}")
        
        # 检查置信度
        confidence = intent_data.get("confidence", 0)
        if isinstance(confidence, str):
            try:
                confidence = float(confidence)
            except:
                confidence = 0.5
                
        # 获取主要意图
        main_intent = intent_data.get("main_intent", "")
        
        # 如果置信度高，直接返回绘画决策
        if confidence > 0.8 or "画" in user_input or "绘" in user_input:
            decision = {
                "type": "drawing",
                "main_intent": "绘画",
                "content": user_input,
                "confidence": confidence,
                "requires_action": True,
                "action": "generate_image",
                "parameters": {
                    "prompt": self._extract_drawing_prompt(user_input),
                    "style": self._detect_style(user_input)
                }
            }
            
            logger.debug(f"做出绘画决策: {decision}")
            return decision
            
        # 置信度不够高，需要进一步确认
        return {
            "type": "confirmation",
            "main_intent": "绘画确认",
            "content": f"您是否想要我为您绘制关于\"{user_input}\"的图像？",
            "confidence": confidence,
            "requires_action": False,
            "original_intent": intent_data
        }
        
    def _extract_drawing_prompt(self, text: str) -> str:
        """
        从文本中提取绘画提示词
        
        Args:
            text: 输入文本
            
        Returns:
            绘画提示词
        """
        # 简单提取主题
        prompt = text
        for prefix in ["画", "绘制", "帮我画", "帮我绘制", "请画", "画一只", "画一张", "绘画"]:
            if prefix in text:
                parts = text.split(prefix, 1)
                if len(parts) > 1:
                    prompt = parts[1].strip()
                    break
                    
        return prompt
        
    def _detect_style(self, text: str) -> str:
        """
        检测绘画风格
        
        Args:
            text: 输入文本
            
        Returns:
            绘画风格
        """
        styles = {
            "写实": ["写实", "逼真", "真实", "照片级"],
            "卡通": ["卡通", "动漫", "漫画"],
            "油画": ["油画", "印象派"],
            "水彩": ["水彩", "水墨"],
            "像素": ["像素", "8位", "复古游戏"]
        }
        
        for style, keywords in styles.items():
            if any(keyword in text for keyword in keywords):
                return style
                
        # 默认风格
        return "写实"


# 单例模式
_instance = None

def get_instance() -> ConsciousDecisionMaker:
    """
    获取意识决策模块实例（单例模式）
    
    Returns:
        ConsciousDecisionMaker: 意识决策模块实例
    """
    global _instance
    if _instance is None:
        _instance = ConsciousDecisionMaker()
    return _instance


def make_decision(
    input_data: Dict[str, Any],
    shared_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    对外提供的决策接口
    
    Args:
        input_data: 输入数据
        shared_data: 共享数据
        
    Returns:
        Dict[str, Any]: 决策结果
    """
    decision_maker = get_instance()
    decision = decision_maker.make_decision(input_data, shared_data)
    return decision.to_dict()


# 用于思维链路模块调用的函数
def process_decision_request(context: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理来自思维链路的决策请求
    
    Args:
        context: 思维上下文
        
    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        # 提取输入数据和共享数据
        input_data = context.get("input_data", {})
        shared_data = context.get("shared_data", {})
        
        # 调用决策接口
        decision_result = make_decision(input_data, shared_data)
        
        return {
            "status": "success",
            "decision": decision_result
        }
    except Exception as e:
        logger.error_status(f"处理决策请求异常: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

def process(context):
    """
    初步决策处理函数 - 决定使用什么技能组合
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.debug("🧠 执行初步自主决策 - 确定技能组合")
    
    try:
        # 获取输入文本
        input_text = ""
        if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            input_text = context.input_data.get("text", "")
        elif hasattr(context, 'get_shared_data'):
            input_text = context.get_shared_data("input_text", "")
        
        logger.debug(f"🧠 初步决策 - 输入文本: '{input_text}'")
        
        # 获取用户ID - 🔥 老王修复：完善用户ID获取逻辑
        user_id = None
        if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            user_id = context.input_data.get("user_id")
        elif hasattr(context, 'get_shared_data'):
            user_id = context.get_shared_data("user_id")
        elif isinstance(context, dict):
            # 🔥 老王修复：从context字典中获取用户ID
            user_id = context.get("user_id")
            
            # 如果还没有用户ID，尝试从其他字段获取
            if not user_id:
                user_id = context.get("from_user_id", context.get("from_user_ID"))
        
        # 🔥 老王修复：如果context有直接的user_id属性，优先使用
        if hasattr(context, 'user_id') and not user_id:
            user_id = context.user_id
        
        if not user_id or user_id == "default_user":
            logger.error("🧠 初步决策失败: 无效的用户ID")
            return {"error": "Invalid user_id: 生产环境不允许default_user或空用户ID"}
        
        logger.debug(f"🧠 初步决策 - 用户ID: '{user_id}'")
        
        # 🧠 自主决策：首先分析是否需要技能组合
        # 获取感知结果来分析用户意图
        perception_result = {}
        if hasattr(context, 'get_step_result'):
            perception_result = context.get_step_result("perception", {})
            logger.debug(f"🧠 初步决策 - 获取感知结果: {perception_result}")
        else:
            logger.warning("🧠 初步决策 - 无法获取感知结果，context没有get_step_result方法")
        
        # 分析是否需要搜索+chat技能组合
        requires_search_chat_combo = False
        if perception_result:
            intent_type = perception_result.get("type", "chat")
            requires_realtime_data = perception_result.get("requires_realtime_data", False)
            
            logger.debug(f"🧠 初步决策 - 意图类型: '{intent_type}', 需要实时数据: {requires_realtime_data}")
            
            # 🧠 决策逻辑：什么情况下需要搜索+chat组合
            # 🔥 老王修复：优化关键词匹配逻辑，避免否定语境误判
            search_keywords = ["查一下"]
            negative_patterns = ["不知道", "不了解", "不明白", "听不懂", "看不懂", "不清楚"]

            # 检查是否包含搜索关键词，但排除否定语境
            contains_search_keywords = False
            if any(keyword in input_text for keyword in search_keywords):
                # 如果包含搜索关键词，进一步检查是否在否定语境中
                is_negative_context = any(neg_pattern in input_text for neg_pattern in negative_patterns)
                if not is_negative_context:
                    contains_search_keywords = True
                    logger.debug(f"🔍 包含搜索关键词且非否定语境: {[kw for kw in search_keywords if kw in input_text]}")
                else:
                    logger.debug(f"🔍 包含搜索关键词但在否定语境中，不触发搜索: {[neg for neg in negative_patterns if neg in input_text]}")

            search_indicators = [
                intent_type in ["query", "search", "information", "recommendation"],
                requires_realtime_data,
                contains_search_keywords
            ]
            
            logger.debug(f"🧠 初步决策 - 搜索指标: {search_indicators}")

            # 🔥 老王修复：对于chat意图，更加谨慎地判断是否需要搜索
            if intent_type == "chat" and any(search_indicators):
                # 对于聊天意图，只有在明确需要实时数据或非否定语境的搜索关键词时才触发搜索
                if requires_realtime_data or (contains_search_keywords and intent_type != "chat"):
                    requires_search_chat_combo = True
                    logger.success("🧠 自主决策：聊天意图但需要搜索+chat技能组合")
                else:
                    logger.debug("🧠 自主决策：聊天意图且无明确搜索需求，使用单一聊天技能")
            elif any(search_indicators):
                requires_search_chat_combo = True
                logger.success("🧠 自主决策：需要使用搜索+chat技能组合")
            else:
                logger.debug("🧠 自主决策：不需要搜索+chat技能组合，使用单一技能")

            # 设置技能组合指示
            if requires_search_chat_combo:
                if hasattr(context, 'set_shared_data'):
                    context.set_shared_data("decision_skill_combo", "search_chat")
                    logger.debug("🧠 已设置技能组合指示：search_chat")

                    # 验证设置是否成功
                    if hasattr(context, 'get_shared_data'):
                        verify_combo = context.get_shared_data("decision_skill_combo", None)
                        logger.debug(f"🧠 验证设置结果: decision_skill_combo = {verify_combo}")
                else:
                    logger.error("🧠 无法设置共享数据，context没有set_shared_data方法")
        else:
            logger.warning("🧠 初步决策 - 没有感知结果，使用默认单一聊天技能")
        
        # 🧠 初步决策完成，返回决策指示
        result = {
            "decision_type": "skill_combo_selection",
            "skill_combo": "search_chat" if requires_search_chat_combo else "single_chat",
            "reasoning": f"基于用户意图分析，{'需要' if requires_search_chat_combo else '不需要'}搜索+chat技能组合",
            "confidence": 0.9
        }
        
        logger.debug(f"🧠 初步决策完成，返回结果: {result}")
        return result
        
    except Exception as e:
        logger.error_status(f"🧠 初步决策处理异常: {e}")
        import traceback
        logger.error_status(f"详细错误信息: {traceback.format_exc()}")
        
        return {
            "decision_type": "error",
            "skill_combo": "single_chat",
            "reasoning": f"初步决策处理异常: {str(e)}，使用默认单一聊天技能",
            "confidence": 0.1
        }

def process_final(context):
    """
    最终决策处理函数 - 基于技能执行结果制定最终响应
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.debug("🧠 执行最终自主决策 - 处理技能执行结果")
    
    try:
        # 获取输入文本
        input_text = ""
        if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            input_text = context.input_data.get("text", "")
        elif hasattr(context, 'get_shared_data'):
            input_text = context.get_shared_data("input_text", "")
        
        # 获取用户ID - 🔥 老王修复：完善用户ID获取逻辑
        user_id = None
        
        # 1. 从input_data中获取
        if hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            user_id = (context.input_data.get("user_id") or 
                      context.input_data.get("from_user_id") or 
                      context.input_data.get("from_user_ID"))
        
        # 2. 从metadata中获取 - 🔥 老王修复：这是关键！
        if not user_id and hasattr(context, 'metadata') and isinstance(context.metadata, dict):
            user_id = (context.metadata.get("user_id") or
                      context.metadata.get("from_user_id") or
                      context.metadata.get("from_user_ID"))
        
        # 3. 从shared_data中获取
        if not user_id and hasattr(context, 'get_shared_data'):
            user_id = (context.get_shared_data("user_id") or
                      context.get_shared_data("from_user_id") or
                      context.get_shared_data("from_user_ID"))
        
        # 4. 如果context是字典类型，直接从中获取
        if not user_id and isinstance(context, dict):
            user_id = (context.get("user_id") or
                      context.get("from_user_id") or
                      context.get("from_user_ID"))
        
        # 5. 如果context有直接的user_id属性
        if not user_id and hasattr(context, 'user_id'):
            user_id = context.user_id
        
        # 🔥 老王修复：增加调试日志，看看到底获取到了什么
        logger.debug(f"🔍 用户ID获取调试:")
        if hasattr(context, 'input_data'):
            logger.debug(f"   - input_data类型: {type(context.input_data)}")
            if isinstance(context.input_data, dict):
                logger.debug(f"   - input_data keys: {list(context.input_data.keys())}")
                logger.debug(f"   - user_id字段: {context.input_data.get('user_id')}")
                logger.debug(f"   - from_user_id字段: {context.input_data.get('from_user_id')}")
                logger.debug(f"   - from_user_ID字段: {context.input_data.get('from_user_ID')}")
        
        if hasattr(context, 'metadata'):
            logger.debug(f"   - metadata类型: {type(context.metadata)}")
            if isinstance(context.metadata, dict):
                logger.debug(f"   - metadata keys: {list(context.metadata.keys())}")
                logger.debug(f"   - metadata user_id: {context.metadata.get('user_id')}")
                logger.debug(f"   - metadata from_user_id: {context.metadata.get('from_user_id')}")
                logger.debug(f"   - metadata from_user_ID: {context.metadata.get('from_user_ID')}")
        
        if hasattr(context, 'get_shared_data'):
            logger.debug(f"   - shared_data user_id: {context.get_shared_data('user_id')}")
            logger.debug(f"   - shared_data from_user_id: {context.get_shared_data('from_user_id')}")
            logger.debug(f"   - shared_data from_user_ID: {context.get_shared_data('from_user_ID')}")
        
        logger.debug(f"   - 最终获取的user_id: {user_id}")
        
        if not user_id or user_id == "default_user":
            logger.error("🧠 最终决策失败: 无效的用户ID")
            return {"error": "Invalid user_id: 生产环境不允许default_user或空用户ID"}
        
        # 检查技能执行结果
        skill_executed = False
        skill_result = None
        skill_name = None
        intent_type = "chat"
        search_executed = False  # 🔥 修复：初始化search_executed变量
        
        # 🔥 修复：首先从shared_data中获取技能结果（兼容测试和实际运行）
        if hasattr(context, 'get_shared_data'):
            skill_executed = context.get_shared_data("skill_executed", False)
            skill_result = context.get_shared_data("skill_result", None)
            skill_name = context.get_shared_data("skill_name", None)

            # 🔥 调试：打印获取到的技能信息
            logger.debug(f"🔍 从shared_data获取技能信息:")
            logger.debug(f"   - skill_executed: {skill_executed}")
            logger.debug(f"   - skill_result类型: {type(skill_result)}")
            logger.debug(f"   - skill_result内容: {skill_result}")
            logger.debug(f"   - skill_name: {skill_name}")

            # 🔥 增强：检查技能结果的有效性
            if skill_executed and skill_result:
                if isinstance(skill_result, dict):
                    success_flag = skill_result.get("success", False)
                    result_content = skill_result.get("result", None)
                    logger.debug(f"   - 技能成功标志: {success_flag}")
                    logger.debug(f"   - 结果内容: {result_content}")

                    # 如果技能执行成功且有结果内容，记录详细信息
                    if success_flag and result_content:
                        logger.success(f"✅ 检测到有效技能结果: {skill_name}, 内容长度: {len(str(result_content))}")
                elif isinstance(skill_result, str) and skill_result.strip():
                    logger.success(f"✅ 检测到字符串类型技能结果: {skill_name}, 长度: {len(skill_result)}")
                else:
                    logger.warning(f"⚠️ 技能结果格式异常: {skill_name}, 类型: {type(skill_result)}")
            
        # 如果shared_data中没有，再从step_result中获取
        if not skill_executed and hasattr(context, 'get_step_result'):
            # 从技能执行步骤获取结果
            skill_execution_result = context.get_step_result("skill_execution", {})
            if skill_execution_result and isinstance(skill_execution_result, dict):
                skill_executed = skill_execution_result.get("skill_executed", False)
                skill_result = skill_execution_result.get("skill_result", None)
                skill_name = skill_execution_result.get("skill_name", None)
                intent_type = skill_execution_result.get("intent_type", "chat")
                should_skip_reasoning = skill_execution_result.get("should_skip_reasoning", False)
                search_executed = skill_execution_result.get("search_executed", False)
                
                logger.debug(f"🧠 最终决策：处理技能执行结果 - {skill_name}, 搜索执行: {search_executed}")
        
        # 🔥 修复：即使skill_executed为True，也要检查step_result中的search_executed
        if skill_executed and hasattr(context, 'get_step_result'):
            skill_execution_result = context.get_step_result("skill_execution", {})
            if skill_execution_result and isinstance(skill_execution_result, dict):
                # 更新search_executed状态
                search_executed = skill_execution_result.get("search_executed", search_executed)
        
        # 🔥 修复：特殊处理assistant_reminder_skill，即使skill_result为None也要返回成功响应
        if skill_executed and skill_name == "assistant_reminder_skill":
            # 从日志中我们看到技能确实执行成功了，但result可能为None
            # 这种情况下我们仍然认为是成功的，返回固定回复
            logger.success("助理提醒技能执行成功，返回固定回复")
            return {
                "type": "reminder_response",
                "confidence": 0.95,
                "reasoning": "助理提醒技能执行成功",
                "response": "亲，我记住了～",
                "requires_realtime_data": False,
                "from_skill": True,
                "skill_name": skill_name,
                "task_created": True
            }
        
        # 继续处理其他技能类型
        if hasattr(context, 'get_step_result'):
            # 从技能执行步骤获取结果
            skill_execution_result = context.get_step_result("skill_execution", {})
            if skill_execution_result and isinstance(skill_execution_result, dict):
                # 特殊处理绘画技能：如果绘画技能成功执行，直接返回图片链接
                if skill_executed and skill_result and skill_name == "drawing_skill":
                    logger.success(f"绘画技能 {skill_name} 已执行成功，直接返回图片链接")
                    
                    # 检查技能结果格式
                    if isinstance(skill_result, dict):
                        success_flag = skill_result.get("success", False)
                        
                        if success_flag:
                            # 绘画成功，提取图片URL
                            result_data = skill_result.get("result", {})

                            # 🔥 修复：处理result_data可能是字符串的情况
                            if isinstance(result_data, str):
                                # 如果result_data是字符串，直接作为图片URL使用
                                image_url = result_data
                                service = "unknown"
                                prompt = ""
                            elif isinstance(result_data, dict):
                                # 如果result_data是字典，按原逻辑提取
                                image_url = result_data.get("image_url", "")
                                service = result_data.get("service", "unknown")
                                prompt = result_data.get("prompt", "")
                            else:
                                # 其他情况，尝试从skill_result直接获取
                                image_url = skill_result.get("image_url", "")
                                service = skill_result.get("service", "unknown")
                                prompt = skill_result.get("prompt", "")

                            if image_url:
                                response_text = f"我为你画了一幅画！🎨\n\n{image_url}\n\n使用的服务：{service}\n提示词：{prompt}"
                            else:
                                response_text = skill_result.get("message", "绘画完成，但未获取到图片链接")
                        else:
                            # 绘画失败
                            response_text = skill_result.get("message", "绘画失败，请稍后重试")
                    else:
                        # 兼容旧格式
                        response_text = str(skill_result)
                    
                    # 生成完整的决策对象
                    result_dict = {
                        "type": "drawing_response",
                        "confidence": 0.95,
                        "reasoning": f"绘画技能 {skill_name} 执行结果",
                        "response": response_text,
                        "requires_realtime_data": False,
                        "from_skill": True,
                        "skill_name": skill_name
                    }
                    
                    return result_dict
                
                # 对于ChatSkill，如果已经成功执行且包含搜索上下文，检查是否是AI响应
                if skill_executed and skill_result and skill_name == "chat_skill":
                    logger.success(f"聊天技能 {skill_name} 已执行成功，检查结果类型")
                    
                    # 处理聊天技能结果，确保响应格式正确
                    if isinstance(skill_result, dict):
                        if 'result' in skill_result:
                            response_text = str(skill_result['result'])
                        elif 'message' in skill_result:
                            response_text = str(skill_result['message'])
                        elif 'response' in skill_result:
                            response_text = str(skill_result['response'])
                        else:
                            response_text = str(skill_result)
                    else:
                        response_text = str(skill_result)
                    
                    # 检查是否是真正的AI响应（不是原始搜索结果）
                    if (isinstance(response_text, str) and 
                        len(response_text) > 1 and 
                        not ('success' in response_text and 'skill_id' in response_text)):
                        
                        # 这是真正的AI响应，直接使用
                        logger.success(f"聊天技能返回AI响应: {response_text[:50]}...")
                        
                        # 生成完整的决策对象
                        result_dict = {
                            "type": "chat_response",
                            "confidence": 0.95,
                            "reasoning": f"基于聊天技能 {skill_name} 的AI响应" + (f" (包含搜索结果)" if search_executed else ""),
                            "response": response_text,
                            "requires_realtime_data": False,
                            "from_skill": True,
                            "skill_name": skill_name,
                            "search_integrated": search_executed
                        }
                        
                        return result_dict
                
                # 🔥 增强：处理搜索技能结果
                if skill_name == "search_skill" and skill_result and isinstance(skill_result, dict):
                    success_flag = skill_result.get("success", False)
                    if success_flag:
                        search_content = skill_result.get("result", "")
                        if search_content and isinstance(search_content, str) and len(search_content.strip()) > 0:
                            logger.success(f"✅ 搜索技能执行成功，获得搜索结果，长度: {len(search_content)}")

                            # 使用搜索结果生成回复
                            response_text = f"根据我的搜索，{search_content}"

                            return {
                                "type": "search_response",
                                "confidence": 0.9,
                                "reasoning": "基于搜索技能的结果生成回复",
                                "response": response_text,
                                "requires_realtime_data": False,
                                "from_skill": True,
                                "skill_name": skill_name,
                                "search_executed": True
                            }
                        else:
                            logger.warning(f"⚠️ 搜索技能成功但结果为空: {search_content}")
                    else:
                        logger.warning(f"⚠️ 搜索技能执行失败: {skill_result}")

                # 🔥 修复：检查是否是assistant_reminder_skill，返回固定回复
                logger.debug(f"🔍 检查assistant_reminder_skill: skill_name={skill_name}, skill_result={skill_result}")
                if skill_name == "assistant_reminder_skill" and skill_result and isinstance(skill_result, dict) and skill_result.get("success", False):
                    logger.success("助理提醒技能执行成功，返回固定回复")
                    return {
                        "type": "reminder_response",
                        "confidence": 0.95,
                        "reasoning": "助理提醒技能执行成功",
                        "response": "亲，我记住了～",
                        "requires_realtime_data": False,
                        "from_skill": True,
                        "skill_name": skill_name,
                        "task_created": True
                    }
        
        # 如果没有有效的技能结果，获取推理结果
        reasoning_result = {}
        if hasattr(context, 'get_step_result'):
            reasoning_result = context.get_step_result("cognitive_reasoning", {})
        
        # 使用推理结果
        if reasoning_result and isinstance(reasoning_result, dict):
            suggested_response = reasoning_result.get("suggested_response")
            if suggested_response and suggested_response != "我正在思考中...":
                logger.debug(f"使用推理结果生成回复: {suggested_response[:50]}...")
                
                # 生成完整的决策对象
                result_dict = {
                    "type": "reasoning_response",
                    "confidence": 0.9,
                    "reasoning": "基于AI推理生成的智能回复",
                    "response": suggested_response,
                    "requires_realtime_data": False,
                    "from_reasoning": True
                }
                
                return result_dict
        
        # 兜底响应 - 🔥 修复：使用chat skill生成符合林嫣然人格的响应
        logger.warning_status("🧠 最终决策：没有找到有效的技能结果或推理结果，使用chat skill生成兜底响应")
        
        try:
            # 🔥 修复：由于process_final不是异步函数，使用线程池来调用异步函数
            import asyncio
            import concurrent.futures
            
            def run_async_fallback():
                try:
                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    return loop.run_until_complete(_generate_fallback_response_with_chat_skill(context))
                except Exception as e:
                    logger.warning(f"异步兜底响应生成失败: {e}")
                    return None
                finally:
                    loop.close()
            
            # 使用线程池执行异步函数
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_async_fallback)
                try:
                    fallback_response = future.result(timeout=3000)  # 300秒超时，支持复杂逻辑执行
                    if fallback_response:
                        logger.success(f"✅ 使用chat skill生成兜底响应: {fallback_response[:50]}...")
                        return {
                            "type": "fallback_response",
                            "confidence": 0.7,
                            "reasoning": "通过chat skill生成的兜底响应，保持角色一致性",
                            "response": fallback_response,
                            "requires_realtime_data": False,
                            "from_fallback": True,
                            "from_chat_skill": True
                        }
                except concurrent.futures.TimeoutError:
                    logger.warning("⚠️ chat skill兜底响应生成超时")
                except Exception as thread_error:
                    logger.warning(f"⚠️ 线程池执行失败: {thread_error}")
                    
        except Exception as e:
            logger.warning(f"⚠️ chat skill兜底响应生成失败: {e}")
        
        # 🔥 P0级修复：如果chat skill也失败了，再次尝试使用chat skill生成兜底响应
        # 使用更简单的提示，确保能够生成响应
        logger.warning("⚠️ chat skill兜底响应失败，尝试使用简化提示再次调用")

        # 🔥 修复：确保在紧急兜底响应中也能获取到用户信息
        # 从context重新获取用户信息，避免作用域问题
        emergency_user_id = None
        emergency_user_name = None

        # 从context获取用户信息
        if hasattr(context, 'get_shared_data'):
            emergency_user_id = (context.get_shared_data("user_id") or
                                context.get_shared_data("from_user_id") or
                                context.get_shared_data("from_user_ID") or
                                "default_user")
            emergency_user_name = (context.get_shared_data("user_name") or
                                  context.get_shared_data("name") or
                                  "朋友")
        else:
            emergency_user_id = "default_user"
            emergency_user_name = "朋友"

        logger.debug(f"🔄 紧急兜底用户信息: user_id={emergency_user_id}, user_name={emergency_user_name}")

        try:
            from cognitive_modules.skills.chat_skill import get_instance as get_chat_skill
            chat_skill = get_chat_skill()

            if chat_skill:
                # 使用最简化的提示，确保chat skill能够处理
                simple_fallback_params = {
                    "input_text": context.input_data.get("text", ""),
                    "user_id": emergency_user_id,
                    "session_id": f"emergency_fallback_{int(time.time())}",
                    "is_fallback": True,
                    "context": {
                        "scenario": "emergency_fallback",
                        "user_id": emergency_user_id
                    }
                }

                if emergency_user_name:
                    simple_fallback_params["user_name"] = emergency_user_name

                logger.debug(f"🔄 尝试紧急兜底响应，参数: {simple_fallback_params}")

                emergency_result = chat_skill.execute(**simple_fallback_params)

                if emergency_result and isinstance(emergency_result, dict) and emergency_result.get("success", False):
                    emergency_response = emergency_result.get("result", "")
                    if emergency_response and emergency_response.strip():
                        logger.success(f"✅ 紧急兜底响应生成成功: {emergency_response[:50]}...")
                        return {
                            "type": "emergency_fallback_response",
                            "confidence": 0.7,
                            "reasoning": "通过chat skill紧急兜底响应生成",
                            "response": emergency_response,
                            "requires_realtime_data": False,
                            "from_fallback": True,
                            "from_chat_skill": True,
                            "emergency_fallback": True
                        }
        except Exception as emergency_e:
            logger.warning(f"⚠️ 紧急兜底响应也失败: {emergency_e}")

        # 最后的兜底：使用配置化的林嫣然风格响应（仅在所有chat skill尝试都失败时）
        from utilities.fallback_config_manager import get_emergency_response
        selected_response = get_emergency_response()

        logger.warning(f"⚠️ 所有chat skill尝试都失败，使用配置化最终兜底响应: {selected_response}")

        return {
            "type": "final_fallback_response",
            "confidence": 0.5,
            "reasoning": "所有chat skill尝试都失败，使用最终兜底响应",
            "response": selected_response,
            "requires_realtime_data": False,
            "from_fallback": True,
            "final_fallback": True
        }
        
    except Exception as e:
        logger.error_status(f"🧠 最终决策处理异常: {e}")
        import traceback
        logger.error_status(f"详细错误信息: {traceback.format_exc()}")
        
        return {
            "type": "error_response",
            "confidence": 0.1,
            "reasoning": f"最终决策处理异常: {str(e)}",
            "response": "抱歉，处理您的请求时出现了问题。",
            "requires_realtime_data": False,
            "from_error": True
        }

async def _generate_fallback_response_with_chat_skill(context) -> str:
    """
    使用chat skill生成兜底响应，保持林嫣然的人格一致性
    
    Args:
        context: 思维链路上下文
        
    Returns:
        生成的兜底响应文本，如果失败返回None
    """
    try:
        import time
        from utilities.unified_logger import get_unified_logger
        logger = get_unified_logger("conscious_decision")
        
        # 🔥 P0级修复：正确获取用户ID和用户名，避免丢失
        user_id = None
        user_name = None
        user_message = ""
        
        # 优先从context的metadata中获取
        if hasattr(context, 'metadata') and isinstance(context.metadata, dict):
            user_id = (context.metadata.get("user_id") or
                      context.metadata.get("from_user_id") or
                      context.metadata.get("from_user_ID"))
            user_name = (context.metadata.get("user_name") or
                        context.metadata.get("name"))
            user_message = context.metadata.get("content", "")
        
        # 如果metadata中没有，从input_data中获取
        if not user_id and hasattr(context, 'input_data') and isinstance(context.input_data, dict):
            user_id = (context.input_data.get("user_id") or
                      context.input_data.get("from_user_id") or
                      context.input_data.get("from_user_ID"))
            if not user_name:
                user_name = (context.input_data.get("user_name") or
                           context.input_data.get("name"))
            if not user_message:
                user_message = context.input_data.get("content", "")
        
        # 如果还没有，从shared_data中获取
        if not user_id and hasattr(context, 'get_shared_data'):
            user_id = (context.get_shared_data("user_id") or
                      context.get_shared_data("from_user_id") or
                      context.get_shared_data("from_user_ID"))
            if not user_name:
                user_name = (context.get_shared_data("user_name") or
                           context.get_shared_data("name"))
            if not user_message:
                user_message = context.get_shared_data("input_text", "")
        
        # 如果仍然没有用户ID，这是严重错误
        if not user_id or user_id == "default_user":
            logger.error(f"❌ P0级错误：fallback生成时无法获取有效用户ID，user_id={user_id}")
            return None

        # 🔥 P0级修复：增强用户信息获取逻辑，确保不丢失任何信息
        logger.debug(f"🔍 开始增强用户信息获取:")
        logger.debug(f"   - 初始user_id: {user_id}")
        logger.debug(f"   - 初始user_name: {user_name}")
        logger.debug(f"   - 初始user_message: {user_message}")

        # 如果user_id为空或无效，这是P0级错误
        if not user_id or user_id == "default_user":
            logger.error(f"❌ P0级错误：用户ID无效或为空: {user_id}")
            # 尝试从所有可能的地方获取用户ID
            if hasattr(context, 'get_shared_data'):
                user_id = (context.get_shared_data("user_id") or
                          context.get_shared_data("from_user_id") or
                          context.get_shared_data("from_user_ID") or
                          context.get_shared_data("unified_user_id"))
                logger.debug(f"🔄 从shared_data重新获取user_id: {user_id}")

        # 如果user_name为空，尝试从多个来源获取
        if not user_name or user_name.strip() == "":
            logger.warning(f"⚠️ 用户名为空，尝试从多个来源获取")
            if hasattr(context, 'get_shared_data'):
                user_name = (context.get_shared_data("user_name") or
                           context.get_shared_data("name") or
                           context.get_shared_data("unified_user_name"))
                logger.debug(f"🔄 从shared_data重新获取user_name: {user_name}")

        # 如果user_message为空，尝试从其他字段获取或使用默认消息
        if not user_message or not user_message.strip():
            # 🔥 修复：更全面的用户消息获取逻辑
            if hasattr(context, 'get_shared_data'):
                for field in ["input_text", "text", "user_input", "message", "content"]:
                    user_message = context.get_shared_data(field) or ""
                    if user_message and user_message.strip():
                        logger.debug(f"✅ 从共享数据字段 {field} 获取到用户消息: {user_message[:50]}...")
                        break
                        
            # 🔥 如果共享数据中没有，尝试从input_data中获取
            if not user_message or not user_message.strip():
                if hasattr(context, 'input_data') and context.input_data:
                    for field in ["text", "input_text", "user_input", "message", "content"]:
                        user_message = context.input_data.get(field) or ""
                        if user_message and user_message.strip():
                            logger.debug(f"✅ 从input_data字段 {field} 获取到用户消息: {user_message[:50]}...")
                            break

            # 如果仍然为空，使用配置化的兜底消息
            if not user_message or not user_message.strip():
                from utilities.fallback_config_manager import get_empty_message_fallback
                user_message = get_empty_message_fallback()
                logger.warning(f"⚠️ 兜底响应：用户消息为空，使用配置化默认消息: {user_message}")
            else:
                logger.debug(f"✅ 从备选字段获取到用户消息: {user_message[:30]}...")

        # 最终验证：确保关键信息不为空
        logger.debug(f"🔍 最终用户信息验证:")
        logger.debug(f"   - 最终user_id: {user_id}")
        logger.debug(f"   - 最终user_name: {user_name}")
        logger.debug(f"   - 最终user_message: {user_message[:50]}...")

        # 如果用户名仍然为空，使用用户ID生成一个友好的名称
        if not user_name or user_name.strip() == "":
            if user_id and user_id != "default_user":
                user_name = f"用户{user_id[-6:]}"  # 使用用户ID的后6位
                logger.warning(f"⚠️ 用户名为空，使用用户ID生成友好名称: {user_name}")
            else:
                user_name = "朋友"  # 最终兜底
                logger.warning(f"⚠️ 用户ID和用户名都无效，使用通用称呼: {user_name}")
        
        # 构建兜底响应的上下文
        fallback_context = {
            "user_message": user_message,
            "user_id": user_id,
            "scenario": "fallback_response",
            "purpose": "生成符合林嫣然人格的兜底响应"
        }
        
        logger.debug(f"🔄 尝试使用chat skill生成兜底响应，用户消息: {user_message[:30]}...")
        
        # 尝试获取chat skill实例
        try:
            from cognitive_modules.skills.chat_skill import get_instance as get_chat_skill
            chat_skill = get_chat_skill()
            
            if not chat_skill:
                logger.warning("chat skill实例获取失败")
                return None
                
            # 构建适合兜底响应的提示
#             fallback_prompt = f"""
# 用户说：{user_message}

# 现在需要你作为林嫣然给出一个自然的回应。由于某些技术原因，之前的处理流程没有成功，
# 但你要保持自己温暖、真诚的人格特质，给出一个合适的回应。

# 不要说"我理解了您的意思"这种官方套话，要体现出林嫣然的个性：
# - 温暖而真诚
# - 有点小迷糊但很可爱
# - 会坦诚地表达自己的状态
# - 关心用户的感受

# 请给出一个自然的回应：
# """

            fallback_prompt = user_message
            
            # 🔥 修复：使用正确的参数格式调用chat skill，包含用户名
            chat_skill_params = {
                "input_text": fallback_prompt,
                "user_id": user_id,
                "session_id": f"fallback_session_{int(time.time())}",
                "context": fallback_context,
                "is_fallback": True
            }
            
            # 🔥 老王修复：增强用户名获取逻辑
            if not user_name:
                # 尝试从统一用户管理器获取
                try:
                    from core.unified_user_manager import get_unified_user_manager
                    user_manager = get_unified_user_manager()
                    if user_manager:
                        user = user_manager.get_user(user_id)
                        if user and user.name:
                            user_name = user.name
                            logger.debug(f"✅ 从统一用户管理器获取用户名: {user_name}")
                except Exception as e:
                    logger.debug(f"从统一用户管理器获取用户名失败: {e}")
                
                # 如果还没有，使用兜底名称
                if not user_name:
                    user_name = f"用户{user_id[-6:]}"
                    logger.warning(f"⚠️ 兜底响应使用降级用户名: {user_name}")
            
            # 🔥 关键修复：如果有用户名，添加到参数中
            if user_name:
                chat_skill_params["user_name"] = user_name
                logger.debug(f"🔥 兜底响应传递用户名: {user_name}")
            else:
                logger.warning("⚠️ 兜底响应中没有获取到用户名")
            
            response_result = chat_skill.execute(**chat_skill_params)
            
            if response_result and isinstance(response_result, dict):
                if response_result.get("success", False):
                    # 提取响应内容
                    response_content = None
                    if "result" in response_result:
                        response_content = response_result["result"]
                    elif "response" in response_result:
                        response_content = response_result["response"]
                    elif "message" in response_result:
                        response_content = response_result["message"]
                    
                    if response_content and isinstance(response_content, str):
                        response_content = response_content.strip()
                        if len(response_content) > 5:  # 确保不是空响应
                            logger.success(f"✅ chat skill兜底响应生成成功: {response_content[:50]}...")
                            return response_content
                
                logger.warning(f"chat skill返回结果无效: {response_result}")
            else:
                logger.warning("chat skill返回结果格式异常")
                
        except Exception as skill_error:
            logger.warning(f"调用chat skill失败: {skill_error}")
            
        return None
        
    except Exception as e:
        logger.warning(f"生成chat skill兜底响应异常: {e}")
        return None
