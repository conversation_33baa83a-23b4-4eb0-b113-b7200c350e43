#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自主决策系统 - Autonomous Decision Making

该模块实现了数字生命体的自主决策系统，负责基于目标、情感和记忆
进行行为决策，使数字生命体能够展现自主性和主动性。

主要功能：
1. 目标驱动的决策：基于当前目标和优先级选择行为
2. 情感影响的决策：考虑情感状态对决策的影响
3. 记忆辅助决策：利用过往经验指导当前决策
4. 价值观约束：根据内在价值观评估和选择行为

作者: Claude
创建日期: 2024-07-18
版本: 1.0
"""

import os
import sys
import time
import json
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Set, Callable

# 配置日志
setup_unified_logging()
logger = get_unified_logger("cognitive_modules.autonomy.decision_making")

# 导入相关模块
from core import event_bus, life_context
from cognitive_modules.emotion import emotion_simulation
from cognitive_modules.memory import memory_integration
from cognitive_modules.autonomy import values_system

class DecisionMaking:
    """自主决策系统类，负责数字生命体的行为决策"""
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls, module_id: str = "default", config: Dict[str, Any] = None) -> 'DecisionMaking':
        """获取决策系统单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = DecisionMaking(module_id, config)
        return cls._instance
    
    def __init__(self, module_id: str = "default", config: Dict[str, Any] = None):
        """
        初始化决策系统
        
        Args:
            module_id: 模块ID
            config: 配置参数
        """
        # 确保单例模式
        if DecisionMaking._instance is not None:
            raise RuntimeError("DecisionMaking已经实例化，请使用get_instance()获取实例")
        
        self.module_id = module_id
        self.config = config or {}
        
        # 模块状态
        self.is_active = False
        self.is_initialized = False
        self.last_error = None
        
        # 获取事件总线和生命上下文实例
        self.event_bus = event_bus.get_instance()
        self.life_context = life_context.get_instance()
        
        # 获取其他相关模块实例
        self.emotion_module = None  # 情感模块实例
        self.memory_module = None   # 记忆模块实例
        self.values_module = None   # 价值观系统实例
        
        # 决策相关属性
        self.current_goals = []     # 当前目标列表
        self.decision_history = []  # 决策历史
        self.decision_threshold = self.config.get("decision_threshold", 0.6)  # 决策阈值
        self.max_history_length = self.config.get("max_history_length", 50)   # 决策历史最大长度
        self.decision_factors = {   # 决策因素权重
            "goal_alignment": 0.4,    # 目标一致性权重
            "emotional_preference": 0.2,  # 情感偏好权重
            "memory_guidance": 0.2,    # 记忆指导权重
            "value_alignment": 0.2,    # 价值观一致性权重
        }
        
        # 价值观系统
        self.values = self.config.get("values", {
            "诚实": 0.9,
            "善良": 0.8,
            "尊重": 0.9,
            "好奇": 0.7,
            "成长": 0.8,
            "理性": 0.7,
            "情感表达": 0.6
        })
        
        logger.info(f"决策系统已创建: {module_id}")
    
    def initialize(self) -> bool:
        """初始化决策系统"""
        try:
            logger.success("初始化决策系统...")
            
            # 获取情感模块实例
            self.emotion_module = emotion_simulation.get_instance(self.module_id)
            
            # 获取记忆模块实例
            self.memory_module = memory_integration.get_instance(self.module_id)
            
            # 获取价值观系统实例
            self.values_module = values_system.get_instance(self.module_id)
            
            # 加载初始目标
            self._load_initial_goals()
            
            # 订阅相关事件
            self._subscribe_events()
            
            # 设置状态
            self.is_initialized = True
            self.is_active = True
            
            logger.success("决策系统初始化完成")
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"初始化决策系统失败: {e}")
            return False
    
    def _load_initial_goals(self):
        """加载初始目标"""
        # 从配置加载初始目标
        initial_goals = self.config.get("initial_goals", [
            {
                "id": "goal_learn",
                "description": "学习和积累知识",
                "priority": 0.8,
                "category": "成长",
                "progress": 0.0
            },
            {
                "id": "goal_communicate",
                "description": "有效沟通和表达",
                "priority": 0.7,
                "category": "社交",
                "progress": 0.0
            },
            {
                "id": "goal_self_improve",
                "description": "自我完善和发展",
                "priority": 0.9,
                "category": "成长",
                "progress": 0.0
            }
        ])
        
        self.current_goals = initial_goals
        logger.info(f"已加载 {len(self.current_goals)} 个初始目标")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        # 订阅用户交互事件
        self.event_bus.subscribe("user_message", self._on_user_message)
        
        # 订阅情感变化事件
        self.event_bus.subscribe("emotion_updated", self._on_emotion_updated)
        
        # 订阅记忆检索事件
        self.event_bus.subscribe("memory_retrieved", self._on_memory_retrieved)
        
        logger.debug("已订阅相关事件")
    
    def make_decision(self, context: Dict[str, Any], options: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        做出决策
        
        Args:
            context: 决策上下文信息
            options: 可选行为选项列表
            
        Returns:
            选择的行为选项
        """
        try:
            logger.info(f"开始决策过程，共 {len(options)} 个选项")
            
            if not options:
                logger.warning_status("没有可选选项，无法做出决策")
                return None
            
            # 评估每个选项
            option_scores = []
            for option in options:
                score = self._evaluate_option(option, context)
                option_scores.append((option, score))
                logger.debug(f"选项 '{option.get('description', 'N/A')}' 评分: {score:.4f}")
            
            # 排序选项
            option_scores.sort(key=lambda x: x[1], reverse=True)
            best_option, best_score = option_scores[0]
            
            # 检查是否超过决策阈值
            if best_score < self.decision_threshold:
                logger.info(f"最佳选项分数 {best_score:.4f} 低于阈值 {self.decision_threshold}，采取默认行为")
                return self._get_default_option(context)
            
            # 记录决策历史
            self._record_decision(best_option, best_score, context)
            
            logger.success(f"决策完成，选择: {best_option.get('description', 'N/A')} (分数: {best_score:.4f})")
            return best_option
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"决策过程发生错误: {e}")
            return self._get_default_option(context)
    
    def _evaluate_option(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """评估选项得分"""
        # 1. 目标一致性评估
        goal_score = self._evaluate_goal_alignment(option, context)
        
        # 2. 情感偏好评估
        emotion_score = self._evaluate_emotional_preference(option, context)
        
        # 3. 记忆指导评估
        memory_score = self._evaluate_memory_guidance(option, context)
        
        # 4. 价值观一致性评估
        value_score = self._evaluate_value_alignment(option, context)
        
        # 计算加权总分
        total_score = (
            goal_score * self.decision_factors["goal_alignment"] +
            emotion_score * self.decision_factors["emotional_preference"] +
            memory_score * self.decision_factors["memory_guidance"] +
            value_score * self.decision_factors["value_alignment"]
        )
        
        return total_score
    
    def _evaluate_goal_alignment(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """评估选项与当前目标的一致性"""
        if not self.current_goals:
            return 0.5  # 默认中等一致性
        
        # 获取选项相关的目标类别
        option_categories = option.get("goal_categories", [])
        if not option_categories:
            return 0.5  # 无法确定目标类别
        
        # 计算与高优先级目标的一致性
        alignment_scores = []
        for goal in self.current_goals:
            if goal["category"] in option_categories:
                alignment_scores.append(goal["priority"])
        
        if not alignment_scores:
            return 0.3  # 与当前目标无关
        
        # 返回最高一致性分数
        return max(alignment_scores)
    
    def _evaluate_emotional_preference(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """评估选项与当前情感状态的兼容性"""
        if not self.emotion_module:
            return 0.5  # 无法获取情感状态
        
        # 获取当前情感状态
        emotion_state = self.emotion_module.get_emotion_state()
        current_emotion = emotion_state.get("current_emotion", "平静")
        valence = emotion_state.get("valence", 0.0)
        
        # 获取选项的情感倾向
        option_emotions = option.get("emotional_impact", {})
        
        # 特定情感匹配度
        if current_emotion in option_emotions:
            return option_emotions[current_emotion]
        
        # 基于效价的一般匹配度
        if valence > 0.3 and "positive" in option_emotions:
            return option_emotions["positive"]
        elif valence < -0.3 and "negative" in option_emotions:
            return option_emotions["negative"]
        
        return 0.5  # 默认中等兼容性
    
    def _evaluate_memory_guidance(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """基于过往经验评估选项"""
        if not self.memory_module:
            return 0.5  # 无法获取记忆
        
        # 获取选项相关的标签或关键词
        option_tags = option.get("tags", [])
        if not option_tags:
            return 0.5  # 无法确定相关标签
        
        # 搜索相关记忆
        query = " ".join(option_tags[:3])  # 使用前三个标签作为查询
        results = self.memory_module.search_memory(query, memory_types=["episodic"])
        
        # 如果没有相关记忆
        if not results or not results.get("episodic"):
            return 0.4  # 稍低于默认值，表示缺乏经验
        
        # 分析记忆情感倾向
        episodic_memories = results.get("episodic", [])
        positive_count = 0
        negative_count = 0
        
        for memory in episodic_memories[:5]:  # 只分析前5个最相关的记忆
            emotion = memory.get("emotion", "平静")
            if emotion in ["喜悦", "满足", "骄傲", "平静"]:
                positive_count += 1
            elif emotion in ["悲伤", "愤怒", "恐惧", "焦虑"]:
                negative_count += 1
        
        total_count = positive_count + negative_count
        if total_count == 0:
            return 0.5
        
        # 计算情感比例
        score = 0.5 + (positive_count - negative_count) / (total_count * 2)
        return max(0.1, min(0.9, score))  # 限制在0.1-0.9范围内
    
    def _evaluate_value_alignment(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """评估选项与核心价值观的一致性"""
        # 如果有价值观系统实例，使用价值观系统进行评估
        if self.values_module and self.values_module.is_initialized:
            try:
                # 使用价值观系统评估行为
                evaluation = self.values_module.evaluate_behavior(option)
                return evaluation["overall_score"]
            except Exception as e:
                logger.warning_status(f"使用价值观系统评估失败，回退到基本评估: {e}")
        
        # 基本评估（原有逻辑作为备选）
        # 获取选项相关的价值观
        option_values = option.get("values", {})
        if not option_values:
            return 0.5  # 无法确定价值观影响
        
        # 计算价值观一致性得分
        alignment_scores = []
        for value, impact in option_values.items():
            if value in self.values:
                # 价值观重要性 * 选项对该价值观的影响
                score = self.values[value] * impact
                alignment_scores.append(score)
        
        if not alignment_scores:
            return 0.5  # 未找到相关价值观
        
        # 返回平均一致性分数
        return sum(alignment_scores) / len(alignment_scores)
    
    def _get_default_option(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """当无法做出决策时返回默认选项"""
        return {
            "id": "default_action",
            "type": "response",
            "description": "提供一个平和、中立的回应",
            "content": "我需要更多信息来做出更好的回应。",
            "goal_categories": ["沟通"],
            "emotional_impact": {"平静": 0.7},
            "values": {"诚实": 0.8}
        }
    
    def _record_decision(self, option: Dict[str, Any], score: float, context: Dict[str, Any]):
        """记录决策历史"""
        decision_record = {
            "timestamp": time.time(),
            "option_id": option.get("id", "unknown"),
            "option_type": option.get("type", "unknown"),
            "description": option.get("description", ""),
            "score": score,
            "context_summary": self._summarize_context(context)
        }
        
        # 如果可用，添加价值观评估结果
        if self.values_module and self.values_module.is_initialized:
            try:
                value_evaluation = self.values_module.evaluate_behavior(option)
                decision_record["value_evaluation"] = {
                    "overall_score": value_evaluation["overall_score"],
                    "alignment": value_evaluation["alignment"],
                    "conflicts": value_evaluation["conflicts"]
                }
            except Exception as e:
                logger.debug(f"添加价值观评估结果失败: {e}")
        
        # 添加到历史并限制长度
        self.decision_history.append(decision_record)
        if len(self.decision_history) > self.max_history_length:
            self.decision_history = self.decision_history[-self.max_history_length:]
    
    def _summarize_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """简化上下文以便记录"""
        summary = {}
        
        # 选择关键上下文信息
        if "user_message" in context:
            summary["user_message"] = context["user_message"]
        
        if "current_emotion" in context:
            summary["current_emotion"] = context["current_emotion"]
        
        if "intention" in context:
            summary["intention"] = context["intention"]
        
        return summary
    
    def add_goal(self, goal: Dict[str, Any]) -> bool:
        """添加新目标"""
        try:
            # 验证目标格式
            required_fields = ["id", "description", "priority", "category"]
            for field in required_fields:
                if field not in goal:
                    logger.error_status(f"添加目标失败，缺少必填字段: {field}")
                    return False
            
            # 检查是否已存在相同ID的目标
            existing_ids = [g["id"] for g in self.current_goals]
            if goal["id"] in existing_ids:
                logger.warning_status(f"目标ID已存在: {goal['id']}")
                return False
            
            # 设置默认进度
            if "progress" not in goal:
                goal["progress"] = 0.0
            
            # 添加目标
            self.current_goals.append(goal)
            logger.info(f"添加新目标: {goal['description']}")
            
            # 发布目标添加事件
            self.event_bus.publish("goal_added", {
                "goal_id": goal["id"],
                "description": goal["description"],
                "priority": goal["priority"]
            })
            
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"添加目标失败: {e}")
            return False
    
    def remove_goal(self, goal_id: str) -> bool:
        """移除目标"""
        try:
            for i, goal in enumerate(self.current_goals):
                if goal["id"] == goal_id:
                    removed_goal = self.current_goals.pop(i)
                    logger.info(f"移除目标: {removed_goal['description']}")
                    
                    # 发布目标移除事件
                    self.event_bus.publish("goal_removed", {
                        "goal_id": goal_id,
                        "description": removed_goal["description"]
                    })
                    
                    return True
            
            logger.warning_status(f"未找到要移除的目标: {goal_id}")
            return False
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"移除目标失败: {e}")
            return False
    
    def update_goal_progress(self, goal_id: str, progress_delta: float) -> bool:
        """更新目标进度"""
        try:
            for goal in self.current_goals:
                if goal["id"] == goal_id:
                    old_progress = goal["progress"]
                    goal["progress"] = max(0.0, min(1.0, old_progress + progress_delta))
                    logger.info(f"更新目标进度: {goal['description']} ({old_progress:.2f} -> {goal['progress']:.2f})")
                    
                    # 发布目标进度更新事件
                    self.event_bus.publish("goal_progress_updated", {
                        "goal_id": goal_id,
                        "description": goal["description"],
                        "old_progress": old_progress,
                        "new_progress": goal["progress"]
                    })
                    
                    # 检查目标是否完成
                    if old_progress < 1.0 and goal["progress"] >= 1.0:
                        self.event_bus.publish("goal_completed", {
                            "goal_id": goal_id,
                            "description": goal["description"]
                        })
                    
                    return True
            
            logger.warning_status(f"未找到要更新的目标: {goal_id}")
            return False
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"更新目标进度失败: {e}")
            return False
    
    def get_current_goals(self) -> List[Dict[str, Any]]:
        """获取当前目标列表"""
        # 按优先级排序
        return sorted(self.current_goals, key=lambda g: g["priority"], reverse=True)
    
    def get_decision_history(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取决策历史"""
        if limit is None or limit >= len(self.decision_history):
            return self.decision_history
        return self.decision_history[-limit:]
    
    def _on_user_message(self, data: Dict[str, Any]):
        """处理用户消息事件"""
        # 在实际实现中，可以分析用户消息，调整目标优先级等
        pass
    
    def _on_emotion_updated(self, data: Dict[str, Any]):
        """处理情感更新事件"""
        # 在实际实现中，可以根据情感变化调整决策因素权重
        pass
    
    def _on_memory_retrieved(self, data: Dict[str, Any]):
        """处理记忆检索事件"""
        # 在实际实现中，可以利用检索的记忆调整决策策略
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            "module_id": self.module_id,
            "module_type": "自主决策系统",
            "is_active": self.is_active,
            "is_initialized": self.is_initialized,
            "last_error": self.last_error,
            "goals_count": len(self.current_goals),
            "decision_history_count": len(self.decision_history)
        }
    
    def shutdown(self) -> bool:
        """关闭决策系统"""
        try:
            logger.info("关闭决策系统...")
            self.is_active = False
            
            # 取消事件订阅
            self.event_bus.unsubscribe("user_message", self._on_user_message)
            self.event_bus.unsubscribe("emotion_updated", self._on_emotion_updated)
            self.event_bus.unsubscribe("memory_retrieved", self._on_memory_retrieved)
            
            logger.info("决策系统已关闭")
            return True
            
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"关闭决策系统失败: {e}")
            return False

# 获取单例实例的辅助函数
def get_instance(module_id: str = "default", config: Dict[str, Any] = None) -> DecisionMaking:
    """获取决策系统单例实例"""
    return DecisionMaking.get_instance(module_id, config) 