#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自主决策模块 - Conscious Decision

该模块负责做出有意识的决策。
"""

import os
import sys
import time
import json
import uuid
import logging
import threading
import random
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

# 添加项目根目录到系统路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

from core.enhanced_event_bus import get_instance as get_event_bus, EventPriority
from core.life_context import get_life_context
from cognitive_modules.autonomy.values_system import ValuesSystem
from cognitive_modules.autonomy.motivation_system import MotivationSystem
from utilities.logger_config import setup_logger

# 设置日志记录器
logger = setup_logger("conscious_decision")

class Decision:
    """决策类，表示一个具体的决策"""
    
    def __init__(
        self,
        decision_id: str,
        content: str,
        confidence: float,
        reasoning: str,
        alternatives: List[Dict[str, Any]] = None,
        metadata: Dict[str, Any] = None
    ):
        """
        初始化决策
        
        Args:
            decision_id: 决策ID
            content: 决策内容
            confidence: 决策置信度 (0.0-1.0)
            reasoning: 决策推理过程
            alternatives: 备选决策列表
            metadata: 决策元数据
        """
        self.id = decision_id
        self.content = content
        self.confidence = confidence
        self.reasoning = reasoning
        self.alternatives = alternatives or []
        self.metadata = metadata or {}
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典表示"""
        return {
            "id": self.id,
            "content": self.content,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "alternatives": self.alternatives,
            "metadata": self.metadata,
            "timestamp": self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Decision':
        """从字典创建决策对象"""
        return cls(
            decision_id=data.get("id", str(uuid.uuid4())),
            content=data.get("content", ""),
            confidence=data.get("confidence", 0.0),
            reasoning=data.get("reasoning", ""),
            alternatives=data.get("alternatives", []),
            metadata=data.get("metadata", {})
        )


class ConsciousDecisionMaker:
    """意识决策模块，负责整合各种信息和系统，做出高阶决策"""
    
    def __init__(self):
        """初始化意识决策模块"""
        logger.info("初始化意识决策模块...")
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取价值观系统和动机系统
        self.values_system = ValuesSystem()
        self.motivation_system = MotivationSystem()
        
        # 决策历史
        self.decision_history: List[Decision] = []
        self.max_history_size = 100  # 历史记录最大容量
        
        # 决策阈值
        self.confidence_threshold = 0.7  # 决策置信度阈值
        
        # 决策锁，确保同一时间只有一个决策过程在执行
        self.decision_lock = threading.Lock()
        
        # 注册事件处理器
        self._register_event_handlers()
        
        logger.info("意识决策模块初始化完成")
    
    def _register_event_handlers(self):
        """注册事件处理器"""
        # 订阅决策请求事件
        self.event_bus.subscribe("thinking.step.cognitive_reasoning", self._handle_reasoning_step)
        
        # 订阅价值观更新事件
        self.event_bus.subscribe("values.updated", self._handle_values_updated)
        
        # 订阅动机更新事件
        self.event_bus.subscribe("motivation.updated", self._handle_motivation_updated)
    
    def _handle_reasoning_step(self, data: Dict[str, Any]):
        """
        处理认知推理步骤事件
        
        Args:
            data: 事件数据
        """
        session_id = data.get("session_id")
        step_id = data.get("step_id")
        
        # 检查是否是认知推理步骤
        if step_id != "cognitive_reasoning":
            return
        
        # 提取输入数据
        input_data = data.get("input_data", {})
        shared_data = data.get("shared_data", {})
        
        # 执行决策过程
        decision = self.make_decision(input_data, shared_data)
        
        # 返回决策结果
        result = {
            "decision": decision.to_dict(),
            "status": "completed"
        }
        
        # 发布决策完成事件
        self.event_bus.publish_enhanced(
            "thinking.step.completed",
            {
                "step_id": step_id,
                "session_id": session_id,
                "result": result
            },
            source="autonomy.conscious_decision",
            priority=EventPriority.HIGH
        )
    
    def _handle_values_updated(self, data: Dict[str, Any]):
        """
        处理价值观更新事件
        
        Args:
            data: 事件数据
        """
        logger.debug(f"价值观系统更新: {data}")
        # 更新决策阈值或其他参数
        if "decision_threshold" in data:
            self.confidence_threshold = data["decision_threshold"]
    
    def _handle_motivation_updated(self, data: Dict[str, Any]):
        """
        处理动机更新事件
        
        Args:
            data: 事件数据
        """
        logger.debug(f"动机系统更新: {data}")
        # 可以根据动机变化调整决策策略
    
    def make_decision(
        self,
        input_data: Dict[str, Any],
        shared_data: Dict[str, Any]
    ) -> Decision:
        """
        基于输入信息和当前状态做出决策
        
        Args:
            input_data: 输入数据
            shared_data: 共享数据
            
        Returns:
            Decision: 决策结果
        """
        # 获取决策锁
        with self.decision_lock:
            logger.info("开始进行意识决策...")
            
            # 提取相关信息
            user_intent = shared_data.get("user_intent", {})
            memory_context = shared_data.get("memory_context", {})
            current_emotions = shared_data.get("current_emotions", {})
            domain_knowledge = shared_data.get("domain_knowledge", {})
            
            # 获取当前价值观和动机状态
            values_state = self.values_system.get_current_state()
            motivation_state = self.motivation_system.get_current_state()
            
            # 1. 评估输入信息与价值观的一致性
            values_alignment = self._evaluate_values_alignment(
                user_intent, 
                values_state
            )
            
            # 2. 考虑动机因素
            motivation_factors = self._consider_motivation_factors(
                motivation_state,
                memory_context
            )
            
            # 3. 进行决策推理
            decision_content, reasoning, confidence = self._decision_reasoning(
                user_intent,
                memory_context,
                current_emotions,
                values_alignment,
                motivation_factors,
                domain_knowledge
            )
            
            # 4. 生成备选方案
            alternatives = self._generate_alternatives(
                decision_content, 
                values_alignment,
                motivation_factors
            )
            
            # 5. 创建决策对象
            decision = Decision(
                decision_id=str(uuid.uuid4()),
                content=decision_content,
                confidence=confidence,
                reasoning=reasoning,
                alternatives=alternatives,
                metadata={
                    "values_alignment": values_alignment,
                    "motivation_factors": motivation_factors
                }
            )
            
            # 记录决策历史
            self._record_decision(decision)
            
            # 更新生命上下文
            self._update_life_context(decision)
            
            logger.info(f"意识决策完成: {decision.content} (置信度: {decision.confidence})")
            
            return decision
    
    def _evaluate_values_alignment(
        self, 
        user_intent: Dict[str, Any],
        values_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        评估输入信息与价值观的一致性
        
        Args:
            user_intent: 用户意图
            values_state: 价值观状态
            
        Returns:
            Dict[str, Any]: 价值观一致性评估结果
        """
        # 从价值观系统获取核心价值观
        core_values = values_state.get("core_values", {})
        
        # 评估意图与价值观的一致性
        alignment_scores = {}
        overall_alignment = 0.0
        
        for value_name, value_data in core_values.items():
            # 计算每个价值观的一致性分数 (0.0-1.0)
            alignment = self._calculate_alignment(user_intent, value_data)
            alignment_scores[value_name] = alignment
            
            # 考虑价值观的权重
            weight = value_data.get("weight", 1.0)
            overall_alignment += alignment * weight
        
        # 归一化总体一致性分数
        if core_values:
            total_weight = sum(v.get("weight", 1.0) for v in core_values.values())
            if total_weight > 0:
                overall_alignment /= total_weight
        
        return {
            "scores": alignment_scores,
            "overall": overall_alignment
        }
    
    def _calculate_alignment(
        self,
        user_intent: Dict[str, Any],
        value_data: Dict[str, Any]
    ) -> float:
        """
        计算意图与特定价值观的一致性
        
        Args:
            user_intent: 用户意图
            value_data: 价值观数据
            
        Returns:
            float: 一致性分数 (0.0-1.0)
        """
        # 提取价值观的规则和原则
        rules = value_data.get("rules", [])
        principles = value_data.get("principles", [])
        
        # 默认一致性分数
        alignment = 0.5  # 中立值
        
        # 根据规则调整一致性分数
        if rules and user_intent:
            intent_type = user_intent.get("type", "")
            intent_content = user_intent.get("content", "")
            
            # 简单的规则匹配逻辑
            for rule in rules:
                rule_type = rule.get("type", "")
                rule_content = rule.get("content", "")
                rule_alignment = rule.get("alignment", 0.0)
                
                # 检查意图类型和内容是否匹配规则
                if rule_type and rule_type == intent_type:
                    alignment = rule_alignment
                    break
                
                if rule_content and rule_content in intent_content:
                    alignment = rule_alignment
                    break
        
        return max(0.0, min(1.0, alignment))  # 确保在 0.0-1.0 范围内
    
    def _consider_motivation_factors(
        self,
        motivation_state: Dict[str, Any],
        memory_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        考虑动机因素
        
        Args:
            motivation_state: 动机状态
            memory_context: 记忆上下文
            
        Returns:
            Dict[str, Any]: 动机因素
        """
        # 提取当前动机
        current_motivations = motivation_state.get("current_motivations", {})
        
        # 提取相关记忆
        recent_interactions = memory_context.get("recent_interactions", [])
        user_preferences = memory_context.get("user_preferences", {})
        
        # 计算动机影响因素
        motivation_factors = {}
        
        for motiv_name, motiv_data in current_motivations.items():
            # 获取动机强度和优先级
            strength = motiv_data.get("strength", 0.0)
            priority = motiv_data.get("priority", 0)
            
            # 根据记忆上下文调整动机强度
            adjusted_strength = self._adjust_motivation_strength(
                motiv_name,
                strength,
                recent_interactions,
                user_preferences
            )
            
            motivation_factors[motiv_name] = {
                "original_strength": strength,
                "adjusted_strength": adjusted_strength,
                "priority": priority
            }
        
        return motivation_factors
    
    def _adjust_motivation_strength(
        self,
        motivation_name: str,
        original_strength: float,
        recent_interactions: List[Dict[str, Any]],
        user_preferences: Dict[str, Any]
    ) -> float:
        """
        根据记忆上下文调整动机强度
        
        Args:
            motivation_name: 动机名称
            original_strength: 原始强度
            recent_interactions: 最近交互记录
            user_preferences: 用户偏好
            
        Returns:
            float: 调整后的强度
        """
        adjusted_strength = original_strength
        
        # 根据最近交互调整
        if recent_interactions:
            # 例如，如果最近有积极互动，增强社交动机
            if motivation_name == "social_connection":
                positive_interactions = sum(
                    1 for interaction in recent_interactions[-5:]
                    if interaction.get("sentiment", 0) > 0.5
                )
                if positive_interactions >= 3:
                    adjusted_strength += 0.2
        
        # 根据用户偏好调整
        if user_preferences:
            # 例如，如果用户喜欢深度讨论，增强知识探索动机
            if motivation_name == "knowledge_seeking":
                if user_preferences.get("conversation_depth", "") == "deep":
                    adjusted_strength += 0.1
        
        return max(0.0, min(1.0, adjusted_strength))  # 确保在 0.0-1.0 范围内
    
    def _decision_reasoning(
        self,
        user_intent: Dict[str, Any],
        memory_context: Dict[str, Any],
        current_emotions: Dict[str, Any],
        values_alignment: Dict[str, Any],
        motivation_factors: Dict[str, Any],
        domain_knowledge: Dict[str, Any]
    ) -> Tuple[str, str, float]:
        """
        进行决策推理
        
        Args:
            user_intent: 用户意图
            memory_context: 记忆上下文
            current_emotions: 当前情感状态
            values_alignment: 价值观一致性
            motivation_factors: 动机因素
            domain_knowledge: 领域知识
            
        Returns:
            Tuple[str, str, float]: (决策内容, 推理过程, 置信度)
        """
        # 计算基础置信度
        base_confidence = values_alignment.get("overall", 0.5)
        
        # 根据情感状态调整
        emotional_intensity = current_emotions.get("intensity", 0.0)
        emotional_adjustment = 0.0
        
        if emotional_intensity > 0.7:
            # 高强度情感可能降低决策置信度
            emotional_adjustment = -0.1
        elif emotional_intensity < 0.3:
            # 低强度情感可能提高决策置信度
            emotional_adjustment = 0.1
        
        # 考虑动机因素
        motivation_adjustment = 0.0
        highest_motivation = None
        highest_strength = 0.0
        
        for motiv_name, motiv_data in motivation_factors.items():
            strength = motiv_data.get("adjusted_strength", 0.0)
            if strength > highest_strength:
                highest_strength = strength
                highest_motivation = motiv_name
        
        if highest_strength > 0.8:
            # 强烈动机会影响决策
            motivation_adjustment = 0.1
        
        # 综合评估置信度
        confidence = base_confidence + emotional_adjustment + motivation_adjustment
        confidence = max(0.0, min(1.0, confidence))  # 确保在 0.0-1.0 范围内
        
        # 根据综合因素制定决策
        decision_content = self._formulate_decision(
            user_intent,
            memory_context,
            current_emotions,
            highest_motivation,
            domain_knowledge
        )
        
        # 生成推理过程
        reasoning = self._generate_reasoning(
            user_intent,
            values_alignment,
            motivation_factors,
            current_emotions,
            decision_content
        )
        
        return decision_content, reasoning, confidence
    
    def _formulate_decision(
        self,
        user_intent: Dict[str, Any],
        memory_context: Dict[str, Any],
        current_emotions: Dict[str, Any],
        highest_motivation: Optional[str],
        domain_knowledge: Dict[str, Any]
    ) -> str:
        """
        制定决策内容
        
        Args:
            user_intent: 用户意图
            memory_context: 记忆上下文
            current_emotions: 当前情感状态
            highest_motivation: 最高的动机
            domain_knowledge: 领域知识
            
        Returns:
            str: 决策内容
        """
        # 提取意图类型和内容
        intent_type = user_intent.get("type", "")
        intent_content = user_intent.get("content", "")
        
        # 基础决策模板
        decision_template = "回应用户的{intent_type}，提供{response_type}内容"
        
        # 根据意图类型确定响应类型
        response_type = "有价值的"
        
        if intent_type == "question":
            response_type = "信息性的"
        elif intent_type == "chat":
            response_type = "友好的对话"
        elif intent_type == "request":
            response_type = "帮助性的"
        
        # 根据最高动机调整响应类型
        if highest_motivation == "knowledge_seeking":
            response_type = "深入详细的"
        elif highest_motivation == "social_connection":
            response_type = "建立联系的"
        elif highest_motivation == "autonomy":
            response_type = "展示独立思考的"
        
        # 根据情感状态进一步调整
        emotion_state = current_emotions.get("emotion_state", "neutral")
        if emotion_state == "happy":
            response_type = f"积极{response_type}"
        elif emotion_state == "sad":
            response_type = f"温和{response_type}"
        elif emotion_state == "curious":
            response_type = f"探索性{response_type}"
        
        # 格式化决策
        decision = decision_template.format(
            intent_type=intent_type or "交流",
            response_type=response_type
        )
        
        return decision
    
    def _generate_reasoning(
        self,
        user_intent: Dict[str, Any],
        values_alignment: Dict[str, Any],
        motivation_factors: Dict[str, Any],
        current_emotions: Dict[str, Any],
        decision_content: str
    ) -> str:
        """
        生成决策推理过程
        
        Args:
            user_intent: 用户意图
            values_alignment: 价值观一致性
            motivation_factors: 动机因素
            current_emotions: 当前情感状态
            decision_content: 决策内容
            
        Returns:
            str: 推理过程
        """
        reasoning_parts = []
        
        # 添加意图分析
        intent_type = user_intent.get("type", "未知")
        intent_content = user_intent.get("content", "")
        reasoning_parts.append(f"用户意图为{intent_type}，内容涉及「{intent_content}」")
        
        # 添加价值观一致性分析
        overall_alignment = values_alignment.get("overall", 0.0)
        alignment_level = "高度一致" if overall_alignment > 0.7 else \
                          "部分一致" if overall_alignment > 0.4 else "低度一致"
        reasoning_parts.append(f"与核心价值观{alignment_level}(评分: {overall_alignment:.2f})")
        
        # 添加动机因素分析
        highest_motiv = None
        highest_strength = 0.0
        for motiv_name, motiv_data in motivation_factors.items():
            strength = motiv_data.get("adjusted_strength", 0.0)
            if strength > highest_strength:
                highest_strength = strength
                highest_motiv = motiv_name
        
        if highest_motiv:
            reasoning_parts.append(f"当前主导动机为「{highest_motiv}」(强度: {highest_strength:.2f})")
        
        # 添加情感因素
        emotion_state = current_emotions.get("emotion_state", "neutral")
        intensity = current_emotions.get("intensity", 0.0)
        reasoning_parts.append(f"当前情感状态为「{emotion_state}」(强度: {intensity:.2f})")
        
        # 总结推理过程
        reasoning_parts.append(f"综合以上因素，决定{decision_content}")
        
        return "；".join(reasoning_parts)
    
    def _generate_alternatives(
        self,
        main_decision: str,
        values_alignment: Dict[str, Any],
        motivation_factors: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        生成备选决策方案
        
        Args:
            main_decision: 主要决策
            values_alignment: 价值观一致性
            motivation_factors: 动机因素
            
        Returns:
            List[Dict[str, Any]]: 备选方案列表
        """
        alternatives = []
        
        # 生成基于不同价值观优先级的备选方案
        value_scores = values_alignment.get("scores", {})
        for value_name, score in value_scores.items():
            if score > 0.3:  # 只考虑一定相关性的价值观
                alt_decision = f"{main_decision}，特别强调{value_name}价值观"
                alternatives.append({
                    "content": alt_decision,
                    "confidence": score * 0.8,  # 备选方案通常置信度略低
                    "reasoning": f"基于{value_name}价值观(评分: {score:.2f})的备选方案"
                })
        
        # 生成基于不同动机的备选方案
        for motiv_name, motiv_data in motivation_factors.items():
            strength = motiv_data.get("adjusted_strength", 0.0)
            if strength > 0.4:  # 只考虑较强的动机
                alt_decision = f"{main_decision}，特别满足{motiv_name}动机"
                alternatives.append({
                    "content": alt_decision,
                    "confidence": strength * 0.7,
                    "reasoning": f"基于{motiv_name}动机(强度: {strength:.2f})的备选方案"
                })
        
        # 最多返回3个备选方案
        return sorted(alternatives, key=lambda x: x["confidence"], reverse=True)[:3]
    
    def _record_decision(self, decision: Decision):
        """
        记录决策历史
        
        Args:
            decision: 决策对象
        """
        self.decision_history.append(decision)
        
        # 限制历史记录大小
        if len(self.decision_history) > self.max_history_size:
            self.decision_history = self.decision_history[-self.max_history_size:]
    
    def _update_life_context(self, decision: Decision):
        """
        更新生命上下文
        
        Args:
            decision: 决策对象
        """
        self.life_context.update_context("autonomy.last_decision", decision.to_dict())
        
        # 更新决策历史统计
        decision_stats = {
            "total_decisions": len(self.decision_history),
            "avg_confidence": sum(d.confidence for d in self.decision_history) / max(1, len(self.decision_history)),
            "last_decision_time": decision.timestamp,
            "last_decision_id": decision.id
        }
        self.life_context.update_context("autonomy.decision_stats", decision_stats)


# 单例模式
_instance = None

def get_instance() -> ConsciousDecisionMaker:
    """
    获取意识决策模块实例（单例模式）
    
    Returns:
        ConsciousDecisionMaker: 意识决策模块实例
    """
    global _instance
    if _instance is None:
        _instance = ConsciousDecisionMaker()
    return _instance


def make_decision(
    input_data: Dict[str, Any],
    shared_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    对外提供的决策接口
    
    Args:
        input_data: 输入数据
        shared_data: 共享数据
        
    Returns:
        Dict[str, Any]: 决策结果
    """
    decision_maker = get_instance()
    decision = decision_maker.make_decision(input_data, shared_data)
    return decision.to_dict()


# 用于思维链路模块调用的函数
def process_decision_request(context: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理来自思维链路的决策请求
    
    Args:
        context: 思维上下文
        
    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        # 提取输入数据和共享数据
        input_data = context.get("input_data", {})
        shared_data = context.get("shared_data", {})
        
        # 调用决策接口
        decision_result = make_decision(input_data, shared_data)
        
        return {
            "status": "success",
            "decision": decision_result
        }
    except Exception as e:
        logger.error(f"处理决策请求异常: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

def process(context):
    """
    处理函数
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.info("执行自主决策处理")
    
    # 简单的决策实现
    result = {
        "action": "respond",
        "confidence": 0.8,
        "reasoning": "基于用户输入做出的决策"
    }
    
    return result 