#!/usr/bin/env python3
"""
多模态表达模块 - Multimodal Expression

该模块负责生成和表达多种模态的内容，包括：
1. 图像生成 - 创建视觉内容
2. 语音合成 - 生成语音内容
3. 表情生成 - 生成表情和情感表达
4. 模态融合 - 整合多种模态的表达
5. 风格适应 - 根据上下文调整表达风格

作者: Claude
创建日期: 2024-08-01
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, ByteString

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
behavior_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(behavior_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from adapters.ai_service_adapter import get_instance as get_ai_service
from cognitive_modules.perception.multimodal_perception import get_instance as get_multimodal_perception

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.behavior.multimodal_expression")

class MultimodalExpression(CognitiveModuleBase):
    """
    多模态表达模块
    
    生成和表达多种模态的内容，实现丰富的交互体验。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化多模态表达模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "multimodal_expression", "behavior", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取AI服务适配器
        self.ai_service = get_ai_service()
        
        # 获取多模态感知模块
        self.multimodal_perception = get_multimodal_perception()
        
        # 表达模式
        self.expression_modes = {
            "text": self._generate_text_expression,
            "image": self._generate_image_expression,
            "audio": self._generate_audio_expression,
            "emoji": self._generate_emoji_expression,
            "mixed": self._generate_mixed_expression
        }
        
        # 表情库
        self.emoji_library = {
            "happiness": ["😊", "😄", "😁", "😆", "😍", "🥰", "😘"],
            "sadness": ["😔", "😢", "😭", "😞", "😥", "😓", "🥺"],
            "anger": ["😠", "😡", "😤", "😒", "😑", "🙄", "😾"],
            "surprise": ["😮", "😲", "😱", "😯", "😳", "🤯", "😵"],
            "fear": ["😨", "😰", "😱", "😖", "😫", "🥶", "😬"],
            "disgust": ["🤢", "🤮", "😖", "😫", "😣", "😩", "😒"],
            "neutral": ["😐", "😶", "😑", "😕", "🤨", "😬", "🙂"],
            "love": ["❤️", "💕", "💖", "💗", "💓", "💘", "💝"],
            "approval": ["👍", "👌", "✅", "✓", "🆗", "🙆", "💯"],
            "disapproval": ["👎", "❌", "❎", "🙅", "🚫", "⛔", "🛑"],
            "thinking": ["🤔", "💭", "🧠", "🤓", "🧐", "💡", "❓"],
            "action": ["🏃", "💪", "👋", "✋", "👏", "👊", "👈"]
        }
        
        # 表达历史
        self.expression_history = []
        
        # 表达计数
        self.expression_counts = {mode: 0 for mode in self.expression_modes}
        
        # 表达偏好
        self.expression_preferences = {
            "default": {
                "text_style": "friendly",
                "emoji_frequency": "medium",
                "image_style": "natural",
                "voice_type": "female"
            }
        }
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"多模态表达模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("response_generated", self._on_response_generated)
        self.event_bus.subscribe("express_emotion", self._on_express_emotion)
        self.event_bus.subscribe("express_multimodal", self._on_express_multimodal)
        
        logger.debug("已订阅相关事件")
    
    def _on_response_generated(self, data: Dict[str, Any]):
        """处理响应生成事件"""
        try:
            response = data.get("response", "")
            user_id = data.get("user_id", "")
            session_id = data.get("session_id", "")
            
            if not response or not user_id:
                return
            
            # 处理响应中的表达指令
            # 例如 [image: a beautiful sunset] 或 [emoji: happiness]
            enhanced_response = self._enhance_response_with_expressions(response, user_id, session_id)
            
            # 如果响应有变化，发布增强响应事件
            if enhanced_response != response:
                self.event_bus.publish("response_enhanced", {
                    "original_response": response,
                    "enhanced_response": enhanced_response,
                    "user_id": user_id,
                    "session_id": session_id,
                    "timestamp": time.time()
                })
        except Exception as e:
            logger.error_status(f"处理响应生成事件失败: {e}")
    
    def _on_express_emotion(self, data: Dict[str, Any]):
        """处理情感表达事件"""
        try:
            emotion = data.get("emotion", "")
            intensity = data.get("intensity", 0.5)
            user_id = data.get("user_id", "")
            
            if not emotion or not user_id:
                return
            
            # 生成情感表达
            emoji_expression = self._generate_emoji_for_emotion(emotion, intensity)
            
            # 发布表情表达事件
            self.event_bus.publish("emoji_expressed", {
                "emotion": emotion,
                "intensity": intensity,
                "emoji": emoji_expression,
                "user_id": user_id,
                "timestamp": time.time()
            })
        except Exception as e:
            logger.error_status(f"处理情感表达事件失败: {e}")
    
    def _on_express_multimodal(self, data: Dict[str, Any]):
        """处理多模态表达事件"""
        try:
            mode = data.get("mode", "")
            content = data.get("content", "")
            user_id = data.get("user_id", "")
            session_id = data.get("session_id", "")
            parameters = data.get("parameters", {})
            
            if not mode or not content or not user_id:
                return
            
            # 生成多模态表达
            if mode in self.expression_modes:
                result = self.expression_modes[mode](content, user_id, session_id, parameters)
                
                # 更新表达计数
                self.expression_counts[mode] += 1
                
                # 记录表达历史
                self.expression_history.append({
                    "mode": mode,
                    "user_id": user_id,
                    "content": content[:50] + "..." if len(content) > 50 else content,
                    "timestamp": time.time(),
                    "success": result.get("success", False)
                })
                
                # 如果表达成功，发布表达结果事件
                if result.get("success", False):
                    self.event_bus.publish(f"{mode}_expressed", {
                        "user_id": user_id,
                        "session_id": session_id,
                        "result": result,
                        "timestamp": time.time()
                    })
        except Exception as e:
            logger.error_status(f"处理多模态表达事件失败: {e}")
    
    def _enhance_response_with_expressions(self, response: str, user_id: str, session_id: str) -> str:
        """
        增强响应，处理特殊表达指令
        
        Args:
            response: 原始响应文本
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            增强后的响应
        """
        try:
            import re
            
            # 处理图像生成指令 [image: prompt]
            image_pattern = r'\[image:\s*(.*?)\]'
            image_matches = re.findall(image_pattern, response)
            
            for prompt in image_matches:
                # 生成图像
                image_result = self._generate_image_expression(prompt, user_id, session_id)
                
                if image_result.get("success", False) and "url" in image_result:
                    # 替换图像指令为图像URL
                    image_url = image_result["url"]
                    response = response.replace(f"[image: {prompt}]", f"[图片: {image_url}]")
            
            # 处理表情指令 [emoji: emotion]
            emoji_pattern = r'\[emoji:\s*(.*?)\]'
            emoji_matches = re.findall(emoji_pattern, response)
            
            for emotion in emoji_matches:
                # 生成表情
                emoji = self._generate_emoji_for_emotion(emotion, 0.8)
                
                # 替换表情指令为表情符号
                response = response.replace(f"[emoji: {emotion}]", emoji)
            
            # 处理语音指令 [audio: text]
            audio_pattern = r'\[audio:\s*(.*?)\]'
            audio_matches = re.findall(audio_pattern, response)
            
            for text in audio_matches:
                # 生成语音
                audio_result = self._generate_audio_expression(text, user_id, session_id)
                
                if audio_result.get("success", False) and "audio_url" in audio_result:
                    # 替换语音指令为语音URL
                    audio_url = audio_result["audio_url"]
                    response = response.replace(f"[audio: {text}]", f"[语音: {audio_url}]")
            
            return response
        except Exception as e:
            logger.error_status(f"增强响应失败: {e}")
            return response
    
    def _generate_text_expression(self, text: str, user_id: str, session_id: str = "", parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成文本表达
        
        Args:
            text: 文本内容
            user_id: 用户ID
            session_id: 会话ID
            parameters: 附加参数
            
        Returns:
            文本表达结果
        """
        try:
            parameters = parameters or {}
            
            # 获取文本风格
            style = parameters.get("style", self._get_user_preference(user_id, "text_style"))
            
            # 根据风格调整文本（这里简化实现）
            if style == "formal":
                # 更正式的表达
                enhanced_text = text
            elif style == "friendly":
                # 更友好的表达
                enhanced_text = text
            elif style == "concise":
                # 更简洁的表达
                enhanced_text = text
            else:
                enhanced_text = text
            
            return {
                "success": True,
                "text": enhanced_text,
                "original_text": text,
                "style": style,
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error_status(f"生成文本表达失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_image_expression(self, prompt: str, user_id: str, session_id: str = "", parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成图像表达
        
        Args:
            prompt: 图像提示
            user_id: 用户ID
            session_id: 会话ID
            parameters: 附加参数
            
        Returns:
            图像表达结果
        """
        try:
            parameters = parameters or {}
            
            # 获取图像风格
            style = parameters.get("style", self._get_user_preference(user_id, "image_style"))
            size = parameters.get("size", "1024x1024")
            
            # 使用多模态感知模块的方法生成图像
            return self.multimodal_perception.generate_multimodal_content(
                "image",
                prompt,
                user_id,
                {
                    "style": style,
                    "size": size
                }
            )
        except Exception as e:
            logger.error_status(f"生成图像表达失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_audio_expression(self, text: str, user_id: str, session_id: str = "", parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成语音表达
        
        Args:
            text: 文本内容
            user_id: 用户ID
            session_id: 会话ID
            parameters: 附加参数
            
        Returns:
            语音表达结果
        """
        try:
            parameters = parameters or {}
            
            # 获取语音类型
            voice_type = parameters.get("voice_type", self._get_user_preference(user_id, "voice_type"))
            
            # 使用多模态感知模块的方法生成语音
            return self.multimodal_perception.generate_multimodal_content(
                "audio",
                text,
                user_id,
                {
                    "voice": voice_type
                }
            )
        except Exception as e:
            logger.error_status(f"生成语音表达失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_emoji_expression(self, prompt: str, user_id: str, session_id: str = "", parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成表情表达
        
        Args:
            prompt: 表情提示（情感类型）
            user_id: 用户ID
            session_id: 会话ID
            parameters: 附加参数
            
        Returns:
            表情表达结果
        """
        try:
            parameters = parameters or {}
            
            # 获取强度
            intensity = parameters.get("intensity", 0.8)
            
            # 生成表情
            emoji = self._generate_emoji_for_emotion(prompt, intensity)
            
            return {
                "success": True,
                "emoji": emoji,
                "emotion": prompt,
                "intensity": intensity,
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error_status(f"生成表情表达失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_mixed_expression(self, content: Dict[str, Any], user_id: str, session_id: str = "", parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成混合表达（多模态组合）
        
        Args:
            content: 包含不同模态内容的字典
            user_id: 用户ID
            session_id: 会话ID
            parameters: 附加参数
            
        Returns:
            混合表达结果
        """
        try:
            parameters = parameters or {}
            results = {}
            
            # 处理各个模态的内容
            if "text" in content:
                results["text"] = self._generate_text_expression(
                    content["text"],
                    user_id,
                    session_id,
                    parameters.get("text_params")
                )
            
            if "image" in content:
                results["image"] = self._generate_image_expression(
                    content["image"],
                    user_id,
                    session_id,
                    parameters.get("image_params")
                )
            
            if "audio" in content:
                results["audio"] = self._generate_audio_expression(
                    content["audio"],
                    user_id,
                    session_id,
                    parameters.get("audio_params")
                )
            
            if "emoji" in content:
                results["emoji"] = self._generate_emoji_expression(
                    content["emoji"],
                    user_id,
                    session_id,
                    parameters.get("emoji_params")
                )
            
            # 判断是否成功
            success = any(result.get("success", False) for result in results.values())
            
            return {
                "success": success,
                "results": results,
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error_status(f"生成混合表达失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_emoji_for_emotion(self, emotion: str, intensity: float = 0.5) -> str:
        """
        根据情感生成表情符号
        
        Args:
            emotion: 情感类型
            intensity: 情感强度（0-1）
            
        Returns:
            表情符号
        """
        try:
            # 规范化情感类型
            emotion = emotion.lower().strip()
            
            # 查找匹配的情感类别
            matched_category = None
            for category in self.emoji_library:
                if emotion in category or category in emotion:
                    matched_category = category
                    break
            
            # 如果没有匹配，使用中性表情
            if not matched_category:
                matched_category = "neutral"
            
            # 根据强度选择表情
            emojis = self.emoji_library[matched_category]
            
            if intensity < 0.3:
                # 低强度，选择靠前的表情
                emoji_index = 0
            elif intensity < 0.7:
                # 中等强度，选择中间的表情
                emoji_index = len(emojis) // 2
            else:
                # 高强度，选择靠后的表情
                emoji_index = len(emojis) - 1
            
            return emojis[min(emoji_index, len(emojis) - 1)]
        except Exception as e:
            logger.error_status(f"生成表情符号失败: {e}")
            return "😐"  # 默认返回中性表情
    
    def _get_user_preference(self, user_id: str, preference_key: str) -> Any:
        """
        获取用户表达偏好
        
        Args:
            user_id: 用户ID
            preference_key: 偏好键名
            
        Returns:
            偏好值
        """
        try:
            # 获取用户上下文
            user_context = self.life_context.get_user_context(user_id)
            
            # 查找用户表达偏好
            expression_prefs = user_context.get("expression_preferences")
            
            if expression_prefs and preference_key in expression_prefs:
                return expression_prefs[preference_key]
            
            # 如果没有用户特定偏好，使用默认偏好
            return self.expression_preferences["default"].get(preference_key)
        except Exception as e:
            logger.error_status(f"获取用户偏好失败: {e}")
            return self.expression_preferences["default"].get(preference_key)
    
    def set_user_preference(self, user_id: str, preference_key: str, preference_value: Any) -> bool:
        """
        设置用户表达偏好
        
        Args:
            user_id: 用户ID
            preference_key: 偏好键名
            preference_value: 偏好值
            
        Returns:
            设置是否成功
        """
        try:
            # 获取用户上下文
            user_context = self.life_context.get_user_context(user_id)
            
            # 获取或创建表达偏好
            expression_prefs = user_context.get("expression_preferences", {})
            
            # 更新偏好
            expression_prefs[preference_key] = preference_value
            
            # 更新用户上下文
            self.life_context.update_user_context(user_id, "expression_preferences", expression_prefs)
            
            return True
        except Exception as e:
            logger.error_status(f"设置用户偏好失败: {e}")
            return False
    
    def express_emotion(self, emotion: str, intensity: float, user_id: str) -> Dict[str, Any]:
        """
        表达情感
        
        Args:
            emotion: 情感类型
            intensity: 情感强度（0-1）
            user_id: 用户ID
            
        Returns:
            表达结果
        """
        try:
            # 生成情感表达
            emoji = self._generate_emoji_for_emotion(emotion, intensity)
            
            # 发布情感表达事件
            self.event_bus.publish("emoji_expressed", {
                "emotion": emotion,
                "intensity": intensity,
                "emoji": emoji,
                "user_id": user_id,
                "timestamp": time.time()
            })
            
            return {
                "success": True,
                "emoji": emoji,
                "emotion": emotion,
                "intensity": intensity
            }
        except Exception as e:
            logger.error_status(f"表达情感失败: {e}")
            return {"success": False, "error": str(e)}
    
    def enhance_response(self, response: str, user_id: str, session_id: str = "") -> str:
        """
        增强响应，添加表情和多模态内容
        
        Args:
            response: 原始响应文本
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            增强后的响应
        """
        try:
            # 获取表情频率偏好
            emoji_frequency = self._get_user_preference(user_id, "emoji_frequency")
            
            # 处理特殊表达指令
            enhanced_response = self._enhance_response_with_expressions(response, user_id, session_id)
            
            # 根据表情频率添加表情
            if emoji_frequency != "none":
                # 获取当前情感状态
                context = self.life_context.get_context()
                current_emotion = context.get("current_state", {}).get("emotion", "neutral")
                
                # 生成对应的表情
                emoji = self._generate_emoji_for_emotion(current_emotion, 0.7)
                
                # 根据表情频率决定是否添加表情
                if emoji_frequency == "high" or (emoji_frequency == "medium" and len(response) > 50):
                    # 在句子末尾添加表情
                    import re
                    sentences = re.split(r'([.!?。！？])', enhanced_response)
                    
                    if len(sentences) >= 2:
                        # 选择一个合适的位置添加表情
                        position = min(len(sentences) // 2 * 2, len(sentences) - 2)
                        sentences[position] += f" {emoji} "
                        enhanced_response = "".join(sentences)
                    else:
                        # 如果没有分段，直接在末尾添加
                        enhanced_response = f"{enhanced_response} {emoji}"
            
            return enhanced_response
        except Exception as e:
            logger.error_status(f"增强响应失败: {e}")
            return response
    
    def get_expression_stats(self) -> Dict[str, Any]:
        """
        获取表达统计
        
        Returns:
            表达统计数据
        """
        return {
            "expression_counts": self.expression_counts,
            "total_expressed": sum(self.expression_counts.values()),
            "history_count": len(self.expression_history)
        }
    
    def shutdown(self):
        """关闭模块"""
        logger.info(f"多模态表达模块 {self.module_id} 已关闭")
        return True


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> MultimodalExpression:
    """
    获取多模态表达模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        多模态表达模块实例
    """
    return MultimodalExpression(config=config) 