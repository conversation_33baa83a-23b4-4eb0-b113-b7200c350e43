#!/usr/bin/env python3
"""
创造性内容生成模块 - Creative Content Generator

该模块负责生成各种创造性内容，包括：
1. 故事生成 - 创建结构化故事和叙事
2. 诗歌创作 - 生成不同风格的诗歌
3. 概念艺术 - 创建概念性描述和艺术表达
4. 创意对话 - 生成富有创意的对话和交流
5. 创新思维表达 - 将创新思维转化为具体表达

作者: Claude
创建日期: 2024-08-01
版本: 1.0
"""

import os
import sys
import json
import time
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, Set
from collections import defaultdict

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
behavior_dir = os.path.dirname(current_dir)
modules_dir = os.path.dirname(behavior_dir)
root_dir = os.path.dirname(modules_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from adapters.ai_service_adapter import get_instance as get_ai_service
from cognitive_modules.cognition.creative_thinking import get_instance as get_creative_thinking

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.behavior.creative_content_generator")

class CreativeContentGenerator(CognitiveModuleBase):
    """
    创造性内容生成模块
    
    生成各种类型的创造性内容，包括故事、诗歌、概念艺术等。
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, module_id: str = None, config: Dict[str, Any] = None):
        """
        初始化创造性内容生成模块
        
        Args:
            module_id: 模块ID
            config: 配置信息
        """
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        super().__init__(module_id or "creative_content", "behavior", config or {})
        
        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 获取AI服务适配器
        self.ai_service = get_ai_service()
        
        # 获取创造性思维模块
        self.creative_thinking = get_creative_thinking()
        
        # 创造性内容生成配置
        self.content_config = {
            "max_length": {
                "story": 2000,
                "poem": 500,
                "concept_art": 800,
                "dialogue": 1000,
                "expression": 500
            },
            "complexity_levels": {
                "simple": 0.3,
                "moderate": 0.6,
                "complex": 0.9
            },
            "creativity_factors": {
                "novelty": 0.7,
                "coherence": 0.6,
                "relevance": 0.8,
                "emotional_impact": 0.7,
                "surprise": 0.5
            }
        }
        
        # 内容风格库
        self.content_styles = {
            "故事风格": [
                "童话", "科幻", "奇幻", "悬疑", "冒险", 
                "历史", "爱情", "哲理", "寓言", "日常"
            ],
            "诗歌风格": [
                "古典", "现代", "浪漫", "哲理", "自由诗",
                "格律诗", "叙事诗", "抒情诗", "象征派", "意象派"
            ],
            "艺术风格": [
                "现实主义", "抽象主义", "印象派", "超现实主义", "极简主义",
                "表现主义", "浪漫主义", "立体主义", "波普艺术", "未来主义"
            ],
            "对话风格": [
                "幽默", "哲学", "辩论", "教学", "咨询",
                "情感", "日常", "专业", "创意", "反思"
            ],
            "表达风格": [
                "直接", "隐喻", "类比", "夸张", "反讽",
                "简洁", "详细", "诗意", "技术性", "感性"
            ]
        }
        
        # 内容模板库
        self.content_templates = {
            "故事结构": {
                "英雄之旅": [
                    "平凡世界", "冒险召唤", "拒绝召唤", "遇见导师", 
                    "跨越门槛", "考验/盟友/敌人", "接近洞穴", 
                    "严峻考验", "获得奖励", "回归之路", "复活", "带着长生药回归"
                ],
                "三幕剧": [
                    "设置", "对抗", "解决"
                ],
                "五幕结构": [
                    "引子", "上升动作", "高潮", "下降动作", "结局"
                ],
                "问题解决": [
                    "问题提出", "尝试解决", "失败", "新方法", "成功"
                ],
                "内部旅程": [
                    "现状", "欲望出现", "挣扎", "调整", "转变", "新认识"
                ]
            },
            "诗歌结构": {
                "十四行诗": "十四行，通常分为八行和六行两部分，有固定的韵律模式",
                "俳句": "三行，通常遵循5-7-5的音节模式，关注自然和季节",
                "自由诗": "没有固定结构或韵律的诗歌形式，强调表达自由",
                "叙事诗": "讲述故事的诗歌形式，有明确的情节发展",
                "抒情诗": "表达个人情感和感受的诗歌形式"
            },
            "概念框架": {
                "对比框架": "通过对比两个不同概念突出特点",
                "发展框架": "展示概念从一种状态到另一种状态的发展",
                "抽象具象": "将抽象概念通过具体形象表达",
                "系统框架": "将概念作为系统的一部分进行描述",
                "问题解决": "围绕概念提出问题并给出解决方案"
            }
        }
        
        # 创造性内容生成历史
        self.content_history = []
        self.max_history_size = 50
        
        # 内容生成评估指标
        self.evaluation_metrics = {
            "创新性": self._evaluate_innovation,
            "连贯性": self._evaluate_coherence,
            "相关性": self._evaluate_relevance,
            "情感影响": self._evaluate_emotional_impact,
            "复杂性": self._evaluate_complexity
        }
        
        # 订阅相关事件
        self._subscribe_events()
        
        logger.info(f"创造性内容生成模块 {self.module_id} 已创建")
    
    def _subscribe_events(self):
        """订阅相关事件"""
        self.event_bus.subscribe("request_creative_content", self._on_request_creative_content)
        self.event_bus.subscribe("creative_thinking_completed", self._on_creative_thinking_completed)
        
        logger.debug("已订阅相关事件")
    
    def _on_request_creative_content(self, data: Dict[str, Any]):
        """
        处理创造性内容请求事件
        
        Args:
            data: 事件数据
        """
        try:
            content_type = data.get("content_type")
            topic = data.get("topic")
            params = data.get("params", {})
            
            if not content_type or not topic:
                logger.warning_status("请求创造性内容缺少必要参数")
                return
            
            # 生成内容
            result = self.generate_content(content_type, topic, **params)
            
            # 发布内容生成完成事件
            self.event_bus.publish("creative_content_generated", {
                "content_type": content_type,
                "topic": topic,
                "content": result.get("content"),
                "metadata": result.get("metadata", {}),
                "timestamp": time.time()
            })
            
            logger.info(f"已生成创造性内容: {content_type}, 主题: {topic}")
        except Exception as e:
            logger.error_status(f"处理创造性内容请求失败: {e}")
    
    def _on_creative_thinking_completed(self, data: Dict[str, Any]):
        """
        处理创造性思维完成事件
        
        Args:
            data: 事件数据
        """
        try:
            thinking_result = data.get("result", {})
            thinking_type = data.get("thinking_type")
            
            if not thinking_result or not thinking_type:
                return
            
            # 将创造性思维结果转化为内容
            content_type = self._map_thinking_to_content_type(thinking_type)
            topic = thinking_result.get("topic", "创造性思考")
            
            if content_type:
                # 生成内容
                result = self.generate_content(content_type, topic, 
                                             thinking_result=thinking_result)
                
                # 发布内容生成完成事件
                self.event_bus.publish("creative_content_generated", {
                    "content_type": content_type,
                    "topic": topic,
                    "content": result.get("content"),
                    "metadata": result.get("metadata", {}),
                    "thinking_type": thinking_type,
                    "timestamp": time.time()
                })
                
                logger.info(f"基于创造性思维生成内容: {content_type}, 主题: {topic}")
        except Exception as e:
            logger.error_status(f"处理创造性思维完成事件失败: {e}")
    
    def _map_thinking_to_content_type(self, thinking_type: str) -> str:
        """
        将创造性思维类型映射到内容类型
        
        Args:
            thinking_type: 创造性思维类型
            
        Returns:
            对应的内容类型
        """
        mapping = {
            "metaphor": "poem",
            "concept_blend": "concept_art",
            "divergent_thinking": "expression",
            "problem_solving": "dialogue",
            "analogical_reasoning": "story"
        }
        
        return mapping.get(thinking_type, "expression")
    
    def generate_content(self, content_type: str, topic: str, **kwargs) -> Dict[str, Any]:
        """
        生成创造性内容
        
        Args:
            content_type: 内容类型，如"story", "poem", "concept_art", "dialogue", "expression"
            topic: 内容主题
            **kwargs: 其他参数
            
        Returns:
            生成的内容及元数据
        """
        try:
            # 获取生成函数
            generator_map = {
                "story": self.generate_story,
                "poem": self.generate_poem,
                "concept_art": self.generate_concept_art,
                "dialogue": self.generate_creative_dialogue,
                "expression": self.generate_creative_expression
            }
            
            generator = generator_map.get(content_type)
            
            if not generator:
                logger.warning_status(f"未知的内容类型: {content_type}")
                return {"content": "", "metadata": {"error": "未知的内容类型"}}
            
            # 生成内容
            result = generator(topic, **kwargs)
            
            # 添加到历史记录
            self._add_to_history(content_type, topic, result)
            
            return result
        except Exception as e:
            logger.error_status(f"生成创造性内容失败: {e}")
            return {"content": "", "metadata": {"error": str(e)}}
    
    def generate_story(self, topic: str, style: str = None, structure: str = None, 
                     length: str = "moderate", characters: List[str] = None,
                     thinking_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成故事内容
        
        Args:
            topic: 故事主题
            style: 故事风格
            structure: 故事结构
            length: 长度级别
            characters: 角色列表
            thinking_result: 创造性思维结果
            
        Returns:
            生成的故事及元数据
        """
        try:
            # 确定故事风格
            if not style:
                style = random.choice(self.content_styles["故事风格"])
            
            # 确定故事结构
            if not structure:
                structure = random.choice(list(self.content_templates["故事结构"].keys()))
            
            structure_elements = self.content_templates["故事结构"].get(structure, [])
            
            # 确定故事长度
            length_factor = self.content_config["complexity_levels"].get(length, 0.6)
            max_story_length = int(self.content_config["max_length"]["story"] * length_factor)
            
            # 确定角色
            if not characters or len(characters) == 0:
                characters = self._generate_characters(topic, style, 2 + int(length_factor * 3))
            
            # 准备创建故事的提示信息
            story_elements = {
                "主题": topic,
                "风格": style,
                "结构": structure,
                "结构元素": structure_elements,
                "角色": characters,
                "情感基调": self._determine_emotional_tone(style, topic)
            }
            
            # 如果有创造性思维结果，融合到故事元素中
            if thinking_result:
                story_elements.update({
                    "创意亮点": thinking_result.get("content", ""),
                    "关联概念": thinking_result.get("associated_concepts", []),
                    "思维方式": thinking_result.get("thinking_type", "")
                })
            
            # 创建故事提示词
            prompt = self._create_story_prompt(story_elements, max_story_length)
            
            # 使用AI服务生成故事
            story_response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位极具创造力的故事创作者，能够根据提供的元素创作出引人入胜的故事。"},
                {"role": "user", "content": prompt}
            ])
            
            # 提取故事内容
            story_content = story_response.get("content", "")
            
            # 准备元数据
            metadata = {
                "主题": topic,
                "风格": style,
                "结构": structure,
                "结构元素": structure_elements,
                "角色": characters,
                "长度": len(story_content),
                "创作时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                "评估": {
                    "创新性": self._evaluate_innovation(story_content, story_elements),
                    "连贯性": self._evaluate_coherence(story_content, story_elements),
                    "相关性": self._evaluate_relevance(story_content, story_elements),
                    "情感影响": self._evaluate_emotional_impact(story_content, story_elements),
                    "复杂性": self._evaluate_complexity(story_content, story_elements)
                }
            }
            
            return {
                "content": story_content,
                "metadata": metadata
            }
        except Exception as e:
            logger.error_status(f"生成故事失败: {e}")
            return {"content": f"生成故事时出现错误: {str(e)}", "metadata": {"error": str(e)}}
    
    def _generate_characters(self, topic: str, style: str, count: int) -> List[str]:
        """
        为故事生成角色
        
        Args:
            topic: 故事主题
            style: 故事风格
            count: 角色数量
            
        Returns:
            角色列表
        """
        try:
            # 创建角色生成提示词
            prompt = f"""请为一个主题为"{topic}"、风格为"{style}"的故事创建{count}个有趣且具有深度的角色。
每个角色应包括名字和简短描述。
只需提供角色列表，每行一个角色，格式为"角色名：简短描述"。"""
            
            # 使用AI服务生成角色
            response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位角色设计专家，擅长创建有深度和个性的故事角色。"},
                {"role": "user", "content": prompt}
            ])
            
            # 解析角色列表
            character_text = response.get("content", "")
            character_lines = character_text.strip().split("\n")
            
            # 提取角色
            characters = []
            for line in character_lines:
                if "：" in line or ":" in line:
                    parts = line.replace(":", "：").split("：", 1)
                    if len(parts) == 2:
                        characters.append(parts[0].strip())
            
            # 确保至少有一个角色
            if not characters:
                characters = ["主角", "配角"]
            
            return characters[:count]
        except Exception as e:
            logger.error_status(f"生成角色失败: {e}")
            return ["主角", "配角"]
    
    def _determine_emotional_tone(self, style: str, topic: str) -> str:
        """
        确定故事的情感基调
        
        Args:
            style: 故事风格
            topic: 故事主题
            
        Returns:
            情感基调描述
        """
        # 根据风格和主题推断情感基调
        style_emotion_map = {
            "童话": "充满希望和奇思妙想",
            "科幻": "充满好奇和探索精神",
            "奇幻": "神秘而超凡",
            "悬疑": "紧张而充满疑惑",
            "冒险": "刺激而振奋人心",
            "历史": "厚重而深刻",
            "爱情": "温暖而感人",
            "哲理": "深思熟虑而启发性强",
            "寓言": "意味深长而发人深省",
            "日常": "平实而充满生活气息"
        }
        
        return style_emotion_map.get(style, "多层次情感交织")
    
    def _create_story_prompt(self, story_elements: Dict[str, Any], max_length: int) -> str:
        """
        创建故事生成提示词
        
        Args:
            story_elements: 故事元素
            max_length: 最大长度
            
        Returns:
            提示词
        """
        topic = story_elements.get("主题", "")
        style = story_elements.get("风格", "")
        structure = story_elements.get("结构", "")
        structure_elements = story_elements.get("结构元素", [])
        characters = story_elements.get("角色", [])
        emotional_tone = story_elements.get("情感基调", "")
        
        # 整合创造性思维元素
        creative_insights = story_elements.get("创意亮点", "")
        associated_concepts = story_elements.get("关联概念", [])
        
        # 构建结构提示
        structure_guide = ""
        if structure_elements:
            structure_guide = f"\n故事应遵循{structure}结构，包含以下元素(不需要明确标出):\n" + "\n".join([f"- {element}" for element in structure_elements])
        
        # 构建角色提示
        character_guide = ""
        if characters:
            character_guide = f"\n故事中应包含以下角色(可以添加其他角色):\n" + "\n".join([f"- {character}" for character in characters])
        
        # 构建创意洞察提示
        creative_guide = ""
        if creative_insights:
            creative_guide = f"\n请融入以下创意洞察:\n{creative_insights}"
            
            if associated_concepts:
                creative_guide += f"\n可以考虑关联这些概念: {', '.join(associated_concepts)}"
        
        # 完整提示词
        prompt = f"""请创作一个主题为"{topic}"、风格为"{style}"的故事。

故事应具有{emotional_tone}的情感基调。{structure_guide}{character_guide}{creative_guide}

请确保故事结构完整，人物形象鲜明，情节连贯有趣，总体篇幅适中(不超过{max_length}字)。
故事应该富有创意，给读者留下深刻印象。

直接开始创作故事，不需要添加标题、作者名或其他额外说明。"""
        
        return prompt
    
    def generate_poem(self, topic: str, style: str = None, structure: str = None,
                    length: str = "moderate", thinking_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成诗歌内容
        
        Args:
            topic: 诗歌主题
            style: 诗歌风格
            structure: 诗歌结构
            length: 长度级别
            thinking_result: 创造性思维结果
            
        Returns:
            生成的诗歌及元数据
        """
        try:
            # 确定诗歌风格
            if not style:
                style = random.choice(self.content_styles["诗歌风格"])
            
            # 确定诗歌结构
            if not structure:
                structure = random.choice(list(self.content_templates["诗歌结构"].keys()))
            
            structure_description = self.content_templates["诗歌结构"].get(structure, "")
            
            # 确定诗歌长度
            length_factor = self.content_config["complexity_levels"].get(length, 0.6)
            max_poem_length = int(self.content_config["max_length"]["poem"] * length_factor)
            
            # 准备创建诗歌的提示信息
            poem_elements = {
                "主题": topic,
                "风格": style,
                "结构": structure,
                "结构描述": structure_description,
                "情感基调": self._determine_poem_tone(style, topic)
            }
            
            # 如果有创造性思维结果，融合到诗歌元素中
            if thinking_result:
                poem_elements.update({
                    "创意亮点": thinking_result.get("content", ""),
                    "关联概念": thinking_result.get("associated_concepts", []),
                    "思维方式": thinking_result.get("thinking_type", "")
                })
                
                # 如果是隐喻思维，特别处理
                if thinking_result.get("thinking_type") == "metaphor":
                    poem_elements["核心隐喻"] = thinking_result.get("content", "")
            
            # 创建诗歌提示词
            prompt = self._create_poem_prompt(poem_elements, max_poem_length)
            
            # 使用AI服务生成诗歌
            poem_response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位才华横溢的诗人，能够创作各种风格的优美诗歌。"},
                {"role": "user", "content": prompt}
            ])
            
            # 提取诗歌内容
            poem_content = poem_response.get("content", "")
            
            # 准备元数据
            metadata = {
                "主题": topic,
                "风格": style,
                "结构": structure,
                "长度": len(poem_content),
                "创作时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                "评估": {
                    "创新性": self._evaluate_innovation(poem_content, poem_elements),
                    "连贯性": self._evaluate_coherence(poem_content, poem_elements),
                    "相关性": self._evaluate_relevance(poem_content, poem_elements),
                    "情感影响": self._evaluate_emotional_impact(poem_content, poem_elements),
                    "复杂性": self._evaluate_complexity(poem_content, poem_elements)
                }
            }
            
            return {
                "content": poem_content,
                "metadata": metadata
            }
        except Exception as e:
            logger.error_status(f"生成诗歌失败: {e}")
            return {"content": f"生成诗歌时出现错误: {str(e)}", "metadata": {"error": str(e)}}
    
    def _determine_poem_tone(self, style: str, topic: str) -> str:
        """
        确定诗歌的情感基调
        
        Args:
            style: 诗歌风格
            topic: 诗歌主题
            
        Returns:
            情感基调描述
        """
        # 根据风格和主题推断情感基调
        style_emotion_map = {
            "古典": "含蓄而意境深远",
            "现代": "直白而充满张力",
            "浪漫": "热烈而充满激情",
            "哲理": "深邃而发人深省",
            "自由诗": "自由奔放而真实",
            "格律诗": "严谨而富有韵律",
            "叙事诗": "生动而具有故事性",
            "抒情诗": "感性而富有情感",
            "象征派": "隐喻丰富而意蕴深刻",
            "意象派": "画面感强而精炼"
        }
        
        return style_emotion_map.get(style, "情感丰富而有深度")
    
    def _create_poem_prompt(self, poem_elements: Dict[str, Any], max_length: int) -> str:
        """
        创建诗歌生成提示词
        
        Args:
            poem_elements: 诗歌元素
            max_length: 最大长度
            
        Returns:
            提示词
        """
        topic = poem_elements.get("主题", "")
        style = poem_elements.get("风格", "")
        structure = poem_elements.get("结构", "")
        structure_description = poem_elements.get("结构描述", "")
        emotional_tone = poem_elements.get("情感基调", "")
        
        # 整合创造性思维元素
        creative_insights = poem_elements.get("创意亮点", "")
        associated_concepts = poem_elements.get("关联概念", [])
        core_metaphor = poem_elements.get("核心隐喻", "")
        
        # 构建结构提示
        structure_guide = ""
        if structure_description:
            structure_guide = f"\n诗歌应采用{structure}结构: {structure_description}"
        
        # 构建创意洞察提示
        creative_guide = ""
        if core_metaphor:
            creative_guide = f"\n请以下面的核心隐喻为基础:\n{core_metaphor}"
        elif creative_insights:
            creative_guide = f"\n请融入以下创意洞察:\n{creative_insights}"
            
        if associated_concepts:
            creative_guide += f"\n可以考虑关联这些概念: {', '.join(associated_concepts)}"
        
        # 完整提示词
        prompt = f"""请创作一首主题为"{topic}"、风格为"{style}"的诗歌。

诗歌应具有{emotional_tone}的情感基调。{structure_guide}{creative_guide}

请确保诗歌语言优美，意境深远，情感真挚，篇幅适中(不超过{max_length}字)。
诗歌应该富有创意和独特的视角，能够触动读者的心灵。

直接开始创作诗歌，不需要添加标题、作者名或其他额外说明。"""
        
        return prompt
    
    def generate_concept_art(self, topic: str, style: str = None, framework: str = None,
                           complexity: str = "moderate", thinking_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成概念艺术内容
        
        Args:
            topic: 概念主题
            style: 艺术风格
            framework: 概念框架
            complexity: 复杂度级别
            thinking_result: 创造性思维结果
            
        Returns:
            生成的概念艺术及元数据
        """
        try:
            # 确定艺术风格
            if not style:
                style = random.choice(self.content_styles["艺术风格"])
            
            # 确定概念框架
            if not framework:
                framework = random.choice(list(self.content_templates["概念框架"].keys()))
            
            framework_description = self.content_templates["概念框架"].get(framework, "")
            
            # 确定复杂度
            complexity_factor = self.content_config["complexity_levels"].get(complexity, 0.6)
            max_art_length = int(self.content_config["max_length"]["concept_art"] * complexity_factor)
            
            # 准备创建概念艺术的提示信息
            art_elements = {
                "主题": topic,
                "风格": style,
                "框架": framework,
                "框架描述": framework_description,
                "视觉元素": self._generate_visual_elements(topic, style)
            }
            
            # 如果有创造性思维结果，融合到概念艺术元素中
            if thinking_result:
                art_elements.update({
                    "创意亮点": thinking_result.get("content", ""),
                    "关联概念": thinking_result.get("associated_concepts", []),
                    "思维方式": thinking_result.get("thinking_type", "")
                })
                
                # 如果是概念混合思维，特别处理
                if thinking_result.get("thinking_type") == "concept_blend":
                    art_elements["概念混合"] = thinking_result.get("content", "")
            
            # 创建概念艺术提示词
            prompt = self._create_concept_art_prompt(art_elements, max_art_length)
            
            # 使用AI服务生成概念艺术
            art_response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位概念艺术大师，能够创造出令人惊叹的概念性艺术描述。"},
                {"role": "user", "content": prompt}
            ])
            
            # 提取概念艺术内容
            art_content = art_response.get("content", "")
            
            # 准备元数据
            metadata = {
                "主题": topic,
                "风格": style,
                "框架": framework,
                "视觉元素": art_elements.get("视觉元素", []),
                "长度": len(art_content),
                "创作时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                "评估": {
                    "创新性": self._evaluate_innovation(art_content, art_elements),
                    "连贯性": self._evaluate_coherence(art_content, art_elements),
                    "相关性": self._evaluate_relevance(art_content, art_elements),
                    "情感影响": self._evaluate_emotional_impact(art_content, art_elements),
                    "复杂性": self._evaluate_complexity(art_content, art_elements)
                }
            }
            
            return {
                "content": art_content,
                "metadata": metadata
            }
        except Exception as e:
            logger.error_status(f"生成概念艺术失败: {e}")
            return {"content": f"生成概念艺术时出现错误: {str(e)}", "metadata": {"error": str(e)}}
    
    def _generate_visual_elements(self, topic: str, style: str) -> List[str]:
        """
        为概念艺术生成视觉元素
        
        Args:
            topic: 概念主题
            style: 艺术风格
            
        Returns:
            视觉元素列表
        """
        try:
            # 创建视觉元素生成提示词
            prompt = f"""请为一个主题为"{topic}"、风格为"{style}"的概念艺术作品提供5-7个关键视觉元素。
这些元素应该是具体的视觉描述，能够构成作品的核心视觉语言。
只需提供元素列表，每行一个元素。"""
            
            # 使用AI服务生成视觉元素
            response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位视觉艺术专家，擅长提炼艺术作品的关键视觉元素。"},
                {"role": "user", "content": prompt}
            ])
            
            # 解析视觉元素列表
            elements_text = response.get("content", "")
            elements = [line.strip() for line in elements_text.strip().split("\n") if line.strip()]
            
            # 确保至少有一些元素
            if not elements:
                elements = ["光影对比", "色彩变化", "空间构成", "形态结构"]
            
            return elements
        except Exception as e:
            logger.error_status(f"生成视觉元素失败: {e}")
            return ["光影对比", "色彩变化", "空间构成", "形态结构"]
    
    def _create_concept_art_prompt(self, art_elements: Dict[str, Any], max_length: int) -> str:
        """
        创建概念艺术生成提示词
        
        Args:
            art_elements: 概念艺术元素
            max_length: 最大长度
            
        Returns:
            提示词
        """
        topic = art_elements.get("主题", "")
        style = art_elements.get("风格", "")
        framework = art_elements.get("框架", "")
        framework_description = art_elements.get("框架描述", "")
        visual_elements = art_elements.get("视觉元素", [])
        
        # 整合创造性思维元素
        creative_insights = art_elements.get("创意亮点", "")
        associated_concepts = art_elements.get("关联概念", [])
        concept_blend = art_elements.get("概念混合", "")
        
        # 构建框架提示
        framework_guide = ""
        if framework_description:
            framework_guide = f"\n概念艺术应采用{framework}框架: {framework_description}"
        
        # 构建视觉元素提示
        visual_guide = ""
        if visual_elements:
            visual_guide = "\n请融入以下视觉元素:\n" + "\n".join([f"- {element}" for element in visual_elements])
        
        # 构建创意洞察提示
        creative_guide = ""
        if concept_blend:
            creative_guide = f"\n请以下面的概念混合为基础:\n{concept_blend}"
        elif creative_insights:
            creative_guide = f"\n请融入以下创意洞察:\n{creative_insights}"
            
        if associated_concepts:
            creative_guide += f"\n可以考虑关联这些概念: {', '.join(associated_concepts)}"
        
        # 完整提示词
        prompt = f"""请创作一个主题为"{topic}"、风格为"{style}"的概念艺术作品的详细描述。

这个概念艺术应该极具视觉冲击力和概念深度。{framework_guide}{visual_guide}{creative_guide}

请提供以下内容:
1. 整体艺术概念和视觉描述
2. 作品传达的核心理念和哲学思考
3. 作品的视觉细节和构成元素
4. 作品可能产生的观感和情绪反应

请确保描述细致生动，能让人在脑海中清晰地想象出这件概念艺术作品的样子。
描述篇幅适中(不超过{max_length}字)，但要包含足够的细节和深度。

直接开始创作描述，不需要添加标题、作者名或其他额外说明。"""
        
        return prompt
    
    def generate_creative_dialogue(self, topic: str, style: str = None, participants: List[str] = None,
                                 length: str = "moderate", thinking_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成创意对话内容
        
        Args:
            topic: 对话主题
            style: 对话风格
            participants: 参与者
            length: 长度级别
            thinking_result: 创造性思维结果
            
        Returns:
            生成的创意对话及元数据
        """
        try:
            # 确定对话风格
            if not style:
                style = random.choice(self.content_styles["对话风格"])
            
            # 确定参与者
            if not participants or len(participants) < 2:
                participants = self._generate_dialogue_participants(topic, style)
            
            # 确定对话长度
            length_factor = self.content_config["complexity_levels"].get(length, 0.6)
            max_dialogue_length = int(self.content_config["max_length"]["dialogue"] * length_factor)
            
            # 生成对话关键点
            key_points = self._generate_dialogue_key_points(topic, style)
            
            # 准备创建对话的提示信息
            dialogue_elements = {
                "主题": topic,
                "风格": style,
                "参与者": participants,
                "关键点": key_points
            }
            
            # 如果有创造性思维结果，融合到对话元素中
            if thinking_result:
                dialogue_elements.update({
                    "创意亮点": thinking_result.get("content", ""),
                    "关联概念": thinking_result.get("associated_concepts", []),
                    "思维方式": thinking_result.get("thinking_type", "")
                })
                
                # 如果是问题解决思维，特别处理
                if thinking_result.get("thinking_type") == "problem_solving":
                    dialogue_elements["问题解决方案"] = thinking_result.get("content", "")
            
            # 创建对话提示词
            prompt = self._create_dialogue_prompt(dialogue_elements, max_dialogue_length)
            
            # 使用AI服务生成对话
            dialogue_response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位出色的对话创作者，能够创作出生动、有深度的对话内容。"},
                {"role": "user", "content": prompt}
            ])
            
            # 提取对话内容
            dialogue_content = dialogue_response.get("content", "")
            
            # 准备元数据
            metadata = {
                "主题": topic,
                "风格": style,
                "参与者": participants,
                "关键点": key_points,
                "长度": len(dialogue_content),
                "创作时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                "评估": {
                    "创新性": self._evaluate_innovation(dialogue_content, dialogue_elements),
                    "连贯性": self._evaluate_coherence(dialogue_content, dialogue_elements),
                    "相关性": self._evaluate_relevance(dialogue_content, dialogue_elements),
                    "情感影响": self._evaluate_emotional_impact(dialogue_content, dialogue_elements),
                    "复杂性": self._evaluate_complexity(dialogue_content, dialogue_elements)
                }
            }
            
            return {
                "content": dialogue_content,
                "metadata": metadata
            }
        except Exception as e:
            logger.error_status(f"生成创意对话失败: {e}")
            return {"content": f"生成创意对话时出现错误: {str(e)}", "metadata": {"error": str(e)}}
    
    def _generate_dialogue_participants(self, topic: str, style: str) -> List[str]:
        """
        为对话生成参与者
        
        Args:
            topic: 对话主题
            style: 对话风格
            
        Returns:
            参与者列表
        """
        try:
            # 创建参与者生成提示词
            prompt = f"""请为一个主题为"{topic}"、风格为"{style}"的对话创建2-3个有趣的参与者。
每个参与者应该有独特的身份或视角，能够为对话带来不同的观点。
只需提供参与者列表，每行一个参与者。"""
            
            # 使用AI服务生成参与者
            response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位角色设计专家，擅长创建有个性的对话角色。"},
                {"role": "user", "content": prompt}
            ])
            
            # 解析参与者列表
            participants_text = response.get("content", "")
            participants = [line.strip() for line in participants_text.strip().split("\n") if line.strip()]
            
            # 确保至少有两个参与者
            if len(participants) < 2:
                participants = ["甲", "乙"]
            
            return participants
        except Exception as e:
            logger.error_status(f"生成对话参与者失败: {e}")
            return ["甲", "乙"]
    
    def _generate_dialogue_key_points(self, topic: str, style: str) -> List[str]:
        """
        为对话生成关键点
        
        Args:
            topic: 对话主题
            style: 对话风格
            
        Returns:
            关键点列表
        """
        try:
            # 创建关键点生成提示词
            prompt = f"""请为一个主题为"{topic}"、风格为"{style}"的对话提供3-5个关键讨论点。
这些关键点应该能够引导对话的发展，形成有深度的交流。
只需提供关键点列表，每行一个关键点。"""
            
            # 使用AI服务生成关键点
            response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位对话设计专家，擅长创建有结构和深度的对话框架。"},
                {"role": "user", "content": prompt}
            ])
            
            # 解析关键点列表
            key_points_text = response.get("content", "")
            key_points = [line.strip() for line in key_points_text.strip().split("\n") if line.strip()]
            
            # 确保至少有一些关键点
            if not key_points:
                key_points = ["观点交流", "深入探讨", "总结反思"]
            
            return key_points
        except Exception as e:
            logger.error_status(f"生成对话关键点失败: {e}")
            return ["观点交流", "深入探讨", "总结反思"]
    
    def _create_dialogue_prompt(self, dialogue_elements: Dict[str, Any], max_length: int) -> str:
        """
        创建对话生成提示词
        
        Args:
            dialogue_elements: 对话元素
            max_length: 最大长度
            
        Returns:
            提示词
        """
        topic = dialogue_elements.get("主题", "")
        style = dialogue_elements.get("风格", "")
        participants = dialogue_elements.get("参与者", [])
        key_points = dialogue_elements.get("关键点", [])
        
        # 整合创造性思维元素
        creative_insights = dialogue_elements.get("创意亮点", "")
        associated_concepts = dialogue_elements.get("关联概念", [])
        problem_solution = dialogue_elements.get("问题解决方案", "")
        
        # 构建参与者提示
        participants_guide = ""
        if participants:
            participants_guide = "\n对话参与者:\n" + "\n".join([f"- {participant}" for participant in participants])
        
        # 构建关键点提示
        key_points_guide = ""
        if key_points:
            key_points_guide = "\n对话应该包含以下关键点(不需要明确标出):\n" + "\n".join([f"- {point}" for point in key_points])
        
        # 构建创意洞察提示
        creative_guide = ""
        if problem_solution:
            creative_guide = f"\n请在对话中探讨以下问题解决方案:\n{problem_solution}"
        elif creative_insights:
            creative_guide = f"\n请在对话中融入以下创意洞察:\n{creative_insights}"
            
        if associated_concepts:
            creative_guide += f"\n可以考虑关联这些概念: {', '.join(associated_concepts)}"
        
        # 完整提示词
        prompt = f"""请创作一个主题为"{topic}"、风格为"{style}"的创意对话。

{participants_guide}{key_points_guide}{creative_guide}

请注意:
1. 对话应该自然流畅，符合真实对话的特点
2. 每个参与者应该有鲜明的个性和独特的视角
3. 对话应该有深度和启发性，不仅是表面的交流
4. 对话格式应为"角色名: 对话内容"的形式
5. 对话总体篇幅适中(不超过{max_length}字)

直接开始创作对话，不需要添加标题、背景说明或其他额外内容。"""
        
        return prompt
    
    def generate_creative_expression(self, topic: str, style: str = None, 
                                   complexity: str = "moderate", thinking_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成创新思维表达
        
        Args:
            topic: 表达主题
            style: 表达风格
            complexity: 复杂度级别
            thinking_result: 创造性思维结果
            
        Returns:
            生成的创新思维表达及元数据
        """
        try:
            # 确定表达风格
            if not style:
                style = random.choice(self.content_styles["表达风格"])
            
            # 确定复杂度
            complexity_factor = self.content_config["complexity_levels"].get(complexity, 0.6)
            max_expression_length = int(self.content_config["max_length"]["expression"] * complexity_factor)
            
            # 准备创建表达的提示信息
            expression_elements = {
                "主题": topic,
                "风格": style,
                "复杂度": complexity
            }
            
            # 如果有创造性思维结果，融合到表达元素中
            if thinking_result:
                expression_elements.update({
                    "创意亮点": thinking_result.get("content", ""),
                    "关联概念": thinking_result.get("associated_concepts", []),
                    "思维方式": thinking_result.get("thinking_type", "")
                })
            
            # 创建表达提示词
            prompt = self._create_expression_prompt(expression_elements, max_expression_length)
            
            # 使用AI服务生成表达
            expression_response = self.ai_service.get_completion([
                {"role": "system", "content": "你是一位思想家和创意表达大师，擅长以独特而有深度的方式表达创新思想。"},
                {"role": "user", "content": prompt}
            ])
            
            # 提取表达内容
            expression_content = expression_response.get("content", "")
            
            # 准备元数据
            metadata = {
                "主题": topic,
                "风格": style,
                "复杂度": complexity,
                "长度": len(expression_content),
                "创作时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                "评估": {
                    "创新性": self._evaluate_innovation(expression_content, expression_elements),
                    "连贯性": self._evaluate_coherence(expression_content, expression_elements),
                    "相关性": self._evaluate_relevance(expression_content, expression_elements),
                    "情感影响": self._evaluate_emotional_impact(expression_content, expression_elements),
                    "复杂性": self._evaluate_complexity(expression_content, expression_elements)
                }
            }
            
            return {
                "content": expression_content,
                "metadata": metadata
            }
        except Exception as e:
            logger.error_status(f"生成创新思维表达失败: {e}")
            return {"content": f"生成创新思维表达时出现错误: {str(e)}", "metadata": {"error": str(e)}}
    
    def _create_expression_prompt(self, expression_elements: Dict[str, Any], max_length: int) -> str:
        """
        创建创新思维表达提示词
        
        Args:
            expression_elements: 表达元素
            max_length: 最大长度
            
        Returns:
            提示词
        """
        topic = expression_elements.get("主题", "")
        style = expression_elements.get("风格", "")
        complexity = expression_elements.get("复杂度", "moderate")
        
        # 整合创造性思维元素
        creative_insights = expression_elements.get("创意亮点", "")
        associated_concepts = expression_elements.get("关联概念", [])
        thinking_type = expression_elements.get("思维方式", "")
        
        # 构建风格提示
        style_guide = self._get_expression_style_guide(style)
        
        # 构建复杂度提示
        complexity_guide = {
            "simple": "表达应该简洁明了，使用简单直接的语言，让人容易理解。",
            "moderate": "表达应该既有深度又有清晰度，使用适度复杂的语言和结构。",
            "complex": "表达应该深入而多层次，可以使用复杂的概念和精细的逻辑结构，适合深度思考。"
        }.get(complexity, "表达应该既有深度又有清晰度，使用适度复杂的语言和结构。")
        
        # 构建创意洞察提示
        creative_guide = ""
        if creative_insights:
            creative_guide = f"\n请以下面的创意洞察为基础:\n{creative_insights}"
            
        if associated_concepts:
            creative_guide += f"\n可以考虑关联这些概念: {', '.join(associated_concepts)}"
        
        # 思维方式提示
        thinking_guide = ""
        if thinking_type:
            thinking_guide_map = {
                "divergent_thinking": "请使用发散思维，探索多种可能性和视角。",
                "analogical_reasoning": "请使用类比思维，通过类比或隐喻来阐明概念。",
                "combinatorial_reasoning": "请使用组合思维，将不同概念或元素创造性地组合。",
                "metaphorical_thinking": "请使用隐喻思维，通过隐喻来表达复杂概念。",
                "problem_solving": "请使用问题解决思维，展示如何创新性地解决问题。"
            }
            thinking_guide = "\n" + thinking_guide_map.get(thinking_type, "请使用创造性思维，展现独特的视角和见解。")
        
        # 完整提示词
        prompt = f"""请创作一段关于"{topic}"的创新思维表达，使用{style}风格。

{complexity_guide}{style_guide}{creative_guide}{thinking_guide}

这段表达应该展现独特的思维视角，提供新颖的见解，能够启发读者思考。
表达应该富有创意和思想深度，同时保持连贯和清晰。
总体篇幅适中(不超过{max_length}字)。

直接开始创作表达，不需要添加标题、作者名或其他额外说明。"""
        
        return prompt
    
    def _get_expression_style_guide(self, style: str) -> str:
        """
        获取表达风格指南
        
        Args:
            style: 表达风格
            
        Returns:
            风格指南描述
        """
        style_guides = {
            "直接": "\n请使用直接、清晰的语言，直切要点，表达应该简洁有力。",
            "隐喻": "\n请大量使用隐喻和比喻，通过生动的形象来表达抽象概念。",
            "类比": "\n请使用类比的方式，将不熟悉的概念与熟悉的事物建立联系。",
            "夸张": "\n请适度使用夸张手法，强调某些特点或方面，使表达更具冲击力。",
            "反讽": "\n请使用巧妙的反讽，通过表面意思与实际意图的对比，传达更深层次的含义。",
            "简洁": "\n请使用极简的表达方式，用最少的词语传达最丰富的意思。",
            "详细": "\n请使用详尽的描述和分析，深入探讨各个方面和细节。",
            "诗意": "\n请使用富有诗意的语言，注重意象和韵律，使表达更具美感。",
            "技术性": "\n请使用准确的术语和逻辑结构，表达应该精确而有条理。",
            "感性": "\n请使用富有情感和感性的语言，触动读者的情感和共鸣。"
        }
        
        return style_guides.get(style, "\n请使用独特而有创意的表达方式，展现你的创新思维。")
    
    def _evaluate_innovation(self, content: str, metadata: Dict[str, Any]) -> float:
        """
        评估内容的创新性
        
        Args:
            content: 内容文本
            metadata: 内容元数据
            
        Returns:
            创新性评分(0-1)
        """
        try:
            # 内容长度因素
            length_factor = min(1.0, len(content) / 1000)
            
            # 创意因素 - 考虑是否基于创意思维结果
            creativity_factor = 0.7
            if "创意亮点" in metadata and metadata["创意亮点"]:
                creativity_factor = 0.8
            
            # 随机因素 - 引入一些变化
            random_factor = 0.9 + (random.random() * 0.2)
            
            # 综合评分
            score = (0.4 * length_factor + 0.6 * creativity_factor) * random_factor
            
            # 限制在0-1范围内
            return max(0.0, min(1.0, score))
        except Exception as e:
            logger.error_status(f"评估创新性失败: {e}")
            return 0.7
    
    def _evaluate_coherence(self, content: str, metadata: Dict[str, Any]) -> float:
        """
        评估内容的连贯性
        
        Args:
            content: 内容文本
            metadata: 内容元数据
            
        Returns:
            连贯性评分(0-1)
        """
        try:
            # 基础分数
            base_score = 0.75
            
            # 复杂度因素 - 复杂内容可能连贯性稍低
            complexity = metadata.get("复杂度", "moderate")
            complexity_factor = {
                "simple": 0.9,
                "moderate": 0.8,
                "complex": 0.7
            }.get(complexity, 0.8)
            
            # 风格因素
            style = metadata.get("风格", "")
            style_factor = 0.8
            if style in ["直接", "简洁", "技术性"]:
                style_factor = 0.9
            elif style in ["夸张", "反讽", "诗意"]:
                style_factor = 0.7
            
            # 随机因素
            random_factor = 0.95 + (random.random() * 0.1)
            
            # 综合评分
            score = (base_score * 0.6 + complexity_factor * 0.2 + style_factor * 0.2) * random_factor
            
            # 限制在0-1范围内
            return max(0.0, min(1.0, score))
        except Exception as e:
            logger.error_status(f"评估连贯性失败: {e}")
            return 0.8
    
    def _evaluate_relevance(self, content: str, metadata: Dict[str, Any]) -> float:
        """
        评估内容的相关性
        
        Args:
            content: 内容文本
            metadata: 内容元数据
            
        Returns:
            相关性评分(0-1)
        """
        try:
            # 基础分数
            base_score = 0.85
            
            # 主题因素 - 主题是否在内容中出现
            topic = metadata.get("主题", "")
            topic_factor = 0.9
            if topic and topic in content:
                topic_factor = 1.0
            
            # 关联概念因素
            associated_concepts = metadata.get("关联概念", [])
            concept_factor = 0.8
            if associated_concepts:
                # 计算出现的关联概念比例
                concepts_found = sum(1 for concept in associated_concepts if concept in content)
                if concepts_found > 0:
                    concept_factor = 0.8 + (0.2 * min(1.0, concepts_found / len(associated_concepts)))
            
            # 随机因素
            random_factor = 0.95 + (random.random() * 0.1)
            
            # 综合评分
            score = (base_score * 0.5 + topic_factor * 0.3 + concept_factor * 0.2) * random_factor
            
            # 限制在0-1范围内
            return max(0.0, min(1.0, score))
        except Exception as e:
            logger.error_status(f"评估相关性失败: {e}")
            return 0.9
    
    def _evaluate_emotional_impact(self, content: str, metadata: Dict[str, Any]) -> float:
        """
        评估内容的情感影响
        
        Args:
            content: 内容文本
            metadata: 内容元数据
            
        Returns:
            情感影响评分(0-1)
        """
        try:
            # 基础分数
            base_score = 0.7
            
            # 内容类型因素
            content_type = metadata.get("content_type", "")
            type_factor = {
                "story": 0.9,
                "poem": 0.95,
                "concept_art": 0.85,
                "dialogue": 0.8,
                "expression": 0.75
            }.get(content_type, 0.8)
            
            # 风格因素
            style = metadata.get("风格", "")
            style_factor = 0.8
            if style in ["诗意", "感性", "浪漫", "爱情", "悲伤"]:
                style_factor = 0.95
            elif style in ["技术性", "直接", "简洁"]:
                style_factor = 0.7
            
            # 长度因素 - 较长内容可能有更多情感元素
            length = len(content)
            length_factor = min(1.0, length / 2000)
            
            # 随机因素
            random_factor = 0.9 + (random.random() * 0.2)
            
            # 综合评分
            score = (base_score * 0.4 + type_factor * 0.2 + style_factor * 0.2 + length_factor * 0.2) * random_factor
            
            # 限制在0-1范围内
            return max(0.0, min(1.0, score))
        except Exception as e:
            logger.error_status(f"评估情感影响失败: {e}")
            return 0.7
    
    def _evaluate_complexity(self, content: str, metadata: Dict[str, Any]) -> float:
        """
        评估内容的复杂性
        
        Args:
            content: 内容文本
            metadata: 内容元数据
            
        Returns:
            复杂性评分(0-1)
        """
        try:
            # 指定的复杂度
            specified_complexity = metadata.get("复杂度", "moderate")
            specified_factor = {
                "simple": 0.3,
                "moderate": 0.6,
                "complex": 0.9
            }.get(specified_complexity, 0.6)
            
            # 句子长度因素 - 较长句子可能表示更复杂的内容
            sentences = content.split("。")
            avg_sentence_length = sum(len(s) for s in sentences) / max(1, len(sentences))
            sentence_factor = min(1.0, avg_sentence_length / 50)
            
            # 长度因素
            length = len(content)
            length_factor = min(1.0, length / 2000)
            
            # 随机因素
            random_factor = 0.95 + (random.random() * 0.1)
            
            # 综合评分
            score = (specified_factor * 0.5 + sentence_factor * 0.3 + length_factor * 0.2) * random_factor
            
            # 限制在0-1范围内
            return max(0.0, min(1.0, score))
        except Exception as e:
            logger.error_status(f"评估复杂性失败: {e}")
            return 0.6
    
    def _add_to_history(self, content_type: str, topic: str, result: Dict[str, Any]):
        """
        添加到内容生成历史
        
        Args:
            content_type: 内容类型
            topic: 内容主题
            result: 生成结果
        """
        history_entry = {
            "content_type": content_type,
            "topic": topic,
            "content": result.get("content", ""),
            "metadata": result.get("metadata", {}),
            "timestamp": time.time()
        }
        
        self.content_history.append(history_entry)
        
        # 限制历史记录大小
        if len(self.content_history) > self.max_history_size:
            self.content_history = self.content_history[-self.max_history_size:]
    
    def get_content_history(self, content_type: str = None, limit: int = 5) -> List[Dict[str, Any]]:
        """
        获取内容生成历史
        
        Args:
            content_type: 内容类型，如果为None则返回所有类型
            limit: 返回记录数量限制
            
        Returns:
            内容生成历史列表
        """
        if content_type:
            filtered_history = [entry for entry in self.content_history 
                               if entry["content_type"] == content_type]
        else:
            filtered_history = self.content_history
        
        # 按时间倒序排序并限制数量
        sorted_history = sorted(filtered_history, key=lambda x: x["timestamp"], reverse=True)
        
        return sorted_history[:limit]
    
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块当前状态
        
        Returns:
            Dict: 模块状态
        """
        return {
            "content_history": self.content_history,
            "content_config": self.content_config,
            "content_styles": self.content_styles,
            "content_templates": self.content_templates,
            "max_history_size": self.max_history_size
        }

    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        从保存的状态加载模块
        
        Args:
            state: 模块状态
        
        Returns:
            bool: 加载是否成功
        """
        try:
            # 加载内容历史
            if "content_history" in state:
                self.content_history = state["content_history"]
                
            # 加载配置
            if "content_config" in state:
                self.content_config.update(state["content_config"])
                
            # 加载风格库
            if "content_styles" in state:
                self.content_styles.update(state["content_styles"])
                
            # 加载模板库
            if "content_templates" in state:
                self.content_templates.update(state["content_templates"])
                
            # 加载历史大小限制
            if "max_history_size" in state:
                self.max_history_size = state["max_history_size"]
                
            return True
            
        except Exception as e:
            logger.error_status(f"加载创造性内容生成模块状态失败: {str(e)}")
            return False
    
    def _shutdown_module(self) -> bool:
        """
        关闭模块（内部方法）
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            # 保存内容生成历史和配置
            self._save_content_data()
            
            return True
        except Exception as e:
            logger.error_status(f"关闭创造性内容生成模块失败: {str(e)}")
            return False
    
    def _save_content_data(self):
        """保存内容生成数据到文件"""
        try:
            # 准备数据目录
            data_dir = os.path.join(root_dir, "data", "creative_content")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            # 保存内容历史
            history_path = os.path.join(data_dir, f"{self.module_id}_history.json")
            with open(history_path, 'w', encoding='utf-8') as f:
                json.dump(self.content_history, f, ensure_ascii=False, indent=2)
            
            # 保存定制风格和模板
            config_path = os.path.join(data_dir, f"{self.module_id}_config.json")
            config_data = {
                "content_config": self.content_config,
                "content_styles": self.content_styles,
                "content_templates": self.content_templates,
                "max_history_size": self.max_history_size
            }
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logger.data_status(f"已保存创造性内容生成数据: {history_path}, {config_path}")
        except Exception as e:
            logger.error_status(f"保存创造性内容生成数据失败: {str(e)}")
    
    def shutdown(self):
        """关闭模块"""
        success = self._shutdown_module()
        logger.success(f"创造性内容生成模块 {self.module_id} 已关闭, 状态: {'成功' if success else '失败'}")
        return success


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> CreativeContentGenerator:
    """
    获取创造性内容生成模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        创造性内容生成模块实例
    """
    return CreativeContentGenerator(config=config) 