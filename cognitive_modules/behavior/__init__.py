"""
行为层模块 - Behavior Module

该模块实现了数字生命体的行为层功能，包括对话生成、行为规划、创意内容生成和多模态表达。
负责将内部决策转化为具体的外部行为表现。

主要组件:
- 回应生成器(response_generator): 负责生成对话和文本响应
- 多模态表达(multimodal_expression): 负责多模态内容表达
- 创意内容生成(creative_content_generator): 负责生成创意性内容
- 行为规划(action_planning): 负责计划和协调行为序列

作者: <PERSON>
创建日期: 2024-07-21
版本: 1.0
"""

from cognitive_modules.behavior.response_generator import ResponseGenerator, get_instance as get_response_generator
from cognitive_modules.behavior.multimodal_expression import MultimodalExpression, get_instance as get_multimodal_expression
from cognitive_modules.behavior.creative_content_generator import CreativeContentGenerator, get_instance as get_creative_content_generator
from cognitive_modules.behavior.action_planning import ActionPlanning, get_instance as get_action_planning

__all__ = [
    'ResponseGenerator', 'get_response_generator',
    'MultimodalExpression', 'get_multimodal_expression',
    'CreativeContentGenerator', 'get_creative_content_generator',
    'ActionPlanning', 'get_action_planning'
]
