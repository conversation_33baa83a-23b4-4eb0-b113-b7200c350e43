#!/usr/bin/env python3
"""
响应生成器模块 - Response Generator

该模块负责生成数字生命体的自然语言回复，包括对话响应、情感表达和行为描述。
它使用模板、规则和AI大模型融合的方式，生成自然、连贯、符合人格的响应。

作者: Claude
创建日期: 2024-07-12
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import random
from typing import Dict, Any, List, Optional, Tuple, Union

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "..", ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.base.cognitive_interface import IBehaviorModule
from core.integrated_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("cognitive.behavior.response_generator")

class ResponseGenerator(CognitiveModuleBase, IBehaviorModule):
    """
    响应生成器模块
    
    负责生成数字生命体的自然语言回复，包括对话响应、情感表达和行为描述。
    它使用模板、规则和AI大模型融合的方式，生成自然、连贯、符合人格的响应。
    """
    
    def __init__(self, module_id: str = "behavior.response_generator", 
                 module_name: str = "响应生成器", 
                 module_type: str = "behavior"):
        """
        初始化响应生成器模块
        
        Args:
            module_id: 模块ID
            module_name: 模块名称
            module_type: 模块类型
        """
        super().__init__(module_id, module_name, module_type)
        
        # 模板数据
        self.templates = {}
        
        # 规则集
        self.rules = {}
        
        # 输出样式配置
        self.output_style = {
            "formality": 0.5,          # 正式程度(0-1)
            "verbosity": 0.6,          # 详细程度(0-1)
            "emotionality": 0.7,       # 情感表达程度(0-1)
            "creativity": 0.8,         # 创造性程度(0-1)
            "humor": 0.4,              # 幽默程度(0-1)
            "empathy": 0.9             # 共情程度(0-1)
        }
        
        # 情感表达词库
        self.emotion_phrases = {}
        
        # AI服务适配器
        self.ai_service_adapter = None
        
        # Legacy适配器
        self.legacy_adapter = None
        
        # 初始化事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        logger.info("响应生成器模块已创建")
    
    def _initialize_module(self) -> bool:
        """
        初始化模块
        
        Returns:
            初始化是否成功
        """
        try:
            # 加载响应模板
            templates_path = os.path.join(root_dir, "configs", "templates", "response_templates.json")
            if os.path.exists(templates_path):
                with open(templates_path, 'r', encoding='utf-8') as f:
                    self.templates = json.load(f)
                logger.info(f"已加载响应模板: {len(self.templates)} 类")
            else:
                logger.warning_status(f"响应模板文件不存在: {templates_path}")
                self.templates = self._get_default_templates()
            
            # 加载情感表达词库
            emotions_path = os.path.join(root_dir, "configs", "templates", "emotion_phrases.json")
            if os.path.exists(emotions_path):
                with open(emotions_path, 'r', encoding='utf-8') as f:
                    self.emotion_phrases = json.load(f)
                logger.info(f"已加载情感表达词库: {len(self.emotion_phrases)} 种情感")
            else:
                logger.warning_status(f"情感表达词库文件不存在: {emotions_path}")
                self.emotion_phrases = self._get_default_emotion_phrases()
            
            # 注册事件处理器
            self.event_bus.subscribe("ai_service_adapter.ready", self._handle_ai_service_ready)
            self.event_bus.subscribe("legacy_adapter.ready", self._handle_legacy_adapter_ready)
            
            # 创建情感表达词库目录
            os.makedirs(os.path.join(root_dir, "configs", "templates"), exist_ok=True)
            
            # 保存默认模板和情感词库（如果不存在）
            if not os.path.exists(templates_path):
                with open(templates_path, 'w', encoding='utf-8') as f:
                    json.dump(self.templates, f, ensure_ascii=False, indent=2)
            
            if not os.path.exists(emotions_path):
                with open(emotions_path, 'w', encoding='utf-8') as f:
                    json.dump(self.emotion_phrases, f, ensure_ascii=False, indent=2)
            
            logger.success("响应生成器模块初始化成功")
            return True
            
        except Exception as e:
            logger.error_status(f"响应生成器模块初始化失败: {str(e)}")
            return False
    
    def _handle_ai_service_ready(self, event_data: Dict[str, Any]):
        """处理AI服务就绪事件"""
        self.ai_service_adapter = event_data.get("adapter")
        logger.info("已获取AI服务适配器")
    
    def _handle_legacy_adapter_ready(self, event_data: Dict[str, Any]):
        """处理Legacy适配器就绪事件"""
        self.legacy_adapter = event_data.get("adapter")
        logger.info("已获取Legacy适配器")
    
    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        # 解析输入数据
        user_id = input_data.get("user_id")
        session_id = input_data.get("session_id")
        input_type = input_data.get("type", "text")
        content = input_data.get("content", "")
        context = input_data.get("context", {})
        
        # 根据输入类型选择不同的处理方法
        if input_type == "text":
            response = self.generate_response(input_data)
        elif input_type == "action":
            response = self.execute_action(input_data)
        else:
            response = {
                "success": False,
                "error": f"不支持的输入类型: {input_type}",
                "response": "抱歉，我无法处理这种类型的输入。"
            }
        
        return response
    
    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
            
        Returns:
            更新是否成功
        """
        # 更新输出样式配置
        if "output_style" in context:
            for key, value in context["output_style"].items():
                if key in self.output_style:
                    self.output_style[key] = value
        
        return True
    
    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态信息
        """
        return {
            "templates_count": len(self.templates),
            "emotion_phrases_count": len(self.emotion_phrases),
            "output_style": self.output_style,
            "has_ai_service": self.ai_service_adapter is not None,
            "has_legacy": self.legacy_adapter is not None
        }
    
    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        加载模块状态
        
        Args:
            state: 状态信息
            
        Returns:
            加载是否成功
        """
        if "output_style" in state:
            self.output_style.update(state["output_style"])
        return True
    
    def _shutdown_module(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        # 取消事件订阅
        self.event_bus.unsubscribe("ai_service_adapter.ready", self._handle_ai_service_ready)
        self.event_bus.unsubscribe("legacy_adapter.ready", self._handle_legacy_adapter_ready)
        
        logger.info("响应生成器模块已关闭")
        return True
    
    def generate_response(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成对话响应
        
        Args:
            input_data: 输入数据
            
        Returns:
            响应结果
        """
        try:
            # 解析输入数据
            user_id = input_data.get("user_id")
            content = input_data.get("content", "")
            context = input_data.get("context", {})
            
            # 获取模板类型
            template_type = self._determine_template_type(content, context)
            
            # 获取情感状态
            emotion = context.get("emotion", {"type": "neutral", "intensity": 0.5})
            
            # 确定是否使用AI服务
            use_ai = self._should_use_ai_service(content, context)
            
            # 生成响应
            if use_ai and self.ai_service_adapter:
                # 使用AI服务生成响应
                response_text = self._generate_with_ai(content, context)
            else:
                # 使用模板生成响应
                response_text = self._generate_with_template(template_type, context)
            
            # 应用情感表达
            response_text = self._apply_emotion_expression(response_text, emotion)
            
            # 记录响应
            if self.legacy_adapter and getattr(self.legacy_adapter, 'legacy_available', False):
                self.legacy_adapter.add_message(user_id, "assistant", response_text)
            
            return {
                "success": True,
                "response": response_text,
                "template_type": template_type,
                "used_ai": use_ai
            }
            
        except Exception as e:
            logger.error_status(f"生成响应失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，我在生成响应时遇到了问题。"
            }
    
    def _determine_template_type(self, content: str, context: Dict[str, Any]) -> str:
        """
        确定使用的模板类型
        
        Args:
            content: 用户输入内容
            context: 上下文信息
            
        Returns:
            模板类型
        """
        # 获取意图分析结果
        intention = context.get("intention", {})
        intent_type = intention.get("type", "general")
        
        # 根据意图类型选择模板
        if intent_type == "greeting":
            return "greeting"
        elif intent_type == "farewell":
            return "farewell"
        elif intent_type == "question":
            return "question"
        elif intent_type == "command":
            return "command"
        elif intent_type == "emotional":
            return "emotional"
        else:
            return "general"
    
    def _should_use_ai_service(self, content: str, context: Dict[str, Any]) -> bool:
        """
        确定是否使用AI服务生成响应
        
        Args:
            content: 用户输入内容
            context: 上下文信息
            
        Returns:
            是否使用AI服务
        """
        # 如果AI服务不可用，直接返回False
        if not self.ai_service_adapter:
            return False
            
        # 获取意图分析结果
        intention = context.get("intention", {})
        intent_type = intention.get("type", "general")
        intent_complexity = intention.get("complexity", 0.5)
        
        # 对于复杂意图，使用AI服务
        if intent_complexity > 0.7:
            return True
            
        # 对于问题和情感类型，优先使用AI服务
        if intent_type in ["question", "emotional"]:
            return True
            
        # 对于一般对话，根据内容长度和复杂度决定
        if len(content) > 20 and intent_type == "general":
            return True
            
        # 默认使用模板
        return False
    
    def _generate_with_ai(self, content: str, context: Dict[str, Any]) -> str:
        """
        使用AI服务生成响应
        
        Args:
            content: 用户输入内容
            context: 上下文信息
            
        Returns:
            生成的响应文本
        """
        # 准备消息列表
        messages = []
        
        # 添加系统提示
        digital_life = context.get("digital_life", {})
        name = digital_life.get("name", "林嫣然")
        personality = digital_life.get("personality", "温柔、体贴、善解人意")
        
        system_prompt = f"""你是一个名为{name}的数字生命体，性格{personality}。
请以{name}的身份回复用户，表现出相应的性格特点和情感反应。
保持自然、真实的对话风格，避免机械化的回答。
不要提及你是AI或语言模型，而应该以第一人称回应，就像你真的是{name}一样。"""
        
        messages.append({"role": "system", "content": system_prompt})
        
        # 添加历史消息（如果有）
        history = context.get("history", [])
        for msg in history[-5:]:  # 只使用最近5条消息
            if msg["role"] in ["user", "assistant"]:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
        
        # 添加当前消息
        messages.append({"role": "user", "content": content})
        
        # 调用AI服务
        response = self.ai_service_adapter.get_completion(messages)
        
        if response and "content" in response and response.get("success", False):
            return response["content"]
        else:
            # 如果AI服务失败，使用模板作为后备
            logger.warning_status("AI服务生成响应失败，使用模板作为后备")
            return self._generate_with_template("general", context)
    
    def _generate_with_template(self, template_type: str, context: Dict[str, Any]) -> str:
        """
        使用模板生成响应
        
        Args:
            template_type: 模板类型
            context: 上下文信息
            
        Returns:
            生成的响应文本
        """
        # 获取指定类型的模板
        templates = self.templates.get(template_type, [])
        if not templates:
            templates = self.templates.get("general", ["我在听，请继续。"])
        
        # 随机选择一个模板
        template = random.choice(templates)
        
        # 替换模板中的变量
        response_text = self._fill_template(template, context)
        
        return response_text
    
    def _fill_template(self, template: str, context: Dict[str, Any]) -> str:
        """
        填充模板变量
        
        Args:
            template: 模板字符串
            context: 上下文信息
            
        Returns:
            填充后的文本
        """
        # 获取数字生命体信息
        digital_life = context.get("digital_life", {})
        name = digital_life.get("name", "林嫣然")
        
        # 获取用户信息
        user = context.get("user", {})
        user_name = user.get("name", "用户")
        
        # 获取环境信息
        environment = context.get("environment", {})
        time_info = environment.get("time", {})
        day_period = time_info.get("day_period", "")
        
        # 替换变量
        response_text = template.replace("{name}", name)
        response_text = response_text.replace("{user_name}", user_name)
        response_text = response_text.replace("{day_period}", day_period)
        
        # 替换其他变量
        for key, value in context.items():
            if isinstance(value, str):
                response_text = response_text.replace(f"{{{key}}}", value)
        
        return response_text
    
    def _apply_emotion_expression(self, text: str, emotion: Dict[str, Any]) -> str:
        """
        应用情感表达
        
        Args:
            text: 原始文本
            emotion: 情感状态
            
        Returns:
            应用情感表达后的文本
        """
        emotion_type = emotion.get("type", "neutral")
        intensity = emotion.get("intensity", 0.5)
        
        # 获取情感表达词组
        phrases = self.emotion_phrases.get(emotion_type, [])
        
        # 如果没有对应情感的词组或情感强度低，直接返回原文本
        if not phrases or intensity < 0.3:
            return text
        
        # 根据情感强度决定是否添加情感表达
        if random.random() < intensity:
            # 随机选择一个词组
            phrase = random.choice(phrases)
            
            # 根据情感强度决定词组放在开头还是结尾
            if intensity > 0.7 and random.random() < 0.7:
                # 情感强度高，更可能放在开头
                return f"{phrase}{text}"
            else:
                # 情感强度低到中等，更可能放在结尾
                return f"{text}{phrase}"
        
        return text
    
    def execute_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行行为动作
        
        Args:
            action_data: 行为动作数据
            
        Returns:
            执行结果
        """
        try:
            # 解析动作数据
            action_type = action_data.get("action_type", "")
            action_params = action_data.get("params", {})
            
            # 根据动作类型执行不同操作
            if action_type == "change_style":
                # 更改输出样式
                for key, value in action_params.items():
                    if key in self.output_style:
                        self.output_style[key] = value
                
                return {
                    "success": True,
                    "message": "输出样式已更新",
                    "current_style": self.output_style
                }
            
            elif action_type == "add_template":
                # 添加响应模板
                template_type = action_params.get("type", "general")
                template_text = action_params.get("text", "")
                
                if not template_text:
                    return {
                        "success": False,
                        "error": "模板文本不能为空"
                    }
                
                if template_type not in self.templates:
                    self.templates[template_type] = []
                
                self.templates[template_type].append(template_text)
                
                # 保存模板
                templates_path = os.path.join(root_dir, "configs", "templates", "response_templates.json")
                with open(templates_path, 'w', encoding='utf-8') as f:
                    json.dump(self.templates, f, ensure_ascii=False, indent=2)
                
                return {
                    "success": True,
                    "message": f"已添加{template_type}类型的响应模板"
                }
            
            else:
                return {
                    "success": False,
                    "error": f"不支持的动作类型: {action_type}"
                }
                
        except Exception as e:
            logger.error_status(f"执行动作失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_default_templates(self) -> Dict[str, List[str]]:
        """
        获取默认响应模板
        
        Returns:
            默认响应模板字典
        """
        return {
            "greeting": [
                "你好啊，{user_name}！今天过得怎么样？",
                "嗨，{user_name}！很高兴见到你。",
                "{day_period}好，{user_name}！有什么我能帮助你的吗？",
                "你好，{user_name}！我是{name}，很高兴和你聊天。"
            ],
            "farewell": [
                "再见，{user_name}！期待下次与你交流。",
                "下次见，{user_name}！祝你有美好的一天。",
                "再见啦，有需要随时找我聊天哦。",
                "回头见，{user_name}！"
            ],
            "question": [
                "这是个好问题，让我想想...",
                "关于这个问题，我的想法是...",
                "让我来回答你的问题...",
                "我理解你的问题，我认为..."
            ],
            "command": [
                "好的，我会按照你说的做。",
                "没问题，正在执行...",
                "收到，我这就去做。",
                "明白了，我会帮你完成这个任务。"
            ],
            "emotional": [
                "我能理解你的感受，{user_name}。",
                "听你这么说，我感到...",
                "你的心情我完全能体会。",
                "谢谢你与我分享你的感受。"
            ],
            "general": [
                "嗯，我明白了。",
                "我在听，请继续。",
                "有趣的想法！",
                "我理解你的意思了。",
                "让我们继续聊这个话题吧。"
            ]
        }
    
    def _get_default_emotion_phrases(self) -> Dict[str, List[str]]:
        """
        获取默认情感表达词库
        
        Returns:
            默认情感表达词库字典
        """
        return {
            "happy": [
                "(*^▽^*) ",
                "✧(≖ ◡ ≖✿) ",
                "(๑•̀ㅂ•́)و✧ ",
                "开心! ",
                "真高兴! "
            ],
            "sad": [
                "(｡•́︿•̀｡) ",
                "╥﹏╥... ",
                "(ノ_<。) ",
                "唉... ",
                "真遗憾... "
            ],
            "angry": [
                "(￣^￣) ",
                "ヽ(≧Д≦)ノ ",
                "(╯°□°）╯︵ ┻━┻ ",
                "哼! ",
                "真是太让人生气了! "
            ],
            "surprised": [
                "∑(O_O;) ",
                "Σ(っ °Д °;)っ ",
                "⊙▽⊙ ",
                "哇! ",
                "真没想到! "
            ],
            "confused": [
                "(・・? ",
                "¯\\_(ツ)_/¯ ",
                "(⊙_⊙)？ ",
                "嗯？ ",
                "我有点迷惑... "
            ],
            "excited": [
                "ヾ(≧▽≦*)o ",
                "o(≧▽≦)o ",
                "ヽ(°◇° )ノ ",
                "太棒了! ",
                "我太激动了! "
            ],
            "neutral": [
                "",
                "",
                "",
                "",
                ""
            ]
        }


def get_instance(config: Dict[str, Any] = None) -> ResponseGenerator:
    """
    获取响应生成器模块实例
    
    Args:
        config: 配置信息
        
    Returns:
        响应生成器模块实例
    """
    return ResponseGenerator() 