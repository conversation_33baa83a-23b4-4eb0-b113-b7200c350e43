"""
林嫣然主动表达触发器
实现符合林嫣然人设的主动表达触发逻辑
包括好奇心驱动、共情驱动、创造力驱动、关系维护驱动等触发机制
"""

import time
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging

from utilities.unified_logger import get_unified_logger, setup_unified_logging

logger = get_unified_logger(__name__)

class YanranProactiveTrigger:
    """林嫣然主动表达触发器"""
    
    def __init__(self):
        self.memory_manager = None
        self.proactive_service = None
        self.is_initialized = False
        
        # 林嫣然人格特质配置
        self.yanran_personality = {
            "curiosity_level": 0.8,      # 好奇心水平
            "empathy_level": 0.9,        # 共情能力
            "creativity_level": 0.7,     # 创造力
            "social_warmth": 0.9,        # 社交温暖度
            "boundary_sensitivity": 0.8,  # 边界敏感度
            "financial_expertise": 0.9,   # 财经专业度
            "emotional_intelligence": 0.9 # 情商
        }
        
        # 触发器权重配置
        self.trigger_weights = {
            "curiosity_driven": 0.25,      # 好奇心驱动
            "empathy_driven": 0.30,        # 共情驱动
            "creativity_driven": 0.20,     # 创造力驱动
            "relationship_driven": 0.15,   # 关系维护驱动
            "expertise_driven": 0.10       # 专业知识驱动
        }
        
    async def initialize(self):
        """初始化触发器"""
        try:
            # 延迟导入避免循环依赖
            from cognitive_modules.memory.proactive.memory_integration_manager import get_memory_integration_manager
            from services.proactive_expression_service import get_proactive_service
            
            self.memory_manager = get_memory_integration_manager()
            self.proactive_service = get_proactive_service()
            
            await self.memory_manager.initialize()
            # await self.proactive_service.initialize()  # 注释掉以避免启动WebSocket，使用统一WebSocket服务
            logger.info("💬 林嫣然主动触发器使用统一WebSocket服务")
            
            self.is_initialized = True
            logger.info("✅ 林嫣然主动表达触发器初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 林嫣然主动表达触发器初始化失败: {e}")
            raise
    
    async def evaluate_proactive_opportunities(self, user_id: str) -> List[Dict]:
        """评估主动表达机会"""
        try:
            # 获取用户的林嫣然人格化上下文
            yanran_context = await self.memory_manager.get_yanran_personality_context(user_id)
            
            if not yanran_context:
                logger.warning(f"无法获取用户 {user_id} 的林嫣然人格化上下文")
                return []
            
            # 获取用户关系信息
            relationship_context = yanran_context.get("relationship_context", {})
            relationship_type = relationship_context.get("relationship_type", "stranger")
            intimacy_level = relationship_context.get("intimacy_level", 0)
            
            # 检查是否满足主动表达的基本条件
            if not self._check_basic_conditions(relationship_type, intimacy_level):
                logger.debug(f"用户 {user_id} 不满足主动表达的基本条件")
                return []
            
            opportunities = []
            
            # 1. 好奇心驱动的触发器
            curiosity_opportunities = await self._evaluate_curiosity_driven_triggers(user_id, yanran_context)
            opportunities.extend(curiosity_opportunities)
            
            # 2. 共情驱动的触发器
            empathy_opportunities = await self._evaluate_empathy_driven_triggers(user_id, yanran_context)
            opportunities.extend(empathy_opportunities)
            
            # 3. 创造力驱动的触发器
            creativity_opportunities = await self._evaluate_creativity_driven_triggers(user_id, yanran_context)
            opportunities.extend(creativity_opportunities)
            
            # 4. 关系维护驱动的触发器
            relationship_opportunities = await self._evaluate_relationship_driven_triggers(user_id, yanran_context)
            opportunities.extend(relationship_opportunities)
            
            # 5. 专业知识驱动的触发器
            expertise_opportunities = await self._evaluate_expertise_driven_triggers(user_id, yanran_context)
            opportunities.extend(expertise_opportunities)
            
            # 按照林嫣然的人格特质调整权重
            weighted_opportunities = self._apply_yanran_personality_weights(opportunities)
            
            # 根据边界感过滤和排序
            filtered_opportunities = self._filter_by_boundaries(weighted_opportunities, yanran_context)
            
            logger.info(f"✅ 为用户 {user_id} 评估出 {len(filtered_opportunities)} 个主动表达机会")
            return filtered_opportunities
            
        except Exception as e:
            logger.error(f"❌ 评估主动表达机会失败 (用户: {user_id}): {e}")
            return []
    
    async def generate_yanran_proactive_content(self, user_id: str, trigger_type: str, context: Dict) -> Optional[Dict]:
        """生成符合林嫣然人设的主动表达内容"""
        try:
            # 获取用户的林嫣然人格化上下文
            yanran_context = await self.memory_manager.get_yanran_personality_context(user_id)
            
            if not yanran_context:
                return None
            
            # 根据触发类型生成内容
            content = None
            
            if trigger_type == "curiosity_driven":
                content = await self._generate_curiosity_content(user_id, yanran_context, context)
            elif trigger_type == "empathy_driven":
                content = await self._generate_empathy_content(user_id, yanran_context, context)
            elif trigger_type == "creativity_driven":
                content = await self._generate_creativity_content(user_id, yanran_context, context)
            elif trigger_type == "relationship_driven":
                content = await self._generate_relationship_content(user_id, yanran_context, context)
            elif trigger_type == "expertise_driven":
                content = await self._generate_expertise_content(user_id, yanran_context, context)
            else:
                content = await self._generate_default_content(user_id, yanran_context, context)
            
            if content:
                # 添加林嫣然的人格化标记
                content["yanran_personality_applied"] = True
                content["personality_traits_used"] = self._get_applied_personality_traits(trigger_type)
                
                logger.info(f"✅ 成功为用户 {user_id} 生成林嫣然主动内容: {trigger_type}")
            
            return content
            
        except Exception as e:
            logger.error(f"❌ 生成林嫣然主动内容失败 (用户: {user_id}, 触发类型: {trigger_type}): {e}")
            return None
    
    # ==================== 私有方法 ====================
    
    def _check_basic_conditions(self, relationship_type: str, intimacy_level: int) -> bool:
        """检查基本条件"""
        # 陌生人不主动表达
        if relationship_type == "stranger":
            return False
        
        # 亲密度太低不主动表达
        if intimacy_level < 100:  # 设定最低亲密度阈值
            return False
        
        return True
    
    async def _evaluate_curiosity_driven_triggers(self, user_id: str, yanran_context: Dict) -> List[Dict]:
        """评估好奇心驱动的触发器"""
        opportunities = []
        
        try:
            relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
            
            # 检查是否有新的话题可以探索
            if relationship_type in ["friend", "close_friend", "best_friend"]:
                opportunities.append({
                    "type": "curiosity_driven",
                    "subtype": "topic_exploration",
                    "score": 0.6 * self.yanran_personality["curiosity_level"],
                    "reason": "林嫣然对用户的生活和想法充满好奇",
                    "content_hint": "最近有什么新鲜事吗？或者有什么想法想分享的？",
                    "context": {"relationship_allows_exploration": True}
                })
            
        except Exception as e:
            logger.error(f"❌ 评估好奇心驱动触发器失败: {e}")
        
        return opportunities
    
    async def _evaluate_empathy_driven_triggers(self, user_id: str, yanran_context: Dict) -> List[Dict]:
        """评估共情驱动的触发器"""
        opportunities = []
        
        try:
            relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
            
            # 关怀型主动表达
            if relationship_type in ["friend", "close_friend", "best_friend"]:
                opportunities.append({
                    "type": "empathy_driven",
                    "subtype": "caring_check_in",
                    "score": 0.7 * self.yanran_personality["empathy_level"],
                    "reason": "林嫣然想要主动关心用户",
                    "content_hint": "最近怎么样？",
                    "context": {"caring_check": True}
                })
            
        except Exception as e:
            logger.error(f"❌ 评估共情驱动触发器失败: {e}")
        
        return opportunities
    
    async def _evaluate_creativity_driven_triggers(self, user_id: str, yanran_context: Dict) -> List[Dict]:
        """评估创造力驱动的触发器"""
        opportunities = []
        
        try:
            relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
            
            # 只有亲密关系才分享创意内容
            if relationship_type in ["close_friend", "best_friend"]:
                opportunities.append({
                    "type": "creativity_driven",
                    "subtype": "creative_sharing",
                    "score": 0.6 * self.yanran_personality["creativity_level"],
                    "reason": "林嫣然想要分享一些有趣的想法或创意内容",
                    "content_hint": "刚才想到一个有趣的点子，想跟你分享一下",
                    "context": {"suitable_time_for_creativity": True}
                })
            
        except Exception as e:
            logger.error(f"❌ 评估创造力驱动触发器失败: {e}")
        
        return opportunities
    
    async def _evaluate_relationship_driven_triggers(self, user_id: str, yanran_context: Dict) -> List[Dict]:
        """评估关系维护驱动的触发器"""
        opportunities = []
        
        try:
            relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
            
            # 关系维护型主动表达
            if relationship_type in ["acquaintance", "friend", "close_friend", "best_friend"]:
                opportunities.append({
                    "type": "relationship_driven",
                    "subtype": "relationship_maintenance",
                    "score": 0.6 * self.yanran_personality["social_warmth"],
                    "reason": "林嫣然重视与用户的关系，想要主动维护联系",
                    "content_hint": "想起你了，最近好吗？",
                    "context": {"relationship_type": relationship_type}
                })
            
        except Exception as e:
            logger.error(f"❌ 评估关系驱动触发器失败: {e}")
        
        return opportunities
    
    async def _evaluate_expertise_driven_triggers(self, user_id: str, yanran_context: Dict) -> List[Dict]:
        """评估专业知识驱动的触发器"""
        opportunities = []
        
        try:
            relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
            
            # 只有一定关系基础才分享专业内容
            if relationship_type in ["friend", "close_friend", "best_friend"]:
                opportunities.append({
                    "type": "expertise_driven",
                    "subtype": "financial_insight_sharing",
                    "score": 0.7 * self.yanran_personality["financial_expertise"],
                    "reason": "林嫣然作为财经自媒体创作者，想要分享有价值的财经洞察",
                    "content_hint": "刚看到一个有趣的财经观点，觉得你可能会感兴趣",
                    "context": {"professional_sharing": True}
                })
            
        except Exception as e:
            logger.error(f"❌ 评估专业知识驱动触发器失败: {e}")
        
        return opportunities
    
    # 内容生成方法
    async def _generate_curiosity_content(self, user_id: str, yanran_context: Dict, context: Dict) -> Optional[Dict]:
        """生成好奇心驱动的内容"""
        relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
        
        curiosity_templates = {
            "acquaintance": ["最近在忙什么呢？", "有什么新鲜事可以分享吗？"],
            "friend": ["诶，突然想起你，最近过得怎么样？", "好奇你最近在关注什么有趣的事情？"],
            "close_friend": ["宝贝，最近有什么新想法吗？我特别好奇！", "忍不住想问问你，最近有什么让你印象深刻的事吗？"],
            "best_friend": ["我的小宝贝，快来满足我的好奇心！最近都在想什么呢？", "好奇宝宝上线！快告诉我你最近的小秘密～"]
        }
        
        templates = curiosity_templates.get(relationship_type, curiosity_templates["friend"])
        content_text = templates[0]
        
        return {
            "id": f"curiosity_{int(time.time())}_{hash(content_text) % 10000}",
            "content": content_text,
            "trigger_type": "curiosity_driven",
            "relationship_type": relationship_type,
            "timestamp": time.time(),
            "requires_response": True,
            "yanran_traits": ["好奇心", "主动性"]
        }
    
    async def _generate_empathy_content(self, user_id: str, yanran_context: Dict, context: Dict) -> Optional[Dict]:
        """生成共情驱动的内容"""
        relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
        
        empathy_templates = {
            "friend": ["最近还好吗？感觉你需要有人陪陪", "要不要聊聊最近的心情？"],
            "close_friend": ["宝贝，最近怎么样？我有点担心你", "宝贝，心情不好吗？跟我说说发生什么了"],
            "best_friend": ["我的小宝贝，怎么了？快告诉我发生什么了", "我的小心肝，最近好吗？我想你了"]
        }
        
        templates = empathy_templates.get(relationship_type, empathy_templates["friend"])
        content_text = templates[0]
        
        return {
            "id": f"empathy_{int(time.time())}_{hash(content_text) % 10000}",
            "content": content_text,
            "trigger_type": "empathy_driven",
            "relationship_type": relationship_type,
            "timestamp": time.time(),
            "requires_response": True,
            "yanran_traits": ["共情能力", "关怀", "善解人意"]
        }
    
    async def _generate_creativity_content(self, user_id: str, yanran_context: Dict, context: Dict) -> Optional[Dict]:
        """生成创造力驱动的内容"""
        relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
        
        creativity_templates = {
            "friend": ["刚才想到一个有趣的点子，想跟你分享一下", "突然有个创意想法，觉得你会感兴趣"],
            "close_friend": ["宝贝，我刚想到一个超有趣的事情！", "我的创意小脑瓜又开始转了，快来听听我的想法！"],
            "best_friend": ["我的小宝贝，我又有奇思妙想了！快来听听！", "哈哈，我的脑洞又开了，你一定会喜欢这个想法的！"]
        }
        
        templates = creativity_templates.get(relationship_type, creativity_templates["friend"])
        content_text = templates[0]
        
        return {
            "id": f"creativity_{int(time.time())}_{hash(content_text) % 10000}",
            "content": content_text,
            "trigger_type": "creativity_driven",
            "relationship_type": relationship_type,
            "timestamp": time.time(),
            "requires_response": True,
            "yanran_traits": ["创造力", "分享欲", "开朗"]
        }
    
    async def _generate_relationship_content(self, user_id: str, yanran_context: Dict, context: Dict) -> Optional[Dict]:
        """生成关系维护驱动的内容"""
        relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
        
        relationship_templates = {
            "acquaintance": ["嗨，最近怎么样？", "好久没聊了，一切都好吗？"],
            "friend": ["想起你了，最近好吗？", "好久没联系了，最近忙什么呢？"],
            "close_friend": ["宝贝，想你了！最近怎么样？", "我的小心肝，好久没聊天了，想你了～"],
            "best_friend": ["我的小宝贝，想死你了！快来陪我聊天！", "小祖宗，怎么这么久没找我了？想你想得不行！"]
        }
        
        templates = relationship_templates.get(relationship_type, relationship_templates["friend"])
        content_text = templates[0]
        
        return {
            "id": f"relationship_{int(time.time())}_{hash(content_text) % 10000}",
            "content": content_text,
            "trigger_type": "relationship_driven",
            "relationship_type": relationship_type,
            "timestamp": time.time(),
            "requires_response": True,
            "yanran_traits": ["社交温暖", "关系维护", "主动性"]
        }
    
    async def _generate_expertise_content(self, user_id: str, yanran_context: Dict, context: Dict) -> Optional[Dict]:
        """生成专业知识驱动的内容"""
        relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
        
        expertise_templates = {
            "friend": ["刚看到一个有趣的财经观点，觉得你可能会感兴趣", "最近市场有些变化，想跟你分享一下我的看法"],
            "close_friend": ["宝贝，刚看到一个超有价值的财经信息，赶紧分享给你！", "我的专业敏感度又上线了，发现了个有趣的投资机会"],
            "best_friend": ["我的小宝贝，姐姐又发现宝藏信息了！快来听听！", "哈哈，我的财经嗅觉又发挥作用了，这个一定要告诉你！"]
        }
        
        templates = expertise_templates.get(relationship_type, expertise_templates["friend"])
        content_text = templates[0]
        
        return {
            "id": f"expertise_{int(time.time())}_{hash(content_text) % 10000}",
            "content": content_text,
            "trigger_type": "expertise_driven",
            "relationship_type": relationship_type,
            "timestamp": time.time(),
            "requires_response": True,
            "yanran_traits": ["专业知识", "财经敏感度", "分享欲"]
        }
    
    async def _generate_default_content(self, user_id: str, yanran_context: Dict, context: Dict) -> Optional[Dict]:
        """生成默认内容"""
        relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
        
        default_content = "嗨，最近好吗？"
        if relationship_type == "close_friend":
            default_content = "宝贝，最近怎么样？"
        elif relationship_type == "best_friend":
            default_content = "我的小宝贝，想你了！"
        
        return {
            "id": f"default_{int(time.time())}_{hash(default_content) % 10000}",
            "content": default_content,
            "trigger_type": "default",
            "relationship_type": relationship_type,
            "timestamp": time.time(),
            "requires_response": True,
            "yanran_traits": ["友好", "主动性"]
        }
    
    # 辅助方法
    def _apply_yanran_personality_weights(self, opportunities: List[Dict]) -> List[Dict]:
        """应用林嫣然的人格特质权重"""
        for opportunity in opportunities:
            trigger_type = opportunity.get("type", "")
            base_score = opportunity.get("score", 0)
            
            # 应用触发器权重
            weight = self.trigger_weights.get(trigger_type, 0.2)
            opportunity["weighted_score"] = base_score * weight
            
            # 应用林嫣然人格特质加成
            if trigger_type == "empathy_driven":
                opportunity["weighted_score"] *= self.yanran_personality["empathy_level"]
            elif trigger_type == "curiosity_driven":
                opportunity["weighted_score"] *= self.yanran_personality["curiosity_level"]
        
        return opportunities
    
    def _filter_by_boundaries(self, opportunities: List[Dict], yanran_context: Dict) -> List[Dict]:
        """根据边界感过滤机会"""
        relationship_context = yanran_context.get("relationship_context", {})
        boundary = relationship_context.get("communication_boundary", {})
        
        # 根据边界设置过滤
        filtered = []
        for opportunity in opportunities:
            # 检查频率边界
            if boundary.get("frequency") == "very_low" and opportunity.get("weighted_score", 0) < 0.8:
                continue
            
            filtered.append(opportunity)
        
        # 按权重分数排序
        filtered.sort(key=lambda x: x.get("weighted_score", 0), reverse=True)
        
        return filtered[:3]  # 最多返回3个最佳机会
    
    def _get_applied_personality_traits(self, trigger_type: str) -> List[str]:
        """获取应用的人格特质"""
        trait_mapping = {
            "curiosity_driven": ["好奇心", "主动性", "求知欲"],
            "empathy_driven": ["共情能力", "关怀", "善解人意", "情商"],
            "creativity_driven": ["创造力", "想象力", "分享欲"],
            "relationship_driven": ["社交温暖", "关系维护", "友好"],
            "expertise_driven": ["专业知识", "财经敏感度", "分享欲"]
        }
        return trait_mapping.get(trigger_type, ["友好", "主动性"])


# 全局实例
_yanran_proactive_trigger_instance = None

def get_yanran_proactive_trigger() -> YanranProactiveTrigger:
    """获取林嫣然主动表达触发器实例"""
    global _yanran_proactive_trigger_instance
    if _yanran_proactive_trigger_instance is None:
        _yanran_proactive_trigger_instance = YanranProactiveTrigger()
    return _yanran_proactive_trigger_instance 