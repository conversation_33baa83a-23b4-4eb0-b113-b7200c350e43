"""
林嫣然智能触发引擎 - Phase 3
实现基于机器学习的智能触发机制，包括用户行为模式分析、个性化触发策略、触发效果评估等高级功能
"""

import time
import json
import asyncio
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
import logging

from utilities.unified_logger import get_unified_logger, setup_unified_logging

logger = get_unified_logger(__name__)

@dataclass
class TriggerScore:
    """触发评分数据结构"""
    trigger_type: str
    base_score: float
    ml_score: float
    behavioral_score: float
    contextual_score: float
    final_score: float
    confidence: float
    reasons: List[str]

@dataclass
class UserBehaviorPattern:
    """用户行为模式数据结构"""
    user_id: str
    active_hours: List[int]  # 活跃小时
    response_rate: float     # 响应率
    preferred_topics: List[str]  # 偏好话题
    emotional_patterns: Dict[str, float]  # 情感模式
    interaction_frequency: float  # 互动频率
    last_updated: float

class IntelligentTriggerEngine:
    """智能触发引擎"""
    
    def __init__(self):
        self.memory_manager = None
        self.yanran_trigger = None
        self.enhanced_memory = None
        self.is_initialized = False
        
        # 用户行为模式缓存
        self.user_patterns: Dict[str, UserBehaviorPattern] = {}
        
        # 触发历史记录
        self.trigger_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 效果评估数据
        self.effectiveness_data: Dict[str, List[Dict]] = defaultdict(list)
        
        # 机器学习模型参数
        self.ml_weights = {
            "time_sensitivity": 0.2,
            "emotional_context": 0.25,
            "behavioral_pattern": 0.3,
            "relationship_depth": 0.15,
            "content_relevance": 0.1
        }
        
        # 个性化触发策略
        self.personalization_factors = {
            "response_history": 0.3,
            "topic_preference": 0.25,
            "timing_preference": 0.2,
            "emotional_state": 0.15,
            "relationship_growth": 0.1
        }
        
    async def initialize(self):
        """初始化智能触发引擎"""
        try:
            # 延迟导入避免循环依赖
            from cognitive_modules.memory.proactive.memory_integration_manager import get_memory_integration_manager
            from cognitive_modules.behavior.proactive.yanran_proactive_trigger import get_yanran_proactive_trigger
            from cognitive_modules.memory.proactive.enhanced_memory_integration import EnhancedMemoryIntegration
            
            self.memory_manager = get_memory_integration_manager()
            self.yanran_trigger = get_yanran_proactive_trigger()
            self.enhanced_memory = EnhancedMemoryIntegration()
            
            await self.memory_manager.initialize()
            await self.yanran_trigger.initialize()
            await self.enhanced_memory.initialize()
            
            # 加载用户行为模式
            await self._load_user_behavior_patterns()
            
            self.is_initialized = True
            logger.info("✅ 智能触发引擎初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 智能触发引擎初始化失败: {e}")
            raise
    
    async def evaluate_intelligent_triggers(self, user_id: str) -> List[TriggerScore]:
        """智能评估触发机会"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # 1. 获取基础触发机会
            base_opportunities = await self.yanran_trigger.evaluate_proactive_opportunities(user_id)
            
            if not base_opportunities:
                logger.debug(f"用户 {user_id} 无基础触发机会")
                return []
            
            # 2. 获取用户行为模式
            user_pattern = await self._get_user_behavior_pattern(user_id)
            
            # 3. 获取增强记忆上下文
            enhanced_context = await self.enhanced_memory.get_enhanced_user_memory(user_id)
            
            # 4. 应用机器学习评分
            intelligent_scores = []
            
            for opportunity in base_opportunities:
                # 计算智能触发评分
                trigger_score = await self._calculate_intelligent_score(
                    user_id, opportunity, user_pattern, enhanced_context
                )
                
                if trigger_score and trigger_score.final_score > 0.4:  # 最低触发阈值
                    intelligent_scores.append(trigger_score)
            
            # 5. 按最终评分排序
            intelligent_scores.sort(key=lambda x: x.final_score, reverse=True)
            
            # 6. 记录触发评估
            await self._record_trigger_evaluation(user_id, intelligent_scores)
            
            logger.info(f"✅ 为用户 {user_id} 智能评估出 {len(intelligent_scores)} 个触发机会")
            return intelligent_scores[:3]  # 返回最佳3个
            
        except Exception as e:
            logger.error(f"❌ 智能触发评估失败 (用户: {user_id}): {e}")
            return []
    
    async def analyze_user_behavior_patterns(self, user_id: str) -> UserBehaviorPattern:
        """分析用户行为模式"""
        try:
            # 获取用户历史互动数据
            interaction_history = await self._get_user_interaction_history(user_id)
            
            if not interaction_history:
                # 创建默认模式
                return UserBehaviorPattern(
                    user_id=user_id,
                    active_hours=[9, 10, 14, 15, 19, 20],  # 默认活跃时间
                    response_rate=0.5,
                    preferred_topics=[],
                    emotional_patterns={},
                    interaction_frequency=0.3,
                    last_updated=time.time()
                )
            
            # 分析活跃时间
            active_hours = self._analyze_active_hours(interaction_history)
            
            # 分析响应率
            response_rate = self._analyze_response_rate(interaction_history)
            
            # 分析偏好话题
            preferred_topics = self._analyze_preferred_topics(interaction_history)
            
            # 分析情感模式
            emotional_patterns = self._analyze_emotional_patterns(interaction_history)
            
            # 分析互动频率
            interaction_frequency = self._analyze_interaction_frequency(interaction_history)
            
            user_pattern = UserBehaviorPattern(
                user_id=user_id,
                active_hours=active_hours,
                response_rate=response_rate,
                preferred_topics=preferred_topics,
                emotional_patterns=emotional_patterns,
                interaction_frequency=interaction_frequency,
                last_updated=time.time()
            )
            
            # 缓存用户模式
            self.user_patterns[user_id] = user_pattern
            
            logger.info(f"✅ 成功分析用户 {user_id} 的行为模式")
            return user_pattern
            
        except Exception as e:
            logger.error(f"❌ 分析用户行为模式失败 (用户: {user_id}): {e}")
            return None
    
    async def generate_personalized_trigger_strategy(self, user_id: str) -> Dict:
        """生成个性化触发策略"""
        try:
            # 获取用户行为模式
            user_pattern = await self._get_user_behavior_pattern(user_id)
            
            if not user_pattern:
                logger.warning(f"无法获取用户 {user_id} 的行为模式")
                return {}
            
            # 获取用户关系上下文
            yanran_context = await self.memory_manager.get_yanran_personality_context(user_id)
            relationship_type = yanran_context.get("relationship_context", {}).get("relationship_type", "stranger")
            intimacy_level = yanran_context.get("relationship_context", {}).get("intimacy_level", 0)
            
            # 生成个性化策略
            strategy = {
                "user_id": user_id,
                "relationship_type": relationship_type,
                "intimacy_level": intimacy_level,
                "optimal_timing": self._calculate_optimal_timing(user_pattern),
                "preferred_triggers": self._calculate_preferred_triggers(user_pattern),
                "content_personalization": self._calculate_content_personalization(user_pattern),
                "frequency_adjustment": self._calculate_frequency_adjustment(user_pattern, relationship_type),
                "emotional_adaptation": self._calculate_emotional_adaptation(user_pattern),
                "confidence_score": self._calculate_strategy_confidence(user_pattern),
                "last_updated": time.time()
            }
            
            logger.info(f"✅ 成功生成用户 {user_id} 的个性化触发策略")
            return strategy
            
        except Exception as e:
            logger.error(f"❌ 生成个性化触发策略失败 (用户: {user_id}): {e}")
            return {}
    
    async def evaluate_trigger_effectiveness(self, user_id: str, trigger_id: str, user_response: Dict) -> Dict:
        """评估触发效果"""
        try:
            # 记录用户响应
            response_data = {
                "trigger_id": trigger_id,
                "user_id": user_id,
                "timestamp": time.time(),
                "response_type": user_response.get("type", "unknown"),
                "response_content": user_response.get("content", ""),
                "response_sentiment": user_response.get("sentiment", 0.0),
                "response_engagement": user_response.get("engagement", 0.0),
                "response_time": user_response.get("response_time", 0)
            }
            
            # 计算效果评分
            effectiveness_score = self._calculate_effectiveness_score(response_data)
            
            # 更新效果数据
            self.effectiveness_data[user_id].append({
                **response_data,
                "effectiveness_score": effectiveness_score
            })
            
            # 如果数据过多，保留最近100条
            if len(self.effectiveness_data[user_id]) > 100:
                self.effectiveness_data[user_id] = self.effectiveness_data[user_id][-100:]
            
            # 更新机器学习权重
            await self._update_ml_weights(user_id, effectiveness_score, response_data)
            
            # 更新用户行为模式
            await self._update_user_behavior_pattern(user_id, response_data)
            
            evaluation_result = {
                "trigger_id": trigger_id,
                "user_id": user_id,
                "effectiveness_score": effectiveness_score,
                "response_quality": self._evaluate_response_quality(response_data),
                "learning_impact": self._calculate_learning_impact(user_id, effectiveness_score),
                "strategy_adjustment": await self._suggest_strategy_adjustment(user_id, effectiveness_score)
            }
            
            logger.info(f"✅ 触发效果评估完成 (用户: {user_id}, 触发: {trigger_id}, 评分: {effectiveness_score:.2f})")
            return evaluation_result
            
        except Exception as e:
            logger.error(f"❌ 触发效果评估失败 (用户: {user_id}, 触发: {trigger_id}): {e}")
            return {}
    
    # ==================== 私有方法 ====================
    
    async def _calculate_intelligent_score(self, user_id: str, opportunity: Dict, 
                                         user_pattern: UserBehaviorPattern, 
                                         enhanced_context: Dict) -> Optional[TriggerScore]:
        """计算智能触发评分"""
        try:
            trigger_type = opportunity.get("type", "unknown")
            base_score = opportunity.get("score", 0.0)
            
            # 1. 机器学习评分
            ml_score = await self._calculate_ml_score(user_id, opportunity, user_pattern, enhanced_context)
            
            # 2. 行为模式评分
            behavioral_score = self._calculate_behavioral_score(opportunity, user_pattern)
            
            # 3. 上下文评分
            contextual_score = self._calculate_contextual_score(opportunity, enhanced_context)
            
            # 4. 计算最终评分
            final_score = (
                base_score * 0.3 +
                ml_score * 0.3 +
                behavioral_score * 0.25 +
                contextual_score * 0.15
            )
            
            # 5. 计算置信度
            confidence = self._calculate_confidence(base_score, ml_score, behavioral_score, contextual_score)
            
            # 6. 生成评分原因
            reasons = self._generate_score_reasons(opportunity, user_pattern, enhanced_context, 
                                                 base_score, ml_score, behavioral_score, contextual_score)
            
            return TriggerScore(
                trigger_type=trigger_type,
                base_score=base_score,
                ml_score=ml_score,
                behavioral_score=behavioral_score,
                contextual_score=contextual_score,
                final_score=final_score,
                confidence=confidence,
                reasons=reasons
            )
            
        except Exception as e:
            logger.error(f"❌ 计算智能触发评分失败: {e}")
            return None
    
    async def _calculate_ml_score(self, user_id: str, opportunity: Dict, 
                                user_pattern: UserBehaviorPattern, enhanced_context: Dict) -> float:
        """计算机器学习评分"""
        try:
            # 获取当前时间特征
            current_hour = datetime.now().hour
            time_score = 1.0 if current_hour in user_pattern.active_hours else 0.3
            
            # 情感上下文特征
            emotional_context = enhanced_context.get("emotional_context", {})
            emotion_score = self._evaluate_emotional_compatibility(opportunity, emotional_context)
            
            # 行为模式匹配度
            behavioral_match = self._calculate_behavioral_match(opportunity, user_pattern)
            
            # 关系深度影响
            relationship_context = enhanced_context.get("relationship_context", {})
            relationship_score = self._calculate_relationship_compatibility(opportunity, relationship_context)
            
            # 内容相关性
            content_relevance = self._calculate_content_relevance(opportunity, enhanced_context)
            
            # 加权计算ML评分
            ml_score = (
                time_score * self.ml_weights["time_sensitivity"] +
                emotion_score * self.ml_weights["emotional_context"] +
                behavioral_match * self.ml_weights["behavioral_pattern"] +
                relationship_score * self.ml_weights["relationship_depth"] +
                content_relevance * self.ml_weights["content_relevance"]
            )
            
            return min(1.0, max(0.0, ml_score))
            
        except Exception as e:
            logger.error(f"❌ 计算ML评分失败: {e}")
            return 0.5
    
    def _calculate_behavioral_score(self, opportunity: Dict, user_pattern: UserBehaviorPattern) -> float:
        """计算行为模式评分"""
        try:
            score = 0.0
            
            # 响应率影响
            score += user_pattern.response_rate * 0.4
            
            # 话题偏好影响
            trigger_type = opportunity.get("type", "")
            if trigger_type in user_pattern.preferred_topics:
                score += 0.3
            
            # 互动频率影响
            score += min(user_pattern.interaction_frequency, 1.0) * 0.3
            
            return min(1.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"❌ 计算行为模式评分失败: {e}")
            return 0.5
    
    def _calculate_contextual_score(self, opportunity: Dict, enhanced_context: Dict) -> float:
        """计算上下文评分"""
        try:
            score = 0.0
            
            # 记忆上下文相关性
            memory_context = enhanced_context.get("memory_context", {})
            if memory_context:
                score += 0.4
            
            # 关系上下文适配性
            relationship_context = enhanced_context.get("relationship_context", {})
            intimacy_level = relationship_context.get("intimacy_level", 0)
            
            # 根据亲密度调整评分
            if intimacy_level > 10000:
                score += 0.4
            elif intimacy_level > 1000:
                score += 0.3
            elif intimacy_level > 100:
                score += 0.2
            
            # 时机适宜性
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 22:  # 活跃时间段
                score += 0.2
            
            return min(1.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"❌ 计算上下文评分失败: {e}")
            return 0.5
    
    def _calculate_confidence(self, base_score: float, ml_score: float, 
                            behavioral_score: float, contextual_score: float) -> float:
        """计算置信度"""
        try:
            # 评分一致性检查
            scores = [base_score, ml_score, behavioral_score, contextual_score]
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            
            # 标准差越小，置信度越高
            consistency = max(0.0, 1.0 - std_score)
            
            # 平均分越高，置信度越高
            magnitude = mean_score
            
            # 综合置信度
            confidence = (consistency * 0.6 + magnitude * 0.4)
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            logger.error(f"❌ 计算置信度失败: {e}")
            return 0.5
    
    def _generate_score_reasons(self, opportunity: Dict, user_pattern: UserBehaviorPattern, 
                              enhanced_context: Dict, base_score: float, ml_score: float, 
                              behavioral_score: float, contextual_score: float) -> List[str]:
        """生成评分原因"""
        reasons = []
        
        try:
            # 基础评分原因
            if base_score > 0.7:
                reasons.append(f"基础触发条件优秀 ({base_score:.2f})")
            elif base_score > 0.5:
                reasons.append(f"基础触发条件良好 ({base_score:.2f})")
            
            # ML评分原因
            if ml_score > 0.7:
                reasons.append(f"机器学习模型预测积极 ({ml_score:.2f})")
            elif ml_score < 0.3:
                reasons.append(f"机器学习模型预测消极 ({ml_score:.2f})")
            
            # 行为模式原因
            if behavioral_score > 0.6:
                reasons.append(f"用户行为模式匹配度高 ({behavioral_score:.2f})")
            
            # 上下文原因
            if contextual_score > 0.6:
                reasons.append(f"当前上下文适宜 ({contextual_score:.2f})")
            
            # 时机原因
            current_hour = datetime.now().hour
            if current_hour in user_pattern.active_hours:
                reasons.append(f"当前时间({current_hour}点)是用户活跃时段")
            
            return reasons
            
        except Exception as e:
            logger.error(f"❌ 生成评分原因失败: {e}")
            return ["评分计算完成"]
    
    async def _get_user_behavior_pattern(self, user_id: str) -> Optional[UserBehaviorPattern]:
        """获取用户行为模式"""
        try:
            # 先从缓存获取
            if user_id in self.user_patterns:
                pattern = self.user_patterns[user_id]
                # 检查是否需要更新（24小时更新一次）
                if time.time() - pattern.last_updated < 24 * 3600:
                    return pattern
            
            # 重新分析用户行为模式
            return await self.analyze_user_behavior_patterns(user_id)
            
        except Exception as e:
            logger.error(f"❌ 获取用户行为模式失败 (用户: {user_id}): {e}")
            return None
    
    async def _get_user_interaction_history(self, user_id: str) -> List[Dict]:
        """获取用户互动历史"""
        try:
            # 检查Redis缓存
            if self.redis_connector:
                recent_key = f"recent_interaction_history:{user_id}"
                recent_data = self.redis_connector.get(recent_key)
                
                if recent_data:
                    return recent_data.get("interactions", [])
            
            return []
            
        except Exception as e:
            logger.error(f"❌ 获取用户互动历史失败 (用户: {user_id}): {e}")
            return []
    
    def _analyze_active_hours(self, interaction_history: List[Dict]) -> List[int]:
        """分析活跃时间"""
        try:
            hour_counts = defaultdict(int)
            
            for interaction in interaction_history:
                timestamp = interaction.get("timestamp", 0)
                hour = datetime.fromtimestamp(timestamp).hour
                hour_counts[hour] += 1
            
            # 找出活跃度前6的小时
            sorted_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)
            active_hours = [hour for hour, count in sorted_hours[:6]]
            
            # 如果没有足够数据，使用默认活跃时间
            if not active_hours:
                active_hours = [9, 10, 14, 15, 19, 20]
            
            return sorted(active_hours)
            
        except Exception as e:
            logger.error(f"❌ 分析活跃时间失败: {e}")
            return [9, 10, 14, 15, 19, 20]
    
    def _analyze_response_rate(self, interaction_history: List[Dict]) -> float:
        """分析响应率"""
        try:
            if not interaction_history:
                return 0.5
            
            total_interactions = len(interaction_history)
            responded_interactions = sum(1 for i in interaction_history if i.get("user_responded", False))
            
            return responded_interactions / total_interactions if total_interactions > 0 else 0.5
            
        except Exception as e:
            logger.error(f"❌ 分析响应率失败: {e}")
            return 0.5
    
    def _analyze_preferred_topics(self, interaction_history: List[Dict]) -> List[str]:
        """分析偏好话题"""
        try:
            topic_counts = defaultdict(int)
            
            for interaction in interaction_history:
                topics = interaction.get("topics", [])
                for topic in topics:
                    topic_counts[topic] += 1
            
            # 返回最常讨论的话题
            sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)
            return [topic for topic, count in sorted_topics[:5]]
            
        except Exception as e:
            logger.error(f"❌ 分析偏好话题失败: {e}")
            return []
    
    def _analyze_emotional_patterns(self, interaction_history: List[Dict]) -> Dict[str, float]:
        """分析情感模式"""
        try:
            emotion_counts = defaultdict(list)
            
            for interaction in interaction_history:
                emotion = interaction.get("emotion", "neutral")
                sentiment = interaction.get("sentiment", 0.0)
                emotion_counts[emotion].append(sentiment)
            
            # 计算各情感的平均强度
            emotion_patterns = {}
            for emotion, sentiments in emotion_counts.items():
                emotion_patterns[emotion] = np.mean(sentiments) if sentiments else 0.0
            
            return emotion_patterns
            
        except Exception as e:
            logger.error(f"❌ 分析情感模式失败: {e}")
            return {}
    
    def _analyze_interaction_frequency(self, interaction_history: List[Dict]) -> float:
        """分析互动频率"""
        try:
            if len(interaction_history) < 2:
                return 0.3
            
            # 计算平均互动间隔
            timestamps = [i.get("timestamp", 0) for i in interaction_history]
            timestamps.sort()
            
            intervals = []
            for i in range(1, len(timestamps)):
                interval = timestamps[i] - timestamps[i-1]
                intervals.append(interval)
            
            if not intervals:
                return 0.3
            
            avg_interval = np.mean(intervals)
            # 转换为每天的互动频率
            daily_frequency = 86400 / avg_interval if avg_interval > 0 else 0.3
            
            return min(1.0, daily_frequency / 10)  # 归一化到0-1
            
        except Exception as e:
            logger.error(f"❌ 分析互动频率失败: {e}")
            return 0.3
    
    def _calculate_optimal_timing(self, user_pattern: UserBehaviorPattern) -> Dict:
        """计算最优时机"""
        return {
            "best_hours": user_pattern.active_hours[:3],  # 最佳3个小时
            "good_hours": user_pattern.active_hours[3:],  # 较好的小时
            "avoid_hours": [h for h in range(24) if h not in user_pattern.active_hours]
        }
    
    def _calculate_preferred_triggers(self, user_pattern: UserBehaviorPattern) -> List[str]:
        """计算偏好触发器"""
        # 基于用户的情感模式和话题偏好
        preferred = []
        
        if "empathy" in user_pattern.emotional_patterns:
            preferred.append("empathy_driven")
        if "curiosity" in user_pattern.preferred_topics:
            preferred.append("curiosity_driven")
        if user_pattern.response_rate > 0.7:
            preferred.append("relationship_driven")
        
        return preferred or ["empathy_driven"]  # 默认共情驱动
    
    def _calculate_content_personalization(self, user_pattern: UserBehaviorPattern) -> Dict:
        """计算内容个性化"""
        return {
            "preferred_topics": user_pattern.preferred_topics,
            "emotional_tone": self._determine_emotional_tone(user_pattern),
            "formality_level": self._determine_formality_level(user_pattern),
            "interaction_style": self._determine_interaction_style(user_pattern)
        }
    
    def _calculate_frequency_adjustment(self, user_pattern: UserBehaviorPattern, relationship_type: str) -> float:
        """计算频率调整"""
        base_frequency = {
            "acquaintance": 0.3,
            "friend": 0.5,
            "close_friend": 0.7,
            "best_friend": 0.9
        }.get(relationship_type, 0.3)
        
        # 根据用户响应率调整
        adjustment = user_pattern.response_rate
        
        return min(1.0, base_frequency * adjustment)
    
    def _calculate_emotional_adaptation(self, user_pattern: UserBehaviorPattern) -> Dict:
        """计算情感适应"""
        return {
            "dominant_emotions": list(user_pattern.emotional_patterns.keys())[:3],
            "emotional_sensitivity": np.mean(list(user_pattern.emotional_patterns.values())) if user_pattern.emotional_patterns else 0.5,
            "adaptation_strategies": self._determine_adaptation_strategies(user_pattern)
        }
    
    def _calculate_strategy_confidence(self, user_pattern: UserBehaviorPattern) -> float:
        """计算策略置信度"""
        # 基于数据完整性和一致性
        data_completeness = 0.0
        
        if user_pattern.active_hours:
            data_completeness += 0.3
        if user_pattern.preferred_topics:
            data_completeness += 0.3
        if user_pattern.emotional_patterns:
            data_completeness += 0.2
        if user_pattern.response_rate > 0:
            data_completeness += 0.2
        
        return data_completeness
    
    def _calculate_effectiveness_score(self, response_data: Dict) -> float:
        """计算效果评分"""
        try:
            score = 0.0
            
            # 响应类型评分
            response_type = response_data.get("response_type", "unknown")
            if response_type == "positive":
                score += 0.4
            elif response_type == "neutral":
                score += 0.2
            elif response_type == "negative":
                score -= 0.2
            
            # 情感评分
            sentiment = response_data.get("response_sentiment", 0.0)
            score += sentiment * 0.3
            
            # 参与度评分
            engagement = response_data.get("response_engagement", 0.0)
            score += engagement * 0.3
            
            return min(1.0, max(-1.0, score))
            
        except Exception as e:
            logger.error(f"❌ 计算效果评分失败: {e}")
            return 0.0
    
    def _evaluate_response_quality(self, response_data: Dict) -> str:
        """评估响应质量"""
        effectiveness = response_data.get("effectiveness_score", 0.0)
        
        if effectiveness > 0.7:
            return "excellent"
        elif effectiveness > 0.4:
            return "good"
        elif effectiveness > 0.0:
            return "fair"
        else:
            return "poor"
    
    def _calculate_learning_impact(self, user_id: str, effectiveness_score: float) -> Dict:
        """计算学习影响"""
        return {
            "score_impact": effectiveness_score,
            "pattern_update": effectiveness_score > 0.5,
            "strategy_adjustment": effectiveness_score < 0.2,
            "confidence_change": effectiveness_score - 0.5
        }
    
    async def _suggest_strategy_adjustment(self, user_id: str, effectiveness_score: float) -> Dict:
        """建议策略调整"""
        suggestions = {
            "timing_adjustment": False,
            "content_adjustment": False,
            "frequency_adjustment": False,
            "approach_change": False
        }
        
        if effectiveness_score < 0.2:
            suggestions["approach_change"] = True
            suggestions["content_adjustment"] = True
        elif effectiveness_score < 0.4:
            suggestions["timing_adjustment"] = True
            suggestions["frequency_adjustment"] = True
        
        return suggestions
    
    # 辅助方法
    def _evaluate_emotional_compatibility(self, opportunity: Dict, emotional_context: Dict) -> float:
        """评估情感兼容性"""
        # 简化实现
        return 0.7
    
    def _calculate_behavioral_match(self, opportunity: Dict, user_pattern: UserBehaviorPattern) -> float:
        """计算行为匹配度"""
        trigger_type = opportunity.get("type", "")
        if trigger_type in user_pattern.preferred_topics:
            return 0.8
        return 0.5
    
    def _calculate_relationship_compatibility(self, opportunity: Dict, relationship_context: Dict) -> float:
        """计算关系兼容性"""
        intimacy_level = relationship_context.get("intimacy_level", 0)
        return min(1.0, intimacy_level / 50000)  # 归一化
    
    def _calculate_content_relevance(self, opportunity: Dict, enhanced_context: Dict) -> float:
        """计算内容相关性"""
        # 简化实现
        return 0.6
    
    def _determine_emotional_tone(self, user_pattern: UserBehaviorPattern) -> str:
        """确定情感基调"""
        if "happy" in user_pattern.emotional_patterns:
            return "cheerful"
        elif "sad" in user_pattern.emotional_patterns:
            return "gentle"
        else:
            return "warm"
    
    def _determine_formality_level(self, user_pattern: UserBehaviorPattern) -> str:
        """确定正式程度"""
        if user_pattern.response_rate > 0.8:
            return "casual"
        else:
            return "semi_formal"
    
    def _determine_interaction_style(self, user_pattern: UserBehaviorPattern) -> str:
        """确定互动风格"""
        if user_pattern.interaction_frequency > 0.7:
            return "active"
        else:
            return "gentle"
    
    def _determine_adaptation_strategies(self, user_pattern: UserBehaviorPattern) -> List[str]:
        """确定适应策略"""
        strategies = []
        
        if "sad" in user_pattern.emotional_patterns:
            strategies.append("emotional_support")
        if "happy" in user_pattern.emotional_patterns:
            strategies.append("cheerful_interaction")
        if user_pattern.response_rate < 0.5:
            strategies.append("gentle_approach")
        
        return strategies or ["balanced_approach"]
    
    async def _load_user_behavior_patterns(self):
        """加载用户行为模式"""
        try:
            # 这里可以从数据库加载已保存的用户行为模式
            # 暂时跳过，使用实时分析
            logger.info("✅ 用户行为模式加载完成")
        except Exception as e:
            logger.error(f"❌ 加载用户行为模式失败: {e}")
    
    async def _record_trigger_evaluation(self, user_id: str, trigger_scores: List[TriggerScore]):
        """记录触发评估"""
        try:
            evaluation_record = {
                "user_id": user_id,
                "timestamp": time.time(),
                "trigger_count": len(trigger_scores),
                "best_score": max([ts.final_score for ts in trigger_scores]) if trigger_scores else 0.0,
                "avg_confidence": np.mean([ts.confidence for ts in trigger_scores]) if trigger_scores else 0.0
            }
            
            # 记录到历史
            self.trigger_history[user_id].append(evaluation_record)
            
        except Exception as e:
            logger.error(f"❌ 记录触发评估失败: {e}")
    
    async def _update_ml_weights(self, user_id: str, effectiveness_score: float, response_data: Dict):
        """更新机器学习权重"""
        try:
            # 简化的权重更新逻辑
            learning_rate = 0.01
            
            if effectiveness_score > 0.7:
                # 增强成功因素的权重
                self.ml_weights["emotional_context"] += learning_rate
            elif effectiveness_score < 0.3:
                # 降低失败因素的权重
                self.ml_weights["emotional_context"] -= learning_rate
            
            # 确保权重在合理范围内
            for key in self.ml_weights:
                self.ml_weights[key] = max(0.1, min(0.5, self.ml_weights[key]))
            
        except Exception as e:
            logger.error(f"❌ 更新ML权重失败: {e}")
    
    async def _update_user_behavior_pattern(self, user_id: str, response_data: Dict):
        """更新用户行为模式"""
        try:
            if user_id not in self.user_patterns:
                return
            
            pattern = self.user_patterns[user_id]
            
            # 更新响应率
            effectiveness = response_data.get("effectiveness_score", 0.0)
            if effectiveness > 0:
                pattern.response_rate = min(1.0, pattern.response_rate * 0.9 + 0.1)
            else:
                pattern.response_rate = max(0.0, pattern.response_rate * 0.9)
            
            pattern.last_updated = time.time()
            
        except Exception as e:
            logger.error(f"❌ 更新用户行为模式失败: {e}")

# 全局实例
_intelligent_trigger_engine = None

def get_intelligent_trigger_engine() -> IntelligentTriggerEngine:
    """获取智能触发引擎实例"""
    global _intelligent_trigger_engine
    if _intelligent_trigger_engine is None:
        _intelligent_trigger_engine = IntelligentTriggerEngine()
    return _intelligent_trigger_engine