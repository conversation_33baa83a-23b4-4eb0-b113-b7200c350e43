#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能用户筛选算法 - Intelligent User Selector

功能：
1. 基于MySQL message表分析最近30天聊天频次
2. 综合用户画像、情感强度、关系类型等多维度评估
3. 利用AI神经网络进行智能决策
4. 动态更新emotions表的need_reply字段

作者: 老王 (数字生命体开发团队)
创建日期: 2025-01-17
版本: 1.0.0
"""

import os
import sys
import time
import json
import asyncio
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, PROJECT_ROOT)

from utilities.unified_logger import get_unified_logger
from connectors.database.mysql_connector import get_instance as get_mysql_connector
from core.neural_network.neural_core import Neural<PERSON>ore
from intelligence.feedback_learning import FeedbackLearning
from cognitive_modules.base.cognitive_interface import CognitiveModuleBase

logger = get_unified_logger("intelligent_user_selector")

@dataclass
class UserProfile:
    """用户画像数据结构"""
    user_id: str
    user_name: str
    chat_frequency_30d: int  # 最近30天聊天次数
    avg_daily_chats: float   # 日均聊天次数
    last_chat_time: datetime # 最后聊天时间
    days_since_last_chat: int # 距离上次聊天天数
    emotion_intensity: int   # 情感强度
    emotion_type: str        # 情感类型
    relationship_type: str   # 关系类型
    trust_level: float       # 信任度
    intimacy_level: float    # 亲密度
    user_activity_score: float # 用户活跃度评分
    ai_impression_score: float # AI对用户的印象评分

@dataclass
class SelectionResult:
    """筛选结果数据结构"""
    selected_users: List[str]
    evaluation_details: Dict[str, Any]
    neural_decision_confidence: float
    total_evaluated_users: int
    selection_reasoning: str

class IntelligentUserSelector(CognitiveModuleBase):
    """智能用户筛选算法"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化智能用户筛选器"""
        super().__init__(config)
        
        self.logger = logger
        self.mysql_connector = get_mysql_connector()
        
        # 算法配置
        self.config = config or {}
        self.selection_config = {
            "min_chat_frequency": 0,      # 🔥 修复：降低最少聊天次数，允许新用户
            "max_days_since_chat": 30,    # 🔥 修复：增加最大距离上次聊天天数
            "min_emotion_intensity": 10,  # 最小情感强度
            "neural_confidence_threshold": 0.4,  # 🔥 修复：降低神经网络置信度阈值
            "max_selected_users": 5,      # 最大选择用户数
            "activity_weight": 0.3,       # 活跃度权重
            "emotion_weight": 0.4,        # 情感权重
            "relationship_weight": 0.3    # 关系权重
        }
        
        # 更新配置
        if "selection_algorithm" in self.config:
            self.selection_config.update(self.config["selection_algorithm"])
        
        # 初始化AI组件
        self._init_ai_components()
        
        # 缓存
        self.user_profiles_cache: Dict[str, UserProfile] = {}
        self.cache_expiry_time = 300  # 5分钟缓存
        self.last_cache_update = 0
        
        # 统计信息
        self.selection_stats = {
            "total_selections": 0,
            "successful_selections": 0,
            "avg_selection_time": 0.0,
            "neural_accuracy": 0.0,
            "user_feedback_score": 0.0
        }
        
        self.logger.success("智能用户筛选算法初始化完成")
    
    def _init_ai_components(self):
        """初始化AI组件"""
        try:
            # 初始化神经网络核心
            neural_config = {
                "layers": [
                    {"name": "input", "size": 12, "type": "input"},      # 12个输入特征
                    {"name": "hidden1", "size": 24, "type": "dense"},    # 第一隐藏层
                    {"name": "hidden2", "size": 16, "type": "dense"},    # 第二隐藏层
                    {"name": "output", "size": 1, "type": "output"}      # 输出层：是否需要主动表达
                ]
            }
            self.neural_core = NeuralCore(neural_config)
            
            # 初始化反馈学习系统
            self.feedback_learning = FeedbackLearning()
            
            self.logger.success("AI组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"AI组件初始化失败: {e}")
            self.neural_core = None
            self.feedback_learning = None
    
    async def select_users_for_proactive_expression(self, 
                                                   expression_type: str = "general",
                                                   max_users: int = None) -> SelectionResult:
        """
        智能选择用户进行主动表达
        
        Args:
            expression_type: 表达类型 (general, greeting, financial, etc.)
            max_users: 最大选择用户数
            
        Returns:
            SelectionResult: 筛选结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"🧠 开始智能用户筛选 - 表达类型: {expression_type}")
            
            # 1. 获取用户画像数据
            user_profiles = await self._get_user_profiles()
            if not user_profiles:
                return SelectionResult(
                    selected_users=[],
                    evaluation_details={},
                    neural_decision_confidence=0.0,
                    total_evaluated_users=0,
                    selection_reasoning="没有找到有效的用户画像数据"
                )
            
            # 2. 基础筛选 - 过滤不符合条件的用户
            filtered_users = await self._basic_filtering(user_profiles)
            self.logger.info(f"🔍 基础筛选完成: {len(filtered_users)}/{len(user_profiles)} 用户通过")
            
            # 3. AI神经网络评估
            neural_evaluations = await self._neural_network_evaluation(filtered_users, expression_type)
            
            # 4. 综合评分和排序
            ranked_users = await self._comprehensive_ranking(neural_evaluations)
            
            # 5. 最终选择
            max_select = max_users or self.selection_config["max_selected_users"]
            selected_users = ranked_users[:max_select]
            
            # 6. 更新emotions表
            await self._update_emotions_table(selected_users, neural_evaluations)
            
            # 7. 记录统计信息
            processing_time = time.time() - start_time
            self._update_selection_stats(processing_time, len(selected_users))
            
            # 8. 构建结果
            result = SelectionResult(
                selected_users=[user.user_id for user in selected_users],
                evaluation_details={
                    "total_profiles": len(user_profiles),
                    "filtered_users": len(filtered_users),
                    "neural_evaluations": len(neural_evaluations),
                    "processing_time": processing_time,
                    "expression_type": expression_type
                },
                neural_decision_confidence=np.mean([eval_data["confidence"] for eval_data in neural_evaluations.values()]),
                total_evaluated_users=len(user_profiles),
                selection_reasoning=f"基于AI神经网络评估，从{len(user_profiles)}个用户中筛选出{len(selected_users)}个最适合的用户"
            )
            
            self.logger.success(f"🎯 智能用户筛选完成: 选择 {len(selected_users)} 个用户，耗时 {processing_time:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"智能用户筛选失败: {e}")
            return SelectionResult(
                selected_users=[],
                evaluation_details={"error": str(e)},
                neural_decision_confidence=0.0,
                total_evaluated_users=0,
                selection_reasoning=f"筛选过程出错: {e}"
            )
    
    async def _get_user_profiles(self) -> List[UserProfile]:
        """获取用户画像数据"""
        try:
            # 检查缓存
            current_time = time.time()
            if (current_time - self.last_cache_update < self.cache_expiry_time and 
                self.user_profiles_cache):
                self.logger.debug("使用缓存的用户画像数据")
                return list(self.user_profiles_cache.values())
            
            # 从数据库获取最新数据
            profiles = []
            
            # 🔥 核心SQL：分析最近30天聊天频次 + 用户画像
            query = """
            SELECT 
                u.id as user_id,
                u.name as user_name,
                COUNT(m.id) as chat_frequency_30d,
                COUNT(m.id) / 30.0 as avg_daily_chats,
                MAX(m.timestamp) as last_chat_time,
                DATEDIFF(NOW(), MAX(m.timestamp)) as days_since_last_chat,
                COALESCE(e.intensity, 0) as emotion_intensity,
                COALESCE(e.emotion, 'neutral') as emotion_type,
                COALESCE(aur.relationship_type, 'stranger') as relationship_type,
                COALESCE(aur.trust_level, 0.0) as trust_level,
                COALESCE(aur.intimacy_level, 0.0) as intimacy_level,
                COALESCE(aur.emotional_weight, 0.0) as emotional_weight
            FROM users u
            LEFT JOIN messages m ON u.id = m.user_id 
                AND m.timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                AND m.role = 'user'
            LEFT JOIN emotions e ON u.id = e.user_id
            LEFT JOIN ai_user_relationships aur ON u.id = aur.user_id AND aur.ai_id = 'yanran'
            WHERE u.id IS NOT NULL 
                AND u.id != '' 
                AND u.id != 'system'
                AND u.id != 'default_user'
                AND e.user_id IS NOT NULL  -- 🔥 修复：只选择有emotions记录的用户
            GROUP BY u.id, u.name, e.intensity, e.emotion, aur.relationship_type, aur.trust_level, aur.intimacy_level, aur.emotional_weight
            HAVING chat_frequency_30d >= 0  -- 🔥 修复：允许新用户（聊天次数为0）
                AND days_since_last_chat <= 30  -- 🔥 修复：限制最近30天内有互动的用户
            ORDER BY chat_frequency_30d DESC, emotion_intensity DESC
            """
            
            success, results, error = self.mysql_connector.query(query)
            
            if not success:
                self.logger.error(f"获取用户画像数据失败: {error}")
                return []
            
            for row in results:
                # 计算用户活跃度评分
                activity_score = self._calculate_activity_score(
                    row.get('chat_frequency_30d', 0),
                    row.get('days_since_last_chat', 999),
                    row.get('avg_daily_chats', 0.0)
                )
                
                # 计算AI印象评分
                impression_score = self._calculate_impression_score(
                    row.get('emotion_intensity', 0),
                    row.get('trust_level', 0.0),
                    row.get('intimacy_level', 0.0),
                    row.get('emotional_weight', 0.0)
                )
                
                # 处理时间字段
                last_chat_time = row.get('last_chat_time')
                if isinstance(last_chat_time, str):
                    last_chat_time = datetime.fromisoformat(last_chat_time.replace('Z', '+00:00'))
                elif last_chat_time is None:
                    last_chat_time = datetime.now() - timedelta(days=999)
                
                profile = UserProfile(
                    user_id=row.get('user_id', ''),
                    user_name=row.get('user_name', 'Unknown'),
                    chat_frequency_30d=row.get('chat_frequency_30d', 0),
                    avg_daily_chats=row.get('avg_daily_chats', 0.0),
                    last_chat_time=last_chat_time,
                    days_since_last_chat=row.get('days_since_last_chat', 999),
                    emotion_intensity=row.get('emotion_intensity', 0),
                    emotion_type=row.get('emotion_type', 'neutral'),
                    relationship_type=row.get('relationship_type', 'stranger'),
                    trust_level=row.get('trust_level', 0.0),
                    intimacy_level=row.get('intimacy_level', 0.0),
                    user_activity_score=activity_score,
                    ai_impression_score=impression_score
                )
                
                profiles.append(profile)
                self.user_profiles_cache[profile.user_id] = profile
            
            # 更新缓存时间
            self.last_cache_update = current_time
            
            self.logger.info(f"📊 获取到 {len(profiles)} 个用户画像数据")
            return profiles
            
        except Exception as e:
            self.logger.error(f"获取用户画像数据异常: {e}")
            return []
    
    def _calculate_activity_score(self, chat_frequency: int, days_since_chat: int, avg_daily: float) -> float:
        """计算用户活跃度评分 (0-1)"""
        try:
            # 🔥 修复Decimal和float类型不兼容问题
            # 确保所有数值都转换为float类型
            chat_frequency = float(chat_frequency) if chat_frequency is not None else 0.0
            days_since_chat = float(days_since_chat) if days_since_chat is not None else 999.0
            avg_daily = float(avg_daily) if avg_daily is not None else 0.0
            
            # 聊天频次评分 (0-0.5)
            frequency_score = min(0.5, chat_frequency / 50.0)
            
            # 时间新鲜度评分 (0-0.3)
            freshness_score = max(0.0, 0.3 * (7 - days_since_chat) / 7.0)
            
            # 日均聊天评分 (0-0.2)
            daily_score = min(0.2, avg_daily / 5.0)
            
            total_score = frequency_score + freshness_score + daily_score
            
            self.logger.debug(f"🧠 活跃度评分: 频次={frequency_score:.3f}, 新鲜度={freshness_score:.3f}, 日均={daily_score:.3f}, 总分={total_score:.3f}")
            return total_score
            
        except Exception as e:
            self.logger.error(f"计算活跃度评分失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return 0.0
    
    def _calculate_impression_score(self, emotion_intensity: int, trust_level: float, 
                                  intimacy_level: float, emotional_weight: float) -> float:
        """计算AI对用户的印象评分 (0-1)"""
        try:
            # 🔥 修复Decimal和float类型不兼容问题
            # 确保所有数值都转换为float类型
            emotion_intensity = float(emotion_intensity) if emotion_intensity is not None else 0.0
            trust_level = float(trust_level) if trust_level is not None else 0.0
            intimacy_level = float(intimacy_level) if intimacy_level is not None else 0.0
            emotional_weight = float(emotional_weight) if emotional_weight is not None else 0.0
            
            # 情感强度评分 (0-0.4)
            emotion_score = min(0.4, emotion_intensity / 1000.0)
            
            # 信任度评分 (0-0.3)
            trust_score = trust_level * 0.3
            
            # 亲密度评分 (0-0.2)
            intimacy_score = intimacy_level * 0.2
            
            # 情感权重评分 (0-0.1)
            weight_score = emotional_weight * 0.1
            
            total_score = emotion_score + trust_score + intimacy_score + weight_score
            
            self.logger.debug(f"🧠 印象评分: 情感={emotion_score:.3f}, 信任={trust_score:.3f}, 亲密={intimacy_score:.3f}, 权重={weight_score:.3f}, 总分={total_score:.3f}")
            return total_score
            
        except Exception as e:
            self.logger.error(f"计算印象评分失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return 0.0
    
    async def _basic_filtering(self, user_profiles: List[UserProfile]) -> List[UserProfile]:
        """基础筛选 - 过滤不符合条件的用户"""
        try:
            filtered_users = []
            
            for profile in user_profiles:
                # 🔥 P0级修复：检查None值，避免NoneType比较错误
                chat_frequency = profile.chat_frequency_30d if profile.chat_frequency_30d is not None else 0
                days_since_chat = profile.days_since_last_chat if profile.days_since_last_chat is not None else 999
                emotion_intensity = profile.emotion_intensity if profile.emotion_intensity is not None else 0
                
                # 检查聊天频次
                if chat_frequency < self.selection_config["min_chat_frequency"]:
                    continue
                
                # 检查距离上次聊天时间
                if days_since_chat > self.selection_config["max_days_since_chat"]:
                    continue
                
                # 🔥 修复：降低情感强度要求，允许更多用户
                if emotion_intensity < self.selection_config["min_emotion_intensity"]:
                    # 对于新用户或低情感强度用户，给予机会
                    if chat_frequency > 0 or days_since_chat <= 7:
                        pass  # 允许通过
                    else:
                        continue
                
                # 排除特殊用户
                if profile.user_id in ['system', 'default_user', 'admin', 'test']:
                    continue
                
                # 🔥 修复：不完全排除陌生人，给新用户机会
                if profile.relationship_type in ['blocked', 'banned']:
                    continue
                
                # 🔥 修复：对于陌生人，如果有聊天记录就允许
                if profile.relationship_type == 'stranger' and chat_frequency == 0:
                    continue
                
                filtered_users.append(profile)
            
            return filtered_users
            
        except Exception as e:
            self.logger.error(f"基础筛选失败: {e}")
            return []
    
    async def _neural_network_evaluation(self, user_profiles: List[UserProfile], 
                                       expression_type: str) -> Dict[str, Dict[str, Any]]:
        """AI神经网络评估"""
        try:
            if not self.neural_core:
                self.logger.warning("神经网络核心不可用，使用规则引擎")
                return await self._rule_based_evaluation(user_profiles, expression_type)
            
            evaluations = {}
            
            for profile in user_profiles:
                # 准备神经网络输入特征 (12维)
                input_features = self._prepare_neural_input(profile, expression_type)
                
                # 神经网络推理
                neural_output = self.neural_core.process(input_features)
                
                # 解析输出
                confidence = float(neural_output.get("result", [0.5])[0])
                should_select = confidence > self.selection_config["neural_confidence_threshold"]
                
                evaluations[profile.user_id] = {
                    "profile": profile,
                    "confidence": confidence,
                    "should_select": should_select,
                    "neural_features": input_features.tolist(),
                    "evaluation_method": "neural_network"
                }
            
            return evaluations
            
        except Exception as e:
            self.logger.error(f"神经网络评估失败: {e}")
            return await self._rule_based_evaluation(user_profiles, expression_type)
    
    def _prepare_neural_input(self, profile: UserProfile, expression_type: str) -> np.ndarray:
        """准备神经网络输入特征"""
        try:
            # 🔥 修复Decimal和float类型不兼容问题 - 确保所有数值都转换为float类型
            chat_frequency_30d = float(profile.chat_frequency_30d) if profile.chat_frequency_30d is not None else 0.0
            avg_daily_chats = float(profile.avg_daily_chats) if profile.avg_daily_chats is not None else 0.0
            days_since_last_chat = float(profile.days_since_last_chat) if profile.days_since_last_chat is not None else 999.0
            user_activity_score = float(profile.user_activity_score) if profile.user_activity_score is not None else 0.0
            emotion_intensity = float(profile.emotion_intensity) if profile.emotion_intensity is not None else 0.0
            trust_level = float(profile.trust_level) if profile.trust_level is not None else 0.0
            intimacy_level = float(profile.intimacy_level) if profile.intimacy_level is not None else 0.0
            ai_impression_score = float(profile.ai_impression_score) if profile.ai_impression_score is not None else 0.0

            # 12维特征向量
            features = [
                # 用户活跃度特征 (4维)
                min(1.0, chat_frequency_30d / 100.0),  # 聊天频次归一化
                min(1.0, avg_daily_chats / 10.0),      # 日均聊天归一化
                max(0.0, 1.0 - days_since_last_chat / 30.0),  # 时间新鲜度
                user_activity_score,                    # 活跃度评分

                # 情感特征 (4维)
                min(1.0, emotion_intensity / 1000.0),  # 情感强度归一化
                self._emotion_type_to_numeric(profile.emotion_type),  # 情感类型数值化
                trust_level,                           # 信任度
                intimacy_level,                        # 亲密度

                # 关系特征 (2维)
                self._relationship_type_to_numeric(profile.relationship_type),  # 关系类型数值化
                ai_impression_score,                   # AI印象评分

                # 表达类型特征 (2维)
                self._expression_type_to_numeric(expression_type),  # 表达类型数值化
                self._calculate_contextual_score(profile, expression_type)  # 上下文评分
            ]

            return np.array(features, dtype=np.float32)

        except Exception as e:
            self.logger.error(f"准备神经网络输入失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return np.zeros(12, dtype=np.float32)
    
    def _emotion_type_to_numeric(self, emotion_type: str) -> float:
        """情感类型转数值"""
        emotion_mapping = {
            'happy': 0.9, 'joy': 0.9, 'excited': 0.8,
            'love': 0.85, 'trust': 0.75, 'admiration': 0.7,
            'neutral': 0.5, 'calm': 0.5,
            'sad': 0.2, 'angry': 0.1, 'fear': 0.1, 'disgust': 0.1
        }
        return emotion_mapping.get(emotion_type.lower(), 0.5)
    
    def _relationship_type_to_numeric(self, relationship_type: str) -> float:
        """关系类型转数值"""
        relationship_mapping = {
            'lover': 1.0, 'close_friend': 0.8, 'friend': 0.6,
            'acquaintance': 0.4, 'colleague': 0.5,
            'stranger': 0.1, 'blocked': 0.0
        }
        return relationship_mapping.get(relationship_type.lower(), 0.3)
    
    def _expression_type_to_numeric(self, expression_type: str) -> float:
        """表达类型转数值"""
        type_mapping = {
            'greeting': 0.8, 'financial': 0.6, 'news': 0.5,
            'general': 0.5, 'emotional': 0.9, 'casual': 0.4
        }
        return type_mapping.get(expression_type.lower(), 0.5)
    
    def _calculate_contextual_score(self, profile: UserProfile, expression_type: str) -> float:
        """计算上下文评分"""
        try:
            # 🔥 修复：确保类型转换安全
            user_activity_score = float(profile.user_activity_score) if profile.user_activity_score is not None else 0.5

            # 基于时间的上下文
            current_hour = datetime.now().hour
            time_appropriateness = 1.0

            # 早安问候适合早上
            if expression_type == 'greeting' and 6 <= current_hour <= 10:
                time_appropriateness = 1.0
            elif expression_type == 'greeting':
                time_appropriateness = 0.3

            # 财经报告适合工作时间
            elif expression_type == 'financial' and 9 <= current_hour <= 18:
                time_appropriateness = 1.0
            elif expression_type == 'financial':
                time_appropriateness = 0.5

            # 结合用户活跃时间模式（简化版）
            user_context_score = user_activity_score * time_appropriateness

            return min(1.0, user_context_score)

        except Exception as e:
            self.logger.error(f"计算上下文评分失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return 0.5
    
    async def _rule_based_evaluation(self, user_profiles: List[UserProfile], 
                                   expression_type: str) -> Dict[str, Dict[str, Any]]:
        """基于规则的评估（神经网络不可用时的备选方案）"""
        try:
            evaluations = {}
            
            for profile in user_profiles:
                # 🔥 修复：确保所有数值都转换为float类型，避免Decimal类型错误
                user_activity_score = float(profile.user_activity_score) if profile.user_activity_score is not None else 0.0
                emotion_intensity = float(profile.emotion_intensity) if profile.emotion_intensity is not None else 0.0
                ai_impression_score = float(profile.ai_impression_score) if profile.ai_impression_score is not None else 0.0

                # 综合评分
                activity_score = user_activity_score * self.selection_config["activity_weight"]
                emotion_score = (emotion_intensity / 1000.0) * self.selection_config["emotion_weight"]
                relationship_score = ai_impression_score * self.selection_config["relationship_weight"]

                total_score = activity_score + emotion_score + relationship_score
                confidence = min(1.0, total_score)
                
                # 🔥 修复：降低阈值，让更多用户有机会被选中
                should_select = confidence > 0.3
                
                evaluations[profile.user_id] = {
                    "profile": profile,
                    "confidence": confidence,
                    "should_select": should_select,
                    "rule_scores": {
                        "activity": activity_score,
                        "emotion": emotion_score,
                        "relationship": relationship_score
                    },
                    "evaluation_method": "rule_based"
                }
            
            return evaluations
            
        except Exception as e:
            self.logger.error(f"基于规则的评估失败: {e}")
            return {}
    
    async def _comprehensive_ranking(self, evaluations: Dict[str, Dict[str, Any]]) -> List[UserProfile]:
        """综合评分和排序"""
        try:
            # 只选择should_select为True的用户
            valid_evaluations = {
                user_id: eval_data for user_id, eval_data in evaluations.items()
                if eval_data.get("should_select", False)
            }
            
            # 🔥 修复：如果没有用户通过筛选，使用兜底机制
            if not valid_evaluations:
                self.logger.warning("没有用户通过筛选，使用兜底机制选择评分最高的用户")
                # 选择评分最高的前3个用户
                all_evaluations = sorted(
                    evaluations.items(),
                    key=lambda x: x[1]["confidence"],
                    reverse=True
                )
                
                # 至少选择1个用户，最多3个
                top_users = all_evaluations[:min(3, len(all_evaluations))]
                return [eval_data["profile"] for _, eval_data in top_users]
            
            # 按置信度排序
            sorted_evaluations = sorted(
                valid_evaluations.items(),
                key=lambda x: x[1]["confidence"],
                reverse=True
            )
            
            # 返回排序后的用户画像
            return [eval_data["profile"] for _, eval_data in sorted_evaluations]
            
        except Exception as e:
            self.logger.error(f"综合排序失败: {e}")
            return []
    
    async def _update_emotions_table(self, selected_users: List[UserProfile], 
                                   evaluations: Dict[str, Dict[str, Any]]):
        """更新emotions表的need_reply字段"""
        try:
            if not selected_users:
                return
            
            # 准备批量更新数据
            updates = []
            for user_profile in selected_users:
                user_id = user_profile.user_id
                confidence = evaluations.get(user_id, {}).get("confidence", 0.0)
                
                # 根据置信度决定need_reply
                need_reply = "yes" if confidence > self.selection_config["neural_confidence_threshold"] else "no"
                
                updates.append((need_reply, user_id))
            
            # 批量更新SQL
            if updates:
                # 🔥 修复：emotions表没有updated_at字段，只更新need_reply
                update_query = """
                UPDATE emotions
                SET need_reply = %s
                WHERE user_id = %s
                """

                success, affected_rows, error = self.mysql_connector.execute_many(update_query, updates)
                
                if success:
                    self.logger.success(f"✅ 已更新 {affected_rows} 个用户的emotions表need_reply字段")
                else:
                    self.logger.error(f"❌ 更新emotions表失败: {error}")
            
        except Exception as e:
            self.logger.error(f"更新emotions表失败: {e}")
    
    def _update_selection_stats(self, processing_time: float, selected_count: int):
        """更新选择统计信息"""
        try:
            self.selection_stats["total_selections"] += 1
            if selected_count > 0:
                self.selection_stats["successful_selections"] += 1
            
            # 更新平均处理时间
            total_time = self.selection_stats["avg_selection_time"] * (self.selection_stats["total_selections"] - 1)
            self.selection_stats["avg_selection_time"] = (total_time + processing_time) / self.selection_stats["total_selections"]
            
        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")
    
    def get_selection_stats(self) -> Dict[str, Any]:
        """获取选择统计信息"""
        return self.selection_stats.copy()
    
    async def train_neural_network(self, feedback_data: List[Dict[str, Any]]):
        """训练神经网络（基于用户反馈）"""
        try:
            if not self.neural_core or not feedback_data:
                return
            
            self.logger.info(f"🧠 开始训练神经网络，样本数: {len(feedback_data)}")
            
            for feedback in feedback_data:
                # 准备训练数据
                input_features = np.array(feedback["input_features"])
                target_output = np.array([1.0 if feedback["user_responded"] else 0.0])
                
                # 训练神经网络
                result = self.neural_core.learn(input_features, target_output)
                
                # 更新反馈学习系统
                if self.feedback_learning:
                    prediction = {"confidence": feedback.get("predicted_confidence", 0.5)}
                    actual = feedback["user_responded"]
                    self.feedback_learning.update_model(prediction, actual)
            
            self.logger.success("🎯 神经网络训练完成")
            
        except Exception as e:
            self.logger.error(f"训练神经网络失败: {e}")


# 单例模式
_intelligent_user_selector = None

def get_intelligent_user_selector(config: Dict[str, Any] = None) -> IntelligentUserSelector:
    """获取智能用户筛选器单例"""
    global _intelligent_user_selector
    if _intelligent_user_selector is None:
        _intelligent_user_selector = IntelligentUserSelector(config)
    return _intelligent_user_selector 