"""
用户行为分析器 - Phase 3
深度分析用户行为模式，为智能触发提供精准的用户画像
包括时间模式、情感模式、话题偏好、互动风格、响应模式等多维度分析
"""

import time
import json
import asyncio
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import logging

from utilities.unified_logger import get_unified_logger, setup_unified_logging

logger = get_unified_logger(__name__)

@dataclass
class TimePattern:
    """时间模式数据结构"""
    active_hours: List[int]
    peak_hours: List[int]
    quiet_hours: List[int]
    weekly_pattern: Dict[str, float]  # 周几的活跃度
    session_duration: float
    response_delay: float

@dataclass
class EmotionalPattern:
    """情感模式数据结构"""
    dominant_emotions: List[str]
    emotional_stability: float
    sentiment_trend: List[float]
    emotional_triggers: Dict[str, float]
    empathy_level: float
    emotional_responsiveness: float

@dataclass
class TopicPreference:
    """话题偏好数据结构"""
    preferred_topics: List[str]
    topic_engagement: Dict[str, float]
    topic_depth: Dict[str, str]  # shallow, medium, deep
    curiosity_areas: List[str]
    expertise_areas: List[str]

@dataclass
class InteractionStyle:
    """互动风格数据结构"""
    communication_style: str  # formal, casual, intimate
    response_length: str     # short, medium, long
    emoji_usage: float
    question_frequency: float
    sharing_tendency: float
    humor_appreciation: float

@dataclass
class ResponsePattern:
    """响应模式数据结构"""
    response_rate: float
    response_time: float
    response_quality: float
    engagement_level: float
    conversation_initiation: float
    topic_switching: float

@dataclass
class UserBehaviorProfile:
    """用户行为画像"""
    user_id: str
    time_pattern: TimePattern
    emotional_pattern: EmotionalPattern
    topic_preference: TopicPreference
    interaction_style: InteractionStyle
    response_pattern: ResponsePattern
    behavioral_consistency: float
    profile_confidence: float
    last_updated: float

class UserBehaviorAnalyzer:
    """用户行为分析器"""
    
    def __init__(self):
        self.redis_connector = None
        self.mysql_connector = None
        self.is_initialized = False
        
        # 用户行为画像缓存
        self.user_profiles: Dict[str, UserBehaviorProfile] = {}
        
        # 分析配置
        self.analysis_config = {
            "min_interactions_for_analysis": 10,
            "time_window_days": 30,
            "emotional_threshold": 0.1,
            "topic_min_mentions": 3,
            "pattern_confidence_threshold": 0.6
        }
        
    async def initialize(self):
        """初始化用户行为分析器"""
        try:
            # 连接Redis和MySQL
            from connectors.redis_connector import get_instance as get_redis
            from connectors.mysql_connector import get_instance as get_mysql
            
            self.redis_connector = get_redis()
            self.mysql_connector = get_mysql()
            
            self.is_initialized = True
            logger.info("✅ 用户行为分析器初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 用户行为分析器初始化失败: {e}")
            raise
    
    async def analyze_user_behavior(self, user_id: str, force_refresh: bool = False) -> Optional[UserBehaviorProfile]:
        """分析用户行为模式"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # 检查是否有缓存的分析结果
            if not force_refresh and user_id in self.user_profiles:
                profile = self.user_profiles[user_id]
                # 如果分析结果还在有效期内（24小时），直接返回
                if time.time() - profile.last_updated < 24 * 3600:
                    return profile
            
            # 获取用户互动数据
            interaction_data = await self._get_user_interaction_data(user_id)
            
            if not interaction_data or len(interaction_data) < self.analysis_config["min_interactions_for_analysis"]:
                logger.warning(f"用户 {user_id} 的互动数据不足，无法进行深度分析")
                return self._create_default_profile(user_id)
            
            # 分析各个维度
            time_pattern = await self._analyze_time_pattern(user_id, interaction_data)
            emotional_pattern = await self._analyze_emotional_pattern(user_id, interaction_data)
            topic_preference = await self._analyze_topic_preference(user_id, interaction_data)
            interaction_style = await self._analyze_interaction_style(user_id, interaction_data)
            response_pattern = await self._analyze_response_pattern(user_id, interaction_data)
            
            # 计算行为一致性
            behavioral_consistency = self._calculate_behavioral_consistency(interaction_data)
            
            # 计算画像置信度
            profile_confidence = self._calculate_profile_confidence(
                time_pattern, emotional_pattern, topic_preference, 
                interaction_style, response_pattern, len(interaction_data)
            )
            
            # 创建用户行为画像
            user_profile = UserBehaviorProfile(
                user_id=user_id,
                time_pattern=time_pattern,
                emotional_pattern=emotional_pattern,
                topic_preference=topic_preference,
                interaction_style=interaction_style,
                response_pattern=response_pattern,
                behavioral_consistency=behavioral_consistency,
                profile_confidence=profile_confidence,
                last_updated=time.time()
            )
            
            # 缓存分析结果
            self.user_profiles[user_id] = user_profile
            
            # 保存到数据库
            await self._save_user_profile(user_profile)
            
            logger.info(f"✅ 完成用户 {user_id} 的行为分析，置信度: {profile_confidence:.2f}")
            return user_profile
            
        except Exception as e:
            logger.error(f"❌ 分析用户行为失败 (用户: {user_id}): {e}")
            return None
    
    async def get_user_behavioral_insights(self, user_id: str) -> Dict:
        """获取用户行为洞察"""
        try:
            profile = await self.analyze_user_behavior(user_id)
            if not profile:
                return {}
            
            insights = {
                "user_id": user_id,
                "behavioral_summary": self._generate_behavioral_summary(profile),
                "optimal_interaction_strategy": self._suggest_optimal_strategy(profile),
                "trigger_recommendations": self._recommend_triggers(profile),
                "content_personalization": self._suggest_content_personalization(profile),
                "timing_recommendations": self._recommend_timing(profile),
                "risk_assessment": self._assess_interaction_risks(profile),
                "growth_opportunities": self._identify_growth_opportunities(profile)
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"❌ 获取用户行为洞察失败 (用户: {user_id}): {e}")
            return {}
    
    async def predict_user_response(self, user_id: str, trigger_context: Dict) -> Dict:
        """预测用户响应"""
        try:
            profile = await self.analyze_user_behavior(user_id)
            if not profile:
                return {"prediction": "unknown", "confidence": 0.0}
            
            # 基于用户画像预测响应
            prediction = self._predict_response_likelihood(profile, trigger_context)
            
            return prediction
            
        except Exception as e:
            logger.error(f"❌ 预测用户响应失败 (用户: {user_id}): {e}")
            return {"prediction": "unknown", "confidence": 0.0}
    
    # ==================== 私有方法 ====================
    
    async def _get_user_interaction_data(self, user_id: str) -> List[Dict]:
        """获取用户互动数据"""
        try:
            interaction_data = []
            
                         # 检查Redis缓存
             if self.redis_connector:
                 recent_key = f"recent_behavior_data:{user_id}"
                 recent_data = self.redis_connector.get(recent_key)
                 
                 if recent_data:
                     interaction_data.extend(recent_data)
                 
                 # 从Redis获取情景记忆
                 episodic_key = f"episodic_behavior_data:{user_id}"
                 episodic_data = self.redis_connector.get(episodic_key)
                 
                 if episodic_data:
                     interaction_data.extend(episodic_data)
            
            # 从MySQL获取历史互动统计
            if self.mysql_connector:
                query = """
                SELECT * FROM user_interaction_stats 
                WHERE user_id = %s 
                ORDER BY last_interaction_time DESC 
                LIMIT 100
                """
                mysql_data = await self.mysql_connector.execute_query(query, (user_id,))
                if mysql_data:
                    interaction_data.extend(mysql_data)
            
            # 按时间排序
            interaction_data.sort(key=lambda x: x.get("timestamp", 0), reverse=True)
            
            # 只保留最近30天的数据
            cutoff_time = time.time() - (30 * 24 * 3600)
            recent_data = [item for item in interaction_data if item.get("timestamp", 0) > cutoff_time]
            
            return recent_data
            
        except Exception as e:
            logger.error(f"❌ 获取用户互动数据失败 (用户: {user_id}): {e}")
            return []
    
    async def _analyze_time_pattern(self, user_id: str, interaction_data: List[Dict]) -> TimePattern:
        """分析时间模式"""
        try:
            # 分析活跃小时
            hour_counts = defaultdict(int)
            weekday_counts = defaultdict(int)
            session_durations = []
            response_delays = []
            
            for interaction in interaction_data:
                timestamp = interaction.get("timestamp", 0)
                if timestamp:
                    dt = datetime.fromtimestamp(timestamp)
                    hour_counts[dt.hour] += 1
                    weekday_counts[dt.strftime("%A")] += 1
                    
                    # 计算会话持续时间
                    session_duration = interaction.get("session_duration", 0)
                    if session_duration > 0:
                        session_durations.append(session_duration)
                    
                    # 计算响应延迟
                    response_delay = interaction.get("response_delay", 0)
                    if response_delay > 0:
                        response_delays.append(response_delay)
            
            # 找出活跃时间
            sorted_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)
            active_hours = [hour for hour, count in sorted_hours if count > 0]
            peak_hours = [hour for hour, count in sorted_hours[:3]]  # 前3个最活跃小时
            quiet_hours = [hour for hour in range(24) if hour not in active_hours]
            
            # 计算周模式
            total_interactions = sum(weekday_counts.values())
            weekly_pattern = {}
            for day, count in weekday_counts.items():
                weekly_pattern[day] = count / total_interactions if total_interactions > 0 else 0
            
            # 计算平均会话时长和响应延迟
            avg_session_duration = np.mean(session_durations) if session_durations else 300  # 默认5分钟
            avg_response_delay = np.mean(response_delays) if response_delays else 60  # 默认1分钟
            
            return TimePattern(
                active_hours=active_hours,
                peak_hours=peak_hours,
                quiet_hours=quiet_hours,
                weekly_pattern=weekly_pattern,
                session_duration=avg_session_duration,
                response_delay=avg_response_delay
            )
            
        except Exception as e:
            logger.error(f"❌ 分析时间模式失败: {e}")
            return TimePattern(
                active_hours=[9, 10, 14, 15, 19, 20],
                peak_hours=[10, 15, 19],
                quiet_hours=[0, 1, 2, 3, 4, 5, 6, 7, 8, 22, 23],
                weekly_pattern={},
                session_duration=300,
                response_delay=60
            )
    
    async def _analyze_emotional_pattern(self, user_id: str, interaction_data: List[Dict]) -> EmotionalPattern:
        """分析情感模式"""
        try:
            emotions = []
            sentiments = []
            emotional_triggers = defaultdict(float)
            empathy_scores = []
            responsiveness_scores = []
            
            for interaction in interaction_data:
                # 提取情感信息
                emotion = interaction.get("emotion", "neutral")
                sentiment = interaction.get("sentiment", 0.0)
                
                if emotion and emotion != "neutral":
                    emotions.append(emotion)
                if sentiment != 0.0:
                    sentiments.append(sentiment)
                
                # 分析情感触发器
                trigger = interaction.get("trigger_type", "unknown")
                if trigger != "unknown":
                    emotional_triggers[trigger] += abs(sentiment)
                
                # 分析共情水平
                empathy_score = interaction.get("empathy_score", 0.0)
                if empathy_score > 0:
                    empathy_scores.append(empathy_score)
                
                # 分析情感响应度
                responsiveness = interaction.get("emotional_responsiveness", 0.0)
                if responsiveness > 0:
                    responsiveness_scores.append(responsiveness)
            
            # 计算主导情感
            emotion_counter = Counter(emotions)
            dominant_emotions = [emotion for emotion, count in emotion_counter.most_common(3)]
            
            # 计算情感稳定性
            emotional_stability = 1.0 - np.std(sentiments) if sentiments else 0.5
            
            # 计算情感趋势
            sentiment_trend = sentiments[-10:] if len(sentiments) >= 10 else sentiments
            
            # 标准化情感触发器
            max_trigger_value = max(emotional_triggers.values()) if emotional_triggers else 1.0
            for trigger in emotional_triggers:
                emotional_triggers[trigger] /= max_trigger_value
            
            # 计算平均共情水平和响应度
            avg_empathy = np.mean(empathy_scores) if empathy_scores else 0.5
            avg_responsiveness = np.mean(responsiveness_scores) if responsiveness_scores else 0.5
            
            return EmotionalPattern(
                dominant_emotions=dominant_emotions,
                emotional_stability=emotional_stability,
                sentiment_trend=sentiment_trend,
                emotional_triggers=dict(emotional_triggers),
                empathy_level=avg_empathy,
                emotional_responsiveness=avg_responsiveness
            )
            
        except Exception as e:
            logger.error(f"❌ 分析情感模式失败: {e}")
            return EmotionalPattern(
                dominant_emotions=["neutral"],
                emotional_stability=0.5,
                sentiment_trend=[],
                emotional_triggers={},
                empathy_level=0.5,
                emotional_responsiveness=0.5
            )
    
    async def _analyze_topic_preference(self, user_id: str, interaction_data: List[Dict]) -> TopicPreference:
        """分析话题偏好"""
        try:
            topic_counts = defaultdict(int)
            topic_engagement = defaultdict(list)
            topic_depth = defaultdict(list)
            curiosity_indicators = []
            expertise_indicators = []
            
            for interaction in interaction_data:
                # 提取话题信息
                topics = interaction.get("topics", [])
                engagement = interaction.get("engagement_level", 0.0)
                depth = interaction.get("conversation_depth", "medium")
                
                for topic in topics:
                    topic_counts[topic] += 1
                    if engagement > 0:
                        topic_engagement[topic].append(engagement)
                    if depth:
                        topic_depth[topic].append(depth)
                
                # 识别好奇心指标
                if "?" in interaction.get("user_message", ""):
                    curiosity_indicators.append(interaction.get("topics", []))
                
                # 识别专业领域
                if interaction.get("expertise_shown", False):
                    expertise_indicators.append(interaction.get("topics", []))
            
            # 计算偏好话题
            sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)
            preferred_topics = [topic for topic, count in sorted_topics[:10] if count >= self.analysis_config["topic_min_mentions"]]
            
            # 计算话题参与度
            topic_engagement_avg = {}
            for topic, engagements in topic_engagement.items():
                topic_engagement_avg[topic] = np.mean(engagements) if engagements else 0.0
            
            # 计算话题深度
            topic_depth_mode = {}
            for topic, depths in topic_depth.items():
                depth_counter = Counter(depths)
                topic_depth_mode[topic] = depth_counter.most_common(1)[0][0] if depths else "medium"
            
            # 识别好奇心领域
            curiosity_topics = []
            for topic_list in curiosity_indicators:
                curiosity_topics.extend(topic_list)
            curiosity_counter = Counter(curiosity_topics)
            curiosity_areas = [topic for topic, count in curiosity_counter.most_common(5)]
            
            # 识别专业领域
            expertise_topics = []
            for topic_list in expertise_indicators:
                expertise_topics.extend(topic_list)
            expertise_counter = Counter(expertise_topics)
            expertise_areas = [topic for topic, count in expertise_counter.most_common(3)]
            
            return TopicPreference(
                preferred_topics=preferred_topics,
                topic_engagement=topic_engagement_avg,
                topic_depth=topic_depth_mode,
                curiosity_areas=curiosity_areas,
                expertise_areas=expertise_areas
            )
            
        except Exception as e:
            logger.error(f"❌ 分析话题偏好失败: {e}")
            return TopicPreference(
                preferred_topics=[],
                topic_engagement={},
                topic_depth={},
                curiosity_areas=[],
                expertise_areas=[]
            )
    
    async def _analyze_interaction_style(self, user_id: str, interaction_data: List[Dict]) -> InteractionStyle:
        """分析互动风格"""
        try:
            message_lengths = []
            emoji_counts = []
            question_counts = []
            sharing_counts = []
            humor_scores = []
            formality_scores = []
            
            for interaction in interaction_data:
                user_message = interaction.get("user_message", "")
                
                # 分析消息长度
                if user_message:
                    message_lengths.append(len(user_message))
                    
                    # 分析emoji使用
                    emoji_count = sum(1 for char in user_message if ord(char) > 127)  # 简化的emoji检测
                    emoji_counts.append(emoji_count)
                    
                    # 分析问题频率
                    question_count = user_message.count("?") + user_message.count("？")
                    question_counts.append(question_count)
                    
                    # 分析分享倾向
                    sharing_indicators = ["我觉得", "我认为", "我的看法", "我想", "我发现"]
                    sharing_count = sum(1 for indicator in sharing_indicators if indicator in user_message)
                    sharing_counts.append(sharing_count)
                
                # 分析幽默感
                humor_score = interaction.get("humor_score", 0.0)
                if humor_score > 0:
                    humor_scores.append(humor_score)
                
                # 分析正式程度
                formality_score = interaction.get("formality_score", 0.5)
                formality_scores.append(formality_score)
            
            # 计算平均消息长度并分类
            avg_length = np.mean(message_lengths) if message_lengths else 50
            if avg_length < 20:
                response_length = "short"
            elif avg_length < 100:
                response_length = "medium"
            else:
                response_length = "long"
            
            # 计算emoji使用频率
            avg_emoji_usage = np.mean(emoji_counts) if emoji_counts else 0.0
            
            # 计算问题频率
            avg_question_frequency = np.mean(question_counts) if question_counts else 0.0
            
            # 计算分享倾向
            avg_sharing_tendency = np.mean(sharing_counts) if sharing_counts else 0.0
            
            # 计算幽默欣赏度
            avg_humor_appreciation = np.mean(humor_scores) if humor_scores else 0.0
            
            # 计算沟通风格
            avg_formality = np.mean(formality_scores) if formality_scores else 0.5
            if avg_formality > 0.7:
                communication_style = "formal"
            elif avg_formality > 0.3:
                communication_style = "casual"
            else:
                communication_style = "intimate"
            
            return InteractionStyle(
                communication_style=communication_style,
                response_length=response_length,
                emoji_usage=avg_emoji_usage,
                question_frequency=avg_question_frequency,
                sharing_tendency=avg_sharing_tendency,
                humor_appreciation=avg_humor_appreciation
            )
            
        except Exception as e:
            logger.error(f"❌ 分析互动风格失败: {e}")
            return InteractionStyle(
                communication_style="casual",
                response_length="medium",
                emoji_usage=0.5,
                question_frequency=0.3,
                sharing_tendency=0.4,
                humor_appreciation=0.5
            )
    
    async def _analyze_response_pattern(self, user_id: str, interaction_data: List[Dict]) -> ResponsePattern:
        """分析响应模式"""
        try:
            response_indicators = []
            response_times = []
            quality_scores = []
            engagement_levels = []
            initiation_counts = []
            topic_switches = []
            
            for interaction in interaction_data:
                # 分析响应率
                if interaction.get("user_responded", False):
                    response_indicators.append(1)
                else:
                    response_indicators.append(0)
                
                # 分析响应时间
                response_time = interaction.get("response_time", 0)
                if response_time > 0:
                    response_times.append(response_time)
                
                # 分析响应质量
                quality_score = interaction.get("response_quality", 0.0)
                if quality_score > 0:
                    quality_scores.append(quality_score)
                
                # 分析参与度
                engagement = interaction.get("engagement_level", 0.0)
                engagement_levels.append(engagement)
                
                # 分析对话发起
                if interaction.get("initiated_by_user", False):
                    initiation_counts.append(1)
                else:
                    initiation_counts.append(0)
                
                # 分析话题切换
                topic_switch = interaction.get("topic_switched", False)
                topic_switches.append(1 if topic_switch else 0)
            
            # 计算各项指标
            response_rate = np.mean(response_indicators) if response_indicators else 0.0
            avg_response_time = np.mean(response_times) if response_times else 300  # 默认5分钟
            avg_quality = np.mean(quality_scores) if quality_scores else 0.5
            avg_engagement = np.mean(engagement_levels) if engagement_levels else 0.5
            initiation_rate = np.mean(initiation_counts) if initiation_counts else 0.0
            topic_switch_rate = np.mean(topic_switches) if topic_switches else 0.0
            
            return ResponsePattern(
                response_rate=response_rate,
                response_time=avg_response_time,
                response_quality=avg_quality,
                engagement_level=avg_engagement,
                conversation_initiation=initiation_rate,
                topic_switching=topic_switch_rate
            )
            
        except Exception as e:
            logger.error(f"❌ 分析响应模式失败: {e}")
            return ResponsePattern(
                response_rate=0.5,
                response_time=300,
                response_quality=0.5,
                engagement_level=0.5,
                conversation_initiation=0.2,
                topic_switching=0.3
            )
    
    def _calculate_behavioral_consistency(self, interaction_data: List[Dict]) -> float:
        """计算行为一致性"""
        try:
            if len(interaction_data) < 5:
                return 0.5
            
            # 计算时间一致性
            hours = [datetime.fromtimestamp(item.get("timestamp", 0)).hour for item in interaction_data if item.get("timestamp")]
            time_consistency = 1.0 - (np.std(hours) / 12) if hours else 0.5
            
            # 计算情感一致性
            sentiments = [item.get("sentiment", 0.0) for item in interaction_data if item.get("sentiment") is not None]
            emotion_consistency = 1.0 - np.std(sentiments) if sentiments else 0.5
            
            # 计算响应一致性
            response_times = [item.get("response_time", 0) for item in interaction_data if item.get("response_time")]
            response_consistency = 1.0 - (np.std(response_times) / np.mean(response_times)) if response_times and np.mean(response_times) > 0 else 0.5
            
            # 综合一致性
            overall_consistency = (time_consistency + emotion_consistency + response_consistency) / 3
            
            return min(1.0, max(0.0, overall_consistency))
            
        except Exception as e:
            logger.error(f"❌ 计算行为一致性失败: {e}")
            return 0.5
    
    def _calculate_profile_confidence(self, time_pattern: TimePattern, emotional_pattern: EmotionalPattern,
                                    topic_preference: TopicPreference, interaction_style: InteractionStyle,
                                    response_pattern: ResponsePattern, data_size: int) -> float:
        """计算画像置信度"""
        try:
            confidence_factors = []
            
            # 数据量因子
            data_factor = min(1.0, data_size / 50)  # 50个互动为满分
            confidence_factors.append(data_factor)
            
            # 时间模式置信度
            time_confidence = len(time_pattern.active_hours) / 24
            confidence_factors.append(time_confidence)
            
            # 情感模式置信度
            emotion_confidence = len(emotional_pattern.dominant_emotions) / 5
            confidence_factors.append(emotion_confidence)
            
            # 话题偏好置信度
            topic_confidence = len(topic_preference.preferred_topics) / 10
            confidence_factors.append(topic_confidence)
            
            # 响应模式置信度
            response_confidence = response_pattern.response_rate
            confidence_factors.append(response_confidence)
            
            # 计算综合置信度
            overall_confidence = np.mean(confidence_factors)
            
            return min(1.0, max(0.0, overall_confidence))
            
        except Exception as e:
            logger.error(f"❌ 计算画像置信度失败: {e}")
            return 0.5
    
    def _create_default_profile(self, user_id: str) -> UserBehaviorProfile:
        """创建默认用户画像"""
        return UserBehaviorProfile(
            user_id=user_id,
            time_pattern=TimePattern(
                active_hours=[9, 10, 14, 15, 19, 20],
                peak_hours=[10, 15, 19],
                quiet_hours=[0, 1, 2, 3, 4, 5, 6, 7, 8, 22, 23],
                weekly_pattern={},
                session_duration=300,
                response_delay=60
            ),
            emotional_pattern=EmotionalPattern(
                dominant_emotions=["neutral"],
                emotional_stability=0.5,
                sentiment_trend=[],
                emotional_triggers={},
                empathy_level=0.5,
                emotional_responsiveness=0.5
            ),
            topic_preference=TopicPreference(
                preferred_topics=[],
                topic_engagement={},
                topic_depth={},
                curiosity_areas=[],
                expertise_areas=[]
            ),
            interaction_style=InteractionStyle(
                communication_style="casual",
                response_length="medium",
                emoji_usage=0.5,
                question_frequency=0.3,
                sharing_tendency=0.4,
                humor_appreciation=0.5
            ),
            response_pattern=ResponsePattern(
                response_rate=0.5,
                response_time=300,
                response_quality=0.5,
                engagement_level=0.5,
                conversation_initiation=0.2,
                topic_switching=0.3
            ),
            behavioral_consistency=0.5,
            profile_confidence=0.3,
            last_updated=time.time()
        )
    
    def _generate_behavioral_summary(self, profile: UserBehaviorProfile) -> str:
        """生成行为摘要"""
        try:
            summary_parts = []
            
            # 时间模式摘要
            if profile.time_pattern.peak_hours:
                peak_hours_str = ", ".join(map(str, profile.time_pattern.peak_hours))
                summary_parts.append(f"最活跃时间: {peak_hours_str}点")
            
            # 情感模式摘要
            if profile.emotional_pattern.dominant_emotions:
                emotions_str = ", ".join(profile.emotional_pattern.dominant_emotions[:2])
                summary_parts.append(f"主要情感: {emotions_str}")
            
            # 互动风格摘要
            summary_parts.append(f"沟通风格: {profile.interaction_style.communication_style}")
            
            # 响应模式摘要
            response_rate_desc = "高" if profile.response_pattern.response_rate > 0.7 else "中" if profile.response_pattern.response_rate > 0.4 else "低"
            summary_parts.append(f"响应积极性: {response_rate_desc}")
            
            return "; ".join(summary_parts)
            
        except Exception as e:
            logger.error(f"❌ 生成行为摘要失败: {e}")
            return "用户行为分析中"
    
    def _suggest_optimal_strategy(self, profile: UserBehaviorProfile) -> Dict:
        """建议最优策略"""
        strategy = {
            "best_contact_hours": profile.time_pattern.peak_hours,
            "preferred_communication_style": profile.interaction_style.communication_style,
            "optimal_message_length": profile.interaction_style.response_length,
            "emotional_approach": "gentle" if profile.emotional_pattern.emotional_stability < 0.5 else "balanced",
            "topic_focus": profile.topic_preference.preferred_topics[:3],
            "engagement_tactics": []
        }
        
        # 根据用户特征添加参与策略
        if profile.interaction_style.question_frequency > 0.5:
            strategy["engagement_tactics"].append("ask_questions")
        if profile.interaction_style.sharing_tendency > 0.5:
            strategy["engagement_tactics"].append("encourage_sharing")
        if profile.interaction_style.humor_appreciation > 0.6:
            strategy["engagement_tactics"].append("use_humor")
        
        return strategy
    
    def _recommend_triggers(self, profile: UserBehaviorProfile) -> List[str]:
        """推荐触发器"""
        triggers = []
        
        # 基于情感模式推荐
        if profile.emotional_pattern.empathy_level > 0.7:
            triggers.append("empathy_driven")
        
        # 基于话题偏好推荐
        if profile.topic_preference.curiosity_areas:
            triggers.append("curiosity_driven")
        
        # 基于互动风格推荐
        if profile.interaction_style.sharing_tendency > 0.6:
            triggers.append("creativity_driven")
        
        # 基于响应模式推荐
        if profile.response_pattern.conversation_initiation > 0.4:
            triggers.append("relationship_driven")
        
        return triggers or ["empathy_driven"]  # 默认推荐
    
    def _suggest_content_personalization(self, profile: UserBehaviorProfile) -> Dict:
        """建议内容个性化"""
        return {
            "tone": self._determine_optimal_tone(profile),
            "topics": profile.topic_preference.preferred_topics[:5],
            "length": profile.interaction_style.response_length,
            "emoji_level": "high" if profile.interaction_style.emoji_usage > 0.7 else "medium" if profile.interaction_style.emoji_usage > 0.3 else "low",
            "formality": profile.interaction_style.communication_style,
            "humor": profile.interaction_style.humor_appreciation > 0.6
        }
    
    def _recommend_timing(self, profile: UserBehaviorProfile) -> Dict:
        """推荐时机"""
        return {
            "optimal_hours": profile.time_pattern.peak_hours,
            "good_hours": profile.time_pattern.active_hours,
            "avoid_hours": profile.time_pattern.quiet_hours,
            "best_days": [day for day, score in profile.time_pattern.weekly_pattern.items() if score > 0.15],
            "response_window": profile.response_pattern.response_time
        }
    
    def _assess_interaction_risks(self, profile: UserBehaviorProfile) -> Dict:
        """评估互动风险"""
        risks = {
            "low_response_risk": profile.response_pattern.response_rate < 0.3,
            "emotional_sensitivity_risk": profile.emotional_pattern.emotional_stability < 0.3,
            "timing_mismatch_risk": len(profile.time_pattern.active_hours) < 4,
            "topic_mismatch_risk": len(profile.topic_preference.preferred_topics) == 0,
            "overall_risk_level": "low"
        }
        
        # 计算总体风险等级
        risk_count = sum(1 for risk in risks.values() if risk is True)
        if risk_count >= 3:
            risks["overall_risk_level"] = "high"
        elif risk_count >= 2:
            risks["overall_risk_level"] = "medium"
        
        return risks
    
    def _identify_growth_opportunities(self, profile: UserBehaviorProfile) -> List[str]:
        """识别成长机会"""
        opportunities = []
        
        # 基于响应模式识别机会
        if profile.response_pattern.response_rate > 0.7:
            opportunities.append("increase_interaction_frequency")
        
        # 基于话题偏好识别机会
        if profile.topic_preference.curiosity_areas:
            opportunities.append("explore_new_topics")
        
        # 基于情感模式识别机会
        if profile.emotional_pattern.empathy_level > 0.6:
            opportunities.append("deepen_emotional_connection")
        
        # 基于互动风格识别机会
        if profile.interaction_style.sharing_tendency > 0.5:
            opportunities.append("encourage_more_sharing")
        
        return opportunities
    
    def _predict_response_likelihood(self, profile: UserBehaviorProfile, trigger_context: Dict) -> Dict:
        """预测响应可能性"""
        try:
            # 基础响应概率
            base_probability = profile.response_pattern.response_rate
            
            # 时间因子
            current_hour = datetime.now().hour
            time_factor = 1.2 if current_hour in profile.time_pattern.peak_hours else 0.8 if current_hour in profile.time_pattern.active_hours else 0.5
            
            # 话题因子
            trigger_topic = trigger_context.get("topic", "")
            topic_factor = 1.3 if trigger_topic in profile.topic_preference.preferred_topics else 1.0
            
            # 情感因子
            trigger_emotion = trigger_context.get("emotional_tone", "neutral")
            emotion_factor = 1.2 if trigger_emotion in profile.emotional_pattern.dominant_emotions else 1.0
            
            # 计算最终概率
            final_probability = base_probability * time_factor * topic_factor * emotion_factor
            final_probability = min(1.0, max(0.0, final_probability))
            
            # 计算预测置信度
            confidence = profile.profile_confidence * 0.8  # 预测置信度通常低于画像置信度
            
            return {
                "prediction": "likely" if final_probability > 0.6 else "possible" if final_probability > 0.3 else "unlikely",
                "probability": final_probability,
                "confidence": confidence,
                "factors": {
                    "time_factor": time_factor,
                    "topic_factor": topic_factor,
                    "emotion_factor": emotion_factor
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 预测响应可能性失败: {e}")
            return {"prediction": "unknown", "probability": 0.5, "confidence": 0.0}
    
    def _determine_optimal_tone(self, profile: UserBehaviorProfile) -> str:
        """确定最优语调"""
        if profile.emotional_pattern.emotional_stability < 0.4:
            return "gentle"
        elif profile.interaction_style.humor_appreciation > 0.7:
            return "playful"
        elif profile.interaction_style.communication_style == "formal":
            return "professional"
        else:
            return "warm"
    
    async def _save_user_profile(self, profile: UserBehaviorProfile):
        """保存用户画像到数据库"""
        try:
            # 这里可以实现将用户画像保存到数据库的逻辑
            # 暂时跳过实际保存，只记录日志
            logger.info(f"✅ 用户画像已保存 (用户: {profile.user_id})")
        except Exception as e:
            logger.error(f"❌ 保存用户画像失败: {e}")

# 全局实例
_user_behavior_analyzer = None

def get_user_behavior_analyzer() -> UserBehaviorAnalyzer:
    """获取用户行为分析器实例"""
    global _user_behavior_analyzer
    if _user_behavior_analyzer is None:
        _user_behavior_analyzer = UserBehaviorAnalyzer()
    return _user_behavior_analyzer