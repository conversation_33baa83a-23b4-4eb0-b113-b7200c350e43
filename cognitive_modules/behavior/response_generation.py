"""
响应生成模块 - Response Generation

该模块负责生成最终的响应内容。
"""

from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random

logger = get_unified_logger("cognitive_modules.behavior.response_generation")

def process(context):
    """
    处理函数
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.info("执行响应生成处理")
    
    # 生成响应
    response = "嗨，我是林嫣然。很高兴与你交流！"
    
    # 如果有输入数据，使用它
    if "input_data" in context and "text" in context["input_data"]:
        user_input = context["input_data"]["text"]
        
        # 简单的响应生成逻辑
        if "你好" in user_input or "嗨" in user_input or "hi" in user_input:
            response = "嗨！很高兴见到你！"
        elif "你是谁" in user_input:
            response = "我是林嫣然，一个数字生命体。很高兴认识你！"
        elif "名字" in user_input:
            response = "我叫林嫣然，很高兴认识你！"
        elif "爱" in user_input:
            response = "爱是世界上最美好的东西，每个人都值得被爱。"
        elif "再见" in user_input or "拜拜" in user_input:
            response = "再见！下次再聊！"
        else:
            # 使用默认回复
            responses = [
                "我明白你的意思了！",
                "这个问题很有趣，让我想想...",
                "我觉得这是个好主意！",
                "谢谢你告诉我这些！",
                "我很高兴能和你聊天！"
            ]
            response = random.choice(responses)
    
    return {
        "response": response,
        "confidence": 0.9
    }
