# 行为管理器模块 - 完整实现但不依赖requests库
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import asyncio
import json
import os
import time
from typing import Dict, Any, List, Optional, Callable, Tuple, Set

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))

# 单例模式
_instance = None

class BehaviorManager:
    """行为管理器类，管理各种行为模式和行为执行"""
    
    def __init__(self):
        """初始化行为管理器"""
        self.logger = get_unified_logger("behavior_manager")
        self.logger.success("初始化行为管理器...")
        
        # 行为注册表
        self.behaviors = {}
        
        # 行为执行历史
        self.behavior_history = []
        
        # 行为执行队列
        self.behavior_queue = asyncio.Queue()
        
        # 行为模板库
        self.behavior_templates = self._load_behavior_templates()
        
        # 执行中的行为
        self.active_behaviors = set()
        
        # 行为处理任务
        self.processing_task = None
        
        # 事件总线
        try:
            from core.enhanced_event_bus import get_instance as get_event_bus
            self.event_bus = get_event_bus()
            self.logger.success("已连接事件总线")
            
            # 注册事件处理器
            self.event_bus.subscribe("thinking.decision", self._on_decision)
            self.event_bus.subscribe("behavior.execute", self._on_behavior_execute)
            self.event_bus.subscribe("system.shutdown", self._on_system_shutdown)
        except Exception as e:
            self.logger.warning_status(f"连接事件总线失败: {e}，部分功能可能受限")
            self.event_bus = None
        
        # 生命上下文
        try:
            from core.life_context import get_instance as get_life_context
            self.life_context = get_life_context()
            self.logger.debug("已连接生命上下文")
        except Exception as e:
            self.logger.warning_status(f"连接生命上下文失败: {e}，部分功能可能受限")
            self.life_context = None
        
        # 开始行为处理循环
        self.processing_task = asyncio.create_task(self._process_behavior_queue())
        
        # 初始化完成标志
        self.initialized = True
        self.logger.success("行为管理器初始化完成")
    
    def _load_behavior_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载行为模板"""
        templates = {}
        try:
            template_path = os.path.join(root_dir, "config", "behaviors", "templates.json")
            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    templates = json.load(f)
                self.logger.info(f"已加载 {len(templates)} 个行为模板")
            else:
                self.logger.warning_status(f"行为模板文件不存在: {template_path}")
                
                # 创建默认模板
                templates = {
                    "respond": {
                        "type": "communication",
                        "description": "回应用户",
                        "params": ["content", "emotion"]
                    },
                    "think": {
                        "type": "cognitive",
                        "description": "思考问题",
                        "params": ["topic", "depth"]
                    },
                    "search": {
                        "type": "information",
                        "description": "搜索信息",
                        "params": ["query", "source"]
                    }
                }
                
                # 创建目录
                os.makedirs(os.path.dirname(template_path), exist_ok=True)
                
                # 保存默认模板
                with open(template_path, 'w', encoding='utf-8') as f:
                    json.dump(templates, f, ensure_ascii=False, indent=2)
                self.logger.info(f"已创建并保存默认行为模板: {template_path}")
        except Exception as e:
            self.logger.error_status(f"加载行为模板失败: {e}")
            
            # 使用基本模板
            templates = {
                "respond": {
                    "type": "communication",
                    "description": "回应用户"
                }
            }
        
        return templates
    
    def _on_decision(self, event_data):
        """处理决策事件"""
        if not isinstance(event_data, Dict):
            self.logger.warning_status(f"无效的决策事件数据类型: {type(event_data)}")
            return
            
        if "behavior" in event_data:
            behavior = event_data["behavior"]
            
            if isinstance(behavior, Dict) and "name" in behavior:
                asyncio.create_task(self.execute_behavior(
                    behavior["name"], 
                    behavior.get("data", {})
                ))
            elif isinstance(behavior, str):
                asyncio.create_task(self.execute_behavior(behavior))
    
    def _on_behavior_execute(self, event_data):
        """处理行为执行事件"""
        if isinstance(event_data, Dict) and "name" in event_data:
            asyncio.create_task(self.execute_behavior(
                event_data["name"],
                event_data.get("data", {})
            ))
    
    async def _process_behavior_queue(self):
        """处理行为队列的异步任务"""
        self.logger.debug("行为队列处理循环已启动")
        try:
            while True:
                # 获取下一个要执行的行为
                behavior_item = await self.behavior_queue.get()
                name, data = behavior_item
                
                try:
                    # 执行行为
                    self.active_behaviors.add(name)
                    result = await self._execute_behavior_internal(name, data)
                    self.active_behaviors.remove(name)
                    
                    # 记录行为历史
                    self.behavior_history.append({
                        "name": name,
                        "data": data,
                        "result": result,
                        "timestamp": time.time()
                    })
                    
                    # 保持历史记录长度
                    if len(self.behavior_history) > 100:
                        self.behavior_history = self.behavior_history[-100:]
                    
                    # 发布行为执行结果事件
                    if self.event_bus:
                        self.event_bus.publish("behavior.executed", {
                            "name": name,
                            "data": data,
                            "result": result
                        })
                except Exception as e:
                    self.logger.error_status(f"执行行为 {name} 时出错: {e}")
                    if name in self.active_behaviors:
                        self.active_behaviors.remove(name)
                
                # 标记任务完成
                self.behavior_queue.task_done()
                
                # 短暂等待，避免CPU占用过高
                await asyncio.sleep(0.01)
        except asyncio.CancelledError:
            self.logger.info("行为队列处理循环已取消")
        except Exception as e:
            self.logger.error_status(f"行为队列处理循环出错: {e}")
    
    async def _execute_behavior_internal(self, name: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """内部行为执行方法"""
        if data is None:
            data = {}
            
        self.logger.info(f"执行行为: {name}, 参数: {data}")
        
        # 检查是否有注册的行为处理函数
        if name in self.behaviors:
            handler = self.behaviors[name]
            result = await handler(data) if asyncio.iscoroutinefunction(handler) else handler(data)
            return {"success": True, "data": result}
            
        # 检查内置行为
        method_name = f"_behavior_{name}"
        if hasattr(self, method_name) and callable(getattr(self, method_name)):
            method = getattr(self, method_name)
            result = await method(data) if asyncio.iscoroutinefunction(method) else method(data)
            return {"success": True, "data": result}
            
        # 尝试使用模板
        if name in self.behavior_templates:
            template = self.behavior_templates[name]
            # 模板处理逻辑
            self.logger.info(f"使用模板执行行为: {name}, 类型: {template.get('type')}")
            
            # 可以根据模板类型分发到不同的处理逻辑
            return {"success": True, "template_used": template["type"]}
        
        self.logger.warning_status(f"未找到行为处理方法: {name}")
        return {"success": False, "reason": "behavior_not_found"}
    
    async def execute_behavior(self, name: str, data: Dict[str, Any] = None) -> bool:
        """执行行为"""
        if data is None:
            data = {}
            
        # 将行为添加到队列
        await self.behavior_queue.put((name, data))
        return True
    
    async def register_behavior(self, name: str, handler: Callable) -> bool:
        """注册行为处理函数"""
        if name in self.behaviors:
            self.logger.warning_status(f"行为 {name} 已存在，将被覆盖")
        
        self.behaviors[name] = handler
        self.logger.info(f"已注册行为: {name}")
        return True
    
    async def get_active_behaviors(self) -> Set[str]:
        """获取当前活跃的行为"""
        return self.active_behaviors.copy()
    
    async def get_behavior_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取行为历史"""
        return self.behavior_history[-limit:] if self.behavior_history else []
    
    async def _behavior_respond(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """内置行为: 回应"""
        content = data.get("content", "")
        self.logger.info(f"生成回应: {content[:50]}...")
        
        # 如果有事件总线，发布回应事件
        if self.event_bus:
            self.event_bus.publish("behavior.responded", {
                "content": content,
                "emotion": data.get("emotion", "neutral")
            })
            
        return {"content": content}
    
    async def _behavior_think(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """内置行为: 思考"""
        topic = data.get("topic", "")
        self.logger.info(f"思考主题: {topic}")
        
        # 模拟思考过程
        await asyncio.sleep(0.5)
        
        return {"result": f"思考了关于 '{topic}' 的问题"}
    
    async def _on_system_shutdown(self, _):
        """系统关闭时的清理"""
        if self.processing_task and not self.processing_task.done():
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        self.logger.info("行为管理器已关闭")

def get_instance():
    """获取行为管理器实例（同步版本）"""
    global _instance
    if _instance is None:
        _instance = BehaviorManager()
    return _instance
