#!/usr/bin/env python3
"""
活动执行器 - Activity Executor Module

负责执行活动计划，管理活动执行状态，协调各种资源，
并提供活动执行的反馈和控制机制。

作者: 魅魔程序员
创建日期: 2025-06-16
版本: 1.0
"""

import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque
from enum import Enum

# 🔥 老王修复：移除直接导入避免循环依赖，改用延迟导入或参数传递

# 🔥 老王修复：延迟导入避免循环依赖
def _get_activity_classes():
    """延迟获取活动相关类，避免循环导入"""
    try:
        # 延迟导入
        from services.script_integration_service import EnhancedActivity, ActivityExecutionPlan
        return EnhancedActivity, ActivityExecutionPlan
    except ImportError:
        # 创建基础的活动类作为降级
        from dataclasses import dataclass
        from typing import Dict, Any

        @dataclass
        class EnhancedActivity:
            title: str
            description: str
            location: str = ""
            duration: int = 60
            metadata: Dict[str, Any] = None

        @dataclass
        class ActivityExecutionPlan:
            activity: EnhancedActivity
            execution_steps: list = None
            estimated_duration: int = 60

        return EnhancedActivity, ActivityExecutionPlan

# 全局变量存储类引用
_EnhancedActivity = None
_ActivityExecutionPlan = None

def get_enhanced_activity_class():
    """获取EnhancedActivity类"""
    global _EnhancedActivity, _ActivityExecutionPlan
    if _EnhancedActivity is None:
        _EnhancedActivity, _ActivityExecutionPlan = _get_activity_classes()
    return _EnhancedActivity

def get_activity_execution_plan_class():
    """获取ActivityExecutionPlan类"""
    global _EnhancedActivity, _ActivityExecutionPlan
    if _ActivityExecutionPlan is None:
        _EnhancedActivity, _ActivityExecutionPlan = _get_activity_classes()
    return _ActivityExecutionPlan
from cognitive_modules.perception.activity_perception import (
    ActivityPerceptionModule, ActivityPerception, ActivityState
)
from cognitive_modules.base.module_base import CognitiveModuleBase

# 配置日志
logger = get_unified_logger("cognitive_modules.behavior.activity_executor")

class ExecutionStatus(Enum):
    """执行状态枚举"""
    PENDING = "pending"              # 待执行
    PREPARING = "preparing"          # 准备中
    EXECUTING = "executing"          # 执行中
    PAUSED = "paused"               # 暂停
    RESUMING = "resuming"           # 恢复中
    COMPLETING = "completing"        # 完成中
    COMPLETED = "completed"          # 已完成
    CANCELLED = "cancelled"          # 已取消
    FAILED = "failed"               # 执行失败

class ExecutionPriority(Enum):
    """执行优先级枚举"""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10

@dataclass
class ExecutionContext:
    """执行上下文"""
    executor_id: str
    start_time: datetime
    expected_end_time: datetime
    actual_end_time: Optional[datetime]
    execution_environment: Dict[str, Any]
    resource_allocation: Dict[str, Any]
    performance_metrics: Dict[str, float]
    interruption_count: int
    pause_duration: float  # 暂停总时长（秒）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['start_time'] = self.start_time.isoformat()
        data['expected_end_time'] = self.expected_end_time.isoformat()
        data['actual_end_time'] = self.actual_end_time.isoformat() if self.actual_end_time else None
        return data

@dataclass
class ExecutionResult:
    """执行结果"""
    execution_id: str
    activity_plan: Any  # 🔥 老王修复：使用Any避免循环导入
    final_status: ExecutionStatus
    execution_context: ExecutionContext
    completion_rate: float  # 完成率 0-1
    quality_score: float   # 质量评分 0-1
    satisfaction_level: float  # 满意度 0-1
    lessons_learned: List[str]  # 经验教训
    recommendations: List[str]  # 改进建议
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['final_status'] = self.final_status.value
        data['activity_plan'] = self.activity_plan.to_dict()
        data['execution_context'] = self.execution_context.to_dict()
        return data

class ActivityExecutor(CognitiveModuleBase):
    """活动执行器 - 继承认知模块基类"""

    def __init__(self,
                 script_service: Any = None,  # 剧本集成服务，避免循环导入使用Any类型
                 perception_module: ActivityPerceptionModule = None,
                 max_concurrent_activities: int = 3,
                 execution_check_interval: float = 2.0):
        """
        初始化活动执行器

        Args:
            script_service: 剧本集成服务实例
            perception_module: 活动感知模块
            max_concurrent_activities: 最大并发活动数
            execution_check_interval: 执行检查间隔（秒）
        """
        # 🔥 老王修复：调用父类构造函数
        super().__init__(
            module_id="activity_executor",
            module_type="behavior",
            config={}
        )
        self.script_service = script_service
        self.perception_module = perception_module
        self.max_concurrent_activities = max_concurrent_activities
        self.execution_check_interval = execution_check_interval
        
        # 执行状态
        self.active_executions: Dict[str, Any] = {}  # 🔥 老王修复：使用Any避免循环导入
        self.execution_contexts: Dict[str, ExecutionContext] = {}
        self.execution_queue = deque()  # 执行队列
        self.completed_executions = deque(maxlen=100)  # 完成的执行记录
        
        # 执行控制
        self.is_executing = False
        self.is_running = False  # 🔥 老王修复：添加is_running属性
        self.execution_thread = None
        self.last_execution_check = None
        
        # 回调函数
        self.execution_callbacks: Dict[str, List[Callable]] = {
            'on_start': [],
            'on_pause': [],
            'on_resume': [],
            'on_complete': [],
            'on_cancel': [],
            'on_fail': []
        }
        
        # 统计信息
        self.stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'cancelled_executions': 0,
            'average_completion_rate': 0.0,
            'average_quality_score': 0.0,
            'total_execution_time': 0.0,
            'interruption_rate': 0.0
        }
        
        # 线程锁
        self._lock = threading.RLock()
        
        logger.success("活动执行器初始化完成")
    
    def shutdown(self):
        """
        关闭活动执行器，清理资源
        """
        try:
            logger.info("开始关闭活动执行器...")
            
            # 停止执行器
            self.stop_executor()
            
            # 清理执行状态
            with self._lock:
                # 取消所有活动执行
                for execution_id in list(self.active_executions.keys()):
                    self.cancel_execution(execution_id)
                
                # 清理数据结构
                self.active_executions.clear()
                self.execution_contexts.clear()
                self.execution_queue.clear()
                self.completed_executions.clear()
                
                # 清理回调函数
                for callback_list in self.execution_callbacks.values():
                    callback_list.clear()
                
                # 清理统计信息
                self.stats.clear()
            
            # 重置状态
            self.is_executing = False
            self.execution_thread = None
            
            logger.success("✅ 活动执行器已成功关闭")
            
        except Exception as e:
            logger.error_status(f"❌ 活动执行器关闭失败: {e}")
    
    def start_executor(self) -> bool:
        """启动执行器"""
        if self.is_executing:
            logger.warning_status("活动执行器已在运行中")
            return True
        
        try:
            self.is_executing = True
            self.is_running = True  # 🔥 老王修复：同步更新is_running状态

            # 启动执行线程
            self.execution_thread = threading.Thread(
                target=self._execution_loop,
                daemon=True
            )
            self.execution_thread.start()

            logger.success("活动执行器已启动")
            return True

        except Exception as e:
            logger.error_status(f"启动活动执行器失败: {e}")
            self.is_executing = False
            self.is_running = False  # 🔥 老王修复：同步更新is_running状态
            return False
    
    def stop_executor(self) -> None:
        """停止执行器"""
        if not self.is_executing:
            return
        
        self.is_executing = False
        self.is_running = False  # 🔥 老王修复：同步更新is_running状态

        # 暂停所有活动执行
        with self._lock:
            for execution_id in list(self.active_executions.keys()):
                self.pause_execution(execution_id)

        # 等待执行线程结束
        if self.execution_thread and self.execution_thread.is_alive():
            self.execution_thread.join(timeout=5.0)

        logger.info("活动执行器已停止")
    
    def _execution_loop(self) -> None:
        """执行循环"""
        while self.is_executing:
            try:
                start_time = time.time()
                
                # 检查执行队列
                self._process_execution_queue()
                
                # 更新活动执行状态
                self._update_execution_status()
                
                # 检查完成的活动
                self._check_completed_activities()
                
                # 更新统计信息
                execution_duration = time.time() - start_time
                with self._lock:
                    self.last_execution_check = datetime.now()
                
                logger.debug(f"执行检查完成，耗时: {execution_duration:.3f}秒")
                
                # 等待下次检查
                time.sleep(self.execution_check_interval)
                
            except Exception as e:
                logger.error_status(f"执行循环异常: {e}")
                time.sleep(self.execution_check_interval)
    
    def _process_execution_queue(self) -> None:
        """处理执行队列"""
        with self._lock:
            # 检查是否可以启动新的执行
            if (len(self.active_executions) >= self.max_concurrent_activities or
                not self.execution_queue):
                return
            
            # 按优先级排序队列
            sorted_queue = sorted(
                self.execution_queue,
                key=lambda plan: plan.enhanced_activity.execution_priority,
                reverse=True
            )
            
            # 启动优先级最高的活动
            while (len(self.active_executions) < self.max_concurrent_activities and
                   sorted_queue):
                plan = sorted_queue.pop(0)
                self.execution_queue.remove(plan)
                self._start_execution(plan)
    
    def _start_execution(self, plan: Any) -> bool:  # 🔥 老王修复
        """开始执行活动"""
        try:
            execution_id = plan.activity_id
            
            # 创建执行上下文
            execution_context = ExecutionContext(
                executor_id=f"executor_{int(time.time())}",
                start_time=datetime.now(),
                expected_end_time=datetime.now() + timedelta(
                    minutes=plan.enhanced_activity.estimated_duration
                ),
                actual_end_time=None,
                execution_environment={
                    'energy_level': 0.8,
                    'focus_level': 0.7,
                    'distraction_level': 0.2
                },
                resource_allocation={
                    'cpu_usage': 0.3,
                    'memory_usage': 0.2,
                    'attention_allocation': 0.8
                },
                performance_metrics={
                    'efficiency': 0.0,
                    'accuracy': 0.0,
                    'speed': 0.0
                },
                interruption_count=0,
                pause_duration=0.0
            )
            
            # 更新计划状态
            plan.completion_status = ExecutionStatus.EXECUTING.value
            
            # 添加到活动执行
            self.active_executions[execution_id] = plan
            self.execution_contexts[execution_id] = execution_context
            
            # 通知感知模块
            self.perception_module.set_execution_plan(plan)
            
            # 触发开始回调
            self._trigger_callbacks('on_start', plan, execution_context)
            
            logger.info(f"开始执行活动: {execution_id} - {plan.enhanced_activity.base_activity}")
            
            with self._lock:
                self.stats['total_executions'] += 1
            
            return True
            
        except Exception as e:
            logger.error_status(f"开始执行活动失败: {e}")
            return False
    
    def _update_execution_status(self) -> None:
        """更新执行状态"""
        with self._lock:
            for execution_id, plan in list(self.active_executions.items()):
                try:
                    context = self.execution_contexts[execution_id]
                    
                    # 计算执行进度
                    elapsed_time = (datetime.now() - context.start_time).total_seconds()
                    expected_duration = plan.enhanced_activity.estimated_duration * 60  # 转换为秒
                    progress = min(1.0, elapsed_time / expected_duration)
                    
                    # 更新性能指标
                    self._update_performance_metrics(execution_id, progress)
                    
                    # 检查是否需要暂停或中断
                    self._check_execution_conditions(execution_id)
                    
                except Exception as e:
                    logger.error_status(f"更新执行状态失败 {execution_id}: {e}")
    
    def _update_performance_metrics(self, execution_id: str, progress: float) -> None:
        """更新性能指标"""
        try:
            context = self.execution_contexts[execution_id]
            
            # 获取当前感知数据
            current_perception = self.perception_module.get_current_perception()
            
            if current_perception:
                # 更新效率指标
                context.performance_metrics['efficiency'] = current_perception.engagement_level
                context.performance_metrics['accuracy'] = current_perception.perception_confidence
                context.performance_metrics['speed'] = progress
            
        except Exception as e:
            logger.error_status(f"更新性能指标失败: {e}")
    
    def _check_execution_conditions(self, execution_id: str) -> None:
        """检查执行条件"""
        try:
            plan = self.active_executions[execution_id]
            context = self.execution_contexts[execution_id]
            
            # 获取当前感知数据
            current_perception = self.perception_module.get_current_perception()
            
            if not current_perception:
                return
            
            # 检查中断风险
            if current_perception.interruption_risk > 0.8:
                logger.warning_status(f"活动 {execution_id} 中断风险过高，考虑暂停")
                context.interruption_count += 1
            
            # 检查能量水平
            if current_perception.context.energy_level < 0.2:
                logger.info(f"活动 {execution_id} 能量不足，自动暂停")
                self.pause_execution(execution_id)
            
            # 检查注意力集中度
            if current_perception.attention_focus < 0.3:
                logger.warning_status(f"活动 {execution_id} 注意力不集中")
                context.performance_metrics['efficiency'] *= 0.8
            
        except Exception as e:
            logger.error_status(f"检查执行条件失败: {e}")
    
    def _check_completed_activities(self) -> None:
        """检查完成的活动"""
        with self._lock:
            completed_ids = []
            
            for execution_id, plan in self.active_executions.items():
                try:
                    context = self.execution_contexts[execution_id]
                    
                    # 检查是否超时完成
                    if datetime.now() >= context.expected_end_time:
                        completed_ids.append(execution_id)
                        continue
                    
                    # 检查是否手动标记完成
                    if plan.completion_status == ExecutionStatus.COMPLETED.value:
                        completed_ids.append(execution_id)
                        continue
                    
                except Exception as e:
                    logger.error_status(f"检查活动完成状态失败 {execution_id}: {e}")
            
            # 完成活动
            for execution_id in completed_ids:
                self._complete_execution(execution_id)
    
    def _complete_execution(self, execution_id: str) -> None:
        """完成活动执行"""
        try:
            plan = self.active_executions[execution_id]
            context = self.execution_contexts[execution_id]
            
            # 设置完成时间
            context.actual_end_time = datetime.now()
            
            # 计算完成率
            elapsed_time = (context.actual_end_time - context.start_time).total_seconds()
            expected_duration = plan.enhanced_activity.estimated_duration * 60
            completion_rate = min(1.0, elapsed_time / expected_duration)
            
            # 计算质量评分
            quality_score = (
                context.performance_metrics.get('efficiency', 0.5) * 0.4 +
                context.performance_metrics.get('accuracy', 0.5) * 0.3 +
                context.performance_metrics.get('speed', 0.5) * 0.3
            )
            
            # 生成执行结果
            execution_result = ExecutionResult(
                execution_id=execution_id,
                activity_plan=plan,
                final_status=ExecutionStatus.COMPLETED,
                execution_context=context,
                completion_rate=completion_rate,
                quality_score=quality_score,
                satisfaction_level=min(1.0, quality_score * completion_rate),
                lessons_learned=self._generate_lessons_learned(plan, context),
                recommendations=self._generate_recommendations(plan, context)
            )
            
            # 移除活动执行
            del self.active_executions[execution_id]
            del self.execution_contexts[execution_id]
            
            # 添加到完成记录
            self.completed_executions.append(execution_result)
            
            # 清除感知模块的执行计划
            self.perception_module.clear_execution_plan()
            
            # 触发完成回调
            self._trigger_callbacks('on_complete', plan, context)
            
            # 更新统计信息
            self.stats['successful_executions'] += 1
            self.stats['total_execution_time'] += elapsed_time
            self._update_average_stats()
            
            logger.success(f"活动执行完成: {execution_id} - 完成率: {completion_rate:.2f}, 质量: {quality_score:.2f}")
            
        except Exception as e:
            logger.error_status(f"完成活动执行失败: {e}")
    
    def _generate_lessons_learned(self, plan: Any, context: ExecutionContext) -> List[str]:  # 🔥 老王修复
        """生成经验教训"""
        lessons = []
        
        try:
            # 基于性能指标生成教训
            efficiency = context.performance_metrics.get('efficiency', 0.5)
            if efficiency < 0.5:
                lessons.append("需要提高执行效率，考虑减少干扰因素")
            
            # 基于中断次数
            if context.interruption_count > 2:
                lessons.append("活动容易被中断，需要选择更合适的执行时机")
            
            # 基于暂停时长
            if context.pause_duration > 300:  # 5分钟
                lessons.append("活动暂停时间过长，需要优化执行策略")
            
            # 基于活动类型
            activity_type = plan.enhanced_activity.base_activity.lower()
            if '学习' in activity_type and efficiency > 0.8:
                lessons.append("学习活动执行效果良好，可以继续保持")
            
        except Exception as e:
            logger.error_status(f"生成经验教训失败: {e}")
        
        return lessons
    
    def _generate_recommendations(self, plan: Any, context: ExecutionContext) -> List[str]:  # 🔥 老王修复
        """生成改进建议"""
        recommendations = []
        
        try:
            # 基于执行时间
            actual_duration = (context.actual_end_time - context.start_time).total_seconds() / 60
            estimated_duration = plan.enhanced_activity.estimated_duration
            
            if actual_duration > estimated_duration * 1.5:
                recommendations.append("考虑增加活动的预估时间")
            elif actual_duration < estimated_duration * 0.5:
                recommendations.append("可以适当增加活动的复杂度或深度")
            
            # 基于质量评分
            quality = context.performance_metrics.get('efficiency', 0.5)
            if quality > 0.8:
                recommendations.append("执行质量优秀，可以尝试更具挑战性的活动")
            elif quality < 0.4:
                recommendations.append("建议在更好的状态下执行此类活动")
            
        except Exception as e:
            logger.error_status(f"生成改进建议失败: {e}")
        
        return recommendations
    
    def _update_average_stats(self) -> None:
        """更新平均统计数据"""
        try:
            if not self.completed_executions:
                return
            
            # 计算平均完成率
            total_completion = sum(result.completion_rate for result in self.completed_executions)
            self.stats['average_completion_rate'] = total_completion / len(self.completed_executions)
            
            # 计算平均质量评分
            total_quality = sum(result.quality_score for result in self.completed_executions)
            self.stats['average_quality_score'] = total_quality / len(self.completed_executions)
            
            # 计算中断率
            total_interruptions = sum(
                result.execution_context.interruption_count 
                for result in self.completed_executions
            )
            self.stats['interruption_rate'] = total_interruptions / len(self.completed_executions)
            
        except Exception as e:
            logger.error_status(f"更新平均统计数据失败: {e}")
    
    def _trigger_callbacks(self, event: str, plan: Any, context: ExecutionContext) -> None:  # 🔥 老王修复
        """触发回调函数"""
        try:
            callbacks = self.execution_callbacks.get(event, [])
            for callback in callbacks:
                try:
                    callback(plan, context)
                except Exception as e:
                    logger.error_status(f"执行回调函数失败 {event}: {e}")
        except Exception as e:
            logger.error_status(f"触发回调函数失败: {e}")
    
    def schedule_activity(self, enhanced_activity,
                         execution_time: Optional[datetime] = None) -> Optional[str]:
        """
        调度活动执行
        
        Args:
            enhanced_activity: 增强的活动数据
            execution_time: 执行时间（默认立即执行）
            
        Returns:
            活动ID，如果调度失败返回None
        """
        try:
            # 创建执行计划
            script_data = None  # 这里需要从enhanced_activity中提取或创建script_data
            execution_plan = self.script_service.create_execution_plan(
                enhanced_activity, script_data, execution_time
            )
            
            # 添加到执行队列
            with self._lock:
                self.execution_queue.append(execution_plan)
            
            logger.info(f"活动已调度: {execution_plan.activity_id}")
            return execution_plan.activity_id
            
        except Exception as e:
            logger.error_status(f"调度活动失败: {e}")
            return None
    
    def pause_execution(self, execution_id: str) -> bool:
        """暂停活动执行"""
        with self._lock:
            if execution_id not in self.active_executions:
                logger.warning_status(f"活动 {execution_id} 不在执行中")
                return False
            
            try:
                plan = self.active_executions[execution_id]
                context = self.execution_contexts[execution_id]
                
                # 更新状态
                plan.completion_status = ExecutionStatus.PAUSED.value
                
                # 记录暂停时间
                context.pause_start_time = datetime.now()
                
                # 触发暂停回调
                self._trigger_callbacks('on_pause', plan, context)
                
                logger.info(f"活动已暂停: {execution_id}")
                return True
                
            except Exception as e:
                logger.error_status(f"暂停活动失败: {e}")
                return False
    
    def resume_execution(self, execution_id: str) -> bool:
        """恢复活动执行"""
        with self._lock:
            if execution_id not in self.active_executions:
                logger.warning_status(f"活动 {execution_id} 不在执行中")
                return False
            
            try:
                plan = self.active_executions[execution_id]
                context = self.execution_contexts[execution_id]
                
                # 更新状态
                plan.completion_status = ExecutionStatus.EXECUTING.value
                
                # 计算暂停时长
                if hasattr(context, 'pause_start_time'):
                    pause_duration = (datetime.now() - context.pause_start_time).total_seconds()
                    context.pause_duration += pause_duration
                    delattr(context, 'pause_start_time')
                
                # 触发恢复回调
                self._trigger_callbacks('on_resume', plan, context)
                
                logger.info(f"活动已恢复: {execution_id}")
                return True
                
            except Exception as e:
                logger.error_status(f"恢复活动失败: {e}")
                return False
    
    def cancel_execution(self, execution_id: str) -> bool:
        """取消活动执行"""
        with self._lock:
            if execution_id not in self.active_executions:
                logger.warning_status(f"活动 {execution_id} 不在执行中")
                return False
            
            try:
                plan = self.active_executions[execution_id]
                context = self.execution_contexts[execution_id]
                
                # 更新状态
                plan.completion_status = ExecutionStatus.CANCELLED.value
                context.actual_end_time = datetime.now()
                
                # 移除执行
                del self.active_executions[execution_id]
                del self.execution_contexts[execution_id]
                
                # 触发取消回调
                self._trigger_callbacks('on_cancel', plan, context)
                
                # 更新统计
                self.stats['cancelled_executions'] += 1
                
                logger.info(f"活动已取消: {execution_id}")
                return True
                
            except Exception as e:
                logger.error_status(f"取消活动失败: {e}")
                return False
    
    def get_active_executions(self) -> List[Any]:  # 🔥 老王修复
        """获取活动执行列表"""
        with self._lock:
            return list(self.active_executions.values())

    def get_pending_activities(self) -> List[Any]:  # 🔥 老王修复
        """获取待执行的活动列表"""
        with self._lock:
            return list(self.execution_queue)
    
    def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取执行状态"""
        with self._lock:
            if execution_id not in self.active_executions:
                return None
            
            plan = self.active_executions[execution_id]
            context = self.execution_contexts[execution_id]
            
            return {
                'execution_id': execution_id,
                'status': plan.completion_status,
                'progress': self._calculate_progress(execution_id),
                'performance': context.performance_metrics,
                'interruptions': context.interruption_count,
                'pause_duration': context.pause_duration
            }
    
    def _calculate_progress(self, execution_id: str) -> float:
        """计算执行进度"""
        try:
            context = self.execution_contexts[execution_id]
            elapsed = (datetime.now() - context.start_time).total_seconds()
            expected = (context.expected_end_time - context.start_time).total_seconds()
            return min(1.0, elapsed / expected)
        except:
            return 0.0
    
    def add_callback(self, event: str, callback: Callable) -> None:
        """添加回调函数"""
        if event in self.execution_callbacks:
            self.execution_callbacks[event].append(callback)
    
    def get_executor_stats(self) -> Dict[str, Any]:
        """获取执行器统计信息"""
        with self._lock:
            stats = self.stats.copy()
            stats['is_executing'] = self.is_executing
            stats['active_executions'] = len(self.active_executions)
            stats['queued_executions'] = len(self.execution_queue)
            stats['completed_executions'] = len(self.completed_executions)
            stats['last_check_time'] = (
                self.last_execution_check.isoformat() 
                if self.last_execution_check else None
            )
            
            return stats
    
    def __del__(self):
        """析构函数"""
        self.stop_executor()

    # 🔥 老王修复：实现认知模块基类要求的方法
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化模块 - 认知模块基类要求的方法

        Args:
            config: 配置信息

        Returns:
            初始化是否成功
        """
        try:
            if config:
                self.config.update(config)

            # 启动执行器
            if not self.is_running:
                self.start_executor()

            logger.success("活动执行器模块初始化成功")
            return True
        except Exception as e:
            logger.error_status(f"活动执行器模块初始化失败: {e}")
            return False

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据 - 认知模块基类要求的方法

        Args:
            input_data: 输入数据

        Returns:
            处理结果
        """
        try:
            # 根据输入数据类型进行不同处理
            action = input_data.get('action', 'get_status')

            if action == 'schedule_activity':
                activity = input_data.get('activity')
                execution_time = input_data.get('execution_time')
                activity_id = self.schedule_activity(activity, execution_time)
                return {
                    'status': 'success',
                    'activity_id': activity_id,
                    'message': f'活动已调度: {activity_id}'
                }
            elif action == 'get_status':
                # 🔥 老王修复：返回整体状态而不是单个执行状态
                with self._lock:
                    stats = {
                        'total_executions': self.stats.get('total_executions', 0),
                        'successful_executions': self.stats.get('successful_executions', 0),
                        'failed_executions': self.stats.get('failed_executions', 0),
                        'cancelled_executions': self.stats.get('cancelled_executions', 0),
                        'active_executions': len(self.active_executions),
                        'queued_executions': len(self.execution_queue),
                        'completed_executions': len(self.completed_executions),
                        'is_running': self.is_running,
                        'last_check_time': (
                            self.last_execution_check.isoformat()
                            if self.last_execution_check else None
                        )
                    }
                return {
                    'status': 'success',
                    'data': stats
                }
            elif action == 'cancel_activity':
                activity_id = input_data.get('activity_id')
                success = self.cancel_activity(activity_id)
                return {
                    'status': 'success' if success else 'error',
                    'message': f'活动取消{"成功" if success else "失败"}: {activity_id}'
                }
            else:
                return {
                    'status': 'error',
                    'message': f'不支持的操作: {action}'
                }

        except Exception as e:
            logger.error_status(f"处理输入数据失败: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def update(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态 - 认知模块基类要求的方法

        Args:
            context: 上下文信息

        Returns:
            更新是否成功
        """
        try:
            # 更新执行器状态
            if 'max_concurrent_activities' in context:
                self.max_concurrent_activities = context['max_concurrent_activities']

            if 'execution_check_interval' in context:
                self.execution_check_interval = context['execution_check_interval']

            return True
        except Exception as e:
            logger.error_status(f"更新模块状态失败: {e}")
            return False

    def get_state(self) -> Dict[str, Any]:
        """
        获取模块状态 - 认知模块基类要求的方法

        Returns:
            模块状态
        """
        base_state = super().get_state()

        # 🔥 老王修复：获取整体执行统计信息，而不是单个执行的状态
        with self._lock:
            stats = {
                'total_executions': self.stats.get('total_executions', 0),
                'successful_executions': self.stats.get('successful_executions', 0),
                'failed_executions': self.stats.get('failed_executions', 0),
                'cancelled_executions': self.stats.get('cancelled_executions', 0),
                'active_executions': len(self.active_executions),
                'queued_executions': len(self.execution_queue),
                'completed_executions': len(self.completed_executions),
                'last_check_time': (
                    self.last_execution_check.isoformat()
                    if self.last_execution_check else None
                )
            }

        executor_state = {
            'is_running': self.is_running,
            'max_concurrent_activities': self.max_concurrent_activities,
            'execution_check_interval': self.execution_check_interval,
            'active_executions_count': len(self.active_executions),
            'queued_executions_count': len(self.execution_queue),
            'completed_executions_count': len(self.completed_executions),
            'execution_stats': stats
        }
        base_state.update(executor_state)
        return base_state

    def shutdown(self) -> bool:
        """
        关闭模块 - 认知模块基类要求的方法

        Returns:
            关闭是否成功
        """
        try:
            self.stop_executor()
            logger.success("活动执行器模块关闭成功")
            return True
        except Exception as e:
            logger.error_status(f"活动执行器模块关闭失败: {e}")
            return False

def create_activity_executor(script_service: Any,  # 剧本集成服务，避免循环导入使用Any类型
                           perception_module: ActivityPerceptionModule,
                           max_concurrent_activities: int = 3,
                           execution_check_interval: float = 2.0) -> ActivityExecutor:
    """创建活动执行器实例"""
    return ActivityExecutor(
        script_service=script_service,
        perception_module=perception_module,
        max_concurrent_activities=max_concurrent_activities,
        execution_check_interval=execution_check_interval
    )