#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行为规划模块 (Action Planning)

该模块负责数字生命体的行为规划和协调，包括短期行为序列规划、
长期行为策略制定以及行为适应性调整。

作者: Claude
创建日期: 2024-07-21
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import random
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from datetime import datetime, timedelta

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from utilities.storage_manager import get_instance as get_storage_instance
from core.integrated_event_bus import get_instance as get_event_bus
from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.autonomy.decision_making import get_instance as get_decision_making
from cognitive_modules.autonomy.conscious_awareness import get_instance as get_conscious_awareness
from cognitive_modules.memory.memory_retrieval import get_instance as get_memory_retrieval

# 设置日志
setup_unified_logging()
logger = get_unified_logger("behavior.action_planning")


class ActionPlanning(CognitiveModuleBase):
    """行为规划模块，负责计划和协调数字生命体的行为序列"""
    
    def __init__(self, module_id: str = "action_planning", config: Dict[str, Any] = None):
        """
        初始化行为规划模块
        
        参数:
            module_id: 模块ID
            config: 配置参数
        """
        super().__init__(module_id, "behavior", config)
        
        # 设置模块描述
        self.description = "行为规划模块 - 负责行为序列的计划和协调"
        
        # 设置依赖模块
        self.dependencies = ["decision_making", "conscious_awareness", "memory_retrieval"]
        
        # 事件总线
        self.event_bus = None
        
        # 存储管理器
        self.storage = None
        
        # 相关模块引用
        self.decision_making = None
        self.conscious_awareness = None
        self.memory_retrieval = None
        
        # 行为规划状态
        self.current_plans = []  # 当前活跃的行为计划列表
        self.execution_history = []  # 行为执行历史
        self.plan_templates = {}  # 预定义的行为计划模板
        
        # 行为规划参数
        self.max_plan_steps = 10  # 单个计划的最大步骤数
        self.max_active_plans = 5  # 最大同时活跃计划数
        self.plan_revision_threshold = 0.7  # 计划修改阈值
        
        # 行为类型定义
        self.action_types = {
            "dialog": {  # 对话行为
                "priority_range": (0.5, 1.0),
                "duration_range": (1, 5),  # 秒
                "resource_cost": 0.2
            },
            "cognitive": {  # 认知行为
                "priority_range": (0.6, 0.9),
                "duration_range": (5, 30),  # 秒
                "resource_cost": 0.5
            },
            "creative": {  # 创造性行为
                "priority_range": (0.3, 0.8),
                "duration_range": (30, 300),  # 秒
                "resource_cost": 0.7
            },
            "learning": {  # 学习行为
                "priority_range": (0.4, 0.7),
                "duration_range": (60, 600),  # 秒
                "resource_cost": 0.6
            },
            "social": {  # 社交行为
                "priority_range": (0.5, 0.8),
                "duration_range": (10, 120),  # 秒
                "resource_cost": 0.4
            }
        }
        
        # 计划执行线程
        self.planning_thread = None
        self.planning_running = False
        
        # 更新间隔（秒）
        self.update_interval = 30  # 默认每30秒更新一次
        
        logger.success(f"行为规划模块创建成功 (ID: {self.module_id})")
    
    def _do_initialize(self) -> None:
        """
        执行初始化操作
        """
        logger.success(f"正在初始化行为规划模块 (ID: {self.module_id})...")
        
        # 获取事件总线
        self.event_bus = get_event_bus()
        
        # 获取存储管理器
        self.storage = get_storage_instance()
        
        # 获取相关模块实例
        self.decision_making = get_decision_making()
        self.conscious_awareness = get_conscious_awareness()
        self.memory_retrieval = get_memory_retrieval()
        
        # 确保相关模块已初始化
        if not self.decision_making.is_initialized:
            self.decision_making.initialize()
        
        if not self.conscious_awareness.is_initialized:
            self.conscious_awareness.initialize()
        
        if not self.memory_retrieval.is_initialized:
            self.memory_retrieval.initialize()
        
        # 从配置中获取参数
        if self.config:
            if "max_plan_steps" in self.config:
                self.max_plan_steps = self.config["max_plan_steps"]
            
            if "max_active_plans" in self.config:
                self.max_active_plans = self.config["max_active_plans"]
            
            if "update_interval" in self.config:
                self.update_interval = self.config["update_interval"]
            
            if "plan_revision_threshold" in self.config:
                self.plan_revision_threshold = self.config["plan_revision_threshold"]
        
        # 加载行为计划模板
        self._load_plan_templates()
        
        # 加载行为规划状态
        self._load_planning_state()
        
        # 订阅事件
        self._subscribe_events()
        
        # 清理过期计划
        self._cleanup_expired_plans()
        
        logger.success(f"行为规划模块初始化成功 (ID: {self.module_id})")
    
    def _do_activate(self) -> None:
        """
        激活模块
        """
        logger.info(f"正在激活行为规划模块 (ID: {self.module_id})...")
        
        # 启动计划执行线程
        self.planning_running = True
        self.planning_thread = threading.Thread(
            target=self._planning_worker,
            daemon=True
        )
        self.planning_thread.start()
        
        logger.success(f"行为规划模块激活成功 (ID: {self.module_id})")
    
    def _do_deactivate(self) -> None:
        """
        停用模块
        """
        logger.info(f"正在停用行为规划模块 (ID: {self.module_id})...")
        
        # 停止计划执行线程
        self.planning_running = False
        if self.planning_thread and self.planning_thread.is_alive():
            self.planning_thread.join(timeout=5)
        
        logger.success(f"行为规划模块停用成功 (ID: {self.module_id})")
    
    def _do_shutdown(self) -> None:
        """
        关闭模块
        """
        logger.info(f"正在关闭行为规划模块 (ID: {self.module_id})...")
        
        # 先停用模块
        if self.is_active:
            self._do_deactivate()
        
        # 保存规划状态
        self._save_planning_state()
        
        # 取消事件订阅
        self._unsubscribe_events()
        
        logger.success(f"行为规划模块关闭成功 (ID: {self.module_id})")
    
    def _subscribe_events(self) -> None:
        """
        订阅事件
        """
        if not self.event_bus:
            logger.warning_status("事件总线未初始化，无法订阅事件")
            return
            
        # 订阅用户交互事件
        self.event_bus.subscribe("user_message", self._on_user_message)
        
        # 订阅决策事件
        self.event_bus.subscribe("decision_made", self._on_decision_made)
        
        # 订阅意图生成事件
        self.event_bus.subscribe("autonomy.intention.generated", self._on_intention_generated)
        
        # 订阅行为完成事件
        self.event_bus.subscribe("action_completed", self._on_action_completed)
        
        # 订阅意识更新事件
        self.event_bus.subscribe("autonomy.consciousness.updated", self._on_consciousness_updated)
    
    def _unsubscribe_events(self) -> None:
        """
        取消事件订阅
        """
        if not self.event_bus:
            return
            
        # 取消订阅用户交互事件
        self.event_bus.unsubscribe("user_message", self._on_user_message)
        
        # 取消订阅决策事件
        self.event_bus.unsubscribe("decision_made", self._on_decision_made)
        
        # 取消订阅意图生成事件
        self.event_bus.unsubscribe("autonomy.intention.generated", self._on_intention_generated)
        
        # 取消订阅行为完成事件
        self.event_bus.unsubscribe("action_completed", self._on_action_completed)
        
        # 取消订阅意识更新事件
        self.event_bus.unsubscribe("autonomy.consciousness.updated", self._on_consciousness_updated)
    
    def _planning_worker(self) -> None:
        """
        计划执行线程函数
        """
        while self.planning_running:
            try:
                # 更新行为计划
                self._update_plans()
                
                # 执行高优先级的行为
                self._execute_planned_actions()
                
                # 清理过期计划
                self._cleanup_expired_plans()
                
                # 休眠一段时间
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error_status(f"行为规划过程中发生错误: {str(e)}")
                time.sleep(300)  # 出错时等待5分钟再试
    
    def _load_plan_templates(self) -> None:
        """
        加载行为计划模板
        """
        try:
            # 从配置加载预定义的计划模板
            templates = self.config.get("plan_templates", {})
            if templates:
                self.plan_templates = templates
                logger.info(f"已加载 {len(templates)} 个行为计划模板")
            else:
                # 如果配置中没有模板，使用默认模板
                self.plan_templates = {
                    "user_response": {
                        "description": "对用户问题或请求的标准响应流程",
                        "trigger": "user_message",
                        "steps": [
                            {
                                "type": "cognitive",
                                "action": "analyze_intent",
                                "description": "分析用户意图",
                                "priority": 0.9
                            },
                            {
                                "type": "dialog",
                                "action": "generate_response",
                                "description": "生成合适的回复",
                                "priority": 0.95
                            }
                        ]
                    },
                    "learning_session": {
                        "description": "自主学习新知识的流程",
                        "trigger": "learning_motivation",
                        "steps": [
                            {
                                "type": "cognitive",
                                "action": "identify_knowledge_gap",
                                "description": "识别知识缺口",
                                "priority": 0.6
                            },
                            {
                                "type": "learning",
                                "action": "acquire_knowledge",
                                "description": "获取新知识",
                                "priority": 0.7
                            },
                            {
                                "type": "cognitive",
                                "action": "integrate_knowledge",
                                "description": "整合新知识",
                                "priority": 0.65
                            }
                        ]
                    },
                    "creative_expression": {
                        "description": "创意表达流程",
                        "trigger": "creative_motivation",
                        "steps": [
                            {
                                "type": "cognitive",
                                "action": "gather_inspiration",
                                "description": "收集灵感",
                                "priority": 0.5
                            },
                            {
                                "type": "creative",
                                "action": "generate_content",
                                "description": "生成创意内容",
                                "priority": 0.6
                            }
                        ]
                    },
                    "social_interaction": {
                        "description": "主动社交互动流程",
                        "trigger": "social_motivation",
                        "steps": [
                            {
                                "type": "cognitive",
                                "action": "analyze_social_context",
                                "description": "分析社交上下文",
                                "priority": 0.7
                            },
                            {
                                "type": "social",
                                "action": "initiate_conversation",
                                "description": "发起对话",
                                "priority": 0.6
                            },
                            {
                                "type": "dialog",
                                "action": "maintain_engagement",
                                "description": "保持互动",
                                "priority": 0.65
                            }
                        ]
                    }
                }
                logger.info("已加载默认行为计划模板")
        except Exception as e:
            logger.error_status(f"加载行为计划模板时发生错误: {str(e)}")
            # 设置一个最小的默认模板
            self.plan_templates = {"user_response": {"description": "用户响应", "trigger": "user_message", "steps": []}}
    
    def _load_planning_state(self) -> None:
        """
        从存储中加载行为规划状态
        """
        if not self.storage:
            logger.warning_status("存储管理器未初始化，无法加载行为规划状态")
            return
            
        try:
            # 构建存储键
            storage_key = f"behavior:{self.module_id}:planning_state"
            
            # 尝试从存储中获取规划状态
            stored_state = self.storage.get(storage_key)
            if stored_state:
                # 解析存储的状态数据
                state_data = json.loads(stored_state)
                
                # 更新规划状态
                if "current_plans" in state_data:
                    self.current_plans = state_data["current_plans"]
                
                if "execution_history" in state_data:
                    self.execution_history = state_data["execution_history"]
                
                logger.info(f"已从存储加载行为规划状态 (ID: {self.module_id})")
        except Exception as e:
            logger.error_status(f"加载行为规划状态时发生错误: {str(e)}")
    
    def _save_planning_state(self) -> None:
        """
        将行为规划状态保存到存储
        """
        if not self.storage:
            logger.warning_status("存储管理器未初始化，无法保存行为规划状态")
            return
            
        try:
            # 构建存储键
            storage_key = f"behavior:{self.module_id}:planning_state"
            
            # 构建要存储的状态数据
            state_data = {
                "current_plans": self.current_plans,
                "execution_history": self.execution_history[-100:],  # 只保存最近100条执行记录
                "last_updated": datetime.now().isoformat()
            }
            
            # 将状态数据转换为JSON并存储
            self.storage.set(storage_key, json.dumps(state_data))
            
            logger.info(f"已将行为规划状态保存到存储 (ID: {self.module_id})")
        except Exception as e:
            logger.error_status(f"保存行为规划状态时发生错误: {str(e)}")
    
    def _cleanup_expired_plans(self) -> None:
        """
        清理过期的行为计划
        """
        current_time = datetime.now()
        updated_plans = []
        
        for plan in self.current_plans:
            # 检查计划是否有过期时间
            if "expires_at" in plan:
                expires_at = datetime.fromisoformat(plan["expires_at"])
                if current_time > expires_at:
                    logger.info(f"计划已过期: {plan['id']} - {plan['description']}")
                    continue
            
            # 检查计划是否已完成
            if plan.get("status") == "completed":
                logger.success(f"计划已完成: {plan['id']} - {plan['description']}")
                continue
            
            # 保留未过期且未完成的计划
            updated_plans.append(plan)
        
        # 更新计划列表
        self.current_plans = updated_plans
    
    def _update_plans(self) -> None:
        """
        更新行为计划，检查是否需要生成新计划或修改现有计划
        """
        # 检查是否需要生成新计划
        self._generate_new_plans()
        
        # 更新现有计划的优先级
        self._update_plan_priorities()
        
        # 检查是否需要修改计划
        self._revise_plans()
    
    def _generate_new_plans(self) -> None:
        """
        基于当前状态生成新的行为计划
        """
        # 如果已有计划数量达到上限，不生成新计划
        if len(self.current_plans) >= self.max_active_plans:
            return
        
        # 获取当前的动机和意图
        motivations = []
        intentions = []
        
        # 从动机系统获取动机
        if hasattr(self.decision_making, 'motivation_system') and self.decision_making.motivation_system:
            motivations = self.decision_making.motivation_system.get_all_motivations()
        
        # 从自主意识获取意图
        if self.conscious_awareness:
            intentions = self.conscious_awareness.get_current_intentions()
        
        # 处理每个动机和意图，看是否需要生成新计划
        triggers = set()
        
        # 从动机中提取触发器
        for motivation in motivations:
            motivation_type = motivation.get("type", "")
            if motivation.get("strength", 0) > 0.6:  # 只考虑强度较高的动机
                if motivation_type == "growth":
                    triggers.add("learning_motivation")
                elif motivation_type == "curiosity":
                    triggers.add("learning_motivation")
                    triggers.add("creative_motivation")
                elif motivation_type == "connection":
                    triggers.add("social_motivation")
                elif motivation_type == "competence":
                    triggers.add("learning_motivation")
        
        # 从意图中提取触发器
        for intention in intentions:
            intention_type = intention.get("type", "")
            if intention_type == "autonomous_learning":
                triggers.add("learning_motivation")
            elif intention_type == "social_initiative":
                triggers.add("social_motivation")
            elif intention_type == "exploration":
                triggers.add("learning_motivation")
                triggers.add("creative_motivation")
            elif intention_type == "skill_mastery":
                triggers.add("learning_motivation")
        
        # 根据触发器匹配计划模板
        for trigger in triggers:
            for template_id, template in self.plan_templates.items():
                if template.get("trigger") == trigger:
                    # 检查是否已存在此类型的计划
                    if any(p.get("template_id") == template_id for p in self.current_plans):
                        continue
                    
                    # 生成新计划
                    self._create_plan_from_template(template_id, template, trigger)
    
    def _create_plan_from_template(self, template_id: str, template: Dict[str, Any], trigger: str) -> None:
        """
        根据模板创建新的行为计划
        
        参数:
            template_id: 模板ID
            template: 模板定义
            trigger: 触发器
        """
        # 如果已有计划数量达到上限，不创建新计划
        if len(self.current_plans) >= self.max_active_plans:
            return
        
        # 创建计划基本信息
        plan_id = f"plan_{int(time.time())}_{random.randint(1000, 9999)}"
        creation_time = datetime.now()
        
        # 设置过期时间 (默认24小时后)
        expires_at = creation_time + timedelta(hours=24)
        
        # 复制步骤并根据需要调整
        steps = []
        for step_template in template.get("steps", []):
            step = step_template.copy()
            
            # 添加步骤状态信息
            step["status"] = "pending"
            step["step_id"] = f"{plan_id}_step_{len(steps) + 1}"
            
            # 添加步骤到列表
            steps.append(step)
        
        # 创建完整计划
        plan = {
            "id": plan_id,
            "template_id": template_id,
            "description": template.get("description", "未命名计划"),
            "trigger": trigger,
            "status": "pending",
            "priority": 0.5,  # 初始优先级
            "steps": steps,
            "current_step_index": 0,
            "progress": 0.0,
            "created_at": creation_time.isoformat(),
            "updated_at": creation_time.isoformat(),
            "expires_at": expires_at.isoformat()
        }
        
        # 添加计划到当前计划列表
        self.current_plans.append(plan)
        logger.info(f"已创建新行为计划: {plan_id} - {plan['description']}")
        
        # 发布计划创建事件
        if self.event_bus:
            self.event_bus.publish("behavior.plan.created", {
                "plan_id": plan_id,
                "description": plan["description"],
                "trigger": trigger
            })
    
    def _update_plan_priorities(self) -> None:
        """
        更新计划优先级
        """
        # 获取意识状态
        consciousness_level = 0.5
        if self.conscious_awareness:
            consciousness_level = self.conscious_awareness.get_consciousness_level()
        
        # 获取当前注意力焦点
        attention_focus = None
        if self.conscious_awareness:
            attention_focus = self.conscious_awareness.get_attention_focus()
        
        # 更新每个计划的优先级
        for plan in self.current_plans:
            base_priority = plan.get("priority", 0.5)
            
            # 根据计划状态调整
            if plan.get("status") == "in_progress":
                # 进行中的计划优先级提高
                base_priority += 0.1
            
            # 根据注意力焦点调整
            if attention_focus and attention_focus.get("type") == "goal":
                goal = attention_focus.get("content", {})
                # 如果计划与当前目标相关，提高优先级
                if (goal.get("category") == "learning" and plan.get("template_id") == "learning_session") or \
                   (goal.get("category") == "social" and plan.get("template_id") == "social_interaction") or \
                   (goal.get("category") == "creative" and plan.get("template_id") == "creative_expression"):
                    base_priority += 0.15
            
            # 根据意识水平调整
            # 意识水平高时，自主计划的优先级提高
            if plan.get("trigger") in ["learning_motivation", "creative_motivation", "social_motivation"]:
                base_priority += (consciousness_level - 0.5) * 0.2
            
            # 确保优先级在有效范围内
            plan["priority"] = max(0.1, min(0.99, base_priority))
    
    def _revise_plans(self) -> None:
        """
        检查并修改现有计划
        """
        for plan in self.current_plans:
            # 只检查进行中或待执行的计划
            if plan.get("status") not in ["pending", "in_progress"]:
                continue
            
            # 检查是否需要修改计划
            revision_needed = False
            
            # 1. 检查环境变化
            if self._check_environment_change():
                revision_needed = True
            
            # 2. 检查计划执行效果
            if plan.get("status") == "in_progress" and self._check_plan_effectiveness(plan) < self.plan_revision_threshold:
                revision_needed = True
            
            # 如果需要修改，根据当前情况调整计划
            if revision_needed:
                self._modify_plan(plan)
    
    def _check_environment_change(self) -> bool:
        """
        检查环境是否发生重大变化
        
        返回:
            环境是否发生变化
        """
        # 实现环境变化检测逻辑
        # 这里仅作为示例返回False
        return False
    
    def _check_plan_effectiveness(self, plan: Dict[str, Any]) -> float:
        """
        评估计划执行效果
        
        参数:
            plan: 行为计划
            
        返回:
            0.0-1.0的有效性评分
        """
        # 实现计划有效性评估
        # 这里返回一个示例值
        return 0.8
    
    def _modify_plan(self, plan: Dict[str, Any]) -> None:
        """
        根据当前情况修改计划
        
        参数:
            plan: 要修改的行为计划
        """
        # 记录修改时间
        plan["updated_at"] = datetime.now().isoformat()
        
        # 示例修改：调整步骤优先级
        for step in plan.get("steps", []):
            if step.get("status") == "pending":
                # 根据当前状态调整步骤优先级
                step["priority"] = min(0.95, step.get("priority", 0.5) + 0.05)
        
        logger.info(f"已修改行为计划: {plan['id']} - {plan['description']}")
    
    def _execute_planned_actions(self) -> None:
        """
        执行计划中的行为
        """
        # 按优先级排序计划
        sorted_plans = sorted(self.current_plans, key=lambda p: p.get("priority", 0), reverse=True)
        
        # 尝试执行最高优先级的计划
        for plan in sorted_plans:
            # 跳过已完成或已取消的计划
            if plan.get("status") in ["completed", "canceled"]:
                continue
            
            # 获取当前步骤
            current_step_index = plan.get("current_step_index", 0)
            if current_step_index >= len(plan.get("steps", [])):
                # 所有步骤已完成，标记计划为已完成
                plan["status"] = "completed"
                plan["progress"] = 1.0
                plan["updated_at"] = datetime.now().isoformat()
                logger.success(f"计划已完成: {plan['id']} - {plan['description']}")
                
                # 发布计划完成事件
                if self.event_bus:
                    self.event_bus.publish("behavior.plan.completed", {
                        "plan_id": plan["id"],
                        "description": plan["description"]
                    })
                
                continue
            
            # 获取当前步骤
            current_step = plan["steps"][current_step_index]
            
            # 跳过已完成的步骤
            if current_step.get("status") == "completed":
                plan["current_step_index"] += 1
                # 更新进度
                plan["progress"] = (current_step_index + 1) / len(plan["steps"])
                continue
            
            # 执行当前步骤
            if current_step.get("status") == "pending":
                # 标记计划和步骤为执行中
                plan["status"] = "in_progress"
                current_step["status"] = "in_progress"
                current_step["started_at"] = datetime.now().isoformat()
                
                # 发布步骤开始事件
                if self.event_bus:
                    self.event_bus.publish("behavior.step.started", {
                        "plan_id": plan["id"],
                        "step_id": current_step["step_id"],
                        "action": current_step.get("action", ""),
                        "description": current_step.get("description", "")
                    })
                
                logger.info(f"开始执行步骤: {current_step.get('description')} (计划: {plan['description']})")
            
            # 如果步骤已在执行中，检查是否完成
            elif current_step.get("status") == "in_progress":
                # 检查步骤是否完成
                if self._check_step_completion(plan, current_step):
                    # 标记步骤为已完成
                    current_step["status"] = "completed"
                    current_step["completed_at"] = datetime.now().isoformat()
                    
                    # 更新计划进度
                    plan["current_step_index"] += 1
                    plan["progress"] = (current_step_index + 1) / len(plan["steps"])
                    plan["updated_at"] = datetime.now().isoformat()
                    
                    # 记录执行历史
                    self._record_execution(plan, current_step)
                    
                    # 发布步骤完成事件
                    if self.event_bus:
                        self.event_bus.publish("behavior.step.completed", {
                            "plan_id": plan["id"],
                            "step_id": current_step["step_id"],
                            "action": current_step.get("action", ""),
                            "description": current_step.get("description", "")
                        })
                    
                    logger.success(f"步骤已完成: {current_step.get('description')} (计划: {plan['description']})")
                    
                    # 检查是否完成了所有步骤
                    if plan["current_step_index"] >= len(plan["steps"]):
                        plan["status"] = "completed"
                        plan["progress"] = 1.0
                        
                        # 发布计划完成事件
                        if self.event_bus:
                            self.event_bus.publish("behavior.plan.completed", {
                                "plan_id": plan["id"],
                                "description": plan["description"]
                            })
                        
                        logger.success(f"计划已完成: {plan['id']} - {plan['description']}")
            
            # 每次循环只处理一个步骤，避免同时执行多个行为
            break
    
    def _check_step_completion(self, plan: Dict[str, Any], step: Dict[str, Any]) -> bool:
        """
        检查步骤是否已完成
        
        参数:
            plan: 行为计划
            step: 计划步骤
            
        返回:
            步骤是否已完成
        """
        # 获取步骤开始时间
        if "started_at" not in step:
            return False
        
        started_at = datetime.fromisoformat(step["started_at"])
        current_time = datetime.now()
        
        # 获取步骤类型对应的持续时间范围
        action_type = step.get("type", "dialog")
        duration_range = self.action_types.get(action_type, {}).get("duration_range", (1, 5))
        
        # 计算预期持续时间（根据优先级调整）
        priority = step.get("priority", 0.5)
        duration_factor = 1.0 - min(0.5, max(0.0, priority - 0.5))  # 优先级越高，持续时间越短
        expected_duration = duration_range[0] + (duration_range[1] - duration_range[0]) * duration_factor
        
        # 检查是否超过预期持续时间
        elapsed_seconds = (current_time - started_at).total_seconds()
        
        # 简单示例：根据预期持续时间判断是否完成
        return elapsed_seconds >= expected_duration
    
    def _record_execution(self, plan: Dict[str, Any], step: Dict[str, Any]) -> None:
        """
        记录行为执行历史
        
        参数:
            plan: 行为计划
            step: 已完成的计划步骤
        """
        # 创建执行记录
        execution_record = {
            "timestamp": datetime.now().isoformat(),
            "plan_id": plan["id"],
            "plan_description": plan["description"],
            "step_id": step["step_id"],
            "action": step.get("action", ""),
            "action_type": step.get("type", ""),
            "description": step.get("description", ""),
            "started_at": step.get("started_at", ""),
            "completed_at": step.get("completed_at", "")
        }
        
        # 添加到执行历史
        self.execution_history.append(execution_record)
        
        # 限制历史记录大小
        max_history_size = 1000
        if len(self.execution_history) > max_history_size:
            self.execution_history = self.execution_history[-max_history_size:]
    
    # 事件处理方法
    
    def _on_user_message(self, data: Dict[str, Any]) -> None:
        """
        处理用户消息事件
        
        参数:
            data: 事件数据
        """
        # 检查是否需要创建用户响应计划
        user_response_exists = any(
            p.get("template_id") == "user_response" and p.get("status") in ["pending", "in_progress"]
            for p in self.current_plans
        )
        
        if not user_response_exists and len(self.current_plans) < self.max_active_plans:
            # 获取用户响应模板
            template = self.plan_templates.get("user_response")
            if template:
                # 创建用户响应计划
                self._create_plan_from_template("user_response", template, "user_message")
                logger.info("已创建用户响应计划")
    
    def _on_decision_made(self, data: Dict[str, Any]) -> None:
        """
        处理决策事件
        
        参数:
            data: 事件数据
        """
        # 决策可能影响行为规划
        decision_type = data.get("decision_type", "")
        
        # 如果是目标相关决策，可能需要创建新计划
        if decision_type == "goal_added" and "goal" in data:
            goal = data["goal"]
            goal_category = goal.get("category", "")
            
            # 根据目标类别创建相应计划
            if goal_category == "learning" and len(self.current_plans) < self.max_active_plans:
                template = self.plan_templates.get("learning_session")
                if template:
                    self._create_plan_from_template("learning_session", template, "learning_motivation")
            
            elif goal_category == "social" and len(self.current_plans) < self.max_active_plans:
                template = self.plan_templates.get("social_interaction")
                if template:
                    self._create_plan_from_template("social_interaction", template, "social_motivation")
            
            elif goal_category == "creative" and len(self.current_plans) < self.max_active_plans:
                template = self.plan_templates.get("creative_expression")
                if template:
                    self._create_plan_from_template("creative_expression", template, "creative_motivation")
    
    def _on_intention_generated(self, data: Dict[str, Any]) -> None:
        """
        处理意图生成事件
        
        参数:
            data: 事件数据
        """
        # 新的意图可能需要新的行为计划
        intention_type = data.get("type", "")
        
        # 根据意图类型创建相应计划
        if intention_type == "autonomous_learning" and len(self.current_plans) < self.max_active_plans:
            template = self.plan_templates.get("learning_session")
            if template:
                self._create_plan_from_template("learning_session", template, "learning_motivation")
        
        elif intention_type == "social_initiative" and len(self.current_plans) < self.max_active_plans:
            template = self.plan_templates.get("social_interaction")
            if template:
                self._create_plan_from_template("social_interaction", template, "social_motivation")
        
        elif intention_type == "exploration" and len(self.current_plans) < self.max_active_plans:
            template = self.plan_templates.get("learning_session")
            if template:
                self._create_plan_from_template("learning_session", template, "learning_motivation")
    
    def _on_action_completed(self, data: Dict[str, Any]) -> None:
        """
        处理行为完成事件
        
        参数:
            data: 事件数据
        """
        # 获取行为信息
        action_type = data.get("action_type", "")
        action_id = data.get("action_id", "")
        
        # 检查是否有相关步骤正在等待此行为完成
        for plan in self.current_plans:
            if plan.get("status") != "in_progress":
                continue
                
            current_step_index = plan.get("current_step_index", 0)
            if current_step_index >= len(plan.get("steps", [])):
                continue
                
            current_step = plan["steps"][current_step_index]
            if current_step.get("status") != "in_progress":
                continue
                
            # 如果步骤期待的行为类型和ID匹配，标记为完成
            if (current_step.get("action", "") == action_id or 
                current_step.get("type", "") == action_type):
                current_step["status"] = "completed"
                current_step["completed_at"] = datetime.now().isoformat()
                
                # 更新计划进度
                plan["current_step_index"] += 1
                plan["progress"] = (current_step_index + 1) / len(plan["steps"])
                plan["updated_at"] = datetime.now().isoformat()
                
                # 记录执行历史
                self._record_execution(plan, current_step)
                
                # 发布步骤完成事件
                if self.event_bus:
                    self.event_bus.publish("behavior.step.completed", {
                        "plan_id": plan["id"],
                        "step_id": current_step["step_id"],
                        "action": current_step.get("action", ""),
                        "description": current_step.get("description", "")
                    })
                
                logger.success(f"外部行为完成触发步骤完成: {current_step.get('description')} (计划: {plan['description']})")
                
                # 检查是否完成了所有步骤
                if plan["current_step_index"] >= len(plan["steps"]):
                    plan["status"] = "completed"
                    plan["progress"] = 1.0
                    
                    # 发布计划完成事件
                    if self.event_bus:
                        self.event_bus.publish("behavior.plan.completed", {
                            "plan_id": plan["id"],
                            "description": plan["description"]
                        })
                    
                    logger.success(f"计划已完成: {plan['id']} - {plan['description']}")
                
                break
    
    def _on_consciousness_updated(self, data: Dict[str, Any]) -> None:
        """
        处理意识更新事件
        
        参数:
            data: 事件数据
        """
        # 意识状态变化可能影响行为规划
        consciousness_level = data.get("consciousness_level", 0.5)
        
        # 当意识水平高时，可能触发自主行为规划
        if consciousness_level > 0.8 and len(self.current_plans) < self.max_active_plans:
            # 更新计划优先级
            self._update_plan_priorities()
    
    # 公共API方法
    
    def create_plan(self, description: str, steps: List[Dict[str, Any]], priority: float = 0.5) -> Optional[str]:
        """
        创建自定义行为计划
        
        参数:
            description: 计划描述
            steps: 计划步骤列表
            priority: 计划优先级
            
        返回:
            计划ID或None（如果创建失败）
        """
        # 如果已有计划数量达到上限，不创建新计划
        if len(self.current_plans) >= self.max_active_plans:
            logger.warning_status(f"已达到最大活跃计划数限制 ({self.max_active_plans})，无法创建新计划")
            return None
        
        # 创建计划基本信息
        plan_id = f"plan_{int(time.time())}_{random.randint(1000, 9999)}"
        creation_time = datetime.now()
        
        # 设置过期时间 (默认24小时后)
        expires_at = creation_time + timedelta(hours=24)
        
        # 处理步骤
        processed_steps = []
        for i, step_data in enumerate(steps):
            step = step_data.copy()
            
            # 添加步骤状态信息
            step["status"] = "pending"
            step["step_id"] = f"{plan_id}_step_{i + 1}"
            
            # 确保步骤有必要的字段
            if "type" not in step:
                step["type"] = "dialog"  # 默认类型
            
            if "priority" not in step:
                step["priority"] = 0.5  # 默认优先级
            
            # 添加步骤到列表
            processed_steps.append(step)
        
        # 创建完整计划
        plan = {
            "id": plan_id,
            "template_id": "custom",
            "description": description,
            "trigger": "api_call",
            "status": "pending",
            "priority": priority,
            "steps": processed_steps,
            "current_step_index": 0,
            "progress": 0.0,
            "created_at": creation_time.isoformat(),
            "updated_at": creation_time.isoformat(),
            "expires_at": expires_at.isoformat()
        }
        
        # 添加计划到当前计划列表
        self.current_plans.append(plan)
        logger.info(f"已创建自定义行为计划: {plan_id} - {description}")
        
        # 发布计划创建事件
        if self.event_bus:
            self.event_bus.publish("behavior.plan.created", {
                "plan_id": plan_id,
                "description": description,
                "trigger": "api_call"
            })
        
        return plan_id
    
    def cancel_plan(self, plan_id: str) -> bool:
        """
        取消行为计划
        
        参数:
            plan_id: 计划ID
            
        返回:
            是否成功取消
        """
        for plan in self.current_plans:
            if plan["id"] == plan_id:
                plan["status"] = "canceled"
                plan["updated_at"] = datetime.now().isoformat()
                
                # 发布计划取消事件
                if self.event_bus:
                    self.event_bus.publish("behavior.plan.canceled", {
                        "plan_id": plan_id,
                        "description": plan["description"]
                    })
                
                logger.info(f"已取消行为计划: {plan_id} - {plan['description']}")
                return True
        
        logger.warning_status(f"未找到计划 {plan_id}，无法取消")
        return False
    
    def get_plans(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取行为计划列表
        
        参数:
            status: 可选的状态过滤（pending, in_progress, completed, canceled）
            
        返回:
            行为计划列表
        """
        if status is None:
            return self.current_plans
        
        return [plan for plan in self.current_plans if plan.get("status") == status]
    
    def get_plan(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """
        获取特定行为计划
        
        参数:
            plan_id: 计划ID
            
        返回:
            行为计划或None（如果未找到）
        """
        for plan in self.current_plans:
            if plan["id"] == plan_id:
                return plan
        
        return None
    
    def get_execution_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取行为执行历史
        
        参数:
            limit: 返回的历史记录数量限制
            
        返回:
            行为执行历史列表
        """
        if limit is None or limit >= len(self.execution_history):
            return self.execution_history
        
        return self.execution_history[-limit:]
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        返回:
            模块状态字典
        """
        status = super().get_status()
        
        # 计算活跃计划数
        active_plans = sum(1 for p in self.current_plans if p.get("status") in ["pending", "in_progress"])
        
        status.update({
            "active_plans_count": active_plans,
            "total_plans_count": len(self.current_plans),
            "execution_history_count": len(self.execution_history),
            "plan_templates_count": len(self.plan_templates)
        })
        
        return status


# 🔥 香草修复：实现真正的单例模式
_action_planning_instance = None

def get_instance(module_id: str = "action_planning", config: Dict[str, Any] = None) -> ActionPlanning:
    """
    获取行为规划模块的单例实例

    参数:
        module_id: 模块ID
        config: 配置参数

    返回:
        行为规划模块实例
    """
    global _action_planning_instance
    if _action_planning_instance is None:
        _action_planning_instance = ActionPlanning(module_id, config)
        # 初始化实例
        if not _action_planning_instance.is_initialized:
            _action_planning_instance.initialize()
    return _action_planning_instance