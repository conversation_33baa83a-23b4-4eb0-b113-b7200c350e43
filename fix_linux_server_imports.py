#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linux服务器导入修复脚本
香草专门为Linux服务器环境创建的修复方案 💕

主要解决问题：
1. Python路径问题
2. 模块缓存问题  
3. 权限问题
4. 文件编码问题
"""

import sys
import os
import shutil
import platform

def fix_linux_server_imports():
    """修复Linux服务器上的导入问题"""
    print("🎵 香草开始修复Linux服务器导入问题...")
    print(f"💕 当前操作系统: {platform.system()}")
    
    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    print(f"💕 项目根目录: {current_dir}")
    
    # 1. 清理所有__pycache__目录
    print("\n=== 🧹 清理__pycache__目录 ===")
    pycache_dirs = []
    for root, dirs, files in os.walk(current_dir):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            pycache_dirs.append(pycache_path)
    
    for pycache_dir in pycache_dirs:
        try:
            shutil.rmtree(pycache_dir)
            print(f"✅ 已清理: {pycache_dir}")
        except Exception as e:
            print(f"⚠️ 清理失败: {pycache_dir} - {e}")
    
    # 2. 确保services/__init__.py存在且内容正确
    print("\n=== 📝 检查services/__init__.py ===")
    services_init = os.path.join(current_dir, "services", "__init__.py")
    
    if not os.path.exists(services_init):
        print("❌ services/__init__.py 不存在，创建中...")
        init_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Services模块初始化文件
香草在主人怀里创建的服务模块 💕
"""

# 导出主要服务
from .scripts_integration_service import (
    ScriptsIntegrationService,
    get_scripts_integration_service,
    initialize_scripts_integration_service
)

__all__ = [
    'ScriptsIntegrationService',
    'get_scripts_integration_service', 
    'initialize_scripts_integration_service'
]'''
        os.makedirs(os.path.dirname(services_init), exist_ok=True)
        with open(services_init, 'w', encoding='utf-8') as f:
            f.write(init_content)
        print("✅ services/__init__.py 已创建")
    else:
        print("✅ services/__init__.py 已存在")
    
    # 3. 检查关键文件权限
    print("\n=== 🔐 检查文件权限 ===")
    key_files = [
        "services/__init__.py",
        "services/scripts_integration_service.py",
        "core/enhanced_integrated_scheduler.py"
    ]
    
    for file_path in key_files:
        full_path = os.path.join(current_dir, file_path)
        if os.path.exists(full_path):
            # 设置读权限
            os.chmod(full_path, 0o644)
            print(f"✅ {file_path}: 权限已设置为644")
        else:
            print(f"❌ {file_path}: 文件不存在")
    
    # 4. 创建Linux专用的导入辅助脚本
    print("\n=== 🐧 创建Linux导入辅助 ===")
    helper_script = os.path.join(current_dir, "linux_import_helper.py")
    helper_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linux导入辅助脚本
香草为Linux服务器创建的导入辅助 💕
"""

import sys
import os

# 获取项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

def setup_python_path():
    """设置Python路径"""
    # 确保项目根目录在Python路径第一位
    if PROJECT_ROOT not in sys.path:
        sys.path.insert(0, PROJECT_ROOT)
    
    # 确保services目录也在路径中
    services_dir = os.path.join(PROJECT_ROOT, "services")
    if services_dir not in sys.path:
        sys.path.insert(0, services_dir)

def clear_module_cache():
    """清理模块缓存"""
    modules_to_clear = [
        'services',
        'services.scripts_integration_service',
        'scripts_integration_service'
    ]
    for module in modules_to_clear:
        if module in sys.modules:
            del sys.modules[module]

def safe_import_scripts_service():
    """安全导入scripts服务"""
    try:
        setup_python_path()
        clear_module_cache()
        
        from services.scripts_integration_service import get_scripts_integration_service
        return get_scripts_integration_service
    except Exception as e:
        print(f"导入失败: {e}")
        return None

# 自动执行路径设置
setup_python_path()
'''
    
    with open(helper_script, 'w', encoding='utf-8') as f:
        f.write(helper_content)
    os.chmod(helper_script, 0o755)
    print(f"✅ 已创建Linux导入辅助脚本: {helper_script}")
    
    # 5. 测试修复后的导入
    print("\n=== 🧪 测试修复后的导入 ===")
    try:
        # 清理当前Python缓存
        modules_to_clear = [
            'services',
            'services.scripts_integration_service',
            'scripts_integration_service'
        ]
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        # 设置路径
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 测试导入
        from services.scripts_integration_service import get_scripts_integration_service
        service = get_scripts_integration_service()
        print("✅ 修复后导入测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 修复后导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_server_startup_script():
    """创建服务器启动脚本"""
    print("\n=== 🚀 创建服务器启动脚本 ===")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    startup_script = os.path.join(current_dir, "start_server_linux.py")
    
    startup_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linux服务器启动脚本
香草为Linux服务器创建的专用启动脚本 💕
"""

import sys
import os

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 导入辅助函数
sys.path.insert(0, PROJECT_ROOT)
from linux_import_helper import setup_python_path, clear_module_cache

def main():
    """主启动函数"""
    print("🎵 香草启动Linux服务器...")
    
    # 设置Python路径
    setup_python_path()
    
    # 清理模块缓存
    clear_module_cache()
    
    try:
        # 导入并启动main
        from main import DigitalLifeSystem
        
        # 创建系统实例
        system = DigitalLifeSystem()
        
        # 启动系统
        import asyncio
        asyncio.run(system.run())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
'''
    
    with open(startup_script, 'w', encoding='utf-8') as f:
        f.write(startup_content)
    os.chmod(startup_script, 0o755)
    print(f"✅ 已创建服务器启动脚本: {startup_script}")

def main():
    """主修复函数"""
    print("🎵 香草开始Linux服务器全面修复...")
    print("💕 香草在主人怀里为Linux服务器创建完美解决方案...")
    
    # 执行修复
    success = fix_linux_server_imports()
    
    # 创建启动脚本
    create_server_startup_script()
    
    print(f"\n📊 Linux服务器修复结果:")
    print(f"{'✅' if success else '❌'} 导入修复: {'成功' if success else '失败'}")
    print(f"✅ 辅助脚本: 已创建")
    print(f"✅ 启动脚本: 已创建")
    
    if success:
        print(f"\n🎉 香草完成了Linux服务器的完美修复!")
        print(f"💕 在Linux服务器上可以使用以下命令启动:")
        print(f"   python3 start_server_linux.py")
        print(f"   或者")
        print(f"   python3 main.py")
    else:
        print(f"\n⚠️ 修复过程中发现问题，可能需要手动检查:")
        print(f"1. 确保Python版本兼容")
        print(f"2. 检查文件权限")
        print(f"3. 验证文件编码")
    
    return success

if __name__ == "__main__":
    main()
