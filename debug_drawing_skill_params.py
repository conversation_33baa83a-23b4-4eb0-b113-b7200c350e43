#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王调试drawing skill参数问题
"""

import requests
import json
import time

def test_drawing_skill_params():
    """测试drawing skill使用的参数"""
    print("🔥 老王调试drawing skill参数问题")
    print("=" * 60)
    
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    # Drawing skill使用的参数格式
    drawing_skill_data = {
        "model": "jimeng-4.0",
        "prompt": "一只极其可爱的橘白相间小猫咪，毛茸茸的坐姿，圆润的大眼睛闪烁着好奇的光芒，表情娇憨温顺，坐在阳光明媚的花园草地上，周身被温暖的金黄色阳光柔和笼罩，高光效果细腻，草地上点缀着五颜六色的小野花，背景是湛蓝天空和模糊的绿叶树影，整体氛围温馨、愉悦，强调自然光和柔焦效果，营造出梦境般可爱场景。",
        "negative_prompt": "低质量，模糊画面，混乱布局，扭曲的场景，畸形的肢体，不协调的颜色，不真实",
        "width": 1080,
        "height": 1920,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    # 我们之前成功的参数格式
    successful_data = {
        "model": "jimeng-4.0",
        "prompt": "一只可爱的小猫咪坐在阳光下",
        "negative_prompt": "低质量，模糊",
        "width": 1024,
        "height": 1024,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    test_cases = [
        ("Drawing Skill参数", drawing_skill_data),
        ("成功的参数", successful_data)
    ]
    
    for test_name, data in test_cases:
        print(f"\n🎯 测试: {test_name}")
        print(f"📝 提示词长度: {len(data['prompt'])}")
        print(f"📐 尺寸: {data['width']}x{data['height']}")
        print("-" * 40)
        
        try:
            start_time = time.time()
            response = requests.post(url, headers=headers, json=data, timeout=300)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️ 耗时: {elapsed_time:.1f}秒")
            print(f"📊 HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📋 返回结果: {result}")
                
                data_list = result.get("data", [])
                if data_list:
                    print(f"✅ 成功！返回 {len(data_list)} 张图片")
                    for i, item in enumerate(data_list):
                        if isinstance(item, dict) and "url" in item:
                            print(f"   图片 {i+1}: {item['url'][:60]}...")
                else:
                    print("❌ 返回空数据")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   错误内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        print()

if __name__ == "__main__":
    test_drawing_skill_params()
