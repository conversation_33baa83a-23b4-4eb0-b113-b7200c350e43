#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理主动表达器官的话题缓存
老王专用缓存清理工具
"""

import os
import sys
import json
import time
import redis
from pathlib import Path

def clear_memory_caches():
    """清理内存缓存 - 需要重启系统才能生效"""
    print("🔥 老王提示：内存缓存需要重启系统才能清理")
    print("   - _recent_templates (模板缓存)")
    print("   - hot_topics_cache (热搜话题缓存)")
    print("   - topic_cache (AI决策话题缓存)")
    print()

def clear_redis_cache():
    """清理Redis缓存 - 老王增强版"""
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)

        # 🔥 老王新增：清理所有命名空间的缓存
        namespaces = [
            "proactive_expression",  # 主动表达缓存
            "templates",            # 模板缓存
            "hot_topics",           # 热搜话题缓存
            "world_perception",     # 世界感知缓存
            "memory_retrieval",     # 记忆检索缓存
            "ai_decision",          # AI决策缓存
        ]

        deleted_count = 0
        for namespace in namespaces:
            pattern = f"{namespace}:*"
            keys = r.keys(pattern)
            if keys:
                deleted = r.delete(*keys)
                deleted_count += deleted
                print(f"✅ 清理Redis命名空间: {namespace} -> 删除 {deleted} 个键")

        # 🔥 老王新增：清理旧的缓存模式（向后兼容）
        old_patterns = [
            "hot_topics_*",
            "topic_*",
            "proactive_*",
            "expression_*",
            "news_*",
            "financial_*"
        ]

        for pattern in old_patterns:
            keys = r.keys(pattern)
            if keys:
                deleted = r.delete(*keys)
                deleted_count += deleted
                print(f"✅ 清理旧Redis缓存: {pattern} -> 删除 {deleted} 个键")

        print(f"🎉 Redis缓存清理完成，共删除 {deleted_count} 个缓存项")
        return True

    except Exception as e:
        print(f"❌ Redis缓存清理失败: {e}")
        return False

def clear_file_caches():
    """清理文件缓存"""
    cache_dirs = [
        "data/cache",
        "data/hot_topics_cache", 
        "data/news_cache",
        "data/financial_cache",
        "data/expression_cache",
        "cache"
    ]
    
    deleted_files = 0
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                for root, dirs, files in os.walk(cache_dir):
                    for file in files:
                        if file.endswith(('.json', '.cache', '.tmp')):
                            file_path = os.path.join(root, file)
                            os.remove(file_path)
                            deleted_files += 1
                            print(f"🗑️  删除缓存文件: {file_path}")
                
                print(f"✅ 清理目录: {cache_dir}")
            except Exception as e:
                print(f"❌ 清理目录失败 {cache_dir}: {e}")
    
    print(f"🎉 文件缓存清理完成，共删除 {deleted_files} 个文件")

def clear_database_cache():
    """清理数据库中的缓存表"""
    try:
        # 这里可以添加数据库缓存清理逻辑
        print("💾 数据库缓存清理（如果有的话）...")
        # 例如：清理临时表、缓存表等
        print("✅ 数据库缓存清理完成")
        
    except Exception as e:
        print(f"❌ 数据库缓存清理失败: {e}")

def reset_topic_selection_state():
    """重置话题选择状态"""
    try:
        # 清理话题选择状态文件
        state_files = [
            "data/topic_selection_state.json",
            "data/expression_history.json", 
            "data/recent_topics.json",
            "data/used_templates.json"
        ]
        
        for state_file in state_files:
            if os.path.exists(state_file):
                os.remove(state_file)
                print(f"🗑️  删除状态文件: {state_file}")
        
        print("✅ 话题选择状态重置完成")
        
    except Exception as e:
        print(f"❌ 话题选择状态重置失败: {e}")

def clear_hot_topics_config_cache():
    """清理热搜配置中的缓存设置"""
    try:
        config_file = "config/hot_topics_sources.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 重置缓存配置
            if 'caching' in config:
                config['caching']['ttl'] = 60  # 缩短缓存时间到1分钟
                config['caching']['max_cache_size'] = 100  # 减少缓存大小
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                print("✅ 热搜配置缓存设置已重置（TTL=60秒）")
            
    except Exception as e:
        print(f"❌ 热搜配置缓存重置失败: {e}")

def main():
    """主函数"""
    print("🔥" + "="*50)
    print("🔥 老王专用：主动表达话题缓存清理工具")
    print("🔥" + "="*50)
    print()
    
    print("开始清理各种缓存...")
    print()
    
    # 1. 清理内存缓存（提示）
    clear_memory_caches()
    
    # 2. 清理Redis缓存
    print("🔄 清理Redis缓存...")
    clear_redis_cache()
    print()
    
    # 3. 清理文件缓存
    print("🔄 清理文件缓存...")
    clear_file_caches()
    print()
    
    # 4. 清理数据库缓存
    print("🔄 清理数据库缓存...")
    clear_database_cache()
    print()
    
    # 5. 重置话题选择状态
    print("🔄 重置话题选择状态...")
    reset_topic_selection_state()
    print()
    
    # 6. 重置热搜配置缓存
    print("🔄 重置热搜配置缓存...")
    clear_hot_topics_config_cache()
    print()
    
    print("🎉" + "="*50)
    print("🎉 缓存清理完成！")
    print("🎉 重启系统后，主动表达将使用新的话题")
    print("🎉" + "="*50)
    print()
    print("💡 老王提示：")
    print("   1. 重启数字生命体系统")
    print("   2. 主动表达器官将重新选择话题")
    print("   3. 不会再重复同一个话题了")

if __name__ == "__main__":
    main()
