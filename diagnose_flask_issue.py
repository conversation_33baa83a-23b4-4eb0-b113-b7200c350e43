#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask环境诊断脚本 - 生产环境问题排查

作者: <PERSON>
创建日期: 2024-12-29
版本: 1.0.0
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_section(title):
    """打印小节标题"""
    print(f"\n📋 {title}")
    print("-" * 40)

def check_python_environment():
    """检查Python环境"""
    print_section("Python环境信息")
    print(f"Python版本: {sys.version}")
    print(f"Python可执行文件: {sys.executable}")
    print(f"虚拟环境: {os.environ.get('VIRTUAL_ENV', '未激活')}")
    print(f"Conda环境: {os.environ.get('CONDA_DEFAULT_ENV', '未设置')}")
    
    # 检查Python路径
    print(f"\nPython搜索路径:")
    for i, path in enumerate(sys.path):
        print(f"  {i+1}. {path}")

def check_flask_installation():
    """检查Flask安装状态"""
    print_section("Flask安装检查")
    
    try:
        import flask
        print(f"✅ Flask已安装")
        print(f"   版本: {flask.__version__}")
        print(f"   路径: {flask.__file__}")
        
        # 检查Flask子模块
        try:
            from flask import Flask, request, jsonify
            print(f"✅ Flask核心模块导入成功")
        except ImportError as e:
            print(f"❌ Flask核心模块导入失败: {e}")
            
    except ImportError as e:
        print(f"❌ Flask未安装或导入失败: {e}")
        return False
    
    return True

def check_other_dependencies():
    """检查其他关键依赖"""
    print_section("关键依赖检查")
    
    dependencies = [
        "websockets",
        "requests", 
        "mysql.connector",
        "redis",
        "openai"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} - 已安装")
        except ImportError:
            print(f"❌ {dep} - 未安装")

def check_import_in_context():
    """在项目上下文中测试导入"""
    print_section("项目上下文导入测试")
    
    # 添加项目根目录到路径
    project_root = Path(__file__).parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
        print(f"已添加项目根目录到Python路径: {project_root}")
    
    try:
        # 模拟main.py中的导入
        from flask import Flask, request, jsonify
        print("✅ 在项目上下文中Flask导入成功")
        
        # 创建测试应用
        app = Flask("test_app")
        print("✅ Flask应用创建成功")
        
    except Exception as e:
        print(f"❌ 项目上下文导入失败: {e}")
        import traceback
        traceback.print_exc()

def suggest_solutions():
    """提供解决方案建议"""
    print_section("解决方案建议")
    
    print("🔧 可能的解决方案:")
    print("1. 确保在正确的虚拟环境中运行:")
    print("   conda activate linyanran")
    print("   python main.py")
    
    print("\n2. 重新安装Flask:")
    print("   pip uninstall flask")
    print("   pip install flask==2.2.3")
    
    print("\n3. 检查环境变量:")
    print("   echo $PYTHONPATH")
    print("   echo $VIRTUAL_ENV")
    
    print("\n4. 使用绝对路径运行:")
    print("   /path/to/linyanran/bin/python main.py")
    
    print("\n5. 重建虚拟环境:")
    print("   conda create -n linyanran python=3.9")
    print("   conda activate linyanran")
    print("   pip install -r requirements.txt")

def run_fix_attempts():
    """尝试自动修复"""
    print_section("自动修复尝试")
    
    try:
        # 尝试重新安装Flask
        print("🔄 尝试重新安装Flask...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "flask==2.2.3"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Flask重新安装成功")
        else:
            print(f"❌ Flask重新安装失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 自动修复失败: {e}")

def create_environment_script():
    """创建环境设置脚本"""
    print_section("创建环境设置脚本")
    
    script_content = '''#!/bin/bash
# 林嫣然数字生命体 - 环境设置脚本

echo "🌸 林嫣然数字生命体系统 - 环境设置"
echo "========================================"

# 检查conda是否可用
if command -v conda &> /dev/null; then
    echo "✅ Conda已安装"
    
    # 检查linyanran环境是否存在
    if conda env list | grep -q "linyanran"; then
        echo "✅ linyanran环境已存在"
        echo "激活环境: conda activate linyanran"
        conda activate linyanran
    else
        echo "❌ linyanran环境不存在，正在创建..."
        conda create -n linyanran python=3.9 -y
        conda activate linyanran
        echo "✅ linyanran环境创建成功"
    fi
    
    # 安装依赖
    echo "📦 安装项目依赖..."
    pip install -r requirements.txt
    
    echo "🎉 环境设置完成！"
    echo "运行系统: python main.py"
    
else
    echo "❌ Conda未安装，请先安装Anaconda或Miniconda"
fi
'''
    
    script_path = "setup_environment.sh"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    print(f"✅ 环境设置脚本已创建: {script_path}")
    print("运行命令: ./setup_environment.sh")

def main():
    """主函数"""
    print_header("Flask环境诊断 - 林嫣然数字生命体系统")
    
    # 基础环境检查
    check_python_environment()
    
    # Flask安装检查
    flask_ok = check_flask_installation()
    
    # 其他依赖检查
    check_other_dependencies()
    
    # 项目上下文测试
    check_import_in_context()
    
    # 如果Flask有问题，尝试修复
    if not flask_ok:
        run_fix_attempts()
    
    # 提供解决方案
    suggest_solutions()
    
    # 创建环境设置脚本
    create_environment_script()
    
    print_header("诊断完成")
    print("🌸 请根据上述建议解决环境问题，然后重新运行系统")

if __name__ == "__main__":
    main() 