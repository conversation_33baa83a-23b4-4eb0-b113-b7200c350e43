#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王直接测试drawing skill的jimeng_generate_images方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill
import time

def test_jimeng_generate_images_direct():
    """直接测试jimeng_generate_images方法"""
    print("🔥 老王直接测试jimeng_generate_images方法")
    print("=" * 60)
    
    try:
        # 初始化drawing skill
        drawing_skill = DrawingSkill()
        
        # 测试参数
        test_prompt = "流浪地球画面"
        
        print(f"🎯 测试提示词: {test_prompt}")
        print(f"📋 使用session_id: {drawing_skill.jimeng_session_id[:20]}...")
        print("-" * 40)
        
        start_time = time.time()
        
        # 直接调用jimeng_generate_images方法
        result = drawing_skill.jimeng_generate_images(
            prompt=test_prompt,
            model="jimeng-4.0",
            negative_prompt="低质量，模糊",
            width=1024,
            height=1024,
            sample_strength=0.5,
            session_id=drawing_skill.jimeng_session_id
        )
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ 耗时: {elapsed_time:.1f}秒")
        
        # 检查结果
        print(f"📋 返回结果类型: {type(result)}")
        print(f"📋 返回结果: {result}")
        
        if isinstance(result, dict):
            if "image_urls" in result and result["image_urls"]:
                print(f"✅ 成功！返回 {len(result['image_urls'])} 张图片")
                for i, url in enumerate(result["image_urls"]):
                    print(f"   图片 {i+1}: {url[:60]}...")
            elif "error" in result:
                print(f"❌ 失败: {result['error']}")
                if "details" in result:
                    print(f"   详情: {result['details']}")
            else:
                print(f"⚠️ 未知结果格式")
        else:
            print(f"❌ 返回结果不是字典格式")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_jimeng_generate_images_direct()
