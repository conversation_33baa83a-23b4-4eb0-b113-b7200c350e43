#!/bin/bash

# 日志文件健康检查脚本
# 检查yanran_production.out文件是否有NUL字符问题

LOG_FILE="/root/yanran_digital_life/logs/yanran_production.out"

if [ ! -f "$LOG_FILE" ]; then
    echo "日志文件不存在: $LOG_FILE"
    exit 1
fi

# 检查文件开头是否有NUL字符
NUL_COUNT=$(head -c 1024 "$LOG_FILE" | tr -cd '\000' | wc -c)

if [ "$NUL_COUNT" -gt 0 ]; then
    echo "⚠️ 发现 $NUL_COUNT 个NUL字符在文件开头"
    echo "建议运行: python fix_log_nul_characters.py"
    exit 1
else
    echo "✅ 日志文件健康"
    exit 0
fi
