#!/bin/bash
# 林嫣然数字生命体 - 环境设置脚本

echo "🌸 林嫣然数字生命体系统 - 环境设置"
echo "========================================"

# 检查conda是否可用
if command -v conda &> /dev/null; then
    echo "✅ Conda已安装"
    
    # 检查linyanran环境是否存在
    if conda env list | grep -q "linyanran"; then
        echo "✅ linyanran环境已存在"
        echo "激活环境: conda activate linyanran"
        conda activate linyanran
    else
        echo "❌ linyanran环境不存在，正在创建..."
        conda create -n linyanran python=3.9 -y
        conda activate linyanran
        echo "✅ linyanran环境创建成功"
    fi
    
    # 安装依赖
    echo "📦 安装项目依赖..."
    pip install -r requirements.txt
    
    echo "🎉 环境设置完成！"
    echo "运行系统: python main.py"
    
else
    echo "❌ Conda未安装，请先安装Anaconda或Miniconda"
fi
