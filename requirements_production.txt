# 🌸 林嫣然数字生命体系统 - 生产环境依赖
# 🔥 老王修复版本，解决所有兼容性问题

# 核心Web框架
flask>=2.2.0,<3.0.0
werkzeug>=2.0.0,<2.3.0
jinja2>=3.0.0

# AI服务
openai>=1.0.0

# 数据库连接
mysql-connector-python>=8.0.0
redis>=4.0.0
pymysql>=1.0.0

# 机器学习 (解决sklearn缺失问题)
scikit-learn>=1.2.0
numpy>=1.21.0
pandas>=1.3.0

# 日志系统 (解决loguru缺失问题)
loguru>=0.7.0

# 网络分析 (解决networkx缺失问题)
networkx>=3.0

# 异步支持
asyncio-mqtt>=0.11.0
websockets>=10.0

# 数据处理
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0

# 配置管理
python-dotenv>=0.19.0
pyyaml>=6.0

# 系统监控
psutil>=5.9.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2022.1

# 加密安全
cryptography>=3.4.8
bcrypt>=3.2.0

# 图像处理 (可选)
pillow>=9.0.0

# 数据可视化 (可选)
matplotlib>=3.5.0
seaborn>=0.11.0

# API文档
flask-restx>=1.0.0
