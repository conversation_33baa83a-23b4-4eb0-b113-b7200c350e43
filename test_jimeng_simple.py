#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王最简单的jimeng API测试
"""

import requests
import json
import time

def test_simple():
    """最简单的测试"""
    
    print("🔥 老王最简单的jimeng API测试")
    print("=" * 50)
    
    # 最基本的配置
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    # 最简单的数据
    data = {
        "model": "jimeng-4.0",
        "prompt": "cat",
        "width": 1024,
        "height": 1024
    }
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    print(f"URL: {url}")
    print(f"Session: {session_id[:20]}...")
    print(f"Data: {json.dumps(data, indent=2)}")
    print("-" * 30)
    
    try:
        print("发送请求...")
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"解析结果: {json.dumps(result, indent=2)}")
            
            data_list = result.get("data", [])
            if data_list:
                print(f"✅ 成功！返回 {len(data_list)} 张图片")
                return True
            else:
                print("❌ 返回空数据")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def test_different_models():
    """测试不同模型"""
    
    print("\n🔧 测试不同模型...")
    
    models = ["jimeng-4.0", "jimeng-3.0", "jimeng"]
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    for model in models:
        print(f"\n测试模型: {model}")
        
        data = {
            "model": model,
            "prompt": "flower",
            "width": 1024,
            "height": 1024
        }
        
        headers = {
            "Authorization": f"Bearer {session_id}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=60)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                data_list = result.get("data", [])
                if data_list:
                    print(f"✅ {model} 成功！返回 {len(data_list)} 张图片")
                    return model
                else:
                    print(f"⚠️ {model} 返回空数据")
            else:
                print(f"❌ {model} HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {model} 异常: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 开始最简单测试...")
    
    # 测试1: 最基本的请求
    success = test_simple()
    
    # 如果失败，测试不同模型
    if not success:
        print("\n基本测试失败，尝试不同模型...")
        working_model = test_different_models()
        
        if working_model:
            print(f"\n✅ 找到可用模型: {working_model}")
        else:
            print("\n❌ 所有模型都失败")
    
    print("\n🔥 测试完成！")
