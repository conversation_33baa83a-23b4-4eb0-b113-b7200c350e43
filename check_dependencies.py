#!/usr/bin/env python3
"""
林嫣然数字生命体系统 - 依赖检查脚本
版本: v3.1.0
作者: Yanran Digital Life Team
创建日期: 2024-12-29

检查生产环境所需的所有Python包是否正确安装
"""

import sys
import importlib
import subprocess
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass


@dataclass
class DependencyInfo:
    """依赖信息"""
    name: str
    import_name: str
    min_version: str
    category: str
    required: bool = True
    description: str = ""


class DependencyChecker:
    """依赖检查器"""
    
    def __init__(self):
        self.dependencies = self._get_dependencies()
        self.results = {}
        
    def _get_dependencies(self) -> List[DependencyInfo]:
        """获取所有依赖信息"""
        return [
            # 核心Web框架
            DependencyInfo("flask", "flask", "2.2.3", "核心Web框架", True, "HTTP API服务器"),
            DependencyInfo("flask-cors", "flask_cors", "4.0.0", "核心Web框架", True, "跨域资源共享"),
            DependencyInfo("websockets", "websockets", "11.0.2", "核心Web框架", True, "WebSocket服务器"),
            
            # 异步HTTP客户端
            DependencyInfo("aiohttp", "aiohttp", "3.8.0", "异步HTTP客户端", True, "异步HTTP请求"),
            DependencyInfo("requests", "requests", "2.28.2", "异步HTTP客户端", True, "同步HTTP请求"),
            
            # 数据库和存储
            DependencyInfo("mysql-connector-python", "mysql.connector", "8.0.33", "数据库和存储", True, "MySQL数据库连接器"),
            DependencyInfo("pymysql", "pymysql", "1.0.3", "数据库和存储", True, "MySQL数据库驱动"),
            DependencyInfo("sqlalchemy", "sqlalchemy", "2.0.9", "数据库和存储", True, "SQL工具包"),
            DependencyInfo("redis", "redis", "4.5.4", "数据库和存储", True, "Redis缓存客户端"),
            DependencyInfo("chromadb", "chromadb", "0.4.6", "数据库和存储", True, "向量数据库"),
            
            # AI和NLP
            DependencyInfo("openai", "openai", "0.27.4", "AI和NLP", True, "OpenAI API客户端"),
            DependencyInfo("nltk", "nltk", "3.8.1", "AI和NLP", True, "自然语言处理工具包"),
            DependencyInfo("transformers", "transformers", "4.28.1", "AI和NLP", True, "Transformer模型"),
            DependencyInfo("sentence-transformers", "sentence_transformers", "2.2.2", "AI和NLP", True, "句子嵌入模型"),
            
            # 数据处理
            DependencyInfo("numpy", "numpy", "1.24.2", "数据处理", True, "数值计算库"),
            DependencyInfo("pandas", "pandas", "2.0.0", "数据处理", True, "数据分析库"),
            DependencyInfo("scipy", "scipy", "1.10.0", "数据处理", True, "科学计算库"),
            DependencyInfo("matplotlib", "matplotlib", "3.7.1", "数据处理", True, "数据可视化库"),
            
            # 系统工具
            DependencyInfo("python-dotenv", "dotenv", "1.0.0", "系统工具", True, "环境变量管理"),
            DependencyInfo("pyyaml", "yaml", "6.0", "系统工具", True, "YAML配置解析"),
            DependencyInfo("psutil", "psutil", "5.9.5", "系统工具", True, "系统监控"),
            DependencyInfo("tqdm", "tqdm", "4.62.0", "系统工具", True, "进度条"),
            DependencyInfo("python-dateutil", "dateutil", "2.8.2", "系统工具", True, "日期时间处理"),
            
            # 安全和验证
            DependencyInfo("pydantic", "pydantic", "1.10.0", "安全和验证", True, "数据验证"),
            DependencyInfo("cryptography", "cryptography", "3.4.8", "安全和验证", True, "加密库"),
            
            # 开发和调试工具
            DependencyInfo("typing-extensions", "typing_extensions", "4.5.0", "开发和调试工具", True, "类型检查扩展"),
            DependencyInfo("memory-profiler", "memory_profiler", "0.60.0", "开发和调试工具", False, "内存分析"),
            
            # 可选依赖
            DependencyInfo("akshare", "akshare", "1.11.0", "可选依赖", False, "金融数据源"),
            DependencyInfo("pillow", "PIL", "9.5.0", "可选依赖", False, "图像处理"),
            DependencyInfo("speechrecognition", "speech_recognition", "3.10.0", "可选依赖", False, "语音识别"),
        ]
    
    def check_import(self, dep: DependencyInfo) -> Tuple[bool, str, Optional[str]]:
        """检查模块是否可以导入"""
        try:
            module = importlib.import_module(dep.import_name)
            # 优先使用importlib.metadata获取版本信息
            try:
                import importlib.metadata
                version = importlib.metadata.version(dep.name)
            except:
                # 回退到__version__属性
                version = getattr(module, '__version__', 'Unknown')
            return True, version, None
        except (ImportError, ValueError, Exception) as e:
            return False, "", str(e)
    
    def compare_version(self, current: str, required: str) -> bool:
        """比较版本号"""
        if current == 'Unknown':
            return True  # 无法确定版本时假设满足要求
        
        try:
            current_parts = [int(x) for x in current.split('.')]
            required_parts = [int(x) for x in required.split('.')]
            
            # 补齐版本号长度
            max_len = max(len(current_parts), len(required_parts))
            current_parts.extend([0] * (max_len - len(current_parts)))
            required_parts.extend([0] * (max_len - len(required_parts)))
            
            return current_parts >= required_parts
        except:
            return True  # 版本比较失败时假设满足要求
    
    def check_all_dependencies(self) -> Dict[str, Dict]:
        """检查所有依赖"""
        print("🔍 正在检查林嫣然数字生命体系统依赖...")
        print("=" * 80)
        
        categories = {}
        missing_required = []
        missing_optional = []
        version_issues = []
        
        for dep in self.dependencies:
            success, version, error = self.check_import(dep)
            
            category = dep.category
            if category not in categories:
                categories[category] = []
            
            result = {
                'name': dep.name,
                'import_name': dep.import_name,
                'required': dep.required,
                'description': dep.description,
                'min_version': dep.min_version,
                'installed': success,
                'version': version,
                'error': error,
                'version_ok': True
            }
            
            if success:
                version_ok = self.compare_version(version, dep.min_version)
                result['version_ok'] = version_ok
                
                if not version_ok:
                    version_issues.append(dep)
            else:
                if dep.required:
                    missing_required.append(dep)
                else:
                    missing_optional.append(dep)
            
            categories[category].append(result)
            self.results[dep.name] = result
        
        # 显示结果
        self._display_results(categories)
        
        # 显示总结
        self._display_summary(missing_required, missing_optional, version_issues)
        
        return self.results
    
    def _display_results(self, categories: Dict[str, List]):
        """显示检查结果"""
        for category, deps in categories.items():
            print(f"\n📦 {category}")
            print("-" * 60)
            
            for dep in deps:
                status = "✅" if dep['installed'] and dep['version_ok'] else "❌"
                required_mark = "🔴" if dep['required'] else "🔵"
                
                version_info = f"v{dep['version']}" if dep['installed'] else "未安装"
                if dep['installed'] and not dep['version_ok']:
                    version_info += f" (需要 ≥{dep['min_version']})"
                
                print(f"  {status} {required_mark} {dep['name']:<25} {version_info:<15} - {dep['description']}")
                
                if not dep['installed'] and dep['error']:
                    print(f"      💬 错误: {dep['error']}")
    
    def _display_summary(self, missing_required: List, missing_optional: List, version_issues: List):
        """显示总结信息"""
        print("\n" + "=" * 80)
        print("📊 依赖检查总结")
        print("=" * 80)
        
        total_deps = len(self.dependencies)
        installed_deps = sum(1 for r in self.results.values() if r['installed'])
        required_deps = sum(1 for dep in self.dependencies if dep.required)
        installed_required = sum(1 for r in self.results.values() if r['installed'] and r['required'])
        
        print(f"📈 总体统计:")
        print(f"  • 总依赖数: {total_deps}")
        print(f"  • 已安装: {installed_deps}")
        print(f"  • 必需依赖: {required_deps}")
        print(f"  • 已安装必需依赖: {installed_required}")
        
        if missing_required:
            print(f"\n❌ 缺少必需依赖 ({len(missing_required)}个):")
            for dep in missing_required:
                print(f"  • {dep.name} - {dep.description}")
        
        if version_issues:
            print(f"\n⚠️  版本不满足要求 ({len(version_issues)}个):")
            for dep in version_issues:
                current_version = self.results[dep.name]['version']
                print(f"  • {dep.name}: 当前 v{current_version}, 需要 ≥{dep.min_version}")
        
        if missing_optional:
            print(f"\n🔵 缺少可选依赖 ({len(missing_optional)}个):")
            for dep in missing_optional:
                print(f"  • {dep.name} - {dep.description}")
        
        # 安装建议
        if missing_required or version_issues:
            print(f"\n💡 安装建议:")
            print(f"  pip install -r requirements.txt")
            
            if missing_required:
                install_cmd = "pip install " + " ".join([dep.name for dep in missing_required])
                print(f"  # 或单独安装缺少的包:")
                print(f"  {install_cmd}")
        
        # 系统状态
        if not missing_required and not version_issues:
            print(f"\n🎉 系统依赖检查通过！可以正常启动林嫣然数字生命体系统。")
        else:
            print(f"\n⚠️  系统依赖不完整，请先安装缺少的依赖包。")
    
    def generate_install_script(self) -> str:
        """生成安装脚本"""
        missing_required = [dep for dep in self.dependencies 
                          if dep.required and not self.results.get(dep.name, {}).get('installed', False)]
        
        if not missing_required:
            return ""
        
        script = "#!/bin/bash\n"
        script += "# 林嫣然数字生命体系统 - 依赖安装脚本\n\n"
        script += "echo '🔧 安装缺少的依赖包...'\n\n"
        
        for dep in missing_required:
            script += f"echo '📦 安装 {dep.name}...'\n"
            script += f"pip install {dep.name}>={dep.min_version}\n\n"
        
        script += "echo '✅ 依赖安装完成！'\n"
        script += "echo '🔍 重新检查依赖...'\n"
        script += "python check_dependencies.py\n"
        
        return script


def main():
    """主函数"""
    print("🌸 林嫣然数字生命体系统 - 依赖检查工具 v3.1.0")
    print("=" * 80)
    
    checker = DependencyChecker()
    results = checker.check_all_dependencies()
    
    # 生成安装脚本
    install_script = checker.generate_install_script()
    if install_script:
        with open("install_missing_dependencies.sh", "w") as f:
            f.write(install_script)
        print(f"\n📝 已生成安装脚本: install_missing_dependencies.sh")
        print(f"   运行: chmod +x install_missing_dependencies.sh && ./install_missing_dependencies.sh")
    
    # 检查Python版本
    print(f"\n🐍 Python版本信息:")
    print(f"  • 当前版本: {sys.version}")
    print(f"  • 推荐版本: Python 3.8+")
    
    if sys.version_info < (3, 8):
        print(f"  ⚠️  警告: Python版本过低，建议升级到3.8或更高版本")
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 