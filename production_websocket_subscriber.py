#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
林嫣然数字生命生产环境WebSocket订阅服务
专门用于订阅所有数字生命对外的交互信息，进行全面测试
"""

import asyncio
import json
import websockets
import time
import signal
import sys
from datetime import datetime
from typing import Dict, Any, List
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('websocket_subscriber.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionWebSocketSubscriber:
    """生产环境WebSocket订阅服务"""
    
    def __init__(self, uri: str = "ws://localhost:8765"):
        self.uri = uri
        self.websocket = None
        self.is_running = False
        self.start_time = None
        
        # 统计信息
        self.stats = {
            "total_messages": 0,
            "message_types": {},
            "priorities": {},
            "levels": {},
            "users": {},
            "errors": 0,
            "reconnects": 0
        }
        
        # 消息存储（用于测试验证）
        self.message_history = []
        self.max_history = 1000
        
        # 测试配置
        self.test_cases = []
        self.test_results = {}
        
    async def connect(self) -> bool:
        """连接到WebSocket服务"""
        try:
            logger.info(f"🔌 连接到WebSocket服务: {self.uri}")
            
            self.websocket = await websockets.connect(
                self.uri,
                ping_interval=30,
                ping_timeout=10,
                close_timeout=10
            )
            
            self.is_running = True
            self.start_time = datetime.now()
            
            logger.info("✅ WebSocket连接成功建立")
            return True
            
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            self.stats["errors"] += 1
            return False
    
    async def subscribe_all_messages(self):
        """订阅所有消息类型"""
        try:
            subscribe_message = {
                "type": "subscribe",
                "message_types": ["all"],
                "user_id": "production_subscriber",
                "timestamp": time.time()
            }
            
            await self.websocket.send(json.dumps(subscribe_message))
            logger.info("📡 已订阅所有消息类型")
            
        except Exception as e:
            logger.error(f"❌ 订阅失败: {e}")
            self.stats["errors"] += 1
    
    def format_message_output(self, message: Dict[str, Any]) -> str:
        """格式化消息输出"""
        msg_type = message.get('type', 'unknown')
        timestamp = message.get('timestamp', time.time())
        target_user = message.get('target_user_id', 'N/A')
        level = message.get('message_level', 'N/A')
        priority = message.get('priority', 'N/A')
        msg_id = message.get('message_id', 'N/A')
        data = message.get('data', {})
        
        time_str = datetime.fromtimestamp(timestamp).strftime('%H:%M:%S.%f')[:-3]
        
        # 选择emoji
        emojis = {
            'reminder_notification': '⏰',
            'proactive_expression': '💬',
            'emotion_update': '😊',
            'cognition_update': '🧠',
            'skill_execution': '🛠️',
            'system_status': '⚙️',
            'chat_response': '💭',
            'market_insight': '📈',
            'creative_expression': '🎨',
            'welcome': '🌸',
            'subscription_confirmed': '✅',
            'pong': '🏓'
        }
        
        emoji = emojis.get(msg_type, '📨')
        
        output_lines = [
            f"\n{emoji} 【{msg_type.upper()}】 - {time_str}",
            f"   🆔 消息ID: {msg_id}",
            f"   👤 目标用户: {target_user}",
            f"   📊 级别: {level} | 优先级: {priority}"
        ]
        
        # 添加数据内容
        if isinstance(data, dict) and data:
            output_lines.append("   📦 数据内容:")
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    value_str = json.dumps(value, ensure_ascii=False)[:100]
                    if len(str(value)) > 100:
                        value_str += "..."
                else:
                    value_str = str(value)[:100]
                output_lines.append(f"      {key}: {value_str}")
        elif data:
            output_lines.append(f"   📦 数据: {str(data)[:100]}")
        
        return "\n".join(output_lines)
    
    def update_statistics(self, message: Dict[str, Any]):
        """更新统计信息"""
        self.stats["total_messages"] += 1
        
        msg_type = message.get('type', 'unknown')
        priority = message.get('priority', 'unknown')
        level = message.get('message_level', 'unknown')
        user_id = message.get('target_user_id', 'unknown')
        
        # 更新各类统计
        self.stats["message_types"][msg_type] = self.stats["message_types"].get(msg_type, 0) + 1
        self.stats["priorities"][priority] = self.stats["priorities"].get(priority, 0) + 1
        self.stats["levels"][level] = self.stats["levels"].get(level, 0) + 1
        self.stats["users"][user_id] = self.stats["users"].get(user_id, 0) + 1
        
        # 存储消息历史
        self.message_history.append({
            "timestamp": datetime.now().isoformat(),
            "message": message
        })
        
        # 保持历史记录大小
        if len(self.message_history) > self.max_history:
            self.message_history = self.message_history[-self.max_history:]
    
    def print_statistics(self):
        """打印详细统计信息"""
        if not self.start_time:
            return
            
        uptime = datetime.now() - self.start_time
        
        print("\n" + "=" * 70)
        print("📊 WebSocket订阅服务统计报告")
        print("=" * 70)
        print(f"⏱️  运行时长: {uptime}")
        print(f"📨 总消息数: {self.stats['total_messages']}")
        print(f"❌ 错误次数: {self.stats['errors']}")
        print(f"🔄 重连次数: {self.stats['reconnects']}")
        
        if self.stats["message_types"]:
            print(f"\n📋 消息类型分布:")
            for msg_type, count in sorted(self.stats["message_types"].items()):
                percentage = (count / self.stats['total_messages']) * 100
                print(f"   {msg_type}: {count} ({percentage:.1f}%)")
        
        if self.stats["priorities"]:
            print(f"\n⚡ 优先级分布:")
            for priority, count in sorted(self.stats["priorities"].items()):
                percentage = (count / self.stats['total_messages']) * 100
                print(f"   {priority}: {count} ({percentage:.1f}%)")
        
        if self.stats["levels"]:
            print(f"\n🎯 消息级别分布:")
            for level, count in sorted(self.stats["levels"].items()):
                percentage = (count / self.stats['total_messages']) * 100
                print(f"   {level}: {count} ({percentage:.1f}%)")
        
        if self.stats["users"]:
            print(f"\n👥 用户分布:")
            for user, count in sorted(self.stats["users"].items()):
                percentage = (count / self.stats['total_messages']) * 100
                print(f"   {user}: {count} ({percentage:.1f}%)")
        
        print("=" * 70)
    
    async def listen_messages(self):
        """监听WebSocket消息"""
        try:
            logger.info("👂 开始监听WebSocket消息...")
            
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    
                    # 更新统计
                    self.update_statistics(data)
                    
                    # 输出消息
                    formatted_output = self.format_message_output(data)
                    print(formatted_output)
                    
                    # 记录到日志
                    logger.info(f"收到消息: {data.get('type', 'unknown')}")
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON解析失败: {e}")
                    self.stats["errors"] += 1
                except Exception as e:
                    logger.error(f"❌ 处理消息失败: {e}")
                    self.stats["errors"] += 1
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("🔌 WebSocket连接已关闭")
        except Exception as e:
            logger.error(f"❌ 监听消息时发生错误: {e}")
            self.stats["errors"] += 1
    
    async def send_ping(self):
        """发送心跳包"""
        try:
            ping_message = {
                "type": "ping",
                "timestamp": time.time()
            }
            await self.websocket.send(json.dumps(ping_message))
            logger.debug("🏓 发送心跳包")
        except Exception as e:
            logger.error(f"❌ 发送心跳失败: {e}")
            self.stats["errors"] += 1
    
    async def run_with_reconnect(self):
        """带重连机制的运行"""
        while True:
            try:
                # 尝试连接
                if await self.connect():
                    # 订阅消息
                    await self.subscribe_all_messages()
                    
                    # 开始监听
                    await self.listen_messages()
                
            except Exception as e:
                logger.error(f"❌ 运行异常: {e}")
                self.stats["errors"] += 1
            
            if not self.is_running:
                break
                
            # 重连逻辑
            self.stats["reconnects"] += 1
            logger.info(f"🔄 5秒后尝试重连... (第{self.stats['reconnects']}次)")
            await asyncio.sleep(5)
    
    async def run_periodic_stats(self):
        """定期显示统计信息"""
        while self.is_running:
            await asyncio.sleep(60)  # 每分钟显示一次
            if self.stats["total_messages"] > 0:
                self.print_statistics()
    
    def save_test_report(self):
        """保存测试报告"""
        report = {
            "test_time": datetime.now().isoformat(),
            "duration": str(datetime.now() - self.start_time) if self.start_time else "0",
            "statistics": self.stats,
            "recent_messages": self.message_history[-10:],  # 最近10条消息
            "websocket_uri": self.uri
        }
        
        with open(f"websocket_test_report_{int(time.time())}.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("📄 测试报告已保存")
    
    async def shutdown(self):
        """优雅关闭"""
        logger.info("🛑 开始关闭WebSocket订阅服务...")
        
        self.is_running = False
        
        if self.websocket:
            await self.websocket.close()
        
        # 打印最终统计
        self.print_statistics()
        
        # 保存测试报告
        self.save_test_report()
        
        logger.info("✅ WebSocket订阅服务已关闭")

async def main():
    """主函数"""
    print("🌸 林嫣然数字生命生产环境WebSocket订阅服务")
    print("=" * 70)
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔗 连接地址: ws://localhost:8765")
    print(f"📋 功能: 订阅所有数字生命对外交互信息")
    print("💡 按 Ctrl+C 停止服务")
    print("=" * 70)
    
    # 创建订阅服务
    subscriber = ProductionWebSocketSubscriber()
    
    # 设置信号处理
    def signal_handler():
        print("\n🛑 收到停止信号，正在优雅关闭...")
        subscriber.is_running = False
    
    # 注册信号处理器
    if hasattr(signal, 'SIGINT'):
        signal.signal(signal.SIGINT, lambda s, f: signal_handler())
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, lambda s, f: signal_handler())
    
    try:
        # 并行运行监听和统计
        await asyncio.gather(
            subscriber.run_with_reconnect(),
            subscriber.run_periodic_stats()
        )
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    finally:
        await subscriber.shutdown()

if __name__ == "__main__":
    asyncio.run(main()) 