#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面Redis缓存清理脚本
老王专业出品 - 把所有缓存都给你清理得干干净净！

涵盖项目中所有Redis使用场景：
1. 主动表达相关缓存
2. 财经数据缓存
3. 工作日服务缓存
4. 联系人管理缓存
5. 实时数据服务缓存
6. 中间件缓存
7. 用户行为分析缓存
8. 记忆系统缓存
9. 智能缓存系统
10. 性能优化缓存
"""

import os
import sys
import json
import redis
import time
import glob
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from utilities.unified_logger import get_unified_logger

logger = get_unified_logger("comprehensive_redis_cleanup")

class ComprehensiveRedisCleanup:
    """全面的Redis缓存清理器"""
    
    def __init__(self):
        """初始化清理器"""
        self.redis_client = None
        self.redis_cluster_client = None
        self.stats = {
            "total_keys_found": 0,
            "total_keys_deleted": 0,
            "categories_cleaned": 0,
            "errors": 0,
            "start_time": datetime.now(),
            "categories": {}
        }
        
        # 所有Redis键的分类模式
        self.redis_key_patterns = {
            # 主动表达相关 (yanran前缀)
            "proactive_expression": [
                "yanran:proactive_behavior:*",
                "yanran:proactive_last_time:*",
                "yanran:proactive_count:*",
                "yanran:daily_proactive_count:*",
                "yanran:proactive_history:*",
                "yanran:proactive_trigger:*",
                "yanran:proactive_memory:*",
                "yanran:proactive_context:*"
            ],
            
            # 记忆系统缓存
            "memory_system": [
                "yanran:recent_conversations:*",
                "yanran:user_emotion:*",
                "yanran:user_activity:*",
                "yanran:episodic_memory:*",
                "yanran:memory_associations:*",
                "yanran:semantic_memory:*",
                "yanran:vector_memory:*",
                "yanran:memory_index:*",
                "yanran:user_memory:*",
                "yanran:conversation_history:*",
                "yanran:interaction_history:*"
            ],
            
            # 用户行为分析
            "user_behavior": [
                "yanran:recent_behavior_data:*",
                "yanran:episodic_behavior_data:*",
                "yanran:user_profile:*",
                "yanran:behavior_pattern:*",
                "yanran:interaction_stats:*",
                "yanran:user_preferences:*",
                "yanran:user_context:*"
            ],
            
            # 财经数据缓存
            "financial_data": [
                "yanran:financial_news:*",
                "yanran:market_data:*",
                "yanran:stock_data:*",
                "yanran:hot_topics:*",
                "yanran:market_insight:*",
                "yanran:financial_report:*",
                "yanran:economic_data:*",
                "yanran:news_cache:*"
            ],
            
            # 工作日服务缓存
            "workday_service": [
                "workday_service:*",
                "yanran:workday:*",
                "yanran:calendar:*",
                "yanran:holiday:*"
            ],
            
            # 联系人管理缓存
            "contacts": [
                "contacts:*",
                "yanran:contacts:*",
                "yanran:user_info:*",
                "yanran:friend_list:*",
                "yanran:group_info:*"
            ],
            
            # 实时数据服务缓存
            "realtime_data": [
                "realtime:*",
                "yanran:realtime:*",
                "yanran:market_info:*",
                "yanran:weather_info:*",
                "yanran:activity_status:*",
                "yanran:calendar_info:*"
            ],
            
            # 中间件缓存
            "middleware_cache": [
                "dl_cache:*",
                "yanran:api_cache:*",
                "yanran:request_cache:*",
                "yanran:response_cache:*",
                "yanran:session_cache:*"
            ],
            
            # 数据整合缓存
            "data_integration": [
                "data_integration:*",
                "yanran:mysql_cache:*",
                "yanran:datasource_cache:*",
                "yanran:query_cache:*"
            ],
            
            # 智能缓存系统
            "intelligent_cache": [
                "yanran:l1_cache:*",
                "yanran:l2_cache:*",
                "yanran:l3_cache:*",
                "yanran:cache_stats:*",
                "yanran:cache_metadata:*"
            ],
            
            # 性能优化缓存
            "performance_cache": [
                "yanran:perf_cache:*",
                "yanran:function_cache:*",
                "yanran:result_cache:*",
                "yanran:computation_cache:*"
            ],
            
            # 临时和会话缓存
            "temporary_cache": [
                "yanran:temp:*",
                "yanran:session:*",
                "yanran:context:*",
                "yanran:state:*",
                "yanran:lock:*"
            ],
            
            # 监控和统计缓存
            "monitoring_stats": [
                "yanran:stats:*",
                "yanran:metrics:*",
                "yanran:monitor:*",
                "yanran:health:*",
                "yanran:performance:*"
            ],
            
            # 其他系统缓存
            "system_cache": [
                "yanran:config:*",
                "yanran:settings:*",
                "yanran:feature_flags:*",
                "yanran:system_state:*"
            ]
        }
        
        self._init_redis_connections()
    
    def _init_redis_connections(self):
        """初始化Redis连接"""
        try:
            # 标准Redis连接
            self.redis_client = redis.Redis(
                host='localhost',
                port=6379,
                db=0,
                decode_responses=True,
                socket_timeout=10,
                socket_connect_timeout=10
            )
            
            # 测试连接
            self.redis_client.ping()
            logger.success("✅ Redis连接初始化成功")
            
        except Exception as e:
            logger.error(f"❌ Redis连接初始化失败: {e}")
            self.redis_client = None
        
        # 尝试连接Redis集群（如果有的话）
        try:
            from redis.cluster import RedisCluster
            startup_nodes = [
                {"host": "localhost", "port": 6379},
                {"host": "localhost", "port": 6380},
                {"host": "localhost", "port": 6381}
            ]
            
            self.redis_cluster_client = RedisCluster(
                startup_nodes=startup_nodes,
                decode_responses=True,
                skip_full_coverage_check=True
            )
            
            self.redis_cluster_client.ping()
            logger.success("✅ Redis集群连接初始化成功")
            
        except Exception as e:
            logger.debug(f"Redis集群连接失败（可能未配置）: {e}")
            self.redis_cluster_client = None
    
    def analyze_redis_usage(self) -> Dict[str, Any]:
        """分析Redis使用情况"""
        if not self.redis_client:
            logger.error("❌ Redis连接不可用")
            return {}
        
        try:
            logger.info("🔍 开始分析Redis使用情况...")
            
            analysis = {
                "total_keys": 0,
                "categories": {},
                "memory_usage": {},
                "key_types": {},
                "ttl_analysis": {},
                "large_keys": [],
                "expired_keys": 0
            }
            
            # 获取所有键
            all_keys = self.redis_client.keys("*")
            analysis["total_keys"] = len(all_keys)
            
            logger.info(f"📊 发现 {len(all_keys)} 个Redis键")
            
            # 按分类统计
            for category, patterns in self.redis_key_patterns.items():
                category_keys = []
                for pattern in patterns:
                    matching_keys = self.redis_client.keys(pattern)
                    category_keys.extend(matching_keys)
                
                analysis["categories"][category] = {
                    "count": len(category_keys),
                    "keys": category_keys[:10],  # 只显示前10个
                    "total_size": 0
                }
                
                # 计算大小
                for key in category_keys:
                    try:
                        key_size = self.redis_client.memory_usage(key) or 0
                        analysis["categories"][category]["total_size"] += key_size
                    except:
                        pass
            
            # 分析键类型
            type_counts = {}
            for key in all_keys[:1000]:  # 限制分析数量
                try:
                    key_type = self.redis_client.type(key)
                    type_counts[key_type] = type_counts.get(key_type, 0) + 1
                except:
                    pass
            
            analysis["key_types"] = type_counts
            
            # 分析TTL
            ttl_ranges = {
                "永久": 0,
                "1小时内": 0,
                "1天内": 0,
                "1周内": 0,
                "1月内": 0,
                "更长": 0
            }
            
            for key in all_keys[:1000]:  # 限制分析数量
                try:
                    ttl = self.redis_client.ttl(key)
                    if ttl == -1:
                        ttl_ranges["永久"] += 1
                    elif ttl <= 3600:
                        ttl_ranges["1小时内"] += 1
                    elif ttl <= 86400:
                        ttl_ranges["1天内"] += 1
                    elif ttl <= 604800:
                        ttl_ranges["1周内"] += 1
                    elif ttl <= 2592000:
                        ttl_ranges["1月内"] += 1
                    else:
                        ttl_ranges["更长"] += 1
                except:
                    pass
            
            analysis["ttl_analysis"] = ttl_ranges
            
            # 获取内存使用情况
            try:
                info = self.redis_client.info("memory")
                analysis["memory_usage"] = {
                    "used_memory": info.get("used_memory_human", "未知"),
                    "used_memory_peak": info.get("used_memory_peak_human", "未知"),
                    "used_memory_lua": info.get("used_memory_lua_human", "未知")
                }
            except:
                pass
            
            self.stats["total_keys_found"] = analysis["total_keys"]
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 分析Redis使用情况失败: {e}")
            return {}
    
    def cleanup_category(self, category: str, dry_run: bool = False) -> Dict[str, Any]:
        """清理指定分类的缓存"""
        if not self.redis_client:
            return {"error": "Redis连接不可用"}
        
        if category not in self.redis_key_patterns:
            return {"error": f"未知分类: {category}"}
        
        try:
            logger.info(f"🧹 开始清理分类: {category}")
            
            patterns = self.redis_key_patterns[category]
            deleted_count = 0
            found_count = 0
            errors = 0
            
            for pattern in patterns:
                try:
                    keys = self.redis_client.keys(pattern)
                    found_count += len(keys)
                    
                    if keys and not dry_run:
                        # 批量删除
                        deleted = self.redis_client.delete(*keys)
                        deleted_count += deleted
                        logger.info(f"  删除 {pattern}: {deleted} 个键")
                    elif keys:
                        logger.info(f"  发现 {pattern}: {len(keys)} 个键 (模拟模式)")
                        
                except Exception as e:
                    logger.error(f"  清理模式 {pattern} 失败: {e}")
                    errors += 1
            
            result = {
                "category": category,
                "found_keys": found_count,
                "deleted_keys": deleted_count,
                "errors": errors,
                "dry_run": dry_run
            }
            
            if not dry_run:
                self.stats["total_keys_deleted"] += deleted_count
                self.stats["categories_cleaned"] += 1
                self.stats["categories"][category] = result
            
            logger.success(f"✅ 分类 {category} 清理完成: 发现 {found_count} 个键, 删除 {deleted_count} 个键")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 清理分类 {category} 失败: {e}")
            self.stats["errors"] += 1
            return {"error": str(e)}
    
    def cleanup_expired_keys(self, dry_run: bool = False) -> Dict[str, Any]:
        """清理过期键"""
        if not self.redis_client:
            return {"error": "Redis连接不可用"}
        
        try:
            logger.info("🧹 开始清理过期键...")
            
            all_keys = self.redis_client.keys("*")
            expired_count = 0
            
            for key in all_keys:
                try:
                    ttl = self.redis_client.ttl(key)
                    if ttl == 0:  # 已过期
                        if not dry_run:
                            self.redis_client.delete(key)
                        expired_count += 1
                except:
                    pass
            
            result = {
                "expired_keys": expired_count,
                "dry_run": dry_run
            }
            
            if not dry_run:
                self.stats["total_keys_deleted"] += expired_count
            
            logger.success(f"✅ 过期键清理完成: 清理 {expired_count} 个过期键")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 清理过期键失败: {e}")
            return {"error": str(e)}
    
    def cleanup_large_keys(self, size_threshold: int = 1024 * 1024, dry_run: bool = False) -> Dict[str, Any]:
        """清理大键"""
        if not self.redis_client:
            return {"error": "Redis连接不可用"}
        
        try:
            logger.info(f"🧹 开始清理大于 {size_threshold} 字节的大键...")
            
            all_keys = self.redis_client.keys("*")
            large_keys = []
            
            for key in all_keys[:1000]:  # 限制检查数量
                try:
                    size = self.redis_client.memory_usage(key)
                    if size and size > size_threshold:
                        large_keys.append({
                            "key": key,
                            "size": size,
                            "type": self.redis_client.type(key)
                        })
                except:
                    pass
            
            deleted_count = 0
            if large_keys and not dry_run:
                keys_to_delete = [item["key"] for item in large_keys]
                deleted_count = self.redis_client.delete(*keys_to_delete)
            
            result = {
                "large_keys_found": len(large_keys),
                "large_keys_deleted": deleted_count,
                "threshold_bytes": size_threshold,
                "dry_run": dry_run,
                "sample_keys": large_keys[:10]  # 显示前10个
            }
            
            if not dry_run:
                self.stats["total_keys_deleted"] += deleted_count
            
            logger.success(f"✅ 大键清理完成: 发现 {len(large_keys)} 个大键, 删除 {deleted_count} 个")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 清理大键失败: {e}")
            return {"error": str(e)}
    
    def cleanup_by_age(self, days_old: int = 7, dry_run: bool = False) -> Dict[str, Any]:
        """根据年龄清理键"""
        if not self.redis_client:
            return {"error": "Redis连接不可用"}
        
        try:
            logger.info(f"🧹 开始清理 {days_old} 天前的键...")
            
            # 这里需要根据键名中的时间戳来判断
            # 实际实现需要根据具体的键命名规则
            cutoff_time = datetime.now() - timedelta(days=days_old)
            
            old_keys = []
            all_keys = self.redis_client.keys("*")
            
            for key in all_keys:
                # 尝试从键名中提取时间戳
                if self._is_key_old(key, cutoff_time):
                    old_keys.append(key)
            
            deleted_count = 0
            if old_keys and not dry_run:
                deleted_count = self.redis_client.delete(*old_keys)
            
            result = {
                "old_keys_found": len(old_keys),
                "old_keys_deleted": deleted_count,
                "cutoff_days": days_old,
                "dry_run": dry_run
            }
            
            if not dry_run:
                self.stats["total_keys_deleted"] += deleted_count
            
            logger.success(f"✅ 按年龄清理完成: 发现 {len(old_keys)} 个旧键, 删除 {deleted_count} 个")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 按年龄清理失败: {e}")
            return {"error": str(e)}
    
    def _is_key_old(self, key: str, cutoff_time: datetime) -> bool:
        """判断键是否过旧"""
        try:
            # 尝试从键名中提取时间戳
            import re
            
            # 匹配时间戳模式
            timestamp_patterns = [
                r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
                r'(\d{10})',             # Unix timestamp
                r'(\d{13})',             # Unix timestamp (ms)
                r'(\d{8})',              # YYYYMMDD
            ]
            
            for pattern in timestamp_patterns:
                match = re.search(pattern, key)
                if match:
                    timestamp_str = match.group(1)
                    
                    try:
                        if len(timestamp_str) == 10 and timestamp_str.isdigit():
                            # Unix timestamp
                            key_time = datetime.fromtimestamp(int(timestamp_str))
                        elif len(timestamp_str) == 13 and timestamp_str.isdigit():
                            # Unix timestamp (ms)
                            key_time = datetime.fromtimestamp(int(timestamp_str) / 1000)
                        elif len(timestamp_str) == 8 and timestamp_str.isdigit():
                            # YYYYMMDD
                            key_time = datetime.strptime(timestamp_str, '%Y%m%d')
                        elif len(timestamp_str) == 10 and '-' in timestamp_str:
                            # YYYY-MM-DD
                            key_time = datetime.strptime(timestamp_str, '%Y-%m-%d')
                        else:
                            continue
                        
                        return key_time < cutoff_time
                    except:
                        continue
            
            return False
            
        except Exception:
            return False
    
    def comprehensive_cleanup(self, dry_run: bool = False, 
                           exclude_categories: List[str] = None) -> Dict[str, Any]:
        """全面清理"""
        if exclude_categories is None:
            exclude_categories = []
        
        try:
            logger.info("🚀 开始全面Redis缓存清理...")
            
            # 1. 分析当前状态
            analysis = self.analyze_redis_usage()
            
            # 2. 清理各个分类
            category_results = {}
            for category in self.redis_key_patterns.keys():
                if category not in exclude_categories:
                    result = self.cleanup_category(category, dry_run)
                    category_results[category] = result
            
            # 3. 清理过期键
            expired_result = self.cleanup_expired_keys(dry_run)
            
            # 4. 清理大键
            large_keys_result = self.cleanup_large_keys(dry_run=dry_run)
            
            # 5. 按年龄清理
            age_result = self.cleanup_by_age(days_old=30, dry_run=dry_run)
            
            # 6. 最终分析
            final_analysis = self.analyze_redis_usage() if not dry_run else {}
            
            # 生成报告
            report = {
                "start_time": self.stats["start_time"].isoformat(),
                "end_time": datetime.now().isoformat(),
                "dry_run": dry_run,
                "initial_analysis": analysis,
                "category_results": category_results,
                "expired_keys_result": expired_result,
                "large_keys_result": large_keys_result,
                "age_cleanup_result": age_result,
                "final_analysis": final_analysis,
                "summary": {
                    "total_keys_found": self.stats["total_keys_found"],
                    "total_keys_deleted": self.stats["total_keys_deleted"],
                    "categories_cleaned": self.stats["categories_cleaned"],
                    "errors": self.stats["errors"]
                }
            }
            
            logger.success("🎉 全面Redis缓存清理完成!")
            logger.info(f"📊 总计发现 {self.stats['total_keys_found']} 个键")
            logger.info(f"📊 总计删除 {self.stats['total_keys_deleted']} 个键")
            logger.info(f"📊 清理了 {self.stats['categories_cleaned']} 个分类")
            
            return report
            
        except Exception as e:
            logger.error(f"❌ 全面清理失败: {e}")
            return {"error": str(e)}
    
    def print_analysis_report(self, analysis: Dict[str, Any]):
        """打印分析报告"""
        if not analysis:
            return
        
        print("\n" + "="*80)
        print("📊 Redis使用情况分析报告")
        print("="*80)
        
        print(f"总键数: {analysis['total_keys']}")
        print(f"内存使用: {analysis.get('memory_usage', {}).get('used_memory', '未知')}")
        
        print("\n📋 分类统计:")
        for category, info in analysis.get('categories', {}).items():
            print(f"  {category}: {info['count']} 个键 ({info['total_size']} 字节)")
        
        print("\n🔑 键类型分布:")
        for key_type, count in analysis.get('key_types', {}).items():
            print(f"  {key_type}: {count} 个")
        
        print("\n⏰ TTL分布:")
        for range_name, count in analysis.get('ttl_analysis', {}).items():
            print(f"  {range_name}: {count} 个")
        
        print("="*80)


def main():
    """主函数"""
    try:
        cleaner = ComprehensiveRedisCleanup()
        
        print("🔧 全面Redis缓存清理工具 - 老王专业版")
        print("="*60)
        print("1. 分析Redis使用情况")
        print("2. 清理主动表达缓存")
        print("3. 清理财经数据缓存")
        print("4. 清理记忆系统缓存")
        print("5. 清理用户行为缓存")
        print("6. 清理所有临时缓存")
        print("7. 清理过期键")
        print("8. 清理大键 (>1MB)")
        print("9. 清理30天前的旧键")
        print("10. 全面清理 (模拟模式)")
        print("11. 全面清理 (真实执行)")
        print("12. 自定义分类清理")
        print("13. 退出")
        
        while True:
            choice = input("\n请选择操作 (1-13): ").strip()
            
            if choice == "1":
                analysis = cleaner.analyze_redis_usage()
                cleaner.print_analysis_report(analysis)
                
            elif choice == "2":
                result = cleaner.cleanup_category("proactive_expression")
                print(f"结果: {result}")
                
            elif choice == "3":
                result = cleaner.cleanup_category("financial_data")
                print(f"结果: {result}")
                
            elif choice == "4":
                result = cleaner.cleanup_category("memory_system")
                print(f"结果: {result}")
                
            elif choice == "5":
                result = cleaner.cleanup_category("user_behavior")
                print(f"结果: {result}")
                
            elif choice == "6":
                result = cleaner.cleanup_category("temporary_cache")
                print(f"结果: {result}")
                
            elif choice == "7":
                result = cleaner.cleanup_expired_keys()
                print(f"结果: {result}")
                
            elif choice == "8":
                result = cleaner.cleanup_large_keys()
                print(f"结果: {result}")
                
            elif choice == "9":
                result = cleaner.cleanup_by_age(days_old=30)
                print(f"结果: {result}")
                
            elif choice == "10":
                confirm = input("确认执行全面清理 (模拟模式)? (y/N): ").strip().lower()
                if confirm == 'y':
                    result = cleaner.comprehensive_cleanup(dry_run=True)
                    print(f"模拟结果: {result['summary']}")
                
            elif choice == "11":
                confirm = input("⚠️  确认执行全面清理 (真实删除)? (y/N): ").strip().lower()
                if confirm == 'y':
                    result = cleaner.comprehensive_cleanup(dry_run=False)
                    print(f"清理结果: {result['summary']}")
                
            elif choice == "12":
                print("可用分类:")
                for i, category in enumerate(cleaner.redis_key_patterns.keys(), 1):
                    print(f"  {i}. {category}")
                
                try:
                    cat_choice = int(input("选择分类编号: ").strip())
                    categories = list(cleaner.redis_key_patterns.keys())
                    if 1 <= cat_choice <= len(categories):
                        category = categories[cat_choice - 1]
                        result = cleaner.cleanup_category(category)
                        print(f"结果: {result}")
                    else:
                        print("❌ 无效选择")
                except ValueError:
                    print("❌ 请输入有效数字")
                
            elif choice == "13":
                print("👋 退出清理工具")
                break
                
            else:
                print("❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出清理工具")
    except Exception as e:
        logger.error(f"主程序异常: {e}")

if __name__ == "__main__":
    main() 