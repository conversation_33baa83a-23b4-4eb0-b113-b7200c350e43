#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试"流浪地球画面"提示词
"""

import requests
import json
import time

def test_liulangdiqiu():
    """测试流浪地球画面提示词"""
    print("🔥 老王测试'流浪地球画面'提示词")
    print("=" * 60)
    
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 测试两种调用方式
    test_cases = [
        {
            "name": "直接API调用",
            "data": {
                "model": "jimeng-4.0",
                "prompt": "流浪地球画面",
                "negative_prompt": "低质量，模糊",
                "width": 1024,
                "height": 1024,
                "sample_strength": 0.5,
                "response_format": "url"
            }
        },
        {
            "name": "模拟Drawing Skill参数",
            "data": {
                "model": "jimeng-4.0",
                "prompt": "流浪地球画面",
                "negative_prompt": "低质量，模糊画面，混乱布局，扭曲的场景，畸形的肢体，不协调的颜色，不真实",
                "width": 1080,
                "height": 1920,
                "sample_strength": 0.8,
                "response_format": "url"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🎯 {test_case['name']}")
        print(f"📝 提示词: {test_case['data']['prompt']}")
        print(f"📐 尺寸: {test_case['data']['width']}x{test_case['data']['height']}")
        print(f"🎚️ 精细度: {test_case['data']['sample_strength']}")
        print("-" * 40)
        
        try:
            start_time = time.time()
            response = requests.post(url, headers=headers, json=test_case['data'], timeout=60)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️ 耗时: {elapsed_time:.1f}秒")
            print(f"📊 HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📋 完整响应: {result}")
                
                data_list = result.get("data", [])
                if data_list:
                    print(f"✅ 成功！返回 {len(data_list)} 张图片")
                    for i, item in enumerate(data_list):
                        if isinstance(item, dict) and "url" in item:
                            print(f"   图片 {i+1}: {item['url'][:60]}...")
                else:
                    print(f"❌ 返回空数据")
                    print(f"   created: {result.get('created', 'N/A')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   错误内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        print()

if __name__ == "__main__":
    test_liulangdiqiu()
