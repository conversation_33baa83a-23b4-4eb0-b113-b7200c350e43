{"skill_config": {"enabled": true, "priority": 8, "timeout": 300, "max_retries": 3, "description": "Python代码执行技能配置 - 集成aipyapp提供强大的自动化执行能力"}, "api_config": {"api_base_url": "http://localhost:8848", "timeout": 300, "max_retries": 3, "connection_pool_size": 5, "keep_alive": true}, "security": {"enabled": true, "allowed_modules": ["pandas", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "plotly", "requests", "beautifulsoup4", "json", "csv", "sqlite3", "openpyxl", "scipy", "sklearn", "yfinance", "feedparser", "lxml", "urllib", "datetime", "time", "re", "math", "statistics"], "blocked_operations": ["os.system", "subprocess.run", "subprocess.call", "subprocess.Popen", "exec", "eval", "__import__", "open('/etc", "open('/root", "open('/home", "rm -rf", "sudo", "chmod", "chown", "kill", "killall"], "blocked_file_patterns": ["/etc/*", "/root/*", "/home/<USER>", "~/.ssh/*", "/var/log/*", "/proc/*", "/sys/*"], "max_execution_time": 600, "max_memory_mb": 1024, "max_file_size_mb": 100}, "deep_research": {"enabled": true, "default_workflow": "comprehensive_analysis", "workflows": {"comprehensive_analysis": {"name": "综合分析工作流", "description": "数据收集 -> 清洗 -> 分析 -> 可视化 -> 报告生成", "steps": ["data_collection", "data_cleaning", "data_analysis", "visualization", "report_generation"]}, "web_scraping": {"name": "网络爬取工作流", "description": "目标识别 -> 数据爬取 -> 数据处理 -> 结果输出", "steps": ["target_identification", "web_scraping", "data_processing", "output_formatting"]}, "financial_analysis": {"name": "金融分析工作流", "description": "数据获取 -> 技术分析 -> 基本面分析 -> 风险评估 -> 投资建议", "steps": ["financial_data_fetch", "technical_analysis", "fundamental_analysis", "risk_assessment", "investment_recommendation"]}}}, "intent_mapping": {"keywords": ["python", "代码", "执行", "分析", "数据", "爬取", "抓取", "统计", "可视化", "图表", "报告", "excel", "csv", "json", "自动化", "批处理", "调研", "研究"], "intent_types": ["data_analysis", "research", "automation", "python_execution", "web_scraping", "visualization", "file_processing"], "priority_patterns": [".*分析.*数据.*", ".*爬取.*网站.*", ".*生成.*图表.*", ".*处理.*文件.*", ".*自动化.*任务.*"]}, "output_formats": {"supported_formats": ["text", "json", "csv", "excel", "html", "pdf", "png", "jpg", "svg"], "default_format": "text", "max_output_size_mb": 50}, "logging": {"enabled": true, "level": "INFO", "log_file": "logs/python_execution_skill.log", "max_log_size_mb": 100, "backup_count": 5}, "monitoring": {"enabled": true, "metrics": ["execution_count", "success_rate", "average_execution_time", "error_rate", "resource_usage"], "alert_thresholds": {"error_rate": 0.1, "execution_time": 300, "memory_usage": 0.8}}}