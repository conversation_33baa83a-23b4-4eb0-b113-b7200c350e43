#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王测试drawing_skill中的jimeng_generate_images方法
找出为什么API直接调用成功，但通过drawing_skill调用失败
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cognitive_modules.skills.drawing_skill import DrawingSkill
from datetime import datetime
import json

def test_drawing_skill_jimeng():
    """测试drawing_skill中的jimeng_generate_images方法"""
    
    print("🔥 老王测试drawing_skill中的jimeng_generate_images方法")
    print("=" * 70)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 初始化绘画技能
        print("📋 初始化绘画技能...")
        drawing_skill = DrawingSkill()

        print(f"✅ 绘画技能初始化成功")
        print(f"🔑 当前jimeng_session_id: {drawing_skill.jimeng_session_id[:20]}...")

        # 检查配置文件中的session_id
        config = drawing_skill.config  # 使用DrawingSkill实例的config属性
        jimeng_config = config.get("jimeng", {})
        config_session_id = jimeng_config.get("session_id", "未配置")
        print(f"📋 配置文件中的session_id: {config_session_id[:20] if config_session_id != '未配置' else config_session_id}...")
        
        print("-" * 50)
        
        # 测试参数（使用和独立测试相同的参数）
        test_params = {
            "prompt": "一只可爱的小猫",
            "model": "jimeng-4.0",
            "negative_prompt": "低质量，模糊画面",
            "width": 1080,  # 使用官网标准尺寸
            "height": 1920,
            "sample_strength": 0.8,
            "session_id": drawing_skill.jimeng_session_id
        }
        
        print("🎯 测试参数:")
        for key, value in test_params.items():
            if key == "session_id":
                print(f"   {key}: {str(value)[:20]}...")
            else:
                print(f"   {key}: {value}")
        
        print("-" * 50)
        print("🚀 调用drawing_skill.jimeng_generate_images...")
        
        # 调用jimeng_generate_images方法
        result = drawing_skill.jimeng_generate_images(**test_params)
        
        print("📥 方法调用完成")
        print(f"🔍 返回结果类型: {type(result)}")
        print(f"🔍 返回结果内容:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 分析结果
        if isinstance(result, dict):
            if "error" in result:
                print(f"❌ 方法返回错误: {result['error']}")
                print(f"   详情: {result.get('details', '无详情')}")
                return False
            elif "image_urls" in result and result["image_urls"]:
                print(f"✅ 方法调用成功！返回 {len(result['image_urls'])} 张图片")
                print("🖼️ 图片URL:")
                for i, url in enumerate(result["image_urls"], 1):
                    print(f"   {i}. {url[:50]}...")
                return True
            elif "created" in result and "data" in result:
                # 可能返回的是原始API格式
                data_list = result.get("data", [])
                if data_list:
                    print(f"✅ 方法返回原始API格式！包含 {len(data_list)} 张图片")
                    return True
                else:
                    print("⚠️ 方法返回原始API格式，但data为空")
                    return False
            else:
                print("⚠️ 方法返回格式异常")
                return False
        else:
            print(f"❌ 方法返回非字典格式: {type(result)}")
            return False
            
    except Exception as e:
        print(f"💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_drawing_skill_config():
    """测试drawing_skill的配置加载"""
    
    print("\n🔧 测试drawing_skill的配置加载...")
    print("-" * 50)
    
    try:
        # 🔥 老王修复：直接加载配置文件
        import os
        PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "drawing_skill.json")

        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}

        jimeng_config = config.get("jimeng", {})
        
        print("📋 jimeng配置内容:")
        for key, value in jimeng_config.items():
            if "session" in key.lower():
                print(f"   {key}: {str(value)[:20]}...")
            else:
                print(f"   {key}: {value}")
        
        # 测试session_id分割逻辑
        session_id_config = jimeng_config.get("session_id", "")
        if session_id_config:
            session_ids = session_id_config.split(",")
            session_ids = [sid.strip() for sid in session_ids if sid.strip()]
            print(f"🔍 分割后的session_id数量: {len(session_ids)}")
            for i, sid in enumerate(session_ids, 1):
                print(f"   Session {i}: {sid[:20]}...")
        else:
            print("⚠️ 配置文件中未找到session_id")
            
    except Exception as e:
        print(f"💥 配置测试异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试drawing_skill的jimeng方法...")
    
    # 测试1: 配置加载
    test_drawing_skill_config()
    
    # 测试2: jimeng_generate_images方法
    success = test_drawing_skill_jimeng()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ drawing_skill测试成功！")
    else:
        print("❌ drawing_skill测试失败！")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔥 测试完成！")
