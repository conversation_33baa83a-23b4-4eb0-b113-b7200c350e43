# OpenAI SDK 兼容性测试

本目录包含对OpenAI SDK兼容性进行测试的脚本和工具，确保系统能够在不同版本的SDK环境下正常工作。

## 目录内容

- `openai_sdk_integration_test.py` - 主测试脚本，检查各种API调用方式的兼容性
- `run_compatibility_tests.sh` - 运行测试并发送通知的Shell脚本
- `logs/` - 测试日志存储目录
- `sdk_integration_results.json` - 最新测试结果
- `crontab_example.txt` - 自动化测试的crontab配置示例

## 测试范围

测试脚本验证以下功能的兼容性：

1. AI适配器统一API调用
2. AI适配器异步API调用
3. 直接使用OpenAI SDK（v0.x或v1.x风格，取决于安装的SDK版本）

## 使用方法

### 手动运行测试

```bash
# 运行兼容性测试
python openai_sdk_integration_test.py

# 或使用Shell脚本（包含通知功能）
./run_compatibility_tests.sh [通知邮箱]
```

### 自动化测试

可以通过cron配置定期自动执行测试：

1. 编辑系统的crontab配置：
   ```bash
   crontab -e
   ```

2. 添加`crontab_example.txt`中的配置（根据实际环境修改路径）

3. 保存并退出编辑器

## 测试结果

测试结果将保存在以下位置：

- `sdk_integration_results.json` - 详细的测试结果（JSON格式）
- `logs/compatibility_test_*.log` - 测试日志

当测试失败时，脚本会自动发送通知邮件到指定地址。

## 注意事项

- 测试需要有效的OpenAI API密钥
- 确保`adapters/ai_service_adapter.py`中的兼容性处理逻辑正确
- 测试脚本会自动检测系统中安装的OpenAI SDK版本并进行相应测试
