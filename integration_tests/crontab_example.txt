# OpenAI SDK 兼容性测试 Crontab 配置示例
# 将以下内容添加到系统的crontab配置中（使用 crontab -e 命令编辑）

# 环境变量设置
SHELL=/bin/bash
PATH=/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin
# 设置项目根目录路径（根据实际情况修改）
PROJECT_ROOT=/Volumes/DiskPro/yanran_digital_life

# 每天凌晨2:00执行兼容性测试
0 2 * * * cd $PROJECT_ROOT && integration_tests/run_compatibility_tests.sh <EMAIL> >> $PROJECT_ROOT/integration_tests/logs/cron_run.log 2>&1

# 每周一凌晨3:00执行完整兼容性测试并发送结果报告（无论是否成功）
0 3 * * 1 cd $PROJECT_ROOT && integration_tests/run_compatibility_tests.sh --send-report <EMAIL> >> $PROJECT_ROOT/integration_tests/logs/cron_weekly.log 2>&1

# 系统更新后执行兼容性测试（需要与系统更新脚本配合）
# @reboot sleep 300 && cd $PROJECT_ROOT && integration_tests/run_compatibility_tests.sh <EMAIL> >> $PROJECT_ROOT/integration_tests/logs/cron_reboot.log 2>&1
