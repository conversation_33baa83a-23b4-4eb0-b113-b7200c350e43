#!/bin/bash
# OpenAI SDK 兼容性测试运行脚本
# 
# 该脚本用于在服务器上定期运行OpenAI SDK兼容性测试，
# 并在测试失败时发送通知。可通过cron定时执行。
#
# 使用方法：
# ./run_compatibility_tests.sh [通知邮箱]
#
# 创建日期: 2025-06-08

# 设置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$SCRIPT_DIR/logs"
LOG_FILE="$LOG_DIR/compatibility_test_$(date +%Y%m%d_%H%M%S).log"
RESULTS_FILE="$SCRIPT_DIR/sdk_integration_results.json"
EMAIL_TO="${1:-<EMAIL>}"  # 默认通知邮箱

# 创建日志目录
mkdir -p "$LOG_DIR"

# 记录开始信息
echo "=======================================" >> "$LOG_FILE"
echo "开始 OpenAI SDK 兼容性测试: $(date)" >> "$LOG_FILE"
echo "=======================================" >> "$LOG_FILE"

# 添加项目根目录到 Python 路径
cd "$PROJECT_ROOT"

# 运行测试
echo "运行兼容性测试..." >> "$LOG_FILE"
python "$SCRIPT_DIR/openai_sdk_integration_test.py" | tee -a "$LOG_FILE"

# 检查测试结果
if [ -f "$RESULTS_FILE" ]; then
    # 提取测试结果数据
    SUCCESS_COUNT=$(grep -o '"success": [0-9]*' "$RESULTS_FILE" | head -1 | awk '{print $2}')
    FAILURE_COUNT=$(grep -o '"failure": [0-9]*' "$RESULTS_FILE" | head -1 | awk '{print $2}')
    TOTAL_COUNT=$((SUCCESS_COUNT + FAILURE_COUNT))
    SUCCESS_RATE=$(echo "scale=2; $SUCCESS_COUNT * 100 / $TOTAL_COUNT" | bc)
    
    echo "测试完成。成功: $SUCCESS_COUNT, 失败: $FAILURE_COUNT, 成功率: $SUCCESS_RATE%" >> "$LOG_FILE"
    
    # 检查是否有失败的测试
    if [ "$FAILURE_COUNT" -gt 0 ]; then
        # 发送通知邮件
        SUBJECT="[警告] OpenAI SDK 兼容性测试失败"
        BODY="OpenAI SDK 兼容性测试发现问题。\n\n"
        BODY+="测试时间: $(date)\n"
        BODY+="成功测试: $SUCCESS_COUNT\n"
        BODY+="失败测试: $FAILURE_COUNT\n"
        BODY+="成功率: $SUCCESS_RATE%\n\n"
        BODY+="详细信息请查看测试日志: $LOG_FILE\n"
        BODY+="和测试结果文件: $RESULTS_FILE\n\n"
        BODY+="请尽快检查系统的 OpenAI SDK 兼容性问题。"
        
        # 使用mail命令发送邮件（需要系统配置好SMTP）
        echo -e "$BODY" | mail -s "$SUBJECT" "$EMAIL_TO"
        
        echo "已发送失败通知到 $EMAIL_TO" >> "$LOG_FILE"
        exit 1
    else
        echo "所有测试通过，无需发送通知。" >> "$LOG_FILE"
    fi
else
    echo "错误: 找不到测试结果文件 $RESULTS_FILE" >> "$LOG_FILE"
    exit 1
fi

echo "测试脚本执行完成: $(date)" >> "$LOG_FILE"
exit 0 