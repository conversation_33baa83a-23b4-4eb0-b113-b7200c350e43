2025-06-08 14:26:19,583 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:26:19,584 - openai_sdk_integration_test - INFO - ========================== OpenAI SDK 集成测试 ===========================
2025-06-08 14:26:19,584 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:26:19,584 - openai_sdk_integration_test - INFO - 测试开始时间: 2025-06-08 14:26:19
2025-06-08 14:26:19,869 - adapters.ai_service_adapter - INFO - AI服务适配器已创建
2025-06-08 14:26:19,869 - adapters.ai_service_adapter - INFO - 服务可用性: {'openai': True, 'zhipuai': False, 'dashscope': False, 'qianfan': False, 'baidu': False}
2025-06-08 14:26:19,869 - adapters.ai_service_adapter - INFO - 开始初始化AI服务适配器
2025-06-08 14:26:19,869 - adapters.ai_service_adapter - INFO - 异步HTTP库修补完成
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 从文件加载配置成功: configs/ai_services.json
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 初始化服务: openai
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - OpenAI配置 - API密钥: sk-FVduy***93887746, Base URL: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - OpenAI设置后 - API密钥: sk-FVduy***93887746, Base URL: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 服务 openai 初始化成功
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 初始化服务: zhipuai
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 zhipuai 不可用，模块未安装
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 zhipuai 初始化失败
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 初始化服务: dashscope
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 dashscope 不可用，模块未安装
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 dashscope 初始化失败
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 初始化服务: default_service
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 default_service 不可用，模块未安装
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 default_service 初始化失败
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 初始化服务: cache_enabled
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 cache_enabled 不可用，模块未安装
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 cache_enabled 初始化失败
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 初始化服务: cache_expiry
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 cache_expiry 不可用，模块未安装
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 cache_expiry 初始化失败
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 初始化服务: auto_fallback
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 auto_fallback 不可用，模块未安装
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - WARNING - 服务 auto_fallback 初始化失败
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 添加legacy系统OpenAI SDK兼容支持
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 兼容性模式 - OpenAI SDK版本: 1.84.0
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 为新版SDK (v1.x+) 添加旧版API兼容
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - AI服务适配器初始化完成
2025-06-08 14:26:19,871 - openai_sdk_integration_test - INFO - 当前OpenAI SDK版本: 1.84.0
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 添加legacy系统OpenAI SDK兼容支持
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 兼容性模式 - OpenAI SDK版本: 1.84.0
2025-06-08 14:26:19,871 - adapters.ai_service_adapter - INFO - 为新版SDK (v1.x+) 添加旧版API兼容
2025-06-08 14:26:19,871 - openai_sdk_integration_test - INFO - 已添加OpenAI SDK兼容性支持
2025-06-08 14:26:19,871 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:26:19,871 - openai_sdk_integration_test - INFO - ========================== 测试1: AI适配器统一API ===========================
2025-06-08 14:26:19,872 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:26:19,872 - adapters.ai_service_adapter - INFO - OpenAI SDK版本: 1.84.0
2025-06-08 14:26:19,872 - adapters.ai_service_adapter - INFO - OpenAI API调用前 - 当前Base URL: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:26:19,872 - adapters.ai_service_adapter - INFO - OpenAI API调用参数: {'messages': [{'role': 'system', 'content': "你是一个测试助手，请回复'集成测试1成功'。"}, {'role': 'user', 'content': '这是一个OpenAI SDK集成测试'}], 'temperature': 0.7, 'model': 'gpt-3.5-turbo', 'max_tokens': 1000, 'stream': False, 'top_p': 1.0}
2025-06-08 14:26:19,872 - adapters.ai_service_adapter - INFO - 检测到第三方API服务: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:26:19,872 - adapters.ai_service_adapter - INFO - 使用模型: gpt-3.5-turbo
2025-06-08 14:26:24,659 - httpx - INFO - HTTP Request: POST https://oneapi.xiongmaodaxia.online/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 14:26:24,663 - openai_sdk_integration_test - INFO - 响应内容: 集成测试1成功
2025-06-08 14:26:24,663 - openai_sdk_integration_test - INFO - 响应时间: 4.79秒
2025-06-08 14:26:24,663 - openai_sdk_integration_test - INFO - 测试1结果: 成功
2025-06-08 14:26:24,663 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:26:24,663 - openai_sdk_integration_test - INFO - ========================== 测试2: AI适配器异步API ===========================
2025-06-08 14:26:24,663 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:26:24,663 - adapters.ai_service_adapter - INFO - 异步调用 - 检测到第三方API服务: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:26:24,663 - adapters.ai_service_adapter - INFO - 异步调用 - 使用模型: gpt-3.5-turbo
2025-06-08 14:26:24,663 - adapters.ai_service_adapter - INFO - 异步调用 - 服务: openai, 模型: gpt-3.5-turbo
2025-06-08 14:26:24,663 - adapters.ai_service_adapter - INFO - 异步调用 - OpenAI SDK版本: 1.84.0
2025-06-08 14:26:24,663 - adapters.ai_service_adapter - INFO - 异步调用 - 检测到第三方API服务: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:26:24,663 - adapters.ai_service_adapter - INFO - 异步调用 - 使用模型: gpt-3.5-turbo
2025-06-08 14:26:28,905 - httpx - INFO - HTTP Request: POST https://oneapi.xiongmaodaxia.online/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 14:26:29,166 - adapters.ai_service_adapter - INFO - 异步调用 - 从响应中检测到DeepSeek模型: deepseek-ai/DeepSeek-V2.5
2025-06-08 14:26:29,168 - adapters.ai_service_adapter - INFO - 异步调用 - 使用专用解析器处理DeepSeek响应
2025-06-08 14:26:29,168 - adapters.ai_service_adapter - INFO - 开始解析DeepSeek响应
2025-06-08 14:26:29,168 - adapters.ai_service_adapter - INFO - 从DeepSeek响应中提取到content: 集成测试2成功...
2025-06-08 14:26:29,168 - adapters.ai_service_adapter - INFO - DeepSeek响应解析成功
2025-06-08 14:26:29,168 - openai_sdk_integration_test - INFO - 响应内容: 集成测试2成功
2025-06-08 14:26:29,168 - openai_sdk_integration_test - INFO - 响应时间: 4.50秒
2025-06-08 14:26:29,168 - openai_sdk_integration_test - INFO - 测试2结果: 成功
2025-06-08 14:26:29,168 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:26:29,168 - openai_sdk_integration_test - INFO - ===================== 测试3: 直接使用OpenAI SDK (v1.x) =====================
2025-06-08 14:26:29,168 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:26:33,698 - httpx - INFO - HTTP Request: POST https://oneapi.xiongmaodaxia.online/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 响应内容: 集成测试3成功
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 响应时间: 4.53秒
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 测试3结果: 成功
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - ============================== 集成测试结果摘要 ==============================
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 总测试数量: 4
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 成功测试数: 4
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 失败测试数: 0
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 成功率: 100.00%
2025-06-08 14:26:33,700 - openai_sdk_integration_test - INFO - 测试结束时间: 2025-06-08 14:26:33
2025-06-08 14:26:33,701 - openai_sdk_integration_test - INFO - 测试结果已保存到: /Volumes/DiskPro/yanran_digital_life/integration_tests/sdk_integration_results.json
2025-06-08 14:28:05,799 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:28:05,800 - openai_sdk_integration_test - INFO - ========================== OpenAI SDK 集成测试 ===========================
2025-06-08 14:28:05,800 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:28:05,800 - openai_sdk_integration_test - INFO - 测试开始时间: 2025-06-08 14:28:05
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - AI服务适配器已创建
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 服务可用性: {'openai': True, 'zhipuai': False, 'dashscope': False, 'qianfan': False, 'baidu': False}
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 开始初始化AI服务适配器
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 异步HTTP库修补完成
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 从文件加载配置成功: configs/ai_services.json
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 初始化服务: openai
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - OpenAI配置 - API密钥: sk-FVduy***93887746, Base URL: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - OpenAI设置后 - API密钥: sk-FVduy***93887746, Base URL: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 服务 openai 初始化成功
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 初始化服务: zhipuai
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 zhipuai 不可用，模块未安装
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 zhipuai 初始化失败
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 初始化服务: dashscope
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 dashscope 不可用，模块未安装
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 dashscope 初始化失败
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 初始化服务: default_service
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 default_service 不可用，模块未安装
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 default_service 初始化失败
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 初始化服务: cache_enabled
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 cache_enabled 不可用，模块未安装
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 cache_enabled 初始化失败
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - INFO - 初始化服务: cache_expiry
2025-06-08 14:28:06,019 - adapters.ai_service_adapter - WARNING - 服务 cache_expiry 不可用，模块未安装
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - WARNING - 服务 cache_expiry 初始化失败
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 初始化服务: auto_fallback
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - WARNING - 服务 auto_fallback 不可用，模块未安装
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - WARNING - 服务 auto_fallback 初始化失败
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 添加legacy系统OpenAI SDK兼容支持
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 兼容性模式 - OpenAI SDK版本: 1.84.0
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 为新版SDK (v1.x+) 添加旧版API兼容
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - AI服务适配器初始化完成
2025-06-08 14:28:06,020 - openai_sdk_integration_test - INFO - 当前OpenAI SDK版本: 1.84.0
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 添加legacy系统OpenAI SDK兼容支持
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 兼容性模式 - OpenAI SDK版本: 1.84.0
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 为新版SDK (v1.x+) 添加旧版API兼容
2025-06-08 14:28:06,020 - openai_sdk_integration_test - INFO - 已添加OpenAI SDK兼容性支持
2025-06-08 14:28:06,020 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:28:06,020 - openai_sdk_integration_test - INFO - ========================== 测试1: AI适配器统一API ===========================
2025-06-08 14:28:06,020 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - OpenAI SDK版本: 1.84.0
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - OpenAI API调用前 - 当前Base URL: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - OpenAI API调用参数: {'messages': [{'role': 'system', 'content': "你是一个测试助手，请回复'集成测试1成功'。"}, {'role': 'user', 'content': '这是一个OpenAI SDK集成测试'}], 'temperature': 0.7, 'model': 'gpt-3.5-turbo', 'max_tokens': 1000, 'stream': False, 'top_p': 1.0}
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 检测到第三方API服务: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:28:06,020 - adapters.ai_service_adapter - INFO - 使用模型: gpt-3.5-turbo
2025-06-08 14:28:10,651 - httpx - INFO - HTTP Request: POST https://oneapi.xiongmaodaxia.online/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 14:28:10,663 - openai_sdk_integration_test - INFO - 响应内容: 集成测试1成功
2025-06-08 14:28:10,664 - openai_sdk_integration_test - INFO - 响应时间: 4.64秒
2025-06-08 14:28:10,664 - openai_sdk_integration_test - INFO - 测试1结果: 成功
2025-06-08 14:28:10,664 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:28:10,664 - openai_sdk_integration_test - INFO - ========================== 测试2: AI适配器异步API ===========================
2025-06-08 14:28:10,664 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:28:10,664 - adapters.ai_service_adapter - INFO - 异步调用 - 检测到第三方API服务: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:28:10,664 - adapters.ai_service_adapter - INFO - 异步调用 - 使用模型: gpt-3.5-turbo
2025-06-08 14:28:10,664 - adapters.ai_service_adapter - INFO - 异步调用 - 服务: openai, 模型: gpt-3.5-turbo
2025-06-08 14:28:10,664 - adapters.ai_service_adapter - INFO - 异步调用 - OpenAI SDK版本: 1.84.0
2025-06-08 14:28:10,664 - adapters.ai_service_adapter - INFO - 异步调用 - 检测到第三方API服务: https://oneapi.xiongmaodaxia.online/v1
2025-06-08 14:28:10,664 - adapters.ai_service_adapter - INFO - 异步调用 - 使用模型: gpt-3.5-turbo
2025-06-08 14:28:15,241 - httpx - INFO - HTTP Request: POST https://oneapi.xiongmaodaxia.online/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 14:28:15,242 - adapters.ai_service_adapter - INFO - 异步调用 - 从响应中检测到DeepSeek模型: deepseek-ai/DeepSeek-V2.5
2025-06-08 14:28:15,242 - adapters.ai_service_adapter - INFO - 异步调用 - 使用专用解析器处理DeepSeek响应
2025-06-08 14:28:15,242 - adapters.ai_service_adapter - INFO - 开始解析DeepSeek响应
2025-06-08 14:28:15,242 - adapters.ai_service_adapter - INFO - 从DeepSeek响应中提取到content: 集成测试2成功...
2025-06-08 14:28:15,242 - adapters.ai_service_adapter - INFO - DeepSeek响应解析成功
2025-06-08 14:28:15,242 - openai_sdk_integration_test - INFO - 响应内容: 集成测试2成功
2025-06-08 14:28:15,242 - openai_sdk_integration_test - INFO - 响应时间: 4.58秒
2025-06-08 14:28:15,242 - openai_sdk_integration_test - INFO - 测试2结果: 成功
2025-06-08 14:28:15,242 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:28:15,242 - openai_sdk_integration_test - INFO - ===================== 测试3: 直接使用OpenAI SDK (v1.x) =====================
2025-06-08 14:28:15,243 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:28:22,774 - httpx - INFO - HTTP Request: POST https://oneapi.xiongmaodaxia.online/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 响应内容: 集成测试3成功
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 响应时间: 7.53秒
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 测试3结果: 成功
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 
======================================================================
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - ============================== 集成测试结果摘要 ==============================
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - ======================================================================
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 总测试数量: 4
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 成功测试数: 4
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 失败测试数: 0
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 成功率: 100.00%
2025-06-08 14:28:22,776 - openai_sdk_integration_test - INFO - 测试结束时间: 2025-06-08 14:28:22
2025-06-08 14:28:22,777 - openai_sdk_integration_test - INFO - 测试结果已保存到: /Volumes/DiskPro/yanran_digital_life/integration_tests/sdk_integration_results.json
