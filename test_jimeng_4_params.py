#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王的即梦4.0接口参数测试脚本
尝试不同的参数格式，找出正确的调用方式
"""

import requests
import json
import time
from datetime import datetime

def test_different_param_formats():
    """测试不同的参数格式"""
    
    base_config = {
        "url": "http://124.221.30.195:47653/v1/images/generations",
        "session_id": "1e6aa5b4800b56a3e345bacacfaa7c09",
        "model": "jimeng-4.0"
    }
    
    prompt = "一只可爱的小猫，简单测试"
    
    # 测试不同的参数格式
    test_cases = [
        {
            "name": "格式1: 当前使用的格式",
            "data": {
                "model": base_config['model'],
                "prompt": prompt,
                "negativePrompt": "低质量",
                "width": 512,
                "height": 1024,
                "sample_strength": 0.8
            }
        },
        {
            "name": "格式2: 去掉negativePrompt",
            "data": {
                "model": base_config['model'],
                "prompt": prompt,
                "width": 512,
                "height": 1024,
                "sample_strength": 0.8
            }
        },
        {
            "name": "格式3: 使用negative_prompt",
            "data": {
                "model": base_config['model'],
                "prompt": prompt,
                "negative_prompt": "低质量",
                "width": 512,
                "height": 1024,
                "sample_strength": 0.8
            }
        },
        {
            "name": "格式4: 最简参数",
            "data": {
                "model": base_config['model'],
                "prompt": prompt
            }
        },
        {
            "name": "格式5: 添加n参数",
            "data": {
                "model": base_config['model'],
                "prompt": prompt,
                "n": 1,
                "width": 512,
                "height": 1024
            }
        },
        {
            "name": "格式6: 使用size参数",
            "data": {
                "model": base_config['model'],
                "prompt": prompt,
                "size": "512x1024"
            }
        }
    ]
    
    print("🔥 老王测试即梦4.0不同参数格式...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"🧪 测试 {i}/{len(test_cases)}: {test_case['name']}")
        print(f"📝 参数: {json.dumps(test_case['data'], indent=2, ensure_ascii=False)}")
        
        headers = {
            "Authorization": f"Bearer {base_config['session_id']}",
            "Content-Type": "application/json"
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                base_config['url'], 
                headers=headers, 
                json=test_case['data'], 
                timeout=60  # 短超时，快速测试
            )
            elapsed_time = time.time() - start_time
            
            print(f"📊 状态码: {response.status_code}, 耗时: {elapsed_time:.2f}秒")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📄 响应: {json.dumps(result, ensure_ascii=False)}")
                
                # 检查是否有数据
                if isinstance(result, dict) and 'data' in result:
                    data_list = result['data']
                    if data_list and len(data_list) > 0:
                        print(f"✅ 成功！找到 {len(data_list)} 个数据项")
                        if isinstance(data_list[0], dict) and 'url' in data_list[0]:
                            print(f"🖼️ 图片URL: {data_list[0]['url']}")
                        return test_case, result
                    else:
                        print("⚠️ data数组为空")
                else:
                    print("⚠️ 响应格式异常")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"错误内容: {response.text[:200]}")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        print("-" * 40)
        
        # 短暂延迟，避免请求过快
        time.sleep(2)
    
    print("❌ 所有格式测试完成，未找到有效格式")
    return None, None

def test_session_id_validity():
    """测试Session ID是否有效"""
    print("🔍 测试Session ID有效性...")
    
    # 尝试一个简单的请求来验证session_id
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 最简单的请求
    data = {
        "model": "jimeng-4.0",
        "prompt": "test"
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"📊 状态码: {response.status_code}")
        print(f"📄 响应: {response.text}")
        
        if response.status_code == 401:
            print("❌ Session ID无效或已过期")
            return False
        elif response.status_code == 200:
            result = response.json()
            if 'error' in result or ('data' in result and not result['data']):
                print("⚠️ Session ID可能有效，但请求参数可能有问题")
            return True
        else:
            print(f"⚠️ 未知状态: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

if __name__ == "__main__":
    print("🔥 老王的即梦4.0参数格式测试")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 先测试Session ID
    session_valid = test_session_id_validity()
    print("=" * 60)
    
    if session_valid is False:
        print("❌ Session ID无效，需要更新配置")
    else:
        # 测试不同参数格式
        success_case, result = test_different_param_formats()
        
        if success_case:
            print("🎉 找到有效的参数格式！")
            print(f"✅ 成功格式: {success_case['name']}")
            print(f"📋 参数: {json.dumps(success_case['data'], indent=2, ensure_ascii=False)}")
        else:
            print("❌ 未找到有效的参数格式，可能需要检查API文档或联系服务提供商")
    
    print("=" * 60)
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
