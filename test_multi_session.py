#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试多session随机选择和轮询机制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill
import time

def test_multi_session():
    """测试多session机制"""
    print("🔥 老王测试多session随机选择和轮询机制")
    print("=" * 70)
    
    try:
        # 初始化drawing skill
        drawing_skill = DrawingSkill()
        
        # 检查配置中的session_id
        jimeng_config = drawing_skill.config.get("jimeng", {})
        session_ids = jimeng_config.get("session_id", "").split(",")
        session_ids = [sid.strip() for sid in session_ids if sid.strip()]
        
        print(f"📋 配置中的session数量: {len(session_ids)}")
        for i, sid in enumerate(session_ids):
            print(f"   Session {i+1}: {sid[:20]}...")
        print("-" * 70)
        
        # 测试提示词
        test_prompt = "简单的花朵"
        
        print(f"🎯 测试提示词: {test_prompt}")
        print(f"🔄 将随机选择session并轮询...")
        print("-" * 70)
        
        start_time = time.time()
        
        # 直接调用jimeng_generate_images方法测试多session
        result = drawing_skill.jimeng_generate_images(
            prompt=test_prompt,
            model="jimeng-4.0",
            negative_prompt="低质量，模糊画面",
            width=1024,
            height=1024,
            sample_strength=0.8
        )
        
        elapsed_time = time.time() - start_time
        print(f"⏱️ 总耗时: {elapsed_time:.1f}秒")
        
        # 检查结果
        if isinstance(result, dict):
            if "image_urls" in result and result["image_urls"]:
                print("✅ 多session机制成功！")
                print(f"🖼️ 返回图片数量: {len(result['image_urls'])}")
                print(f"📅 创建时间: {result.get('created', 'N/A')}")
                print(f"🔗 第一张图片: {result['image_urls'][0]}")
            elif "error" in result:
                print(f"❌ 多session机制失败: {result['error']}")
                print(f"📋 详细信息: {result.get('details', 'N/A')}")
            else:
                print(f"⚠️ 未知返回格式: {result}")
        else:
            print(f"⚠️ 返回类型异常: {type(result)}")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_multi_session()
