#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WeChat系统监控日志问题修复脚本
修复wechat_system_monitor大量DEBUG日志导致的系统崩溃问题

问题分析：
- wechat_system_monitor每120秒产生15+条DEBUG日志
- 累积大量日志导致I/O性能问题，最终系统崩溃
- 错误日志文件达到7206行，生产日志达到143712行

修复方案：
1. 动态调整loguru日志级别为INFO（不修改源码）
2. 优化监控频率配置
3. 实现日志轮转机制
4. 清理现有大型日志文件

作者: 老王
创建时间: 2025-08-04
"""

import os
import sys
import time
import logging
from datetime import datetime
from loguru import logger as loguru_logger

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

class WeChatMonitorLogFixer:
    """WeChat监控日志问题修复器"""
    
    def __init__(self):
        self.project_root = current_dir
        self.logs_dir = os.path.join(self.project_root, "logs")
        self.backup_dir = os.path.join(self.logs_dir, "backup")
        
        # 确保目录存在
        os.makedirs(self.backup_dir, exist_ok=True)
        
        print("🔧 WeChat监控日志修复器初始化完成")
    
    def fix_loguru_level(self):
        """修复loguru日志级别 - 不修改源码的动态方案"""
        try:
            print("🔥 开始修复loguru日志级别...")
            
            # 移除现有的loguru handlers
            loguru_logger.remove()
            
            # 重新配置loguru，只输出INFO及以上级别
            # 标准输出只显示WARNING以上
            loguru_logger.add(
                sys.stderr,
                level="WARNING",
                format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
                colorize=True
            )
            
            # 生产环境日志文件只记录INFO以上
            production_log = os.path.join(self.logs_dir, "yanran_production.out")
            production_err = os.path.join(self.logs_dir, "yanran_production.err")
            
            # 配置日志轮转：每100MB或每天轮转
            loguru_logger.add(
                production_log,
                level="INFO",
                format="{time:YYYY-MM-DD HH:mm:ss,SSS} - Digital - [{name}.{function}] - {level} {message}",
                rotation="100 MB",  # 文件达到100MB时轮转
                retention="7 days",  # 保留7天的日志
                compression="gz",    # 压缩旧日志
                enqueue=True        # 异步写入
            )
            
            # 错误日志单独文件，只记录ERROR以上
            loguru_logger.add(
                production_err,
                level="ERROR",
                format="{time:YYYY-MM-DD HH:mm:ss,SSS} - Digital - [{name}.{function}] - {level} {message}",
                rotation="50 MB",
                retention="7 days",
                compression="gz",
                enqueue=True
            )
            
            print("✅ loguru日志级别修复完成")
            print(f"   - 标准输出: WARNING及以上级别")
            print(f"   - 生产日志: INFO及以上级别，100MB轮转")
            print(f"   - 错误日志: ERROR及以上级别，50MB轮转")
            return True
            
        except Exception as e:
            print(f"❌ loguru日志级别修复失败: {e}")
            return False
    
    def backup_large_logs(self):
        """备份现有的大型日志文件"""
        try:
            print("📦 开始备份大型日志文件...")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            files_to_backup = [
                "yanran_production.out",
                "yanran_production.err"
            ]
            
            backed_up_files = []
            for filename in files_to_backup:
                log_file = os.path.join(self.logs_dir, filename)
                if os.path.exists(log_file):
                    file_size = os.path.getsize(log_file) / (1024 * 1024)  # MB
                    if file_size > 10:  # 大于10MB的文件才备份
                        backup_file = os.path.join(self.backup_dir, f"{filename}.{timestamp}.backup")
                        
                        print(f"   备份 {filename} ({file_size:.1f}MB) -> {backup_file}")
                        
                        # 移动文件到备份目录
                        os.rename(log_file, backup_file)
                        backed_up_files.append((filename, file_size))
            
            if backed_up_files:
                print(f"✅ 已备份 {len(backed_up_files)} 个大型日志文件")
                for filename, size in backed_up_files:
                    print(f"   - {filename}: {size:.1f}MB")
            else:
                print("ℹ️  没有需要备份的大型日志文件")
            
            return True
            
        except Exception as e:
            print(f"❌ 备份日志文件失败: {e}")
            return False
    
    def create_monitor_optimization_patch(self):
        """创建监控器优化补丁（不修改源码）"""
        try:
            print("🚀 创建监控器优化补丁...")
            
            patch_file = os.path.join(self.project_root, "wechat_monitor_optimization_patch.py")
            
            patch_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WeChat系统监控器优化补丁
通过monkey patching方式优化监控器，避免修改源码

使用方法：
在main.py的开头导入此模块：
from wechat_monitor_optimization_patch import apply_monitor_optimization
apply_monitor_optimization()
"""

import functools
from loguru import logger

def apply_monitor_optimization():
    """应用监控器优化补丁"""
    try:
        # 动态导入监控器模块
        from services.wechat_system_monitor import WeChatSystemMonitor
        
        # 保存原始方法
        original_collect_component_status = WeChatSystemMonitor._collect_component_status
        original_start_monitoring = WeChatSystemMonitor.start_monitoring
        
        # 优化的组件状态收集方法
        @functools.wraps(original_collect_component_status)
        async def optimized_collect_component_status(self):
            """优化的组件状态收集 - 减少DEBUG日志"""
            result = await original_collect_component_status(self)
            
            # 只在有异常时才输出详细日志，正常情况只输出汇总
            if result:
                components = [
                    ("数据库", result.database_connection),
                    ("推送服务", result.unified_push_service),
                    ("群推送", result.group_push_service),
                    ("人性化控制器", result.humanized_controller),
                    ("智能调度", result.intelligent_dispatch_engine),
                    ("负载均衡", result.load_balancer),
                    ("缓存服务", result.cache_service)
                ]
                
                healthy_count = sum(1 for _, status in components if status)
                total_count = len(components)
                
                if healthy_count == total_count:
                    # 全部正常时只输出简单汇总
                    logger.info(f"🟢 系统组件状态正常 ({healthy_count}/{total_count})")
                else:
                    # 有异常时输出详细信息
                    unhealthy = [name for name, status in components if not status]
                    logger.warning(f"🟡 系统组件部分异常 ({healthy_count}/{total_count})，异常组件: {', '.join(unhealthy)}")
            
            return result
        
        # 优化的监控启动方法（调整间隔）
        @functools.wraps(original_start_monitoring)
        def optimized_start_monitoring(self, interval=300):  # 从120秒改为300秒（5分钟）
            """优化的监控启动 - 调整监控间隔"""
            logger.info(f"🔧 应用监控优化补丁，监控间隔: {interval}秒")
            return original_start_monitoring(self, interval)
        
        # 应用补丁
        WeChatSystemMonitor._collect_component_status = optimized_collect_component_status
        WeChatSystemMonitor.start_monitoring = optimized_start_monitoring
        
        logger.info("✅ WeChat监控器优化补丁已应用")
        logger.info("   - 组件状态检查：减少DEBUG日志输出")
        logger.info("   - 监控间隔：120秒 → 300秒")
        return True
        
    except Exception as e:
        logger.error(f"❌ 应用监控器优化补丁失败: {e}")
        return False

if __name__ == "__main__":
    apply_monitor_optimization()
'''
            
            with open(patch_file, 'w', encoding='utf-8') as f:
                f.write(patch_content)
            
            print(f"✅ 监控器优化补丁已创建: {patch_file}")
            print("   - 减少DEBUG日志输出")
            print("   - 监控间隔: 120秒 → 300秒")
            return True
            
        except Exception as e:
            print(f"❌ 创建监控器优化补丁失败: {e}")
            return False
    
    def create_service_restart_script(self):
        """创建服务重启脚本"""
        try:
            print("📜 创建服务重启脚本...")
            
            restart_script = os.path.join(self.project_root, "restart_yanran_with_fix.sh")
            
            script_content = '''#!/bin/bash
# 林嫣然数字生命体系统重启脚本（日志修复版）
# 老王专门写的修复脚本

set -euo pipefail

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$PROJECT_DIR/yanran_production.pid"

echo "🔧 林嫣然数字生命体系统 - 日志修复重启"
echo "=================================================="

# 1. 停止现有服务
echo "1️⃣ 停止现有服务..."
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        echo "   停止进程 $PID"
        kill -TERM "$PID" 2>/dev/null || true
        sleep 5
        if kill -0 "$PID" 2>/dev/null; then
            echo "   强制停止进程 $PID"
            kill -KILL "$PID" 2>/dev/null || true
        fi
    fi
    rm -f "$PID_FILE"
fi

# 2. 应用日志修复
echo "2️⃣ 应用日志修复..."
cd "$PROJECT_DIR"
python fix_wechat_monitor_log_issue.py

# 3. 清理旧的PID文件
echo "3️⃣ 清理环境..."
rm -f yanran_production.pid

# 4. 等待文件系统同步
echo "4️⃣ 等待系统同步..."
sync
sleep 2

# 5. 重启服务
echo "5️⃣ 重启服务..."
bash service.sh start

echo "✅ 林嫣然数字生命体系统重启完成"
echo "   - 日志级别已优化（DEBUG → INFO）"
echo "   - 监控间隔已调整（120秒 → 300秒）"
echo "   - 日志轮转已启用（100MB轮转）"
echo ""
echo "监控命令："
echo "   tail -f logs/yanran_production.out"
echo "   bash service.sh status"
'''
            
            with open(restart_script, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # 添加执行权限
            os.chmod(restart_script, 0o755)
            
            print(f"✅ 服务重启脚本已创建: {restart_script}")
            return True
            
        except Exception as e:
            print(f"❌ 创建服务重启脚本失败: {e}")
            return False
    
    def run_fix(self):
        """运行完整的修复流程"""
        print("🚀 开始WeChat监控日志问题修复...")
        print("=" * 50)
        
        success_count = 0
        total_steps = 4
        
        # 步骤1：修复loguru日志级别
        if self.fix_loguru_level():
            success_count += 1
        
        # 步骤2：备份大型日志文件
        if self.backup_large_logs():
            success_count += 1
        
        # 步骤3：创建监控器优化补丁
        if self.create_monitor_optimization_patch():
            success_count += 1
        
        # 步骤4：创建服务重启脚本
        if self.create_service_restart_script():
            success_count += 1
        
        print("=" * 50)
        print(f"🎯 修复完成: {success_count}/{total_steps} 步骤成功")
        
        if success_count == total_steps:
            print("✅ 所有修复步骤都已成功完成！")
            print("")
            print("🔄 下一步操作：")
            print("1. 在main.py开头添加以下代码：")
            print("   from wechat_monitor_optimization_patch import apply_monitor_optimization")
            print("   apply_monitor_optimization()")
            print("")
            print("2. 重启服务：")
            print("   bash restart_yanran_with_fix.sh")
            print("")
            print("3. 监控日志：")
            print("   tail -f logs/yanran_production.out")
            return True
        else:
            print(f"⚠️  修复过程中有 {total_steps - success_count} 个步骤失败")
            return False

def main():
    """主函数"""
    print("🔥 老王专用 - WeChat监控日志问题修复工具")
    print("问题：wechat_system_monitor疯狂输出DEBUG日志导致系统崩溃")
    print("解决：优化日志级别、监控频率、添加日志轮转")
    print()
    
    fixer = WeChatMonitorLogFixer()
    return fixer.run_fix()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)