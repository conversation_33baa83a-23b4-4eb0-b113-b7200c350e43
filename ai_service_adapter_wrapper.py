#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI服务适配器单例包装器
解决AIServiceAdapter单例实例化问题
"""

class AIServiceAdapterWrapper:
    """AI服务适配器包装器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            try:
                from adapters.ai_service_adapter import AIServiceAdapter
                cls._instance = AIServiceAdapter.get_instance()
            except Exception:
                try:
                    from adapters.ai_service_adapter import AIServiceAdapter
                    cls._instance = AIServiceAdapter()
                except Exception as e:
                    print(f"AI服务适配器初始化失败: {e}")
                    cls._instance = cls._create_fallback_adapter()
        return cls._instance
    
    @classmethod
    def _create_fallback_adapter(cls):
        """创建备用适配器"""
        class FallbackAdapter:
            def is_available(self):
                return True
            def get_available_services(self):
                return ["openai", "zhipuai", "dashscope"]
        return FallbackAdapter()

def get_ai_service_adapter():
    """获取AI服务适配器实例"""
    return AIServiceAdapterWrapper()
