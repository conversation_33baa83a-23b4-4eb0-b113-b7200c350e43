#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王的AI响应格式全面测试
测试项目中所有AI调用是否都能正确处理统一的响应格式
"""

import asyncio
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utilities.logger import get_unified_logger

logger = get_unified_logger("test_ai_format_comprehensive")

async def test_scripts_integration_ai_call():
    """测试Scripts集成服务的AI调用"""
    try:
        logger.info("🔥 测试Scripts集成服务AI调用...")
        
        from services.scripts_integration_service import ScriptsIntegrationService
        
        # 初始化服务
        service = ScriptsIntegrationService()
        
        # 模拟一个活动结果
        mock_activity_result = {
            'activity_script': '今天天气很好，我在公园里散步，看到了很多美丽的花朵，心情特别愉悦。',
            'activity_title': '公园散步',
            'reality_score': 0.85
        }
        
        # 模拟基础评估结果
        mock_basic_evaluation = {
            'basic_score': 0.7,
            'content_quality': 0.8,
            'personal_meaning': 0.6,
            'social_value': 0.7
        }
        
        start_time = time.time()
        
        # 测试AI分享决策
        ai_decision = await service._ai_sharing_decision(mock_activity_result, mock_basic_evaluation)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"📊 Scripts AI调用完成，耗时: {execution_time:.2f}秒")
        
        if ai_decision and isinstance(ai_decision, dict):
            ai_should_share = ai_decision.get('ai_should_share', False)
            ai_confidence = ai_decision.get('ai_confidence', 0.0)
            ai_reasoning = ai_decision.get('ai_reasoning', '')
            
            logger.success(f"✅ Scripts AI调用成功:")
            logger.success(f"   - 是否分享: {ai_should_share}")
            logger.success(f"   - 置信度: {ai_confidence:.2f}")
            logger.success(f"   - 推理: {ai_reasoning[:100]}...")
            return True
        else:
            logger.warning(f"⚠️ Scripts AI调用返回格式异常: {type(ai_decision)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Scripts AI调用测试失败: {e}")
        import traceback
        logger.debug(f"错误堆栈: {traceback.format_exc()}")
        return False

async def test_yanran_decision_engine_ai_call():
    """测试燕然决策引擎的AI调用"""
    try:
        logger.info("🔥 测试燕然决策引擎AI调用...")
        
        from cognitive_modules.decision.yanran_response_decision import YanranResponseDecision
        
        # 初始化决策引擎
        decision_engine = YanranResponseDecision()
        
        start_time = time.time()
        
        # 测试AI决策 - 🔥 老王修复：提供正确的参数
        ai_decision = await decision_engine._get_ai_decision(
            message="你好，今天天气怎么样？",
            current_state={"mood": "平静", "energy": 0.8},
            current_script="正在家里休息",
            user_relationship={"intimacy": 0.5, "interaction_frequency": 0.3}
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"📊 决策引擎AI调用完成，耗时: {execution_time:.2f}秒")
        
        if ai_decision and isinstance(ai_decision, dict):
            should_reply = ai_decision.get('should_reply', False)
            reason = ai_decision.get('reason', '')
            delay_hours = ai_decision.get('delay_hours', 0)
            
            logger.success(f"✅ 决策引擎AI调用成功:")
            logger.success(f"   - 是否回复: {should_reply}")
            logger.success(f"   - 理由: {reason}")
            logger.success(f"   - 延迟小时: {delay_hours}")
            return True
        else:
            logger.warning(f"⚠️ 决策引擎AI调用返回格式异常: {type(ai_decision)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 决策引擎AI调用测试失败: {e}")
        import traceback
        logger.debug(f"错误堆栈: {traceback.format_exc()}")
        return False

async def test_ai_adapter_direct_call():
    """测试AI适配器直接调用"""
    try:
        logger.info("🔥 测试AI适配器直接调用...")
        
        from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
        
        ai_adapter = get_ai_service_adapter()
        
        start_time = time.time()
        
        # 测试异步调用
        response = await ai_adapter.get_completion_async(
            messages=[{"role": "user", "content": "请简单回答：什么是人工智能？"}],
            model="MiniMax-M1",
            temperature=0.7,
            max_tokens=200
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"📊 AI适配器直接调用完成，耗时: {execution_time:.2f}秒")
        
        if response and isinstance(response, dict):
            # 🔥 老王修复：处理原始API响应格式
            if "error" in response:
                error_info = response.get('error', '未知错误')
                logger.warning(f"⚠️ AI适配器调用失败: {error_info}")
                return False
            elif "choices" in response and response["choices"]:
                choice = response["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    content = choice["message"]["content"]
                    model = response.get('model', 'unknown')

                    logger.success(f"✅ AI适配器直接调用成功:")
                    logger.success(f"   - 模型: {model}")
                    logger.success(f"   - 内容: {content[:100]}...")
                    return True
                else:
                    logger.warning(f"⚠️ AI响应格式异常，choice结构: {choice}")
                    return False
            else:
                logger.warning(f"⚠️ AI响应格式异常，缺少choices字段: {list(response.keys())}")
                return False
        else:
            logger.warning(f"⚠️ AI适配器响应格式异常: {type(response)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ AI适配器直接调用测试失败: {e}")
        import traceback
        logger.debug(f"错误堆栈: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("🔥 老王的AI响应格式全面测试开始...")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 测试1：AI适配器直接调用
    logger.info("测试1：AI适配器直接调用")
    test_results['ai_adapter_direct'] = await test_ai_adapter_direct_call()
    
    logger.info("=" * 60)
    
    # 测试2：Scripts集成服务AI调用
    logger.info("测试2：Scripts集成服务AI调用")
    test_results['scripts_integration'] = await test_scripts_integration_ai_call()
    
    logger.info("=" * 60)
    
    # 测试3：燕然决策引擎AI调用
    logger.info("测试3：燕然决策引擎AI调用")
    test_results['yanran_decision'] = await test_yanran_decision_engine_ai_call()
    
    logger.info("=" * 60)
    
    # 汇总结果
    logger.info("🔥 测试结果汇总:")
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    logger.info(f"📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        logger.success("🎉 所有AI响应格式测试通过！AI适配器统一格式修复成功！")
    else:
        logger.warning(f"⚠️ 还有 {total_tests - passed_tests} 个测试失败，需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
