#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优雅关闭功能测试

验证系统是否能够正确处理Ctrl+C中断，优雅关闭所有服务。

作者: <PERSON>
创建日期: 2025-06-30
版本: 1.0
"""

import asyncio
import signal
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utilities.unified_logger import get_unified_logger, setup_unified_logging
from utilities.singleton_manager import get_singleton_manager

# 配置日志
setup_unified_logging()
logger = get_unified_logger("graceful_shutdown_test")

class MockAsyncService:
    """模拟异步服务"""
    
    def __init__(self, name: str):
        self.name = name
        self.running = False
        self.background_task = None
    
    async def start(self):
        """启动服务"""
        self.running = True
        self.background_task = asyncio.create_task(self._background_work())
        logger.info(f"✅ 模拟服务 {self.name} 已启动")
    
    async def _background_work(self):
        """后台工作"""
        try:
            while self.running:
                logger.debug(f"服务 {self.name} 正在工作...")
                await asyncio.sleep(2)
        except asyncio.CancelledError:
            logger.info(f"服务 {self.name} 后台任务已取消")
            raise
    
    async def shutdown(self):
        """异步关闭"""
        logger.info(f"开始关闭服务 {self.name}...")
        self.running = False
        
        if self.background_task:
            self.background_task.cancel()
            try:
                await self.background_task
            except asyncio.CancelledError:
                pass
        
        # 模拟关闭时间
        await asyncio.sleep(0.5)
        logger.info(f"✅ 服务 {self.name} 已优雅关闭")

class MockSyncService:
    """模拟同步服务"""
    
    def __init__(self, name: str):
        self.name = name
        self.running = False
    
    def start(self):
        """启动服务"""
        self.running = True
        logger.info(f"✅ 模拟同步服务 {self.name} 已启动")
    
    def shutdown(self):
        """同步关闭"""
        logger.info(f"开始关闭同步服务 {self.name}...")
        self.running = False
        time.sleep(0.2)  # 模拟关闭时间
        logger.info(f"✅ 同步服务 {self.name} 已关闭")

async def test_graceful_shutdown():
    """测试优雅关闭功能"""
    logger.info("🚀 开始测试优雅关闭功能")
    
    # 获取单例管理器
    singleton_manager = get_singleton_manager()
    
    # 创建并注册模拟服务
    async_service1 = MockAsyncService("AsyncService1")
    async_service2 = MockAsyncService("AsyncService2")
    sync_service1 = MockSyncService("SyncService1")
    sync_service2 = MockSyncService("SyncService2")
    
    # 注册到单例管理器
    singleton_manager.register("async_service_1", async_service1)
    singleton_manager.register("async_service_2", async_service2)
    singleton_manager.register("sync_service_1", sync_service1)
    singleton_manager.register("sync_service_2", sync_service2)
    
    # 启动服务
    await async_service1.start()
    await async_service2.start()
    sync_service1.start()
    sync_service2.start()
    
    logger.info("所有服务已启动，等待中断信号...")
    logger.info("请按 Ctrl+C 测试优雅关闭功能")
    
    # 设置信号处理器
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，开始优雅关闭...")
        # 不在这里处理，让主循环处理
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 保持运行直到收到中断信号
        while True:
            await asyncio.sleep(1)
            logger.debug("主循环运行中...")
            
    except KeyboardInterrupt:
        logger.info("🛑 收到键盘中断，开始优雅关闭...")
        
        # 测试优化后的shutdown_all方法
        from utilities.singleton_manager import shutdown_all
        
        start_time = time.time()
        shutdown_results = shutdown_all()
        end_time = time.time()
        
        logger.info(f"关闭完成，耗时: {end_time - start_time:.2f}秒")
        logger.info(f"关闭结果: {shutdown_results}")
        
        # 验证结果
        success_count = sum(1 for result in shutdown_results.values() if result)
        total_count = len(shutdown_results)
        
        if success_count == total_count:
            logger.success(f"✅ 优雅关闭测试成功！{success_count}/{total_count} 个服务正确关闭")
        else:
            logger.error_status(f"❌ 优雅关闭测试失败！只有 {success_count}/{total_count} 个服务正确关闭")
        
        return success_count == total_count

async def main():
    """主函数"""
    try:
        success = await test_graceful_shutdown()
        if success:
            logger.success("🎉 优雅关闭功能测试通过！")
            return 0
        else:
            logger.error_status("💥 优雅关闭功能测试失败！")
            return 1
    except Exception as e:
        logger.error_status(f"测试过程中发生异常: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(0) 