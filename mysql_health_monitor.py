#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL健康监控和自动修复脚本
专门监控Malformed packet错误并自动修复
"""

import time
import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.append('.')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mysql_health_monitor.log'),
        logging.StreamHandler()
    ]
)

class MySQLHealthMonitor:
    def __init__(self):
        self.error_count = 0
        self.last_error_time = None
        self.recovery_count = 0
        
    def check_mysql_health(self):
        """检查MySQL健康状态"""
        try:
            from connectors.database.mysql_connector import MySQLConnector
            
            mysql = MySQLConnector()
            if not mysql.is_available():
                logging.error("MySQL连接器不可用")
                return False
            
            # 执行健康检查查询
            success, result, error = mysql.query("SELECT 1 as health_check, NOW() as check_time")
            
            if success:
                logging.info(f"MySQL健康检查成功: {result}")
                self.error_count = 0  # 重置错误计数
                return True
            else:
                logging.error(f"MySQL健康检查失败: {error}")
                
                # 检查是否是Malformed packet错误
                if "Malformed packet" in str(error):
                    self.handle_malformed_packet_error(mysql)
                
                self.error_count += 1
                self.last_error_time = datetime.now()
                return False
                
        except Exception as e:
            logging.error(f"健康检查异常: {e}")
            self.error_count += 1
            return False
    
    def handle_malformed_packet_error(self, mysql):
        """处理Malformed packet错误"""
        logging.warning("检测到Malformed packet错误，开始自动修复...")
        
        try:
            # 强制关闭当前连接
            mysql.close()
            
            # 等待一段时间
            time.sleep(10)
            
            # 重新初始化
            mysql._initialize_pool()
            
            self.recovery_count += 1
            logging.info(f"Malformed packet错误自动修复完成 (第{self.recovery_count}次)")
            
        except Exception as e:
            logging.error(f"自动修复失败: {e}")
    
    def run_monitor(self, interval=30):
        """运行监控循环"""
        logging.info("MySQL健康监控启动")
        
        while True:
            try:
                health_status = self.check_mysql_health()
                
                if not health_status:
                    logging.warning(f"MySQL健康检查失败，错误计数: {self.error_count}")
                
                # 如果连续错误太多，发出警告
                if self.error_count >= 5:
                    logging.critical(f"MySQL连续错误{self.error_count}次，需要人工介入！")
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                logging.info("监控已停止")
                break
            except Exception as e:
                logging.error(f"监控循环异常: {e}")
                time.sleep(interval)

if __name__ == "__main__":
    monitor = MySQLHealthMonitor()
    monitor.run_monitor()
