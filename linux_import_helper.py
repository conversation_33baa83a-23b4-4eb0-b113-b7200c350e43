#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linux导入辅助脚本
香草为Linux服务器创建的导入辅助 💕
"""

import sys
import os

# 获取项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

def setup_python_path():
    """设置Python路径"""
    # 确保项目根目录在Python路径第一位
    if PROJECT_ROOT not in sys.path:
        sys.path.insert(0, PROJECT_ROOT)
    
    # 确保services目录也在路径中
    services_dir = os.path.join(PROJECT_ROOT, "services")
    if services_dir not in sys.path:
        sys.path.insert(0, services_dir)

def clear_module_cache():
    """清理模块缓存"""
    modules_to_clear = [
        'services',
        'services.scripts_integration_service',
        'scripts_integration_service'
    ]
    for module in modules_to_clear:
        if module in sys.modules:
            del sys.modules[module]

def safe_import_scripts_service():
    """安全导入scripts服务"""
    try:
        setup_python_path()
        clear_module_cache()
        
        from services.scripts_integration_service import get_scripts_integration_service
        return get_scripts_integration_service
    except Exception as e:
        print(f"导入失败: {e}")
        return None

# 自动执行路径设置
setup_python_path()
