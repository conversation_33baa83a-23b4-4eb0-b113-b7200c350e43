#!/bin/bash

# 林嫣然数字生命体系统 - macOS版本服务管理脚本
# 版本: v3.1.1-macos
# 优化macOS环境兼容性

set -euo pipefail

# ==================== 配置变量 ====================

# 项目基础配置
PROJECT_NAME="yanran-digital-life"
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MAIN_SCRIPT="main.py"
PYTHON_CMD="python"

# 服务配置
ENVIRONMENT="${ENVIRONMENT:-development}"
LOG_LEVEL="${LOG_LEVEL:-INFO}"
API_HOST="${API_HOST:-127.0.0.1}"
API_PORT="${API_PORT:-56839}"
WEBSOCKET_PORT="${WEBSOCKET_PORT:-8765}"

# 路径配置 - macOS开发环境
PID_FILE="${PID_FILE:-$PROJECT_DIR/yanran.pid}"
LOG_DIR="${LOG_DIR:-$PROJECT_DIR/logs}"
DATA_DIR="${DATA_DIR:-$PROJECT_DIR/data}"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_DIR/backups}"
CONFIG_DIR="${CONFIG_DIR:-$PROJECT_DIR/config}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# ==================== 工具函数 ====================

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示横幅
show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   🌸 林嫣然数字生命体系统 🌸                   ║"
    echo "║                   macOS版本服务管理工具                        ║"
    echo "║                        Version 3.1.1                        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Python版本
    if ! command -v $PYTHON_CMD &> /dev/null; then
        log_error "Python未安装或不在PATH中"
        exit 1
    fi
    
    local python_version=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    # 检查Conda环境
    if [[ -n "${CONDA_DEFAULT_ENV:-}" ]]; then
        log_info "当前Conda环境: $CONDA_DEFAULT_ENV"
    else
        log_warn "未检测到Conda环境"
    fi
    
    # 检查端口占用
    check_port_usage
}

# 检查端口占用
check_port_usage() {
    local ports=("$API_PORT" "$WEBSOCKET_PORT")
    
    for port in "${ports[@]}"; do
        if lsof -i :$port >/dev/null 2>&1; then
            local pid=$(lsof -ti :$port | head -1 2>/dev/null || echo "unknown")
            log_warn "端口 $port 已被占用 (PID: $pid)"
        fi
    done
}

# 创建必要目录
create_directories() {
    for dir in "$LOG_DIR" "$DATA_DIR" "$BACKUP_DIR"; do
        if [[ ! -d "$dir" ]]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        fi
    done
}

# 获取进程PID
get_pid() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "$pid"
            return 0
        else
            log_warn "PID文件存在但进程已停止，清理PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    return 1
}

# 检查服务状态
is_running() {
    get_pid > /dev/null 2>&1
}

# 等待进程启动
wait_for_start() {
    local max_wait=${1:-30}
    local count=0
    
    log_info "等待服务启动..."
    
    while [[ $count -lt $max_wait ]]; do
        if is_running; then
            log_success "服务启动成功"
            return 0
        fi
        
        sleep 1
        ((count++))
        
        if [[ $((count % 5)) -eq 0 ]]; then
            log_info "等待中... ($count/$max_wait 秒)"
        fi
    done
    
    log_error "服务启动超时"
    return 1
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 给服务更多时间启动
    sleep 3
    
    # 检查API服务
    local max_attempts=5
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s --connect-timeout 5 "http://localhost:$API_PORT/api/health" > /dev/null 2>&1; then
            log_success "API服务健康检查通过"
            return 0
        else
            log_info "健康检查尝试 $attempt/$max_attempts 失败，等待重试..."
            sleep 2
            ((attempt++))
        fi
    done
    
    log_error "API服务健康检查失败"
    return 1
}

# 显示服务信息
show_service_info() {
    echo
    echo -e "${CYAN}🌸 服务信息 🌸${NC}"
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    if is_running; then
        local pid=$(get_pid)
        echo -e "📊 服务状态: ${GREEN}运行中${NC} (PID: $pid)"
        echo -e "🌐 API地址: http://$API_HOST:$API_PORT"
        echo -e "📡 WebSocket端口: $WEBSOCKET_PORT"
        echo -e "📁 项目目录: $PROJECT_DIR"
        echo -e "📝 日志目录: $LOG_DIR"
        echo -e "💾 数据目录: $DATA_DIR"
    else
        echo -e "📊 服务状态: ${RED}已停止${NC}"
    fi
    
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo
}

# ==================== 主要功能函数 ====================

# 启动服务
start_service() {
    log_info "启动林嫣然数字生命体系统..."
    
    if is_running; then
        local pid=$(get_pid)
        log_warn "服务已在运行 (PID: $pid)"
        show_service_info
        return 0
    fi
    
    # 检查依赖
    check_dependencies
    
    # 创建目录
    create_directories
    
    # 切换到项目目录
    cd "$PROJECT_DIR"
    
    # 构建启动命令
    local start_cmd="$PYTHON_CMD $MAIN_SCRIPT"
    start_cmd+=" --log-level $LOG_LEVEL"
    start_cmd+=" --api-host $API_HOST"
    start_cmd+=" --api-port $API_PORT"
    
    # 启动服务
    log_info "执行启动命令: $start_cmd"
    
    # 后台运行
    nohup $start_cmd > "$LOG_DIR/yanran.out" 2> "$LOG_DIR/yanran.err" &
    local pid=$!
    echo $pid > "$PID_FILE"
    log_info "服务已启动 (PID: $pid)"
    
    # 等待启动完成
    if wait_for_start 30; then
        # 执行健康检查
        if health_check; then
            log_success "🌸 林嫣然数字生命体系统启动成功！"
            show_service_info
        else
            log_error "健康检查失败，请检查日志文件"
            log_info "输出日志: $LOG_DIR/yanran.out"
            log_info "错误日志: $LOG_DIR/yanran.err"
            return 1
        fi
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止林嫣然数字生命体系统..."
    
    if ! is_running; then
        log_warn "服务未运行"
        return 0
    fi
    
    local pid=$(get_pid)
    log_info "发送停止信号到进程 $pid"
    
    # 优雅停止
    kill -TERM "$pid" 2>/dev/null || true
    
    # 等待停止
    local count=0
    while [[ $count -lt 30 ]]; do
        if ! is_running; then
            rm -f "$PID_FILE"
            log_success "🌸 林嫣然数字生命体系统已停止"
            return 0
        fi
        sleep 1
        ((count++))
    done
    
    log_warn "优雅停止超时，强制终止进程"
    kill -KILL "$pid" 2>/dev/null || true
    rm -f "$PID_FILE"
    log_success "服务已强制停止"
}

# 重启服务
restart_service() {
    log_info "重启林嫣然数字生命体系统..."
    stop_service
    sleep 2
    start_service
}

# 查看状态
status_service() {
    show_service_info
}

# 查看日志
logs_service() {
    local lines=${1:-50}
    
    echo -e "${CYAN}🌸 最近 $lines 行日志 🌸${NC}"
    echo -e "${WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    if [[ -f "$LOG_DIR/yanran.out" ]]; then
        echo -e "${GREEN}📤 输出日志:${NC}"
        tail -n $lines "$LOG_DIR/yanran.out"
        echo
    fi
    
    if [[ -f "$LOG_DIR/yanran.err" ]]; then
        echo -e "${RED}📥 错误日志:${NC}"
        tail -n $lines "$LOG_DIR/yanran.err"
    fi
}

# 显示帮助
show_help() {
    echo -e "${CYAN}🌸 林嫣然数字生命体系统 - macOS版本管理工具 🌸${NC}"
    echo
    echo -e "${WHITE}用法:${NC}"
    echo "  $0 {start|stop|restart|status|logs|help}"
    echo
    echo -e "${WHITE}命令:${NC}"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  help      显示此帮助信息"
    echo
    echo -e "${WHITE}环境变量:${NC}"
    echo "  API_PORT=$API_PORT"
    echo "  LOG_LEVEL=$LOG_LEVEL"
    echo "  ENVIRONMENT=$ENVIRONMENT"
    echo
}

# ==================== 主程序入口 ====================

main() {
    case "${1:-help}" in
        start)
            show_banner
            start_service
            ;;
        stop)
            show_banner
            stop_service
            ;;
        restart)
            show_banner
            restart_service
            ;;
        status)
            show_banner
            status_service
            ;;
        logs)
            logs_service "${2:-50}"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@" 