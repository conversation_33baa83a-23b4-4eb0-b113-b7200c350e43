#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王的即梦4.0接口测试脚本
测试实际返回格式，生成图片：2077年的纽约时代广场
"""

import requests
import json
import time
from datetime import datetime

def test_jimeng_4_api():
    """测试即梦4.0接口实际返回格式"""
    
    # 从配置文件读取参数
    config = {
        "url": "http://124.221.30.195:47653/v1/images/generations",
        "session_id": "1e6aa5b4800b56a3e345bacacfaa7c09",
        "model": "jimeng-4.0",
        "negative_prompt": "低质量，模糊画面，混乱布局，扭曲的场景，畸形的肢体，不协调的颜色，不真实"
    }
    
    # 测试提示词
    prompt = "2077年的纽约时代广场，未来科技感，霓虹灯闪烁，飞行汽车穿梭，高楼大厦林立，赛博朋克风格，高质量，超清晰"
    
    print("🔥 老王测试即梦4.0接口开始...")
    print(f"📝 测试提示词: {prompt}")
    print(f"🔗 API地址: {config['url']}")
    print(f"🔑 Session ID: {config['session_id'][:20]}...")
    print("=" * 60)
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {config['session_id']}",
        "Content-Type": "application/json"
    }
    
    # 准备请求体
    data = {
        "model": config['model'],
        "prompt": prompt,
        "negativePrompt": config['negative_prompt'],
        "width": 512,
        "height": 1024,
        "sample_strength": 0.8
    }
    
    print("📤 发送请求...")
    print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
    print(f"请求体: {json.dumps(data, indent=2, ensure_ascii=False)}")
    print("=" * 60)
    
    try:
        start_time = time.time()
        
        # 发送POST请求
        response = requests.post(
            config['url'], 
            headers=headers, 
            json=data, 
            timeout=600  # 10分钟超时
        )
        
        elapsed_time = time.time() - start_time
        
        print(f"📥 响应接收完成，耗时: {elapsed_time:.2f}秒")
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        print("=" * 60)
        
        # 打印完整响应内容
        response_text = response.text
        print(f"📄 完整响应内容:")
        print(response_text)
        print("=" * 60)
        
        # 尝试解析JSON
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ JSON解析成功!")
                print(f"📋 解析后的数据结构:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                print("=" * 60)
                
                # 分析数据结构
                print("🔍 数据结构分析:")
                print(f"- 根级别键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                
                if isinstance(result, dict):
                    for key, value in result.items():
                        print(f"- {key}: {type(value)} = {value}")
                        if isinstance(value, list):
                            print(f"  - 数组长度: {len(value)}")
                            if value:
                                print(f"  - 第一个元素: {value[0]}")
                                if isinstance(value[0], dict):
                                    print(f"  - 第一个元素的键: {list(value[0].keys())}")
                
                print("=" * 60)
                
                # 检查是否有图片URL
                if 'data' in result:
                    data_list = result['data']
                    if data_list and isinstance(data_list, list):
                        print(f"✅ 找到 {len(data_list)} 个数据项")
                        for i, item in enumerate(data_list):
                            print(f"  数据项 {i+1}: {item}")
                            if isinstance(item, dict) and 'url' in item:
                                print(f"  图片URL: {item['url']}")
                    else:
                        print("⚠️ data字段为空或不是数组")
                else:
                    print("⚠️ 响应中没有data字段")
                
                return result
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response_text[:500]}...")
                return None
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"错误响应: {response_text}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时（10分钟）")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None
    except Exception as e:
        print(f"❌ 未知异常: {e}")
        return None

if __name__ == "__main__":
    print("🔥 老王的即梦4.0接口测试")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("⚠️ 注意：图片生成预计需要10分钟左右，请耐心等待...")
    print("=" * 60)
    
    result = test_jimeng_4_api()
    
    print("=" * 60)
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if result:
        print("🎉 测试完成！获得了即梦4.0的实际返回格式")
    else:
        print("❌ 测试失败，未能获取有效响应")
