#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王的即梦4.0修复测试脚本
测试修复后的drawing_skill是否能通过jimeng-free-api正确生成图片
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, PROJECT_ROOT)

def test_jimeng_free_api_fix():
    """测试修复后的即梦4.0调用"""
    
    print("🔥 老王测试即梦4.0修复效果...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 导入绘画技能
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        print("✅ 成功导入DrawingSkill")
        
        # 初始化绘画技能
        drawing_skill = DrawingSkill()
        print("✅ 成功初始化DrawingSkill")
        
        # 测试提示词 - 使用简单内容避免内容过滤
        test_prompt = "一只可爱的小猫咪，坐在花园里，阳光明媚，卡通风格"
        
        print(f"📝 测试提示词: {test_prompt}")
        print("🎨 开始即梦4.0测试...")
        print("⚠️ 注意：jimeng-free-api内部会轮询，可能需要10-15分钟...")
        print("-" * 40)
        
        start_time = time.time()
        
        # 直接调用即梦方法
        result = drawing_skill.jimeng_generate_images(
            prompt=test_prompt,
            model="jimeng-4.0",
            negative_prompt="低质量，模糊，扭曲",
            width=512,
            height=1024,
            sample_strength=0.8,
            session_id=drawing_skill.jimeng_session_id
        )
        
        elapsed_time = time.time() - start_time
        
        print("🎯 即梦4.0调用完成！")
        print(f"⏱️ 总耗时: {elapsed_time:.2f}秒 ({elapsed_time/60:.1f}分钟)")
        print("=" * 60)
        
        # 分析结果
        if result:
            print("📊 调用结果分析:")
            print(f"结果类型: {type(result)}")
            
            if isinstance(result, dict):
                print("📋 结果详情:")
                for key, value in result.items():
                    if key == "image_urls" and isinstance(value, list):
                        print(f"  {key}: {len(value)} 张图片")
                        for i, url in enumerate(value):
                            print(f"    图片 {i+1}: {url}")
                    elif key == "details" and isinstance(value, dict):
                        print(f"  {key}:")
                        for sub_key, sub_value in value.items():
                            if isinstance(sub_value, list):
                                print(f"    {sub_key}: {len(sub_value)} 项")
                                for item in sub_value:
                                    print(f"      - {item}")
                            else:
                                print(f"    {sub_key}: {sub_value}")
                    else:
                        print(f"  {key}: {value}")
                
                # 检查是否成功
                if "image_urls" in result and result["image_urls"]:
                    print("🎉 即梦4.0修复成功！")
                    print(f"✅ 成功生成 {len(result['image_urls'])} 张图片")
                    print("🔧 jimeng-free-api轮询机制工作正常")
                    return True
                elif "error" in result:
                    print("❌ 即梦4.0生成失败")
                    print(f"📝 失败原因: {result['error']}")
                    
                    # 分析失败原因
                    details = result.get("details", {})
                    if isinstance(details, dict):
                        if "possible_causes" in details:
                            print("🔍 可能原因:")
                            for cause in details["possible_causes"]:
                                print(f"   - {cause}")
                        
                        if "suggestions" in details:
                            print("💡 建议解决方案:")
                            for suggestion in details["suggestions"]:
                                print(f"   - {suggestion}")
                    
                    return False
                else:
                    print("⚠️ 结果格式异常，无法判断成功或失败")
                    return False
            else:
                print(f"⚠️ 结果格式异常: {result}")
                return False
        else:
            print("❌ 调用结果为空")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_drawing_skill():
    """测试完整的绘画技能流程"""
    
    print("🧪 测试完整绘画技能流程...")
    
    try:
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        drawing_skill = DrawingSkill()
        
        # 测试完整的execute方法
        result = drawing_skill.execute(
            input_text="画一只可爱的小猫咪，坐在花园里",
            user_id="test_user",
            session_id="test_session"
        )
        
        print("📊 完整流程测试结果:")
        if result and isinstance(result, dict):
            success = result.get("success", False)
            message = result.get("message", "")
            
            if success:
                print("🎉 完整流程测试成功！")
                result_data = result.get("result", {})
                if isinstance(result_data, dict):
                    service = result_data.get("service", "")
                    image_url = result_data.get("image_url", "")
                    print(f"🔧 使用服务: {service}")
                    if image_url:
                        print(f"🖼️ 图片URL: {image_url}")
                return True
            else:
                print(f"❌ 完整流程测试失败: {message}")
                return False
        else:
            print("❌ 完整流程测试无结果")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔥 老王的即梦4.0修复测试")
    print("=" * 60)
    
    # 测试1: 直接测试即梦4.0方法
    print("🧪 测试1: 直接测试即梦4.0方法")
    success1 = test_jimeng_free_api_fix()
    
    print("\n" + "=" * 60)
    
    # 测试2: 测试完整绘画技能流程
    print("🧪 测试2: 测试完整绘画技能流程")
    success2 = test_complete_drawing_skill()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    
    if success1:
        print("✅ 即梦4.0直接调用: 成功")
    else:
        print("❌ 即梦4.0直接调用: 失败")
    
    if success2:
        print("✅ 完整绘画流程: 成功")
    else:
        print("❌ 完整绘画流程: 失败")
    
    if success1 or success2:
        print("🎉 修复效果: 至少部分成功")
    else:
        print("❌ 修复效果: 完全失败")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔥 老王修复测试完成！")
