#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建统一的技能配置文件

该脚本将所有技能配置整合到一个文件中
"""

import json
import os

# 设置配置目录
config_dir = "config"
skills_dir = os.path.join(config_dir, "skills")
output_file = os.path.join(config_dir, "skills_unified.json")

# 创建统一配置
unified_config = {
    "skill_manager": {},
    "chat_skill": {},
    "drawing_skill": {},
    "search_skill": {},
    "music_skill": {
        "enabled": True,
        "api_key": "",
        "max_memory_size": 2,
        "default_settings": {
            "format": "mp3",
            "quality": "high"
        }
    },
    "global_skill_settings": {
        "cache_enabled": True,
        "cache_ttl": 3600,
        "default_confidence_threshold": 0.6,
        "log_level": "INFO",
        "max_retry_attempts": 3,
        "timeout": 30
    }
}

# 读取各个技能配置
try:
    # 技能管理器配置
    with open(os.path.join(skills_dir, "skill_manager.json"), 'r', encoding='utf-8') as f:
        unified_config["skill_manager"] = json.load(f)
    print("已加载技能管理器配置")
        
    # 聊天技能配置
    with open(os.path.join(skills_dir, "chat_skill.json"), 'r', encoding='utf-8') as f:
        unified_config["chat_skill"] = json.load(f)
    print("已加载聊天技能配置")
        
    # 绘画技能配置
    with open(os.path.join(skills_dir, "drawing_skill.json"), 'r', encoding='utf-8') as f:
        unified_config["drawing_skill"] = json.load(f)
    print("已加载绘画技能配置")
        
    # 搜索技能配置
    with open(os.path.join(skills_dir, "search_skill.json"), 'r', encoding='utf-8') as f:
        unified_config["search_skill"] = json.load(f)
    print("已加载搜索技能配置")
        
    # 写入统一配置文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(unified_config, f, ensure_ascii=False, indent=2)
    print(f"已创建统一配置文件: {output_file}")
    
except Exception as e:
    print(f"创建统一配置文件失败: {str(e)}") 