#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王直接测试jimeng-free-api接口
"""

import requests
import json
import time
from datetime import datetime

def test_jimeng_free_api_direct():
    """直接测试jimeng-free-api接口"""
    
    print("🔥 老王直接测试jimeng-free-api接口...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # API配置 - 🔥 老王修复：使用正确的session_id
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"  # 从配置文件中获取的真实session_id
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 🔥 老王修复：使用官网标准尺寸
    # 手机竖屏 9:16 = 1080x1920
    data = {
        "model": "jimeng-4.0",
        "prompt": "一只可爱的小猫",
        "negative_prompt": "低质量",
        "width": 1080,
        "height": 1920,
        "sample_strength": 0.8,
        "response_format": "url"
    }
    
    print("📤 发送请求到jimeng-free-api...")
    print(f"🔗 URL: {url}")
    print(f"🔑 Authorization: Bearer {session_id[:20]}...")
    print(f"📋 请求体: {json.dumps(data, indent=2, ensure_ascii=False)}")
    print("-" * 40)
    
    try:
        start_time = time.time()
        
        # 发送请求
        response = requests.post(url, headers=headers, json=data, timeout=900)
        
        elapsed_time = time.time() - start_time
        
        print(f"📥 响应接收完成，耗时: {elapsed_time:.2f}秒")
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        print("-" * 40)
        
        # 打印响应内容
        response_text = response.text
        print(f"📄 完整响应内容:")
        print(response_text)
        print("-" * 40)
        
        # 解析JSON
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ JSON解析成功!")
                print(f"📋 解析后的数据:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 分析结果
                if isinstance(result, dict):
                    print("-" * 40)
                    print("🔍 结果分析:")
                    
                    created = result.get("created", "未知")
                    data_list = result.get("data", [])
                    
                    print(f"- 创建时间: {created}")
                    print(f"- 数据数量: {len(data_list) if isinstance(data_list, list) else '非数组'}")
                    
                    if isinstance(data_list, list) and data_list:
                        print("- 图片URL:")
                        for i, item in enumerate(data_list):
                            if isinstance(item, dict) and "url" in item:
                                print(f"  {i+1}. {item['url']}")
                            else:
                                print(f"  {i+1}. 格式异常: {item}")
                        return True
                    else:
                        print("- ⚠️ 数据为空或格式异常")
                        return False
                else:
                    print("⚠️ 响应不是字典格式")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"错误响应: {response_text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时（15分钟）")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知异常: {e}")
        return False

def test_different_session_ids():
    """测试不同的session_id"""

    print("🔧 测试不同的session_id...")

    # 🔥 老王修复：使用正确的session_id列表（从配置文件中获取的真实值）
    session_ids = [
        "1e6aa5b4800b56a3e345bacacfaa7c09",  # session 1
        "96425b141fffc2443d5abdafb494c184",  # session 2
        "661e6f442106e10947d9e838a656293b"   # session 3
    ]
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    for i, session_id in enumerate(session_ids, 1):
        print(f"\n🧪 测试 {i}: session_id = {session_id[:20]}...")
        
        headers = {
            "Authorization": f"Bearer {session_id}",
            "Content-Type": "application/json"
        }
        
        # 🔥 老王修复：使用官网标准尺寸 - 正方形 1:1 = 1024x1024
        data = {
            "model": "jimeng-4.0",
            "prompt": "测试",
            "width": 1024,
            "height": 1024,
            "response_format": "url"
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=60)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                data_list = result.get("data", [])
                if data_list:
                    print(f"✅ 成功！返回 {len(data_list)} 张图片")
                    return session_id
                else:
                    print("⚠️ 返回空数据")
            else:
                print(f"❌ 失败: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
    
    return None

if __name__ == "__main__":
    print("🔥 老王的jimeng-free-api直接测试")
    print("=" * 60)
    
    # 测试1: 直接调用API
    success = test_jimeng_free_api_direct()
    
    print("\n" + "=" * 60)
    
    # 如果失败，测试不同的session_id
    if not success:
        print("🔧 主测试失败，尝试不同的session_id...")
        valid_session_id = test_different_session_ids()
        
        if valid_session_id:
            print(f"✅ 找到有效的session_id: {valid_session_id}")
        else:
            print("❌ 所有session_id都无效")
    
    print("\n" + "=" * 60)
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔥 测试完成！")
