#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老王测试跳过提示词优化的drawing skill
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cognitive_modules.skills.drawing_skill import DrawingSkill

def test_skip_optimize():
    """测试跳过提示词优化"""
    print("🔥 老王测试跳过提示词优化的drawing skill")
    print("=" * 60)
    
    try:
        # 初始化drawing skill
        drawing_skill = DrawingSkill()
        
        # 模拟drawing skill的execute方法，但跳过提示词优化
        input_text = "流浪地球画面"
        user_id = "test_user"
        
        print(f"🎯 原始提示词: {input_text}")
        
        # 直接使用原始提示词，跳过_optimize_prompt
        ai_image_prompt = input_text
        ai_aspect_ratio = "9:16"  # 默认比例
        
        print(f"📝 使用提示词: {ai_image_prompt}")
        print(f"📐 使用比例: {ai_aspect_ratio}")
        
        # 设置图片尺寸
        image_width, image_height, keling_width, keling_height = drawing_skill._get_image_dimensions(ai_aspect_ratio)
        print(f"📏 图片尺寸: {image_width}x{image_height}")
        
        # 直接调用jimeng服务
        result = drawing_skill._try_jimeng_service(ai_image_prompt, image_width, image_height)
        
        print(f"📋 返回结果: {result}")
        
        if isinstance(result, dict):
            if result.get("success"):
                print(f"✅ 成功！跳过提示词优化后jimeng服务正常工作！")
                print(f"   图片URL: {result.get('result', {}).get('image_url', 'N/A')}")
            else:
                print(f"❌ 失败: {result.get('message', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_skip_optimize()
